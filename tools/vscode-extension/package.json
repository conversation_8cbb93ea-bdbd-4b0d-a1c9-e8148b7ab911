{"name": "yixintang-css-standards", "displayName": "壹心堂CSS标准检查器", "description": "基于轮廓调试标准规则v4.0的实时CSS约束检查插件", "version": "1.0.0", "publisher": "y<PERSON>intang", "engines": {"vscode": "^1.74.0"}, "categories": ["Linters", "Other"], "keywords": ["css", "standards", "y<PERSON>intang", "outline-debug", "layout"], "activationEvents": ["onLanguage:vue", "onLanguage:css", "onLanguage:scss", "onLanguage:less"], "main": "./out/extension.js", "contributes": {"commands": [{"command": "yixintang.checkCssStandards", "title": "检查CSS标准合规性", "category": "壹心堂"}, {"command": "yixintang.enableOutlineDebug", "title": "启用轮廓调试模式", "category": "壹心堂"}, {"command": "yixintang.insertStandardComponent", "title": "插入标准组件模板", "category": "壹心堂"}, {"command": "yixintang.validateLayout", "title": "验证布局合规性", "category": "壹心堂"}], "keybindings": [{"command": "yixintang.checkCssStandards", "key": "ctrl+shift+s", "mac": "cmd+shift+s", "when": "editorTextFocus"}, {"command": "yixintang.enableOutlineDebug", "key": "ctrl+shift+d", "mac": "cmd+shift+d", "when": "editorTextFocus"}], "configuration": {"title": "壹心堂CSS标准", "properties": {"yixintang.enableRealTimeCheck": {"type": "boolean", "default": true, "description": "启用实时CSS标准检查"}, "yixintang.standardsPath": {"type": "string", "default": "admin/src/styles/standards.css", "description": "CSS标准库文件路径"}, "yixintang.outlineDebugEnabled": {"type": "boolean", "default": true, "description": "启用轮廓调试功能"}, "yixintang.checkOnSave": {"type": "boolean", "default": true, "description": "保存时自动检查"}}}, "snippets": [{"language": "vue", "path": "./snippets/vue-standards.json"}, {"language": "css", "path": "./snippets/css-standards.json"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "tsc -p ./", "watch": "tsc -watch -p ./"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "typescript": "^4.9.4"}, "dependencies": {"css-tree": "^2.3.1", "postcss": "^8.4.21", "vue-template-compiler": "^2.7.14"}}