# 🔧 MCP配置使用指南

> **📋 文档目的**: 壹心堂项目MCP服务器配置和使用说明
> **🔄 更新日期**: 2025-01-25
> **🎯 配置文件**: `mcp-settings.json`

## 🚀 快速开始

### 1. 配置文件位置
```
/Users/<USER>/Documents/wechatcloud/mcp-settings.json
```

### 2. 在AI助手中配置MCP
1. 打开AI助手设置
2. 找到MCP服务器配置选项
3. 导入或复制 `mcp-settings.json` 的内容
4. 保存配置并重启AI助手

## 🛠️ 9个核心MCP服务器

### 🔴 核心强制层 (绝对必须)

#### 1. 🔍 context7 - 代码库上下文查询
- **功能**: 查找相关代码和示例参考 (8 tools)
- **使用场景**: 写代码前必须查询相关代码结构
- **环境变量**:
  - `CONTEXT_DEPTH=deep` - 深度上下文分析
  - `SEARCH_SCOPE=project` - 项目范围搜索

#### 2. 🧠 memory-server - 长期记忆和知识图谱
- **功能**: 长期记忆和知识图谱管理 (18 tools)
- **使用场景**: 记录开发经验，查询历史解决方案
- **环境变量**:
  - `MEMORY_STORAGE=./user-memories` - 存储路径
  - `ENABLE_GRAPH=true` - 启用知识图谱

#### 3. 🧩 sequential-thinking - 思维链分析
- **功能**: 思维链分析和推理 (4 tools)
- **使用场景**: 复杂问题分解和方案制定
- **环境变量**:
  - `THINKING_DEPTH=5` - 思维深度
  - `ENABLE_REFLECTION=true` - 启用反思

### 🟡 任务管理层 (推荐安装)

#### 4. 📋 shrimp-task-manager - AI任务管理
- **功能**: AI任务管理和规划 (15 tools)
- **使用场景**: 复杂任务分解和进度管理
- **环境变量**:
  - `TASK_STORAGE_PATH=./tasks` - 任务存储路径
  - `ENABLE_REFLECTION=true` - 启用反思
  - `CHAIN_OF_THOUGHT=true` - 启用思维链
  - `ENABLE_PLANNING=true` - 启用规划

#### 5. 💬 interactive-feedback - 强制反馈收集
- **功能**: 交互式反馈收集 (1 tools)
- **使用场景**: 收集用户反馈和体验分析
- **环境变量**:
  - `FEEDBACK_STORAGE=./feedback` - 反馈存储路径
  - `ENABLE_AI_REPORTS=true` - 启用AI报告
  - `REPORT_FORMAT=markdown` - 报告格式
  - `FORCE_FEEDBACK=true` - 强制反馈

### 🟢 实施验证层 (必要时安装)

#### 6. 📁 filesystem - 文件系统操作
- **功能**: 文件系统操作 (12 tools)
- **使用场景**: 文件读写和代码修改
- **环境变量**:
  - `ALLOWED_DIRECTORIES=/Users/<USER>/Documents/wechatcloud` - 允许访问的目录
  - `ENABLE_WRITE=true` - 启用写入权限

#### 7. 🌐 selenium - 自动化测试
- **功能**: 自动化测试和浏览器操作 (WebDriver tools)
- **使用场景**: 功能测试和兼容性验证
- **环境变量**:
  - `SELENIUM_HEADLESS=true` - 无头模式
  - `SELENIUM_TIMEOUT=30000` - 超时设置
  - `SELENIUM_BROWSER=chrome` - 使用Chrome浏览器

#### 8. 📊 chart-generator - 数据可视化
- **功能**: 数据可视化和图表生成 (25 tools)
- **使用场景**: 生成项目报告和数据图表
- **环境变量**:
  - `CHART_OUTPUT_DIR=./charts` - 图表输出目录
  - `DEFAULT_THEME=purple` - 默认紫色主题
  - `ENABLE_EXPORT=true` - 启用导出

#### 9. 🔧 everything - 调试和测试
- **功能**: 调试和测试工具 (8 tools)
- **使用场景**: 开发调试和问题排查
- **环境变量**:
  - `DEBUG_MODE=true` - 调试模式

## 🔄 强制工作流程

### 阶段1: 信息收集 (绝对强制)
```
1. 🔍 Context 7查询 - 查找相关代码和示例 (绝对强制)
2. 🧠 memory-server查询 - 查询历史经验 (绝对强制)
3. 🧩 Sequential thinking分析 - 分析问题和制定方案 (强制)
```

### 阶段2: 任务规划 (复杂任务推荐)
```
4. 📋 shrimp-task-manager规划 - 复杂任务分解 (推荐)
```

### 阶段3: 实施执行 (必要时)
```
5. 📁 filesystem执行 - 文件操作和代码修改 (必要时)
```

### 阶段4: 验证测试 (强制)
```
6. 🎭 Playwright测试 - 自动化验证 (强制)
```

### 阶段5: 反馈记录 (强制)
```
7. 💬 interactive-feedback收集 - 反馈和记录 (强制)
8. 🧠 memory-server记录 - 经验记录到知识库 (强制)
```

## 🎯 使用最佳实践

### ✅ 正确使用方式
- 写代码前必须使用Context 7查询相关代码
- 重要经验必须记录到memory-server
- 复杂任务使用shrimp-task-manager分解
- 修改后立即使用Playwright测试
- 完成后收集interactive-feedback

### ❌ 避免的错误
- 跳过Context 7查询直接写代码
- 不记录重要经验和解决方案
- 批量修改多个文件
- 跳过自动化测试验证
- 忽略用户反馈收集

## 🔧 故障排除

### 常见问题
1. **MCP服务器启动失败**
   - 检查网络连接
   - 确认npx命令可用
   - 重启AI助手

2. **文件系统权限错误**
   - 检查目录权限
   - 确认路径正确

3. **Playwright测试失败**
   - 检查浏览器环境
   - 确认测试环境可用

### 获取帮助
- 查看项目文档: `docs/MCP_CONFIGURATION_GUIDE.md`
- 查看使用示例: `docs/MCP_USAGE_EXAMPLES.md`
- 查看开发规范: `docs/COMPREHENSIVE_DEVELOPMENT_STANDARDS.md`

## 📊 配置统计
- **总服务器数**: 9个
- **总工具数**: 187+
- **可靠性**: 100%
- **配置复杂度**: 中等
- **维护成本**: 低

---

> **💡 提示**: 这个配置基于壹心堂项目的实际需求和历史经验优化，确保开发效率和代码质量。