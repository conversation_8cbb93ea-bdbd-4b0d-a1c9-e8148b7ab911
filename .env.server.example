# 服务器AI图片生成服务环境配置示例
# 复制此文件为 .env 并填入真实的API密钥

# ===========================================
# AI图片生成服务配置
# ===========================================

# AI服务提供商选择
# 可选值: alibaba, stabilityAI, openAI, baidu
AI_IMAGE_PROVIDER=alibaba

# 阿里通义万相 API密钥 (推荐-国内服务器)
# 获取地址: https://dashscope.console.aliyun.com/api-key
ALIBABA_API_KEY=your-alibaba-api-key-here

# Stability AI API密钥 (推荐-国外服务器)
# 获取地址: https://platform.stability.ai/account/keys
STABILITY_API_KEY=your-stability-api-key-here

# OpenAI API密钥 (最高质量)
# 获取地址: https://platform.openai.com/api-keys
OPENAI_API_KEY=your-openai-api-key-here

# 百度文心一言 API密钥 (当前使用)
# 获取地址: https://console.bce.baidu.com/qianfan/ais/console/applicationConsole/application
BAIDU_API_KEY=your-baidu-api-key-here

# ===========================================
# 图片生成配置
# ===========================================

# 图片缓存目录
AI_IMAGE_CACHE_DIR=/var/cache/ai-images

# 图片最大尺寸 (推荐512x512，适合服务器)
AI_IMAGE_MAX_SIZE=512x512

# 图片质量 (standard/high)
AI_IMAGE_QUALITY=standard

# 缓存过期时间 (秒)
AI_IMAGE_CACHE_TTL=604800

# 最大缓存数量
AI_IMAGE_MAX_CACHE=1000

# ===========================================
# 应用配置
# ===========================================

# 运行环境
NODE_ENV=production

# 服务端口
PORT=3000

# 服务主机
HOST=0.0.0.0

# ===========================================
# 日志配置
# ===========================================

# 日志级别 (error/warn/info/debug)
LOG_LEVEL=info

# 日志文件路径
LOG_FILE=logs/ai-service.log

# 错误日志文件
ERROR_LOG_FILE=logs/error.log

# ===========================================
# 性能配置
# ===========================================

# API请求超时时间 (毫秒)
API_TIMEOUT=60000

# 最大并发请求数
MAX_CONCURRENT_REQUESTS=5

# 请求重试次数
MAX_RETRIES=3

# ===========================================
# 安全配置
# ===========================================

# API密钥加密盐值 (可选)
API_KEY_SALT=your-random-salt-here

# 允许的域名 (CORS)
ALLOWED_ORIGINS=http://localhost:3000,https://yourdomain.com

# ===========================================
# 监控配置
# ===========================================

# 健康检查端点
HEALTH_CHECK_ENDPOINT=/health

# 监控数据收集间隔 (秒)
MONITOR_INTERVAL=300

# ===========================================
# 区域配置 (针对不同服务商)
# ===========================================

# 阿里云区域
ALIBABA_REGION=cn-beijing

# AWS区域 (Stability AI)
AWS_REGION=us-east-1

# ===========================================
# 使用说明
# ===========================================

# 1. 复制此文件为 .env
# 2. 根据你的服务器位置选择合适的AI服务:
#    - 国内服务器: 推荐使用 alibaba (阿里通义万相)
#    - 国外服务器: 推荐使用 stabilityAI (Stability AI)
#    - 追求质量: 使用 openAI (OpenAI DALL-E 3)
#    - 保持现状: 使用 baidu (百度文心一言)
#
# 3. 获取对应服务的API密钥并填入上方
# 4. 调整其他配置参数 (可选)
# 5. 运行部署脚本: chmod +x scripts/deploy-server-ai.sh && ./scripts/deploy-server-ai.sh

# ===========================================
# 推荐配置
# ===========================================

# 国内服务器推荐配置:
# AI_IMAGE_PROVIDER=alibaba
# ALIBABA_API_KEY=your-key
# AI_IMAGE_QUALITY=standard
# LOG_LEVEL=info

# 国外服务器推荐配置:
# AI_IMAGE_PROVIDER=stabilityAI
# STABILITY_API_KEY=your-key
# AI_IMAGE_QUALITY=high
# LOG_LEVEL=info

# 高质量配置:
# AI_IMAGE_PROVIDER=openAI
# OPENAI_API_KEY=your-key
# AI_IMAGE_QUALITY=high
# LOG_LEVEL=warn
