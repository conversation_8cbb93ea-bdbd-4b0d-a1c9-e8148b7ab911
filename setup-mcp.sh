#!/bin/bash

# 🔧 MCP环境配置脚本
# 用途: 自动创建MCP服务器所需的目录结构
# 作者: AI助手
# 日期: 2025-01-25

echo "🚀 开始配置MCP环境..."

# 设置项目根目录
PROJECT_ROOT="/Users/<USER>/Documents/wechatcloud"
cd "$PROJECT_ROOT" || exit 1

echo "📁 创建MCP服务器所需目录..."

# 创建memory-server存储目录
if [ ! -d "user-memories" ]; then
    mkdir -p user-memories
    echo "✅ 创建 user-memories 目录"
else
    echo "📂 user-memories 目录已存在"
fi

# 创建task-manager存储目录
if [ ! -d "tasks" ]; then
    mkdir -p tasks
    echo "✅ 创建 tasks 目录"
else
    echo "📂 tasks 目录已存在"
fi

# 创建feedback存储目录
if [ ! -d "feedback" ]; then
    mkdir -p feedback
    echo "✅ 创建 feedback 目录"
else
    echo "📂 feedback 目录已存在"
fi

# 创建charts输出目录
if [ ! -d "charts" ]; then
    mkdir -p charts
    echo "✅ 创建 charts 目录"
else
    echo "📂 charts 目录已存在"
fi

# 创建reports目录（如果不存在）
if [ ! -d "reports" ]; then
    mkdir -p reports
    echo "✅ 创建 reports 目录"
else
    echo "📂 reports 目录已存在"
fi

echo "📋 检查必要文件..."

# 检查MCP配置文件
if [ -f "mcp-settings.json" ]; then
    echo "✅ MCP配置文件存在: mcp-settings.json"
else
    echo "❌ MCP配置文件不存在: mcp-settings.json"
    echo "请确保已创建配置文件"
fi

# 检查使用指南
if [ -f "MCP_配置使用指南.md" ]; then
    echo "✅ 使用指南存在: MCP_配置使用指南.md"
else
    echo "❌ 使用指南不存在: MCP_配置使用指南.md"
fi

echo "🔍 验证Node.js和npm环境..."

# 检查Node.js
if command -v node &> /dev/null; then
    NODE_VERSION=$(node --version)
    echo "✅ Node.js已安装: $NODE_VERSION"
else
    echo "❌ Node.js未安装，请先安装Node.js"
    exit 1
fi

# 检查npm
if command -v npm &> /dev/null; then
    NPM_VERSION=$(npm --version)
    echo "✅ npm已安装: $NPM_VERSION"
else
    echo "❌ npm未安装，请先安装npm"
    exit 1
fi

# 检查npx
if command -v npx &> /dev/null; then
    echo "✅ npx可用"
else
    echo "❌ npx不可用，请更新npm版本"
    exit 1
fi

echo "📊 目录结构概览:"
echo "├── user-memories/     (memory-server存储)"
echo "├── tasks/            (task-manager存储)"
echo "├── feedback/         (反馈收集存储)"
echo "├── charts/           (图表输出)"
echo "├── reports/          (报告输出)"
echo "├── mcp-settings.json (MCP配置文件)"
echo "└── MCP_配置使用指南.md (使用说明)"

echo ""
echo "🎯 下一步操作:"
echo "1. 在AI助手中导入 mcp-settings.json 配置"
echo "2. 重启AI助手以加载MCP服务器"
echo "3. 阅读 MCP_配置使用指南.md 了解使用方法"
echo "4. 开始使用9个MCP服务器进行开发"

echo ""
echo "✅ MCP环境配置完成！"
echo "📖 详细使用说明请查看: MCP_配置使用指南.md"

# 设置目录权限
chmod 755 user-memories tasks feedback charts reports 2>/dev/null || true

echo "🔧 目录权限已设置"
echo "🚀 MCP环境已就绪，可以开始使用！"