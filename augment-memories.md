# 🧠 Augment Memories - 壹心堂管理系统专用配置

> **📋 配置目的**: 为壹心堂管理系统项目优化的Augment记忆配置  
> **🔄 更新日期**: 2025-01-28  
> **🎯 项目类型**: Vue.js + Django 健康服务管理系统  
> **👥 开发模式**: AI辅助开发 + 标准化工作流程  
> **📊 MCP生态**: 9个稳定服务器，100%可靠性

## 🎯 项目核心信息

### **项目身份**
- **项目名称**: 壹心堂管理系统 (WeChat Cloud Health Management System)
- **项目类型**: 现代化健康服务管理系统
- **技术栈**: Vue 3 + Django + PostgreSQL + 微信云开发
- **UI风格**: 梵高风格 + 紫色主题 + 毛玻璃效果
- **开发模式**: AI辅助开发 + 标准化规范 + 持续改进

### **核心业务领域**
- 🏥 **健康服务管理**: 服务项目、技师管理、客户档案
- 📅 **预约调度系统**: 智能时间安排、预约管理
- 💰 **财务管理**: 收支记录、提成计算、财务报表
- 📱 **微信小程序**: 客户端应用、在线预约
- 🎨 **UI/UX设计**: 响应式设计、毛玻璃效果、紫色主题

## 🛠️ 技术架构记忆

### **前端技术栈**
```javascript
// Vue 3 Composition API + 标准化组件
- Framework: Vue 3.3+ (Composition API)
- Build Tool: Vite 4.0+
- UI Library: Ant Design Vue 4.0+
- CSS Framework: PostCSS + 自定义毛玻璃效果
- State Management: Pinia
- Router: Vue Router 4.0+
- Testing: Playwright + 自动化测试
```

### **后端技术栈**
```python
# Django + RESTful API
- Framework: Django 4.2+
- Database: PostgreSQL + SQLite (开发)
- API: Django REST Framework
- Authentication: JWT + 微信授权
- Deployment: 微信云开发 + Docker
```

### **开发工具生态**
```bash
# MCP服务器配置 (9个稳定服务器)
- context7: 代码库上下文查询 (绝对强制)
- memory-server: 长期记忆管理 (绝对强制)
- sequential-thinking: 深度思考分析 (绝对强制)
- shrimp-task-manager: 任务规划管理
- interactive-feedback: 用户反馈收集 (绝对强制)
- filesystem: 文件系统操作
- playwright: 自动化测试 (绝对强制)
- chart-generator: 图表生成
- everything: 调试和实用工具
```

## 🎨 设计系统记忆

### **视觉设计标准**
```css
/* 毛玻璃效果标准 */
--glass-primary: rgba(255, 255, 255, 0.08);
--glass-secondary: rgba(255, 255, 255, 0.05);
--glass-input: rgba(255, 255, 255, 0.03);
--glass-border: rgba(255, 255, 255, 0.15);
--glass-blur-standard: blur(25px) saturate(1.5);

/* 紫色主题色彩 */
--primary-purple: rgba(139, 92, 246, 0.15);
--secondary-purple: rgba(168, 85, 247, 0.12);
--accent-purple: #4f46e5;

/* Z-index层级系统 */
--z-base: 1;
--z-table-header: 300;
--z-modal: 1001;
--z-toast: 2000;
```

### **响应式断点系统**
```css
/* 标准断点配置 */
--mobile: (max-width: 767px);
--tablet: (min-width: 768px) and (max-width: 1199px);
--desktop: (min-width: 1200px);

/* 动态字体缩放 */
font-size: clamp(12px, 1.2vw, 18px);
```

## 📋 开发流程记忆

### **强制执行的工作流程**
1. **🚨 Context 7查询** - 查找相关代码和示例 (绝对强制)
2. **🚨 memory-server查询** - 查询历史经验 (绝对强制)
3. **🚨 Sequential thinking分析** - 深度思考分析 (绝对强制)
4. **shrimp-task-manager规划** - 任务分解和规划
5. **filesystem执行** - 代码实现和文件操作
6. **🚨 Playwright测试** - 自动化测试验证 (绝对强制)
7. **🚨 interactive-feedback收集** - 用户反馈收集 (绝对强制)
8. **🚨 memory-server记录** - 经验记录和知识更新 (绝对强制)

### **质量保证标准**
```bash
# CSS规范检查 (必须通过)
node css-standards-checker.js admin/src/views/*.vue

# 智能Git提交 (标准化提交信息)
./smart-commit.sh "feat: 功能描述"

# 响应式测试 (多分辨率验证)
npm run test:responsive

# 代码质量检查 (100%合规率要求)
npm run lint && npm run test
```

## 🏗️ 项目结构记忆

### **目录组织标准**
```
wechatcloud/
├── admin/                 # Vue.js 管理后台
│   ├── src/views/        # 页面组件 (6个核心页面)
│   ├── src/components/   # 通用组件
│   └── src/utils/        # 工具函数
├── client/               # 微信小程序客户端
├── server/               # Django 后端服务
├── docs/                 # 完整文档体系 (50+ 标准文档)
├── config/               # MCP和工作流程配置
├── scripts/              # 自动化脚本
├── tools/                # 开发工具
└── reports/              # 测试和质量报告
```

### **核心页面组件**
```javascript
// 6个核心管理页面 (已完成现代化改造)
1. ServiceManagement.vue     // 服务管理 (标准模板)
2. TherapistManagement.vue   // 技师管理
3. CustomerManagement.vue    // 客户管理
4. AppointmentManagement.vue // 预约管理
5. Dashboard.vue             // 仪表板
6. FinanceOverview.vue       // 财务概览

// 统一特性：
- 智能表头 (搜索模式切换 + 排序功能)
- 毛玻璃效果背景
- 翻页组件 (固定底部定位)
- 响应式设计 (移动端适配)
- 统一的数据展示和交互方式
```

## 🔧 开发经验记忆

### **常见问题解决方案**
1. **页面结构不一致** → 使用 `standardize-page-structure.js` 脚本
2. **缺少翻页组件** → 使用 `add-pagination-to-pages.js` 脚本
3. **数据展示为空** → 使用 `add-data-to-pages.js` 脚本
4. **CSS规范不合规** → 运行 `css-standards-checker.js` 检查
5. **Git提交不规范** → 使用 `smart-commit.sh` 智能提交

### **性能优化经验**
- 使用 `transform` 和 `opacity` 进行GPU加速动画
- 限制 `backdrop-filter` 使用范围
- 避免深层CSS选择器嵌套 (最大3层)
- 使用CSS自定义属性实现主题一致性

### **测试验证经验**
- 多分辨率测试：1920x1080, 1366x768, 768x1024, 375x667
- 浏览器兼容性：Chrome 90+, Firefox 88+, Safari 14+
- 性能指标：LCP < 2.5s, FID < 100ms, CLS < 0.1

## 🎯 项目目标记忆

### **短期目标 (已完成)**
- ✅ 6个核心页面现代化改造 (100%完成)
- ✅ 统一页面结构和视觉效果
- ✅ 完整的翻页和数据展示功能
- ✅ CSS规范100%合规率
- ✅ 响应式设计全覆盖

### **长期目标**
- 🎯 微信小程序客户端完善
- 🎯 后端API优化和扩展
- 🎯 数据分析和报表功能
- 🎯 移动端原生应用开发
- 🎯 AI智能推荐系统集成

## 💡 最佳实践记忆

### **代码质量标准**
- 使用Vue 3 Composition API编写组件
- 遵循PostCSS兼容语法
- 实现完整的错误处理和加载状态
- 提供用户友好的反馈机制
- 保持代码可读性和可维护性

### **用户体验标准**
- 毛玻璃效果增强视觉层次
- 紫色主题保持品牌一致性
- 响应式设计适配所有设备
- 智能搜索支持中文和拼音
- 流畅的动画和过渡效果

---

> **🎯 记忆更新策略**: 每次重大功能更新后，使用memory-server记录新的经验和最佳实践  
> **📊 质量监控**: 持续监控代码质量指标，保持100%规范合规率  
> **🔄 持续改进**: 基于interactive-feedback收集的用户反馈，不断优化开发流程和用户体验
