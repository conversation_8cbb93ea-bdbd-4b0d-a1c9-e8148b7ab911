# 📁 壹心堂项目目录结构规范

## 🎯 规范目标
建立清晰、统一的项目目录结构，确保文档、脚本、测试文件的有序管理，提高开发效率和项目维护性。

## 📋 根目录结构规范

### **🚨 强制约束 - 根目录只允许以下文件**

```
wechatcloud/                          # 项目根目录
├── README.md                         # ✅ 项目主文档
├── LICENSE                           # ✅ 许可证文件
├── PROJECT_STRUCTURE_STANDARDS.md    # ✅ 本规范文档
├── project_check.py                  # ✅ 统一检查脚本
├── one_click_start.py                # ✅ Python启动脚本
├── start-all-dev.sh                  # ✅ 开发环境启动脚本
├── stop-all-dev.sh                   # ✅ 开发环境停止脚本
├── start.py                          # ✅ 备用启动脚本
├── start.bat                         # ✅ Windows启动脚本
├── package-lock.json                 # ✅ 根级依赖锁文件
├── qodana.yaml                       # ✅ 代码质量配置
├── admin/                            # ✅ 管理后台目录
├── client/                           # ✅ 小程序目录
├── server/                           # ✅ 后端服务目录
├── docs/                             # ✅ 文档目录
├── scripts/                          # ✅ 通用脚本目录
├── logs/                             # ✅ 日志目录
└── test-reports/                     # ✅ 测试报告目录
```

### **❌ 严格禁止在根目录的文件类型**
- ❌ 临时文档文件 (*.md，除规范允许外)
- ❌ 测试脚本文件 (test_*.py, verify_*.py)
- ❌ 批量修改脚本 (batch_*.py, fix_*.py)
- ❌ 临时报告文件 (*.json, *.log)
- ❌ 开发过程文档 (CRITICAL_*, QUICK_*, STANDARDS_*)
- ❌ 项目状态报告 (PROJECT_STATUS_*, FINAL_*)

## 📂 docs/ 目录结构规范

```
docs/                                 # 文档根目录
├── README.md                         # 文档目录说明
├── CI_CD_STANDARDS.md                # 开发规范主文档
├── standards/                        # 开发规范目录
│   ├── DEVELOPMENT_STANDARDS.md      # 详细开发规范
│   ├── TEMPLATE_STANDARDS.md         # 模板标准
│   └── UI_DESIGN_STANDARDS.md        # UI设计规范
├── guides/                           # 使用指南目录
│   ├── SCRIPTS_GUIDE.md              # 脚本使用指南
│   ├── DEPLOYMENT_GUIDE.md           # 部署指南
│   └── DEVELOPMENT_GUIDE.md          # 开发指南
├── reports/                          # 项目报告目录
│   ├── FINAL_TEST_REPORT.md          # 最终测试报告
│   ├── PROJECT_STATUS_REPORT.md      # 项目状态报告
│   └── PROJECT_CLEANUP_SUMMARY_*.md  # 清理总结报告
├── archive/                          # 归档文档目录
│   ├── CRITICAL_ISSUES_TRACKING.md   # 历史问题跟踪
│   ├── QUICK_FIX_GUIDE.md            # 历史修复指南
│   └── STANDARDS_CHECK_ANALYSIS.md   # 历史分析文档
├── api/                              # API文档目录
├── deployment/                       # 部署文档目录
└── design/                           # 设计文档目录
```

## 🧪 测试文件目录规范

### **测试脚本路径约束**
```
wechatcloud/
├── test-reports/                     # 🚨 根级测试报告目录
│   ├── comprehensive/                # 综合测试报告
│   ├── manual-system-analysis.md     # 手动系统分析
│   └── latest-test-summary.md        # 最新测试总结
├── admin/                            # 前端测试
│   ├── test-reports/                 # 前端测试报告
│   ├── scripts/                      # 前端专用脚本
│   └── tests/                        # 前端测试文件
├── client/                           # 小程序测试
│   ├── test-reports/                 # 小程序测试报告
│   └── scripts/                      # 小程序专用脚本
└── server/                           # 后端测试
    ├── test-reports/                 # 后端测试报告
    ├── scripts/                      # 后端专用脚本
    └── tests/                        # 后端测试文件
```

### **🚨 测试脚本命名规范**
- ✅ **单元测试**: `test_*.py` (放在对应模块的tests目录)
- ✅ **集成测试**: `integration_*.py` (放在对应模块的tests目录)
- ✅ **E2E测试**: `e2e_*.py` (放在对应模块的tests目录)
- ❌ **禁止**: 在根目录创建任何测试脚本

## 🔧 脚本文件目录规范

### **脚本分类和位置**
```
wechatcloud/
├── project_check.py                  # 🚨 统一检查脚本 (根目录)
├── one_click_start.py                # 🚨 启动脚本 (根目录)
├── start-all-dev.sh                  # 🚨 开发环境脚本 (根目录)
├── scripts/                          # 通用脚本目录
│   ├── check-standards.js            # Node.js检查脚本
│   ├── deploy.sh                     # 部署脚本
│   └── backup.sh                     # 备份脚本
├── admin/scripts/                    # 前端专用脚本
│   ├── build.js                      # 构建脚本
│   └── test.js                       # 测试脚本
├── client/scripts/                   # 小程序专用脚本
└── server/scripts/                   # 后端专用脚本
    ├── migrate.py                    # 数据库迁移
    └── seed.py                       # 数据种子
```

### **🚨 脚本创建约束**
- ❌ **禁止**: 在根目录创建临时脚本
- ❌ **禁止**: 创建批量修改脚本 (batch_*, fix_*, apply_*)
- ✅ **允许**: 在对应的scripts目录创建功能脚本
- ✅ **允许**: 更新现有的核心脚本 (经过测试)

## 📊 日志文件目录规范

```
wechatcloud/
├── logs/                             # 🚨 根级日志目录
│   ├── dev-startup-*.log             # 开发启动日志
│   ├── backend.log                   # 后端日志
│   ├── frontend.log                  # 前端日志
│   └── miniprogram.log               # 小程序日志
├── admin/logs/                       # 前端专用日志
├── client/logs/                      # 小程序专用日志
└── server/logs/                      # 后端专用日志
```

## 🚨 强制执行规则

### **文件创建规则**
1. **根目录文件**: 🚨 **仅允许创建本规范明确列出的文件类型**
2. **文档文件**: 🚨 **必须放在docs/对应子目录中**
3. **测试文件**: 🚨 **必须放在对应模块的tests目录中**
4. **脚本文件**: 🚨 **必须放在对应的scripts目录中**
5. **临时文件**: 🚨 **禁止提交到版本控制系统**

### **文件命名规则**
1. **文档文件**: 使用大写字母和下划线 (EXAMPLE_DOCUMENT.md)
2. **脚本文件**: 使用小写字母和下划线 (example_script.py)
3. **测试文件**: 使用test_前缀 (test_example.py)
4. **配置文件**: 使用小写字母和点号 (example.config.js)

### **文件大小限制**
- 📄 **文档文件**: 单个文件不超过 500 行
- 🔧 **脚本文件**: 单个文件不超过 300 行
- 📊 **日志文件**: 自动轮转，单个文件不超过 10MB

## 🔄 维护和更新规范

### **定期清理任务**
1. **每周**: 清理logs目录中的过期日志文件
2. **每月**: 检查docs/archive目录，删除过时文档
3. **每季度**: 审查目录结构，确保符合规范
4. **每半年**: 更新本规范文档，适应项目发展

### **违规处理**
1. **发现违规**: 立即移动文件到正确位置
2. **重复违规**: 更新开发规范，加强约束
3. **严重违规**: 回滚提交，重新整理

## ✅ 规范检查清单

### **提交前检查**
- [ ] 根目录无违规文件
- [ ] 文档放在正确的docs子目录
- [ ] 测试文件放在正确的tests目录
- [ ] 脚本文件放在正确的scripts目录
- [ ] 临时文件已清理
- [ ] 文件命名符合规范

### **定期审查**
- [ ] 目录结构符合规范
- [ ] 文件分类正确
- [ ] 无重复或冗余文件
- [ ] 文档内容时效性良好

---

**📋 规范版本**: v1.0  
**📅 生效日期**: 2025-07-17  
**👥 制定团队**: 壹心堂开发团队  
**🔄 更新频率**: 根据项目发展需要更新

> 🚨 **强制执行**: 本规范为强制执行标准，所有开发活动必须严格遵守。违反规范的提交将被拒绝。
