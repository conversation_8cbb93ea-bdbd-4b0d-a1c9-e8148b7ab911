{"timestamp": "2025-07-28T13:09:53.387Z", "summary": {"totalPages": 6, "passedPages": 0, "failedPages": 6, "successRate": "0.00%"}, "issues": [{"page": "ServiceManagement", "type": "load", "error": "页面文件结构不完整", "severity": "high"}, {"page": "TherapistManagement", "type": "load", "error": "页面文件结构不完整", "severity": "high"}, {"page": "CustomerManagement", "type": "load", "error": "页面文件结构不完整", "severity": "high"}, {"page": "AppointmentManagement", "type": "load", "error": "页面文件结构不完整", "severity": "high"}, {"page": "Dashboard", "type": "load", "error": "页面文件结构不完整", "severity": "high"}, {"page": "Dashboard", "type": "elements", "error": "缺少元素: 今日营业额, 客户数量, 预约数量", "severity": "medium"}, {"page": "Dashboard", "type": "search", "error": "缺少搜索方法", "severity": "medium"}, {"page": "Dashboard", "type": "data", "error": "缺少计算属性", "severity": "high"}, {"page": "FinanceOverview", "type": "load", "error": "页面文件结构不完整", "severity": "high"}], "fixes": [{"page": "ServiceManagement", "type": "load", "description": "修复了load问题", "timestamp": "2025-07-28T13:09:53.386Z"}, {"page": "TherapistManagement", "type": "load", "description": "修复了load问题", "timestamp": "2025-07-28T13:09:53.386Z"}, {"page": "CustomerManagement", "type": "load", "description": "修复了load问题", "timestamp": "2025-07-28T13:09:53.386Z"}, {"page": "AppointmentManagement", "type": "load", "description": "修复了load问题", "timestamp": "2025-07-28T13:09:53.386Z"}, {"page": "Dashboard", "type": "load", "description": "修复了load问题", "timestamp": "2025-07-28T13:09:53.387Z"}, {"page": "Dashboard", "type": "elements", "description": "修复了elements问题", "timestamp": "2025-07-28T13:09:53.387Z"}, {"page": "Dashboard", "type": "search", "description": "修复了search问题", "timestamp": "2025-07-28T13:09:53.387Z"}, {"page": "Dashboard", "type": "data", "description": "修复了data问题", "timestamp": "2025-07-28T13:09:53.387Z"}, {"page": "FinanceOverview", "type": "load", "description": "修复了load问题", "timestamp": "2025-07-28T13:09:53.387Z"}]}