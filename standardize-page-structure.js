#!/usr/bin/env node

/**
 * 🎯 页面结构标准化脚本
 * 用途: 将所有页面调整为服务管理页面的标准结构
 * 作者: AI助手
 * 日期: 2025-01-27
 */

const fs = require('fs');
const path = require('path');

// 颜色定义
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

// 页面配置映射
const PAGE_CONFIGS = {
  'TherapistManagement.vue': {
    title: '技师管理',
    columns: [
      { field: 'name', label: '技师信息', flex: '2.2', searchable: true, sortable: true },
      { field: 'phone', label: '联系方式', flex: '1', searchable: true, sortable: true },
      { field: 'rating', label: '绩效评价', flex: '1', searchable: true, sortable: true },
      { field: 'joinDate', label: '入职时间', flex: '0.9', searchable: true, sortable: true },
      { field: 'status', label: '状态', flex: '0.9', searchable: false, sortable: false, hasFilter: true },
      { field: 'actions', label: '操作', flex: '2', searchable: false, sortable: false, hasAdd: true }
    ],
    addButtonText: '新增技师'
  },
  'CustomerManagement.vue': {
    title: '客户管理',
    columns: [
      { field: 'name', label: '客户信息', flex: '2.2', searchable: true, sortable: true },
      { field: 'phone', label: '联系方式', flex: '1', searchable: true, sortable: true },
      { field: 'level', label: '客户等级', flex: '1', searchable: true, sortable: true },
      { field: 'points', label: '积分余额', flex: '0.9', searchable: true, sortable: true },
      { field: 'status', label: '状态', flex: '0.9', searchable: false, sortable: false, hasFilter: true },
      { field: 'actions', label: '操作', flex: '2', searchable: false, sortable: false, hasAdd: true }
    ],
    addButtonText: '新增客户'
  },
  'AppointmentManagement.vue': {
    title: '预约管理',
    columns: [
      { field: 'customer', label: '客户信息', flex: '2.2', searchable: true, sortable: true },
      { field: 'service', label: '服务项目', flex: '1', searchable: true, sortable: true },
      { field: 'therapist', label: '技师', flex: '1', searchable: true, sortable: true },
      { field: 'time', label: '预约时间', flex: '0.9', searchable: true, sortable: true },
      { field: 'status', label: '状态', flex: '0.9', searchable: false, sortable: false, hasFilter: true },
      { field: 'actions', label: '操作', flex: '2', searchable: false, sortable: false, hasAdd: true }
    ],
    addButtonText: '新增预约'
  },
  'Dashboard.vue': {
    title: '仪表板',
    columns: [
      { field: 'metric', label: '业务指标', flex: '2.2', searchable: true, sortable: true },
      { field: 'today', label: '今日数据', flex: '1', searchable: true, sortable: true },
      { field: 'week', label: '本周数据', flex: '1', searchable: true, sortable: true },
      { field: 'month', label: '本月数据', flex: '0.9', searchable: true, sortable: true },
      { field: 'trend', label: '趋势', flex: '0.9', searchable: false, sortable: false, hasFilter: true },
      { field: 'actions', label: '操作', flex: '2', searchable: false, sortable: false, hasAdd: true }
    ],
    addButtonText: '新增指标'
  },
  'FinanceOverview.vue': {
    title: '财务概览',
    columns: [
      { field: 'item', label: '财务项目', flex: '2.2', searchable: true, sortable: true },
      { field: 'income', label: '收入', flex: '1', searchable: true, sortable: true },
      { field: 'expense', label: '支出', flex: '1', searchable: true, sortable: true },
      { field: 'profit', label: '利润', flex: '0.9', searchable: true, sortable: true },
      { field: 'status', label: '状态', flex: '0.9', searchable: false, sortable: false, hasFilter: true },
      { field: 'actions', label: '操作', flex: '2', searchable: false, sortable: false, hasAdd: true }
    ],
    addButtonText: '新增记录'
  }
};

/**
 * 生成标准化的页面模板
 */
function generateStandardTemplate(pageConfig) {
  const { title, columns, addButtonText } = pageConfig;
  
  // 生成列标题HTML
  const headerColumns = columns.map(col => {
    if (col.searchable && col.sortable) {
      return `            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.${col.field} }" style="flex: ${col.flex};">
              <!-- 搜索模式 -->
              <div v-if="searchModes.${col.field}" class="header-search-container" @mouseleave="handleSearchMouseLeave('${col.field}')">
                <input
                  :ref="el => searchInputRefs.${col.field} = el"
                  type="text"
                  placeholder="🔍 搜索${col.label}..."
                  v-model="searchValues.${col.field}"
                  @input="handleSearchInput('${col.field}')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('${col.field}')"
                  @keydown.enter="handleSearchEnter($event, '${col.field}')"
                  @keydown.esc="exitSearchMode('${col.field}')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('${col.field}')">
                <span class="header-text">${col.label}</span>
              </div>
              <!-- 动态按钮 -->
              <button v-if="searchValues.${col.field} && searchValues.${col.field}.trim()" @click="exitSearchMode('${col.field}')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.${col.field}.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('${col.field}')" :disabled="sortButtonStates.${col.field}.disabled" title="排序${col.label}">
                <span class="sort-indicator" :class="getSortClass('${col.field}')">
                  {{ getSortIcon('${col.field}') }}
                </span>
              </button>
            </div>`;
    } else if (col.hasFilter) {
      return `            <div class="header-cell" style="flex: ${col.flex};">
              <div class="header-normal-container">
                <span class="header-text">${col.label}</span>
              </div>
              <button
                class="status-filter-btn"
                @click="toggleStatusFilter"
                :class="getStatusFilterClass()"
                :title="getStatusFilterTitle()">
                <span class="filter-icon">{{ getStatusFilterIcon() }}</span>
                <span class="filter-text">{{ getStatusFilterLabel() }}</span>
              </button>
            </div>`;
    } else if (col.hasAdd) {
      return `            <div class="header-cell" style="flex: ${col.flex};">
              <span>${col.label}</span>
              <button class="header-add-btn-small" @click="showAddModal" title="${addButtonText}">
                <span class="add-icon">➕</span>
                <span class="add-text">新增</span>
              </button>
            </div>`;
    } else {
      return `            <div class="header-cell" style="flex: ${col.flex};">
              <span class="header-text">${col.label}</span>
            </div>`;
    }
  }).join('\n');

  return `<template>
  <div class="picasso-services">
    <!-- 科幻通知组件 -->
    <SciFiNotification ref="notification" />

    <!-- 毕加索风格数据表格 -->
    <div class="data-cubism" :style="{ height: dynamicTableHeight + 'px' }">
      <div class="table-container">
        <!-- 集成操作功能的智能表头 -->
        <div class="smart-table-header">
          <!-- 列标题（支持搜索和排序） -->
          <div class="header-columns">
${headerColumns}
          </div>
        </div>

        <!-- 数据行容器 -->
        <div class="table-body" :style="{ height: (dynamicTableHeight - 60) + 'px' }">
          <!-- 数据行将在这里动态生成 -->
          <div v-for="(item, index) in paginatedData" :key="item.id" class="data-row">
            <!-- 数据行内容将根据具体页面需求实现 -->
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import SciFiNotification from '@/components/SciFiNotification.vue';

export default {
  name: '${title.replace('管理', 'Management')}',
  components: {
    SciFiNotification
  },
  setup() {
    // 基础响应式数据
    const notification = ref(null);
    const dynamicTableHeight = ref(600);
    
    // 搜索功能数据
    const searchModes = reactive({
${columns.filter(col => col.searchable).map(col => `      ${col.field}: false`).join(',\n')}
    });
    
    const searchValues = reactive({
${columns.filter(col => col.searchable).map(col => `      ${col.field}: ''`).join(',\n')}
    });
    
    const searchInputRefs = reactive({
${columns.filter(col => col.searchable).map(col => `      ${col.field}: null`).join(',\n')}
    });
    
    // 排序功能数据
    const sortButtonStates = reactive({
${columns.filter(col => col.sortable).map(col => `      ${col.field}: { loading: false, disabled: false }`).join(',\n')}
    });
    
    // 过渡动画数据
    const isTransitioning = reactive({
${columns.filter(col => col.searchable).map(col => `      ${col.field}: false`).join(',\n')}
    });
    
    // 数据和状态
    const paginatedData = ref([]);
    
    // 方法定义
    const handleClickToSearch = (field) => {
      searchModes[field] = true;
      nextTick(() => {
        if (searchInputRefs[field]) {
          searchInputRefs[field].focus();
        }
      });
    };
    
    const handleSearchInput = (field) => {
      // 实现搜索逻辑
    };
    
    const exitSearchMode = (field) => {
      searchModes[field] = false;
      searchValues[field] = '';
    };
    
    const handleSort = (field) => {
      // 实现排序逻辑
    };
    
    const getSortClass = (field) => {
      return '';
    };
    
    const getSortIcon = (field) => {
      return '↕';
    };
    
    const toggleStatusFilter = () => {
      // 实现状态过滤逻辑
    };
    
    const getStatusFilterClass = () => {
      return '';
    };
    
    const getStatusFilterTitle = () => {
      return '状态筛选';
    };
    
    const getStatusFilterIcon = () => {
      return '🔽';
    };
    
    const getStatusFilterLabel = () => {
      return '全部';
    };
    
    const showAddModal = () => {
      // 实现新增模态框逻辑
    };
    
    // 生命周期
    onMounted(() => {
      // 初始化逻辑
    });
    
    return {
      // 数据
      notification,
      dynamicTableHeight,
      searchModes,
      searchValues,
      searchInputRefs,
      sortButtonStates,
      isTransitioning,
      paginatedData,
      
      // 方法
      handleClickToSearch,
      handleSearchInput,
      exitSearchMode,
      handleSort,
      getSortClass,
      getSortIcon,
      toggleStatusFilter,
      getStatusFilterClass,
      getStatusFilterTitle,
      getStatusFilterIcon,
      getStatusFilterLabel,
      showAddModal
    };
  }
};
</script>

<style scoped>
/* 这里将包含服务管理页面的完整CSS样式 */
/* 由于篇幅限制，样式部分需要从ServiceManagement.vue复制 */
</style>`;
}

/**
 * 标准化指定页面
 */
function standardizePage(pageName) {
  console.log(`${colors.blue}🔧 开始标准化页面: ${pageName}${colors.reset}`);
  
  const pageConfig = PAGE_CONFIGS[pageName];
  if (!pageConfig) {
    console.log(`${colors.red}❌ 未找到页面配置: ${pageName}${colors.reset}`);
    return false;
  }
  
  const filePath = path.resolve(`admin/src/views/${pageName}`);
  if (!fs.existsSync(filePath)) {
    console.log(`${colors.red}❌ 文件不存在: ${filePath}${colors.reset}`);
    return false;
  }
  
  // 生成标准化模板
  const standardTemplate = generateStandardTemplate(pageConfig);
  
  // 备份原文件
  const backupPath = filePath.replace('.vue', '_Backup.vue');
  fs.copyFileSync(filePath, backupPath);
  console.log(`${colors.yellow}📋 已备份原文件: ${backupPath}${colors.reset}`);
  
  // 写入标准化模板
  fs.writeFileSync(filePath, standardTemplate, 'utf8');
  console.log(`${colors.green}✅ 页面标准化完成: ${pageName}${colors.reset}`);
  
  return true;
}

// 主函数
function main() {
  console.log(`${colors.blue}🚀 页面结构标准化工具启动${colors.reset}\n`);
  
  const targetPages = process.argv.slice(2);
  
  if (targetPages.length === 0) {
    console.log(`${colors.yellow}📋 使用方法: node standardize-page-structure.js <页面名称>${colors.reset}`);
    console.log(`${colors.cyan}📋 可用页面:${colors.reset}`);
    Object.keys(PAGE_CONFIGS).forEach(page => {
      console.log(`   - ${page}`);
    });
    return;
  }
  
  let successCount = 0;
  let totalCount = targetPages.length;
  
  targetPages.forEach(pageName => {
    if (standardizePage(pageName)) {
      successCount++;
    }
  });
  
  console.log(`\n${colors.magenta}📊 标准化结果统计:${colors.reset}`);
  console.log(`   成功: ${successCount}/${totalCount}`);
  console.log(`   失败: ${totalCount - successCount}/${totalCount}`);
  
  if (successCount === totalCount) {
    console.log(`\n${colors.green}🎉 所有页面标准化完成！${colors.reset}`);
  } else {
    console.log(`\n${colors.red}❌ 部分页面标准化失败，请检查错误信息${colors.reset}`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { standardizePage, PAGE_CONFIGS };
