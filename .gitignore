# 壹心堂项目 Git 忽略文件

# 依赖文件
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# 构建输出
dist/
build/
.next/
.nuxt/

# 环境变量
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# 日志文件
*.log
logs/

# 运行时文件
*.pid
*.seed
*.pid.lock

# 缓存文件
.cache/
.parcel-cache/
.eslintcache

# IDE 文件
.vscode/
.idea/
*.swp
*.swo
*~

# 操作系统文件
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# 临时文件
*.tmp
*.temp
temp/
tmp/

# 测试覆盖率
coverage/
.nyc_output/

# 备份文件
*.bak
*.backup

# 压缩文件
*.zip
*.tar.gz
*.rar

# 本地配置文件
config.local.js
settings.local.json
local_settings.py

# 用户上传文件
uploads/
user-data/

# 数据库文件
*.db
*.sqlite
*.sqlite3

# 密钥文件
*.key
*.pem
*.p12
*.pfx

# 本地开发文件
.local/
local/

# Python 相关
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
venv/
env/
ENV/
env.bak/
venv.bak/
.venv/
.env/

# Django 相关
*.log
local_settings.py
db.sqlite3
db.sqlite3-journal
media/
staticfiles/

# 启动日志
/tmp/django_startup.log
/tmp/vite_startup.log
/tmp/taro_startup.log
