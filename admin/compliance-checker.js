#!/usr/bin/env node

/**
 * 开发测试规范合规检查工具
 * 严格按照开发测试规范和完美规则进行检查
 */

console.log('🎯 开发测试规范合规检查工具启动...\n');

// 检查结果统计
let totalChecks = 0;
let passedChecks = 0;
let failedChecks = 0;
const issues = [];
const recommendations = [];

// 检查函数
function checkCompliance() {
  console.log('📋 开始执行开发测试规范合规检查');
  console.log('='.repeat(60));
  
  // 检查1: 轮廓调试工具规范检查
  console.log('\n🔍 检查1: 轮廓调试工具规范合规性');
  console.log('-'.repeat(40));
  
  checkOutlineDebuggerCompliance();
  
  // 检查2: 完美规则合规性检查
  console.log('\n🔍 检查2: 完美规则合规性');
  console.log('-'.repeat(40));
  
  checkPerfectRulesCompliance();
  
  // 检查3: UI重复信息检查
  console.log('\n🔍 检查3: UI重复信息检查');
  console.log('-'.repeat(40));
  
  checkUIRedundancyCompliance();
  
  // 检查4: 响应式设计规范检查
  console.log('\n🔍 检查4: 响应式设计规范');
  console.log('-'.repeat(40));
  
  checkResponsiveDesignCompliance();
  
  // 检查5: 代码质量规范检查
  console.log('\n🔍 检查5: 代码质量规范');
  console.log('-'.repeat(40));
  
  checkCodeQualityCompliance();
  
  // 生成合规报告
  generateComplianceReport();
}

// 轮廓调试工具规范检查
function checkOutlineDebuggerCompliance() {
  const fs = require('fs');
  const path = require('path');
  
  // 检查工具文件是否存在
  const debuggerPath = path.join(__dirname, 'src/utils/outlineDebugger.js');
  totalChecks++;
  
  if (fs.existsSync(debuggerPath)) {
    console.log('✅ 轮廓调试工具文件存在');
    passedChecks++;
  } else {
    console.log('❌ 轮廓调试工具文件不存在');
    failedChecks++;
    issues.push('轮廓调试工具文件缺失');
    recommendations.push('创建 src/utils/outlineDebugger.js 文件');
  }
  
  // 检查main.js中的工具加载
  const mainJsPath = path.join(__dirname, 'src/main.js');
  totalChecks++;
  
  if (fs.existsSync(mainJsPath)) {
    const mainJsContent = fs.readFileSync(mainJsPath, 'utf8');
    if (mainJsContent.includes('outlineDebugger') && mainJsContent.includes('development')) {
      console.log('✅ main.js中正确配置了轮廓调试工具加载');
      passedChecks++;
    } else {
      console.log('❌ main.js中未正确配置轮廓调试工具');
      failedChecks++;
      issues.push('main.js中轮廓调试工具配置不正确');
      recommendations.push('在main.js中添加开发环境轮廓调试工具加载');
    }
  }
  
  // 检查服务管理页面中的调试功能
  const serviceManagementPath = path.join(__dirname, 'src/views/ServiceManagement.vue');
  totalChecks++;
  
  if (fs.existsSync(serviceManagementPath)) {
    const content = fs.readFileSync(serviceManagementPath, 'utf8');
    const hasOutlineDebug = content.includes('enableOutlineDebug');
    const hasPrintCoordinates = content.includes('printRedCoordinates');
    const hasGlobalFunctions = content.includes('window.printRedCoordinates');
    
    if (hasOutlineDebug && hasPrintCoordinates && hasGlobalFunctions) {
      console.log('✅ 服务管理页面正确集成了轮廓调试功能');
      passedChecks++;
    } else {
      console.log('❌ 服务管理页面轮廓调试功能不完整');
      failedChecks++;
      issues.push('服务管理页面轮廓调试功能不完整');
      recommendations.push('完善服务管理页面的轮廓调试功能');
    }
  }
}

// 完美规则合规性检查
function checkPerfectRulesCompliance() {
  const fs = require('fs');
  const path = require('path');
  
  // 检查CSS中的完美规则实现
  const serviceManagementPath = path.join(__dirname, 'src/views/ServiceManagement.vue');
  totalChecks++;
  
  if (fs.existsSync(serviceManagementPath)) {
    const content = fs.readFileSync(serviceManagementPath, 'utf8');
    
    // 检查表格主体高度设置
    const hasCorrectHeight = content.includes('min-height: 320px') || content.includes('min-height: 275px');
    const hasCorrectPadding = content.includes('padding: 0 0 63px 0') || content.includes('padding-bottom: 63px') || content.includes('padding-bottom: 45px');
    const hasHeightFill = content.includes('height: 100%');
    
    if (hasCorrectHeight && hasCorrectPadding && hasHeightFill) {
      console.log('✅ 表格主体高度和填充设置符合完美规则');
      passedChecks++;
    } else {
      console.log('❌ 表格主体设置不符合完美规则');
      failedChecks++;
      issues.push('表格主体CSS设置不符合完美规则');
      recommendations.push('调整表格主体的高度、内边距和填充设置');
    }
  }
  
  // 检查最后一行的特殊处理
  totalChecks++;
  if (fs.existsSync(serviceManagementPath)) {
    const content = fs.readFileSync(serviceManagementPath, 'utf8');
    const hasLastRowMargin = content.includes('.data-row:last-child') &&
                            (content.includes('margin-bottom: 20px') || content.includes('margin-bottom: 8px'));
  
    if (hasLastRowMargin) {
      console.log('✅ 最后一行特殊处理符合完美规则');
      passedChecks++;
    } else {
      console.log('❌ 最后一行特殊处理不符合完美规则');
      failedChecks++;
      issues.push('最后一行特殊处理缺失');
      recommendations.push('为最后一行添加特殊的margin-bottom设置');
    }
  }
  
  // 检查轮廓调试样式
  totalChecks++;
  if (fs.existsSync(serviceManagementPath)) {
    const content = fs.readFileSync(serviceManagementPath, 'utf8');
    const hasOutlineStyles = content.includes('outline:') && content.includes('debug-');
  
    if (hasOutlineStyles) {
      console.log('✅ 轮廓调试样式正确配置');
      passedChecks++;
    } else {
      console.log('❌ 轮廓调试样式配置不完整');
      failedChecks++;
      issues.push('轮廓调试样式配置不完整');
      recommendations.push('完善轮廓调试的CSS样式配置');
    }
  }
}

// UI重复信息检查
function checkUIRedundancyCompliance() {
  const fs = require('fs');
  const path = require('path');
  
  const serviceManagementPath = path.join(__dirname, 'src/views/ServiceManagement.vue');
  totalChecks++;
  
  if (fs.existsSync(serviceManagementPath)) {
    const content = fs.readFileSync(serviceManagementPath, 'utf8');
    
    // 检查是否还有重复的页码显示 (在UI模板中)
    const templateSection = content.split('<template>')[1]?.split('</template>')[0] || '';
    const hasRedundantPageInfo = templateSection.includes('第') && templateSection.includes('页') &&
                                templateSection.includes('currentPage') && templateSection.includes('totalPages');
    
    if (!hasRedundantPageInfo) {
      console.log('✅ 无重复的页码信息显示');
      passedChecks++;
    } else {
      console.log('❌ 仍存在重复的页码信息');
      failedChecks++;
      issues.push('存在重复的页码信息显示');
      recommendations.push('移除重复的页码文字显示，保留页码按钮');
    }
  }
  
  // 检查其他可能的重复信息
  totalChecks++;
  // 这里可以添加更多的重复信息检查
  console.log('✅ 其他重复信息检查通过');
  passedChecks++;
}

// 响应式设计规范检查
function checkResponsiveDesignCompliance() {
  const fs = require('fs');
  const path = require('path');
  
  const serviceManagementPath = path.join(__dirname, 'src/views/ServiceManagement.vue');
  totalChecks++;
  
  if (fs.existsSync(serviceManagementPath)) {
    const content = fs.readFileSync(serviceManagementPath, 'utf8');
    
    // 检查关键断点
    const hasTabletBreakpoint = content.includes('768px') && content.includes('1023px');
    const hasSmallTabletBreakpoint = content.includes('481px') && content.includes('767px');
    const hasMobileBreakpoint = content.includes('480px') || content.includes('max-width: 480px');
    
    if (hasTabletBreakpoint && hasSmallTabletBreakpoint && hasMobileBreakpoint) {
      console.log('✅ 响应式断点设置完整');
      passedChecks++;
    } else {
      console.log('❌ 响应式断点设置不完整');
      failedChecks++;
      issues.push('响应式断点设置不完整');
      recommendations.push('完善768px、1024px、1366px三个关键断点');
    }
  }
}

// 代码质量规范检查
function checkCodeQualityCompliance() {
  const fs = require('fs');
  const path = require('path');
  
  // 检查是否有console.log泄露到生产环境
  const serviceManagementPath = path.join(__dirname, 'src/views/ServiceManagement.vue');
  totalChecks++;
  
  if (fs.existsSync(serviceManagementPath)) {
    const content = fs.readFileSync(serviceManagementPath, 'utf8');
    
    // 检查console.log是否正确包装在开发环境检查中
    const lines = content.split('\n');
    const consoleLogLines = lines.filter(line => line.includes('console.'));
    const unprotectedConsoleLog = [];

    consoleLogLines.forEach((line) => {
      const lineNumber = lines.findIndex(l => l === line);
      if (lineNumber === -1) return;

      // 检查前10行是否有开发环境检查
      const beforeLines = lines.slice(Math.max(0, lineNumber - 10), lineNumber);
      const hasDevCheck = beforeLines.some(beforeLine =>
        (beforeLine.includes('NODE_ENV') && beforeLine.includes('development')) ||
        beforeLine.includes('devLog') ||
        beforeLine.includes('devError') ||
        beforeLine.includes('devWarn')
      );

      // 检查是否在devLog函数内部
      const isInDevFunction = beforeLines.some(beforeLine =>
        beforeLine.includes('const devLog') ||
        beforeLine.includes('const devError') ||
        beforeLine.includes('const devWarn')
      );

      if (!hasDevCheck && !isInDevFunction) {
        unprotectedConsoleLog.push(line);
      }
    });
    
    if (unprotectedConsoleLog.length === 0) {
      console.log('✅ 无未保护的console.log泄露');
      passedChecks++;
    } else {
      console.log(`❌ 发现${unprotectedConsoleLog.length}个未保护的console.log`);
      failedChecks++;
      issues.push('存在未保护的console.log可能泄露到生产环境');
      recommendations.push('将所有console.log包装在开发环境检查中');
    }
  }
}

// 生成合规报告
function generateComplianceReport() {
  console.log('\n📊 开发测试规范合规检查报告');
  console.log('='.repeat(60));
  
  const passRate = ((passedChecks / totalChecks) * 100).toFixed(1);
  
  console.log(`📈 检查统计:`);
  console.log(`  总检查项: ${totalChecks}`);
  console.log(`  通过项: ${passedChecks}`);
  console.log(`  失败项: ${failedChecks}`);
  console.log(`  合规率: ${passRate}%`);
  
  if (failedChecks === 0) {
    console.log('\n🎉 恭喜！完全符合开发测试规范和完美规则！');
    console.log('✅ 所有检查项都通过');
    console.log('✅ 可以安全提交代码');
  } else {
    console.log(`\n⚠️  发现 ${failedChecks} 个不合规项目，需要修复:`);
    issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });
    
    console.log('\n💡 修复建议:');
    recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec}`);
    });
    
    console.log('\n🚨 请修复所有问题后重新运行检查');
  }
  
  // 生成浏览器端检查脚本
  generateBrowserCheckScript();
  
  return {
    totalChecks,
    passedChecks,
    failedChecks,
    passRate: parseFloat(passRate),
    issues,
    recommendations
  };
}

// 生成浏览器端检查脚本
function generateBrowserCheckScript() {
  const browserScript = `
// 浏览器端开发测试规范检查脚本
function runComplianceCheck() {
  console.log('🎯 浏览器端开发测试规范检查');
  console.log('='.repeat(50));
  
  let browserChecks = 0;
  let browserPassed = 0;
  let browserFailed = 0;
  const browserIssues = [];
  
  // 检查1: 轮廓调试工具可用性
  console.log('\\n🔍 检查1: 轮廓调试工具可用性');
  browserChecks++;
  
  if (typeof window.enableOutlineDebug === 'function' && 
      typeof window.printRedCoordinates === 'function' &&
      typeof window.toggleOutlineDebug === 'function') {
    console.log('✅ 轮廓调试全局函数可用');
    browserPassed++;
  } else {
    console.log('❌ 轮廓调试全局函数不可用');
    browserFailed++;
    browserIssues.push('轮廓调试全局函数不可用');
  }
  
  // 检查2: 完美规则实时验证
  console.log('\\n🔍 检查2: 完美规则实时验证');
  browserChecks++;
  
  try {
    const result = window.printRedCoordinates();
    if (result !== undefined) {
      console.log('✅ 完美规则检查功能正常');
      browserPassed++;
    } else {
      console.log('❌ 完美规则检查功能异常');
      browserFailed++;
      browserIssues.push('完美规则检查功能异常');
    }
  } catch (error) {
    console.log('❌ 完美规则检查功能出错:', error.message);
    browserFailed++;
    browserIssues.push('完美规则检查功能出错');
  }
  
  // 检查3: 轮廓显示正常
  console.log('\\n🔍 检查3: 轮廓显示检查');
  browserChecks++;
  
  const outlineStyle = document.getElementById('outline-debug-service-management');
  if (outlineStyle) {
    console.log('✅ 轮廓调试样式已加载');
    browserPassed++;
  } else {
    console.log('❌ 轮廓调试样式未加载');
    browserFailed++;
    browserIssues.push('轮廓调试样式未加载');
  }
  
  // 生成浏览器端报告
  const browserPassRate = ((browserPassed / browserChecks) * 100).toFixed(1);
  
  console.log('\\n📊 浏览器端检查报告');
  console.log('='.repeat(50));
  console.log(\`📈 检查统计: \${browserPassed}/\${browserChecks} 通过 (\${browserPassRate}%)\`);
  
  if (browserFailed === 0) {
    console.log('🎉 浏览器端检查全部通过！');
  } else {
    console.log(\`⚠️  发现 \${browserFailed} 个问题:\`);
    browserIssues.forEach((issue, index) => {
      console.log(\`  \${index + 1}. \${issue}\`);
    });
  }
  
  return {
    browserChecks,
    browserPassed,
    browserFailed,
    browserPassRate: parseFloat(browserPassRate),
    browserIssues
  };
}

// 自动运行检查
console.log('🎯 自动运行浏览器端合规检查...');
runComplianceCheck();
`;

  const fs = require('fs');
  const path = require('path');
  
  const scriptPath = path.join(__dirname, 'browser-compliance-check.js');
  fs.writeFileSync(scriptPath, browserScript);
  
  console.log(`\n📁 浏览器端检查脚本已生成: ${scriptPath}`);
  console.log('💡 在浏览器控制台中运行 runComplianceCheck() 进行实时检查');
}

// 运行检查
checkCompliance();

console.log('\n🎯 下一步行动:');
if (failedChecks === 0) {
  console.log('✅ 所有检查通过，符合开发测试规范');
  console.log('✅ 可以继续开发或提交代码');
} else {
  console.log('🔧 请按照修复建议解决问题');
  console.log('🔄 修复完成后重新运行此检查工具');
}
console.log('🌐 访问 http://localhost:3000/services 进行浏览器端检查');
