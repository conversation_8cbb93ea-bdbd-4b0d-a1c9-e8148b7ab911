<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>坐标分析测试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .coordinates-display {
            position: fixed;
            top: 10px;
            right: 10px;
            background: rgba(0,0,0,0.8);
            color: white;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            z-index: 9999;
        }
        
        .test-section {
            margin-bottom: 30px;
            border: 2px solid #ddd;
            border-radius: 8px;
            padding: 15px;
        }
        
        .test-section h3 {
            margin-top: 0;
            color: #333;
        }
        
        /* 模拟翻页组件样式 */
        .pagination-container {
            margin-top: 25px; /* 增加间距避免遮挡 */
            padding: 12px 18px;
            background: linear-gradient(135deg, rgba(139, 92, 246, 0.1), rgba(168, 85, 247, 0.1));
            border-radius: 12px;
            border: 2px solid rgba(192, 132, 252, 0.4);
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 15px;
        }
        
        .pagination-controls {
            display: flex;
            align-items: center;
            gap: 20px;
            flex-wrap: wrap;
        }
        
        .page-navigation {
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .page-btn {
            padding: 8px 12px;
            background: #6366f1;
            color: white;
            border: none;
            border-radius: 6px;
            cursor: pointer;
            font-size: 13px;
        }
        
        /* 模拟表格样式 - 修复后的样式 */
        .table-container {
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: hidden;
            margin-bottom: 8px; /* 为横向滚动条预留空间 */
            padding-bottom: 8px; /* 响应式下的额外间距 */
        }

        /* 模拟横向滚动条 */
        .table-container.with-horizontal-scroll {
            overflow-x: auto;
        }

        /* 自定义横向滚动条样式 */
        .table-container::-webkit-scrollbar {
            height: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: rgba(155, 89, 182, 0.1);
            border-radius: 4px;
            margin: 0 5px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: linear-gradient(90deg, #9b59b6, #8e44ad);
            border-radius: 4px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: linear-gradient(90deg, #8e44ad, #7d3c98);
            box-shadow: 0 2px 4px rgba(142, 68, 173, 0.3);
        }
        
        .table-body {
            min-height: 350px;
            max-height: calc(100vh - 320px);
            overflow-y: auto;
            overflow-x: hidden;
            padding-bottom: 5px;
            margin-bottom: 10px;
        }
        
        .data-row {
            display: flex;
            min-height: 60px;
            border-bottom: 1px solid #eee;
            align-items: center;
        }
        
        .data-row:last-child {
            border-bottom: none;
            background: #fffbeb;
        }
        
        .data-cell {
            padding: 10px;
            border-right: 1px solid #eee;
            overflow: hidden;
        }
        
        .data-cell:last-child {
            border-right: none;
        }
        
        .data-cell[data-flex="2.2"] { flex: 2.2; }
        .data-cell[data-flex="1"] { flex: 1; }
        .data-cell[data-flex="0.9"] { flex: 0.9; }
        .data-cell[data-flex="2"] { flex: 2; }
        
        .highlight-element {
            outline: 3px solid red !important;
            background: rgba(255, 0, 0, 0.1) !important;
        }
        
        .coordinate-info {
            position: absolute;
            background: rgba(255, 255, 0, 0.9);
            color: black;
            padding: 5px;
            border-radius: 3px;
            font-size: 11px;
            pointer-events: none;
            z-index: 1000;
        }
    </style>
</head>
<body>
    <div class="coordinates-display" id="coordinates">
        鼠标坐标: (0, 0)<br>
        窗口尺寸: 0 x 0<br>
        滚动位置: (0, 0)
    </div>

    <div class="test-container">
        <h1>翻页组件和表格坐标分析</h1>
        
        <div class="test-section">
            <h3>1. 表格容器测试</h3>
            <div class="table-container" id="tableContainer">
                <div class="table-body" id="tableBody">
                    <div class="data-row">
                        <div class="data-cell" data-flex="2.2">服务信息 1</div>
                        <div class="data-cell" data-flex="1">¥100</div>
                        <div class="data-cell" data-flex="1">¥50</div>
                        <div class="data-cell" data-flex="0.9">60分钟</div>
                        <div class="data-cell" data-flex="0.9">上架</div>
                        <div class="data-cell" data-flex="2">编辑 删除</div>
                    </div>
                    <div class="data-row">
                        <div class="data-cell" data-flex="2.2">服务信息 2</div>
                        <div class="data-cell" data-flex="1">¥120</div>
                        <div class="data-cell" data-flex="1">¥60</div>
                        <div class="data-cell" data-flex="0.9">90分钟</div>
                        <div class="data-cell" data-flex="0.9">上架</div>
                        <div class="data-cell" data-flex="2">编辑 删除</div>
                    </div>
                    <div class="data-row">
                        <div class="data-cell" data-flex="2.2">服务信息 3</div>
                        <div class="data-cell" data-flex="1">¥150</div>
                        <div class="data-cell" data-flex="1">¥75</div>
                        <div class="data-cell" data-flex="0.9">120分钟</div>
                        <div class="data-cell" data-flex="0.9">上架</div>
                        <div class="data-cell" data-flex="2">编辑 删除</div>
                    </div>
                    <div class="data-row">
                        <div class="data-cell" data-flex="2.2">服务信息 4</div>
                        <div class="data-cell" data-flex="1">¥200</div>
                        <div class="data-cell" data-flex="1">¥100</div>
                        <div class="data-cell" data-flex="0.9">150分钟</div>
                        <div class="data-cell" data-flex="0.9">上架</div>
                        <div class="data-cell" data-flex="2">编辑 删除</div>
                    </div>
                    <div class="data-row" id="lastRow">
                        <div class="data-cell" data-flex="2.2">服务信息 5 (最后一行)</div>
                        <div class="data-cell" data-flex="1">¥250</div>
                        <div class="data-cell" data-flex="1">¥125</div>
                        <div class="data-cell" data-flex="0.9">180分钟</div>
                        <div class="data-cell" data-flex="0.9">上架</div>
                        <div class="data-cell" data-flex="2">编辑 删除</div>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>2. 翻页组件测试</h3>
            <div class="pagination-container" id="paginationContainer">
                <div class="pagination-info">
                    <span>共 25 条记录，第 1 / 5 页</span>
                </div>
                <div class="pagination-controls" id="paginationControls">
                    <div class="page-size-selector">
                        <label>每页显示：</label>
                        <select>
                            <option>5条</option>
                            <option>10条</option>
                            <option>20条</option>
                        </select>
                    </div>
                    <div class="page-navigation" id="pageNavigation">
                        <button class="page-btn">‹ 上一页</button>
                        <button class="page-btn">1</button>
                        <button class="page-btn">2</button>
                        <button class="page-btn">3</button>
                        <button class="page-btn">下一页 ›</button>
                    </div>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>3. 坐标信息面板</h3>
            <div id="coordinateInfo">
                <p><strong>表格容器坐标:</strong> <span id="tableCoords">未检测</span></p>
                <p><strong>最后一行坐标:</strong> <span id="lastRowCoords">未检测</span></p>
                <p><strong>翻页组件坐标:</strong> <span id="paginationCoords">未检测</span></p>
                <p><strong>是否有横向滚动:</strong> <span id="horizontalScroll">未检测</span></p>
                <p><strong>是否有纵向滚动:</strong> <span id="verticalScroll">未检测</span></p>
            </div>
            
            <button onclick="analyzeCoordinates()" style="padding: 10px 20px; background: #6366f1; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 10px;">
                分析坐标
            </button>
            
            <button onclick="highlightElements()" style="padding: 10px 20px; background: #dc2626; color: white; border: none; border-radius: 5px; cursor: pointer; margin-top: 10px; margin-left: 10px;">
                高亮关键元素
            </button>
        </div>
    </div>

    <script>
        // 实时显示鼠标坐标
        document.addEventListener('mousemove', function(e) {
            const coords = document.getElementById('coordinates');
            coords.innerHTML = `
                鼠标坐标: (${e.clientX}, ${e.clientY})<br>
                窗口尺寸: ${window.innerWidth} x ${window.innerHeight}<br>
                滚动位置: (${window.scrollX}, ${window.scrollY})
            `;
        });

        // 分析坐标函数
        function analyzeCoordinates() {
            const tableContainer = document.getElementById('tableContainer');
            const lastRow = document.getElementById('lastRow');
            const paginationContainer = document.getElementById('paginationContainer');
            const tableBody = document.getElementById('tableBody');
            
            // 获取元素的边界矩形
            const tableRect = tableContainer.getBoundingClientRect();
            const lastRowRect = lastRow.getBoundingClientRect();
            const paginationRect = paginationContainer.getBoundingClientRect();
            
            // 检查滚动情况
            const hasHorizontalScroll = tableBody.scrollWidth > tableBody.clientWidth;
            const hasVerticalScroll = tableBody.scrollHeight > tableBody.clientHeight;
            
            // 更新显示
            document.getElementById('tableCoords').textContent = 
                `left: ${Math.round(tableRect.left)}, top: ${Math.round(tableRect.top)}, width: ${Math.round(tableRect.width)}, height: ${Math.round(tableRect.height)}`;
            
            document.getElementById('lastRowCoords').textContent = 
                `left: ${Math.round(lastRowRect.left)}, top: ${Math.round(lastRowRect.top)}, width: ${Math.round(lastRowRect.width)}, height: ${Math.round(lastRowRect.height)}`;
            
            document.getElementById('paginationCoords').textContent = 
                `left: ${Math.round(paginationRect.left)}, top: ${Math.round(paginationRect.top)}, width: ${Math.round(paginationRect.width)}, height: ${Math.round(paginationRect.height)}`;
            
            document.getElementById('horizontalScroll').textContent = hasHorizontalScroll ? '是' : '否';
            document.getElementById('verticalScroll').textContent = hasVerticalScroll ? '是' : '否';
            
            // 检查元素重叠
            const gap = paginationRect.top - (lastRowRect.top + lastRowRect.height);
            console.log('表格最后一行和翻页组件之间的间距:', gap + 'px');
            
            if (gap < 0) {
                alert('警告：翻页组件与表格最后一行重叠！间距: ' + gap + 'px');
            } else if (gap < 10) {
                alert('注意：翻页组件与表格最后一行间距较小: ' + gap + 'px');
            }
        }

        // 高亮关键元素
        function highlightElements() {
            // 清除之前的高亮
            document.querySelectorAll('.highlight-element').forEach(el => {
                el.classList.remove('highlight-element');
            });
            
            // 高亮关键元素
            document.getElementById('tableContainer').classList.add('highlight-element');
            document.getElementById('lastRow').classList.add('highlight-element');
            document.getElementById('paginationContainer').classList.add('highlight-element');
            
            setTimeout(() => {
                document.querySelectorAll('.highlight-element').forEach(el => {
                    el.classList.remove('highlight-element');
                });
            }, 3000);
        }

        // 页面加载完成后自动分析
        window.addEventListener('load', function() {
            setTimeout(analyzeCoordinates, 1000);
        });

        // 窗口大小改变时重新分析
        window.addEventListener('resize', function() {
            setTimeout(analyzeCoordinates, 500);
        });
    </script>
</body>
</html>
