{"summary": {"totalTests": 72, "passedTests": 0, "failedTests": 72, "skippedTests": 0, "passRate": "0.0", "duration": "98.0秒", "timestamp": "2025-07-19T07:16:41.567Z"}, "resolutionResults": {"1366x768-Laptop": {"total": 8, "passed": 0, "failed": 8, "passRate": "0.0"}, "1920x1080-Desktop": {"total": 8, "passed": 0, "failed": 8, "passRate": "0.0"}, "2560x1440-2K": {"total": 8, "passed": 0, "failed": 8, "passRate": "0.0"}, "3840x2160-4K": {"total": 8, "passed": 0, "failed": 8, "passRate": "0.0"}, "iPhone-SE": {"total": 8, "passed": 0, "failed": 8, "passRate": "0.0"}, "iPhone-11": {"total": 8, "passed": 0, "failed": 8, "passRate": "0.0"}, "Android-Standard": {"total": 8, "passed": 0, "failed": 8, "passRate": "0.0"}, "iPad-Portrait": {"total": 8, "passed": 0, "failed": 8, "passRate": "0.0"}, "iPad-Landscape": {"total": 8, "passed": 0, "failed": 8, "passRate": "0.0"}}, "pageResults": {"服务管理页面": {"total": 9, "passed": 0, "failed": 9, "passRate": "0.0"}, "技师管理页面": {"total": 9, "passed": 0, "failed": 9, "passRate": "0.0"}, "客户管理页面": {"total": 9, "passed": 0, "failed": 9, "passRate": "0.0"}, "预约管理页面": {"total": 9, "passed": 0, "failed": 9, "passRate": "0.0"}, "财务管理页面": {"total": 9, "passed": 0, "failed": 9, "passRate": "0.0"}, "健康贴士页面": {"total": 9, "passed": 0, "failed": 9, "passRate": "0.0"}, "登录页面": {"total": 9, "passed": 0, "failed": 9, "passRate": "0.0"}, "首页仪表板": {"total": 9, "passed": 0, "failed": 9, "passRate": "0.0"}}, "issues": [], "results": [{"page": "服务管理页面", "resolution": "1366x768-<PERSON><PERSON><PERSON>", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "服务管理页面", "resolution": "1920x1080-Desktop", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "服务管理页面", "resolution": "2560x1440-2K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "服务管理页面", "resolution": "3840x2160-4K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "服务管理页面", "resolution": "iPhone-SE", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "服务管理页面", "resolution": "iPhone-11", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "服务管理页面", "resolution": "Android-Standard", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "服务管理页面", "resolution": "iPad-Portrait", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "服务管理页面", "resolution": "iPad-Landscape", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "技师管理页面", "resolution": "1366x768-<PERSON><PERSON><PERSON>", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "技师管理页面", "resolution": "1920x1080-Desktop", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "技师管理页面", "resolution": "2560x1440-2K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "技师管理页面", "resolution": "3840x2160-4K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "技师管理页面", "resolution": "iPhone-SE", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "技师管理页面", "resolution": "iPhone-11", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "技师管理页面", "resolution": "Android-Standard", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "技师管理页面", "resolution": "iPad-Portrait", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "技师管理页面", "resolution": "iPad-Landscape", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "客户管理页面", "resolution": "1366x768-<PERSON><PERSON><PERSON>", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "客户管理页面", "resolution": "1920x1080-Desktop", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "客户管理页面", "resolution": "2560x1440-2K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "客户管理页面", "resolution": "3840x2160-4K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "客户管理页面", "resolution": "iPhone-SE", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "客户管理页面", "resolution": "iPhone-11", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "客户管理页面", "resolution": "Android-Standard", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "客户管理页面", "resolution": "iPad-Portrait", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "客户管理页面", "resolution": "iPad-Landscape", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "预约管理页面", "resolution": "1366x768-<PERSON><PERSON><PERSON>", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "预约管理页面", "resolution": "1920x1080-Desktop", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "预约管理页面", "resolution": "2560x1440-2K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "预约管理页面", "resolution": "3840x2160-4K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "预约管理页面", "resolution": "iPhone-SE", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "预约管理页面", "resolution": "iPhone-11", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "预约管理页面", "resolution": "Android-Standard", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "预约管理页面", "resolution": "iPad-Portrait", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "预约管理页面", "resolution": "iPad-Landscape", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "财务管理页面", "resolution": "1366x768-<PERSON><PERSON><PERSON>", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "财务管理页面", "resolution": "1920x1080-Desktop", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "财务管理页面", "resolution": "2560x1440-2K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "财务管理页面", "resolution": "3840x2160-4K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "财务管理页面", "resolution": "iPhone-SE", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "财务管理页面", "resolution": "iPhone-11", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "财务管理页面", "resolution": "Android-Standard", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "财务管理页面", "resolution": "iPad-Portrait", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "财务管理页面", "resolution": "iPad-Landscape", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "健康贴士页面", "resolution": "1366x768-<PERSON><PERSON><PERSON>", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "健康贴士页面", "resolution": "1920x1080-Desktop", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "健康贴士页面", "resolution": "2560x1440-2K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "健康贴士页面", "resolution": "3840x2160-4K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "健康贴士页面", "resolution": "iPhone-SE", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "健康贴士页面", "resolution": "iPhone-11", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "健康贴士页面", "resolution": "Android-Standard", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "健康贴士页面", "resolution": "iPad-Portrait", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "健康贴士页面", "resolution": "iPad-Landscape", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "登录页面", "resolution": "1366x768-<PERSON><PERSON><PERSON>", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "登录页面", "resolution": "1920x1080-Desktop", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "登录页面", "resolution": "2560x1440-2K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "登录页面", "resolution": "3840x2160-4K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "登录页面", "resolution": "iPhone-SE", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "登录页面", "resolution": "iPhone-11", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "登录页面", "resolution": "Android-Standard", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "登录页面", "resolution": "iPad-Portrait", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "登录页面", "resolution": "iPad-Landscape", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "首页仪表板", "resolution": "1366x768-<PERSON><PERSON><PERSON>", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "首页仪表板", "resolution": "1920x1080-Desktop", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "首页仪表板", "resolution": "2560x1440-2K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "首页仪表板", "resolution": "3840x2160-4K", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "首页仪表板", "resolution": "iPhone-SE", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "首页仪表板", "resolution": "iPhone-11", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "首页仪表板", "resolution": "Android-Standard", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "首页仪表板", "resolution": "iPad-Portrait", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}, {"page": "首页仪表板", "resolution": "iPad-Landscape", "status": "ERROR", "error": "browserPage.waitForTimeout is not a function"}]}