{"summary": {"totalTests": 8, "passedTests": 0, "warningTests": 8, "failedTests": 8, "errorCount": 16, "warningCount": 18, "testDate": "2025-07-18T11:42:51.371Z"}, "results": [{"resolution": {"name": "4K超高清", "width": 3840, "height": 2160, "type": "desktop"}, "timestamp": "2025-07-18T11:42:10.082Z", "loadTime": 1096, "checks": {"searchSection": {"status": "fail", "message": "搜索区域未找到"}, "tableLayout": {"status": "fail", "message": "表格容器未找到"}, "pagination": {"status": "warning", "message": "分页组件未找到"}, "actionButtons": {"status": "warning", "message": "未找到操作按钮"}, "responsive": {"status": "pass", "deviceType": "desktop", "breakpoint": "desktop"}, "scrollbars": {"status": "pass", "scrollableElements": 0, "details": []}, "performance": {"status": "pass", "jsHeapUsedSize": 10, "jsHeapTotalSize": 13, "domNodes": 267, "layoutCount": 10, "recalcStyleCount": 33}}, "issues": [{"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "4K超高清"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "4K超高清"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "4K超高清"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "4K超高清"}], "screenshots": ["/Users/<USER>/Documents/wechatcloud/admin/test-reports/screenshot-4K----1752838934191.png"]}, {"resolution": {"name": "2K高清", "width": 2560, "height": 1440, "type": "desktop"}, "timestamp": "2025-07-18T11:42:16.536Z", "loadTime": 713, "checks": {"searchSection": {"status": "fail", "message": "搜索区域未找到"}, "tableLayout": {"status": "fail", "message": "表格容器未找到"}, "pagination": {"status": "warning", "message": "分页组件未找到"}, "actionButtons": {"status": "warning", "message": "未找到操作按钮"}, "responsive": {"status": "pass", "deviceType": "desktop", "breakpoint": "desktop"}, "scrollbars": {"status": "pass", "scrollableElements": 0, "details": []}, "performance": {"status": "pass", "jsHeapUsedSize": 14, "jsHeapTotalSize": 18, "domNodes": 532, "layoutCount": 6, "recalcStyleCount": 32}}, "issues": [{"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "2K高清"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "2K高清"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "2K高清"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "2K高清"}], "screenshots": ["/Users/<USER>/Documents/wechatcloud/admin/test-reports/screenshot-2K---1752838940259.png"]}, {"resolution": {"name": "标准桌面", "width": 1920, "height": 1080, "type": "desktop"}, "timestamp": "2025-07-18T11:42:21.956Z", "loadTime": 690, "checks": {"searchSection": {"status": "fail", "message": "搜索区域未找到"}, "tableLayout": {"status": "fail", "message": "表格容器未找到"}, "pagination": {"status": "warning", "message": "分页组件未找到"}, "actionButtons": {"status": "warning", "message": "未找到操作按钮"}, "responsive": {"status": "pass", "deviceType": "desktop", "breakpoint": "desktop"}, "scrollbars": {"status": "pass", "scrollableElements": 0, "details": []}, "performance": {"status": "pass", "jsHeapUsedSize": 18, "jsHeapTotalSize": 22, "domNodes": 797, "layoutCount": 7, "recalcStyleCount": 31}}, "issues": [{"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "标准桌面"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "标准桌面"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "标准桌面"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "标准桌面"}], "screenshots": ["/Users/<USER>/Documents/wechatcloud/admin/test-reports/screenshot------1752838945659.png"]}, {"resolution": {"name": "小桌面", "width": 1366, "height": 768, "type": "desktop"}, "timestamp": "2025-07-18T11:42:27.091Z", "loadTime": 689, "checks": {"searchSection": {"status": "fail", "message": "搜索区域未找到"}, "tableLayout": {"status": "fail", "message": "表格容器未找到"}, "pagination": {"status": "warning", "message": "分页组件未找到"}, "actionButtons": {"status": "warning", "message": "未找到操作按钮"}, "responsive": {"status": "pass", "deviceType": "desktop", "breakpoint": "desktop"}, "scrollbars": {"status": "pass", "scrollableElements": 0, "details": []}, "performance": {"status": "pass", "jsHeapUsedSize": 22, "jsHeapTotalSize": 26, "domNodes": 1062, "layoutCount": 7, "recalcStyleCount": 32}}, "issues": [{"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "小桌面"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "小桌面"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "小桌面"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "小桌面"}], "screenshots": ["/Users/<USER>/Documents/wechatcloud/admin/test-reports/screenshot-----1752838950792.png"]}, {"resolution": {"name": "平板横屏", "width": 1024, "height": 768, "type": "tablet"}, "timestamp": "2025-07-18T11:42:32.049Z", "loadTime": 683, "checks": {"searchSection": {"status": "fail", "message": "搜索区域未找到"}, "tableLayout": {"status": "fail", "message": "表格容器未找到"}, "pagination": {"status": "warning", "message": "分页组件未找到"}, "actionButtons": {"status": "warning", "message": "未找到操作按钮"}, "responsive": {"status": "pass", "deviceType": "tablet", "breakpoint": "tablet"}, "scrollbars": {"status": "pass", "scrollableElements": 0, "details": []}, "performance": {"status": "pass", "jsHeapUsedSize": 27, "jsHeapTotalSize": 32, "domNodes": 1327, "layoutCount": 7, "recalcStyleCount": 30}}, "issues": [{"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "平板横屏"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "平板横屏"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "平板横屏"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "平板横屏"}], "screenshots": ["/Users/<USER>/Documents/wechatcloud/admin/test-reports/screenshot------1752838955741.png"]}, {"resolution": {"name": "平板竖屏", "width": 768, "height": 1024, "type": "tablet"}, "timestamp": "2025-07-18T11:42:36.936Z", "loadTime": 726, "checks": {"searchSection": {"status": "fail", "message": "搜索区域未找到"}, "tableLayout": {"status": "fail", "message": "表格容器未找到"}, "pagination": {"status": "warning", "message": "分页组件未找到"}, "actionButtons": {"status": "warning", "message": "未找到操作按钮"}, "responsive": {"status": "pass", "deviceType": "tablet", "breakpoint": "mobile"}, "scrollbars": {"status": "pass", "scrollableElements": 0, "details": []}, "performance": {"status": "pass", "jsHeapUsedSize": 7, "jsHeapTotalSize": 22, "domNodes": 244, "layoutCount": 7, "recalcStyleCount": 32}}, "issues": [{"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "平板竖屏"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "平板竖屏"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "平板竖屏"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "平板竖屏"}], "screenshots": ["/Users/<USER>/Documents/wechatcloud/admin/test-reports/screenshot------1752838960675.png"]}, {"resolution": {"name": "手机横屏", "width": 667, "height": 375, "type": "mobile"}, "timestamp": "2025-07-18T11:42:41.793Z", "loadTime": 696, "checks": {"searchSection": {"status": "fail", "message": "搜索区域未找到"}, "tableLayout": {"status": "fail", "message": "表格容器未找到"}, "pagination": {"status": "warning", "message": "分页组件未找到"}, "actionButtons": {"status": "warning", "message": "未找到操作按钮"}, "responsive": {"status": "warning", "deviceType": "mobile", "breakpoint": "mobile", "hasHorizontalScroll": false, "minFontSize": 11, "message": "移动端字体过小: 11px < 14px"}, "scrollbars": {"status": "pass", "scrollableElements": 0, "details": []}, "performance": {"status": "pass", "jsHeapUsedSize": 11, "jsHeapTotalSize": 25, "domNodes": 509, "layoutCount": 7, "recalcStyleCount": 32}}, "issues": [{"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "手机横屏"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "手机横屏"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "手机横屏"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "手机横屏"}, {"type": "warning", "check": "responsive", "message": "移动端字体过小: 11px < 14px", "resolution": "手机横屏"}], "screenshots": ["/Users/<USER>/Documents/wechatcloud/admin/test-reports/screenshot------1752838965500.png"]}, {"resolution": {"name": "手机竖屏", "width": 375, "height": 667, "type": "mobile"}, "timestamp": "2025-07-18T11:42:46.572Z", "loadTime": 708, "checks": {"searchSection": {"status": "fail", "message": "搜索区域未找到"}, "tableLayout": {"status": "fail", "message": "表格容器未找到"}, "pagination": {"status": "warning", "message": "分页组件未找到"}, "actionButtons": {"status": "warning", "message": "未找到操作按钮"}, "responsive": {"status": "warning", "deviceType": "mobile", "breakpoint": "mobile", "hasHorizontalScroll": false, "minFontSize": 11, "message": "移动端字体过小: 11px < 14px"}, "scrollbars": {"status": "pass", "scrollableElements": 0, "details": []}, "performance": {"status": "pass", "jsHeapUsedSize": 7, "jsHeapTotalSize": 12, "domNodes": 244, "layoutCount": 7, "recalcStyleCount": 31}}, "issues": [{"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "手机竖屏"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "手机竖屏"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "手机竖屏"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "手机竖屏"}, {"type": "warning", "check": "responsive", "message": "移动端字体过小: 11px < 14px", "resolution": "手机竖屏"}], "screenshots": ["/Users/<USER>/Documents/wechatcloud/admin/test-reports/screenshot------1752838970292.png"]}], "issues": [{"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "4K超高清"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "4K超高清"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "4K超高清"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "4K超高清"}, {"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "2K高清"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "2K高清"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "2K高清"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "2K高清"}, {"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "标准桌面"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "标准桌面"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "标准桌面"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "标准桌面"}, {"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "小桌面"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "小桌面"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "小桌面"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "小桌面"}, {"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "平板横屏"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "平板横屏"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "平板横屏"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "平板横屏"}, {"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "平板竖屏"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "平板竖屏"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "平板竖屏"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "平板竖屏"}, {"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "手机横屏"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "手机横屏"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "手机横屏"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "手机横屏"}, {"type": "warning", "check": "responsive", "message": "移动端字体过小: 11px < 14px", "resolution": "手机横屏"}, {"type": "error", "check": "searchSection", "message": "搜索区域未找到", "resolution": "手机竖屏"}, {"type": "error", "check": "tableLayout", "message": "表格容器未找到", "resolution": "手机竖屏"}, {"type": "warning", "check": "pagination", "message": "分页组件未找到", "resolution": "手机竖屏"}, {"type": "warning", "check": "actionButtons", "message": "未找到操作按钮", "resolution": "手机竖屏"}, {"type": "warning", "check": "responsive", "message": "移动端字体过小: 11px < 14px", "resolution": "手机竖屏"}], "recommendations": ["建议优化搜索区域的响应式布局，确保在小屏幕下有足够的宽度", "建议优化表格在小屏幕下的显示方式，考虑使用卡片式布局", "建议增加按钮的最小高度到44px以符合触摸标准"]}