<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <link rel="icon" type="image/png" href="/src/assets/images/favicon.png" />
  <link rel="apple-touch-icon" href="/src/assets/images/favicon.png" />
  <link rel="shortcut icon" href="/src/assets/images/favicon.png" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>壹心堂中医推拿管理系统</title>
  <meta name="description" content="壹心堂中医推拿管理系统 - 专业的中医推拿服务管理平台" />
  <meta name="keywords" content="中医推拿,管理系统,壹心堂,预约管理,客户管理" />

  <!-- 预加载关键资源 -->
  <link rel="preconnect" href="https://fonts.googleapis.com">
  <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>

  <!-- Ant Design Vue 样式 -->
  <style>
    /* 加载动画 */
    .loading-container {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      background: #f0f2f5;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      z-index: 9999;
    }

    .loading-spinner {
      width: 40px;
      height: 40px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #1890ff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }

    .loading-text {
      margin-top: 16px;
      color: #666;
      font-size: 14px;
    }

    @keyframes spin {
      0% {
        transform: rotate(0deg);
      }

      100% {
        transform: rotate(360deg);
      }
    }

    /* 隐藏加载动画 */
    .loaded .loading-container {
      display: none;
    }
  </style>
</head>

<body>
  <!-- 加载动画 -->
  <div class="loading-container">
    <div class="loading-spinner"></div>
    <div class="loading-text">正在加载壹心堂管理系统...</div>
  </div>

  <!-- Vue应用挂载点 -->
  <div id="app"></div>

  <!-- 应用脚本 -->
  <script type="module" src="/src/main.js"></script>

  <!-- 加载完成后隐藏动画 -->
  <script>
    window.addEventListener('load', function () {
      setTimeout(function () {
        document.body.classList.add('loaded');
      }, 500);
    });
  </script>
</body>

</html>