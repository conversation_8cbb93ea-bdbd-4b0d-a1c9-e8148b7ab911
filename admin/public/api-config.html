<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AI API配置 - 壹心堂管理系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .config-container {
            background: white;
            border-radius: 16px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 600px;
            width: 100%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #4c1d95;
            font-size: 28px;
            margin-bottom: 8px;
        }
        
        .header p {
            color: #6b7280;
            font-size: 16px;
        }
        
        .form-group {
            margin-bottom: 24px;
        }
        
        .form-group label {
            display: block;
            color: #374151;
            font-weight: 600;
            margin-bottom: 8px;
            font-size: 14px;
        }
        
        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.2s;
        }
        
        .form-group input:focus {
            outline: none;
            border-color: #8b5cf6;
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.1);
        }
        
        .btn {
            width: 100%;
            padding: 12px 24px;
            background: #8b5cf6;
            color: white;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: background-color 0.2s;
            margin-bottom: 16px;
        }
        
        .btn:hover {
            background: #7c3aed;
        }
        
        .btn-secondary {
            background: #6b7280;
        }
        
        .btn-secondary:hover {
            background: #4b5563;
        }
        
        .status {
            padding: 12px 16px;
            border-radius: 8px;
            margin-bottom: 16px;
            font-size: 14px;
        }
        
        .status.success {
            background: #ecfdf5;
            color: #065f46;
            border: 1px solid #a7f3d0;
        }
        
        .status.error {
            background: #fef2f2;
            color: #991b1b;
            border: 1px solid #fecaca;
        }
        
        .info-box {
            background: #f8fafc;
            border: 1px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 24px;
        }
        
        .info-box h3 {
            color: #1e293b;
            font-size: 16px;
            margin-bottom: 8px;
        }
        
        .info-box p {
            color: #64748b;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .current-config {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 8px;
            padding: 16px;
            margin-top: 16px;
        }
        
        .current-config h4 {
            color: #0c4a6e;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .current-config p {
            color: #0369a1;
            font-size: 12px;
            font-family: monospace;
            word-break: break-all;
        }
    </style>
</head>
<body>
    <div class="config-container">
        <div class="header">
            <h1>🎨 AI API配置</h1>
            <p>配置百度文心一言API密钥以启用AI图片生成功能</p>
        </div>
        
        <div class="info-box">
            <h3>📋 配置说明</h3>
            <p>您的API密钥将安全存储在浏览器本地，不会上传到服务器。配置后即可在服务管理页面使用AI生成功能。</p>
        </div>
        
        <div id="status"></div>
        
        <form id="configForm">
            <div class="form-group">
                <label for="baiduApiKey">百度文心一言API密钥</label>
                <input
                    type="text"
                    id="baiduApiKey"
                    placeholder="bce-v3/ALTAK-xxx..."
                    value="bce-v3/ALTAK-fpYd2rn5cS653qxilQ1fm/ec7c7b2079b7adeb584348a13fed4640d3b09f06"
                >
            </div>
            
            <button type="submit" class="btn">💾 保存配置</button>
            <button type="button" class="btn btn-secondary" onclick="clearConfig()">🗑️ 清除配置</button>
        </form>
        
        <div class="current-config" id="currentConfig" style="display: none;">
            <h4>当前配置</h4>
            <p id="currentApiKey"></p>
        </div>
        
        <div class="info-box">
            <h3>🚀 使用步骤</h3>
            <p>1. 保存API密钥配置<br>
               2. 访问 <a href="/" style="color: #8b5cf6;">管理系统首页</a><br>
               3. 登录系统 (admin/admin)<br>
               4. 进入服务管理页面<br>
               5. 添加服务项目，输入服务名称<br>
               6. 切换字段即可触发AI生成</p>
        </div>
    </div>

    <script>
        // 页面加载时检查当前配置
        document.addEventListener('DOMContentLoaded', function() {
            checkCurrentConfig();
        });

        // 检查当前配置
        function checkCurrentConfig() {
            const baiduApiKey = localStorage.getItem('BAIDU_API_KEY');

            if (baiduApiKey) {
                document.getElementById('currentConfig').style.display = 'block';
                document.getElementById('currentApiKey').textContent = baiduApiKey.substring(0, 30) + '...';

                showStatus('✅ API密钥已配置，AI功能可用', 'success');
            } else {
                document.getElementById('currentConfig').style.display = 'none';
                showStatus('⚠️ 尚未配置API密钥', 'error');
            }
        }

        // 表单提交处理
        document.getElementById('configForm').addEventListener('submit', function(e) {
            e.preventDefault();

            const baiduApiKey = document.getElementById('baiduApiKey').value.trim();

            if (!baiduApiKey) {
                showStatus('❌ 请输入API密钥', 'error');
                return;
            }

            if (!baiduApiKey.startsWith('bce-v3/')) {
                showStatus('⚠️ API密钥格式可能不正确，应以 bce-v3/ 开头', 'error');
                return;
            }

            // 保存到localStorage
            localStorage.setItem('BAIDU_API_KEY', baiduApiKey);

            showStatus('✅ API密钥配置成功！现在可以使用AI生成功能了', 'success');
            checkCurrentConfig();

            // 清空输入框
            document.getElementById('baiduApiKey').value = '';
        });

        // 清除配置
        function clearConfig() {
            localStorage.removeItem('BAIDU_API_KEY');
            showStatus('🗑️ 配置已清除', 'success');
            checkCurrentConfig();
        }

        // 显示状态消息
        function showStatus(message, type) {
            const statusDiv = document.getElementById('status');
            statusDiv.innerHTML = `<div class="status ${type}">${message}</div>`;
            
            // 3秒后自动隐藏
            setTimeout(() => {
                statusDiv.innerHTML = '';
            }, 3000);
        }

        // 测试API连接
        function testConnection() {
            const baiduApiKey = localStorage.getItem('BAIDU_API_KEY');
            
            if (!baiduApiKey) {
                showStatus('❌ 请先配置API密钥', 'error');
                return;
            }
            
            showStatus('🔄 正在测试API连接...', 'success');
            
            // 这里可以添加实际的API测试逻辑
            setTimeout(() => {
                showStatus('✅ API连接测试成功！', 'success');
            }, 2000);
        }
    </script>
</body>
</html>
