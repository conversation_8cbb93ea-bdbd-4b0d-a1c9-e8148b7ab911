import vue from '@vitejs/plugin-vue';
import { fileURLToPath, URL } from 'node:url';
import { defineConfig } from 'vite';
import postCssPxToRem from 'postcss-pxtorem';

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  optimizeDeps: {
    include: [
      'ant-design-vue',
      '@babel/runtime/helpers/esm/objectSpread2',
      '@babel/runtime/helpers/esm/defineProperty',
      '@babel/runtime/helpers/esm/extends',
      '@babel/runtime/helpers/esm/typeof',
      '@babel/runtime/helpers/esm/objectWithoutProperties',
      '@babel/runtime/helpers/esm/toConsumableArray',
      '@babel/runtime/helpers/esm/slicedToArray',
      '@babel/runtime/helpers/esm/createForOfIteratorHelper',
      '@babel/runtime/helpers/esm/asyncToGenerator'
    ],
    force: true
  },
  server: {
    port: 3000,
    proxy: {
      '/api': {
        target: 'http://localhost:8000',  // Django后端运行在8000端口
        changeOrigin: true,
        // 不重写路径，保持/api前缀
      }
    },
  },
  css: {
    preprocessorOptions: {
      scss: {
        additionalData: '@use "@/assets/styles/variables.scss" as *;',
        api: 'modern-compiler' // 使用现代Sass API
      }
    },
    postcss: {
      plugins: [
        postCssPxToRem({
          rootValue: 16, // 1rem的大小，基于1512px宽度设计
          propList: ['*'], // 转换所有属性
          selectorBlackList: [
            '.no-rem', // 不转换的选择器
            // 只排除浮层和特殊组件，允许布局组件转换
            /^\.ant-modal/,
            /^\.ant-tooltip/,
            /^\.ant-popover/,
            /^\.ant-dropdown/,
            /^\.ant-notification/,
            /^\.ant-message/,
            /^\.ant-drawer/,
            /^\.ant-affix/,
            /^\.ant-back-top/,
            /^\.ant-anchor/,
            /^\.ant-tour/,
            /^\.ant-watermark/,
          ],
          minPixelValue: 0, // 最小转换像素值改为0，转换所有px值
          mediaQuery: true, // 在媒体查询中也转换px
          exclude: /node_modules/i, // 排除node_modules
          replace: true, // 替换而不是添加备用属性
          unitPrecision: 6, // rem单位的小数位数，提高精度确保缩放准确
        })
      ]
    }
  },
})
