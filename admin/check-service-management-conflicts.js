#!/usr/bin/env node

/**
 * 服务管理页面元素冲突检查器
 * 仔细检查内部是否存在元素冲突问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 服务管理页面元素冲突检查器启动...\n');

function checkServiceManagementConflicts() {
  const filePath = path.join(__dirname, 'src/views/ServiceManagement.vue');
  
  if (!fs.existsSync(filePath)) {
    console.log('❌ 服务管理页面文件不存在');
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];
  
  console.log('📋 检查服务管理页面元素冲突问题...\n');
  
  // 检查1: Z-index层级冲突
  console.log('🔍 检查1: Z-index层级冲突');
  const zIndexMatches = content.match(/z-index:\s*(\d+)/g) || [];
  const zIndexValues = zIndexMatches.map(match => {
    const value = parseInt(match.match(/\d+/)[0]);
    const context = content.substring(
      content.indexOf(match) - 100, 
      content.indexOf(match) + 100
    ).match(/\.[a-zA-Z-]+/g)?.[0] || 'unknown';
    return { value, context, match };
  });
  
  console.log('  📊 发现的Z-index层级:');
  zIndexValues.forEach(z => {
    console.log(`    ${z.context}: z-index: ${z.value}`);
  });
  
  // 检查是否有相同的z-index值
  const duplicateZIndex = {};
  zIndexValues.forEach(z => {
    if (duplicateZIndex[z.value]) {
      duplicateZIndex[z.value].push(z.context);
    } else {
      duplicateZIndex[z.value] = [z.context];
    }
  });
  
  Object.keys(duplicateZIndex).forEach(zValue => {
    if (duplicateZIndex[zValue].length > 1) {
      console.log(`  ⚠️  发现重复的z-index值 ${zValue}: ${duplicateZIndex[zValue].join(', ')}`);
      issues.push(`重复的z-index值 ${zValue}: ${duplicateZIndex[zValue].join(', ')}`);
    }
  });
  
  if (Object.keys(duplicateZIndex).every(z => duplicateZIndex[z].length === 1)) {
    console.log('  ✅ 未发现z-index层级冲突');
  }
  
  // 检查2: 绝对定位元素重叠风险
  console.log('\n🔍 检查2: 绝对定位元素重叠风险');
  const absolutePositions = content.match(/position:\s*absolute[^}]*?(?:top|left|right|bottom):\s*[^;]+/g) || [];
  
  console.log(`  📊 发现 ${absolutePositions.length} 个绝对定位元素:`);
  absolutePositions.forEach((pos, index) => {
    const selector = content.substring(0, content.indexOf(pos))
      .split('\n').pop().match(/\.[a-zA-Z-]+/)?.[0] || `element-${index}`;
    console.log(`    ${selector}: ${pos.replace(/\s+/g, ' ')}`);
  });
  
  // 检查搜索下拉框和表格的重叠风险
  const searchDropdownZ = zIndexValues.find(z => z.context.includes('search-dropdown'))?.value || 0;
  const tableZ = zIndexValues.find(z => z.context.includes('table') || z.context.includes('data'))?.value || 0;
  
  if (searchDropdownZ > 0 && tableZ > 0 && searchDropdownZ <= tableZ) {
    console.log(`  ⚠️  搜索下拉框(z-index:${searchDropdownZ})可能被表格(z-index:${tableZ})遮挡`);
    issues.push(`搜索下拉框可能被表格遮挡`);
  } else {
    console.log('  ✅ 搜索下拉框层级设置合理');
  }
  
  // 检查3: 模态框和其他元素的冲突
  console.log('\n🔍 检查3: 模态框层级冲突');
  const modalZ = zIndexValues.find(z => z.context.includes('modal'))?.value || 0;
  const otherHighZ = zIndexValues.filter(z => !z.context.includes('modal') && z.value >= modalZ);
  
  if (otherHighZ.length > 0) {
    console.log(`  ⚠️  发现可能遮挡模态框的元素:`);
    otherHighZ.forEach(z => {
      console.log(`    ${z.context}: z-index: ${z.value}`);
    });
    issues.push(`存在可能遮挡模态框的高层级元素`);
  } else {
    console.log('  ✅ 模态框层级设置合理');
  }
  
  // 检查4: 固定定位元素边界问题
  console.log('\n🔍 检查4: 固定定位元素边界问题');
  const fixedPositions = content.match(/position:\s*fixed[^}]*?(?:top|left|right|bottom):\s*[^;]+/g) || [];
  
  console.log(`  📊 发现 ${fixedPositions.length} 个固定定位元素:`);
  fixedPositions.forEach((pos, index) => {
    const selector = content.substring(0, content.indexOf(pos))
      .split('\n').pop().match(/\.[a-zA-Z-]+/)?.[0] || `fixed-element-${index}`;
    console.log(`    ${selector}: ${pos.replace(/\s+/g, ' ')}`);
  });
  
  // 检查是否有负值定位
  const negativePositions = content.match(/(?:top|left|right|bottom):\s*-\d+px/g) || [];
  if (negativePositions.length > 0) {
    console.log(`  ⚠️  发现负值定位: ${negativePositions.join(', ')}`);
    issues.push(`存在负值定位: ${negativePositions.join(', ')}`);
  } else {
    console.log('  ✅ 未发现负值定位问题');
  }
  
  // 检查5: 表格内部元素溢出
  console.log('\n🔍 检查5: 表格内部元素溢出');
  
  // 检查表格单元格的flex值总和
  const flexValues = content.match(/style="flex:\s*([\d.]+);"/g) || [];
  const totalFlex = flexValues.reduce((sum, flex) => {
    const value = parseFloat(flex.match(/[\d.]+/)[0]);
    return sum + value;
  }, 0);
  
  console.log(`  📊 表格列flex值总和: ${totalFlex}`);
  if (totalFlex > 10) {
    console.log(`  ⚠️  表格列flex值总和过大，可能导致溢出`);
    issues.push(`表格列flex值总和过大: ${totalFlex}`);
  } else {
    console.log('  ✅ 表格列flex值设置合理');
  }
  
  // 检查6: 响应式断点冲突
  console.log('\n🔍 检查6: 响应式断点冲突');
  const mediaQueries = content.match(/@media[^{]+\{/g) || [];
  const breakpoints = mediaQueries.map(mq => {
    const widthMatch = mq.match(/(?:min-width|max-width):\s*(\d+)px/g);
    return widthMatch ? widthMatch.map(w => parseInt(w.match(/\d+/)[0])) : [];
  }).flat().sort((a, b) => a - b);
  
  console.log(`  📊 发现响应式断点: ${[...new Set(breakpoints)].join('px, ')}px`);
  
  // 检查是否有重叠的断点范围
  const overlaps = [];
  for (let i = 0; i < breakpoints.length - 1; i++) {
    if (breakpoints[i] === breakpoints[i + 1]) {
      overlaps.push(breakpoints[i]);
    }
  }
  
  if (overlaps.length > 0) {
    console.log(`  ⚠️  发现重叠的断点: ${[...new Set(overlaps)].join('px, ')}px`);
    issues.push(`存在重叠的响应式断点`);
  } else {
    console.log('  ✅ 响应式断点设置无冲突');
  }
  
  // 检查7: CSS选择器优先级冲突
  console.log('\n🔍 检查7: CSS选择器优先级冲突');
  
  // 检查是否有过多的!important
  const importantCount = (content.match(/!important/g) || []).length;
  console.log(`  📊 发现 ${importantCount} 个 !important 声明`);
  
  if (importantCount > 20) {
    console.log(`  ⚠️  !important使用过多，可能导致样式冲突`);
    issues.push(`!important使用过多: ${importantCount}个`);
  } else {
    console.log('  ✅ !important使用合理');
  }
  
  // 总结报告
  console.log('\n📊 服务管理页面元素冲突检查总结:');
  console.log('='.repeat(50));
  
  if (issues.length === 0) {
    console.log('🎉 未发现元素冲突问题，页面布局完美！');
  } else {
    console.log(`❌ 发现 ${issues.length} 个潜在问题:`);
    issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });
  }
  
  return issues;
}

// 运行检查
const issues = checkServiceManagementConflicts();

if (issues.length > 0) {
  console.log('\n🔧 建议修复措施:');
  issues.forEach((issue, index) => {
    console.log(`\n${index + 1}. ${issue}`);
    
    if (issue.includes('z-index')) {
      console.log('   💡 建议: 重新规划z-index层级，使用CSS变量统一管理');
    }
    if (issue.includes('负值定位')) {
      console.log('   💡 建议: 避免使用负值定位，改用margin或transform');
    }
    if (issue.includes('flex值')) {
      console.log('   💡 建议: 调整表格列的flex值，确保总和合理');
    }
    if (issue.includes('!important')) {
      console.log('   💡 建议: 减少!important使用，提高选择器特异性');
    }
  });
}
