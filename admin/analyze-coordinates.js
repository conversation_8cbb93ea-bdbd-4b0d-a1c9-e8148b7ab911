#!/usr/bin/env node

/**
 * 翻页组件和表格最后一行坐标分析工具
 * 分析横向滚动和坐标问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 翻页组件和表格坐标分析工具启动...\n');

function analyzeCoordinates() {
  const filePath = path.join(__dirname, 'src/views/ServiceManagement.vue');
  
  if (!fs.existsSync(filePath)) {
    console.log('❌ 服务管理页面文件不存在');
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  
  console.log('📋 分析翻页组件和表格坐标...\n');
  
  // 分析1: 翻页组件的布局和定位
  console.log('🔍 分析1: 翻页组件布局分析');
  console.log('='.repeat(50));
  
  // 查找翻页组件的CSS样式
  const paginationStyles = [
    '.pagination-container',
    '.pagination-controls',
    '.page-navigation',
    '.page-numbers'
  ];
  
  paginationStyles.forEach(selector => {
    const regex = new RegExp(`${selector.replace('.', '\\.')}\\s*\\{([^}]+)\\}`, 'g');
    const matches = content.match(regex);
    
    if (matches) {
      console.log(`\n📊 ${selector} 样式分析:`);
      matches.forEach(match => {
        const styles = match.match(/([a-z-]+):\s*([^;]+);/g) || [];
        styles.forEach(style => {
          const [property, value] = style.split(':').map(s => s.trim());
          
          // 重点关注布局相关属性
          if (['display', 'flex-wrap', 'overflow', 'width', 'min-width', 'max-width', 'gap'].includes(property)) {
            console.log(`    ${property}: ${value}`);
            
            // 分析潜在问题
            if (property === 'flex-wrap' && value.includes('wrap')) {
              console.log(`      ⚠️  flex-wrap: wrap 可能导致换行，影响布局`);
            }
            if (property === 'overflow' && value.includes('hidden')) {
              console.log(`      ⚠️  overflow: hidden 可能隐藏内容`);
            }
            if (property === 'gap' && parseFloat(value) > 20) {
              console.log(`      ⚠️  gap值较大(${value})，可能导致空间不足`);
            }
          }
        });
      });
    } else {
      console.log(`\n❌ 未找到 ${selector} 的样式定义`);
    }
  });
  
  // 分析2: 表格容器和最后一行的定位
  console.log('\n\n🔍 分析2: 表格容器和最后一行分析');
  console.log('='.repeat(50));
  
  // 查找表格相关的样式
  const tableStyles = [
    '.table-container',
    '.table-body',
    '.data-row',
    '.data-cell'
  ];
  
  tableStyles.forEach(selector => {
    const regex = new RegExp(`${selector.replace('.', '\\.')}\\s*\\{([^}]+)\\}`, 'g');
    const matches = content.match(regex);
    
    if (matches) {
      console.log(`\n📊 ${selector} 样式分析:`);
      matches.forEach(match => {
        const styles = match.match(/([a-z-]+):\s*([^;]+);/g) || [];
        styles.forEach(style => {
          const [property, value] = style.split(':').map(s => s.trim());
          
          // 重点关注定位和尺寸相关属性
          if (['position', 'top', 'bottom', 'left', 'right', 'height', 'min-height', 'max-height', 'overflow', 'flex'].includes(property)) {
            console.log(`    ${property}: ${value}`);
            
            // 分析潜在问题
            if (property === 'overflow-x' && value.includes('auto')) {
              console.log(`      ⚠️  overflow-x: auto 可能导致横向滚动`);
            }
            if (property === 'max-height' && value.includes('calc')) {
              console.log(`      📐 动态高度计算: ${value}`);
            }
            if (property === 'flex' && parseFloat(value) > 2) {
              console.log(`      ⚠️  flex值较大(${value})，可能占用过多空间`);
            }
          }
        });
      });
    }
  });
  
  // 分析3: 响应式设计对坐标的影响
  console.log('\n\n🔍 分析3: 响应式设计对坐标的影响');
  console.log('='.repeat(50));
  
  // 查找媒体查询中的翻页和表格样式
  const mediaQueries = content.match(/@media[^{]+\{[^}]*(?:\{[^}]*\}[^}]*)*\}/g) || [];
  
  console.log(`📱 发现 ${mediaQueries.length} 个媒体查询`);
  
  mediaQueries.forEach((mq, index) => {
    const breakpoint = mq.match(/(?:min-width|max-width):\s*(\d+)px/g);
    if (breakpoint) {
      console.log(`\n📊 媒体查询 ${index + 1}: ${breakpoint.join(', ')}`);
      
      // 检查是否包含翻页或表格相关样式
      const hasPagination = mq.includes('pagination') || mq.includes('page-');
      const hasTable = mq.includes('table') || mq.includes('data-row');
      
      if (hasPagination) {
        console.log(`    ✅ 包含翻页组件样式调整`);
        
        // 提取翻页相关的样式变化
        const paginationChanges = mq.match(/\.pagination[^}]*\{[^}]*\}/g) || [];
        paginationChanges.forEach(change => {
          console.log(`      ${change.replace(/\s+/g, ' ')}`);
        });
      }
      
      if (hasTable) {
        console.log(`    ✅ 包含表格样式调整`);
        
        // 提取表格相关的样式变化
        const tableChanges = mq.match(/\.(?:table|data-row)[^}]*\{[^}]*\}/g) || [];
        tableChanges.forEach(change => {
          console.log(`      ${change.replace(/\s+/g, ' ')}`);
        });
      }
      
      if (!hasPagination && !hasTable) {
        console.log(`    ⚠️  此断点未包含翻页或表格样式调整`);
      }
    }
  });
  
  // 分析4: 潜在的横向滚动问题
  console.log('\n\n🔍 分析4: 潜在的横向滚动问题');
  console.log('='.repeat(50));
  
  // 查找可能导致横向滚动的样式
  const horizontalScrollCauses = [
    { pattern: /width:\s*calc\([^)]*\+[^)]*\)/g, desc: '宽度计算可能超出容器' },
    { pattern: /min-width:\s*\d{3,}px/g, desc: '最小宽度设置过大' },
    { pattern: /flex:\s*none/g, desc: 'flex: none 可能导致不收缩' },
    { pattern: /white-space:\s*nowrap/g, desc: '不换行可能导致溢出' },
    { pattern: /overflow-x:\s*auto/g, desc: '明确设置了横向滚动' },
    { pattern: /position:\s*absolute.*left:\s*-\d+/g, desc: '负值定位可能超出边界' }
  ];
  
  horizontalScrollCauses.forEach(cause => {
    const matches = content.match(cause.pattern) || [];
    if (matches.length > 0) {
      console.log(`\n⚠️  ${cause.desc}:`);
      matches.forEach(match => {
        console.log(`    ${match}`);
      });
    }
  });
  
  // 分析5: 翻页组件和表格的空间分配
  console.log('\n\n🔍 分析5: 空间分配分析');
  console.log('='.repeat(50));
  
  // 计算表格列的flex值总和
  const flexValues = content.match(/style="flex:\s*([\d.]+);"/g) || [];
  const totalFlex = flexValues.reduce((sum, flex) => {
    const value = parseFloat(flex.match(/[\d.]+/)[0]);
    return sum + value;
  }, 0);
  
  console.log(`📊 表格列flex值分析:`);
  flexValues.forEach(flex => {
    const value = parseFloat(flex.match(/[\d.]+/)[0]);
    console.log(`    flex: ${value}`);
  });
  console.log(`    总和: ${totalFlex}`);
  
  if (totalFlex > 10) {
    console.log(`    ⚠️  flex值总和过大，可能导致列宽分配不均`);
  } else {
    console.log(`    ✅ flex值总和合理`);
  }
  
  // 分析翻页组件的空间需求
  const paginationGap = content.match(/\.pagination-controls[^}]*gap:\s*(\d+)px/);
  const paginationPadding = content.match(/\.pagination-container[^}]*padding:\s*([^;]+);/);
  
  console.log(`\n📊 翻页组件空间需求:`);
  if (paginationGap) {
    console.log(`    控件间距: ${paginationGap[1]}px`);
  }
  if (paginationPadding) {
    console.log(`    容器内边距: ${paginationPadding[1]}`);
  }
  
  // 总结和建议
  console.log('\n\n📊 坐标分析总结和建议');
  console.log('='.repeat(50));
  
  const issues = [];
  const recommendations = [];
  
  // 检查是否有明确的横向滚动设置
  if (content.includes('overflow-x: auto')) {
    issues.push('存在明确的横向滚动设置');
    recommendations.push('检查是否真的需要横向滚动，考虑使用响应式布局');
  }
  
  // 检查翻页组件是否会换行
  if (content.includes('flex-wrap: wrap')) {
    issues.push('翻页组件设置了换行，可能影响布局');
    recommendations.push('考虑在小屏幕上调整翻页组件布局');
  }
  
  // 检查表格列宽分配
  if (totalFlex > 8) {
    issues.push(`表格列flex值总和较大(${totalFlex})`);
    recommendations.push('优化表格列宽分配，确保在小屏幕上正常显示');
  }
  
  if (issues.length === 0) {
    console.log('🎉 未发现明显的坐标或布局问题！');
  } else {
    console.log('❌ 发现的问题:');
    issues.forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue}`);
    });
    
    console.log('\n💡 建议的解决方案:');
    recommendations.forEach((rec, index) => {
      console.log(`  ${index + 1}. ${rec}`);
    });
  }
}

// 运行分析
analyzeCoordinates();
