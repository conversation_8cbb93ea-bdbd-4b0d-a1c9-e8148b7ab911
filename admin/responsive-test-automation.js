#!/usr/bin/env node

/**
 * 响应式自动化测试工具 - 可视化测试
 * 全面测试每个页面在各个分辨率下的显示效果
 * 严格按照完美布局规则进行测试
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

console.log('🧪 响应式自动化测试工具启动...\n');

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  screenshotDir: './test-screenshots',
  reportDir: './test-reports',
  timeout: 30000,
  waitTime: 2000
};

// 测试分辨率配置 - 移除平板端
const TEST_RESOLUTIONS = [
  // 桌面端 - 主要测试
  { name: '1366x768-Laptop', width: 1366, height: 768, deviceScaleFactor: 1, category: 'desktop' },
  { name: '1920x1080-Desktop', width: 1920, height: 1080, deviceScaleFactor: 1, category: 'desktop' },
  { name: '2560x1440-2K', width: 2560, height: 1440, deviceScaleFactor: 1, category: 'desktop' },
  { name: '3840x2160-4K', width: 3840, height: 2160, deviceScaleFactor: 2, category: 'desktop' },

  // 移动端
  { name: 'iPhone-SE', width: 375, height: 667, deviceScaleFactor: 2, category: 'mobile' },
  { name: 'iPhone-11', width: 414, height: 896, deviceScaleFactor: 2, category: 'mobile' },
  { name: 'Android-Standard', width: 360, height: 640, deviceScaleFactor: 2, category: 'mobile' }
];

// 测试页面配置
const TEST_PAGES = [
  // 核心页面
  { name: '服务管理页面', path: '/services', priority: 'high' },
  { name: '技师管理页面', path: '/technicians', priority: 'high' },
  { name: '客户管理页面', path: '/customers', priority: 'high' },
  { name: '预约管理页面', path: '/appointments', priority: 'high' },
  { name: '财务管理页面', path: '/finance', priority: 'medium' },
  { name: '健康贴士页面', path: '/health-tips', priority: 'medium' },
  { name: '登录页面', path: '/login', priority: 'high' },
  { name: '首页仪表板', path: '/dashboard', priority: 'high' }
];

// 完美布局规则检查点
const PERFECT_LAYOUT_CHECKS = [
  'elementBoundaryCheck',      // 元素边界检查
  'overlapDetection',          // 重叠检测
  'spacingAlignment',          // 间距对齐
  'scrollbarImpact',          // 滚动条影响
  'visualConsistency',        // 视觉一致性
  'fontReadability',          // 字体可读性
  'buttonAccessibility',      // 按钮可访问性
  'modalResponsiveness'       // 弹窗响应性
];

// 测试结果统计
let testStats = {
  totalTests: 0,
  passedTests: 0,
  failedTests: 0,
  skippedTests: 0,
  startTime: new Date(),
  endTime: null,
  issues: []
};

// 创建测试目录
function createTestDirectories() {
  [TEST_CONFIG.screenshotDir, TEST_CONFIG.reportDir].forEach(dir => {
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
  });
}

// 启动浏览器
async function launchBrowser() {
  console.log('🚀 启动浏览器...');

  // 尝试使用系统Chrome
  const possiblePaths = [
    '/Applications/Google Chrome.app/Contents/MacOS/Google Chrome',
    '/usr/bin/google-chrome',
    '/usr/bin/chromium-browser',
    'google-chrome',
    'chromium'
  ];

  let executablePath = null;
  const fs = require('fs');

  for (const path of possiblePaths) {
    if (path.startsWith('/') && fs.existsSync(path)) {
      executablePath = path;
      break;
    }
  }

  const launchOptions = {
    headless: false, // 可视化测试，显示浏览器
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-dev-shm-usage',
      '--disable-web-security',
      '--allow-running-insecure-content'
    ]
  };

  if (executablePath) {
    launchOptions.executablePath = executablePath;
    console.log(`📍 使用系统Chrome: ${executablePath}`);
  } else {
    console.log('📍 使用Puppeteer内置Chrome');
  }

  const browser = await puppeteer.launch(launchOptions);

  return browser;
}

// 完美布局规则检查
async function checkPerfectLayoutRules(page, pageName, resolution) {
  console.log(`  🎯 执行完美布局规则检查...`);
  
  const results = {};
  
  try {
    // 注入轮廓调试工具
    await page.evaluate(() => {
      if (typeof window.enableOutlineDebug === 'function') {
        window.enableOutlineDebug();
      }
    });
    
    // 等待轮廓调试工具加载
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    // 执行完美规则检查
    const perfectRulesResult = await page.evaluate(() => {
      if (typeof window.printRedCoordinates === 'function') {
        return window.printRedCoordinates();
      }
      return null;
    });
    
    results.perfectRulesCheck = perfectRulesResult ? 'PASS' : 'SKIP';
    
    // 元素边界检查
    const boundaryCheck = await page.evaluate(() => {
      const elements = document.querySelectorAll('*');
      const violations = [];
      
      elements.forEach(el => {
        const rect = el.getBoundingClientRect();
        if (rect.width > 0 && rect.height > 0) {
          // 检查是否超出视口
          if (rect.right > window.innerWidth || rect.bottom > window.innerHeight) {
            violations.push({
              element: el.tagName + (el.className ? '.' + el.className.split(' ')[0] : ''),
              issue: 'Element exceeds viewport',
              rect: { width: rect.width, height: rect.height, right: rect.right, bottom: rect.bottom }
            });
          }
        }
      });
      
      return violations;
    });
    
    results.boundaryCheck = boundaryCheck.length === 0 ? 'PASS' : 'FAIL';
    if (boundaryCheck.length > 0) {
      results.boundaryIssues = boundaryCheck;
    }
    
    // 重叠检测
    const overlapCheck = await page.evaluate(() => {
      const elements = Array.from(document.querySelectorAll('*')).filter(el => {
        const rect = el.getBoundingClientRect();
        return rect.width > 0 && rect.height > 0;
      });
      
      const overlaps = [];
      
      for (let i = 0; i < elements.length; i++) {
        const el1 = elements[i];
        const rect1 = el1.getBoundingClientRect();
        
        for (let j = i + 1; j < elements.length; j++) {
          const el2 = elements[j];
          const rect2 = el2.getBoundingClientRect();
          
          // 检查重叠
          if (!(rect1.right <= rect2.left || rect1.left >= rect2.right || 
                rect1.bottom <= rect2.top || rect1.top >= rect2.bottom)) {
            // 检查是否为父子关系
            if (!el1.contains(el2) && !el2.contains(el1)) {
              overlaps.push({
                element1: el1.tagName + (el1.className ? '.' + el1.className.split(' ')[0] : ''),
                element2: el2.tagName + (el2.className ? '.' + el2.className.split(' ')[0] : ''),
                issue: 'Elements overlap'
              });
            }
          }
        }
      }
      
      return overlaps;
    });
    
    results.overlapCheck = overlapCheck.length === 0 ? 'PASS' : 'FAIL';
    if (overlapCheck.length > 0) {
      results.overlapIssues = overlapCheck;
    }
    
    // 字体可读性检查
    const fontCheck = await page.evaluate(() => {
      const textElements = document.querySelectorAll('p, span, div, h1, h2, h3, h4, h5, h6, button, a');
      const issues = [];
      
      textElements.forEach(el => {
        const style = window.getComputedStyle(el);
        const fontSize = parseFloat(style.fontSize);
        
        if (fontSize < 12) {
          issues.push({
            element: el.tagName + (el.className ? '.' + el.className.split(' ')[0] : ''),
            issue: 'Font too small',
            fontSize: fontSize
          });
        }
      });
      
      return issues;
    });
    
    results.fontCheck = fontCheck.length === 0 ? 'PASS' : 'FAIL';
    if (fontCheck.length > 0) {
      results.fontIssues = fontCheck;
    }
    
    // 按钮可访问性检查
    const buttonCheck = await page.evaluate(() => {
      const buttons = document.querySelectorAll('button, .btn, [role="button"]');
      const issues = [];
      
      buttons.forEach(btn => {
        const rect = btn.getBoundingClientRect();
        
        // 检查按钮最小尺寸 (44x44px for touch)
        if (rect.width < 44 || rect.height < 44) {
          issues.push({
            element: btn.tagName + (btn.className ? '.' + btn.className.split(' ')[0] : ''),
            issue: 'Button too small for touch',
            size: { width: rect.width, height: rect.height }
          });
        }
      });
      
      return issues;
    });
    
    results.buttonCheck = buttonCheck.length === 0 ? 'PASS' : 'FAIL';
    if (buttonCheck.length > 0) {
      results.buttonIssues = buttonCheck;
    }
    
  } catch (error) {
    console.error(`    ❌ 完美布局规则检查出错: ${error.message}`);
    results.error = error.message;
  }
  
  return results;
}

// 截图并保存
async function takeScreenshot(page, pageName, resolution) {
  const filename = `${pageName.replace(/[^a-zA-Z0-9]/g, '_')}_${resolution.name}.png`;
  const filepath = path.join(TEST_CONFIG.screenshotDir, filename);
  
  await page.screenshot({
    path: filepath,
    fullPage: true
  });
  
  return filepath;
}

// 测试单个页面在特定分辨率下
async function testPageAtResolution(browser, page, resolution) {
  console.log(`\n📱 测试页面: ${page.name} | 分辨率: ${resolution.name}`);
  
  const browserPage = await browser.newPage();
  
  try {
    // 设置视口
    await browserPage.setViewport({
      width: resolution.width,
      height: resolution.height,
      deviceScaleFactor: resolution.deviceScaleFactor
    });
    
    console.log(`  🔧 设置视口: ${resolution.width}x${resolution.height}`);
    
    // 导航到页面
    const url = TEST_CONFIG.baseUrl + page.path;
    console.log(`  🌐 导航到: ${url}`);
    
    await browserPage.goto(url, { 
      waitUntil: 'networkidle2',
      timeout: TEST_CONFIG.timeout 
    });
    
    // 等待页面完全加载
    await new Promise(resolve => setTimeout(resolve, TEST_CONFIG.waitTime));
    
    // 执行完美布局规则检查
    const layoutResults = await checkPerfectLayoutRules(browserPage, page.name, resolution);
    
    // 截图
    const screenshotPath = await takeScreenshot(browserPage, page.name, resolution);
    console.log(`  📸 截图保存: ${screenshotPath}`);
    
    // 统计结果
    testStats.totalTests++;
    
    const hasIssues = Object.values(layoutResults).some(result => 
      result === 'FAIL' || (Array.isArray(result) && result.length > 0)
    );
    
    if (hasIssues) {
      testStats.failedTests++;
      testStats.issues.push({
        page: page.name,
        resolution: resolution.name,
        results: layoutResults,
        screenshot: screenshotPath
      });
      console.log(`  ❌ 测试失败: 发现布局问题`);
    } else {
      testStats.passedTests++;
      console.log(`  ✅ 测试通过: 布局完美`);
    }
    
    return {
      page: page.name,
      resolution: resolution.name,
      status: hasIssues ? 'FAIL' : 'PASS',
      results: layoutResults,
      screenshot: screenshotPath
    };
    
  } catch (error) {
    console.error(`  ❌ 测试出错: ${error.message}`);
    testStats.totalTests++;
    testStats.failedTests++;
    
    return {
      page: page.name,
      resolution: resolution.name,
      status: 'ERROR',
      error: error.message
    };
    
  } finally {
    await browserPage.close();
  }
}

// 生成测试报告
function generateTestReport(results) {
  testStats.endTime = new Date();
  const duration = (testStats.endTime - testStats.startTime) / 1000;
  
  const report = {
    summary: {
      totalTests: testStats.totalTests,
      passedTests: testStats.passedTests,
      failedTests: testStats.failedTests,
      skippedTests: testStats.skippedTests,
      passRate: ((testStats.passedTests / testStats.totalTests) * 100).toFixed(1),
      duration: `${duration.toFixed(1)}秒`,
      timestamp: new Date().toISOString()
    },
    resolutionResults: {},
    pageResults: {},
    issues: testStats.issues,
    results: results
  };
  
  // 按分辨率统计
  TEST_RESOLUTIONS.forEach(resolution => {
    const resolutionResults = results.filter(r => r.resolution === resolution.name);
    const passed = resolutionResults.filter(r => r.status === 'PASS').length;
    const total = resolutionResults.length;
    
    report.resolutionResults[resolution.name] = {
      total,
      passed,
      failed: total - passed,
      passRate: total > 0 ? ((passed / total) * 100).toFixed(1) : '0.0'
    };
  });
  
  // 按页面统计
  TEST_PAGES.forEach(page => {
    const pageResults = results.filter(r => r.page === page.name);
    const passed = pageResults.filter(r => r.status === 'PASS').length;
    const total = pageResults.length;
    
    report.pageResults[page.name] = {
      total,
      passed,
      failed: total - passed,
      passRate: total > 0 ? ((passed / total) * 100).toFixed(1) : '0.0'
    };
  });
  
  // 保存报告
  const reportPath = path.join(TEST_CONFIG.reportDir, `responsive-test-report-${Date.now()}.json`);
  fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
  
  return { report, reportPath };
}

// 打印测试报告
function printTestReport(report) {
  console.log('\n📊 响应式自动化测试报告');
  console.log('='.repeat(60));
  
  console.log(`📈 测试统计:`);
  console.log(`  总测试用例: ${report.summary.totalTests}`);
  console.log(`  通过用例: ${report.summary.passedTests}`);
  console.log(`  失败用例: ${report.summary.failedTests}`);
  console.log(`  跳过用例: ${report.summary.skippedTests}`);
  console.log(`  通过率: ${report.summary.passRate}%`);
  console.log(`  执行时间: ${report.summary.duration}`);
  
  console.log(`\n🎯 分辨率测试结果:`);
  Object.entries(report.resolutionResults).forEach(([resolution, result]) => {
    const status = parseFloat(result.passRate) >= 95 ? '✅' : parseFloat(result.passRate) >= 80 ? '⚠️' : '❌';
    console.log(`  ${resolution}: ${status} ${result.passRate}% (${result.passed}/${result.total})`);
  });
  
  console.log(`\n📋 页面测试结果:`);
  Object.entries(report.pageResults).forEach(([page, result]) => {
    const status = parseFloat(result.passRate) >= 95 ? '✅' : parseFloat(result.passRate) >= 80 ? '⚠️' : '❌';
    console.log(`  ${page}: ${status} ${result.passRate}% (${result.passed}/${result.total})`);
  });
  
  if (report.issues.length > 0) {
    console.log(`\n🚨 发现的问题 (${report.issues.length}个):`);
    report.issues.slice(0, 10).forEach((issue, index) => {
      console.log(`  ${index + 1}. ${issue.page} | ${issue.resolution}`);
      if (issue.results.boundaryIssues) {
        console.log(`     - 边界问题: ${issue.results.boundaryIssues.length}个`);
      }
      if (issue.results.overlapIssues) {
        console.log(`     - 重叠问题: ${issue.results.overlapIssues.length}个`);
      }
      if (issue.results.fontIssues) {
        console.log(`     - 字体问题: ${issue.results.fontIssues.length}个`);
      }
    });
    
    if (report.issues.length > 10) {
      console.log(`  ... 还有 ${report.issues.length - 10} 个问题，详见报告文件`);
    }
  }
}

// 主测试函数
async function runResponsiveTests() {
  console.log('🎯 开始响应式自动化测试');
  console.log(`📊 测试计划: ${TEST_PAGES.length}个页面 × ${TEST_RESOLUTIONS.length}个分辨率 = ${TEST_PAGES.length * TEST_RESOLUTIONS.length}个测试用例`);
  
  // 创建测试目录
  createTestDirectories();
  
  // 启动浏览器
  const browser = await launchBrowser();
  
  const results = [];
  
  try {
    // 遍历所有页面和分辨率组合
    for (const page of TEST_PAGES) {
      for (const resolution of TEST_RESOLUTIONS) {
        const result = await testPageAtResolution(browser, page, resolution);
        results.push(result);
        
        // 短暂延迟，避免过快请求
        await new Promise(resolve => setTimeout(resolve, 500));
      }
    }
    
    // 生成测试报告
    const { report, reportPath } = generateTestReport(results);
    
    // 打印测试报告
    printTestReport(report);
    
    console.log(`\n📁 详细报告已保存: ${reportPath}`);
    console.log(`📸 截图已保存到: ${TEST_CONFIG.screenshotDir}`);
    
    // 测试结果评估
    const passRate = parseFloat(report.summary.passRate);
    if (passRate >= 95) {
      console.log('\n🎉 测试结果: 优秀! 响应式设计完美!');
    } else if (passRate >= 80) {
      console.log('\n⚠️ 测试结果: 良好，但需要优化一些问题');
    } else {
      console.log('\n❌ 测试结果: 需要重大改进');
    }
    
  } finally {
    await browser.close();
  }
}

// 运行测试
if (require.main === module) {
  runResponsiveTests().catch(error => {
    console.error('❌ 测试执行失败:', error);
    process.exit(1);
  });
}

module.exports = {
  runResponsiveTests,
  TEST_RESOLUTIONS,
  TEST_PAGES,
  PERFECT_LAYOUT_CHECKS
};
