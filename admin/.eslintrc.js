module.exports = {
  root: true,
  env: {
    node: true,
    browser: true,
    es2022: true
  },
  extends: [
    'eslint:recommended',
    'plugin:vue/vue3-essential'
  ],
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module'
  },
  plugins: ['vue'],
  rules: {
    'vue/multi-word-component-names': 'off',
    'vue/no-unused-vars': 'warn',
    'vue/no-unused-components': 'warn',
    'no-console': 'off',
    'no-debugger': 'off', 
    'no-unused-vars': 'warn',
    'prefer-const': 'warn',
    'no-var': 'warn'
  },
  globals: {
    defineProps: 'readonly',
    defineEmits: 'readonly', 
    defineExpose: 'readonly',
    withDefaults: 'readonly'
  }
}
