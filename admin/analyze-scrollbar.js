#!/usr/bin/env node

/**
 * 横向滚动条分析工具
 * 专门分析翻页组件上方的滚动条问题
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 横向滚动条分析工具启动...\n');

function analyzeScrollbar() {
  const filePath = path.join(__dirname, 'src/views/ServiceManagement.vue');
  
  if (!fs.existsSync(filePath)) {
    console.log('❌ 服务管理页面文件不存在');
    return;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  
  console.log('📋 分析翻页组件上方的横向滚动条...\n');
  
  // 分析1: 基础滚动设置
  console.log('🔍 分析1: 表格容器滚动设置');
  console.log('='.repeat(50));
  
  // 查找基础的table-container样式
  const baseTableContainer = content.match(/\.table-container\s*\{([^}]+)\}/);
  if (baseTableContainer) {
    console.log('📊 基础 .table-container 样式:');
    const styles = baseTableContainer[1].match(/([a-z-]+):\s*([^;]+);/g) || [];
    styles.forEach(style => {
      const [property, value] = style.split(':').map(s => s.trim());
      console.log(`    ${property}: ${value}`);
      
      if (property === 'overflow' && value === 'hidden') {
        console.log(`      ✅ 基础状态隐藏滚动条`);
      }
      if (property === 'overflow-x') {
        console.log(`      ⚠️  基础状态就设置了横向滚动: ${value}`);
      }
    });
  }
  
  // 分析2: 响应式滚动设置
  console.log('\n🔍 分析2: 响应式滚动设置');
  console.log('='.repeat(50));
  
  // 查找所有包含overflow-x: auto的媒体查询
  const mediaQueries = content.match(/@media[^{]+\{[^}]*(?:\{[^}]*\}[^}]*)*\}/g) || [];
  
  const scrollbarBreakpoints = [];
  
  mediaQueries.forEach((mq, index) => {
    if (mq.includes('overflow-x: auto')) {
      const breakpoint = mq.match(/(?:min-width|max-width):\s*(\d+)px/g);
      const minWidth = mq.match(/min-width:\s*(\d+)px/);
      const maxWidth = mq.match(/max-width:\s*(\d+)px/);
      
      console.log(`\n📊 媒体查询 ${index + 1}:`);
      if (breakpoint) {
        console.log(`    断点: ${breakpoint.join(', ')}`);
      }
      
      // 提取table-container的overflow-x设置
      const tableOverflow = mq.match(/\.table-container[^}]*overflow-x:\s*auto/);
      if (tableOverflow) {
        console.log(`    ✅ 设置了表格容器横向滚动`);
        
        const range = {
          min: minWidth ? parseInt(minWidth[1]) : 0,
          max: maxWidth ? parseInt(maxWidth[1]) : Infinity
        };
        scrollbarBreakpoints.push(range);
      }
      
      // 检查对应的min-width设置
      const minWidthMatch = mq.match(/min-width:\s*(\d+)px/g);
      const tableMinWidth = mq.match(/\.(?:table-header|data-row)[^}]*min-width:\s*(\d+)px/);
      if (tableMinWidth) {
        console.log(`    ⚠️  强制表格最小宽度: ${tableMinWidth[1]}px`);
        
        if (maxWidth && parseInt(tableMinWidth[1]) > parseInt(maxWidth[1])) {
          console.log(`    🚨 问题: 表格最小宽度(${tableMinWidth[1]}px) > 屏幕最大宽度(${maxWidth[1]}px)`);
          console.log(`         这会导致必然出现横向滚动条！`);
        }
      }
    }
  });
  
  // 分析3: 滚动条出现的屏幕尺寸范围
  console.log('\n🔍 分析3: 滚动条出现的屏幕尺寸范围');
  console.log('='.repeat(50));
  
  if (scrollbarBreakpoints.length > 0) {
    console.log('📊 横向滚动条会在以下屏幕尺寸出现:');
    scrollbarBreakpoints.forEach((range, index) => {
      const minText = range.min > 0 ? `${range.min}px` : '0px';
      const maxText = range.max < Infinity ? `${range.max}px` : '∞';
      console.log(`    范围 ${index + 1}: ${minText} - ${maxText}`);
      
      // 常见设备分析
      if (range.min <= 768 && range.max >= 768) {
        console.log(`      📱 iPad竖屏 (768px) 会出现滚动条`);
      }
      if (range.min <= 1024 && range.max >= 1024) {
        console.log(`      📱 iPad横屏 (1024px) 会出现滚动条`);
      }
      if (range.min <= 1366 && range.max >= 1366) {
        console.log(`      💻 小笔记本 (1366px) 会出现滚动条`);
      }
    });
  } else {
    console.log('✅ 未发现明确的横向滚动条设置');
  }
  
  // 分析4: 滚动条与翻页组件的位置关系
  console.log('\n🔍 分析4: 滚动条与翻页组件的位置关系');
  console.log('='.repeat(50));
  
  // 查找翻页组件的位置设置
  const paginationContainer = content.match(/\.pagination-container\s*\{([^}]+)\}/);
  if (paginationContainer) {
    console.log('📊 翻页组件位置分析:');
    const styles = paginationContainer[1].match(/([a-z-]+):\s*([^;]+);/g) || [];
    styles.forEach(style => {
      const [property, value] = style.split(':').map(s => s.trim());
      if (['margin-top', 'margin-bottom', 'position', 'top', 'bottom'].includes(property)) {
        console.log(`    ${property}: ${value}`);
      }
    });
    
    const marginTop = paginationContainer[1].match(/margin-top:\s*(\d+)px/);
    if (marginTop) {
      console.log(`\n📐 翻页组件与表格间距: ${marginTop[1]}px`);
      if (parseInt(marginTop[1]) < 20) {
        console.log(`    ⚠️  间距较小，滚动条可能影响视觉效果`);
      }
    }
  }
  
  // 分析5: 滚动条样式自定义
  console.log('\n🔍 分析5: 滚动条样式自定义');
  console.log('='.repeat(50));
  
  // 查找滚动条样式
  const scrollbarStyles = [
    'scrollbar-width',
    'scrollbar-color',
    '::-webkit-scrollbar',
    '::-webkit-scrollbar-track',
    '::-webkit-scrollbar-thumb'
  ];
  
  let hasCustomScrollbar = false;
  scrollbarStyles.forEach(style => {
    const regex = new RegExp(style.replace(/:/g, '\\:'), 'g');
    const matches = content.match(regex);
    if (matches) {
      hasCustomScrollbar = true;
      console.log(`📊 发现自定义滚动条样式: ${style} (${matches.length}处)`);
    }
  });
  
  if (!hasCustomScrollbar) {
    console.log('⚠️  未发现自定义滚动条样式，使用浏览器默认样式');
    console.log('    建议: 添加自定义滚动条样式以改善视觉效果');
  }
  
  // 分析6: 潜在的解决方案
  console.log('\n🔍 分析6: 潜在的解决方案分析');
  console.log('='.repeat(50));
  
  console.log('💡 发现的问题和建议解决方案:');
  
  // 检查是否有不必要的横向滚动
  const unnecessaryScroll = scrollbarBreakpoints.some(range => 
    range.max < 1200 // 在较大屏幕上仍有滚动条
  );
  
  if (unnecessaryScroll) {
    console.log('\n🚨 问题1: 在较大屏幕上出现不必要的横向滚动');
    console.log('   解决方案:');
    console.log('   - 调整表格列的flex值分配');
    console.log('   - 减少表格的min-width设置');
    console.log('   - 优化响应式断点设置');
  }
  
  // 检查滚动条是否影响翻页组件
  console.log('\n🚨 问题2: 横向滚动条可能影响翻页组件显示');
  console.log('   解决方案:');
  console.log('   - 增加翻页组件的margin-top');
  console.log('   - 为表格容器添加padding-bottom');
  console.log('   - 自定义滚动条样式，减少视觉干扰');
  
  // 检查是否需要优化表格布局
  console.log('\n🚨 问题3: 表格布局可能需要优化');
  console.log('   解决方案:');
  console.log('   - 在小屏幕上使用卡片式布局替代表格');
  console.log('   - 隐藏不重要的列');
  console.log('   - 使用省略号处理长文本');
  
  return {
    hasHorizontalScrollbar: scrollbarBreakpoints.length > 0,
    scrollbarRanges: scrollbarBreakpoints,
    hasCustomStyles: hasCustomScrollbar
  };
}

// 运行分析
const result = analyzeScrollbar();

console.log('\n📊 横向滚动条分析总结');
console.log('='.repeat(50));

if (result.hasHorizontalScrollbar) {
  console.log('🚨 确认: 翻页组件上方确实存在横向滚动条');
  console.log(`📊 滚动条出现在 ${result.scrollbarRanges.length} 个屏幕尺寸范围内`);
  console.log('⚠️  这可能影响用户体验和翻页组件的视觉效果');
} else {
  console.log('✅ 未发现明确的横向滚动条设置');
}

if (!result.hasCustomStyles) {
  console.log('💡 建议添加自定义滚动条样式以改善视觉效果');
}
