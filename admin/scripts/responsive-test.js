#!/usr/bin/env node

/**
 * 响应式测试启动脚本
 * 用于快速启动服务管理页面的响应式测试
 * 符合壹心堂开发规范 - 支持8种分辨率测试
 */

const fs = require('fs');
const path = require('path');

console.log('🚀 壹心堂响应式测试工具启动器');
console.log('=====================================');

// 检查是否在正确的目录
const packageJsonPath = path.join(process.cwd(), 'package.json');
if (!fs.existsSync(packageJsonPath)) {
  console.error('❌ 错误：请在admin目录下运行此脚本');
  process.exit(1);
}

// 读取package.json检查项目
try {
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  if (!packageJson.name || !packageJson.name.includes('admin')) {
    console.warn('⚠️ 警告：当前目录可能不是admin项目目录');
  }
} catch (error) {
  console.error('❌ 无法读取package.json:', error.message);
  process.exit(1);
}

// 检查必要文件是否存在
const requiredFiles = [
  'src/utils/responsiveTestTool.js',
  'src/config/responsiveTestConfig.js',
  'src/views/ServiceManagement.vue'
];

console.log('🔍 检查必要文件...');
let allFilesExist = true;

requiredFiles.forEach(file => {
  const filePath = path.join(process.cwd(), file);
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件不存在`);
    allFilesExist = false;
  }
});

if (!allFilesExist) {
  console.error('\n❌ 部分必要文件缺失，请确保已正确安装响应式测试工具');
  process.exit(1);
}

console.log('\n✅ 所有必要文件检查通过');

// 显示使用说明
console.log('\n📖 使用说明:');
console.log('=====================================');
console.log('1. 启动开发服务器: npm run dev');
console.log('2. 打开浏览器访问服务管理页面');
console.log('3. 使用快捷键 Ctrl+Shift+R 启动测试工具');
console.log('4. 或者在浏览器控制台运行: responsiveTestTool.init()');

console.log('\n🔧 测试功能:');
console.log('- 📱 8种分辨率自动测试');
console.log('- 🔍 布局元素检查');
console.log('- 📊 性能指标监控');
console.log('- 🎨 UI规范合规检查');
console.log('- 📋 详细测试报告生成');

console.log('\n🎯 测试分辨率:');
console.log('- 4K显示器: 3840×2160');
console.log('- 2K显示器: 2560×1440');
console.log('- 标准桌面: 1920×1080');
console.log('- 小桌面: 1366×768');
console.log('- 平板横屏: 1024×768');
console.log('- 平板竖屏: 768×1024');
console.log('- 手机横屏: 667×375');
console.log('- 手机竖屏: 375×667');

console.log('\n⚡ 快捷操作:');
console.log('- Ctrl+Shift+R: 切换测试面板');
console.log('- 点击分辨率按钮: 切换到指定分辨率');
console.log('- 运行完整测试: 自动测试所有分辨率');
console.log('- 检查当前布局: 分析当前分辨率下的布局');

console.log('\n📊 测试报告:');
console.log('- 实时显示测试结果');
console.log('- 自动保存到localStorage');
console.log('- 支持导出详细报告');
console.log('- 包含性能和UI合规性分析');

// 检查开发服务器是否运行
const { exec } = require('child_process');

console.log('\n🔍 检查开发服务器状态...');

exec('lsof -ti:5173', (error, stdout, stderr) => {
  if (stdout.trim()) {
    console.log('✅ 开发服务器正在运行 (端口 5173)');
    console.log('🌐 请访问: http://localhost:5173');
    console.log('📄 导航到服务管理页面开始测试');
  } else {
    console.log('⚠️ 开发服务器未运行');
    console.log('💡 请先运行: npm run dev');
  }
});

// 生成测试检查清单
console.log('\n📋 测试检查清单:');
console.log('=====================================');

const checkList = [
  '□ 搜索区域在所有分辨率下可见',
  '□ 表格布局在移动端正确适配',
  '□ 分页组件响应式显示正常',
  '□ 操作按钮大小符合触摸标准',
  '□ 模态框在小屏幕下不被遮挡',
  '□ 滚动条仅在必要时显示',
  '□ 文字大小在移动端可读',
  '□ 图片在不同比例下正确显示',
  '□ 动画效果流畅无卡顿',
  '□ 颜色对比度符合可访问性标准'
];

checkList.forEach(item => {
  console.log(item);
});

console.log('\n🎯 性能标准:');
console.log('- 桌面端加载时间 < 3秒');
console.log('- 平板端加载时间 < 4秒');
console.log('- 移动端加载时间 < 5秒');
console.log('- 搜索响应时间 < 500ms');
console.log('- 分页切换时间 < 300ms');

console.log('\n🚨 注意事项:');
console.log('- 测试过程中请勿最小化浏览器窗口');
console.log('- 确保浏览器缩放比例为100%');
console.log('- 建议使用Chrome或Firefox浏览器');
console.log('- 测试时关闭其他占用资源的应用');

console.log('\n✨ 开始测试吧！');
console.log('=====================================');

// 如果是开发环境，提供额外的调试信息
if (process.env.NODE_ENV === 'development') {
  console.log('\n🔧 开发者调试信息:');
  console.log('- 测试工具源码: src/utils/responsiveTestTool.js');
  console.log('- 测试配置: src/config/responsiveTestConfig.js');
  console.log('- 集成位置: src/views/ServiceManagement.vue');
  console.log('- 控制台命令: responsiveTestTool.runFullTest()');
}
