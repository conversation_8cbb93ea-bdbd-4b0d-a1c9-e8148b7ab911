/**
 * 自动化响应式测试脚本
 * 使用Puppeteer自动测试服务管理页面的响应式布局
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// 测试配置
const TEST_CONFIG = {
  baseUrl: 'http://localhost:3000',
  testPath: '/services',
  loginPath: '/login',
  resolutions: [
    { name: '4K超高清', width: 3840, height: 2160, type: 'desktop' },
    { name: '2K高清', width: 2560, height: 1440, type: 'desktop' },
    { name: '标准桌面', width: 1920, height: 1080, type: 'desktop' },
    { name: '小桌面', width: 1366, height: 768, type: 'desktop' },
    { name: '平板横屏', width: 1024, height: 768, type: 'tablet' },
    { name: '平板竖屏', width: 768, height: 1024, type: 'tablet' },
    { name: '手机横屏', width: 667, height: 375, type: 'mobile' },
    { name: '手机竖屏', width: 375, height: 667, type: 'mobile' }
  ],
  timeout: 30000,
  waitForLoad: 3000
};

class AutoResponsiveTest {
  constructor() {
    this.browser = null;
    this.page = null;
    this.testResults = [];
    this.issues = [];
  }

  async init() {
    console.log('🚀 启动自动化响应式测试...');
    
    this.browser = await puppeteer.launch({
      headless: false, // 显示浏览器窗口以便观察
      defaultViewport: null,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-dev-shm-usage',
        '--disable-web-security',
        '--allow-running-insecure-content'
      ]
    });

    this.page = await this.browser.newPage();
    
    // 设置用户代理
    await this.page.setUserAgent('Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36');
    
    console.log('✅ 浏览器已启动');
  }

  async loginAndNavigate() {
    try {
      // 首先访问登录页面
      await this.page.goto(TEST_CONFIG.baseUrl + TEST_CONFIG.loginPath, {
        waitUntil: 'networkidle2',
        timeout: TEST_CONFIG.timeout
      });

      // 等待登录表单加载
      await this.page.waitForSelector('input[type="text"], input[type="email"], .login-form', { timeout: 5000 });

      // 尝试自动登录（如果有登录表单）
      const hasLoginForm = await this.page.$('input[type="password"]');
      if (hasLoginForm) {
        // 填写登录信息（使用测试账号）
        await this.page.type('input[type="text"], input[type="email"]', 'admin');
        await this.page.type('input[type="password"]', 'admin123');

        // 点击登录按钮
        const loginButton = await this.page.$('button[type="submit"], .login-btn, .submit-btn');
        if (loginButton) {
          await loginButton.click();

          // 等待登录完成并跳转
          await this.page.waitForNavigation({ waitUntil: 'networkidle2', timeout: 10000 });
        }
      }

      // 导航到服务管理页面
      await this.page.goto(TEST_CONFIG.baseUrl + TEST_CONFIG.testPath, {
        waitUntil: 'networkidle2',
        timeout: TEST_CONFIG.timeout
      });

      // 等待页面内容加载
      await this.page.waitForSelector('body', { timeout: 5000 });

    } catch (error) {
      console.log('⚠️ 登录过程中出现问题，尝试直接访问页面:', error.message);

      // 如果登录失败，尝试直接访问页面
      await this.page.goto(TEST_CONFIG.baseUrl + TEST_CONFIG.testPath, {
        waitUntil: 'networkidle2',
        timeout: TEST_CONFIG.timeout
      });
    }
  }

  async runFullTest() {
    try {
      await this.init();
      
      console.log('📊 开始测试所有分辨率...');
      
      for (const resolution of TEST_CONFIG.resolutions) {
        console.log(`\n📐 测试分辨率: ${resolution.name} (${resolution.width}×${resolution.height})`);
        
        const result = await this.testResolution(resolution);
        this.testResults.push(result);
        
        // 等待一下再测试下一个分辨率
        await this.delay(1000);
      }
      
      // 生成测试报告
      const report = this.generateReport();
      await this.saveReport(report);
      
      console.log('\n✅ 所有测试完成！');
      console.log('📋 测试报告已生成');
      
      return report;
      
    } catch (error) {
      console.error('❌ 测试过程中出现错误:', error);
      throw error;
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }

  async testResolution(resolution) {
    const startTime = Date.now();
    const result = {
      resolution,
      timestamp: new Date().toISOString(),
      loadTime: 0,
      checks: {},
      issues: [],
      screenshots: []
    };

    try {
      // 设置视窗大小
      await this.page.setViewport({
        width: resolution.width,
        height: resolution.height
      });

      // 先登录，然后导航到测试页面
      const navigationStart = Date.now();
      await this.loginAndNavigate();
      result.loadTime = Date.now() - navigationStart;
      
      // 等待页面完全加载
      await this.delay(TEST_CONFIG.waitForLoad);
      
      // 执行各项检查
      result.checks = await this.performChecks(resolution);
      
      // 截图
      const screenshotPath = await this.takeScreenshot(resolution);
      result.screenshots.push(screenshotPath);
      
      // 收集问题
      result.issues = this.collectIssues(result.checks, resolution);
      
      console.log(`  ⏱️  加载时间: ${result.loadTime}ms`);
      console.log(`  ✅ 检查完成: ${Object.keys(result.checks).length}项`);
      console.log(`  ⚠️  发现问题: ${result.issues.length}个`);
      
    } catch (error) {
      console.error(`  ❌ 测试失败: ${error.message}`);
      result.error = error.message;
    }

    return result;
  }

  async performChecks(resolution) {
    const checks = {};

    try {
      // 检查搜索区域
      checks.searchSection = await this.checkSearchSection();
      
      // 检查表格布局
      checks.tableLayout = await this.checkTableLayout();
      
      // 检查分页组件
      checks.pagination = await this.checkPagination();
      
      // 检查操作按钮
      checks.actionButtons = await this.checkActionButtons();
      
      // 检查响应式适配
      checks.responsive = await this.checkResponsiveAdaptation(resolution);
      
      // 检查滚动条
      checks.scrollbars = await this.checkScrollbars();
      
      // 检查性能
      checks.performance = await this.checkPerformance();
      
    } catch (error) {
      console.error('检查过程中出现错误:', error);
    }

    return checks;
  }

  async checkSearchSection() {
    return await this.page.evaluate(() => {
      // 根据实际的ServiceManagement.vue组件结构查找搜索区域
      const searchSection = document.querySelector('.action-toolbar, .search-cubism, .smart-search-container, .picasso-services .action-toolbar');
      if (!searchSection) return { status: 'fail', message: '搜索区域未找到' };

      const rect = searchSection.getBoundingClientRect();
      const searchInput = document.querySelector('.search-fragment, input[placeholder*="搜索"], input[role="searchbox"]');

      const result = {
        status: 'pass',
        visible: rect.width > 0 && rect.height > 0,
        width: Math.round(rect.width),
        height: Math.round(rect.height),
        hasInput: !!searchInput
      };

      if (!result.visible) {
        result.status = 'fail';
        result.message = '搜索区域不可见';
      } else if (result.width < 200) {
        result.status = 'warning';
        result.message = `搜索区域宽度过小: ${result.width}px`;
      }

      if (searchInput) {
        const inputRect = searchInput.getBoundingClientRect();
        result.inputWidth = Math.round(inputRect.width);
        if (result.inputWidth < 150) {
          result.status = 'warning';
          result.message = `搜索输入框宽度过小: ${result.inputWidth}px`;
        }
      }

      return result;
    });
  }

  async checkTableLayout() {
    return await this.page.evaluate(() => {
      // 根据实际的ServiceManagement.vue组件结构查找表格
      const tableContainer = document.querySelector('.services-grid, .picasso-services, .services-container, .main-content');
      if (!tableContainer) return { status: 'fail', message: '表格容器未找到' };

      const rect = tableContainer.getBoundingClientRect();
      const hasHorizontalScroll = tableContainer.scrollWidth > tableContainer.clientWidth;
      const dataRows = document.querySelectorAll('.service-card, .service-item, .data-row, .service-row');

      const result = {
        status: 'pass',
        width: Math.round(rect.width),
        height: Math.round(rect.height),
        hasHorizontalScroll,
        rowCount: dataRows.length,
        visible: rect.width > 0 && rect.height > 0
      };

      if (!result.visible) {
        result.status = 'fail';
        result.message = '表格容器不可见';
      } else if (result.rowCount === 0) {
        result.status = 'warning';
        result.message = '未找到数据行';
      }

      // 检查表格头部或标题区域
      const tableHeader = document.querySelector('.services-header, .page-title, h1, h2');
      if (tableHeader) {
        const headerRect = tableHeader.getBoundingClientRect();
        result.headerVisible = headerRect.width > 0 && headerRect.height > 0;
        result.headerHeight = Math.round(headerRect.height);
      }

      return result;
    });
  }

  async checkPagination() {
    return await this.page.evaluate(() => {
      // 根据实际组件查找分页
      const pagination = document.querySelector('.pagination-container, .pagination, .page-controls, .van-gogh-pagination');
      if (!pagination) return { status: 'warning', message: '分页组件未找到' };

      const rect = pagination.getBoundingClientRect();
      const pageButtons = pagination.querySelectorAll('.page-btn, .page-number, button');
      const totalInfo = pagination.querySelector('.total-info, .page-info');

      const result = {
        status: 'pass',
        visible: rect.width > 0 && rect.height > 0,
        width: Math.round(rect.width),
        height: Math.round(rect.height),
        buttonCount: pageButtons.length,
        hasTotalInfo: !!totalInfo
      };

      if (!result.visible) {
        result.status = 'fail';
        result.message = '分页组件不可见';
      } else if (result.buttonCount === 0) {
        result.status = 'warning';
        result.message = '未找到分页按钮';
      }

      return result;
    });
  }

  async checkActionButtons() {
    return await this.page.evaluate(() => {
      // 查找各种可能的按钮
      const actionButtons = document.querySelectorAll('.action-btn, .btn, button, .edit-btn, .delete-btn, .add-btn, .confirm-btn');
      if (actionButtons.length === 0) return { status: 'warning', message: '未找到操作按钮' };

      const buttons = Array.from(actionButtons).map(btn => {
        const rect = btn.getBoundingClientRect();
        const style = window.getComputedStyle(btn);
        return {
          width: Math.round(rect.width),
          height: Math.round(rect.height),
          visible: rect.width > 0 && rect.height > 0,
          fontSize: parseFloat(style.fontSize),
          padding: style.padding
        };
      });

      const result = {
        status: 'pass',
        buttonCount: buttons.length,
        buttons: buttons,
        minWidth: Math.min(...buttons.map(b => b.width)),
        minHeight: Math.min(...buttons.map(b => b.height)),
        avgWidth: Math.round(buttons.reduce((sum, b) => sum + b.width, 0) / buttons.length),
        avgHeight: Math.round(buttons.reduce((sum, b) => sum + b.height, 0) / buttons.length)
      };

      // 检查触摸友好性 (最小44px)
      if (result.minHeight < 44) {
        result.status = 'warning';
        result.message = `按钮高度过小，不符合触摸标准: ${result.minHeight}px < 44px`;
      }

      return result;
    });
  }

  async checkResponsiveAdaptation(resolution) {
    return await this.page.evaluate((res) => {
      const result = {
        status: 'pass',
        deviceType: res.type,
        breakpoint: res.width <= 768 ? 'mobile' : res.width <= 1024 ? 'tablet' : 'desktop'
      };

      // 移动端特殊检查
      if (res.type === 'mobile') {
        // 检查是否有水平滚动
        const hasHorizontalScroll = document.body.scrollWidth > window.innerWidth;
        result.hasHorizontalScroll = hasHorizontalScroll;
        
        if (hasHorizontalScroll) {
          result.status = 'warning';
          result.message = '移动端出现水平滚动条';
        }

        // 检查文字大小
        const textElements = document.querySelectorAll('p, span, div, td, th');
        const fontSizes = Array.from(textElements).map(el => {
          const style = window.getComputedStyle(el);
          return parseFloat(style.fontSize);
        }).filter(size => size > 0);

        if (fontSizes.length > 0) {
          result.minFontSize = Math.min(...fontSizes);
          if (result.minFontSize < 14) {
            result.status = 'warning';
            result.message = `移动端字体过小: ${result.minFontSize}px < 14px`;
          }
        }
      }

      return result;
    }, resolution);
  }

  async checkScrollbars() {
    return await this.page.evaluate(() => {
      const scrollableElements = [
        '.table-body',
        '.table-container', 
        '.services-table',
        '.modal-content',
        '.form-modal'
      ];

      const scrollInfo = [];
      
      scrollableElements.forEach(selector => {
        const element = document.querySelector(selector);
        if (element) {
          const hasVerticalScroll = element.scrollHeight > element.clientHeight;
          const hasHorizontalScroll = element.scrollWidth > element.clientWidth;
          
          if (hasVerticalScroll || hasHorizontalScroll) {
            scrollInfo.push({
              selector,
              vertical: hasVerticalScroll,
              horizontal: hasHorizontalScroll,
              scrollHeight: element.scrollHeight,
              clientHeight: element.clientHeight,
              scrollWidth: element.scrollWidth,
              clientWidth: element.clientWidth
            });
          }
        }
      });

      return {
        status: 'pass',
        scrollableElements: scrollInfo.length,
        details: scrollInfo
      };
    });
  }

  async checkPerformance() {
    const metrics = await this.page.metrics();
    
    return {
      status: 'pass',
      jsHeapUsedSize: Math.round(metrics.JSHeapUsedSize / 1024 / 1024), // MB
      jsHeapTotalSize: Math.round(metrics.JSHeapTotalSize / 1024 / 1024), // MB
      domNodes: metrics.Nodes,
      layoutCount: metrics.LayoutCount,
      recalcStyleCount: metrics.RecalcStyleCount
    };
  }

  collectIssues(checks, resolution) {
    const issues = [];

    Object.entries(checks).forEach(([checkName, result]) => {
      if (result.status === 'fail') {
        issues.push({
          type: 'error',
          check: checkName,
          message: result.message || `${checkName} 检查失败`,
          resolution: resolution.name
        });
      } else if (result.status === 'warning') {
        issues.push({
          type: 'warning',
          check: checkName,
          message: result.message || `${checkName} 存在警告`,
          resolution: resolution.name
        });
      }
    });

    return issues;
  }

  async takeScreenshot(resolution) {
    const filename = `screenshot-${resolution.name.replace(/[^a-zA-Z0-9]/g, '-')}-${Date.now()}.png`;
    const filepath = path.join(process.cwd(), 'test-reports', filename);
    
    // 确保目录存在
    const dir = path.dirname(filepath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    await this.page.screenshot({
      path: filepath,
      fullPage: true
    });
    
    return filepath;
  }

  generateReport() {
    const totalTests = this.testResults.length;
    const passedTests = this.testResults.filter(r => !r.error && r.issues.length === 0).length;
    const warningTests = this.testResults.filter(r => !r.error && r.issues.some(i => i.type === 'warning')).length;
    const failedTests = this.testResults.filter(r => r.error || r.issues.some(i => i.type === 'error')).length;

    const allIssues = this.testResults.flatMap(r => r.issues);
    const errorCount = allIssues.filter(i => i.type === 'error').length;
    const warningCount = allIssues.filter(i => i.type === 'warning').length;

    return {
      summary: {
        totalTests,
        passedTests,
        warningTests,
        failedTests,
        errorCount,
        warningCount,
        testDate: new Date().toISOString()
      },
      results: this.testResults,
      issues: allIssues,
      recommendations: this.generateRecommendations(allIssues)
    };
  }

  generateRecommendations(issues) {
    const recommendations = [];
    
    // 分析常见问题并提供建议
    const issueTypes = {};
    issues.forEach(issue => {
      issueTypes[issue.check] = (issueTypes[issue.check] || 0) + 1;
    });

    Object.entries(issueTypes).forEach(([check, count]) => {
      if (count >= 3) { // 如果同一问题在多个分辨率下出现
        switch (check) {
          case 'searchSection':
            recommendations.push('建议优化搜索区域的响应式布局，确保在小屏幕下有足够的宽度');
            break;
          case 'actionButtons':
            recommendations.push('建议增加按钮的最小高度到44px以符合触摸标准');
            break;
          case 'responsive':
            recommendations.push('建议检查移动端的水平滚动问题，可能需要调整容器宽度');
            break;
          case 'tableLayout':
            recommendations.push('建议优化表格在小屏幕下的显示方式，考虑使用卡片式布局');
            break;
        }
      }
    });

    return recommendations;
  }

  async saveReport(report) {
    const filename = `responsive-test-report-${Date.now()}.json`;
    const filepath = path.join(process.cwd(), 'test-reports', filename);
    
    // 确保目录存在
    const dir = path.dirname(filepath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(filepath, JSON.stringify(report, null, 2));
    console.log(`📋 测试报告已保存: ${filepath}`);
    
    return filepath;
  }

  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

// 运行测试
async function runTest() {
  const tester = new AutoResponsiveTest();
  
  try {
    const report = await tester.runFullTest();
    
    // 输出摘要
    console.log('\n📊 测试摘要:');
    console.log('=====================================');
    console.log(`总测试数: ${report.summary.totalTests}`);
    console.log(`✅ 通过: ${report.summary.passedTests}`);
    console.log(`⚠️ 警告: ${report.summary.warningTests}`);
    console.log(`❌ 失败: ${report.summary.failedTests}`);
    console.log(`🐛 错误数: ${report.summary.errorCount}`);
    console.log(`⚠️ 警告数: ${report.summary.warningCount}`);
    
    if (report.recommendations.length > 0) {
      console.log('\n💡 改进建议:');
      report.recommendations.forEach((rec, index) => {
        console.log(`${index + 1}. ${rec}`);
      });
    }
    
    return report;
    
  } catch (error) {
    console.error('❌ 测试失败:', error);
    process.exit(1);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runTest();
}

module.exports = { AutoResponsiveTest, runTest };
