#!/usr/bin/env node

/**
 * 登录问题诊断和修复脚本
 * 自动检测和修复登录页面的常见问题
 */

const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

class LoginDiagnosis {
  constructor() {
    this.browser = null;
    this.page = null;
    this.issues = [];
    this.fixes = [];
  }

  async init() {
    console.log('🔧 启动登录诊断工具...');
    
    this.browser = await puppeteer.launch({
      headless: false,
      defaultViewport: null,
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-web-security'
      ]
    });

    this.page = await this.browser.newPage();
    console.log('✅ 浏览器已启动');
  }

  async diagnoseLogin() {
    try {
      console.log('🔍 开始诊断登录页面...');
      
      // 1. 检查页面加载
      await this.checkPageLoad();
      
      // 2. 检查表单元素
      await this.checkFormElements();
      
      // 3. 检查API连接
      await this.checkAPIConnection();
      
      // 4. 测试登录流程
      await this.testLoginFlow();
      
      // 5. 检查响应式设计
      await this.checkResponsiveDesign();
      
      // 生成诊断报告
      this.generateReport();
      
    } catch (error) {
      console.error('❌ 诊断过程中出现错误:', error);
    } finally {
      if (this.browser) {
        await this.browser.close();
      }
    }
  }

  async checkPageLoad() {
    console.log('📄 检查页面加载...');
    
    try {
      const startTime = Date.now();
      
      await this.page.goto('http://localhost:3000/login', {
        waitUntil: 'networkidle2',
        timeout: 10000
      });
      
      const loadTime = Date.now() - startTime;
      
      if (loadTime > 3000) {
        this.issues.push({
          type: 'warning',
          category: 'performance',
          message: `页面加载时间过长: ${loadTime}ms`,
          suggestion: '优化资源加载，减少首屏渲染时间'
        });
      } else {
        console.log(`✅ 页面加载正常: ${loadTime}ms`);
      }
      
      // 检查页面标题
      const title = await this.page.title();
      if (!title.includes('登录')) {
        this.issues.push({
          type: 'warning',
          category: 'seo',
          message: '页面标题不包含"登录"关键词',
          suggestion: '更新页面标题以提高SEO效果'
        });
      }
      
    } catch (error) {
      this.issues.push({
        type: 'error',
        category: 'loading',
        message: '页面加载失败',
        error: error.message,
        suggestion: '检查开发服务器是否正常运行'
      });
    }
  }

  async checkFormElements() {
    console.log('📝 检查表单元素...');
    
    try {
      // 检查用户名输入框
      const usernameInput = await this.page.$('input[placeholder*="用户名"], input[name="username"]');
      if (!usernameInput) {
        this.issues.push({
          type: 'error',
          category: 'form',
          message: '未找到用户名输入框',
          suggestion: '确保用户名输入框存在且有正确的选择器'
        });
      } else {
        console.log('✅ 用户名输入框存在');
      }
      
      // 检查密码输入框
      const passwordInput = await this.page.$('input[type="password"], input[name="password"]');
      if (!passwordInput) {
        this.issues.push({
          type: 'error',
          category: 'form',
          message: '未找到密码输入框',
          suggestion: '确保密码输入框存在且类型为password'
        });
      } else {
        console.log('✅ 密码输入框存在');
      }
      
      // 检查登录按钮
      const loginButton = await this.page.$('button[type="submit"], .login-button, .ant-btn-primary');
      if (!loginButton) {
        this.issues.push({
          type: 'error',
          category: 'form',
          message: '未找到登录按钮',
          suggestion: '确保登录按钮存在且有正确的类型或类名'
        });
      } else {
        console.log('✅ 登录按钮存在');
      }
      
      // 检查表单验证
      await this.checkFormValidation();
      
    } catch (error) {
      this.issues.push({
        type: 'error',
        category: 'form',
        message: '表单元素检查失败',
        error: error.message
      });
    }
  }

  async checkFormValidation() {
    console.log('🔍 检查表单验证...');
    
    try {
      // 尝试提交空表单
      const submitButton = await this.page.$('button[type="submit"], .login-button, .ant-btn-primary');
      if (submitButton) {
        await submitButton.click();
        
        // 等待一下看是否有验证提示
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 检查是否有错误提示
        const errorMessages = await this.page.$$('.error-message, .ant-form-item-explain-error, .form-error');
        if (errorMessages.length === 0) {
          this.issues.push({
            type: 'warning',
            category: 'validation',
            message: '空表单提交时没有显示验证错误',
            suggestion: '添加前端表单验证提示'
          });
        } else {
          console.log('✅ 表单验证正常');
        }
      }
    } catch (error) {
      console.log('⚠️ 表单验证检查出现问题:', error.message);
    }
  }

  async checkAPIConnection() {
    console.log('🌐 检查API连接...');
    
    try {
      // 监听网络请求
      const responses = [];
      this.page.on('response', response => {
        if (response.url().includes('/api/auth/login')) {
          responses.push({
            url: response.url(),
            status: response.status(),
            statusText: response.statusText()
          });
        }
      });
      
      // 尝试登录以触发API调用
      await this.fillLoginForm('test', 'test');
      await this.submitForm();
      
      // 等待API响应
      await new Promise(resolve => setTimeout(resolve, 2000));
      
      if (responses.length === 0) {
        this.issues.push({
          type: 'error',
          category: 'api',
          message: '未检测到登录API调用',
          suggestion: '检查前端是否正确调用登录API'
        });
      } else {
        const response = responses[0];
        if (response.status >= 500) {
          this.issues.push({
            type: 'error',
            category: 'api',
            message: `API服务器错误: ${response.status} ${response.statusText}`,
            suggestion: '检查后端服务器状态'
          });
        } else if (response.status === 404) {
          this.issues.push({
            type: 'error',
            category: 'api',
            message: 'API端点不存在',
            suggestion: '检查API路由配置'
          });
        } else {
          console.log(`✅ API连接正常: ${response.status}`);
        }
      }
      
    } catch (error) {
      this.issues.push({
        type: 'error',
        category: 'api',
        message: 'API连接检查失败',
        error: error.message
      });
    }
  }

  async testLoginFlow() {
    console.log('🔐 测试登录流程...');
    
    try {
      // 刷新页面重新开始
      await this.page.reload({ waitUntil: 'networkidle2' });
      
      // 使用正确的管理员账号测试
      await this.fillLoginForm('root', '13210583333');
      
      // 提交表单
      await this.submitForm();
      
      // 等待登录结果
      await new Promise(resolve => setTimeout(resolve, 3000));
      
      // 检查是否跳转到仪表盘
      const currentUrl = this.page.url();
      if (currentUrl.includes('/dashboard') || currentUrl.includes('/services')) {
        console.log('✅ 登录流程正常，成功跳转');
        this.fixes.push('登录功能正常工作');
      } else if (currentUrl.includes('/login')) {
        // 检查是否有错误提示
        const errorElement = await this.page.$('.ant-message-error, .error-message, .login-error');
        if (errorElement) {
          const errorText = await errorElement.textContent();
          this.issues.push({
            type: 'warning',
            category: 'auth',
            message: `登录失败: ${errorText}`,
            suggestion: '检查用户凭据或后端认证逻辑'
          });
        } else {
          this.issues.push({
            type: 'warning',
            category: 'auth',
            message: '登录后未跳转，可能存在路由问题',
            suggestion: '检查登录成功后的路由跳转逻辑'
          });
        }
      }
      
    } catch (error) {
      this.issues.push({
        type: 'error',
        category: 'auth',
        message: '登录流程测试失败',
        error: error.message
      });
    }
  }

  async checkResponsiveDesign() {
    console.log('📱 检查响应式设计...');
    
    const viewports = [
      { width: 375, height: 667, name: '手机竖屏' },
      { width: 768, height: 1024, name: '平板竖屏' },
      { width: 1920, height: 1080, name: '桌面' }
    ];
    
    for (const viewport of viewports) {
      try {
        await this.page.setViewport(viewport);
        await new Promise(resolve => setTimeout(resolve, 500));
        
        // 检查登录卡片是否可见
        const loginCard = await this.page.$('.login-card, .login-container');
        if (loginCard) {
          const rect = await loginCard.boundingBox();
          if (rect.width > viewport.width || rect.height > viewport.height) {
            this.issues.push({
              type: 'warning',
              category: 'responsive',
              message: `${viewport.name}下登录卡片超出视窗`,
              suggestion: '调整登录卡片的响应式样式'
            });
          } else {
            console.log(`✅ ${viewport.name}下显示正常`);
          }
        }
        
      } catch (error) {
        this.issues.push({
          type: 'warning',
          category: 'responsive',
          message: `${viewport.name}响应式检查失败`,
          error: error.message
        });
      }
    }
  }

  async fillLoginForm(username, password) {
    const usernameInput = await this.page.$('input[placeholder*="用户名"], input[name="username"]');
    const passwordInput = await this.page.$('input[type="password"], input[name="password"]');
    
    if (usernameInput) {
      await usernameInput.click({ clickCount: 3 }); // 选中所有文本
      await usernameInput.type(username);
    }
    
    if (passwordInput) {
      await passwordInput.click({ clickCount: 3 });
      await passwordInput.type(password);
    }
  }

  async submitForm() {
    const submitButton = await this.page.$('button[type="submit"], .login-button, .ant-btn-primary');
    if (submitButton) {
      await submitButton.click();
    }
  }

  generateReport() {
    console.log('\n📋 登录诊断报告');
    console.log('=====================================');
    
    if (this.issues.length === 0) {
      console.log('🎉 恭喜！未发现任何问题');
    } else {
      console.log(`发现 ${this.issues.length} 个问题:`);
      
      this.issues.forEach((issue, index) => {
        const icon = issue.type === 'error' ? '❌' : '⚠️';
        console.log(`\n${index + 1}. ${icon} [${issue.category.toUpperCase()}] ${issue.message}`);
        if (issue.suggestion) {
          console.log(`   💡 建议: ${issue.suggestion}`);
        }
        if (issue.error) {
          console.log(`   🔍 详情: ${issue.error}`);
        }
      });
    }
    
    if (this.fixes.length > 0) {
      console.log('\n✅ 正常功能:');
      this.fixes.forEach((fix, index) => {
        console.log(`${index + 1}. ${fix}`);
      });
    }
    
    // 保存报告到文件
    const report = {
      timestamp: new Date().toISOString(),
      issues: this.issues,
      fixes: this.fixes,
      summary: {
        totalIssues: this.issues.length,
        errors: this.issues.filter(i => i.type === 'error').length,
        warnings: this.issues.filter(i => i.type === 'warning').length
      }
    };
    
    const reportPath = path.join(process.cwd(), 'test-reports', `login-diagnosis-${Date.now()}.json`);
    
    // 确保目录存在
    const dir = path.dirname(reportPath);
    if (!fs.existsSync(dir)) {
      fs.mkdirSync(dir, { recursive: true });
    }
    
    fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
    console.log(`\n📄 详细报告已保存: ${reportPath}`);
  }
}

// 运行诊断
async function runDiagnosis() {
  const diagnosis = new LoginDiagnosis();
  await diagnosis.init();
  await diagnosis.diagnoseLogin();
}

// 如果直接运行此脚本
if (require.main === module) {
  runDiagnosis().catch(console.error);
}

module.exports = { LoginDiagnosis, runDiagnosis };
