/**
 * 统一的浏览器配置工具
 * 解决多标签页问题，提供一致的浏览器设置
 */

const puppeteer = require('puppeteer');

class BrowserManager {
  /**
   * 创建浏览器实例，避免多标签页问题
   * @param {Object} options - 浏览器配置选项
   * @returns {Object} { browser, page }
   */
  static async createBrowser(options = {}) {
    const defaultOptions = {
      headless: false,
      defaultViewport: { width: 1280, height: 720 },
      args: [
        '--no-sandbox',
        '--disable-setuid-sandbox',
        '--disable-web-security',
        '--disable-features=VizDisplayCompositor'
      ]
    };

    const config = { ...defaultOptions, ...options };
    
    const browser = await puppeteer.launch(config);
    
    // 获取所有现有页面，使用默认页面避免创建多个标签
    const pages = await browser.pages();
    let page;
    
    if (pages.length > 0) {
      page = pages[0];
      console.log('✅ 使用默认页面，避免多标签页');
    } else {
      page = await browser.newPage();
      console.log('✅ 创建新页面');
    }
    
    // 设置默认超时
    page.setDefaultTimeout(30000);
    
    // 设置视口大小
    if (config.defaultViewport) {
      await page.setViewport(config.defaultViewport);
    }
    
    return { browser, page };
  }

  /**
   * 为页面添加标准监听器
   * @param {Object} page - Puppeteer页面对象
   * @param {Object} collectors - 收集器对象 { errors: [], warnings: [] }
   */
  static addStandardListeners(page, collectors = {}) {
    const { errors = [], warnings = [] } = collectors;

    // 监控console输出
    page.on('console', (msg) => {
      const type = msg.type();
      const text = msg.text();
      
      if (type === 'error') {
        errors.push({
          message: text,
          url: page.url(),
          timestamp: new Date().toISOString()
        });
        console.log(`🔴 Console错误: ${text}`);
      } else if (type === 'warning') {
        warnings.push({
          message: text,
          url: page.url(),
          timestamp: new Date().toISOString()
        });
        console.log(`🟡 Console警告: ${text}`);
      }
    });

    // 监控网络错误
    page.on('response', (response) => {
      if (response.status() >= 400) {
        const error = {
          url: response.url(),
          status: response.status(),
          statusText: response.statusText(),
          timestamp: new Date().toISOString()
        };
        
        errors.push({
          message: `HTTP错误: ${response.status()} ${response.url()}`,
          url: page.url(),
          timestamp: new Date().toISOString()
        });
        
        console.log(`🌐 HTTP错误: ${response.status()} ${response.url()}`);
      }
    });

    // 监控页面错误
    page.on('pageerror', (error) => {
      errors.push({
        message: `页面错误: ${error.message}`,
        url: page.url(),
        timestamp: new Date().toISOString()
      });
      console.log(`💥 页面错误: ${error.message}`);
    });

    return { errors, warnings };
  }

  /**
   * 安全关闭浏览器
   * @param {Object} browser - Puppeteer浏览器对象
   */
  static async closeBrowser(browser) {
    if (browser) {
      try {
        await browser.close();
        console.log('✅ 浏览器已安全关闭');
      } catch (error) {
        console.log(`⚠️ 关闭浏览器时出错: ${error.message}`);
      }
    }
  }

  /**
   * 等待页面加载完成
   * @param {Object} page - Puppeteer页面对象
   * @param {number} timeout - 超时时间（毫秒）
   */
  static async waitForPageLoad(page, timeout = 3000) {
    try {
      await page.waitForLoadState?.('networkidle') || 
            await new Promise(resolve => setTimeout(resolve, timeout));
      console.log('✅ 页面加载完成');
    } catch (error) {
      console.log(`⚠️ 等待页面加载超时: ${error.message}`);
    }
  }
}

module.exports = BrowserManager;
