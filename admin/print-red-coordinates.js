#!/usr/bin/env node

/**
 * 红色区域坐标打印工具
 * 打印最后一行(红色区域)的上下左右坐标并核对问题
 */

console.log('🎯 红色区域坐标打印工具启动...\n');

// 创建浏览器端坐标检测脚本
const browserScript = `
// 红色区域坐标检测脚本 - 在浏览器控制台运行
function printRedCoordinates() {
  console.log('🔍 开始检测红色区域(最后一行)坐标...');
  console.log('='.repeat(60));
  
  // 查找关键元素
  const tableContainer = document.querySelector('.table-container');
  const tableBody = document.querySelector('.table-body');
  const lastRow = document.querySelector('.data-row:last-child');
  const paginationContainer = document.querySelector('.pagination-container');
  
  if (!tableContainer || !tableBody || !lastRow || !paginationContainer) {
    console.error('❌ 未找到必要的元素，请确保在服务管理页面运行此脚本');
    return;
  }
  
  // 获取元素的边界矩形
  const tableContainerRect = tableContainer.getBoundingClientRect();
  const tableBodyRect = tableBody.getBoundingClientRect();
  const lastRowRect = lastRow.getBoundingClientRect();
  const paginationRect = paginationContainer.getBoundingClientRect();
  
  console.log('📊 元素坐标详情:');
  console.log('');
  
  // 蓝色区域 (表格容器)
  console.log('🔵 蓝色区域 (表格容器) 坐标:');
  console.log(\`  上边界 (top):    \${Math.round(tableContainerRect.top)}px\`);
  console.log(\`  下边界 (bottom): \${Math.round(tableContainerRect.bottom)}px\`);
  console.log(\`  左边界 (left):   \${Math.round(tableContainerRect.left)}px\`);
  console.log(\`  右边界 (right):  \${Math.round(tableContainerRect.right)}px\`);
  console.log(\`  宽度 (width):    \${Math.round(tableContainerRect.width)}px\`);
  console.log(\`  高度 (height):   \${Math.round(tableContainerRect.height)}px\`);
  console.log('');
  
  // 绿色区域 (表格主体)
  console.log('🟢 绿色区域 (表格主体) 坐标:');
  console.log(\`  上边界 (top):    \${Math.round(tableBodyRect.top)}px\`);
  console.log(\`  下边界 (bottom): \${Math.round(tableBodyRect.bottom)}px\`);
  console.log(\`  左边界 (left):   \${Math.round(tableBodyRect.left)}px\`);
  console.log(\`  右边界 (right):  \${Math.round(tableBodyRect.right)}px\`);
  console.log(\`  宽度 (width):    \${Math.round(tableBodyRect.width)}px\`);
  console.log(\`  高度 (height):   \${Math.round(tableBodyRect.height)}px\`);
  console.log('');
  
  // 红色区域 (最后一行) - 重点检查
  console.log('🔴 红色区域 (最后一行) 坐标:');
  console.log(\`  上边界 (top):    \${Math.round(lastRowRect.top)}px\`);
  console.log(\`  下边界 (bottom): \${Math.round(lastRowRect.bottom)}px\`);
  console.log(\`  左边界 (left):   \${Math.round(lastRowRect.left)}px\`);
  console.log(\`  右边界 (right):  \${Math.round(lastRowRect.right)}px\`);
  console.log(\`  宽度 (width):    \${Math.round(lastRowRect.width)}px\`);
  console.log(\`  高度 (height):   \${Math.round(lastRowRect.height)}px\`);
  console.log('');
  
  // 橙色区域 (翻页组件)
  console.log('🟠 橙色区域 (翻页组件) 坐标:');
  console.log(\`  上边界 (top):    \${Math.round(paginationRect.top)}px\`);
  console.log(\`  下边界 (bottom): \${Math.round(paginationRect.bottom)}px\`);
  console.log(\`  左边界 (left):   \${Math.round(paginationRect.left)}px\`);
  console.log(\`  右边界 (right):  \${Math.round(paginationRect.right)}px\`);
  console.log(\`  宽度 (width):    \${Math.round(paginationRect.width)}px\`);
  console.log(\`  高度 (height):   \${Math.round(paginationRect.height)}px\`);
  console.log('');
  
  // 🎯 核对问题分析
  console.log('🎯 完美规则核对分析:');
  console.log('='.repeat(60));
  
  // 检查1: 红色区域是否在绿色区域内
  console.log('📋 检查1: 红色区域边界约束');
  const redInGreen = {
    top: lastRowRect.top >= tableBodyRect.top,
    bottom: lastRowRect.bottom <= tableBodyRect.bottom,
    left: lastRowRect.left >= tableBodyRect.left,
    right: lastRowRect.right <= tableBodyRect.right
  };
  
  console.log(\`  红色上边界在绿色内: \${redInGreen.top ? '✅' : '❌'} (\${Math.round(lastRowRect.top)} >= \${Math.round(tableBodyRect.top)})\`);
  console.log(\`  红色下边界在绿色内: \${redInGreen.bottom ? '✅' : '❌'} (\${Math.round(lastRowRect.bottom)} <= \${Math.round(tableBodyRect.bottom)})\`);
  console.log(\`  红色左边界在绿色内: \${redInGreen.left ? '✅' : '❌'} (\${Math.round(lastRowRect.left)} >= \${Math.round(tableBodyRect.left)})\`);
  console.log(\`  红色右边界在绿色内: \${redInGreen.right ? '✅' : '❌'} (\${Math.round(lastRowRect.right)} <= \${Math.round(tableBodyRect.right)})\`);
  
  const allInBounds = Object.values(redInGreen).every(v => v);
  console.log(\`  🎯 边界约束结果: \${allInBounds ? '✅ 完全合规' : '❌ 存在越界'}\`);
  console.log('');
  
  // 检查2: 绿色区域是否在蓝色区域内
  console.log('📋 检查2: 绿色区域边界约束');
  const greenInBlue = {
    top: tableBodyRect.top >= tableContainerRect.top,
    bottom: tableBodyRect.bottom <= tableContainerRect.bottom,
    left: tableBodyRect.left >= tableContainerRect.left,
    right: tableBodyRect.right <= tableContainerRect.right
  };
  
  console.log(\`  绿色上边界在蓝色内: \${greenInBlue.top ? '✅' : '❌'} (\${Math.round(tableBodyRect.top)} >= \${Math.round(tableContainerRect.top)})\`);
  console.log(\`  绿色下边界在蓝色内: \${greenInBlue.bottom ? '✅' : '❌'} (\${Math.round(tableBodyRect.bottom)} <= \${Math.round(tableContainerRect.bottom)})\`);
  console.log(\`  绿色左边界在蓝色内: \${greenInBlue.left ? '✅' : '❌'} (\${Math.round(tableBodyRect.left)} >= \${Math.round(tableContainerRect.left)})\`);
  console.log(\`  绿色右边界在蓝色内: \${greenInBlue.right ? '✅' : '❌'} (\${Math.round(tableBodyRect.right)} <= \${Math.round(tableContainerRect.right)})\`);
  
  const greenAllInBounds = Object.values(greenInBlue).every(v => v);
  console.log(\`  🎯 绿色边界约束结果: \${greenAllInBounds ? '✅ 完全合规' : '❌ 存在越界'}\`);
  console.log('');
  
  // 检查3: 间距分析
  console.log('📋 检查3: 关键间距分析');
  const gaps = {
    redToGreenBottom: tableBodyRect.bottom - lastRowRect.bottom,
    greenToBlueBottom: tableContainerRect.bottom - tableBodyRect.bottom,
    greenToPagination: paginationRect.top - tableBodyRect.bottom,
    blueToPagination: paginationRect.top - tableContainerRect.bottom
  };
  
  console.log(\`  红色底部到绿色底部: \${Math.round(gaps.redToGreenBottom)}px \${gaps.redToGreenBottom >= 5 ? '✅' : '⚠️'}\`);
  console.log(\`  绿色底部到蓝色底部: \${Math.round(gaps.greenToBlueBottom)}px \${Math.abs(gaps.greenToBlueBottom) <= 2 ? '✅' : '❌'}\`);
  console.log(\`  绿色底部到翻页组件: \${Math.round(gaps.greenToPagination)}px \${gaps.greenToPagination >= 0 ? '✅' : '❌'}\`);
  console.log(\`  蓝色底部到翻页组件: \${Math.round(gaps.blueToPagination)}px \${gaps.blueToPagination >= 0 ? '✅' : '❌'}\`);
  console.log('');
  
  // 检查4: 滚动条影响
  console.log('📋 检查4: 滚动条影响分析');
  const hasVerticalScroll = tableBody.scrollHeight > tableBody.clientHeight;
  const hasHorizontalScroll = tableBody.scrollWidth > tableBody.clientWidth;
  const scrollbarWidth = tableBody.offsetWidth - tableBody.clientWidth;
  const scrollbarHeight = tableBody.offsetHeight - tableBody.clientHeight;
  
  console.log(\`  纵向滚动条存在: \${hasVerticalScroll ? '是' : '否'}\`);
  console.log(\`  横向滚动条存在: \${hasHorizontalScroll ? '是' : '否'}\`);
  console.log(\`  滚动条宽度: \${scrollbarWidth}px\`);
  console.log(\`  滚动条高度: \${scrollbarHeight}px\`);
  
  if (hasVerticalScroll && gaps.redToGreenBottom < scrollbarHeight + 5) {
    console.log(\`  ⚠️  警告: 最后一行可能被纵向滚动条遮挡\`);
  }
  if (hasHorizontalScroll && gaps.redToGreenBottom < scrollbarHeight + 5) {
    console.log(\`  ⚠️  警告: 最后一行可能被横向滚动条遮挡\`);
  }
  console.log('');
  
  // 总结
  console.log('📊 完美规则核对总结:');
  console.log('='.repeat(60));
  
  const issues = [];
  if (!allInBounds) issues.push('红色区域超出绿色区域边界');
  if (!greenAllInBounds) issues.push('绿色区域超出蓝色区域边界');
  if (gaps.redToGreenBottom < 5) issues.push('红色区域底部间距不足');
  if (Math.abs(gaps.greenToBlueBottom) > 2) issues.push('绿色区域与蓝色区域底部不对齐');
  if (gaps.blueToPagination < 0) issues.push('蓝色区域与翻页组件重叠');
  
  if (issues.length === 0) {
    console.log('🎉 完美！所有坐标都符合完美规则！');
  } else {
    console.log(\`❌ 发现 \${issues.length} 个问题:\`);
    issues.forEach((issue, index) => {
      console.log(\`  \${index + 1}. \${issue}\`);
    });
  }
  
  // 返回详细数据供进一步分析
  return {
    tableContainer: tableContainerRect,
    tableBody: tableBodyRect,
    lastRow: lastRowRect,
    pagination: paginationRect,
    gaps,
    scrollInfo: { hasVerticalScroll, hasHorizontalScroll, scrollbarWidth, scrollbarHeight },
    compliance: { redInGreen: allInBounds, greenInBlue: greenAllInBounds },
    issues
  };
}

// 自动运行检测
console.log('🎯 正在检测红色区域坐标...');
const result = printRedCoordinates();

// 如果有问题，提供修复建议
if (result && result.issues.length > 0) {
  console.log('');
  console.log('💡 修复建议:');
  result.issues.forEach((issue, index) => {
    console.log(\`\${index + 1}. \${issue}\`);
    
    if (issue.includes('红色区域超出')) {
      console.log('   → 增加table-body的padding-bottom');
      console.log('   → 为最后一行添加margin-bottom');
    }
    if (issue.includes('绿色区域超出')) {
      console.log('   → 检查table-body的flex设置');
      console.log('   → 确保align-self: stretch正确');
    }
    if (issue.includes('间距不足')) {
      console.log('   → 增加最后一行的margin-bottom');
      console.log('   → 调整table-body的padding-bottom');
    }
    if (issue.includes('不对齐')) {
      console.log('   → 检查table-container的padding设置');
      console.log('   → 确保table-body占满容器高度');
    }
  });
}
`;

console.log('📋 浏览器端坐标检测脚本已生成');
console.log('');
console.log('🎯 使用方法:');
console.log('1. 打开浏览器，访问服务管理页面: http://localhost:3003/services');
console.log('2. 打开浏览器开发者工具 (F12)');
console.log('3. 在控制台中粘贴并运行以下脚本:');
console.log('');
console.log('='.repeat(60));
console.log(browserScript);
console.log('='.repeat(60));
console.log('');
console.log('📊 脚本将自动打印所有坐标信息并核对问题');
console.log('🎯 重点关注红色区域的上下左右坐标是否符合完美规则');

// 保存脚本到文件
const fs = require('fs');
const path = require('path');

const scriptPath = path.join(__dirname, 'browser-coordinate-checker.js');
fs.writeFileSync(scriptPath, browserScript);

console.log(`\n📁 脚本已保存到: ${scriptPath}`);
console.log('💡 您也可以直接在浏览器中加载此文件运行检测');
