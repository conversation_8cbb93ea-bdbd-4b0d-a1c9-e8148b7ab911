{"name": "yixintang-admin", "version": "1.0.0", "description": "壹心堂中医推拿管理系统后台", "private": true, "scripts": {"dev": "NODE_OPTIONS='--no-deprecation' vite", "build": "NODE_OPTIONS='--no-deprecation' vite build", "preview": "NODE_OPTIONS='--no-deprecation' vite preview", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix", "format": "prettier --write src/", "check:formstate": "node scripts/check-vue-formstate.js", "check:vue": "npm run check:formstate", "test:auto": "echo '🤖 启动自动化测试...' && npm run dev & sleep 15 && node scripts/runAutoTests.js", "test:silent": "echo '🤫 启动静默测试...' && node scripts/silentTest.js", "test:quick": "echo '⚡ 快速静默测试...' && node scripts/silentTest.js", "test:comprehensive": "echo '🏆 启动完善测试...' && node scripts/runComprehensiveTests.js", "test:api": "echo '📡 启动API测试...' && node scripts/runAPITests.js", "test:frontend": "echo '🎨 启动前端测试...' && node scripts/runFrontendTests.js", "test:all": "npm run test:api && npm run test:frontend && npm run test:comprehensive", "test:responsive": "echo '📱 启动响应式测试工具...' && node scripts/responsive-test.js", "outline-check": "echo '🎯 启动轮廓调试检查...' && node ../scripts/automation/outline-debug-check.js", "standards-check": "echo '📏 检查CSS标准合规性...' && node scripts/automation/standards-check.js", "pre-commit": "npm run outline-check && npm run standards-check", "stylelint": "stylelint \"src/**/*.{css,scss,vue}\" --fix", "stylelint-check": "stylelint \"src/**/*.{css,scss,vue}\""}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@babel/runtime": "^7.27.6", "ant-design-vue": "^3.2.20", "axios": "^1.5.0", "cors": "^2.8.5", "dayjs": "^1.11.13", "echarts": "^5.4.3", "express": "^5.1.0", "pinia": "^2.1.6", "pinyin-pro": "^3.26.0", "qrcode": "^1.5.4", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@playwright/test": "^1.53.2", "@rushstack/eslint-patch": "^1.3.3", "@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "less": "^4.3.0", "playwright": "^1.53.2", "postcss-html": "^1.8.0", "postcss-pxtorem": "^6.1.0", "postcss-scss": "^4.0.9", "prettier": "^3.0.3", "puppeteer": "^24.14.0", "sass": "^1.67.0", "stylelint": "^16.22.0", "stylelint-config-recommended-vue": "^1.6.1", "stylelint-config-standard": "^38.0.0", "stylelint-order": "^7.0.0", "stylelint-scss": "^6.12.1", "vite": "^4.4.9"}}