/**
 * 壹心堂CSS标准检查配置
 * 基于轮廓调试标准规则v4.0和历史成功案例
 * 使用现成的Stylelint工具，避免重复造轮子
 */

module.exports = {
  extends: [
    'stylelint-config-standard',
    'stylelint-config-recommended-vue'
  ],
  
  plugins: [
    'stylelint-order',
    'stylelint-scss'
  ],
  
  rules: {
    // 🎯 壹心堂CSS变量强制使用规则（暂时放宽）
    'custom-property-pattern': null, // 暂时禁用，逐步迁移到标准命名

    // 🎯 暂时放宽的规则（逐步改进）
    'keyframes-name-pattern': null, // 暂时允许camelCase动画名
    'declaration-block-single-line-max-declarations': null, // 暂时允许单行多声明
    'no-descending-specificity': null, // 暂时允许选择器优先级问题
    'no-duplicate-selectors': null, // 暂时允许重复选择器
    'at-rule-no-unknown': null, // 暂时允许SCSS语法
    'media-query-no-invalid': null, // 暂时允许SCSS媒体查询
    'no-invalid-position-at-import-rule': null, // 暂时允许@import位置问题
    'property-no-unknown': null, // 暂时允许未知属性（如user-drag）
    'selector-class-pattern': null, // 暂时允许中文类名（业务相关）
    
    // 🎯 基于历史问题的预防规则
    'declaration-block-no-duplicate-properties': [
      true,
      {
        ignore: ['consecutive-duplicates-with-different-values'],
        message: '发现重复的CSS属性，这可能导致样式冲突（历史教训：min-height重复设置）'
      }
    ],
    
    // 🎯 强制使用标准尺寸（暂时放宽，逐步迁移）
    'declaration-property-value-allowed-list': null, // 暂时禁用，逐步迁移到标准变量
    
    // 🎯 禁止可能导致边界超出的CSS属性组合
    'declaration-block-no-shorthand-property-overrides': true,

    // 🎯 !important使用警告（允许但警告）
    'declaration-no-important': null, // 暂时禁用，因为现有代码使用较多
    
    // 🎯 CSS选择器具体性检查（暂时放宽）
    'selector-max-specificity': null, // 暂时禁用，逐步优化选择器
    
    // 🎯 媒体查询标准化（暂时放宽）
    // 'media-query-list-comma-newline-after': null, // 暂时禁用 - 这个规则不存在
    // 'media-feature-name-allowed-list': null, // 暂时禁用 - 这个规则不存在
    
    // 🎯 颜色使用规范（壹心堂品牌标准）
    'color-named': null, // 暂时允许颜色名称，逐步迁移
    // 'color-hex-case': null, // 暂时不强制大小写 - 这个规则不存在
    // 'color-hex-length': null, // 暂时不强制长度 - 这个规则不存在
    
    // 🎯 属性排序（提高可读性）
    'order/properties-order': [
      // 布局相关
      'display',
      'position',
      'top', 'right', 'bottom', 'left',
      'z-index',
      
      // 盒模型
      'width', 'height',
      'max-width', 'max-height',
      'min-width', 'min-height',
      'margin', 'padding',
      'border', 'border-radius',
      'box-sizing',
      'overflow',
      
      // 文字
      'font-family', 'font-size', 'font-weight',
      'line-height', 'text-align', 'color',
      
      // 背景和装饰
      'background', 'box-shadow',
      'opacity', 'transform', 'transition'
    ],
    
    // 🎯 Vue特定规则
    'selector-pseudo-element-no-unknown': [
      true,
      {
        ignorePseudoElements: ['v-deep', 'deep']
      }
    ]
  },
  
  // 🎯 忽略第三方库文件
  ignoreFiles: [
    'node_modules/**/*',
    'dist/**/*',
    '**/*.min.css'
  ],
  
  // 🎯 自定义消息
  customSyntax: 'postcss-html',
  
  overrides: [
    {
      files: ['**/*.vue'],
      customSyntax: 'postcss-html'
    },
    {
      files: ['**/*.scss'],
      customSyntax: 'postcss-scss'
    }
  ]
};
