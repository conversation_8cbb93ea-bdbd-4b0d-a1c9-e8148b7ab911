#!/usr/bin/env node

/**
 * 自动修复console.log保护问题
 * 将所有未保护的console.log包装在开发环境检查中
 */

const fs = require('fs');
const path = require('path');

console.log('🔧 开始修复console.log保护问题...\n');

const serviceManagementPath = path.join(__dirname, 'src/views/ServiceManagement.vue');

if (!fs.existsSync(serviceManagementPath)) {
  console.error('❌ 服务管理页面文件不存在');
  process.exit(1);
}

// 读取文件内容
let content = fs.readFileSync(serviceManagementPath, 'utf8');
const originalContent = content;

console.log('📋 分析console.log使用情况...');

// 统计console.log
const consoleLogLines = content.split('\n').filter(line => line.includes('console.log'));
console.log(`📊 发现 ${consoleLogLines.length} 个console.log`);

// 检查已经被保护的console.log
const protectedConsoleLog = consoleLogLines.filter(line => {
  const lineIndex = content.split('\n').indexOf(line);
  const beforeLines = content.split('\n').slice(Math.max(0, lineIndex - 5), lineIndex);
  const afterLines = content.split('\n').slice(lineIndex + 1, lineIndex + 3);
  
  // 检查前后几行是否有开发环境检查
  const contextLines = [...beforeLines, ...afterLines].join('\n');
  return contextLines.includes('development') || contextLines.includes('NODE_ENV');
});

console.log(`✅ 已保护的console.log: ${protectedConsoleLog.length} 个`);
console.log(`❌ 未保护的console.log: ${consoleLogLines.length - protectedConsoleLog.length} 个`);

// 修复策略：在script标签开始处添加开发环境检查
const scriptStartPattern = /<script setup>/;
const scriptMatch = content.match(scriptStartPattern);

if (scriptMatch) {
  const scriptStartIndex = content.indexOf(scriptMatch[0]) + scriptMatch[0].length;
  
  // 在script开始处添加开发环境检查和console包装
  const devConsoleWrapper = `
// 🔒 开发环境console.log保护 - 生产环境自动禁用
const devLog = (...args) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(...args);
  }
};

const devError = (...args) => {
  if (process.env.NODE_ENV === 'development') {
    console.error(...args);
  }
};

const devWarn = (...args) => {
  if (process.env.NODE_ENV === 'development') {
    console.warn(...args);
  }
};
`;

  // 插入保护代码
  content = content.slice(0, scriptStartIndex) + devConsoleWrapper + content.slice(scriptStartIndex);
  
  // 替换所有console.log为devLog
  content = content.replace(/console\.log\(/g, 'devLog(');
  content = content.replace(/console\.error\(/g, 'devError(');
  content = content.replace(/console\.warn\(/g, 'devWarn(');
  
  console.log('🔧 已添加开发环境console保护包装器');
  console.log('🔄 已将所有console.log替换为devLog');
  
} else {
  console.error('❌ 未找到<script setup>标签');
  process.exit(1);
}

// 写入修复后的内容
fs.writeFileSync(serviceManagementPath, content);

console.log('\n✅ console.log保护修复完成！');

// 验证修复效果
const newContent = fs.readFileSync(serviceManagementPath, 'utf8');
const newConsoleLogCount = (newContent.match(/console\.log\(/g) || []).length;
const devLogCount = (newContent.match(/devLog\(/g) || []).length;

console.log('📊 修复效果统计:');
console.log(`  修复前console.log: ${consoleLogLines.length} 个`);
console.log(`  修复后console.log: ${newConsoleLogCount} 个`);
console.log(`  新增devLog: ${devLogCount} 个`);

if (newConsoleLogCount === 0) {
  console.log('🎉 所有console.log已成功保护！');
} else {
  console.log(`⚠️  仍有 ${newConsoleLogCount} 个未保护的console.log`);
}

console.log('\n🎯 修复说明:');
console.log('1. 添加了开发环境检查包装器');
console.log('2. 所有console.log替换为devLog');
console.log('3. 生产环境下devLog不会输出任何内容');
console.log('4. 开发环境下devLog功能与console.log完全相同');

console.log('\n📋 使用方法:');
console.log('- 开发环境: devLog正常输出到控制台');
console.log('- 生产环境: devLog不输出，零性能影响');
console.log('- 新代码: 使用devLog代替console.log');
