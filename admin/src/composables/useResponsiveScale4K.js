/**
 * 🎯 4K基准响应式缩放Vue组合式API
 * 为Vue组件提供4K基准的响应式缩放功能
 * 
 * <AUTHOR> Agent
 * @date 2025-01-21
 * @version 1.0.0
 */

import { ref, reactive, onMounted, onUnmounted, computed, watch, readonly } from 'vue';
import { 
  calculateScale4K, 
  applyScale4K, 
  getCurrentResolutionInfo, 
  validateScale4K,
  getScaleCSS,
  SCALE_CONFIG 
} from '@/utils/responsiveScale4K.js';

/**
 * 🎯 4K基准响应式缩放组合式API
 * @param {Object} options 配置选项
 * @returns {Object} 响应式缩放相关的状态和方法
 */
export function useResponsiveScale4K(options = {}) {
  // 🔧 配置选项
  const config = {
    autoInit: true,           // 是否自动初始化
    enableValidation: true,   // 是否启用验证
    debugMode: false,         // 是否启用调试模式
    ...options
  };

  // 📊 响应式状态
  const scale = ref(1);
  const resolution = reactive({
    width: 0,
    height: 0,
    type: 'CUSTOM',
    name: '自定义分辨率',
    scale: 1,
    scalePercentage: '100%',
    isBase: false,
    devicePixelRatio: 1
  });

  const validation = reactive({
    valid: false,
    scaleInRange: false,
    transformApplied: false,
    cssVariablesSet: false,
    containerScaled: false
  });

  const isInitialized = ref(false);
  const isLoading = ref(false);

  // 🔄 清理函数引用
  let cleanupFunction = null;

  // 💡 计算属性
  const scalePercentage = computed(() => `${(scale.value * 100).toFixed(1)}%`);
  
  const isSmallScreen = computed(() => resolution.type === 'SMALL');
  const isMediumScreen = computed(() => resolution.type === 'MEDIUM');
  const isLargeScreen = computed(() => resolution.type === 'LARGE');
  const isXLScreen = computed(() => resolution.type === 'XL');
  const is4KScreen = computed(() => resolution.type === 'XXL');
  
  const scaleLevel = computed(() => {
    if (scale.value < 0.7) return 'very-small';
    if (scale.value < 0.9) return 'small';
    if (scale.value < 1.1) return 'normal';
    if (scale.value < 1.3) return 'large';
    return 'very-large';
  });

  // 🎨 CSS缩放函数
  const scaleCSS = getScaleCSS();

  /**
   * 🔄 更新缩放状态
   */
  const updateScale = () => {
    try {
      isLoading.value = true;
      
      // 计算新的缩放比例
      const newScale = calculateScale4K();
      scale.value = newScale;
      
      // 获取分辨率信息
      const resolutionInfo = getCurrentResolutionInfo();
      Object.assign(resolution, resolutionInfo);
      
      // 验证缩放效果
      if (config.enableValidation) {
        const validationResult = validateScale4K();
        Object.assign(validation, validationResult.validation);
        validation.valid = validationResult.valid;
      }
      
      if (config.debugMode) {
        console.log('🔄 缩放状态已更新:', {
          scale: scale.value,
          resolution: resolution,
          validation: validation
        });
      }
      
    } catch (error) {
      console.error('❌ 更新缩放状态失败:', error);
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 🚀 初始化4K基准缩放系统
   */
  const initScale = async () => {
    try {
      isLoading.value = true;
      
      // 应用缩放
      const appliedScale = applyScale4K();
      
      // 更新状态
      updateScale();
      
      // 监听窗口大小变化
      const handleResize = () => {
        updateScale();
      };
      
      // 监听自定义缩放变化事件
      const handleScaleChange = (event) => {
        const { scale: newScale, resolution: newResolution } = event.detail;
        scale.value = newScale;
        Object.assign(resolution, newResolution);
        
        if (config.debugMode) {
          console.log('📡 接收到缩放变化事件:', event.detail);
        }
      };
      
      // 防抖处理
      let resizeTimer = null;
      const debouncedResize = () => {
        if (resizeTimer) clearTimeout(resizeTimer);
        resizeTimer = setTimeout(handleResize, 150);
      };
      
      // 添加事件监听
      window.addEventListener('resize', debouncedResize);
      window.addEventListener('scale4k-changed', handleScaleChange);
      
      // 保存清理函数
      cleanupFunction = () => {
        window.removeEventListener('resize', debouncedResize);
        window.removeEventListener('scale4k-changed', handleScaleChange);
        if (resizeTimer) clearTimeout(resizeTimer);
      };
      
      isInitialized.value = true;
      
      if (config.debugMode) {
        console.log('🚀 4K基准缩放系统初始化完成');
      }
      
    } catch (error) {
      console.error('❌ 初始化4K基准缩放系统失败:', error);
    } finally {
      isLoading.value = false;
    }
  };

  /**
   * 🧪 手动验证缩放效果
   */
  const validateScale = () => {
    const result = validateScale4K();
    Object.assign(validation, result.validation);
    validation.valid = result.valid;
    
    if (config.debugMode) {
      console.log('🧪 手动验证结果:', result);
    }
    
    return result;
  };

  /**
   * 🎯 获取特定值的缩放CSS
   */
  const getScaledValue = (value, unit = 'px') => {
    return `calc(${value}${unit} * var(--scale-factor-4k, 1))`;
  };

  /**
   * 📱 检查是否为特定分辨率类型
   */
  const isResolutionType = (type) => {
    return resolution.type === type;
  };

  /**
   * 🎨 获取当前缩放级别的CSS类名
   */
  const getScaleClassName = () => {
    return `scale-${scaleLevel.value}`;
  };

  /**
   * 📊 获取缩放统计信息
   */
  const getScaleStats = () => {
    return {
      current: {
        scale: scale.value,
        percentage: scalePercentage.value,
        level: scaleLevel.value
      },
      resolution: { ...resolution },
      validation: { ...validation },
      config: SCALE_CONFIG,
      isInitialized: isInitialized.value,
      isLoading: isLoading.value
    };
  };

  // 🔄 监听配置变化
  watch(() => config.debugMode, (newValue) => {
    if (newValue) {
      console.log('🐛 调试模式已启用');
      console.log('📊 当前缩放状态:', getScaleStats());
    }
  });

  // 🚀 生命周期钩子
  onMounted(() => {
    if (config.autoInit) {
      initScale();
    }
  });

  onUnmounted(() => {
    if (cleanupFunction) {
      cleanupFunction();
    }
  });

  // 🎯 返回API
  return {
    // 响应式状态
    scale: readonly(scale),
    resolution: readonly(resolution),
    validation: readonly(validation),
    isInitialized: readonly(isInitialized),
    isLoading: readonly(isLoading),
    
    // 计算属性
    scalePercentage,
    isSmallScreen,
    isMediumScreen,
    isLargeScreen,
    isXLScreen,
    is4KScreen,
    scaleLevel,
    
    // 方法
    initScale,
    updateScale,
    validateScale,
    getScaledValue,
    isResolutionType,
    getScaleClassName,
    getScaleStats,
    
    // CSS工具
    scaleCSS,
    
    // 配置
    config: readonly(config)
  };
}

/**
 * 🌐 全局4K缩放状态管理
 * 单例模式，确保整个应用使用同一个缩放状态
 */
let globalScale4KInstance = null;

export function useGlobalScale4K(options = {}) {
  if (!globalScale4KInstance) {
    globalScale4KInstance = useResponsiveScale4K({
      autoInit: true,
      enableValidation: true,
      debugMode: process.env.NODE_ENV === 'development',
      ...options
    });
  }
  
  return globalScale4KInstance;
}

// 🎯 导出类型定义（用于TypeScript支持）
export const RESOLUTION_TYPES = {
  SMALL: 'SMALL',
  MEDIUM: 'MEDIUM', 
  LARGE: 'LARGE',
  XL: 'XL',
  XXL: 'XXL',
  CUSTOM: 'CUSTOM'
};

export const SCALE_LEVELS = {
  VERY_SMALL: 'very-small',
  SMALL: 'small',
  NORMAL: 'normal',
  LARGE: 'large',
  VERY_LARGE: 'very-large'
};
