/* 
 * 壹心堂CSS标准库 v1.0
 * 基于历史成功案例和轮廓调试经验制定
 * 创建日期：2025-01-21
 * 目的：预防布局问题，统一设计标准
 */

:root {
  /* 🎯 壹心堂核心色彩系统 */
  --primary-color: #8B5CF6;        /* 主紫色 */
  --secondary-color: #A78BFA;      /* 浅紫色 */
  --accent-color: #7C3AED;         /* 深紫色 */
  --hover-color: rgba(139, 92, 246, 0.1); /* 悬停背景 */
  --header-bg-color: rgba(139, 92, 246, 0.15); /* 表头背景色 - 浅紫色半透明 */
  
  /* 🎯 标准尺寸系统（基于轮廓调试成功案例） */
  --row-height: 50px;              /* 数据行标准高度 - 与菜单项对齐 */
  --cell-padding: 0 8px;           /* 单元格标准内边距 - 避免超出边界 */
  --cell-font-size: 0.9rem;        /* 表格文字标准大小 - 适应50px高度 */
  --cell-line-height: 1.2;         /* 紧凑行高 - 防止内容撑开 */
  
  /* 🎯 服务图片标准尺寸（历史优化结果） */
  --service-image-width: 55px;     /* 服务图片宽度 */
  --service-image-height: 20px;    /* 服务图片高度 - 适应50px行高 */
  
  /* 🎯 表格容器标准尺寸（轮廓调试验证） */
  --table-body-height: 680px;      /* 绿色区域标准高度 - 包含红色区域 */
  --table-container-padding: 58px; /* 蓝色区域底部内边距 - 对齐绿色区域 */
  
  /* 🎯 响应式断点系统 */
  --breakpoint-tablet: 1024px;     /* 平板断点 */
  --breakpoint-desktop: 1366px;    /* 桌面断点 */
  --breakpoint-large: 1920px;      /* 大屏断点 */
  
  /* 🎯 间距系统（基于黄金比例） */
  --spacing-xs: 4px;               /* 极小间距 */
  --spacing-sm: 8px;               /* 小间距 */
  --spacing-md: 16px;              /* 中等间距 */
  --spacing-lg: 24px;              /* 大间距 */
  --spacing-xl: 32px;              /* 极大间距 */

  /* 🎯 响应式缩放系统 - 基于1512x768基准分辨率 */
  --base-width: 1512px;            /* 基准宽度 */
  --base-height: 768px;            /* 基准高度 */
  --scale-factor: min(100vw / 1512px, 100vh / 768px); /* 等比缩放因子 */
  
  /* 🎯 边框和阴影系统 */
  --border-radius: 8px;            /* 标准圆角 */
  --border-color: var(--primary-color); /* 边框颜色 */
  --shadow-light: 0 2px 4px rgba(139, 92, 246, 0.1); /* 轻阴影 */
  --shadow-medium: 0 4px 8px rgba(139, 92, 246, 0.15); /* 中阴影 */
  --shadow-heavy: 0 8px 16px rgba(139, 92, 246, 0.2); /* 重阴影 */
  
  /* 🎯 动画系统 */
  --transition-fast: 0.15s ease;   /* 快速过渡 */
  --transition-normal: 0.3s ease;  /* 正常过渡 */
  --transition-slow: 0.5s ease;    /* 慢速过渡 */
}

/* 🎯 数据行标准样式（基于历史成功案例） */
.standard-data-row {
  height: var(--row-height);
  max-height: var(--row-height);
  min-height: var(--row-height);
  overflow: hidden; /* 防止内容超出 */
  display: flex;
  align-items: center;
}

/* 🎯 数据单元格标准样式（防止边界超出） */
.standard-data-cell {
  padding: var(--cell-padding);
  font-size: var(--cell-font-size);
  line-height: var(--cell-line-height);
  max-height: var(--row-height);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 🎯 服务图片标准样式（历史优化结果） */
.standard-service-image {
  width: var(--service-image-width);
  height: var(--service-image-height);
  object-fit: cover;
  border-radius: 4px;
}

/* 🎯 表格容器标准样式（轮廓调试验证） */
.standard-table-body {
  height: var(--table-body-height);
  max-height: var(--table-body-height);
  min-height: var(--table-body-height);
  overflow-y: auto;
  overflow-x: hidden;
}

/* 🎯 响应式媒体查询标准 */
@media (min-width: 1366px) and (max-width: 1919px) {
  :root {
    --table-body-height: 680px; /* 1366-1919px分辨率标准高度 */
  }
}

@media (min-width: 1024px) and (max-width: 1365px) {
  :root {
    --table-body-height: 520px; /* 1024-1365px分辨率标准高度 */
  }
}

/* 🎯 轮廓调试辅助样式（开发环境） */
.debug-outline-blue { outline: 3px solid blue !important; }
.debug-outline-green { outline: 3px solid green !important; }
.debug-outline-red { outline: 3px solid red !important; }
.debug-outline-orange { outline: 3px solid orange !important; }
.debug-outline-purple { outline: 3px solid purple !important; }
.debug-outline-yellow { outline: 3px solid yellow !important; }

/* 🎯 预防常见问题的工具类 */
.prevent-overflow {
  overflow: hidden;
  max-height: var(--row-height);
}

.force-height {
  height: var(--row-height) !important;
  max-height: var(--row-height) !important;
  min-height: var(--row-height) !important;
}

.standard-transition {
  transition: var(--transition-normal);
}

.hover-effect:hover {
  background-color: var(--hover-color);
  transform: translateX(-2px);
  box-shadow: var(--shadow-medium);
}
