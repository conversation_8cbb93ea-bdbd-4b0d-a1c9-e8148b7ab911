/* 
 * 🎯 管理页面通用样式 - 基于服务管理页面标准
 * 适用于：技师管理、客户管理、预约管理、财务概览等所有管理页面
 * 确保所有页面的表头和数据行完美对齐，样式完全一致
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-28
 */

/* 🎯 CSS变量定义 - 基于服务管理页面标准 */
:root {
  /* 毛玻璃效果标准 */
  --glass-bg-primary: rgb(255 255 255 / 8%);
  --glass-bg-secondary: rgb(255 255 255 / 5%);
  --glass-bg-input: rgb(255 255 255 / 3%);
  --glass-border: rgb(255 255 255 / 15%);
  --glass-blur-standard: blur(25px) saturate(1.5);
  --glass-blur-heavy: blur(40px) saturate(1.8) brightness(1.2);

  /* 紫色主题色彩 */
  --primary-purple: rgba(139, 92, 246, 0.15);
  --secondary-purple: rgba(168, 85, 247, 0.12);
  --accent-purple: #4f46e5;

  /* Z-index层级系统 */
  --z-base: 1;
  --z-content: 10;
  --z-dropdown: 100;
  --z-toolbar: 200;
  --z-table-header: 300;
  --z-tooltip: 500;
  --z-modal-backdrop: 1000;
  --z-modal: 1001;
  --z-toast: 2000;
  --z-debug: 9999;

  /* 标准尺寸系统 */
  --row-height: 50px;
  --cell-padding: 0 8px;
  --cell-font-size: 0.9rem;
  --cell-line-height: 1.2;
}

/* 🎯 主容器样式 - 与服务管理页面完全一致 */
.picasso-services {
  display: flex;
  position: fixed;
  z-index: var(--z-base);
  width: calc(100vw - 180px);
  height: 100vh;
  min-height: 100vh;
  padding: 1.984vw;
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;
  font-size: clamp(12px, 1.2vw, 18px);
  background: transparent;
  inset: 0 0 0 180px;
  flex-direction: column;
}

/* 🎯 数据表格容器 */
.data-cubism {
  display: flex;
  position: relative;
  z-index: var(--z-base);
  padding: 0 20px;
  border-radius: 16px;
  overflow: hidden;
  background: transparent;
  flex: 1;
  flex-direction: column;
  margin-top: -10px;
}

/* 🎯 表格容器 */
.table-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: calc(100vh - 120px);
  max-height: calc(100vh - 120px);
  min-height: 400px;
  padding: 0 5px 4px;
  border-radius: 12px;
  overflow: hidden;
  background: transparent;
}

/* 🎯 智能表头 */
.smart-table-header {
  position: sticky;
  top: 0;
  z-index: var(--z-table-header);
  border-radius: 15px 15px 0 0;
  background: var(--primary-purple);
  box-shadow: 0 2px 8px rgba(139, 92, 246, 0.2);
  backdrop-filter: blur(10px);
  margin-bottom: 2px;
}

/* 🎯 表头列 */
.header-columns {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 8px;
}

.header-cell {
  display: flex;
  position: relative;
  padding: var(--cell-padding);
  align-items: center;
  justify-content: space-between;
}

/* 🎯 数据行基础样式 - 现代化单行布局 */
.data-row {
  display: flex;
  align-items: center;
  height: var(--row-height);
  padding: 12px 16px;
  margin-bottom: 0;
  border-bottom: 1px solid rgb(255 255 255 / 10%);
  transition: all 0.2s ease;
  line-height: var(--cell-line-height);
  min-width: 0;
  overflow: hidden;
}

.data-row:hover {
  background: var(--glass-bg-secondary);
  transform: translateY(-1px);
}

/* 🎯 现代化数据展示样式 - 遵循现代开发规范 */
.data-cell {
  display: flex;
  min-width: 0;
  padding: var(--cell-padding);
  overflow: hidden;
  font-size: var(--cell-font-size);
  align-items: center;
}

/* 🎯 通用信息样式 - 完美单行布局 */
.info-container {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  min-width: 0;
}

.info-icon {
  display: flex;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: var(--primary-purple);
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
}

.icon-placeholder {
  font-size: 1rem;
  font-weight: 600;
  color: var(--accent-purple);
}

.info-details {
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.primary-text {
  overflow: hidden;
  font-weight: 600;
  line-height: var(--cell-line-height);
  color: #111827;
  margin-bottom: 2px;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.secondary-text {
  overflow: hidden;
  font-size: 0.8rem;
  color: #374151;
  margin-bottom: 1px;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.tertiary-text {
  overflow: hidden;
  font-size: 0.8rem;
  font-weight: 500;
  color: #6366f1;
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 🎯 状态容器样式 */
.status-container {
  display: flex;
  width: 100%;
  min-width: 0;
  justify-content: center;
}

.status-badge {
  max-width: 100%;
  padding: 4px 8px;
  border-radius: 12px;
  overflow: hidden;
  font-size: 0.75rem;
  font-weight: 600;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.status-badge.active,
.status-badge.available,
.status-badge.completed {
  color: #047857;
  background: rgb(34 197 94 / 20%);
}

.status-badge.inactive,
.status-badge.unavailable,
.status-badge.cancelled {
  color: #b91c1c;
  background: rgb(239 68 68 / 20%);
}

.status-badge.pending,
.status-badge.busy {
  color: #d97706;
  background: rgb(245 158 11 / 20%);
}

.status-badge.vip,
.status-badge.confirmed {
  color: #7c3aed;
  background: rgb(139 92 246 / 20%);
}

/* 🎯 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  width: 100%;
  min-width: 0;
  justify-content: flex-start;
}

.action-btn-small {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  flex-shrink: 1;
}

.action-btn-small.primary {
  background: rgb(59 130 246 / 20%);
  color: #1d4ed8;
  font-weight: 500;
}

.action-btn-small.secondary {
  background: rgb(107 114 128 / 20%);
  color: #374151;
  font-weight: 500;
}

.action-btn-small.danger {
  background: rgb(239 68 68 / 20%);
  color: #b91c1c;
  font-weight: 500;
}

.action-btn-small.info {
  background: rgb(59 130 246 / 15%);
  color: #2563eb;
  font-weight: 500;
}

.action-btn-small:hover {
  transform: translateY(-1px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.btn-icon {
  flex-shrink: 0;
}

.btn-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
}

/* 🎯 翻页组件样式 */
.pagination-container {
  display: flex;
  position: fixed;
  right: 50px;
  bottom: 15px;
  left: auto;
  z-index: var(--z-modal);
  width: auto;
  height: 30px;
  margin: 0;
  padding: 6px 12px;
  border: 1px solid var(--primary-purple);
  border-radius: 8px;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
  backdrop-filter: blur(15px);
  justify-content: flex-end;
  align-items: center;
  flex-wrap: nowrap;
  gap: 15px;
}

/* 🎯 无数据提示样式 */
.no-data-container {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 300px;
  width: 100%;
}

.no-data-content {
  text-align: center;
  color: #6b7280;
}

.no-data-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.no-data-text {
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 8px;
}

.no-data-subtitle {
  font-size: 0.9rem;
  opacity: 0.8;
}

/* 🎯 响应式设计 */
@media (width <= 768px) {
  .picasso-services {
    padding: 16px;
    font-size: 14px;
  }

  .header-columns {
    flex-direction: column;
    gap: 12px;
  }

  .header-cell {
    width: 100%;
    justify-content: space-between;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .action-btn-small {
    width: 100%;
    justify-content: center;
  }
}
