import { createRouter, createWebHistory } from 'vue-router';

// 布局组件
const AdminLayout = () => import('@/components/Layout/AdminLayout.vue')

// 页面组件
const Dashboard = () => import('@/views/Dashboard.vue')
const Login = () => import('@/views/LoginView.vue')

// 创建路由实例
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/login',
      name: 'login',
      component: Login,
      meta: {
        title: '登录',
        requiresAuth: false
      },
    },
    {
      path: '/',
      component: AdminLayout,
      meta: { requiresAuth: true },
      children: [
        {
          path: '',
          redirect: '/dashboard'
        },
        {
          path: 'dashboard',
          name: 'dashboard',
          component: Dashboard,
          meta: { title: '仪表盘' }
        },
        {
          path: 'appointments',
          name: 'appointment-list',
          component: () => import('../views/AppointmentManagement.vue'),
          meta: { title: '预约管理' }
        },
        {
          path: 'customers',
          name: 'customers',
          component: () => import('../views/CustomerManagement.vue'),
          meta: { title: '客户管理' }
        },
        {
          path: 'therapists',
          name: 'therapists',
          component: () => import('../views/TherapistManagement.vue'),
          meta: { title: '技师管理' }
        },
        {
          path: 'services',
          name: 'services',
          component: () => import('../views/ServiceManagement.vue'),
          meta: { title: '服务管理' }
        },
        {
          path: 'system',
          name: 'system-management',
          component: () => import('../views/SystemManagement.vue'),
          meta: { title: '系统管理' }
        },
        {
          path: 'finance',
          name: 'finance-overview',
          component: () => import('../views/FinanceOverview.vue'),
          meta: { title: '财务概览' }
        },
        {
          path: 'finance/records',
          name: 'finance-records',
          component: () => import('../views/FinanceRecordsView.vue'),
          meta: { title: '收支记录' }
        },
        {
          path: 'finance/reports',
          name: 'finance-reports',
          component: () => import('../views/FinanceReportsView.vue'),
          meta: { title: '财务报表' }
        },
        {
          path: 'system/settings',
          name: 'system-settings',
          component: () => import('../views/SystemSettingsView.vue'),
          meta: { title: '系统设置' }
        },
        {
          path: 'system/logs',
          name: 'system-logs',
          component: () => import('../views/SystemLogsView.vue'),
          meta: { title: '操作日志' }
        },
        {
          path: 'health-tips',
          name: 'health-tips',
          component: () => import('../views/HealthTipsView.vue'),
          meta: { title: '健康小贴士' }
        }
      ]
    }
  ]
})

// 路由守卫
router.beforeEach(async (to, from, next) => {
  try {
    // 动态导入store以避免循环依赖
    const { useUserStore } = await import('@/store')
    const userStore = useUserStore()

    // 设置页面标题
    if (to.meta.title) {
      document.title = `${to.meta.title} - 壹心堂管理系统`
    }

    console.log('🔍 路由守卫检查:', {
      path: to.path,
      requiresAuth: to.meta.requiresAuth,
      isLoggedIn: userStore.isLoggedIn,
      token: userStore.token
    })

    // 检查认证
    if (to.meta.requiresAuth !== false && !userStore.isLoggedIn) {
      console.log('🔐 未登录，重定向到登录页')
      next('/login')
    } else if (to.path === '/login' && userStore.isLoggedIn) {
      console.log('✅ 已登录，重定向到仪表盘')
      next('/dashboard')
    } else {
      console.log('✅ 路由检查通过')
      next()
    }
  } catch (error) {
    console.error('❌ 路由守卫错误:', error)
    next('/login')
  }
})


// 全局路由错误处理
router.onError((error) => {
  console.error('🚨 路由错误:', error);
  console.error('错误堆栈:', error.stack);

  // 尝试恢复到仪表盘
  if (error.message.includes('Failed to resolve component')) {
    console.log('🔄 组件解析失败，重定向到仪表盘');
    router.replace('/dashboard');
  }
});

export default router
