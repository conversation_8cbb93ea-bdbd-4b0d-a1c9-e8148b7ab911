/* 怡心堂中医理疗管理系统 - 主样式文件 */

/* 导入主题样式 */
@use './variables.scss';
@use './purple-theme.scss';
@use './table-theme.scss';

/* 导入梵高艺术风格主题 */
@use './van-gogh-theme.scss';
@use './van-gogh-table.scss';

/* 全局重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  /* 使用100vh确保在所有分辨率下占满视口高度 */
  height: 100vh;
  min-height: 100vh;
  overflow: hidden; /* 完全移除页面外层滚动条 */
  font-family: var(--van-gogh-font-body), -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: var(--van-gogh-text-base);
  line-height: var(--van-gogh-leading-normal);
  color: var(--van-gogh-text-primary);

  /* 全局背景：使用侧边栏的梵高风格紫色渐变 */
  background: linear-gradient(180deg,
    #2d1b69 0%,    /* 深紫色（梵高星夜风格） */
    #3730a3 15%,   /* 靛蓝紫 */
    #4338ca 30%,   /* 中紫色 */
    #5b21b6 45%,   /* 深紫色 */
    #6b21a8 60%,   /* 紫色 */
    #7c2d92 75%,   /* 紫红色 */
    #86198f 90%,   /* 深紫红 */
    #701a75 100%   /* 最深紫色 */
  ) !important;
}

#app {
  /* 768px ÷ 16px = 48rem，使用px单位让pxtorem转换为rem */
  height: 768px;
}

/* 主题色彩 - 浅紫色主题 */
:root {
  --primary-color: #8b5cf6;
  --primary-color-hover: #a855f7;
  --primary-color-active: #7c3aed;
  --primary-color-light: #f3e8ff;
  --success-color: #52c41a;
  --warning-color: #faad14;
  --error-color: #f5222d;
  --info-color: #8b5cf6;
  --text-color: #333;
  --text-color-secondary: #666;
  --text-color-disabled: #999;
  --border-color: #d9d9d9;
  --border-color-light: #e8e8e8;
  --background-color: #fff;
  --background-color-light: #fafafa;
  --background-color-dark: #f5f5f5;
  --shadow-light: 0 2px 8px rgb(0 0 0 / 10%);
  --shadow-medium: 0 4px 12px rgb(0 0 0 / 15%);
  --border-radius: 6px;
  --border-radius-small: 4px;
  --border-radius-large: 8px;
}

/* 布局样式 */
.layout {
  display: flex;

  /* 使用100vh确保在所有分辨率下占满视口高度 */
  height: 100vh;
  min-height: 100vh;
  
  &__sidebar {
    width: 240px;
    background: #001529;
    transition: width 0.3s;
    
    &--collapsed {
      width: 80px;
    }
  }
  
  &__content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
  }
  
  &__header {
    display: flex;
    height: 32px;
    padding: 0 24px;
    background: var(--background-color);
    box-shadow: var(--shadow-light);
    border-bottom: 1px solid var(--border-color-light);
    align-items: center;
  }
  
  &__main {
    flex: 1;
    padding: 24px;
    overflow-y: auto;
    background: var(--background-color-dark);
  }
}

/* 页面容器 */
.page-container {
  border-radius: var(--border-radius);
  overflow: hidden;
  background: var(--background-color);
  box-shadow: var(--shadow-light);
  
  &__header {
    padding: 16px 24px;
    border-bottom: 1px solid var(--border-color-light);
    background: var(--background-color);
    
    .title {
      margin: 0;
      font-size: 18px;
      font-weight: 500;
      color: var(--text-color);
    }
    
    .description {
      color: var(--text-color-secondary);
      margin-top: 4px;
    }
  }
  
  &__content {
    padding: 24px;
  }
}

/* 卡片样式 */
.card {
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius);
  overflow: hidden;
  background: var(--background-color);
  box-shadow: var(--shadow-light);
  
  &__header {
    padding: 16px 20px;
    border-bottom: 1px solid var(--border-color-light);
    background: var(--background-color-light);
    
    .title {
      margin: 0;
      font-size: 16px;
      font-weight: 500;
      color: var(--text-color);
    }
  }
  
  &__body {
    padding: 20px;
  }
  
  &__footer {
    padding: 12px 20px;
    text-align: right;
    background: var(--background-color-light);
    border-top: 1px solid var(--border-color-light);
  }
}

/* 统计卡片 */
.stat-card {
  padding: 24px;
  border: 1px solid var(--border-color-light);
  border-radius: var(--border-radius);
  background: var(--background-color);
  box-shadow: var(--shadow-light);
  transition: all 0.3s;
  
  &:hover {
    box-shadow: var(--shadow-medium);
    transform: translateY(-2px);
  }
  
  &__icon {
    display: flex;
    width: 48px;
    height: 48px;
    border-radius: 50%;
    font-size: 24px;
    color: #fff;
    align-items: center;
    justify-content: center;
    margin-bottom: 16px;
    
    &--primary { background: var(--primary-color); }
    &--success { background: var(--success-color); }
    &--warning { background: var(--warning-color); }
    &--error { background: var(--error-color); }
  }
  
  &__value {
    font-size: 28px;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 4px;
  }
  
  &__label {
    font-size: 14px;
    color: var(--text-color-secondary);
  }
  
  &__trend {
    margin-top: 8px;
    font-size: 12px;
    
    &--up {
      color: var(--success-color);
      
      &::before {
        content: '↗ ';
      }
    }
    
    &--down {
      color: var(--error-color);
      
      &::before {
        content: '↘ ';
      }
    }
  }
}

/* 表格样式增强 */
.ant-table {
  .ant-table-thead > tr > th {
    font-weight: 500;
    background: var(--background-color-light);
  }
  
  .ant-table-tbody > tr:hover > td {
    background: #f5f5f5;
  }
}

/* 表单样式增强 - 按照菜单规格调整 */
.form-section {
  margin-bottom: 8px;                      /* 🎯 与菜单间距一致：8px */

  &__title {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-color);
    margin-bottom: 8px;                    /* 🎯 与菜单间距一致：8px */
    padding-bottom: 4px;                   /* 减少内边距 */
    border-bottom: 2px solid var(--primary-color);
  }
}

/* 全局表单控件高度调整 - 与菜单高度一致 */
.ant-input,
.ant-select-selector,
.ant-picker,
.ant-input-number,
.ant-textarea {
  height: 50px !important;                 /* 🎯 与菜单高度一致：50px */
  line-height: 50px !important;
}

.ant-textarea {
  height: auto !important;                 /* 文本域保持自适应高度 */
  min-height: 50px !important;             /* 最小高度与菜单一致 */
  line-height: 1.5 !important;             /* 文本域使用正常行高 */
}

/* 表单项间距调整 - 与菜单间距一致 */
.ant-form-item {
  margin-bottom: 8px !important;           /* 🎯 与菜单间距一致：8px */
}

/* 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 8px;
  
  &--right {
    justify-content: flex-end;
  }
  
  &--center {
    justify-content: center;
  }
}

/* 状态标签 */
.status-tag {
  &--active {
    color: var(--success-color);
    background: #f6ffed;
    border-color: #b7eb8f;
  }
  
  &--inactive {
    color: var(--text-color-secondary);
    background: #f5f5f5;
    border-color: #d9d9d9;
  }
  
  &--pending {
    color: var(--warning-color);
    background: #fffbe6;
    border-color: #ffe58f;
  }
  
  &--cancelled {
    color: var(--error-color);
    background: #fff2f0;
    border-color: #ffccc7;
  }
}

/* 响应式设计 */
@media (width <= 768px) {
  .layout {
    &__sidebar {
      position: fixed;
      left: -240px;
      z-index: 1000;
      height: 100vh;
      transition: left 0.3s;
      
      &--open {
        left: 0;
      }
    }
    
    &__content {
      margin-left: 0;
    }
    
    &__main {
      padding: 16px;
    }
  }
  
  .page-container {
    &__content {
      padding: 16px;
    }
  }
  
  .card {
    &__body {
      padding: 16px;
    }
  }
  
  .stat-card {
    padding: 16px;
    text-align: center;
  }
}

/* 工具类 */
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-left { text-align: left; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: 8px; }
.mb-2 { margin-bottom: 16px; }
.mb-3 { margin-bottom: 24px; }
.mb-4 { margin-bottom: 32px; }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: 8px; }
.mt-2 { margin-top: 16px; }
.mt-3 { margin-top: 24px; }
.mt-4 { margin-top: 32px; }

.p-0 { padding: 0; }
.p-1 { padding: 8px; }
.p-2 { padding: 16px; }
.p-3 { padding: 24px; }
.p-4 { padding: 32px; }

.d-flex { display: flex; }
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-none { display: none; }

.justify-center { justify-content: center; }
.justify-between { justify-content: space-between; }
.justify-end { justify-content: flex-end; }

.align-center { align-items: center; }
.align-start { align-items: flex-start; }
.align-end { align-items: flex-end; }

.flex-1 { flex: 1; }
.flex-wrap { flex-wrap: wrap; }

/* 全局滚动条样式 - 紫色主题 */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  border-radius: 4px;
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background: linear-gradient(135deg, var(--van-gogh-primary), var(--van-gogh-secondary));

  &:hover {
    background: linear-gradient(135deg, var(--van-gogh-primary-dark), var(--van-gogh-accent));
  }
}

/* 梵高风格全局组件样式 */

/* 卡片艺术设计 */
.ant-card {
  position: relative !important;
  border: 2px solid var(--van-gogh-border-medium) !important;
  border-radius: 16px !important;
  overflow: hidden !important;
  background: var(--van-gogh-bg-primary) !important;
  box-shadow:
    0 8px 32px var(--van-gogh-shadow-medium),
    0 4px 16px var(--van-gogh-shadow-light) !important;

  &::before {
    position: absolute;
    inset: 0;
    z-index: 0;
    background:
      radial-gradient(circle at 20% 80%, rgb(251 191 36 / 5%) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgb(30 58 138 / 5%) 0%, transparent 50%);
    content: '';
    pointer-events: none;
  }

  .ant-card-head {
    position: relative !important;
    z-index: 1 !important;
    border-radius: 14px 14px 0 0 !important;
    background: linear-gradient(135deg,
      var(--van-gogh-primary-dark) 0%,
      var(--van-gogh-primary) 50%,
      var(--van-gogh-secondary-dark) 100%) !important;
    border-bottom: 3px solid var(--van-gogh-border-dark) !important;

    .ant-card-head-title {
      font-family: var(--van-gogh-font-heading) !important;
      font-size: var(--van-gogh-text-xl) !important;
      font-weight: 600 !important;
      color: white !important;
      text-shadow: 0 2px 4px rgb(0 0 0 / 30%) !important;
    }
  }

  .ant-card-body {
    position: relative !important;
    z-index: 1 !important;
    background: transparent !important;
  }
}

/* 表单艺术设计 */
.ant-form {
  .ant-form-item-label > label {
    font-family: var(--van-gogh-font-ui) !important;
    font-size: var(--van-gogh-text-base) !important;
    font-weight: 600 !important;
    color: var(--van-gogh-text-primary) !important;
  }

  .ant-input,
  .ant-select-selector,
  .ant-picker {
    border: 2px solid var(--van-gogh-border-medium) !important;
    border-radius: 8px !important;
    font-family: var(--van-gogh-font-body) !important;
    font-size: var(--van-gogh-text-base) !important;
    color: var(--van-gogh-text-primary) !important;
    background: var(--van-gogh-bg-secondary) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

    &:hover {
      border-color: var(--van-gogh-secondary) !important;
      box-shadow: 0 2px 8px rgb(251 191 36 / 20%) !important;
    }

    &:focus,
    &.ant-input-focused,
    &.ant-select-focused .ant-select-selector,
    &.ant-picker-focused {
      border-color: var(--van-gogh-primary) !important;
      box-shadow:
        0 0 0 2px rgb(30 58 138 / 20%),
        0 4px 12px rgb(30 58 138 / 30%) !important;
    }
  }

  .ant-btn {
    position: relative !important;
    border-radius: 8px !important;
    overflow: hidden !important;
    font-family: var(--van-gogh-font-ui) !important;
    font-weight: 600 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

    &::before {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgb(255 255 255 / 30%);
      transform: translate(-50%, -50%);
      transition: width 0.3s ease, height 0.3s ease;
      content: '';
    }

    &:hover::before {
      width: 200%;
      height: 200%;
    }

    &.ant-btn-primary {
      border: none !important;
      color: white !important;
      background: linear-gradient(135deg,
        var(--van-gogh-primary) 0%,
        var(--van-gogh-secondary) 100%) !important;

      &:hover {
        box-shadow: 0 6px 20px rgb(30 58 138 / 40%) !important;
        transform: translateY(-2px) !important;
      }
    }

    &.ant-btn-default {
      border: 2px solid var(--van-gogh-border-medium) !important;
      color: var(--van-gogh-text-primary) !important;
      background: var(--van-gogh-bg-secondary) !important;

      &:hover {
        border-color: var(--van-gogh-secondary) !important;
        background: var(--van-gogh-secondary-light) !important;
        transform: translateY(-2px) !important;
      }
    }
  }
}
