/* 壹心堂浅紫色主题配置 */

/* 主要紫色色板 */
$purple-25: #fdfdff;
$purple-50: #faf5ff;
$purple-100: #f3e8ff;
$purple-200: #e9d5ff;
$purple-300: #d8b4fe;
$purple-400: #c084fc;
$purple-500: #a855f7;
$purple-600: #9333ea;
$purple-700: #7c3aed;
$purple-800: #6b21a8;
$purple-900: #581c87;

/* 主题色彩定义 */
$theme-primary: $purple-600;        // #9333ea - 主要紫色
$theme-primary-light: $purple-500;  // #a855f7 - 浅紫色
$theme-primary-dark: $purple-700;   // #7c3aed - 深紫色
$theme-primary-bg: $purple-50;      // #faf5ff - 背景紫色

/* 组件专用颜色 */
$button-primary-bg: $theme-primary;
$button-primary-hover: $theme-primary-light;
$button-primary-active: $theme-primary-dark;

$link-color: $theme-primary;
$link-hover-color: $theme-primary-light;

$table-header-bg: $purple-50;
$table-row-hover: #faf5ff;

$card-border: rgb(147 51 234 / 10%);
$card-shadow: 0 2px 12px rgb(147 51 234 / 8%);

$input-border-focus: $theme-primary;
$input-border-hover: $theme-primary-light;

/* 状态颜色（保持原有，只更新info） */
$status-success: #52c41a;
$status-warning: #faad14;
$status-error: #f5222d;
$status-info: $theme-primary;

/* 标签颜色 */
$tag-purple: $theme-primary;
$tag-purple-bg: $purple-100;

/* 进度条颜色 */
$progress-bg: $theme-primary;
$progress-success: $status-success;

/* 菜单颜色 */
$menu-selected-bg: linear-gradient(135deg, rgb(147 51 234 / 15%) 0%, rgb(168 85 247 / 12%) 50%, rgb(147 51 234 / 15%) 100%);
$menu-selected-color: $theme-primary-dark;
$menu-hover-bg: rgb(147 51 234 / 10%);

/* 表单颜色 */
$form-item-border: rgb(147 51 234 / 20%);
$form-item-focus: $theme-primary;

/* 模态框颜色 */
$modal-header-bg: $purple-50;
$modal-border: rgb(147 51 234 / 10%);

/* 面包屑颜色 */
$breadcrumb-link: $theme-primary;
$breadcrumb-separator: rgb(147 51 234 / 50%);

/* 分页颜色 */
$pagination-active: $theme-primary;
$pagination-hover: $theme-primary-light;

/* 通知颜色 */
$notification-info: $theme-primary;
$notification-bg: $purple-50;

/* 工具提示颜色 */
$tooltip-bg: $theme-primary-dark;
$tooltip-arrow: $theme-primary-dark;

/* 加载动画颜色 */
$loading-color: $theme-primary;
$loading-bg: rgb(147 51 234 / 10%);

/* 选择器颜色 */
$select-border-focus: $theme-primary;
$select-option-selected: $purple-100;
$select-option-hover: $purple-50;

/* 日期选择器颜色 */
$datepicker-selected: $theme-primary;
$datepicker-hover: $theme-primary-light;
$datepicker-today: $purple-100;

/* 开关颜色 */
$switch-checked: $theme-primary;
$switch-loading: $theme-primary-light;

/* 滑块颜色 */
$slider-track: $theme-primary;
$slider-handle: $theme-primary;
$slider-handle-hover: $theme-primary-light;

/* 评分颜色 */
$rate-star: $theme-primary;
$rate-star-half: $theme-primary-light;

/* 步骤条颜色 */
$steps-finish: $theme-primary;
$steps-active: $theme-primary-light;

/* 时间轴颜色 */
$timeline-color: $theme-primary;
$timeline-dot: $theme-primary;

/* 树形控件颜色 */
$tree-selected: $purple-100;
$tree-hover: $purple-50;

/* 穿梭框颜色 */
$transfer-selected: $purple-100;
$transfer-hover: $purple-50;

/* 上传组件颜色 */
$upload-border: rgb(147 51 234 / 30%);
$upload-hover: $purple-50;

/* 抽屉颜色 */
$drawer-header: $purple-50;
$drawer-border: rgb(147 51 234 / 10%);

/* 锚点颜色 */
$anchor-link: $theme-primary;
$anchor-active: $theme-primary-dark;

/* 回到顶部颜色 */
$backtop-bg: $theme-primary;
$backtop-hover: $theme-primary-light;

/* 图标颜色 */
$icon-primary: $theme-primary;
$icon-hover: $theme-primary-light;

/* 徽章颜色 */
$badge-color: $theme-primary;
$badge-bg: $purple-100;

/* 日历颜色 */
$calendar-selected: $theme-primary;
$calendar-today: $purple-100;

/* 卡片颜色 */
$card-head-bg: $purple-50;
$card-actions-border: rgb(147 51 234 / 10%);

/* 折叠面板颜色 */
$collapse-header-bg: $purple-50;
$collapse-border: rgb(147 51 234 / 10%);

/* 描述列表颜色 */
$descriptions-border: rgb(147 51 234 / 10%);
$descriptions-header: $purple-50;

/* 空状态颜色 */
$empty-color: rgb(147 51 234 / 50%);

/* 列表颜色 */
$list-border: rgb(147 51 234 / 10%);
$list-hover: $purple-50;

/* 统计数值颜色 */
$statistic-content: $theme-primary;
$statistic-title: rgb(147 51 234 / 70%);

/* 结果页颜色 */
$result-icon: $theme-primary;
$result-title: $theme-primary-dark;

/* 骨架屏颜色 */
$skeleton-color: rgb(147 51 234 / 10%);
$skeleton-active: rgb(147 51 234 / 20%);

/* 旋转器颜色 */
$spin-color: $theme-primary;
$spin-bg: rgb(147 51 234 / 10%);

/* 全局覆盖类 */
.purple-theme {
  // 主要按钮
  .ant-btn-primary {
    background-color: $button-primary-bg;
    border-color: $button-primary-bg;
    
    &:hover {
      background-color: $button-primary-hover;
      border-color: $button-primary-hover;
    }
    
    &:active {
      background-color: $button-primary-active;
      border-color: $button-primary-active;
    }
  }
  
  // 链接
  .ant-typography a,
  .ant-btn-link {
    color: $link-color;
    
    &:hover {
      color: $link-hover-color;
    }
  }
  
  // 表格
  .ant-table-thead > tr > th {
    background-color: $table-header-bg;
  }
  
  // 标签
  .ant-tag-purple {
    background-color: $tag-purple-bg;
    border-color: $tag-purple;
    color: $tag-purple;
  }
  
  // 进度条
  .ant-progress-bg {
    background-color: $progress-bg;
  }
  
  // 输入框焦点
  .ant-input:focus,
  .ant-input-focused {
    border-color: $input-border-focus;
    box-shadow: 0 0 0 2px rgb(147 51 234 / 20%);
  }
  
  // 选择器焦点
  .ant-select:not(.ant-select-disabled):hover .ant-select-selector,
  .ant-select-focused .ant-select-selector {
    border-color: $select-border-focus;
  }
}
