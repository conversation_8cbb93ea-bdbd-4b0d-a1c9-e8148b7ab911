/**
 * 梵高艺术风格主题系统
 * 灵感来源：《星夜》、《向日葵》、《麦田》
 */

// 浅紫色梵高风格调色板
:root {
  // 主色调 - 梦幻紫
  --van-gogh-primary: #8b5cf6;
  --van-gogh-primary-light: #a855f7;
  --van-gogh-primary-dark: #7c3aed;

  // 辅助色 - 薰衣草紫
  --van-gogh-secondary: #c084fc;
  --van-gogh-secondary-light: #d8b4fe;
  --van-gogh-secondary-dark: #a855f7;

  // 强调色 - 玫瑰金
  --van-gogh-accent: #f472b6;
  --van-gogh-accent-light: #f9a8d4;
  --van-gogh-accent-dark: #ec4899;
  
  // 背景色 - 紫色画布
  --van-gogh-bg-primary: #faf7ff;
  --van-gogh-bg-secondary: #f3f0ff;
  --van-gogh-bg-tertiary: #ede9fe;
  
  // 文字色 - 炭笔黑
  --van-gogh-text-primary: #1f2937;
  --van-gogh-text-secondary: #4b5563;
  --van-gogh-text-tertiary: #6b7280;
  
  // 边框色 - 紫色画框
  --van-gogh-border-light: #e9d5ff;
  --van-gogh-border-medium: #d8b4fe;
  --van-gogh-border-dark: #c084fc;

  // 阴影色 - 梦幻紫
  --van-gogh-shadow-light: rgb(139 92 246 / 10%);
  --van-gogh-shadow-medium: rgb(139 92 246 / 15%);
  --van-gogh-shadow-dark: rgb(139 92 246 / 25%);
  
  // 成功色 - 生命绿
  --van-gogh-success: #059669;
  --van-gogh-success-light: #10b981;
  --van-gogh-success-bg: #ecfdf5;
  
  // 警告色 - 夕阳橙
  --van-gogh-warning: #d97706;
  --van-gogh-warning-light: #f59e0b;
  --van-gogh-warning-bg: #fffbeb;
  
  // 错误色 - 激情红
  --van-gogh-error: #dc2626;
  --van-gogh-error-light: #ef4444;
  --van-gogh-error-bg: #fef2f2;
  
  // 信息色 - 天空蓝
  --van-gogh-info: #0284c7;
  --van-gogh-info-light: #0ea5e9;
  --van-gogh-info-bg: #f0f9ff;
}

// 梵高风格字体系统
@import url('https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=Playfair+Display:wght@400;500;600;700&display=swap');

:root {
  // 标题字体 - 优雅衬线
  --van-gogh-font-heading: 'Playfair Display', 'Times New Roman', serif;
  
  // 正文字体 - 经典衬线
  --van-gogh-font-body: 'Crimson Text', 'Georgia', serif;
  
  // 界面字体 - 现代无衬线
  --van-gogh-font-ui: -apple-system, blinkmacsystemfont, 'Segoe UI', roboto, sans-serif;
  
  // 字体大小
  --van-gogh-text-xs: 0.75rem;
  --van-gogh-text-sm: 0.875rem;
  --van-gogh-text-base: 1rem;
  --van-gogh-text-lg: 1.125rem;
  --van-gogh-text-xl: 1.25rem;
  --van-gogh-text-2xl: 1.5rem;
  --van-gogh-text-3xl: 1.875rem;
  --van-gogh-text-4xl: 2.25rem;
  
  // 行高
  --van-gogh-leading-tight: 1.25;
  --van-gogh-leading-normal: 1.5;
  --van-gogh-leading-relaxed: 1.75;
}

// 梵高风格动画系统
@keyframes van-gogh-brush-stroke {
  0% {
    opacity: 0;
    transform: translateX(-100%) rotate(-2deg);
  }

  50% {
    opacity: 0.8;
    transform: translateX(0%) rotate(1deg);
  }

  100% {
    opacity: 1;
    transform: translateX(0%) rotate(0deg);
  }
}

@keyframes van-gogh-paint-drop {
  0% {
    opacity: 0;
    transform: translateY(-20px) scale(0.8);
  }

  50% {
    opacity: 0.9;
    transform: translateY(0) scale(1.05);
  }

  100% {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes van-gogh-swirl {
  0% {
    opacity: 0.7;
    transform: rotate(0deg) scale(0.9);
  }

  50% {
    opacity: 1;
    transform: rotate(180deg) scale(1.1);
  }

  100% {
    opacity: 1;
    transform: rotate(360deg) scale(1);
  }
}

@keyframes van-gogh-shimmer {
  0% {
    background-position: -200% 0;
  }

  100% {
    background-position: 200% 0;
  }
}

// 梵高风格阴影系统
.van-gogh-shadow {
  &-soft {
    box-shadow: 
      0 2px 8px var(--van-gogh-shadow-light),
      0 1px 3px var(--van-gogh-shadow-medium);
  }
  
  &-medium {
    box-shadow: 
      0 4px 16px var(--van-gogh-shadow-medium),
      0 2px 6px var(--van-gogh-shadow-dark);
  }
  
  &-strong {
    box-shadow: 
      0 8px 32px var(--van-gogh-shadow-dark),
      0 4px 12px var(--van-gogh-shadow-medium);
  }
  
  &-artistic {
    box-shadow: 
      0 0 20px var(--van-gogh-secondary-light),
      0 0 40px var(--van-gogh-primary-light),
      inset 0 1px 0 rgb(255 255 255 / 20%);
  }
}

// 浅紫色梵高风格渐变系统
.van-gogh-gradient {
  &-starry-night {
    background: linear-gradient(135deg,
      var(--van-gogh-primary-dark) 0%,
      var(--van-gogh-primary) 50%,
      var(--van-gogh-secondary-dark) 100%);
  }

  &-sunflower {
    background: linear-gradient(45deg,
      var(--van-gogh-secondary-light) 0%,
      var(--van-gogh-secondary) 50%,
      var(--van-gogh-accent) 100%);
  }
  
  &-wheat-field {
    background: linear-gradient(180deg,
      var(--van-gogh-bg-primary) 0%,
      var(--van-gogh-bg-secondary) 50%,
      var(--van-gogh-accent-light) 100%);
  }
  
  &-canvas {
    background: linear-gradient(45deg,
      var(--van-gogh-bg-primary) 0%,
      var(--van-gogh-bg-secondary) 25%,
      var(--van-gogh-bg-tertiary) 50%,
      var(--van-gogh-bg-secondary) 75%,
      var(--van-gogh-bg-primary) 100%);
  }
}

// 梵高风格纹理效果
.van-gogh-texture {
  &-canvas {
    background-image: 
      radial-gradient(circle at 1px 1px, rgb(255 255 255 / 15%) 1px, transparent 0);
    background-size: 20px 20px;
  }
  
  &-brush-stroke {
    background-image: 
      linear-gradient(45deg, transparent 40%, rgb(255 255 255 / 10%) 50%, transparent 60%);
    background-size: 30px 30px;
  }
  
  &-paint-texture {
    background-image: 
      repeating-linear-gradient(
        45deg,
        transparent,
        transparent 2px,
        rgb(255 255 255 / 5%) 2px,
        rgb(255 255 255 / 5%) 4px
      );
  }
}

// 梵高风格边框系统
.van-gogh-border {
  &-artistic {
    border: 3px solid var(--van-gogh-border-medium);
    border-image: linear-gradient(45deg, 
      var(--van-gogh-secondary), 
      var(--van-gogh-accent), 
      var(--van-gogh-primary)) 1;
    border-radius: 8px;
  }
  
  &-frame {
    border: 2px solid var(--van-gogh-border-dark);
    border-radius: 12px;
    box-shadow: 
      inset 0 0 0 1px var(--van-gogh-border-light),
      0 2px 8px var(--van-gogh-shadow-medium);
  }
  
  &-canvas {
    border: 1px solid var(--van-gogh-border-light);
    border-radius: 6px;
    background: var(--van-gogh-bg-primary);
  }
}

// 响应式断点
@media (width <= 768px) {
  :root {
    --van-gogh-text-base: 0.9rem;
    --van-gogh-text-lg: 1rem;
    --van-gogh-text-xl: 1.125rem;
  }
}

@media (width <= 480px) {
  :root {
    --van-gogh-text-base: 0.85rem;
    --van-gogh-text-lg: 0.95rem;
    --van-gogh-text-xl: 1rem;
  }
}
