/**
 * 统一表格主题样式
 * 确保所有页面的表格样式完全一致
 *
 * 透明度设计理念：
 * - 表格整体透明，可以看到背景图案
 * - 表头：80% 透明度，保持可读性
 * - 普通行：30-40% 透明度，轻盈感
 * - 悬停行：60% 透明度，交互反馈
 * - 选中行：70% 透明度，状态明确
 * - 添加背景模糊效果增强视觉层次
 */

/* 表头统一样式 - 浅紫色主题 */
.ant-table-thead > tr > th {
  padding: 12px 16px !important;
  font-size: 14px !important;
  font-weight: 600 !important;
  color: #4c1d95 !important;
  background: rgb(248 244 255 / 80%) !important; /* 80% 透明度表头 */
  border-bottom: 1px solid rgb(230 215 255 / 80%) !important;
  
  /* 表头文字样式 */
  &::before {
    display: none !important;
  }
  
  /* 排序图标颜色 */
  .ant-table-column-sorter {
    color: #8b5cf6 !important;
  }
  
  /* 筛选图标颜色 */
  .ant-table-filter-trigger {
    color: #8b5cf6 !important;
  }
}

/* 表格行样式 */
.ant-table-tbody > tr {
  /* 隐藏测量行和空行 */
  &[aria-hidden="true"] {
    display: none !important;
  }

  /* 正常行样式 */
  &:not([aria-hidden="true"]) {
    /* 默认背景色 - 透明 */
    > td {
      background: rgb(255 255 255 / 30%) !important; /* 30% 透明度 */
    }

    /* 奇偶行颜色 - 轻微透明度差异 */
    &:nth-child(even) > td {
      background: rgb(253 253 255 / 40%) !important; /* 40% 透明度 */
    }

    &:nth-child(odd) > td {
      background: rgb(255 255 255 / 30%) !important; /* 30% 透明度 */
    }

    /* 悬停效果 - 只在鼠标悬停时生效 */
    &:hover:not(.ant-table-row-selected) > td {
      background: rgb(250 245 255 / 60%) !important; /* 60% 透明度悬停 */
    }

    /* 选中行样式 - 避免过度突出 */
    &.ant-table-row-selected > td {
      background: rgb(243 240 255 / 70%) !important; /* 70% 透明度选中 */
      border-color: rgb(230 215 255 / 80%) !important;
    }
  }
}

/* 表格单元格样式 */
.ant-table-tbody > tr > td {
  border-bottom: 1px solid rgb(240 240 240 / 60%) !important; /* 边框透明度 */
  padding: 12px 16px !important;
  color: #374151 !important;
  background: transparent !important; /* 单元格背景透明 */

  /* 固定列样式 */
  &.ant-table-cell-fix-right {
    background: rgb(255 255 255 / 80%) !important; /* 固定列稍微不透明 */
    border-left: 1px solid rgb(230 215 255 / 80%) !important;
  }
}

/* 隐藏所有测量行和空行 */
.ant-table-measure-row,
.ant-table-tbody > tr[aria-hidden="true"],
.ant-table-tbody > tr[style*="height: 0px"] {
  display: none !important;
  height: 0 !important;
  max-height: 0 !important;
  min-height: 0 !important;
  overflow: hidden !important;
  visibility: hidden !important;
}

/* 重置所有可能的行状态样式 */
.ant-table-tbody > tr {
  /* 清除默认选中状态 */
  &.ant-table-row-selected {
    background: transparent !important;

    > td {
      background: rgb(243 240 255 / 70%) !important; /* 选中状态透明度 */
    }
  }

  /* 清除点击状态 */
  &:active > td {
    background: inherit !important;
  }

  /* 清除焦点状态 */
  &:focus > td {
    background: inherit !important;
  }
}

/* 表格容器样式 */
.ant-table-container {
  border: 1px solid rgb(230 215 255 / 80%) !important; /* 边框也增加透明度 */
  border-radius: 8px !important;
  overflow: hidden !important;
  background: rgb(255 255 255 / 75%) !important; /* 容器背景透明度 */
}

/* 表格包装器样式 */
.ant-table-wrapper {
  .ant-table {
    border-radius: 8px !important;
    background: rgb(255 255 255 / 85%) !important; /* 85% 透明度，可以看到背景 */ /* 添加背景模糊效果 */
    backdrop-filter: blur(8px) !important; /* Safari 兼容性 */
  }

  /* 空状态样式 */
  .ant-empty {
    color: #8b5cf6 !important;
  }
}

/* 分页器样式 */
.ant-pagination {
  margin-top: 16px !important;
  text-align: right !important;
  
  .ant-pagination-item {
    border-color: #e6d7ff !important;
    
    &:hover {
      border-color: #8b5cf6 !important;
      color: #8b5cf6 !important;
    }
    
    &.ant-pagination-item-active {
      color: #fff !important;
      background: #8b5cf6 !important;
      border-color: #8b5cf6 !important;
    }
  }
  
  .ant-pagination-prev,
  .ant-pagination-next {
    border-color: #e6d7ff !important;
    color: #8b5cf6 !important;
    
    &:hover {
      border-color: #8b5cf6 !important;
      color: #8b5cf6 !important;
    }
  }
  
  .ant-pagination-jump-prev,
  .ant-pagination-jump-next {
    color: #8b5cf6 !important;
  }
}

/* 表格滚动条统一样式 */
.ant-table-body {
  /* 垂直滚动条 */
  &::-webkit-scrollbar {
    width: 8px !important;
    height: 8px !important;
  }

  &::-webkit-scrollbar-track {
    border-radius: 4px !important;
    background: #f1f1f1 !important;
  }

  &::-webkit-scrollbar-thumb {
    border-radius: 4px !important;
    background: #8b5cf6 !important;
    
    &:hover {
      background: #7c3aed !important;
    }
  }
  
  /* 水平滚动条 */
  &::-webkit-scrollbar:horizontal {
    height: 8px !important;
  }
  
  &::-webkit-scrollbar-thumb:horizontal {
    border-radius: 4px !important;
    background: #8b5cf6 !important;
    
    &:hover {
      background: #7c3aed !important;
    }
  }
}

/* 表格加载状态 */
.ant-spin-nested-loading {
  .ant-spin-container {
    .ant-table-placeholder {
      color: #8b5cf6 !important;
    }
  }
}

/* 表格选择框样式 */
.ant-table-selection-column {
  .ant-checkbox-wrapper {
    .ant-checkbox {
      .ant-checkbox-inner {
        border-color: #e6d7ff !important;
        
        &:hover {
          border-color: #8b5cf6 !important;
        }
      }
      
      &.ant-checkbox-checked {
        .ant-checkbox-inner {
          background: #8b5cf6 !important;
          border-color: #8b5cf6 !important;
        }
      }
    }
  }
}

/* 表格展开行样式 */
.ant-table-expanded-row {
  background: #faf5ff !important;
  
  .ant-table-expanded-row-fixed {
    background: #faf5ff !important;
  }
}

/* 表格筛选下拉框样式 */
.ant-table-filter-dropdown {
  border: 1px solid #e6d7ff !important;
  border-radius: 8px !important;
  background: #fff !important;
  box-shadow: 0 4px 12px rgb(139 92 246 / 15%) !important;
}

/* 表格排序样式 */
.ant-table-column-sorter-up.active,
.ant-table-column-sorter-down.active {
  color: #8b5cf6 !important;
}

/* 响应式表格样式 */
@media (width <= 768px) {
  .ant-table-thead > tr > th {
    padding: 8px 12px !important;
    font-size: 13px !important;
  }
  
  .ant-table-tbody > tr > td {
    padding: 8px 12px !important;
    font-size: 13px !important;
  }
}

/* 表格固定列阴影 */
.ant-table-ping-left {
  .ant-table-cell-fix-left-last::after {
    box-shadow: inset 10px 0 8px -8px rgb(139 92 246 / 15%) !important;
  }
}

.ant-table-ping-right {
  .ant-table-cell-fix-right-first::after {
    box-shadow: inset -10px 0 8px -8px rgb(139 92 246 / 15%) !important;
  }
}
