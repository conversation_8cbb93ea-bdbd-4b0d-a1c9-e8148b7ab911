/* 怡心堂中医理疗管理系统 - 样式变量 */

/* 主题色彩 - 浅紫色主题 */
$primary-color: #8b5cf6;
$primary-color-hover: #a855f7;
$primary-color-active: #7c3aed;
$primary-color-light: #f3e8ff;

$success-color: #52c41a;
$success-color-hover: #73d13d;
$success-color-active: #389e0d;
$success-color-light: #f6ffed;

$warning-color: #faad14;
$warning-color-hover: #ffc53d;
$warning-color-active: #d48806;
$warning-color-light: #fffbe6;

$error-color: #f5222d;
$error-color-hover: #ff4d4f;
$error-color-active: #cf1322;
$error-color-light: #fff2f0;

$info-color: #8b5cf6;
$info-color-hover: #a855f7;
$info-color-active: #7c3aed;
$info-color-light: #f3e8ff;

/* 文字颜色 */
$text-color: #333;
$text-color-secondary: #666;
$text-color-disabled: #999;
$text-color-light: #fff;

/* 背景色 */
$background-color: #fff;
$background-color-light: #fafafa;
$background-color-dark: #f5f5f5;
$background-color-darker: #e8e8e8;

/* 边框颜色 */
$border-color: #d9d9d9;
$border-color-light: #e8e8e8;
$border-color-dark: #bfbfbf;

/* 阴影 */
$shadow-light: 0 2px 8px rgb(0 0 0 / 10%);
$shadow-medium: 0 4px 12px rgb(0 0 0 / 15%);
$shadow-heavy: 0 6px 16px rgb(0 0 0 / 20%);

/* 圆角 */
$border-radius: 6px;
$border-radius-small: 4px;
$border-radius-large: 8px;
$border-radius-round: 50%;

/* 间距 */
$spacing-xs: 4px;
$spacing-sm: 8px;
$spacing-md: 16px;
$spacing-lg: 24px;
$spacing-xl: 32px;
$spacing-xxl: 48px;

/* 字体大小 */
$font-size-xs: 12px;
$font-size-sm: 14px;
$font-size-md: 16px;
$font-size-lg: 18px;
$font-size-xl: 20px;
$font-size-xxl: 24px;
$font-size-xxxl: 32px;

/* 字体粗细 */
$font-weight-light: 300;
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

/* 行高 */
$line-height-sm: 1.2;
$line-height-md: 1.5;
$line-height-lg: 1.8;

/* 布局尺寸 */
$header-height: 32px;
$sidebar-width: 240px;
$sidebar-width-collapsed: 80px;

/* 断点 */
$breakpoint-xs: 480px;
$breakpoint-sm: 768px;
$breakpoint-md: 992px;
$breakpoint-lg: 1200px;
$breakpoint-xl: 1600px;

/* Z-index层级 */
$z-index-dropdown: 1000;
$z-index-modal: 1050;
$z-index-popover: 1060;
$z-index-tooltip: 1070;
$z-index-notification: 1080;

/* 过渡动画 */
$transition-fast: 0.2s;
$transition-normal: 0.3s;
$transition-slow: 0.5s;

$transition-ease: ease;
$transition-ease-in: ease-in;
$transition-ease-out: ease-out;
$transition-ease-in-out: ease-in-out;

/* 中医理疗特色色彩 */
$tcm-green: #52c41a;        // 中医绿
$tcm-gold: #faad14;         // 金黄色
$tcm-red: #f5222d;          // 中国红
$tcm-blue: #8b5cf6;         // 浅紫色（替代蓝色）
$tcm-brown: #8b4513;        // 中药棕
$tcm-purple: #8b5cf6;       // 主题紫色

/* 状态色彩 */
$status-active: $success-color;
$status-inactive: $text-color-disabled;
$status-pending: $warning-color;
$status-cancelled: $error-color;
$status-completed: $success-color;
$status-processing: $info-color;

/* 业务相关色彩 */
$appointment-color: #8b5cf6;    // 预约（紫色主题）
$service-color: #52c41a;        // 服务
$customer-color: #9333ea;       // 客户（紫色主题）
$employee-color: #fa8c16;       // 员工
$finance-color: #13c2c2;        // 财务
$health-color: #eb2f96;         // 健康

/* 表格相关 */
$table-header-bg: $background-color-light;
$table-row-hover-bg: #f5f5f5;
$table-border-color: $border-color-light;

/* 表单相关 - 按照菜单规格调整 */
$form-item-margin: 8px;                    // 🎯 与菜单间距一致：8px
$form-item-height: 50px;                   // 🎯 与菜单高度一致：50px
$form-label-color: $text-color;
$form-input-border: $border-color;
$form-input-focus-border: $primary-color;

/* 卡片相关 */
$card-bg: $background-color;
$card-border: $border-color-light;
$card-shadow: $shadow-light;
$card-radius: $border-radius;
$card-padding: $spacing-lg;

/* 按钮相关 - 按照菜单规格调整 */
$button-height-sm: 42px;                   // 小按钮高度
$button-height-md: 50px;                   // 🎯 中等按钮高度与菜单一致：50px
$button-height-lg: 58px;                   // 大按钮高度
$button-padding-sm: 0 $spacing-sm;
$button-padding-md: 0 $spacing-md;
$button-padding-lg: 0 $spacing-lg;

/* 导航相关 */
$nav-bg: #001529;
$nav-text-color: rgb(255 255 255 / 65%);
$nav-text-color-active: #fff;
$nav-item-hover-bg: rgb(255 255 255 / 10%);

/* 响应式混入 */
@mixin mobile {
  @media (max-width: #{$breakpoint-sm - 1px}) {
    @content;
  }
}

@mixin tablet {
  @media (min-width: #{$breakpoint-sm}) and (max-width: #{$breakpoint-md - 1px}) {
    @content;
  }
}

@mixin desktop {
  @media (min-width: #{$breakpoint-md}) {
    @content;
  }
}

/* 工具混入 */
@mixin flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

@mixin flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

@mixin text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin clearfix {
  &::after {
    content: '';
    display: table;
    clear: both;
  }
}

/* 阴影混入 */
@mixin box-shadow($shadow: $shadow-light) {
  box-shadow: $shadow;
}

@mixin hover-shadow {
  transition: box-shadow $transition-normal $transition-ease;
  
  &:hover {
    box-shadow: $shadow-medium;
  }
}

/* 边框混入 */
@mixin border($color: $border-color, $width: 1px, $style: solid) {
  border: $width $style $color;
}

@mixin border-radius($radius: $border-radius) {
  border-radius: $radius;
}

/* 过渡动画混入 */
@mixin transition($property: all, $duration: $transition-normal, $timing: $transition-ease) {
  transition: $property $duration $timing;
}
