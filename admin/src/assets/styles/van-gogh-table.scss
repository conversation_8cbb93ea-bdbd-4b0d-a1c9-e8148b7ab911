/**
 * 梵高艺术风格表格设计
 * 将数据表格转化为艺术画布
 */

// 导入梵高主题 - 使用现代@use语法
@use './van-gogh-theme.scss';

// 表格容器 - 画布框架
.van-gogh-table-container {
  position: relative;
  margin: 20px 0;
  padding: 24px;
  
  // 艺术边框
  border: 3px solid transparent;
  border-radius: 16px;
  overflow: hidden;
  background: var(--van-gogh-bg-primary);
  background-image: 
    linear-gradient(var(--van-gogh-bg-primary), var(--van-gogh-bg-primary)),
    linear-gradient(45deg, 
      var(--van-gogh-secondary), 
      var(--van-gogh-accent), 
      var(--van-gogh-primary),
      var(--van-gogh-secondary));
  background-origin: border-box;
  background-clip: content-box, border-box;
  
  // 画布纹理
  &::before {
    position: absolute;
    inset: 0;
    z-index: 0;
    background: 
      radial-gradient(circle at 20% 80%, rgb(251 191 36 / 10%) 0%, transparent 50%),
      radial-gradient(circle at 80% 20%, rgb(30 58 138 / 10%) 0%, transparent 50%),
      radial-gradient(circle at 40% 40%, rgb(217 119 6 / 5%) 0%, transparent 50%);
    content: '';
    pointer-events: none;
  }
  
  @extend .van-gogh-shadow-artistic;
}

// 表格主体 - 艺术画布
.ant-table-wrapper.van-gogh-table {
  position: relative;
  z-index: 1;
  
  .ant-table {
    border: none;
    border-radius: 12px;
    overflow: hidden;
    background: transparent;
    table-layout: auto !important;

    // 修复表格布局
    .ant-table-container {
      .ant-table-header,
      .ant-table-body {
        table-layout: auto !important;

        table {
          table-layout: auto !important;
          width: 100% !important;
          min-width: 100% !important;
        }

        colgroup col {
          width: auto !important;
        }
      }
    }
    
    // 表格头部 - 画作标题区
    .ant-table-thead {
      background: linear-gradient(135deg,
        #a855f7 0%,
        #8b5cf6 50%,
        #7c3aed 100%);

      th {
        position: relative !important;
        width: auto !important;
        min-width: 80px !important;
        padding: 20px 16px !important;
        border: none !important;
        font-family: var(--van-gogh-font-heading) !important;
        font-size: var(--van-gogh-text-lg) !important;
        font-weight: 600 !important;
        text-align: center !important;
        color: white !important;
        background: transparent !important;
        
        // 标题装饰效果
        &::before {
          position: absolute;
          bottom: 0;
          left: 50%;
          width: 60%;
          height: 2px;
          background: linear-gradient(90deg,
            transparent 0%,
            var(--van-gogh-secondary-light) 50%,
            transparent 100%);
          transform: translateX(-50%);
          content: '';
        }
        
        // 悬停效果
        &:hover {
          background: rgb(255 255 255 / 10%) !important;
          transform: translateY(-2px);
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
      }
    }
    
    // 表格主体 - 画作内容区
    .ant-table-tbody {
      background: var(--van-gogh-bg-primary);
      
      tr {
        position: relative;
        border: none !important;
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        
        // 行背景艺术效果
        &:nth-child(odd) {
          background: linear-gradient(90deg,
            var(--van-gogh-bg-primary) 0%,
            var(--van-gogh-bg-secondary) 50%,
            var(--van-gogh-bg-primary) 100%) !important;
        }
        
        &:nth-child(even) {
          background: linear-gradient(90deg,
            var(--van-gogh-bg-secondary) 0%,
            var(--van-gogh-bg-tertiary) 50%,
            var(--van-gogh-bg-secondary) 100%) !important;
        }
        
        // 悬停时的艺术效果 - 修复错位问题
        &:hover {
          position: relative !important;
          z-index: 2 !important;
          border-radius: 6px !important;
          background: linear-gradient(135deg,
            rgb(168 85 247 / 15%) 0%,
            rgb(139 92 246 / 10%) 50%,
            rgb(124 58 237 / 15%) 100%) !important;
          box-shadow:
            0 4px 16px rgb(139 92 246 / 20%),
            0 2px 8px rgb(168 85 247 / 30%) !important;
          transform: translateY(-1px) !important;

        }
        
        // 选中状态
        &.ant-table-row-selected {
          background: linear-gradient(135deg,
            rgb(30 58 138 / 20%) 0%,
            rgb(251 191 36 / 15%) 100%) !important;
          border-left: 4px solid var(--van-gogh-secondary) !important;
          
          &::after {
            position: absolute;
            top: 50%;
            right: 10px;
            font-size: 1.2em;
            transform: translateY(-50%);
            content: '✨';
            animation: van-gogh-swirl 3s infinite;
          }
        }
        
        td {
          position: relative;
          padding: 16px !important;
          border: none !important;
          font-family: var(--van-gogh-font-body) !important;
          font-size: var(--van-gogh-text-base) !important;
          line-height: var(--van-gogh-leading-normal) !important;
          color: var(--van-gogh-text-primary) !important;
          
          // 单元格分隔线艺术效果
          &:not(:last-child)::after {
            position: absolute;
            top: 20%;
            right: 0;
            bottom: 20%;
            width: 1px;
            background: linear-gradient(180deg,
              transparent 0%,
              var(--van-gogh-border-light) 20%,
              var(--van-gogh-border-medium) 50%,
              var(--van-gogh-border-light) 80%,
              transparent 100%);
            content: '';
          }
        }
      }
    }
  }
  
  // 分页器艺术设计
  .ant-pagination {
    margin-top: 32px !important;
    text-align: center !important;
    
    .ant-pagination-item {
      margin: 0 4px !important;
      border: 2px solid var(--van-gogh-border-medium) !important;
      border-radius: 8px !important;
      font-family: var(--van-gogh-font-ui) !important;
      font-weight: 500 !important;
      background: var(--van-gogh-bg-secondary) !important;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
      
      &:hover {
        background: var(--van-gogh-secondary-light) !important;
        box-shadow: 0 4px 12px rgb(251 191 36 / 30%) !important;
        transform: translateY(-2px) !important;
        border-color: var(--van-gogh-secondary) !important;
      }
      
      &.ant-pagination-item-active {
        color: white !important;
        background: linear-gradient(135deg,
          var(--van-gogh-primary) 0%,
          var(--van-gogh-secondary) 100%) !important;
        box-shadow: 0 6px 20px rgb(30 58 138 / 40%) !important;
        transform: scale(1.1) !important;
        border-color: var(--van-gogh-primary) !important;
      }
      
      a {
        font-weight: 600 !important;
        color: var(--van-gogh-text-primary) !important;
      }
    }
    
    .ant-pagination-prev,
    .ant-pagination-next {
      border: 2px solid var(--van-gogh-border-medium) !important;
      border-radius: 8px !important;
      background: var(--van-gogh-bg-secondary) !important;
      
      &:hover {
        border-color: var(--van-gogh-accent) !important;
        background: var(--van-gogh-accent-light) !important;
        transform: translateY(-2px) !important;
      }
      
      .anticon {
        color: var(--van-gogh-text-primary) !important;
      }
    }
    
    .ant-pagination-total-text {
      font-family: var(--van-gogh-font-ui) !important;
      color: var(--van-gogh-text-secondary) !important;
      font-style: italic !important;
      margin-right: 16px !important;
    }
  }
}

// 标签艺术设计
.ant-tag {
  position: relative !important;
  padding: 4px 12px !important;
  border: none !important;
  border-radius: 20px !important;
  overflow: hidden !important;
  font-family: var(--van-gogh-font-ui) !important;
  font-size: var(--van-gogh-text-sm) !important;
  font-weight: 500 !important;
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg,
      transparent 0%,
      rgb(255 255 255 / 30%) 50%,
      transparent 100%);
    transition: left 0.5s ease;
  }
  
  &:hover::before {
    left: 100%;
  }
  
  &.ant-tag-purple {
    color: white !important;
    background: linear-gradient(135deg,
      var(--van-gogh-primary) 0%,
      var(--van-gogh-primary-light) 100%) !important;
    box-shadow: 0 2px 8px rgb(30 58 138 / 30%) !important;
  }
  
  &.ant-tag-magenta {
    color: white !important;
    background: linear-gradient(135deg,
      var(--van-gogh-accent) 0%,
      var(--van-gogh-secondary) 100%) !important;
    box-shadow: 0 2px 8px rgb(217 119 6 / 30%) !important;
  }
}

// 进度条艺术设计
.ant-progress {
  .ant-progress-bg {
    position: relative !important;
    border-radius: 10px !important;
    background: linear-gradient(90deg,
      var(--van-gogh-success) 0%,
      var(--van-gogh-secondary) 50%,
      var(--van-gogh-accent) 100%) !important;
    
    &::after {
      position: absolute;
      inset: 0;
      border-radius: 10px;
      background: linear-gradient(90deg,
        rgb(255 255 255 / 30%) 0%,
        transparent 50%,
        rgb(255 255 255 / 30%) 100%);
      content: '';
      animation: van-gogh-shimmer 2s infinite;
    }
  }
  
  .ant-progress-inner {
    border: 1px solid var(--van-gogh-border-light) !important;
    border-radius: 10px !important;
    background: var(--van-gogh-bg-tertiary) !important;
  }
  
  .ant-progress-text {
    font-family: var(--van-gogh-font-ui) !important;
    font-weight: 600 !important;
    color: var(--van-gogh-text-primary) !important;
  }
}

// 按钮艺术设计
.table-actions {
  display: flex !important;
  gap: 8px !important;
  justify-content: center !important;
  
  .ant-btn {
    position: relative !important;
    height: auto !important;
    padding: 4px 12px !important;
    border: none !important;
    border-radius: 20px !important;
    overflow: hidden !important;
    font-family: var(--van-gogh-font-ui) !important;
    font-weight: 500 !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
    
    &::before {
      position: absolute;
      top: 50%;
      left: 50%;
      width: 0;
      height: 0;
      border-radius: 50%;
      background: rgb(255 255 255 / 30%);
      transform: translate(-50%, -50%);
      transition: width 0.3s ease, height 0.3s ease;
      content: '';
    }
    
    &:hover::before {
      width: 200%;
      height: 200%;
    }
    
    &.ant-btn-link {
      color: white !important;
      background: linear-gradient(135deg,
        var(--van-gogh-primary-light) 0%,
        var(--van-gogh-secondary-light) 100%) !important;
      
      &:hover {
        box-shadow: 0 4px 12px rgb(30 58 138 / 30%) !important;
        transform: translateY(-2px) scale(1.05) !important;
      }
    }
    
    &.ant-btn-dangerous {
      color: white !important;
      background: linear-gradient(135deg,
        var(--van-gogh-error) 0%,
        var(--van-gogh-error-light) 100%) !important;
      
      &:hover {
        box-shadow: 0 4px 12px rgb(220 38 38 / 30%) !important;
        transform: translateY(-2px) scale(1.05) !important;
      }
    }
  }
}

// 表格布局修复 - 确保表头和数据对齐
.ant-table-wrapper {
  .ant-table {
    table-layout: fixed !important;

    .ant-table-container {
      .ant-table-header,
      .ant-table-body {
        table {
          table-layout: fixed !important;
          width: 100% !important;
        }
      }

      // 强制表头和表体使用相同的列宽
      .ant-table-header {
        table {
          colgroup col {
            width: auto !important;
          }
        }
      }

      .ant-table-body {
        table {
          colgroup col {
            width: auto !important;
          }
        }
      }
    }

    // 确保列宽一致性
    .ant-table-thead th,
    .ant-table-tbody td {
      overflow: hidden !important;
      text-overflow: ellipsis !important;
      white-space: nowrap !important;
    }
  }
}

// 隐藏测量行和空行
.ant-table-measure-row,
.ant-table-tbody > tr[aria-hidden="true"] {
  display: none !important;
  height: 0 !important;
  max-height: 0 !important;
  min-height: 0 !important;
  overflow: hidden !important;
  visibility: hidden !important;
}

// 响应式设计
@media (width <= 768px) {
  .van-gogh-table-container {
    margin: 10px 0;
    padding: 16px;
  }
  
  .ant-table-wrapper.van-gogh-table {
    .ant-table-thead th {
      padding: 12px 8px !important;
      font-size: var(--van-gogh-text-base) !important;
    }
    
    .ant-table-tbody td {
      padding: 12px 8px !important;
      font-size: var(--van-gogh-text-sm) !important;
    }
  }
  
  .table-actions {
    flex-direction: column !important;
    gap: 4px !important;
    
    .ant-btn {
      padding: 2px 8px !important;
      font-size: var(--van-gogh-text-xs) !important;
    }
  }
}
