/**
 * 🎯 4K基准响应式缩放系统
 * 基于用户当前4K分辨率(3840×2160)实现完美自适应缩放
 * 
 * <AUTHOR> Agent
 * @date 2025-01-21
 * @version 1.0.0
 */

// 🎯 基准分辨率配置 - 基于用户当前4K分辨率
const SCALE_CONFIG = {
  // 基准分辨率：用户当前的4K分辨率
  BASE_WIDTH: 3840,
  BASE_HEIGHT: 2160,
  
  // 缩放范围限制
  MIN_SCALE: 0.5,   // 最小50%缩放
  MAX_SCALE: 2.0,   // 最大200%缩放
  
  // 支持的分辨率断点
  BREAKPOINTS: {
    SMALL: { width: 1024, height: 768, name: '小屏桌面' },
    MEDIUM: { width: 1366, height: 768, name: '标准笔记本' },
    LARGE: { width: 1920, height: 1080, name: '桌面显示器' },
    XL: { width: 2560, height: 1440, name: '2K显示器' },
    XXL: { width: 3840, height: 2160, name: '4K显示器' }
  }
};

/**
 * 🔢 计算基于4K基准的缩放比例
 * @returns {number} 缩放比例 (0.5 - 2.0)
 */
export function calculateScale4K() {
  const currentWidth = window.innerWidth;
  const currentHeight = window.innerHeight;
  
  // 基于宽度和高度计算缩放比例
  const scaleX = currentWidth / SCALE_CONFIG.BASE_WIDTH;
  const scaleY = currentHeight / SCALE_CONFIG.BASE_HEIGHT;
  
  // 使用较小的缩放比例，确保内容不会超出视口
  const rawScale = Math.min(scaleX, scaleY);
  
  // 应用缩放范围限制
  const clampedScale = Math.max(
    SCALE_CONFIG.MIN_SCALE,
    Math.min(SCALE_CONFIG.MAX_SCALE, rawScale)
  );
  
  console.log(`🎯 4K基准缩放计算:`, {
    current: `${currentWidth}×${currentHeight}`,
    base: `${SCALE_CONFIG.BASE_WIDTH}×${SCALE_CONFIG.BASE_HEIGHT}`,
    scaleX: scaleX.toFixed(3),
    scaleY: scaleY.toFixed(3),
    rawScale: rawScale.toFixed(3),
    finalScale: clampedScale.toFixed(3)
  });
  
  return clampedScale;
}

/**
 * 🎨 应用4K基准缩放到根容器 - 确保不超出屏幕且完美对齐
 */
export function applyScale4K() {
  const scale = calculateScale4K();
  const root = document.documentElement;

  // 设置CSS自定义属性
  root.style.setProperty('--scale-factor-4k', scale);
  root.style.setProperty('--base-width-4k', `${SCALE_CONFIG.BASE_WIDTH}px`);
  root.style.setProperty('--base-height-4k', `${SCALE_CONFIG.BASE_HEIGHT}px`);

  // 应用到根容器 - 确保不超出屏幕
  const appContainer = document.querySelector('.admin-layout') || document.body;
  if (appContainer) {
    // 🎯 使用viewport单位而不是transform，确保不超出屏幕
    appContainer.style.transform = 'none'; // 移除transform缩放
    appContainer.style.width = '100vw'; // 占满视口宽度
    appContainer.style.height = '100vh'; // 占满视口高度

    // 🎯 通过CSS变量实现所有元素的比例缩放
    // 这样可以确保对齐关系保持正确
  }

  console.log(`✅ 4K基准缩放已应用: ${(scale * 100).toFixed(1)}% (viewport模式)`);

  return scale;
}

/**
 * 📊 获取当前分辨率信息
 */
export function getCurrentResolutionInfo() {
  const currentWidth = window.innerWidth;
  const currentHeight = window.innerHeight;
  const scale = calculateScale4K();
  
  // 确定当前分辨率类型
  let resolutionType = 'CUSTOM';
  let resolutionName = '自定义分辨率';
  
  for (const [key, breakpoint] of Object.entries(SCALE_CONFIG.BREAKPOINTS)) {
    if (Math.abs(currentWidth - breakpoint.width) <= 50 && 
        Math.abs(currentHeight - breakpoint.height) <= 50) {
      resolutionType = key;
      resolutionName = breakpoint.name;
      break;
    }
  }
  
  return {
    width: currentWidth,
    height: currentHeight,
    type: resolutionType,
    name: resolutionName,
    scale: scale,
    scalePercentage: `${(scale * 100).toFixed(1)}%`,
    isBase: resolutionType === 'XXL', // 是否为4K基准分辨率
    devicePixelRatio: window.devicePixelRatio || 1
  };
}

/**
 * 🔄 监听窗口大小变化并自动调整缩放
 */
export function initResponsiveScale4K() {
  // 防抖函数，避免频繁计算
  let resizeTimer = null;
  
  const handleResize = () => {
    if (resizeTimer) {
      clearTimeout(resizeTimer);
    }
    
    resizeTimer = setTimeout(() => {
      const newScale = applyScale4K();
      const resolutionInfo = getCurrentResolutionInfo();
      
      // 触发自定义事件，通知其他组件
      window.dispatchEvent(new CustomEvent('scale4k-changed', {
        detail: {
          scale: newScale,
          resolution: resolutionInfo
        }
      }));
      
      console.log(`🔄 分辨率变化，重新计算缩放:`, resolutionInfo);
    }, 150); // 150ms防抖
  };
  
  // 监听窗口大小变化
  window.addEventListener('resize', handleResize);
  
  // 初始化时应用缩放
  applyScale4K();
  
  console.log('🚀 4K基准响应式缩放系统已初始化');
  
  // 返回清理函数
  return () => {
    window.removeEventListener('resize', handleResize);
    if (resizeTimer) {
      clearTimeout(resizeTimer);
    }
  };
}

/**
 * 🧪 测试和验证缩放效果
 */
export function validateScale4K() {
  const resolutionInfo = getCurrentResolutionInfo();
  const scale = resolutionInfo.scale;
  
  const validation = {
    scaleInRange: scale >= SCALE_CONFIG.MIN_SCALE && scale <= SCALE_CONFIG.MAX_SCALE,
    transformApplied: false,
    cssVariablesSet: false,
    containerScaled: false
  };
  
  // 检查CSS变量是否设置
  const root = document.documentElement;
  const scaleFactor = getComputedStyle(root).getPropertyValue('--scale-factor-4k');
  validation.cssVariablesSet = scaleFactor && parseFloat(scaleFactor) === scale;
  
  // 检查容器变换是否应用
  const appContainer = document.querySelector('.admin-layout') || document.body;
  if (appContainer) {
    const transform = getComputedStyle(appContainer).transform;
    validation.transformApplied = transform && transform !== 'none';
    validation.containerScaled = transform.includes(`scale(${scale})`);
  }
  
  const allValid = Object.values(validation).every(v => v === true);
  
  console.log('🧪 4K缩放验证结果:', {
    resolution: resolutionInfo,
    validation: validation,
    allValid: allValid
  });
  
  return {
    valid: allValid,
    resolution: resolutionInfo,
    validation: validation
  };
}

/**
 * 🎯 获取元素级缩放CSS函数
 * 用于在CSS中应用基于4K基准的缩放
 */
export function getScaleCSS() {
  return {
    // 基础缩放函数
    scale: (value) => `calc(${value} * var(--scale-factor-4k, 1))`,
    
    // 常用属性的缩放函数
    width: (value) => `calc(${value} * var(--scale-factor-4k, 1))`,
    height: (value) => `calc(${value} * var(--scale-factor-4k, 1))`,
    padding: (value) => `calc(${value} * var(--scale-factor-4k, 1))`,
    margin: (value) => `calc(${value} * var(--scale-factor-4k, 1))`,
    fontSize: (value) => `calc(${value} * var(--scale-factor-4k, 1))`,
    borderRadius: (value) => `calc(${value} * var(--scale-factor-4k, 1))`,
    
    // 复合属性的缩放函数
    boxShadow: (x, y, blur, spread, color) => 
      `calc(${x} * var(--scale-factor-4k, 1)) calc(${y} * var(--scale-factor-4k, 1)) calc(${blur} * var(--scale-factor-4k, 1)) calc(${spread} * var(--scale-factor-4k, 1)) ${color}`
  };
}

// 导出配置供其他模块使用
export { SCALE_CONFIG };
