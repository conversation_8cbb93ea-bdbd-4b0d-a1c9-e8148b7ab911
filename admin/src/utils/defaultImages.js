/**
 * 默认服务图片库
 * 为服务管理提供高质量的默认图片
 */

// 使用高质量的Unsplash图片作为默认图片
const DEFAULT_IMAGES = {
  // 传统中医服务
  '按摩': 'https://images.unsplash.com/photo-1600334129128-685c5582fd35?w=400&h=300&fit=crop&q=80',
  '推拿': 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400&h=300&fit=crop&q=80',
  '足疗': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80',
  '艾灸': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&q=80',
  '拔罐': 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop&q=80',
  '刮痧': 'https://images.unsplash.com/photo-1540555700478-4be289fbecef?w=400&h=300&fit=crop&q=80',
  '针灸': 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop&q=80',
  
  // 现代理疗服务
  '理疗': 'https://images.unsplash.com/photo-1576091160399-112ba8d25d1f?w=400&h=300&fit=crop&q=80',
  '康复': 'https://images.unsplash.com/photo-1576091160550-2173dba999ef?w=400&h=300&fit=crop&q=80',
  '物理治疗': 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400&h=300&fit=crop&q=80',
  
  // 养生保健服务
  '养生': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&q=80',
  '保健': 'https://images.unsplash.com/photo-1600334129128-685c5582fd35?w=400&h=300&fit=crop&q=80',
  '调理': 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400&h=300&fit=crop&q=80',
  
  // 美容护理服务
  '美容': 'https://images.unsplash.com/photo-1560472354-b33ff0c44a43?w=400&h=300&fit=crop&q=80',
  '护肤': 'https://images.unsplash.com/photo-1556228578-8c89e6adf883?w=400&h=300&fit=crop&q=80',
  '面部护理': 'https://images.unsplash.com/photo-1570172619644-dfd03ed5d881?w=400&h=300&fit=crop&q=80',
  
  // 身心调理服务
  '瑜伽': 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=300&fit=crop&q=80',
  '冥想': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&q=80',
  '香薰': 'https://images.unsplash.com/photo-1600334129128-685c5582fd35?w=400&h=300&fit=crop&q=80',
  '芳疗': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80',
  
  // 特色服务
  '减肥': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop&q=80',
  '塑形': 'https://images.unsplash.com/photo-1544367567-0f2fcb009e0b?w=400&h=300&fit=crop&q=80',
  '排毒': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400&h=300&fit=crop&q=80',
  '调经': 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400&h=300&fit=crop&q=80',
  
  // 通用默认图片
  'default': 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400&h=300&fit=crop&q=80'
};

/**
 * 获取服务默认图片
 * @param {string} serviceName - 服务名称
 * @returns {string} 图片URL
 */
export const getDefaultServiceImage = (serviceName) => {
  if (!serviceName) {
    return DEFAULT_IMAGES.default;
  }

  // 精确匹配
  if (DEFAULT_IMAGES[serviceName]) {
    return DEFAULT_IMAGES[serviceName];
  }

  // 模糊匹配
  for (const [key, imageUrl] of Object.entries(DEFAULT_IMAGES)) {
    if (serviceName.includes(key)) {
      return imageUrl;
    }
  }

  // 返回默认图片
  return DEFAULT_IMAGES.default;
};

/**
 * 获取所有可用的服务图片
 * @returns {Object} 服务图片映射
 */
export const getAllServiceImages = () => {
  return { ...DEFAULT_IMAGES };
};

/**
 * 检查图片URL是否有效
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<boolean>} 是否有效
 */
export const validateImageUrl = async (imageUrl) => {
  try {
    const response = await fetch(imageUrl, { method: 'HEAD' });
    return response.ok;
  } catch (error) {
    console.warn('图片URL验证失败:', imageUrl, error);
    return false;
  }
};

/**
 * 预加载图片
 * @param {string} imageUrl - 图片URL
 * @returns {Promise<void>}
 */
export const preloadImage = (imageUrl) => {
  return new Promise((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve();
    img.onerror = () => reject(new Error(`图片加载失败: ${imageUrl}`));
    img.src = imageUrl;
  });
};

/**
 * 批量预加载默认图片
 * @returns {Promise<void>}
 */
export const preloadDefaultImages = async () => {
  const imageUrls = Object.values(DEFAULT_IMAGES);
  const uniqueUrls = [...new Set(imageUrls)]; // 去重
  
  try {
    await Promise.all(uniqueUrls.map(url => preloadImage(url)));
    console.log('✅ 默认图片预加载完成');
  } catch (error) {
    console.warn('⚠️ 部分图片预加载失败:', error);
  }
};

export default {
  getDefaultServiceImage,
  getAllServiceImages,
  validateImageUrl,
  preloadImage,
  preloadDefaultImages
};
