/**
 * 响应式测试工具 - 用于测试不同分辨率下的布局
 * 符合壹心堂开发规范 - 支持8种分辨率测试
 */

export class ResponsiveTestTool {
  constructor() {
    this.isActive = false;
    this.currentResolution = null;
    this.testResults = [];
    
    // 🚨 强制要求：必须测试8种不同分辨率 (符合开发规范)
    this.resolutions = [
      { name: '4K显示器', width: 3840, height: 2160, type: 'desktop' },
      { name: '2K显示器', width: 2560, height: 1440, type: 'desktop' },
      { name: '标准桌面', width: 1920, height: 1080, type: 'desktop' },
      { name: '小桌面', width: 1366, height: 768, type: 'desktop' },
      { name: '平板横屏', width: 1024, height: 768, type: 'tablet' },
      { name: '平板竖屏', width: 768, height: 1024, type: 'tablet' },
      { name: '手机横屏', width: 667, height: 375, type: 'mobile' },
      { name: '手机竖屏', width: 375, height: 667, type: 'mobile' }
    ];

    this.breakpoints = {
      mobile: 768,
      tablet: 1024,
      desktop: 1025
    };
  }

  /**
   * 初始化响应式测试工具
   */
  init() {
    this.createTestPanel();
    this.bindEvents();
    console.log('🔧 响应式测试工具已初始化');
  }

  /**
   * 创建测试控制面板
   */
  createTestPanel() {
    const panel = document.createElement('div');
    panel.id = 'responsive-test-panel';
    panel.className = 'responsive-test-panel';
    panel.innerHTML = `
      <div class="test-panel-header">
        <h3>📱 响应式测试工具</h3>
        <button class="toggle-btn" onclick="responsiveTestTool.toggle()">
          ${this.isActive ? '关闭' : '开启'}
        </button>
      </div>
      <div class="test-panel-content" style="display: ${this.isActive ? 'block' : 'none'}">
        <div class="resolution-grid">
          ${this.resolutions.map(res => `
            <button class="resolution-btn ${res.type}" 
                    onclick="responsiveTestTool.setResolution(${res.width}, ${res.height}, '${res.name}')">
              <div class="res-name">${res.name}</div>
              <div class="res-size">${res.width} × ${res.height}</div>
            </button>
          `).join('')}
        </div>
        <div class="test-controls">
          <button onclick="responsiveTestTool.runFullTest()" class="test-btn primary">
            🧪 运行完整测试
          </button>
          <button onclick="responsiveTestTool.checkCurrentLayout()" class="test-btn">
            🔍 检查当前布局
          </button>
          <button onclick="responsiveTestTool.resetViewport()" class="test-btn">
            🔄 重置视窗
          </button>
        </div>
        <div class="test-results" id="test-results"></div>
      </div>
    `;

    document.body.appendChild(panel);
    this.addTestPanelStyles();
  }

  /**
   * 添加测试面板样式
   */
  addTestPanelStyles() {
    const style = document.createElement('style');
    style.textContent = `
      .responsive-test-panel {
        position: fixed;
        top: 20px;
        right: 20px;
        width: 350px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 12px;
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        z-index: 10000;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        color: white;
      }

      .test-panel-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 15px 20px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2);
      }

      .test-panel-header h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
      }

      .toggle-btn {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        color: white;
        padding: 6px 12px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 12px;
        transition: all 0.3s ease;
      }

      .toggle-btn:hover {
        background: rgba(255, 255, 255, 0.3);
        transform: scale(1.05);
      }

      .test-panel-content {
        padding: 20px;
      }

      .resolution-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 8px;
        margin-bottom: 15px;
      }

      .resolution-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 10px;
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
        text-align: center;
      }

      .resolution-btn:hover {
        background: rgba(255, 255, 255, 0.2);
        transform: translateY(-2px);
      }

      .resolution-btn.desktop { border-left: 4px solid #4CAF50; }
      .resolution-btn.tablet { border-left: 4px solid #FF9800; }
      .resolution-btn.mobile { border-left: 4px solid #F44336; }

      .res-name {
        font-size: 11px;
        font-weight: 600;
        margin-bottom: 4px;
      }

      .res-size {
        font-size: 10px;
        opacity: 0.8;
      }

      .test-controls {
        display: flex;
        gap: 8px;
        margin-bottom: 15px;
        flex-wrap: wrap;
      }

      .test-btn {
        background: rgba(255, 255, 255, 0.1);
        border: 1px solid rgba(255, 255, 255, 0.2);
        color: white;
        padding: 8px 12px;
        border-radius: 6px;
        cursor: pointer;
        font-size: 11px;
        transition: all 0.3s ease;
        flex: 1;
        min-width: 80px;
      }

      .test-btn:hover {
        background: rgba(255, 255, 255, 0.2);
      }

      .test-btn.primary {
        background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
        border-color: #ff6b6b;
      }

      .test-results {
        max-height: 200px;
        overflow-y: auto;
        background: rgba(0, 0, 0, 0.2);
        border-radius: 6px;
        padding: 10px;
        font-size: 11px;
        line-height: 1.4;
      }

      .test-result-item {
        margin-bottom: 8px;
        padding: 6px;
        border-radius: 4px;
        background: rgba(255, 255, 255, 0.05);
      }

      .test-result-item.success { border-left: 3px solid #4CAF50; }
      .test-result-item.warning { border-left: 3px solid #FF9800; }
      .test-result-item.error { border-left: 3px solid #F44336; }

      /* 响应式适配 */
      @media (max-width: 768px) {
        .responsive-test-panel {
          width: calc(100vw - 40px);
          right: 20px;
          left: 20px;
        }
        
        .resolution-grid {
          grid-template-columns: 1fr;
        }
      }
    `;
    document.head.appendChild(style);
  }

  /**
   * 切换测试工具显示状态
   */
  toggle() {
    this.isActive = !this.isActive;
    const content = document.querySelector('.test-panel-content');
    const toggleBtn = document.querySelector('.toggle-btn');
    
    if (content && toggleBtn) {
      content.style.display = this.isActive ? 'block' : 'none';
      toggleBtn.textContent = this.isActive ? '关闭' : '开启';
    }
  }

  /**
   * 设置指定分辨率
   */
  setResolution(width, height, name) {
    this.currentResolution = { width, height, name };
    
    // 调整视窗大小
    if (window.outerWidth && window.outerHeight) {
      window.resizeTo(width, height);
    } else {
      // 如果无法调整窗口大小，则模拟视窗
      this.simulateViewport(width, height);
    }

    this.logResult(`📐 已切换到 ${name} (${width} × ${height})`, 'success');
    
    // 延迟检查布局，等待DOM更新
    setTimeout(() => {
      this.checkCurrentLayout();
    }, 500);
  }

  /**
   * 模拟视窗大小（当无法调整窗口时）
   */
  simulateViewport(width, height) {
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', `width=${width}, initial-scale=1.0`);
    }

    // 添加模拟样式
    const simulateStyle = document.getElementById('viewport-simulate') || document.createElement('style');
    simulateStyle.id = 'viewport-simulate';
    simulateStyle.textContent = `
      body {
        width: ${width}px !important;
        max-width: ${width}px !important;
      }
      .service-management {
        width: ${width}px !important;
        max-width: ${width}px !important;
      }
    `;
    document.head.appendChild(simulateStyle);
  }

  /**
   * 检查当前布局
   */
  checkCurrentLayout() {
    const results = [];
    const currentWidth = window.innerWidth;
    const currentHeight = window.innerHeight;

    results.push(`🔍 当前视窗: ${currentWidth} × ${currentHeight}`);

    // 检查断点
    const deviceType = this.getDeviceType(currentWidth);
    results.push(`📱 设备类型: ${deviceType}`);

    // 检查关键元素
    this.checkSearchSection(results);
    this.checkTableLayout(results);
    this.checkPaginationLayout(results);
    this.checkModalLayout(results);
    this.checkScrollbars(results);

    // 显示结果
    this.displayResults(results);
    return results;
  }

  /**
   * 获取设备类型
   */
  getDeviceType(width) {
    if (width <= this.breakpoints.mobile) return 'Mobile (移动端)';
    if (width <= this.breakpoints.tablet) return 'Tablet (平板端)';
    return 'Desktop (桌面端)';
  }

  /**
   * 检查搜索区域布局
   */
  checkSearchSection(results) {
    const searchSection = document.querySelector('.search-section');
    if (!searchSection) {
      results.push('❌ 搜索区域未找到');
      return;
    }

    const rect = searchSection.getBoundingClientRect();
    const isVisible = rect.width > 0 && rect.height > 0;

    results.push(`🔍 搜索区域: ${isVisible ? '✅' : '❌'} (${Math.round(rect.width)} × ${Math.round(rect.height)})`);

    // 检查搜索输入框
    const searchInput = document.querySelector('.search-input');
    if (searchInput) {
      const inputRect = searchInput.getBoundingClientRect();
      const inputVisible = inputRect.width > 200; // 最小宽度检查
      results.push(`📝 搜索输入框: ${inputVisible ? '✅' : '⚠️'} 宽度 ${Math.round(inputRect.width)}px`);
    }

    // 检查搜索建议下拉框
    const suggestions = document.querySelector('.search-suggestions');
    if (suggestions && suggestions.style.display !== 'none') {
      const sugRect = suggestions.getBoundingClientRect();
      results.push(`💡 搜索建议: 显示中 (${Math.round(sugRect.width)} × ${Math.round(sugRect.height)})`);
    }
  }

  /**
   * 检查表格布局
   */
  checkTableLayout(results) {
    const tableContainer = document.querySelector('.table-container');
    if (!tableContainer) {
      results.push('❌ 表格容器未找到');
      return;
    }

    const rect = tableContainer.getBoundingClientRect();
    results.push(`📊 表格容器: ${Math.round(rect.width)} × ${Math.round(rect.height)}`);

    // 检查表格头部
    const tableHeader = document.querySelector('.table-header');
    if (tableHeader) {
      const headerRect = tableHeader.getBoundingClientRect();
      results.push(`📋 表格头部: ${Math.round(headerRect.width)} × ${Math.round(headerRect.height)}`);
    }

    // 检查数据行
    const dataRows = document.querySelectorAll('.data-row');
    if (dataRows.length > 0) {
      const firstRowRect = dataRows[0].getBoundingClientRect();
      results.push(`📄 数据行: ${dataRows.length}行, 高度 ${Math.round(firstRowRect.height)}px`);

      // 检查是否有水平滚动
      const hasHorizontalScroll = tableContainer.scrollWidth > tableContainer.clientWidth;
      if (hasHorizontalScroll) {
        results.push('⚠️ 检测到水平滚动条');
      }
    }

    // 检查响应式适配
    const currentWidth = window.innerWidth;
    if (currentWidth <= 768) {
      // 移动端检查
      const mobileOptimized = this.checkMobileTableOptimization();
      results.push(`📱 移动端优化: ${mobileOptimized ? '✅' : '❌'}`);
    }
  }

  /**
   * 检查分页布局
   */
  checkPaginationLayout(results) {
    const pagination = document.querySelector('.pagination-container');
    if (!pagination) {
      results.push('❌ 分页容器未找到');
      return;
    }

    const rect = pagination.getBoundingClientRect();
    results.push(`📄 分页容器: ${Math.round(rect.width)} × ${Math.round(rect.height)}`);

    // 检查分页信息
    const totalInfo = document.querySelector('.total-info');
    const pageNavigation = document.querySelector('.page-navigation');
    const pageSizeSelect = document.querySelector('.page-size-select');

    if (totalInfo) {
      const infoRect = totalInfo.getBoundingClientRect();
      results.push(`ℹ️ 分页信息: ${Math.round(infoRect.width)}px 宽度`);
    }

    if (pageNavigation) {
      const navRect = pageNavigation.getBoundingClientRect();
      results.push(`🔄 页码导航: ${Math.round(navRect.width)}px 宽度`);
    }

    if (pageSizeSelect) {
      const selectRect = pageSizeSelect.getBoundingClientRect();
      results.push(`📏 页面大小选择: ${Math.round(selectRect.width)}px 宽度`);
    }
  }

  /**
   * 检查模态框布局
   */
  checkModalLayout(results) {
    const modals = document.querySelectorAll('.modal-overlay');
    if (modals.length === 0) {
      results.push('ℹ️ 当前无模态框显示');
      return;
    }

    modals.forEach((modal, index) => {
      const rect = modal.getBoundingClientRect();
      const isVisible = modal.style.display !== 'none' && rect.width > 0;

      if (isVisible) {
        results.push(`🔲 模态框 ${index + 1}: ${Math.round(rect.width)} × ${Math.round(rect.height)}`);

        // 检查模态框内容
        const modalContent = modal.querySelector('.form-modal');
        if (modalContent) {
          const contentRect = modalContent.getBoundingClientRect();
          results.push(`📝 模态框内容: ${Math.round(contentRect.width)} × ${Math.round(contentRect.height)}`);

          // 检查是否超出视窗
          const exceedsViewport = contentRect.bottom > window.innerHeight || contentRect.right > window.innerWidth;
          if (exceedsViewport) {
            results.push('⚠️ 模态框超出视窗范围');
          }
        }
      }
    });
  }

  /**
   * 检查滚动条
   */
  checkScrollbars(results) {
    const scrollableElements = [
      { selector: '.table-body', name: '表格主体' },
      { selector: '.search-suggestions', name: '搜索建议' },
      { selector: '.form-modal', name: '模态框' },
      { selector: '.service-management', name: '主容器' }
    ];

    scrollableElements.forEach(({ selector, name }) => {
      const element = document.querySelector(selector);
      if (element) {
        const hasVerticalScroll = element.scrollHeight > element.clientHeight;
        const hasHorizontalScroll = element.scrollWidth > element.clientWidth;

        if (hasVerticalScroll || hasHorizontalScroll) {
          const scrollInfo = [];
          if (hasVerticalScroll) scrollInfo.push('垂直');
          if (hasHorizontalScroll) scrollInfo.push('水平');
          results.push(`📜 ${name}: ${scrollInfo.join('+')}滚动条`);
        }
      }
    });
  }

  /**
   * 检查移动端表格优化
   */
  checkMobileTableOptimization() {
    const tableHeader = document.querySelector('.table-header');
    const dataRows = document.querySelectorAll('.data-row');

    if (!tableHeader || dataRows.length === 0) return false;

    // 检查是否隐藏了表头（移动端优化）
    const headerStyle = window.getComputedStyle(tableHeader);
    const headerHidden = headerStyle.display === 'none';

    // 检查数据行是否采用了卡片式布局
    const firstRow = dataRows[0];
    const rowStyle = window.getComputedStyle(firstRow);
    const isCardLayout = rowStyle.flexDirection === 'column';

    return headerHidden && isCardLayout;
  }

  /**
   * 运行完整测试
   */
  async runFullTest() {
    this.testResults = [];
    this.logResult('🚀 开始运行完整响应式测试...', 'success');

    for (const resolution of this.resolutions) {
      this.logResult(`📐 测试分辨率: ${resolution.name} (${resolution.width} × ${resolution.height})`, 'info');

      // 设置分辨率
      this.setResolution(resolution.width, resolution.height, resolution.name);

      // 等待布局更新
      await this.delay(1000);

      // 检查布局
      const layoutResults = this.checkCurrentLayout();

      // 记录测试结果
      this.testResults.push({
        resolution: resolution,
        results: layoutResults,
        timestamp: new Date().toISOString()
      });

      // 等待下一个测试
      await this.delay(500);
    }

    this.logResult('✅ 完整测试完成！', 'success');
    this.generateDetailedReport();
  }

  /**
   * 生成详细报告
   */
  generateDetailedReport() {
    const report = {
      testDate: new Date().toISOString(),
      totalResolutions: this.resolutions.length,
      results: this.testResults,
      summary: this.generateSummary()
    };

    // 显示报告
    this.displayDetailedReport(report);

    // 保存到本地存储
    localStorage.setItem('responsive-test-report', JSON.stringify(report));

    console.log('📊 响应式测试报告:', report);
    return report;
  }

  /**
   * 生成测试摘要
   */
  generateSummary() {
    const summary = {
      totalTests: this.testResults.length,
      passedTests: 0,
      warningTests: 0,
      failedTests: 0,
      commonIssues: []
    };

    this.testResults.forEach(test => {
      const hasErrors = test.results.some(result => result.includes('❌'));
      const hasWarnings = test.results.some(result => result.includes('⚠️'));

      if (hasErrors) {
        summary.failedTests++;
      } else if (hasWarnings) {
        summary.warningTests++;
      } else {
        summary.passedTests++;
      }
    });

    return summary;
  }

  /**
   * 显示详细报告
   */
  displayDetailedReport(report) {
    const resultsContainer = document.getElementById('test-results');
    if (!resultsContainer) return;

    resultsContainer.innerHTML = `
      <div class="detailed-report">
        <h4>📊 测试报告摘要</h4>
        <div class="summary-stats">
          <div class="stat-item success">✅ 通过: ${report.summary.passedTests}</div>
          <div class="stat-item warning">⚠️ 警告: ${report.summary.warningTests}</div>
          <div class="stat-item error">❌ 失败: ${report.summary.failedTests}</div>
        </div>
        <div class="test-details">
          ${report.results.map(test => `
            <div class="test-result-detail">
              <h5>${test.resolution.name} (${test.resolution.width}×${test.resolution.height})</h5>
              <div class="result-items">
                ${test.results.slice(0, 5).map(result => `
                  <div class="result-item ${this.getResultType(result)}">${result}</div>
                `).join('')}
              </div>
            </div>
          `).join('')}
        </div>
      </div>
    `;
  }

  /**
   * 获取结果类型
   */
  getResultType(result) {
    if (result.includes('❌')) return 'error';
    if (result.includes('⚠️')) return 'warning';
    if (result.includes('✅')) return 'success';
    return 'info';
  }

  /**
   * 显示测试结果
   */
  displayResults(results) {
    const resultsContainer = document.getElementById('test-results');
    if (!resultsContainer) return;

    const resultHtml = results.map(result => `
      <div class="test-result-item ${this.getResultType(result)}">
        ${result}
      </div>
    `).join('');

    resultsContainer.innerHTML = resultHtml;
  }

  /**
   * 记录测试结果
   */
  logResult(message, type = 'info') {
    const timestamp = new Date().toLocaleTimeString();
    const logMessage = `[${timestamp}] ${message}`;

    console.log(logMessage);

    // 添加到结果显示
    const resultsContainer = document.getElementById('test-results');
    if (resultsContainer) {
      const resultItem = document.createElement('div');
      resultItem.className = `test-result-item ${type}`;
      resultItem.textContent = logMessage;
      resultsContainer.appendChild(resultItem);

      // 滚动到底部
      resultsContainer.scrollTop = resultsContainer.scrollHeight;
    }
  }

  /**
   * 重置视窗
   */
  resetViewport() {
    // 移除模拟样式
    const simulateStyle = document.getElementById('viewport-simulate');
    if (simulateStyle) {
      simulateStyle.remove();
    }

    // 重置视窗标签
    const viewport = document.querySelector('meta[name="viewport"]');
    if (viewport) {
      viewport.setAttribute('content', 'width=device-width, initial-scale=1.0');
    }

    this.currentResolution = null;
    this.logResult('🔄 视窗已重置', 'success');
  }

  /**
   * 绑定事件
   */
  bindEvents() {
    // 监听窗口大小变化
    window.addEventListener('resize', () => {
      if (this.isActive) {
        setTimeout(() => {
          this.checkCurrentLayout();
        }, 300);
      }
    });
  }

  /**
   * 延迟函数
   */
  delay(ms) {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 销毁测试工具
   */
  destroy() {
    const panel = document.getElementById('responsive-test-panel');
    if (panel) {
      panel.remove();
    }

    this.resetViewport();
    console.log('🔧 响应式测试工具已销毁');
  }
}

// 创建全局实例
window.responsiveTestTool = new ResponsiveTestTool();
