/**
 * 全局表格格式化工具
 * 自动优化表格数据显示效果
 */

class TableFormatter {
  /**
   * 格式化价格显示
   * @param {number} price - 价格
   * @returns {string} 格式化后的价格
   */
  static formatPrice(price) {
    if (price === null || price === undefined) return '-';
    const num = parseFloat(price);
    if (isNaN(num)) return '-';
    
    // 根据价格大小选择显示格式
    if (num >= 1000) {
      return `¥${(num / 1000).toFixed(1)}k`;
    } else if (num >= 100) {
      return `¥${num.toFixed(0)}`;
    } else {
      return `¥${num.toFixed(2)}`;
    }
  }

  /**
   * 格式化提成显示
   * @param {number} commission - 提成金额
   * @param {number} price - 服务价格
   * @returns {string} 格式化后的提成
   */
  static formatCommission(commission, price) {
    if (!commission || !price) return '-';
    
    const commissionNum = parseFloat(commission);
    const priceNum = parseFloat(price);
    
    if (isNaN(commissionNum) || isNaN(priceNum) || priceNum === 0) return '-';
    
    const rate = ((commissionNum / priceNum) * 100).toFixed(1);
    const formattedCommission = this.formatPrice(commissionNum);
    
    return `${formattedCommission} (${rate}%)`;
  }

  /**
   * 格式化时长显示
   * @param {number} duration - 时长（分钟）
   * @returns {string} 格式化后的时长
   */
  static formatDuration(duration) {
    if (!duration) return '-';
    const num = parseInt(duration);
    if (isNaN(num)) return '-';
    
    if (num >= 60) {
      const hours = Math.floor(num / 60);
      const minutes = num % 60;
      if (minutes === 0) {
        return `${hours}小时`;
      } else {
        return `${hours}小时${minutes}分钟`;
      }
    } else {
      return `${num}分钟`;
    }
  }

  /**
   * 格式化状态显示
   * @param {string} status - 状态值
   * @returns {object} 包含文本和样式的对象
   */
  static formatStatus(status) {
    const statusMap = {
      'active': { text: '上架', color: '#52c41a', bgColor: '#f6ffed' },
      'inactive': { text: '下架', color: '#ff4d4f', bgColor: '#fff2f0' },
      'pending': { text: '审核', color: '#faad14', bgColor: '#fffbe6' },
      'draft': { text: '草稿', color: '#8c8c8c', bgColor: '#f5f5f5' },
      'completed': { text: '完成', color: '#52c41a', bgColor: '#f6ffed' },
      'cancelled': { text: '取消', color: '#ff4d4f', bgColor: '#fff2f0' },
      'confirmed': { text: '确认', color: '#1890ff', bgColor: '#e6f7ff' },
      'processing': { text: '处理', color: '#722ed1', bgColor: '#f9f0ff' }
    };

    return statusMap[status] || { text: status, color: '#8c8c8c', bgColor: '#f5f5f5' };
  }

  /**
   * 格式化日期显示
   * @param {string|Date} date - 日期
   * @returns {string} 格式化后的日期
   */
  static formatDate(date) {
    if (!date) return '-';
    
    const dateObj = new Date(date);
    if (isNaN(dateObj.getTime())) return '-';
    
    const now = new Date();
    const diffTime = now - dateObj;
    const diffDays = Math.floor(diffTime / (1000 * 60 * 60 * 24));
    
    // 今天
    if (diffDays === 0) {
      return `今天 ${dateObj.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
    }
    // 昨天
    else if (diffDays === 1) {
      return `昨天 ${dateObj.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
    }
    // 本周内
    else if (diffDays < 7) {
      const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      return `${weekdays[dateObj.getDay()]} ${dateObj.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })}`;
    }
    // 本年内
    else if (dateObj.getFullYear() === now.getFullYear()) {
      return dateObj.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' });
    }
    // 其他年份
    else {
      return dateObj.toLocaleDateString('zh-CN', { year: 'numeric', month: '2-digit', day: '2-digit' });
    }
  }

  /**
   * 格式化手机号显示
   * @param {string} phone - 手机号
   * @returns {string} 格式化后的手机号
   */
  static formatPhone(phone) {
    if (!phone) return '-';
    const phoneStr = phone.toString();
    
    // 中国手机号格式化
    if (phoneStr.length === 11 && phoneStr.startsWith('1')) {
      return `${phoneStr.slice(0, 3)} ${phoneStr.slice(3, 7)} ${phoneStr.slice(7)}`;
    }
    
    return phoneStr;
  }

  /**
   * 格式化评分显示
   * @param {number} rating - 评分
   * @param {number} maxRating - 最高评分
   * @returns {string} 格式化后的评分
   */
  static formatRating(rating, maxRating = 5) {
    if (rating === null || rating === undefined) return '-';
    const num = parseFloat(rating);
    if (isNaN(num)) return '-';
    
    const stars = '★'.repeat(Math.floor(num)) + '☆'.repeat(maxRating - Math.floor(num));
    return `${stars} ${num.toFixed(1)}`;
  }

  /**
   * 格式化文本长度
   * @param {string} text - 文本
   * @param {number} maxLength - 最大长度
   * @returns {string} 格式化后的文本
   */
  static formatText(text, maxLength = 20) {
    if (!text) return '-';
    const str = text.toString();
    
    if (str.length <= maxLength) {
      return str;
    }
    
    return `${str.slice(0, maxLength)}...`;
  }

  /**
   * 格式化数字显示
   * @param {number} num - 数字
   * @param {string} unit - 单位
   * @returns {string} 格式化后的数字
   */
  static formatNumber(num, unit = '') {
    if (num === null || num === undefined) return '-';
    const number = parseFloat(num);
    if (isNaN(number)) return '-';
    
    if (number >= 10000) {
      return `${(number / 10000).toFixed(1)}万${unit}`;
    } else if (number >= 1000) {
      return `${(number / 1000).toFixed(1)}k${unit}`;
    } else {
      return `${number}${unit}`;
    }
  }

  /**
   * 格式化百分比显示
   * @param {number} value - 数值
   * @param {number} total - 总数
   * @returns {string} 格式化后的百分比
   */
  static formatPercentage(value, total) {
    if (!value || !total || total === 0) return '0%';
    const percentage = (value / total * 100).toFixed(1);
    return `${percentage}%`;
  }

  /**
   * 自动检测并格式化数据
   * @param {any} value - 值
   * @param {string} type - 数据类型提示
   * @param {object} options - 格式化选项
   * @returns {string} 格式化后的值
   */
  static autoFormat(value, type = 'auto', options = {}) {
    if (value === null || value === undefined || value === '') {
      return '-';
    }

    switch (type) {
      case 'price':
        return this.formatPrice(value);
      case 'commission':
        return this.formatCommission(value, options.price);
      case 'duration':
        return this.formatDuration(value);
      case 'status':
        return this.formatStatus(value);
      case 'date':
        return this.formatDate(value);
      case 'phone':
        return this.formatPhone(value);
      case 'rating':
        return this.formatRating(value, options.maxRating);
      case 'text':
        return this.formatText(value, options.maxLength);
      case 'number':
        return this.formatNumber(value, options.unit);
      case 'percentage':
        return this.formatPercentage(value, options.total);
      case 'auto':
      default:
        // 自动检测数据类型
        if (typeof value === 'number') {
          if (value > 1000000000) return this.formatDate(value); // 时间戳
          if (value < 1) return this.formatPercentage(value * 100, 100); // 小数当百分比
          return this.formatNumber(value);
        }
        if (typeof value === 'string') {
          if (/^\d{11}$/.test(value)) return this.formatPhone(value); // 手机号
          if (/^\d{4}-\d{2}-\d{2}/.test(value)) return this.formatDate(value); // 日期
          return this.formatText(value);
        }
        return value.toString();
    }
  }
}

export default TableFormatter;
