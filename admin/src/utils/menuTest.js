/**
 * 菜单切换逻辑专项测试工具
 * 基于20年经验的专业菜单测试框架
 */

import { message } from 'ant-design-vue'

class MenuTester {
  constructor() {
    this.testResults = []
    this.errors = []
    this.router = null
    this.currentRoute = null
  }

  // 初始化测试环境
  async init() {
    try {
      const { useRouter, useRoute } = await import('vue-router')
      this.router = useRouter()
      this.currentRoute = useRoute()
      console.log('🔧 菜单测试环境初始化完成')
      return true
    } catch (error) {
      console.error('❌ 菜单测试环境初始化失败:', error)
      return false
    }
  }

  // 开始全面菜单测试
  async runMenuTests() {
    console.log('🧪 开始菜单切换逻辑测试...')
    message.info('开始菜单测试，请查看控制台输出')

    if (!(await this.init())) {
      return
    }

    try {
      await this.testMenuStructure()
      await this.testMenuNavigation()
      await this.testMenuStates()
      await this.testSubMenus()
      await this.testBreadcrumbs()
      
      this.generateMenuTestReport()
    } catch (error) {
      console.error('❌ 菜单测试过程中发生错误:', error)
      this.errors.push(`菜单测试执行错误: ${error.message}`)
    }
  }

  // 测试菜单结构
  async testMenuStructure() {
    console.log('📋 测试菜单结构...')
    
    const expectedMenuItems = [
      { key: 'dashboard', name: '仪表盘', route: '/dashboard' },
      { key: 'appointment-list', name: '预约列表', route: '/appointments' },
      { key: 'appointment-calendar', name: '预约日历', route: '/appointment-calendar' },
      { key: 'customers', name: '客户管理', route: '/customers' },
      { key: 'therapists', name: '技师管理', route: '/therapists' },
      { key: 'services', name: '服务管理', route: '/services' },
      { key: 'finance-overview', name: '财务概览', route: '/finance' },
      { key: 'finance-records', name: '收支记录', route: '/finance/records' },
      { key: 'finance-reports', name: '财务报表', route: '/finance/reports' },
      { key: 'system-settings', name: '系统设置', route: '/system/settings' },
      { key: 'system-logs', name: '操作日志', route: '/system/logs' }
    ]

    for (const menuItem of expectedMenuItems) {
      try {
        // 检查路由是否存在
        const routeExists = this.router.hasRoute(menuItem.route.slice(1))
        
        if (routeExists) {
          console.log(`✅ 菜单项 "${menuItem.name}" 路由配置正确`)
          this.testResults.push({
            type: 'Structure',
            item: menuItem.name,
            status: 'success'
          })
        } else {
          console.error(`❌ 菜单项 "${menuItem.name}" 路由不存在: ${menuItem.route}`)
          this.errors.push(`菜单项 "${menuItem.name}" 路由不存在`)
        }
      } catch (error) {
        console.error(`❌ 菜单项 "${menuItem.name}" 结构测试失败:`, error)
        this.errors.push(`菜单项 "${menuItem.name}" 结构测试失败`)
      }
    }
  }

  // 测试菜单导航
  async testMenuNavigation() {
    console.log('🧭 测试菜单导航...')
    
    const navigationTests = [
      { route: '/dashboard', name: '仪表盘' },
      { route: '/appointments', name: '预约列表' },
      { route: '/customers', name: '客户管理' },
      { route: '/therapists', name: '技师管理' },
      { route: '/services', name: '服务管理' },
      { route: '/finance', name: '财务概览' }
    ]

    for (const test of navigationTests) {
      try {
        console.log(`🔄 测试导航到: ${test.name} (${test.route})`)
        
        // 模拟导航
        await this.router.push(test.route)
        
        // 等待路由更新
        await new Promise(resolve => setTimeout(resolve, 100))
        
        // 检查当前路由
        const currentPath = this.router.currentRoute.value.path
        
        if (currentPath === test.route) {
          console.log(`✅ 导航到 "${test.name}" 成功`)
          this.testResults.push({
            type: 'Navigation',
            item: test.name,
            status: 'success'
          })
        } else {
          console.error(`❌ 导航到 "${test.name}" 失败，当前路径: ${currentPath}`)
          this.errors.push(`导航到 "${test.name}" 失败`)
        }
      } catch (error) {
        console.error(`❌ 导航测试失败 "${test.name}":`, error)
        this.errors.push(`导航测试失败 "${test.name}": ${error.message}`)
      }
    }
  }

  // 测试菜单状态
  async testMenuStates() {
    console.log('🎯 测试菜单状态...')
    
    try {
      // 测试菜单选中状态
      const menuElement = document.querySelector('.ant-menu')
      if (menuElement) {
        console.log('✅ 菜单DOM元素存在')
        
        // 检查选中项
        const selectedItems = document.querySelectorAll('.ant-menu-item-selected')
        console.log(`📍 当前选中菜单项数量: ${selectedItems.length}`)
        
        if (selectedItems.length > 0) {
          selectedItems.forEach((item, index) => {
            console.log(`📍 选中项 ${index + 1}: ${item.textContent?.trim()}`)
          })
        }
        
        this.testResults.push({
          type: 'State',
          item: '菜单状态',
          status: 'success'
        })
      } else {
        console.error('❌ 菜单DOM元素不存在')
        this.errors.push('菜单DOM元素不存在')
      }
    } catch (error) {
      console.error('❌ 菜单状态测试失败:', error)
      this.errors.push(`菜单状态测试失败: ${error.message}`)
    }
  }

  // 测试子菜单
  async testSubMenus() {
    console.log('📂 测试子菜单...')
    
    const subMenuTests = [
      {
        parent: 'appointment',
        children: ['appointment-list', 'appointment-calendar'],
        name: '预约管理'
      },
      {
        parent: 'finance',
        children: ['finance-overview', 'finance-records', 'finance-reports'],
        name: '财务管理'
      },
      {
        parent: 'system',
        children: ['system-settings', 'system-logs'],
        name: '系统管理'
      }
    ]

    for (const subMenu of subMenuTests) {
      try {
        console.log(`📂 测试子菜单: ${subMenu.name}`)
        
        // 检查子菜单DOM结构
        const subMenuElement = document.querySelector(`[data-menu-id="${subMenu.parent}"]`)
        
        if (subMenuElement) {
          console.log(`✅ 子菜单 "${subMenu.name}" DOM结构存在`)
        } else {
          console.warn(`⚠️ 子菜单 "${subMenu.name}" DOM结构未找到`)
        }
        
        // 测试子菜单项导航
        for (const childKey of subMenu.children) {
          const routeMap = {
            'appointment-list': '/appointments',
            'appointment-calendar': '/appointment-calendar',
            'finance-overview': '/finance',
            'finance-records': '/finance/records',
            'finance-reports': '/finance/reports',
            'system-settings': '/system/settings',
            'system-logs': '/system/logs'
          }
          
          const route = routeMap[childKey]
          if (route && this.router.hasRoute(route.slice(1))) {
            console.log(`✅ 子菜单项 "${childKey}" 路由配置正确`)
          } else {
            console.error(`❌ 子菜单项 "${childKey}" 路由配置错误`)
            this.errors.push(`子菜单项 "${childKey}" 路由配置错误`)
          }
        }
        
        this.testResults.push({
          type: 'SubMenu',
          item: subMenu.name,
          status: 'success'
        })
      } catch (error) {
        console.error(`❌ 子菜单测试失败 "${subMenu.name}":`, error)
        this.errors.push(`子菜单测试失败 "${subMenu.name}": ${error.message}`)
      }
    }
  }

  // 测试面包屑导航
  async testBreadcrumbs() {
    console.log('🍞 测试面包屑导航...')
    
    try {
      const breadcrumbElement = document.querySelector('.ant-breadcrumb')
      
      if (breadcrumbElement) {
        console.log('✅ 面包屑组件存在')
        
        const breadcrumbItems = document.querySelectorAll('.ant-breadcrumb-link')
        console.log(`📍 面包屑项目数量: ${breadcrumbItems.length}`)
        
        breadcrumbItems.forEach((item, index) => {
          console.log(`📍 面包屑 ${index + 1}: ${item.textContent?.trim()}`)
        })
        
        this.testResults.push({
          type: 'Breadcrumb',
          item: '面包屑导航',
          status: 'success'
        })
      } else {
        console.warn('⚠️ 面包屑组件不存在')
        this.errors.push('面包屑组件不存在')
      }
    } catch (error) {
      console.error('❌ 面包屑测试失败:', error)
      this.errors.push(`面包屑测试失败: ${error.message}`)
    }
  }

  // 模拟菜单点击
  async simulateMenuClick(menuKey) {
    console.log(`🖱️ 模拟点击菜单: ${menuKey}`)
    
    try {
      // 查找菜单项
      const menuItem = document.querySelector(`[data-menu-id="${menuKey}"]`)
      
      if (menuItem) {
        // 模拟点击事件
        const clickEvent = new MouseEvent('click', {
          bubbles: true,
          cancelable: true,
          view: window
        })
        
        menuItem.dispatchEvent(clickEvent)
        
        // 等待导航完成
        await new Promise(resolve => setTimeout(resolve, 200))
        
        console.log(`✅ 菜单点击模拟完成: ${menuKey}`)
        return true
      } else {
        console.error(`❌ 菜单项不存在: ${menuKey}`)
        return false
      }
    } catch (error) {
      console.error(`❌ 菜单点击模拟失败: ${menuKey}`, error)
      return false
    }
  }

  // 生成菜单测试报告
  generateMenuTestReport() {
    console.log('\n📋 ===== 菜单测试报告 =====')
    console.log(`✅ 成功: ${this.testResults.length} 项`)
    console.log(`❌ 错误: ${this.errors.length} 项`)
    
    if (this.errors.length > 0) {
      console.log('\n❌ 错误详情:')
      this.errors.forEach(error => console.log(`  - ${error}`))
    }
    
    console.log('\n📊 测试结果详情:')
    const groupedResults = this.testResults.reduce((acc, result) => {
      if (!acc[result.type]) acc[result.type] = []
      acc[result.type].push(result)
      return acc
    }, {})
    
    Object.entries(groupedResults).forEach(([type, results]) => {
      console.log(`  ${type}: ${results.length} 项通过`)
    })
    
    const overallStatus = this.errors.length === 0 ? '✅ 通过' : '❌ 失败'
    console.log(`\n🎯 菜单测试状态: ${overallStatus}`)
    console.log('========================\n')
    
    // 显示用户友好的消息
    if (this.errors.length === 0) {
      message.success(`菜单测试完成！所有 ${this.testResults.length} 项测试通过`)
    } else {
      message.error(`菜单测试完成，发现 ${this.errors.length} 个问题，请查看控制台`)
    }
  }
}

// 导出菜单测试工具
export const menuTester = new MenuTester()

// 在开发环境下自动暴露到全局
if (process.env.NODE_ENV === 'development') {
  window.menuTester = menuTester
  console.log('🔧 菜单测试工具已加载，使用 window.menuTester.runMenuTests() 开始测试')
}
