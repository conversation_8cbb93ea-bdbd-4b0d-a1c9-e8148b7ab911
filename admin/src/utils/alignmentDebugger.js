/**
 * 🎯 精确对齐调试工具
 * 专门用于检查表单数据和菜单的对齐细节
 * 基于轮廓测试方法的精确对齐分析
 */

class AlignmentDebugger {
  constructor() {
    this.isActive = false
    this.gridOverlay = null
    this.measurementLines = []
    this.isDevelopment = process.env.NODE_ENV === 'development'
    
    if (this.isDevelopment) {
      this.init()
    }
  }

  init() {
    this.addDebugStyles()
    this.addKeyboardShortcuts()
    this.addConsoleCommands()
    console.log('🎯 精确对齐调试工具已初始化')
  }

  // 添加调试样式
  addDebugStyles() {
    const style = document.createElement('style')
    style.id = 'alignment-debug-styles'
    style.textContent = `
      /* 🎯 对齐调试样式 */
      .alignment-debug-active * {
        outline: 1px solid rgba(255, 0, 0, 0.3) !important;
        outline-offset: -1px !important;
      }
      
      .alignment-grid-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100vw;
        height: 100vh;
        pointer-events: none;
        z-index: 9999;
        background-image: 
          linear-gradient(to right, rgba(255, 0, 0, 0.1) 1px, transparent 1px),
          linear-gradient(to bottom, rgba(255, 0, 0, 0.1) 1px, transparent 1px);
        background-size: 10px 10px;
      }
      
      .alignment-measurement-line {
        position: fixed;
        background: rgba(255, 0, 0, 0.8);
        pointer-events: none;
        z-index: 10000;
      }
      
      .alignment-measurement-horizontal {
        height: 1px;
        width: 100vw;
        left: 0;
      }
      
      .alignment-measurement-vertical {
        width: 1px;
        height: 100vh;
        top: 0;
      }
      
      .alignment-highlight {
        outline: 3px solid #ff0000 !important;
        outline-offset: -2px !important;
        background: rgba(255, 0, 0, 0.1) !important;
      }
      
      .alignment-menu-item {
        outline: 2px solid #00ff00 !important;
        outline-offset: -1px !important;
      }
      
      .alignment-table-row {
        outline: 2px solid #0066ff !important;
        outline-offset: -1px !important;
      }
      
      .alignment-info-panel {
        position: fixed;
        top: 10px;
        right: 10px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 15px;
        border-radius: 8px;
        font-family: monospace;
        font-size: 12px;
        z-index: 10001;
        max-width: 300px;
      }
    `
    document.head.appendChild(style)
  }

  // 添加键盘快捷键
  addKeyboardShortcuts() {
    document.addEventListener('keydown', (e) => {
      // Alt + A: 切换对齐调试
      if (e.altKey && e.key === 'a') {
        e.preventDefault()
        this.toggleAlignment()
      }
      
      // Alt + G: 切换网格覆盖
      if (e.altKey && e.key === 'g') {
        e.preventDefault()
        this.toggleGrid()
      }
      
      // Alt + M: 测量菜单和表格对齐
      if (e.altKey && e.key === 'm') {
        e.preventDefault()
        this.measureMenuTableAlignment()
      }
    })
  }

  // 添加控制台命令
  addConsoleCommands() {
    window.alignmentDebugger = this
    window.checkAlignment = () => this.checkAlignment()
    window.measureAlignment = () => this.measureMenuTableAlignment()
    window.fixAlignment = () => this.suggestAlignmentFixes()
  }

  // 切换对齐调试模式
  toggleAlignment() {
    this.isActive = !this.isActive
    
    if (this.isActive) {
      document.body.classList.add('alignment-debug-active')
      this.showInfoPanel()
      console.log('🎯 对齐调试模式已启用')
    } else {
      document.body.classList.remove('alignment-debug-active')
      this.hideInfoPanel()
      console.log('🎯 对齐调试模式已关闭')
    }
  }

  // 切换网格覆盖
  toggleGrid() {
    if (this.gridOverlay) {
      this.gridOverlay.remove()
      this.gridOverlay = null
      console.log('🎯 网格覆盖已关闭')
    } else {
      this.gridOverlay = document.createElement('div')
      this.gridOverlay.className = 'alignment-grid-overlay'
      document.body.appendChild(this.gridOverlay)
      console.log('🎯 网格覆盖已启用')
    }
  }

  // 测量菜单和表格对齐
  measureMenuTableAlignment() {
    console.log('🎯 开始测量菜单和表格对齐...')
    
    // 清除之前的测量线
    this.clearMeasurementLines()
    
    // 获取菜单项
    const menuItems = document.querySelectorAll('.ant-menu-item')
    // 获取表格行
    const tableRows = document.querySelectorAll('.ant-table-tbody tr')
    
    if (menuItems.length === 0) {
      console.warn('⚠️ 未找到菜单项')
      return
    }
    
    if (tableRows.length === 0) {
      console.warn('⚠️ 未找到表格行')
      return
    }

    // 高亮显示元素
    menuItems.forEach(item => item.classList.add('alignment-menu-item'))
    tableRows.forEach(row => row.classList.add('alignment-table-row'))
    
    // 分析对齐情况
    this.analyzeAlignment(menuItems, tableRows)
    
    // 5秒后清除高亮
    setTimeout(() => {
      menuItems.forEach(item => item.classList.remove('alignment-menu-item'))
      tableRows.forEach(row => row.classList.remove('alignment-table-row'))
    }, 5000)
  }

  // 分析对齐情况
  analyzeAlignment(menuItems, tableRows) {
    const menuData = Array.from(menuItems).slice(0, 8).map((item, index) => {
      const rect = item.getBoundingClientRect()
      return {
        index,
        element: item,
        top: rect.top,
        height: rect.height,
        centerY: rect.top + rect.height / 2
      }
    })

    const tableData = Array.from(tableRows).slice(0, 8).map((row, index) => {
      const rect = row.getBoundingClientRect()
      return {
        index,
        element: row,
        top: rect.top,
        height: rect.height,
        centerY: rect.top + rect.height / 2
      }
    })

    console.log('📊 菜单项数据:', menuData)
    console.log('📊 表格行数据:', tableData)

    // 计算对齐偏差
    const alignmentAnalysis = this.calculateAlignmentDeviation(menuData, tableData)
    console.log('📐 对齐分析结果:', alignmentAnalysis)

    // 显示测量线
    this.showMeasurementLines(menuData, tableData)
    
    return alignmentAnalysis
  }

  // 计算对齐偏差
  calculateAlignmentDeviation(menuData, tableData) {
    const deviations = []
    const minLength = Math.min(menuData.length, tableData.length)

    for (let i = 0; i < minLength; i++) {
      const menuItem = menuData[i]
      const tableRow = tableData[i]
      
      const deviation = {
        index: i,
        menuCenterY: menuItem.centerY,
        tableCenterY: tableRow.centerY,
        verticalDeviation: Math.abs(menuItem.centerY - tableRow.centerY),
        menuHeight: menuItem.height,
        tableHeight: tableRow.height,
        heightDifference: Math.abs(menuItem.height - tableRow.height)
      }
      
      deviations.push(deviation)
    }

    const avgDeviation = deviations.reduce((sum, d) => sum + d.verticalDeviation, 0) / deviations.length
    const maxDeviation = Math.max(...deviations.map(d => d.verticalDeviation))

    return {
      deviations,
      averageDeviation: avgDeviation,
      maxDeviation,
      isWellAligned: avgDeviation < 5 && maxDeviation < 10,
      recommendation: this.getAlignmentRecommendation(avgDeviation, maxDeviation)
    }
  }

  // 获取对齐建议
  getAlignmentRecommendation(avgDeviation, maxDeviation) {
    if (avgDeviation < 2 && maxDeviation < 5) {
      return '✅ 对齐良好，无需调整'
    } else if (avgDeviation < 5 && maxDeviation < 10) {
      return '⚠️ 轻微偏差，建议微调'
    } else {
      return '❌ 对齐偏差较大，需要重新调整'
    }
  }

  // 显示测量线
  showMeasurementLines(menuData, tableData) {
    const minLength = Math.min(menuData.length, tableData.length)
    
    for (let i = 0; i < minLength; i++) {
      const menuY = menuData[i].centerY
      const tableY = tableData[i].centerY
      
      // 菜单中心线
      const menuLine = document.createElement('div')
      menuLine.className = 'alignment-measurement-line alignment-measurement-horizontal'
      menuLine.style.top = `${menuY}px`
      menuLine.style.background = 'rgba(0, 255, 0, 0.8)'
      document.body.appendChild(menuLine)
      this.measurementLines.push(menuLine)
      
      // 表格中心线
      const tableLine = document.createElement('div')
      tableLine.className = 'alignment-measurement-line alignment-measurement-horizontal'
      tableLine.style.top = `${tableY}px`
      tableLine.style.background = 'rgba(0, 0, 255, 0.8)'
      document.body.appendChild(tableLine)
      this.measurementLines.push(tableLine)
    }
    
    // 5秒后清除测量线
    setTimeout(() => {
      this.clearMeasurementLines()
    }, 5000)
  }

  // 清除测量线
  clearMeasurementLines() {
    this.measurementLines.forEach(line => line.remove())
    this.measurementLines = []
  }

  // 显示信息面板
  showInfoPanel() {
    const panel = document.createElement('div')
    panel.className = 'alignment-info-panel'
    panel.innerHTML = `
      <div><strong>🎯 对齐调试工具</strong></div>
      <div>Alt + A: 切换调试模式</div>
      <div>Alt + G: 切换网格覆盖</div>
      <div>Alt + M: 测量对齐</div>
      <div>---</div>
      <div>绿色: 菜单项</div>
      <div>蓝色: 表格行</div>
      <div>红色: 对齐偏差</div>
    `
    document.body.appendChild(panel)
    this.infoPanel = panel
  }

  // 隐藏信息面板
  hideInfoPanel() {
    if (this.infoPanel) {
      this.infoPanel.remove()
      this.infoPanel = null
    }
  }

  // 建议对齐修复方案
  suggestAlignmentFixes() {
    console.log('🔧 生成对齐修复建议...')
    
    const menuItems = document.querySelectorAll('.ant-menu-item')
    const tableRows = document.querySelectorAll('.ant-table-tbody tr')
    
    if (menuItems.length === 0 || tableRows.length === 0) {
      console.warn('⚠️ 未找到菜单项或表格行')
      return
    }

    const analysis = this.analyzeAlignment(
      Array.from(menuItems).slice(0, 8),
      Array.from(tableRows).slice(0, 8)
    )

    const fixes = []
    
    if (analysis.averageDeviation > 5) {
      fixes.push({
        type: 'CSS调整',
        target: '菜单项高度',
        suggestion: '调整 .ant-menu-item 的 height 和 line-height',
        code: `
.ant-menu-item {
  height: 60px !important;
  line-height: 60px !important;
}
        `
      })
    }

    if (analysis.maxDeviation > 10) {
      fixes.push({
        type: 'CSS调整',
        target: '表格行高度',
        suggestion: '调整表格行的高度以匹配菜单项',
        code: `
.ant-table-tbody tr {
  height: 60px !important;
}
        `
      })
    }

    console.log('🔧 修复建议:', fixes)
    return fixes
  }
}

// 创建全局实例
export const alignmentDebugger = new AlignmentDebugger()

// 挂载到window对象供调试使用
if (typeof window !== 'undefined') {
  window.alignmentDebugger = alignmentDebugger
}

export default AlignmentDebugger
