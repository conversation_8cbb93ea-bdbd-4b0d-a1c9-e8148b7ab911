/**
 * 🎯 强制对齐修复工具
 * 针对5.14px偏差的精确修复
 * 使用更强的CSS优先级和精确的数值调整
 */

class ForceAlignmentFixer {
  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development'
    this.currentOffset = 0
    
    if (this.isDevelopment) {
      this.init()
    }
  }

  init() {
    this.addConsoleCommands()
    console.log('💪 强制对齐修复工具已初始化')
  }

  addConsoleCommands() {
    window.forceFixAlignment = () => this.forceFixAlignment()
    window.adjustOffset = (offset) => this.adjustOffset(offset)
    window.resetForceAlignment = () => this.resetForceAlignment()
    window.testOffsets = () => this.testMultipleOffsets()
  }

  // 强制修复对齐
  async forceFixAlignment() {
    console.log('💪 开始强制对齐修复...')
    
    // 先测量当前偏差
    const measurements = await this.measureCurrentState()
    
    if (measurements.averageDeviation === 0) {
      console.log('✅ 当前已经完美对齐')
      return
    }
    
    console.log(`📏 检测到偏差: ${measurements.averageDeviation.toFixed(2)}px`)
    
    // 计算需要的偏移量
    const requiredOffset = -measurements.averageDeviation
    console.log(`🔧 计算出需要的偏移量: ${requiredOffset.toFixed(2)}px`)
    
    // 应用强制修复
    this.applyForceAlignment(requiredOffset)
    
    // 验证结果
    setTimeout(async () => {
      const newMeasurements = await this.measureCurrentState()
      console.log(`📊 修复后偏差: ${newMeasurements.averageDeviation.toFixed(2)}px`)
      
      if (newMeasurements.averageDeviation < 1) {
        console.log('🎉 强制对齐修复成功！')
      } else {
        console.log('⚠️ 需要进一步调整，尝试: adjustOffset(' + (-newMeasurements.averageDeviation).toFixed(1) + ')')
      }
    }, 500)
  }

  // 测量当前状态
  async measureCurrentState() {
    // 等待DOM稳定
    await new Promise(resolve => setTimeout(resolve, 100))
    
    const menuItems = document.querySelectorAll('.ant-menu-item')
    const dataRows = document.querySelectorAll('.data-row')
    
    if (menuItems.length === 0 || dataRows.length === 0) {
      return { averageDeviation: 0, deviations: [] }
    }
    
    const deviations = []
    const minLength = Math.min(menuItems.length, dataRows.length, 8)
    
    for (let i = 0; i < minLength; i++) {
      const menuRect = menuItems[i].getBoundingClientRect()
      const dataRect = dataRows[i].getBoundingClientRect()
      
      const menuCenterY = menuRect.top + menuRect.height / 2
      const dataCenterY = dataRect.top + dataRect.height / 2
      const deviation = dataCenterY - menuCenterY
      
      deviations.push({
        index: i,
        menuCenterY,
        dataCenterY,
        deviation
      })
    }
    
    const averageDeviation = deviations.reduce((sum, d) => sum + d.deviation, 0) / deviations.length
    
    return { averageDeviation, deviations }
  }

  // 应用强制对齐
  applyForceAlignment(offset) {
    console.log(`💪 应用强制偏移: ${offset.toFixed(2)}px`)
    
    this.currentOffset = offset
    
    // 移除之前的样式
    const existingStyle = document.getElementById('force-alignment-fix')
    if (existingStyle) {
      existingStyle.remove()
    }
    
    // 创建新的强制样式
    const style = document.createElement('style')
    style.id = 'force-alignment-fix'
    style.textContent = `
      /* 💪 强制对齐修复 - 最高优先级 */
      .data-row {
        transform: translateY(${offset}px) !important;
        height: 50px !important;
        max-height: 50px !important;
        min-height: 50px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
        box-sizing: border-box !important;
      }
      
      .data-row .data-cell {
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
        padding: 8px 12px !important;
        line-height: 1.2 !important;
        vertical-align: middle !important;
      }
      
      /* 确保菜单项也是标准高度 */
      .ant-menu-item {
        height: 50px !important;
        line-height: 50px !important;
        display: flex !important;
        align-items: center !important;
        justify-content: flex-start !important;
      }
    `
    
    document.head.appendChild(style)
    console.log('✅ 强制样式已应用')
  }

  // 手动调整偏移量
  adjustOffset(additionalOffset) {
    const newOffset = this.currentOffset + additionalOffset
    console.log(`🔧 调整偏移量: ${this.currentOffset.toFixed(2)}px → ${newOffset.toFixed(2)}px`)
    this.applyForceAlignment(newOffset)
    
    // 验证结果
    setTimeout(async () => {
      const measurements = await this.measureCurrentState()
      console.log(`📊 调整后偏差: ${measurements.averageDeviation.toFixed(2)}px`)
    }, 300)
  }

  // 重置强制对齐
  resetForceAlignment() {
    const style = document.getElementById('force-alignment-fix')
    if (style) {
      style.remove()
      this.currentOffset = 0
      console.log('🔄 强制对齐已重置')
    }
  }

  // 测试多个偏移量
  async testMultipleOffsets() {
    console.log('🧪 测试多个偏移量...')
    
    const initialMeasurement = await this.measureCurrentState()
    const baseDeviation = initialMeasurement.averageDeviation
    
    console.log(`📏 基础偏差: ${baseDeviation.toFixed(2)}px`)
    
    const testOffsets = [-8, -6, -4, -2, 0, 2, 4, 6, 8]
    const results = []
    
    for (const offset of testOffsets) {
      this.applyForceAlignment(offset)
      await new Promise(resolve => setTimeout(resolve, 200))
      
      const measurement = await this.measureCurrentState()
      results.push({
        offset,
        deviation: measurement.averageDeviation,
        absDeviation: Math.abs(measurement.averageDeviation)
      })
      
      console.log(`📊 偏移${offset}px: 偏差${measurement.averageDeviation.toFixed(2)}px`)
    }
    
    // 找到最佳偏移量
    const bestResult = results.reduce((best, current) => 
      current.absDeviation < best.absDeviation ? current : best
    )
    
    console.log(`🎯 最佳偏移量: ${bestResult.offset}px (偏差: ${bestResult.deviation.toFixed(2)}px)`)
    
    // 应用最佳偏移量
    this.applyForceAlignment(bestResult.offset)
    
    return bestResult
  }

  // 智能对齐修复
  async smartAlignment() {
    console.log('🤖 开始智能对齐修复...')
    
    // 1. 测量当前状态
    const initial = await this.measureCurrentState()
    console.log(`📏 初始偏差: ${initial.averageDeviation.toFixed(2)}px`)
    
    if (Math.abs(initial.averageDeviation) < 1) {
      console.log('✅ 已经很好对齐了')
      return
    }
    
    // 2. 计算理论最佳偏移量
    let targetOffset = -initial.averageDeviation
    
    // 3. 应用并微调
    this.applyForceAlignment(targetOffset)
    await new Promise(resolve => setTimeout(resolve, 300))
    
    let currentMeasurement = await this.measureCurrentState()
    let iterations = 0
    const maxIterations = 5
    
    while (Math.abs(currentMeasurement.averageDeviation) > 0.5 && iterations < maxIterations) {
      const adjustment = -currentMeasurement.averageDeviation * 0.8
      targetOffset += adjustment
      
      console.log(`🔧 第${iterations + 1}次微调: ${adjustment.toFixed(2)}px`)
      
      this.applyForceAlignment(targetOffset)
      await new Promise(resolve => setTimeout(resolve, 200))
      
      currentMeasurement = await this.measureCurrentState()
      iterations++
    }
    
    console.log(`🎉 智能修复完成! 最终偏差: ${currentMeasurement.averageDeviation.toFixed(2)}px`)
    console.log(`🎯 使用的偏移量: ${targetOffset.toFixed(2)}px`)
    
    return {
      finalOffset: targetOffset,
      finalDeviation: currentMeasurement.averageDeviation,
      iterations
    }
  }
}

// 创建全局实例
export const forceAlignmentFixer = new ForceAlignmentFixer()

// 挂载到window对象供调试使用
if (typeof window !== 'undefined') {
  window.forceAlignmentFixer = forceAlignmentFixer
  window.smartAlignment = () => forceAlignmentFixer.smartAlignment()
}

export default ForceAlignmentFixer
