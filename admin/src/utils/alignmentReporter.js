/**
 * 🎯 对齐测试报告生成器
 * 生成详细的对齐测试报告和修复建议
 */

class AlignmentReporter {
  constructor() {
    this.reports = []
    this.isDevelopment = process.env.NODE_ENV === 'development'
    
    if (this.isDevelopment) {
      this.init()
    }
  }

  init() {
    this.addConsoleCommands()
    console.log('📊 对齐测试报告生成器已初始化')
  }

  addConsoleCommands() {
    window.generateAlignmentReport = () => this.generateDetailedReport()
    window.showAlignmentSummary = () => this.showSummary()
    window.exportAlignmentReport = () => this.exportReport()
  }

  // 生成详细报告
  async generateDetailedReport() {
    console.log('📊 生成详细对齐报告...')
    
    const report = {
      timestamp: new Date().toISOString(),
      url: window.location.href,
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      measurements: await this.collectMeasurements(),
      analysis: null,
      recommendations: [],
      score: 0
    }

    // 分析测量结果
    report.analysis = this.analyzeReport(report.measurements)
    
    // 生成建议
    report.recommendations = this.generateRecommendations(report.analysis)
    
    // 计算得分
    report.score = this.calculateScore(report.analysis)
    
    // 保存报告
    this.reports.push(report)
    
    // 显示报告
    this.displayReport(report)
    
    return report
  }

  // 收集测量数据
  async collectMeasurements() {
    const measurements = {
      menuItems: [],
      dataRows: [],
      selectors: {
        menuUsed: '.ant-menu-item',
        dataUsed: null
      }
    }

    // 收集菜单项数据
    const menuItems = document.querySelectorAll('.ant-menu-item')
    measurements.menuItems = Array.from(menuItems).slice(0, 8).map((item, index) => {
      const rect = item.getBoundingClientRect()
      const style = window.getComputedStyle(item)
      
      return {
        index,
        rect: {
          top: rect.top,
          height: rect.height,
          centerY: rect.top + rect.height / 2
        },
        style: {
          height: style.height,
          lineHeight: style.lineHeight,
          margin: style.margin,
          padding: style.padding
        }
      }
    })

    // 尝试多种选择器收集数据行
    const selectors = [
      '.ant-table-tbody tr:not([aria-hidden="true"])',
      '.data-row',
      '.service-row',
      '.table-row'
    ]

    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector)
      if (elements.length > 0) {
        measurements.selectors.dataUsed = selector
        measurements.dataRows = Array.from(elements).slice(0, 8).map((row, index) => {
          const rect = row.getBoundingClientRect()
          const style = window.getComputedStyle(row)
          
          return {
            index,
            rect: {
              top: rect.top,
              height: rect.height,
              centerY: rect.top + rect.height / 2
            },
            style: {
              height: style.height,
              lineHeight: style.lineHeight,
              margin: style.margin,
              padding: style.padding
            }
          }
        })
        break
      }
    }

    return measurements
  }

  // 分析报告
  analyzeReport(measurements) {
    const { menuItems, dataRows } = measurements
    
    if (dataRows.length === 0) {
      return {
        status: 'no-data',
        message: '未找到数据行，无法进行对齐分析',
        deviations: [],
        averageDeviation: 0,
        maxDeviation: 0
      }
    }

    const deviations = []
    const minLength = Math.min(menuItems.length, dataRows.length)

    for (let i = 0; i < minLength; i++) {
      const menu = menuItems[i]
      const data = dataRows[i]
      
      const deviation = Math.abs(menu.rect.centerY - data.rect.centerY)
      const heightDiff = Math.abs(menu.rect.height - data.rect.height)
      
      deviations.push({
        index: i,
        menuCenterY: menu.rect.centerY,
        dataCenterY: data.rect.centerY,
        verticalDeviation: deviation,
        menuHeight: menu.rect.height,
        dataHeight: data.rect.height,
        heightDifference: heightDiff,
        status: deviation < 3 ? 'excellent' : deviation < 5 ? 'good' : deviation < 10 ? 'fair' : 'poor'
      })
    }

    const totalDeviation = deviations.reduce((sum, d) => sum + d.verticalDeviation, 0)
    const averageDeviation = totalDeviation / deviations.length
    const maxDeviation = Math.max(...deviations.map(d => d.verticalDeviation))

    return {
      status: 'analyzed',
      deviations,
      averageDeviation,
      maxDeviation,
      alignmentQuality: this.getAlignmentQuality(averageDeviation, maxDeviation)
    }
  }

  // 获取对齐质量评级
  getAlignmentQuality(avgDeviation, maxDeviation) {
    if (avgDeviation < 1 && maxDeviation < 2) return 'perfect'
    if (avgDeviation < 3 && maxDeviation < 5) return 'excellent'
    if (avgDeviation < 5 && maxDeviation < 10) return 'good'
    if (avgDeviation < 10 && maxDeviation < 15) return 'fair'
    return 'poor'
  }

  // 生成建议
  generateRecommendations(analysis) {
    const recommendations = []

    if (analysis.status === 'no-data') {
      recommendations.push({
        type: 'navigation',
        priority: 'high',
        title: '导航到数据页面',
        description: '请导航到包含表格数据的页面进行对齐测试',
        action: 'navigateToTestPage()'
      })
      return recommendations
    }

    switch (analysis.alignmentQuality) {
      case 'perfect':
        recommendations.push({
          type: 'success',
          priority: 'info',
          title: '对齐完美',
          description: '当前对齐状态非常好，无需调整'
        })
        break

      case 'excellent':
        recommendations.push({
          type: 'success',
          priority: 'low',
          title: '对齐优秀',
          description: '对齐状态良好，可选择性进行微调'
        })
        break

      case 'good':
        recommendations.push({
          type: 'adjustment',
          priority: 'medium',
          title: '建议微调',
          description: '对齐基本良好，建议进行轻微调整',
          action: 'fixAlignment()'
        })
        break

      case 'fair':
        recommendations.push({
          type: 'fix',
          priority: 'high',
          title: '需要调整',
          description: '对齐偏差较明显，建议执行对齐修复',
          action: 'fixAlignment()'
        })
        break

      case 'poor':
        recommendations.push({
          type: 'major-fix',
          priority: 'critical',
          title: '需要重大调整',
          description: '对齐偏差很大，需要重新设计对齐方案',
          action: 'fixAlignment()'
        })
        break
    }

    return recommendations
  }

  // 计算得分
  calculateScore(analysis) {
    if (analysis.status === 'no-data') return 0

    const qualityScores = {
      perfect: 100,
      excellent: 90,
      good: 75,
      fair: 60,
      poor: 30
    }

    return qualityScores[analysis.alignmentQuality] || 0
  }

  // 显示报告
  displayReport(report) {
    console.log('\n📊 ===== 对齐测试详细报告 =====')
    console.log(`🕐 时间: ${new Date(report.timestamp).toLocaleString()}`)
    console.log(`🌐 页面: ${report.url}`)
    console.log(`📐 视口: ${report.viewport.width} x ${report.viewport.height}`)
    console.log(`📊 得分: ${report.score}/100`)
    
    if (report.analysis.status === 'no-data') {
      console.log('⚠️ 状态: 未找到数据行')
    } else {
      console.log(`📈 对齐质量: ${report.analysis.alignmentQuality}`)
      console.log(`📏 平均偏差: ${report.analysis.averageDeviation.toFixed(2)}px`)
      console.log(`📏 最大偏差: ${report.analysis.maxDeviation.toFixed(2)}px`)
      
      console.log('\n📋 详细偏差数据:')
      report.analysis.deviations.forEach((dev, i) => {
        const status = dev.status === 'excellent' ? '✅' : 
                     dev.status === 'good' ? '👍' : 
                     dev.status === 'fair' ? '⚠️' : '❌'
        console.log(`  第${i+1}行: ${dev.verticalDeviation.toFixed(1)}px ${status}`)
      })
    }
    
    console.log('\n💡 建议:')
    report.recommendations.forEach(rec => {
      const priority = rec.priority === 'critical' ? '🚨' :
                      rec.priority === 'high' ? '⚠️' :
                      rec.priority === 'medium' ? '💡' : 'ℹ️'
      console.log(`  ${priority} ${rec.title}: ${rec.description}`)
      if (rec.action) {
        console.log(`     执行: ${rec.action}`)
      }
    })
    
    console.log('\n===============================\n')
  }

  // 显示摘要
  showSummary() {
    if (this.reports.length === 0) {
      console.log('📊 暂无对齐测试报告，请先运行 generateAlignmentReport()')
      return
    }

    const latest = this.reports[this.reports.length - 1]
    console.log(`📊 最新对齐报告摘要 (得分: ${latest.score}/100)`)
    console.log(`📈 对齐质量: ${latest.analysis.alignmentQuality || '无数据'}`)
    
    if (latest.analysis.averageDeviation) {
      console.log(`📏 平均偏差: ${latest.analysis.averageDeviation.toFixed(2)}px`)
    }
  }

  // 导出报告
  exportReport() {
    if (this.reports.length === 0) {
      console.log('📊 暂无报告可导出')
      return
    }

    const reportData = JSON.stringify(this.reports, null, 2)
    console.log('📄 对齐测试报告数据:')
    console.log(reportData)
    
    // 如果在浏览器环境，可以下载文件
    if (typeof window !== 'undefined' && window.document) {
      const blob = new Blob([reportData], { type: 'application/json' })
      const url = URL.createObjectURL(blob)
      const a = document.createElement('a')
      a.href = url
      a.download = `alignment-report-${Date.now()}.json`
      a.click()
      URL.revokeObjectURL(url)
      console.log('📥 报告已下载')
    }
  }
}

// 创建全局实例
export const alignmentReporter = new AlignmentReporter()

// 挂载到window对象供调试使用
if (typeof window !== 'undefined') {
  window.alignmentReporter = alignmentReporter
}

export default AlignmentReporter
