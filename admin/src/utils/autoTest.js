/**
 * 自动化前端测试工具 - 基于20年经验的专业测试框架
 * 自动检测页面逻辑、API调用、组件状态等
 */

import { message } from 'ant-design-vue'
import { serviceAPI, therapistAPI, customerAPI, appointmentAPI, dashboardAPI } from '@/api'

class AutoTester {
  constructor() {
    this.testResults = []
    this.errors = []
    this.warnings = []
  }

  // 开始自动化测试
  async runAllTests() {
    console.log('🚀 开始自动化前端测试...')
    message.info('开始自动化测试，请查看控制台输出')

    try {
      await this.testAPIConnections()
      await this.testStoreOperations()
      await this.testRouterNavigation()
      await this.testComponentLogic()
      await this.testDataValidation()
      
      this.generateTestReport()
    } catch (error) {
      console.error('❌ 测试过程中发生错误:', error)
      this.errors.push(`测试执行错误: ${error.message}`)
    }
  }

  // 测试API连接
  async testAPIConnections() {
    console.log('📡 测试API连接...')
    
    const apiTests = [
      { name: '服务API', test: () => serviceAPI.getServices() },
      { name: '技师API', test: () => therapistAPI.getTherapists() },
      { name: '客户API', test: () => customerAPI.getCustomers() },
      { name: '预约API', test: () => appointmentAPI.getAppointments() },
      { name: '仪表盘API', test: () => dashboardAPI.getStats() }
    ]

    for (const apiTest of apiTests) {
      try {
        const startTime = Date.now()
        await apiTest.test()
        const duration = Date.now() - startTime
        
        console.log(`✅ ${apiTest.name} 连接成功 (${duration}ms)`)
        this.testResults.push({
          type: 'API',
          name: apiTest.name,
          status: 'success',
          duration
        })
      } catch (error) {
        console.error(`❌ ${apiTest.name} 连接失败:`, error.message)
        this.errors.push(`${apiTest.name} 连接失败: ${error.message}`)
      }
    }
  }

  // 测试Store状态管理
  async testStoreOperations() {
    console.log('🗄️ 测试Store状态管理...')
    
    try {
      // 动态导入store以避免循环依赖
      const { useUserStore, useServiceStore, useDashboardStore } = await import('@/store')
      
      // 测试用户Store
      const userStore = useUserStore()
      console.log('👤 用户Store状态:', {
        isLoggedIn: userStore.isLoggedIn,
        userName: userStore.userName
      })
      
      // 测试服务Store
      const serviceStore = useServiceStore()
      await serviceStore.fetchServices()
      console.log(`📋 服务Store: 加载了 ${serviceStore.services.length} 个服务`)
      
      // 测试仪表盘Store
      const dashboardStore = useDashboardStore()
      await dashboardStore.fetchStats()
      console.log('📊 仪表盘Store: 统计数据加载完成')
      
      this.testResults.push({
        type: 'Store',
        name: 'Pinia状态管理',
        status: 'success'
      })
    } catch (error) {
      console.error('❌ Store测试失败:', error)
      this.errors.push(`Store测试失败: ${error.message}`)
    }
  }

  // 测试路由导航
  async testRouterNavigation() {
    console.log('🧭 测试路由导航...')
    
    const routes = [
      '/dashboard',
      '/appointments',
      '/customers',
      '/therapists',
      '/services',
      '/finance'
    ]

    for (const route of routes) {
      try {
        // 检查路由是否存在
        const { useRouter } = await import('vue-router')
        const router = useRouter()
        
        if (router.hasRoute(route.slice(1))) {
          console.log(`✅ 路由 ${route} 配置正确`)
          this.testResults.push({
            type: 'Router',
            name: route,
            status: 'success'
          })
        } else {
          console.warn(`⚠️ 路由 ${route} 未找到`)
          this.warnings.push(`路由 ${route} 未找到`)
        }
      } catch (error) {
        console.error(`❌ 路由 ${route} 测试失败:`, error)
        this.errors.push(`路由 ${route} 测试失败: ${error.message}`)
      }
    }
  }

  // 测试组件逻辑
  async testComponentLogic() {
    console.log('🧩 测试组件逻辑...')
    
    // 测试表单验证
    this.testFormValidation()
    
    // 测试数据格式化
    this.testDataFormatting()
    
    // 测试事件处理
    this.testEventHandling()
  }

  // 测试表单验证
  testFormValidation() {
    console.log('📝 测试表单验证...')
    
    const validationTests = [
      {
        name: '手机号验证',
        test: (phone) => /^1[3-9]\d{9}$/.test(phone),
        validCases: ['13800138000', '15912345678'],
        invalidCases: ['123', '1380013800a', '']
      },
      {
        name: '邮箱验证',
        test: (email) => /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email),
        validCases: ['<EMAIL>', '<EMAIL>'],
        invalidCases: ['invalid-email', '@domain.com', 'user@']
      }
    ]

    validationTests.forEach(validation => {
      let passed = 0
      let total = validation.validCases.length + validation.invalidCases.length

      validation.validCases.forEach(testCase => {
        if (validation.test(testCase)) {
          passed++
        } else {
          console.warn(`⚠️ ${validation.name} 有效案例失败: ${testCase}`)
        }
      })

      validation.invalidCases.forEach(testCase => {
        if (!validation.test(testCase)) {
          passed++
        } else {
          console.warn(`⚠️ ${validation.name} 无效案例通过: ${testCase}`)
        }
      })

      if (passed === total) {
        console.log(`✅ ${validation.name} 验证通过`)
        this.testResults.push({
          type: 'Validation',
          name: validation.name,
          status: 'success'
        })
      } else {
        console.error(`❌ ${validation.name} 验证失败 (${passed}/${total})`)
        this.errors.push(`${validation.name} 验证失败`)
      }
    })
  }

  // 测试数据格式化
  testDataFormatting() {
    console.log('🎨 测试数据格式化...')
    
    try {
      // 测试日期格式化
      const testDate = new Date('2025-07-05T12:30:00')
      const formattedDate = testDate.toLocaleDateString('zh-CN')
      console.log(`✅ 日期格式化: ${formattedDate}`)
      
      // 测试金额格式化
      const testAmount = 1234.56
      const formattedAmount = `¥${testAmount.toFixed(2)}`
      console.log(`✅ 金额格式化: ${formattedAmount}`)
      
      this.testResults.push({
        type: 'Formatting',
        name: '数据格式化',
        status: 'success'
      })
    } catch (error) {
      console.error('❌ 数据格式化测试失败:', error)
      this.errors.push(`数据格式化失败: ${error.message}`)
    }
  }

  // 测试事件处理
  testEventHandling() {
    console.log('⚡ 测试事件处理...')
    
    try {
      // 模拟点击事件
      const mockClickEvent = new Event('click')
      console.log('✅ 点击事件创建成功')
      
      // 模拟表单提交事件
      const mockSubmitEvent = new Event('submit')
      console.log('✅ 提交事件创建成功')
      
      this.testResults.push({
        type: 'Events',
        name: '事件处理',
        status: 'success'
      })
    } catch (error) {
      console.error('❌ 事件处理测试失败:', error)
      this.errors.push(`事件处理失败: ${error.message}`)
    }
  }

  // 测试数据验证
  async testDataValidation() {
    console.log('🔍 测试数据验证...')
    
    // 测试必填字段验证
    const requiredFieldTests = [
      { field: 'name', value: '', shouldFail: true },
      { field: 'name', value: '张三', shouldFail: false },
      { field: 'phone', value: '', shouldFail: true },
      { field: 'phone', value: '13800138000', shouldFail: false }
    ]

    requiredFieldTests.forEach(test => {
      const isEmpty = !test.value || test.value.trim() === ''
      const failed = isEmpty
      
      if (failed === test.shouldFail) {
        console.log(`✅ 必填字段验证通过: ${test.field}`)
      } else {
        console.warn(`⚠️ 必填字段验证异常: ${test.field}`)
        this.warnings.push(`必填字段验证异常: ${test.field}`)
      }
    })
  }

  // 生成测试报告
  generateTestReport() {
    console.log('\n📋 ===== 自动化测试报告 =====')
    console.log(`✅ 成功: ${this.testResults.length} 项`)
    console.log(`⚠️ 警告: ${this.warnings.length} 项`)
    console.log(`❌ 错误: ${this.errors.length} 项`)
    
    if (this.warnings.length > 0) {
      console.log('\n⚠️ 警告详情:')
      this.warnings.forEach(warning => console.log(`  - ${warning}`))
    }
    
    if (this.errors.length > 0) {
      console.log('\n❌ 错误详情:')
      this.errors.forEach(error => console.log(`  - ${error}`))
    }
    
    console.log('\n📊 测试结果详情:')
    const groupedResults = this.testResults.reduce((acc, result) => {
      if (!acc[result.type]) acc[result.type] = []
      acc[result.type].push(result)
      return acc
    }, {})
    
    Object.entries(groupedResults).forEach(([type, results]) => {
      console.log(`  ${type}: ${results.length} 项通过`)
    })
    
    const overallStatus = this.errors.length === 0 ? '✅ 通过' : '❌ 失败'
    console.log(`\n🎯 总体状态: ${overallStatus}`)
    console.log('========================\n')
    
    // 显示用户友好的消息
    if (this.errors.length === 0) {
      message.success(`自动化测试完成！通过 ${this.testResults.length} 项测试`)
    } else {
      message.error(`测试完成，发现 ${this.errors.length} 个错误，请查看控制台`)
    }
  }
}

// 导出测试工具
export const autoTester = new AutoTester()

// 在开发环境下自动暴露到全局
if (process.env.NODE_ENV === 'development') {
  window.autoTester = autoTester
  console.log('🔧 自动化测试工具已加载，使用 window.autoTester.runAllTests() 开始测试')
}
