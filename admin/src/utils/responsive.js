/**
 * 响应式rem设置工具
 * 基于壹心堂管理系统的完美基准状态：1512×768
 * 实现真正的比例缩放，保持所有元素相对位置不变
 */

class ResponsiveManager {
  constructor() {
    // 完美基准状态的分辨率
    this.baseWidth = 1512;
    this.baseHeight = 768;
    
    // 基准rem值（16px）
    this.baseRem = 16;
    
    // 最小和最大缩放比例限制
    this.minScale = 0.5;
    this.maxScale = 2.0;
    
    this.init();
  }
  
  /**
   * 初始化响应式系统
   */
  init() {
    this.setRem();
    this.bindEvents();
    console.log('🎯 响应式rem系统已启动');
  }
  
  /**
   * 计算并设置rem值
   */
  setRem() {
    const { clientWidth, clientHeight } = document.documentElement;
    
    // 计算缩放比例（取较小值确保不超出屏幕）
    const scaleX = clientWidth / this.baseWidth;
    const scaleY = clientHeight / this.baseHeight;
    const scale = Math.min(scaleX, scaleY);
    
    // 限制缩放范围
    const finalScale = Math.max(this.minScale, Math.min(this.maxScale, scale));
    
    // 计算新的rem值
    const newRem = this.baseRem * finalScale;
    
    // 设置html的font-size
    document.documentElement.style.fontSize = `${newRem}px`;
    
    // 设置CSS变量供其他地方使用
    document.documentElement.style.setProperty('--responsive-scale', finalScale);
    document.documentElement.style.setProperty('--responsive-rem', `${newRem}px`);
    
    console.log(`📊 响应式缩放: ${clientWidth}×${clientHeight} → ${(finalScale * 100).toFixed(1)}% (${newRem.toFixed(2)}px)`);
    
    return {
      scale: finalScale,
      rem: newRem,
      viewport: { width: clientWidth, height: clientHeight }
    };
  }
  
  /**
   * 绑定窗口大小变化事件
   */
  bindEvents() {
    let resizeTimer = null;
    
    window.addEventListener('resize', () => {
      // 防抖处理，避免频繁计算
      if (resizeTimer) {
        clearTimeout(resizeTimer);
      }
      
      resizeTimer = setTimeout(() => {
        this.setRem();
      }, 100);
    });
    
    // 监听屏幕方向变化（移动设备）
    window.addEventListener('orientationchange', () => {
      setTimeout(() => {
        this.setRem();
      }, 300);
    });
  }
  
  /**
   * 获取当前缩放信息
   */
  getScaleInfo() {
    const { clientWidth, clientHeight } = document.documentElement;
    const scaleX = clientWidth / this.baseWidth;
    const scaleY = clientHeight / this.baseHeight;
    const scale = Math.min(scaleX, scaleY);
    const finalScale = Math.max(this.minScale, Math.min(this.maxScale, scale));
    
    return {
      viewport: { width: clientWidth, height: clientHeight },
      baseResolution: { width: this.baseWidth, height: this.baseHeight },
      scale: finalScale,
      scalePercentage: `${(finalScale * 100).toFixed(1)}%`,
      rem: this.baseRem * finalScale
    };
  }
  
  /**
   * 手动设置缩放比例（用于测试）
   */
  setScale(scale) {
    const finalScale = Math.max(this.minScale, Math.min(this.maxScale, scale));
    const newRem = this.baseRem * finalScale;
    
    document.documentElement.style.fontSize = `${newRem}px`;
    document.documentElement.style.setProperty('--responsive-scale', finalScale);
    document.documentElement.style.setProperty('--responsive-rem', `${newRem}px`);
    
    console.log(`🎯 手动设置缩放: ${(finalScale * 100).toFixed(1)}% (${newRem.toFixed(2)}px)`);
    
    return { scale: finalScale, rem: newRem };
  }
}

// 创建全局实例
const responsiveManager = new ResponsiveManager();

// 导出供其他模块使用
export default responsiveManager;

// 挂载到window对象供调试使用
if (typeof window !== 'undefined') {
  window.responsiveManager = responsiveManager;
}
