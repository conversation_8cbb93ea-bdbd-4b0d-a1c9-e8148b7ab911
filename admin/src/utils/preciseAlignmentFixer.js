/**
 * 🎯 精确对齐修复工具
 * 基于轮廓测试方法，精确调整表单数据和菜单的对齐
 * 实现像素级完美对齐
 */

class PreciseAlignmentFixer {
  constructor() {
    this.isDevelopment = process.env.NODE_ENV === 'development'
    this.measurements = {}
    this.fixApplied = false
    
    if (this.isDevelopment) {
      this.init()
    }
  }

  init() {
    this.addConsoleCommands()
    console.log('🎯 精确对齐修复工具已初始化')
    console.log('💡 使用 fixAlignment() 开始精确对齐修复')
  }

  addConsoleCommands() {
    window.fixAlignment = () => this.performPreciseAlignment()
    window.measureCurrentAlignment = () => this.measureCurrentAlignment()
    window.resetAlignment = () => this.resetAlignment()
    window.showAlignmentReport = () => this.showAlignmentReport()
    window.autoTestAlignment = () => this.autoTestAlignment()
    window.navigateToTestPage = () => this.navigateToTestPage()
  }

  // 执行精确对齐
  async performPreciseAlignment() {
    console.log('🎯 开始精确对齐修复...')
    
    // 1. 测量当前状态
    const measurements = await this.measureCurrentAlignment()
    
    // 2. 分析偏差
    const analysis = this.analyzeAlignment(measurements)
    
    // 3. 应用修复
    const fixes = this.generateFixes(analysis)
    this.applyFixes(fixes)
    
    // 4. 验证结果
    setTimeout(() => {
      this.verifyAlignment()
    }, 500)
    
    return {
      measurements,
      analysis,
      fixes
    }
  }

  // 测量当前对齐状态
  async measureCurrentAlignment() {
    console.log('📏 测量当前对齐状态...')
    
    // 等待DOM稳定
    await this.waitForDOM()
    
    const menuItems = document.querySelectorAll('.ant-menu-item')

    // 尝试多种表格行选择器
    let tableRows = document.querySelectorAll('.ant-table-tbody tr:not([aria-hidden="true"])')

    // 如果没有找到标准表格行，尝试自定义数据行
    if (tableRows.length === 0) {
      tableRows = document.querySelectorAll('.data-row')
      console.log('📋 使用自定义数据行选择器 .data-row')
    }

    // 如果还是没有找到，尝试其他可能的选择器
    if (tableRows.length === 0) {
      tableRows = document.querySelectorAll('.table-row, .service-row, [class*="row"]')
      console.log('📋 使用通用行选择器')
    }
    
    const menuMeasurements = Array.from(menuItems).slice(0, 8).map((item, index) => {
      const rect = item.getBoundingClientRect()
      const computedStyle = window.getComputedStyle(item)
      
      return {
        index,
        element: item,
        top: rect.top,
        height: rect.height,
        centerY: rect.top + rect.height / 2,
        computedHeight: parseInt(computedStyle.height),
        computedLineHeight: computedStyle.lineHeight,
        marginTop: parseInt(computedStyle.marginTop),
        marginBottom: parseInt(computedStyle.marginBottom),
        paddingTop: parseInt(computedStyle.paddingTop),
        paddingBottom: parseInt(computedStyle.paddingBottom)
      }
    })

    const tableMeasurements = Array.from(tableRows).slice(0, 8).map((row, index) => {
      const rect = row.getBoundingClientRect()
      const computedStyle = window.getComputedStyle(row)
      
      return {
        index,
        element: row,
        top: rect.top,
        height: rect.height,
        centerY: rect.top + rect.height / 2,
        computedHeight: parseInt(computedStyle.height),
        computedLineHeight: computedStyle.lineHeight,
        marginTop: parseInt(computedStyle.marginTop),
        marginBottom: parseInt(computedStyle.marginBottom),
        paddingTop: parseInt(computedStyle.paddingTop),
        paddingBottom: parseInt(computedStyle.paddingBottom)
      }
    })

    this.measurements = {
      menu: menuMeasurements,
      table: tableMeasurements,
      timestamp: Date.now()
    }

    console.log('📊 菜单测量结果:', menuMeasurements)
    console.log('📊 表格测量结果:', tableMeasurements)

    return this.measurements
  }

  // 分析对齐情况
  analyzeAlignment(measurements) {
    const { menu, table } = measurements
    const minLength = Math.min(menu.length, table.length)
    
    const deviations = []
    let totalDeviation = 0
    let maxDeviation = 0

    for (let i = 0; i < minLength; i++) {
      const menuItem = menu[i]
      const tableRow = table[i]
      
      const verticalDeviation = Math.abs(menuItem.centerY - tableRow.centerY)
      const heightDifference = Math.abs(menuItem.height - tableRow.height)
      
      deviations.push({
        index: i,
        menuCenterY: menuItem.centerY,
        tableCenterY: tableRow.centerY,
        verticalDeviation,
        menuHeight: menuItem.height,
        tableHeight: tableRow.height,
        heightDifference,
        menuComputedHeight: menuItem.computedHeight,
        tableComputedHeight: tableRow.computedHeight
      })
      
      totalDeviation += verticalDeviation
      maxDeviation = Math.max(maxDeviation, verticalDeviation)
    }

    const averageDeviation = totalDeviation / deviations.length
    
    const analysis = {
      deviations,
      averageDeviation,
      maxDeviation,
      isWellAligned: averageDeviation < 3 && maxDeviation < 5,
      needsMinorAdjustment: averageDeviation < 5 && maxDeviation < 10,
      needsMajorAdjustment: averageDeviation >= 5 || maxDeviation >= 10,
      recommendation: this.getRecommendation(averageDeviation, maxDeviation)
    }

    console.log('📐 对齐分析结果:', analysis)
    return analysis
  }

  // 获取修复建议
  getRecommendation(avgDeviation, maxDeviation) {
    if (avgDeviation < 1 && maxDeviation < 2) {
      return '✅ 对齐完美，无需调整'
    } else if (avgDeviation < 3 && maxDeviation < 5) {
      return '✅ 对齐良好，可选择性微调'
    } else if (avgDeviation < 5 && maxDeviation < 10) {
      return '⚠️ 轻微偏差，建议微调'
    } else {
      return '❌ 对齐偏差较大，需要重新调整'
    }
  }

  // 生成修复方案
  generateFixes(analysis) {
    const fixes = []
    
    if (analysis.needsMajorAdjustment) {
      // 主要修复：统一高度
      fixes.push({
        type: 'height-unification',
        target: 'table-rows',
        description: '统一表格行高度为50px',
        css: `
          .ant-table-tbody > tr,
          .data-row,
          .service-row {
            height: 50px !important;
            max-height: 50px !important;
            min-height: 50px !important;
          }
        `,
        priority: 'high'
      })

      fixes.push({
        type: 'padding-adjustment',
        target: 'table-cells',
        description: '调整表格单元格内边距',
        css: `
          .ant-table-tbody > tr > td,
          .data-row .data-cell,
          .service-row .data-cell {
            padding: 8px 12px !important;
            line-height: 1.2 !important;
            vertical-align: middle !important;
          }
        `,
        priority: 'high'
      })
    }
    
    if (analysis.needsMinorAdjustment) {
      // 微调修复：边距调整
      fixes.push({
        type: 'margin-adjustment',
        target: 'menu-items',
        description: '微调菜单项边距',
        css: `
          .ant-menu-item {
            margin: 8px 20px !important;
          }
        `,
        priority: 'medium'
      })
    }
    
    // 通用修复：确保垂直居中
    fixes.push({
      type: 'vertical-centering',
      target: 'all-elements',
      description: '确保所有元素垂直居中',
      css: `
        .ant-menu-item,
        .ant-table-tbody > tr > td,
        .data-row,
        .service-row {
          display: flex !important;
          align-items: center !important;
          justify-content: flex-start !important;
        }

        .data-row .data-cell,
        .service-row .data-cell {
          display: flex !important;
          align-items: center !important;
          justify-content: flex-start !important;
        }
      `,
      priority: 'medium'
    })

    console.log('🔧 生成的修复方案:', fixes)
    return fixes
  }

  // 应用修复
  applyFixes(fixes) {
    console.log('🔧 应用修复方案...')
    
    // 创建或更新样式标签
    let styleElement = document.getElementById('precise-alignment-fixes')
    if (!styleElement) {
      styleElement = document.createElement('style')
      styleElement.id = 'precise-alignment-fixes'
      document.head.appendChild(styleElement)
    }
    
    // 合并所有CSS修复
    const combinedCSS = fixes.map(fix => `
      /* ${fix.description} */
      ${fix.css}
    `).join('\n')
    
    styleElement.textContent = combinedCSS
    this.fixApplied = true
    
    console.log('✅ 修复已应用')
    console.log('📝 应用的CSS:', combinedCSS)
  }

  // 验证对齐结果
  async verifyAlignment() {
    console.log('🔍 验证对齐结果...')
    
    const newMeasurements = await this.measureCurrentAlignment()
    const newAnalysis = this.analyzeAlignment(newMeasurements)
    
    console.log('📊 修复后分析结果:', newAnalysis)
    
    if (newAnalysis.isWellAligned) {
      console.log('🎉 对齐修复成功！')
    } else if (newAnalysis.needsMinorAdjustment) {
      console.log('⚠️ 对齐有所改善，但仍需微调')
    } else {
      console.log('❌ 对齐修复效果不佳，需要重新分析')
    }
    
    return newAnalysis
  }

  // 重置对齐修复
  resetAlignment() {
    const styleElement = document.getElementById('precise-alignment-fixes')
    if (styleElement) {
      styleElement.remove()
      this.fixApplied = false
      console.log('🔄 对齐修复已重置')
    }
  }

  // 显示对齐报告
  showAlignmentReport() {
    if (!this.measurements.menu || !this.measurements.table) {
      console.log('⚠️ 请先运行 measureCurrentAlignment() 或 fixAlignment()')
      return
    }
    
    console.log('📋 详细对齐报告')
    console.log('=' .repeat(50))
    
    const { menu, table } = this.measurements
    const minLength = Math.min(menu.length, table.length)
    
    for (let i = 0; i < minLength; i++) {
      const menuItem = menu[i]
      const tableRow = table[i]
      const deviation = Math.abs(menuItem.centerY - tableRow.centerY)
      
      console.log(`第${i + 1}行:`)
      console.log(`  菜单项: 高度${menuItem.height}px, 中心Y=${menuItem.centerY.toFixed(1)}px`)
      console.log(`  表格行: 高度${tableRow.height}px, 中心Y=${tableRow.centerY.toFixed(1)}px`)
      console.log(`  偏差: ${deviation.toFixed(1)}px ${deviation < 3 ? '✅' : deviation < 5 ? '⚠️' : '❌'}`)
      console.log('')
    }
  }

  // 自动测试对齐
  async autoTestAlignment() {
    console.log('🤖 开始自动对齐测试...')

    // 检查当前页面是否有表格数据
    const hasTableData = await this.checkForTableData()

    if (!hasTableData) {
      console.log('⚠️ 当前页面没有表格数据，尝试导航到服务管理页面...')
      await this.navigateToTestPage()

      // 等待页面加载
      await this.waitForPageLoad()

      // 再次检查
      const hasTableDataAfterNav = await this.checkForTableData()
      if (!hasTableDataAfterNav) {
        console.log('❌ 导航后仍然没有找到表格数据')
        return false
      }
    }

    // 执行对齐测试
    console.log('✅ 找到表格数据，开始执行对齐测试...')
    return await this.performPreciseAlignment()
  }

  // 检查是否有表格数据
  async checkForTableData() {
    await this.waitForDOM()

    const selectors = [
      '.ant-table-tbody tr:not([aria-hidden="true"])',
      '.data-row',
      '.service-row',
      '.table-row',
      '[class*="row"]'
    ]

    for (const selector of selectors) {
      const elements = document.querySelectorAll(selector)
      if (elements.length > 0) {
        console.log(`📋 找到 ${elements.length} 个数据行 (选择器: ${selector})`)
        return true
      }
    }

    console.log('⚠️ 未找到任何表格数据行')
    return false
  }

  // 导航到测试页面
  async navigateToTestPage() {
    console.log('🧭 导航到服务管理页面...')

    // 尝试点击服务管理菜单项
    const serviceMenuItem = document.querySelector('.ant-menu-item[data-menu-id*="service"], .ant-menu-item:nth-child(5)')

    if (serviceMenuItem) {
      serviceMenuItem.click()
      console.log('✅ 已点击服务管理菜单项')
    } else {
      // 如果找不到菜单项，尝试直接修改URL
      if (window.location.pathname !== '/services') {
        window.location.hash = '#/services'
        console.log('✅ 已修改URL到服务管理页面')
      }
    }
  }

  // 等待页面加载
  waitForPageLoad() {
    return new Promise(resolve => {
      let attempts = 0
      const maxAttempts = 20

      const checkLoad = () => {
        attempts++

        if (this.checkForTableData() || attempts >= maxAttempts) {
          resolve()
        } else {
          setTimeout(checkLoad, 500)
        }
      }

      setTimeout(checkLoad, 1000)
    })
  }

  // 等待DOM稳定
  waitForDOM() {
    return new Promise(resolve => {
      if (document.readyState === 'complete') {
        setTimeout(resolve, 100)
      } else {
        window.addEventListener('load', () => {
          setTimeout(resolve, 100)
        })
      }
    })
  }
}

// 创建全局实例
export const preciseAlignmentFixer = new PreciseAlignmentFixer()

// 挂载到window对象供调试使用
if (typeof window !== 'undefined') {
  window.preciseAlignmentFixer = preciseAlignmentFixer
}

export default PreciseAlignmentFixer
