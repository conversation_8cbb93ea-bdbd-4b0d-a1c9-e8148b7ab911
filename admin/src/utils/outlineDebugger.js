/**
 * 轮廓调试工具 - 开发环境专用
 * 通过轮廓调试技术快速定位布局问题
 */

class OutlineDebugger {
  constructor() {
    this.isActive = false;
    this.debugElements = new Set();
    this.isDevelopment = process.env.NODE_ENV === 'development';
    
    // 只在开发环境初始化
    if (this.isDevelopment) {
      this.init();
    }
  }

  init() {
    // 添加调试样式
    this.addDebugStyles();
    
    // 添加快捷键
    this.addKeyboardShortcuts();
    
    // 添加控制台命令
    this.addConsoleCommands();
    
    console.log('🎯 轮廓调试工具已初始化 (开发环境)');
  }

  // 添加调试样式到页面
  addDebugStyles() {
    if (!this.isDevelopment) return;
    
    const style = document.createElement('style');
    style.id = 'outline-debug-styles';
    style.textContent = `
      /* 🎯 轮廓调试基础样式 - 仅开发环境 */
      .debug-outline { outline: 2px solid red !important; outline-offset: -1px; }
      .debug-container { outline: 3px solid blue !important; outline-offset: -2px; }
      .debug-content { outline: 2px solid green !important; outline-offset: -1px; }
      .debug-interactive { outline: 2px solid orange !important; outline-offset: -1px; }
      
      /* 🎨 颜色编码系统 */
      .debug-red { outline: 2px solid #ff0000 !important; }
      .debug-blue { outline: 2px solid #0066ff !important; }
      .debug-green { outline: 2px solid #00cc00 !important; }
      .debug-orange { outline: 2px solid #ff6600 !important; }
      .debug-purple { outline: 2px solid #9900cc !important; }
      .debug-yellow { outline: 2px solid #ffcc00 !important; }
      
      /* 🔍 半透明背景调试 */
      .debug-bg-red { background: rgba(255, 0, 0, 0.1) !important; }
      .debug-bg-blue { background: rgba(0, 102, 255, 0.1) !important; }
      .debug-bg-green { background: rgba(0, 204, 0, 0.1) !important; }
      
      /* 📐 尺寸信息显示 */
      .debug-size::before {
        content: attr(data-debug-size);
        position: absolute;
        top: -20px;
        left: 0;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 2px 6px;
        font-size: 10px;
        border-radius: 3px;
        z-index: 9999;
        pointer-events: none;
      }
      
      /* 🎯 坐标信息显示 */
      .debug-coords::after {
        content: attr(data-debug-coords);
        position: absolute;
        bottom: -20px;
        right: 0;
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 2px 6px;
        font-size: 10px;
        border-radius: 3px;
        z-index: 9999;
        pointer-events: none;
      }
      
      /* 🚨 全局调试模式 */
      .debug-global-active * {
        outline: 1px solid rgba(255, 0, 0, 0.3) !important;
      }
      
      .debug-global-active *:hover {
        outline: 2px solid red !important;
        background: rgba(255, 0, 0, 0.05) !important;
      }
    `;
    document.head.appendChild(style);
  }

  // 添加键盘快捷键
  addKeyboardShortcuts() {
    if (!this.isDevelopment) return;
    
    document.addEventListener('keydown', (e) => {
      if (e.ctrlKey && e.shiftKey) {
        switch(e.key) {
          case 'D': // Ctrl+Shift+D 启用/禁用全局调试
            e.preventDefault();
            this.toggleGlobalDebug();
            break;
          case 'C': // Ctrl+Shift+C 清除调试
            e.preventDefault();
            this.clearDebug();
            break;
          case 'L': // Ctrl+Shift+L 调试布局
            e.preventDefault();
            this.debugLayout();
            break;
          case 'S': // Ctrl+Shift+S 调试滚动条
            e.preventDefault();
            this.debugScrollbars();
            break;
          case 'O': // Ctrl+Shift+O 调试重叠
            e.preventDefault();
            this.debugOverlap();
            break;
        }
      }
    });
  }

  // 添加控制台命令
  addConsoleCommands() {
    if (!this.isDevelopment) return;
    
    window.debug = {
      on: () => this.enableGlobalDebug(),
      off: () => this.disableGlobalDebug(),
      toggle: () => this.toggleGlobalDebug(),
      clear: () => this.clearDebug(),
      layout: () => this.debugLayout(),
      scroll: () => this.debugScrollbars(),
      overlap: () => this.debugOverlap(),
      element: (selector, color = 'red') => this.debugElement(selector, color),
      pagination: () => this.debugPagination(),
      table: () => this.debugTable(),
      help: () => this.showHelp()
    };
    
    console.log('🎯 调试命令已注册到 window.debug');
    this.showHelp();
  }

  // 显示帮助信息
  showHelp() {
    if (!this.isDevelopment) return;
    
    console.log(`
🎯 轮廓调试工具帮助
===================
快捷键:
  Ctrl+Shift+D  启用/禁用全局调试
  Ctrl+Shift+C  清除所有调试样式
  Ctrl+Shift+L  调试布局问题
  Ctrl+Shift+S  调试滚动条问题
  Ctrl+Shift+O  调试重叠问题

控制台命令:
  debug.on()        启用全局调试
  debug.off()       禁用全局调试
  debug.toggle()    切换全局调试
  debug.clear()     清除调试样式
  debug.layout()    调试布局
  debug.scroll()    调试滚动条
  debug.overlap()   调试重叠
  debug.pagination() 调试翻页组件
  debug.table()     调试表格
  debug.element(selector, color) 调试指定元素
  debug.help()      显示帮助
    `);
  }

  // 启用全局调试
  enableGlobalDebug() {
    if (!this.isDevelopment) return;
    
    document.body.classList.add('debug-global-active');
    this.isActive = true;
    console.log('🎯 全局轮廓调试已启用');
  }

  // 禁用全局调试
  disableGlobalDebug() {
    if (!this.isDevelopment) return;
    
    document.body.classList.remove('debug-global-active');
    this.isActive = false;
    console.log('🎯 全局轮廓调试已禁用');
  }

  // 切换全局调试
  toggleGlobalDebug() {
    if (!this.isDevelopment) return;
    
    if (this.isActive) {
      this.disableGlobalDebug();
    } else {
      this.enableGlobalDebug();
    }
  }

  // 调试特定元素
  debugElement(selector, color = 'red', showInfo = true) {
    if (!this.isDevelopment) return;
    
    const elements = document.querySelectorAll(selector);
    elements.forEach(el => {
      el.classList.add(`debug-${color}`);
      this.debugElements.add(el);
      
      if (showInfo) {
        const rect = el.getBoundingClientRect();
        el.setAttribute('data-debug-size', `${Math.round(rect.width)}×${Math.round(rect.height)}`);
        el.setAttribute('data-debug-coords', `(${Math.round(rect.left)},${Math.round(rect.top)})`);
        el.classList.add('debug-size', 'debug-coords');
      }
    });
    console.log(`🎯 已调试 ${elements.length} 个元素: ${selector}`);
    return elements;
  }

  // 清除所有调试样式
  clearDebug() {
    if (!this.isDevelopment) return;
    
    this.debugElements.forEach(el => {
      el.className = el.className.replace(/debug-[\w-]+/g, '');
      el.removeAttribute('data-debug-size');
      el.removeAttribute('data-debug-coords');
    });
    this.debugElements.clear();
    this.disableGlobalDebug();
    console.log('🎯 已清除所有调试样式');
  }

  // 调试布局问题
  debugLayout() {
    if (!this.isDevelopment) return;
    
    this.debugElement('.table-container', 'blue');
    this.debugElement('.table-body', 'green');
    this.debugElement('.pagination-container', 'orange');
    this.debugElement('.data-row', 'purple');
    console.log('🎯 布局调试已启用');
  }

  // 调试翻页组件
  debugPagination() {
    if (!this.isDevelopment) return;
    
    this.debugElement('.pagination-container', 'orange', true);
    this.debugElement('.pagination-controls', 'yellow', true);
    this.debugElement('.page-navigation', 'green', true);
    
    // 检查翻页组件与表格的间距
    const table = document.querySelector('.table-container');
    const pagination = document.querySelector('.pagination-container');
    
    if (table && pagination) {
      const tableRect = table.getBoundingClientRect();
      const paginationRect = pagination.getBoundingClientRect();
      const gap = paginationRect.top - tableRect.bottom;
      
      console.log(`📐 表格与翻页组件间距: ${gap}px`);
      if (gap < 15) {
        console.warn('⚠️ 间距过小，可能存在遮挡问题');
        pagination.classList.add('debug-red');
      }
    }
  }

  // 调试表格
  debugTable() {
    if (!this.isDevelopment) return;
    
    this.debugElement('.table-container', 'blue', true);
    this.debugElement('.table-header', 'green', true);
    this.debugElement('.table-body', 'purple', true);
    this.debugElement('.data-row:last-child', 'red', true);
    
    // 检查横向滚动
    const containers = document.querySelectorAll('.table-container');
    containers.forEach(container => {
      const hasHScroll = container.scrollWidth > container.clientWidth;
      if (hasHScroll) {
        container.classList.add('debug-yellow', 'debug-bg-yellow');
        console.log('🔄 发现横向滚动条:', container);
      }
    });
  }

  // 调试滚动条问题
  debugScrollbars() {
    if (!this.isDevelopment) return;
    
    const elements = document.querySelectorAll('*');
    let horizontalScrollCount = 0;
    let verticalScrollCount = 0;
    
    elements.forEach(el => {
      const hasHorizontalScroll = el.scrollWidth > el.clientWidth;
      const hasVerticalScroll = el.scrollHeight > el.clientHeight;
      
      if (hasHorizontalScroll) {
        el.classList.add('debug-yellow', 'debug-bg-yellow');
        horizontalScrollCount++;
        console.log('🔄 横向滚动:', el);
      }
      if (hasVerticalScroll) {
        el.classList.add('debug-green', 'debug-bg-green');
        verticalScrollCount++;
        console.log('🔄 纵向滚动:', el);
      }
    });
    
    console.log(`🔄 滚动条统计: 横向${horizontalScrollCount}个, 纵向${verticalScrollCount}个`);
  }

  // 调试重叠问题
  debugOverlap() {
    if (!this.isDevelopment) return;
    
    const elements = Array.from(document.querySelectorAll('*')).filter(el => {
      const rect = el.getBoundingClientRect();
      return rect.width > 0 && rect.height > 0;
    });
    
    const overlaps = [];
    
    for (let i = 0; i < elements.length; i++) {
      const el1 = elements[i];
      const rect1 = el1.getBoundingClientRect();
      
      for (let j = i + 1; j < elements.length; j++) {
        const el2 = elements[j];
        const rect2 = el2.getBoundingClientRect();
        
        if (this.isOverlapping(rect1, rect2) && !this.isParentChild(el1, el2)) {
          overlaps.push({ el1, el2, rect1, rect2 });
          el1.classList.add('debug-red', 'debug-bg-red');
          el2.classList.add('debug-red', 'debug-bg-red');
        }
      }
    }
    
    console.log(`🚨 发现 ${overlaps.length} 个重叠问题`);
    overlaps.forEach(({ el1, el2 }, index) => {
      console.log(`重叠 ${index + 1}:`, el1, 'vs', el2);
    });
    
    return overlaps;
  }

  // 检查元素是否重叠
  isOverlapping(rect1, rect2) {
    return !(rect1.right <= rect2.left || 
             rect1.left >= rect2.right || 
             rect1.bottom <= rect2.top || 
             rect1.top >= rect2.bottom);
  }

  // 检查是否为父子关系
  isParentChild(el1, el2) {
    return el1.contains(el2) || el2.contains(el1);
  }
}

// 只在开发环境创建实例
let outlineDebugger = null;
if (process.env.NODE_ENV === 'development') {
  // 确保在DOM加载完成后初始化
  if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
      outlineDebugger = new OutlineDebugger();
    });
  } else {
    outlineDebugger = new OutlineDebugger();
  }
} else {
  console.log('🎯 轮廓调试工具已禁用 (生产环境)');
}

export default outlineDebugger;
