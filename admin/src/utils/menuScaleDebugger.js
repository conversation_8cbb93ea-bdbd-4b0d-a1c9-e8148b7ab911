/**
 * 菜单栏缩放调试工具
 * 专门用于测试菜单栏是否按比例缩放
 * 
 * <AUTHOR> Agent
 * @date 2025-01-25
 */

class MenuScaleDebugger {
  constructor() {
    this.isActive = false;
    this.testResults = [];
    this.baselineData = null;
  }

  /**
   * 🎯 启动菜单栏缩放测试
   */
  startTest() {
    console.log('🎯 开始菜单栏缩放测试...');
    console.log('=' .repeat(60));
    
    this.clearPreviousResults();
    this.enableOutlineDebug();
    this.measureMenuElements();
    this.checkScalingBehavior();
    this.generateReport();
    
    return this.testResults;
  }

  /**
   * 🧹 清除之前的测试结果
   */
  clearPreviousResults() {
    this.testResults = [];
    this.isActive = true;
    
    // 清除之前的调试样式
    const existingStyles = document.querySelectorAll('[id*="menu-debug"]');
    existingStyles.forEach(style => style.remove());
  }

  /**
   * 🎨 启用轮廓调试
   */
  enableOutlineDebug() {
    const debugStyle = document.createElement('style');
    debugStyle.id = 'menu-debug-outline';
    debugStyle.textContent = `
      /* 菜单栏轮廓调试样式 */
      .admin-sider {
        outline: 3px solid #ff0000 !important;
        position: relative !important;
      }
      
      .admin-sider::before {
        content: "菜单栏容器";
        position: absolute;
        top: -25px;
        left: 0;
        background: #ff0000;
        color: white;
        padding: 2px 8px;
        font-size: 12px;
        z-index: 9999;
      }
      
      .ant-menu {
        outline: 2px solid #00ff00 !important;
        position: relative !important;
      }
      
      .ant-menu::before {
        content: "菜单内容";
        position: absolute;
        top: -20px;
        right: 0;
        background: #00ff00;
        color: black;
        padding: 2px 8px;
        font-size: 12px;
        z-index: 9999;
      }
      
      .ant-menu-item {
        outline: 1px solid #0000ff !important;
      }
      
      .admin-header {
        outline: 2px solid #ff8800 !important;
      }
      
      .admin-main {
        outline: 2px solid #8800ff !important;
      }
    `;
    document.head.appendChild(debugStyle);
    
    console.log('🎨 轮廓调试已启用');
  }

  /**
   * 📏 测量菜单元素尺寸和位置
   */
  measureMenuElements() {
    console.log('\n📏 测量菜单元素尺寸和位置...');
    
    const elements = {
      'admin-sider': '.admin-sider',
      'ant-menu': '.ant-menu', 
      'admin-header': '.admin-header',
      'admin-main': '.admin-main',
      'first-menu-item': '.ant-menu-item:first-child',
      'last-menu-item': '.ant-menu-item:last-child'
    };

    const measurements = {};
    
    Object.entries(elements).forEach(([name, selector]) => {
      const element = document.querySelector(selector);
      if (element) {
        const rect = element.getBoundingClientRect();
        const computedStyle = getComputedStyle(element);
        
        measurements[name] = {
          selector: selector,
          exists: true,
          rect: {
            width: rect.width,
            height: rect.height,
            left: rect.left,
            top: rect.top,
            right: rect.right,
            bottom: rect.bottom
          },
          computedStyle: {
            width: computedStyle.width,
            height: computedStyle.height,
            marginLeft: computedStyle.marginLeft,
            fontSize: computedStyle.fontSize,
            padding: computedStyle.padding
          }
        };
        
        console.log(`📍 ${name}:`);
        console.log(`   尺寸: ${rect.width.toFixed(2)}px × ${rect.height.toFixed(2)}px`);
        console.log(`   位置: left=${rect.left.toFixed(2)}px, top=${rect.top.toFixed(2)}px`);
        console.log(`   CSS宽度: ${computedStyle.width}`);
        console.log(`   CSS高度: ${computedStyle.height}`);
        if (computedStyle.marginLeft !== '0px') {
          console.log(`   左边距: ${computedStyle.marginLeft}`);
        }
        console.log('');
      } else {
        measurements[name] = {
          selector: selector,
          exists: false
        };
        console.log(`❌ ${name}: 元素不存在 (${selector})`);
      }
    });

    this.testResults.push({
      type: 'measurements',
      timestamp: new Date().toISOString(),
      viewport: {
        width: window.innerWidth,
        height: window.innerHeight
      },
      measurements: measurements
    });
  }

  /**
   * 🔍 检查缩放行为
   */
  checkScalingBehavior() {
    console.log('\n🔍 检查缩放行为...');
    
    // 获取当前rem值
    const htmlFontSize = getComputedStyle(document.documentElement).fontSize;
    const currentRem = parseFloat(htmlFontSize);
    
    // 获取响应式缩放信息
    const scaleProperty = getComputedStyle(document.documentElement).getPropertyValue('--responsive-scale');
    const remProperty = getComputedStyle(document.documentElement).getPropertyValue('--responsive-rem');
    
    console.log(`📊 当前rem值: ${currentRem}px`);
    console.log(`📊 响应式缩放比例: ${scaleProperty || '未设置'}`);
    console.log(`📊 响应式rem: ${remProperty || '未设置'}`);
    
    // 检查菜单栏是否使用rem单位
    const siderElement = document.querySelector('.admin-sider');
    if (siderElement) {
      const siderStyle = getComputedStyle(siderElement);
      const siderWidth = siderStyle.width;
      
      console.log(`📏 菜单栏CSS宽度: ${siderWidth}`);
      
      // 计算期望的rem宽度
      const expectedRemWidth = 11.25 * currentRem;
      const actualWidth = parseFloat(siderWidth);
      
      console.log(`📏 期望宽度 (11.25rem): ${expectedRemWidth.toFixed(2)}px`);
      console.log(`📏 实际宽度: ${actualWidth.toFixed(2)}px`);
      console.log(`📏 差异: ${Math.abs(expectedRemWidth - actualWidth).toFixed(2)}px`);
      
      const isCorrectWidth = Math.abs(expectedRemWidth - actualWidth) < 1;
      console.log(`${isCorrectWidth ? '✅' : '❌'} 宽度${isCorrectWidth ? '正确' : '不正确'}`);
      
      this.testResults.push({
        type: 'scaling_check',
        currentRem: currentRem,
        expectedWidth: expectedRemWidth,
        actualWidth: actualWidth,
        difference: Math.abs(expectedRemWidth - actualWidth),
        isCorrect: isCorrectWidth,
        scaleProperty: scaleProperty,
        remProperty: remProperty
      });
    }
  }

  /**
   * 📊 生成测试报告
   */
  generateReport() {
    console.log('\n📊 菜单栏缩放测试报告');
    console.log('=' .repeat(60));
    
    const latestMeasurement = this.testResults.find(r => r.type === 'measurements');
    const latestScalingCheck = this.testResults.find(r => r.type === 'scaling_check');
    
    if (latestMeasurement) {
      console.log(`🖥️ 当前分辨率: ${latestMeasurement.viewport.width}×${latestMeasurement.viewport.height}`);
      
      const siderData = latestMeasurement.measurements['admin-sider'];
      if (siderData && siderData.exists) {
        console.log(`📏 菜单栏实际尺寸: ${siderData.rect.width.toFixed(2)}px × ${siderData.rect.height.toFixed(2)}px`);
      }
    }
    
    if (latestScalingCheck) {
      console.log(`📊 rem基准: ${latestScalingCheck.currentRem}px`);
      console.log(`📊 期望宽度: ${latestScalingCheck.expectedWidth.toFixed(2)}px`);
      console.log(`📊 实际宽度: ${latestScalingCheck.actualWidth.toFixed(2)}px`);
      console.log(`📊 宽度差异: ${latestScalingCheck.difference.toFixed(2)}px`);
      console.log(`${latestScalingCheck.isCorrect ? '✅' : '❌'} 缩放状态: ${latestScalingCheck.isCorrect ? '正确' : '不正确'}`);
    }
    
    console.log('\n🎯 测试完成！请查看上方的轮廓边框和数据分析。');
    console.log('🔧 使用 menuScaleDebugger.disableDebug() 关闭调试模式');
  }

  /**
   * 🔧 禁用调试模式
   */
  disableDebug() {
    const debugStyles = document.querySelectorAll('[id*="menu-debug"]');
    debugStyles.forEach(style => style.remove());
    this.isActive = false;
    console.log('🔧 菜单栏调试模式已关闭');
  }

  /**
   * 📋 获取测试结果
   */
  getResults() {
    return this.testResults;
  }

  /**
   * 🔄 模拟不同分辨率测试
   */
  testDifferentScales() {
    console.log('\n🔄 模拟不同缩放比例测试...');
    
    const testScales = [0.8, 0.9, 1.0, 1.1, 1.2];
    const originalFontSize = getComputedStyle(document.documentElement).fontSize;
    
    testScales.forEach(scale => {
      console.log(`\n📊 测试缩放比例: ${scale}`);
      
      // 设置新的font-size
      const newFontSize = 16 * scale;
      document.documentElement.style.fontSize = `${newFontSize}px`;
      
      // 等待样式应用
      setTimeout(() => {
        const siderElement = document.querySelector('.admin-sider');
        if (siderElement) {
          const rect = siderElement.getBoundingClientRect();
          const expectedWidth = 11.25 * newFontSize;
          
          console.log(`   rem基准: ${newFontSize}px`);
          console.log(`   期望宽度: ${expectedWidth.toFixed(2)}px`);
          console.log(`   实际宽度: ${rect.width.toFixed(2)}px`);
          console.log(`   差异: ${Math.abs(expectedWidth - rect.width).toFixed(2)}px`);
        }
      }, 100);
    });
    
    // 恢复原始字体大小
    setTimeout(() => {
      document.documentElement.style.fontSize = originalFontSize;
      console.log('\n🔄 已恢复原始缩放比例');
    }, testScales.length * 150);
  }
}

// 创建全局实例
const menuScaleDebugger = new MenuScaleDebugger();

// 导出供其他模块使用
export default menuScaleDebugger;

// 在开发环境下挂载到window对象
if (typeof window !== 'undefined' && process.env.NODE_ENV === 'development') {
  window.menuScaleDebugger = menuScaleDebugger;
  console.log('🔧 菜单栏缩放调试工具已加载');
  console.log('🎯 使用 menuScaleDebugger.startTest() 开始测试');
  console.log('🔄 使用 menuScaleDebugger.testDifferentScales() 测试不同缩放');
  console.log('🔧 使用 menuScaleDebugger.disableDebug() 关闭调试');
}
