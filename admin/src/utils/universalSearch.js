/**
 * 🎯 通用搜索工具 - 壹心堂管理系统
 * 
 * 支持功能：
 * - 中文搜索
 * - 英文搜索（大小写不区分）
 * - 中英文混合搜索
 * - 拼音首字母搜索
 * - 全拼搜索
 * - 模糊匹配
 * - 相似度匹配
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-28
 */

// 🎯 拼音映射表 - 常用汉字拼音首字母和全拼
const PINYIN_MAP = {
  // 服务相关
  '按': ['an', 'a'], '摩': ['mo', 'm'], '中': ['zhong', 'z'], '式': ['shi', 's'],
  '足': ['zu', 'z'], '疗': ['liao', 'l'], '保': ['bao', 'b'], '健': ['jian', 'j'],
  '面': ['mian', 'm'], '部': ['bu', 'b'], '护': ['hu', 'h'], '理': ['li', 'l'],
  '颈': ['jing', 'j'], '椎': ['zhui', 'z'], '调': ['tiao', 't'], '腰': ['yao', 'y'],
  '推': ['tui', 't'], '拿': ['na', 'n'], '肩': ['jian', 'j'], '全': ['quan', 'q'],
  '身': ['shen', 's'], '艾': ['ai', 'a'], '灸': ['jiu', 'j'], '拔': ['ba', 'b'],
  '罐': ['guan', 'g'], '刮': ['gua', 'g'], '痧': ['sha', 's'], '经': ['jing', 'j'],
  '络': ['luo', 'l'], '疏': ['shu', 's'], '通': ['tong', 't'],
  
  // 管理相关
  '服': ['fu', 'f'], '务': ['wu', 'w'], '管': ['guan', 'g'], '客': ['ke', 'k'],
  '户': ['hu', 'h'], '技': ['ji', 'j'], '师': ['shi', 's'], '财': ['cai', 'c'],
  '系': ['xi', 'x'], '统': ['tong', 't'], '设': ['she', 's'], '置': ['zhi', 'z'],
  '数': ['shu', 's'], '据': ['ju', 'j'], '计': ['ji', 'j'], '仪': ['yi', 'y'],
  '表': ['biao', 'b'], '盘': ['pan', 'p'], '预': ['yu', 'y'], '约': ['yue', 'y'],
  '退': ['tui', 't'], '出': ['chu', 'c'], '登': ['deng', 'd'], '录': ['lu', 'l'],
  
  // 描述相关
  '心': ['xin', 'x'], '贴': ['tie', 't'], '士': ['shi', 's'], '元': ['yuan', 'y'],
  '分': ['fen', 'f'], '钟': ['zhong', 'z'], '美': ['mei', 'm'], '容': ['rong', 'r'],
  '养': ['yang', 'y'], '生': ['sheng', 's'], '深': ['shen', 's'], '层': ['ceng', 'c'],
  '清': ['qing', 'q'], '洁': ['jie', 'j'], '补': ['bu', 'b'], '水': ['shui', 's'],
  '湿': ['shi', 's'], '延': ['yan', 'y'], '缓': ['huan', 'h'], '衰': ['shuai', 's'],
  '老': ['lao', 'l'], '专': ['zhuan', 'z'], '业': ['ye', 'y'], '僵': ['jiang', 'j'],
  '硬': ['ying', 'y'], '疼': ['teng', 't'], '痛': ['tong', 't'], '放': ['fang', 'f'],
  '松': ['song', 's'], '肌': ['ji', 'j'], '肉': ['rou', 'r'], '舒': ['shu', 's'],
  '体': ['ti', 't'], '验': ['yan', 'y'], '传': ['chuan', 'c'], '温': ['wen', 'w'],
  '排': ['pai', 'p'], '毒': ['du', 'd'], '颜': ['yan', 'y'], '平': ['ping', 'p'],
  '衡': ['heng', 'h'], '气': ['qi', 'q'], '血': ['xue', 'x'],
  
  // 常用字
  '的': ['de', 'd'], '了': ['le', 'l'], '是': ['shi', 's'], '我': ['wo', 'w'],
  '你': ['ni', 'n'], '他': ['ta', 't'], '她': ['ta', 't'], '它': ['ta', 't'],
  '们': ['men', 'm'], '这': ['zhe', 'z'], '那': ['na', 'n'], '有': ['you', 'y'],
  '在': ['zai', 'z'], '不': ['bu', 'b'], '和': ['he', 'h'], '与': ['yu', 'y'],
  '或': ['huo', 'h'], '及': ['ji', 'j'], '等': ['deng', 'd'], '为': ['wei', 'w'],
  '以': ['yi', 'y'], '可': ['ke', 'k'], '能': ['neng', 'n'], '会': ['hui', 'h'],
  '要': ['yao', 'y'], '用': ['yong', 'y'], '做': ['zuo', 'z'], '到': ['dao', 'd'],
  '从': ['cong', 'c'], '对': ['dui', 'd'], '于': ['yu', 'y'], '向': ['xiang', 'x'],
  '给': ['gei', 'g'], '把': ['ba', 'b'], '被': ['bei', 'b'], '让': ['rang', 'r'],
  '使': ['shi', 's'], '得': ['de', 'd'], '过': ['guo', 'g'], '来': ['lai', 'l'],
  '去': ['qu', 'q'], '上': ['shang', 's'], '下': ['xia', 'x'], '前': ['qian', 'q'],
  '后': ['hou', 'h'], '左': ['zuo', 'z'], '右': ['you', 'y'], '中': ['zhong', 'z'],
  '内': ['nei', 'n'], '外': ['wai', 'w'], '里': ['li', 'l'], '边': ['bian', 'b'],
  '间': ['jian', 'j'], '时': ['shi', 's'], '候': ['hou', 'h'], '年': ['nian', 'n'],
  '月': ['yue', 'y'], '日': ['ri', 'r'], '天': ['tian', 't'], '小': ['xiao', 'x'],
  '大': ['da', 'd'], '多': ['duo', 'd'], '少': ['shao', 's'], '好': ['hao', 'h'],
  '坏': ['huai', 'h'], '新': ['xin', 'x'], '旧': ['jiu', 'j'], '高': ['gao', 'g'],
  '低': ['di', 'd'], '长': ['chang', 'c'], '短': ['duan', 'd'], '快': ['kuai', 'k'],
  '慢': ['man', 'm'], '早': ['zao', 'z'], '晚': ['wan', 'w'], '今': ['jin', 'j'],
  '明': ['ming', 'm'], '昨': ['zuo', 'z'], '每': ['mei', 'm'], '都': ['dou', 'd'],
  '还': ['hai', 'h'], '也': ['ye', 'y'], '就': ['jiu', 'j'], '只': ['zhi', 'z'],
  '但': ['dan', 'd'], '而': ['er', 'e'], '所': ['suo', 's'], '因': ['yin', 'y'],
  '如': ['ru', 'r'], '果': ['guo', 'g'], '然': ['ran', 'r'], '虽': ['sui', 's'],
  '虽': ['sui', 's'], '然': ['ran', 'r'], '但': ['dan', 'd'], '是': ['shi', 's']
};

/**
 * 🎯 获取文本的所有可能匹配形式
 * @param {string} text - 输入文本
 * @returns {Array<string>} 所有可能的匹配形式
 */
export const getTextVariants = (text) => {
  if (!text) return [];
  
  const textStr = String(text).trim();
  const variants = new Set();
  
  // 1. 原文本（保持大小写）
  variants.add(textStr);
  
  // 2. 小写版本
  variants.add(textStr.toLowerCase());
  
  // 3. 大写版本
  variants.add(textStr.toUpperCase());
  
  // 4. 去除空格版本
  variants.add(textStr.replace(/\s+/g, ''));
  variants.add(textStr.toLowerCase().replace(/\s+/g, ''));
  
  // 5. 去除标点符号版本
  const noPunctuation = textStr.replace(/[^\w\u4e00-\u9fa5]/g, '');
  if (noPunctuation) {
    variants.add(noPunctuation);
    variants.add(noPunctuation.toLowerCase());
  }
  
  // 6. 拼音首字母和全拼
  let pinyinInitials = '';
  let fullPinyin = '';
  
  for (let char of textStr) {
    if (PINYIN_MAP[char]) {
      pinyinInitials += PINYIN_MAP[char][1]; // 首字母
      fullPinyin += PINYIN_MAP[char][0]; // 全拼
    }
  }
  
  if (pinyinInitials) {
    variants.add(pinyinInitials);
    variants.add(pinyinInitials.toLowerCase());
    variants.add(pinyinInitials.toUpperCase());
  }
  
  if (fullPinyin) {
    variants.add(fullPinyin);
    variants.add(fullPinyin.toLowerCase());
    variants.add(fullPinyin.toUpperCase());
  }
  
  // 7. 数字相关的变体
  if (/\d/.test(textStr)) {
    // 提取数字
    const numbers = textStr.match(/\d+/g);
    if (numbers) {
      numbers.forEach(num => {
        variants.add(num);
        variants.add(`${num}元`);
        variants.add(`¥${num}`);
        variants.add(`${num}分钟`);
        variants.add(`${num}min`);
      });
    }
  }
  
  return Array.from(variants).filter(v => v.length > 0);
};

/**
 * 🎯 计算字符串相似度（编辑距离算法）
 * @param {string} str1 - 字符串1
 * @param {string} str2 - 字符串2
 * @returns {number} 相似度（0-1之间）
 */
export const calculateSimilarity = (str1, str2) => {
  if (!str1 || !str2) return 0;
  if (str1 === str2) return 1;
  
  const len1 = str1.length;
  const len2 = str2.length;
  
  if (len1 === 0) return len2 === 0 ? 1 : 0;
  if (len2 === 0) return 0;
  
  const matrix = Array(len1 + 1).fill().map(() => Array(len2 + 1).fill(0));
  
  for (let i = 0; i <= len1; i++) matrix[i][0] = i;
  for (let j = 0; j <= len2; j++) matrix[0][j] = j;
  
  for (let i = 1; i <= len1; i++) {
    for (let j = 1; j <= len2; j++) {
      const cost = str1[i - 1] === str2[j - 1] ? 0 : 1;
      matrix[i][j] = Math.min(
        matrix[i - 1][j] + 1,      // 删除
        matrix[i][j - 1] + 1,      // 插入
        matrix[i - 1][j - 1] + cost // 替换
      );
    }
  }
  
  const maxLen = Math.max(len1, len2);
  return maxLen === 0 ? 1 : (maxLen - matrix[len1][len2]) / maxLen;
};

/**
 * 🎯 通用搜索匹配函数 - 支持所有搜索形式
 * @param {string} text - 被搜索的文本
 * @param {string} searchTerm - 搜索词
 * @param {Object} options - 搜索选项
 * @returns {boolean} 是否匹配
 */
export const universalMatch = (text, searchTerm, options = {}) => {
  if (!text || !searchTerm) return false;
  
  const {
    exactMatch = false,        // 是否精确匹配
    caseSensitive = false,     // 是否区分大小写
    minSimilarity = 0.6,       // 最小相似度阈值
    enableFuzzy = true,        // 是否启用模糊匹配
    enablePinyin = true,       // 是否启用拼音匹配
    debug = false              // 是否输出调试信息
  } = options;
  
  try {
    const textVariants = getTextVariants(text);
    const searchVariants = getTextVariants(searchTerm);
    
    if (debug) {
      console.log('🔍 搜索调试信息:');
      console.log('  原文本:', text);
      console.log('  搜索词:', searchTerm);
      console.log('  文本变体:', textVariants.slice(0, 5));
      console.log('  搜索变体:', searchVariants.slice(0, 5));
    }
    
    // 检查所有可能的组合
    for (const textVar of textVariants) {
      for (const searchVar of searchVariants) {
        // 1. 完全匹配
        if (exactMatch) {
          if (caseSensitive ? textVar === searchVar : textVar.toLowerCase() === searchVar.toLowerCase()) {
            if (debug) console.log('✅ 完全匹配:', textVar, '===', searchVar);
            return true;
          }
        } else {
          // 2. 包含匹配
          const textLower = caseSensitive ? textVar : textVar.toLowerCase();
          const searchLower = caseSensitive ? searchVar : searchVar.toLowerCase();
          
          if (textLower.includes(searchLower) || searchLower.includes(textLower)) {
            if (debug) console.log('✅ 包含匹配:', textLower, 'includes', searchLower);
            return true;
          }
          
          // 3. 模糊匹配（相似度匹配）
          if (enableFuzzy && textVar.length > 2 && searchVar.length > 2) {
            const similarity = calculateSimilarity(textLower, searchLower);
            if (similarity >= minSimilarity) {
              if (debug) console.log('✅ 模糊匹配:', textLower, '~', searchLower, '相似度:', similarity);
              return true;
            }
          }
        }
      }
    }
    
    return false;
  } catch (error) {
    console.warn('通用搜索匹配出错:', error);
    return false;
  }
};

/**
 * 🎯 创建搜索过滤器
 * @param {Array} data - 数据数组
 * @param {string} searchTerm - 搜索词
 * @param {Array<string>} searchFields - 搜索字段
 * @param {Object} options - 搜索选项
 * @returns {Array} 过滤后的数据
 */
export const createSearchFilter = (data, searchTerm, searchFields, options = {}) => {
  if (!data || !Array.isArray(data) || !searchTerm || !searchTerm.trim()) {
    return data || [];
  }
  
  const search = searchTerm.trim();
  const { debug = false } = options;
  
  if (debug) {
    console.log('🎯 创建搜索过滤器:');
    console.log('  数据量:', data.length);
    console.log('  搜索词:', search);
    console.log('  搜索字段:', searchFields);
  }
  
  return data.filter(item => {
    // 检查每个搜索字段
    for (const field of searchFields) {
      const fieldValue = getNestedValue(item, field);
      if (fieldValue && universalMatch(fieldValue, search, options)) {
        if (debug) console.log('✅ 匹配项:', item, '字段:', field, '值:', fieldValue);
        return true;
      }
    }
    
    return false;
  });
};

/**
 * 🎯 获取嵌套对象的值
 * @param {Object} obj - 对象
 * @param {string} path - 路径（支持点号分隔）
 * @returns {any} 值
 */
const getNestedValue = (obj, path) => {
  if (!obj || !path) return null;
  
  const keys = path.split('.');
  let value = obj;
  
  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      return null;
    }
  }
  
  return value;
};

/**
 * 🎯 默认导出 - 通用搜索工具集合
 */
export default {
  universalMatch,
  getTextVariants,
  calculateSimilarity,
  createSearchFilter,
  PINYIN_MAP
};
