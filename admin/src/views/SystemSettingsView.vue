<template>
  <div class="picasso-settings">
    <!-- 毕加索风格选项卡 -->
    <div class="tabs-cubism">
      <div 
        v-for="tab in tabs" 
        :key="tab.key"
        class="tab-cube"
        :class="{ active: activeTab === tab.key }"
        @click="activeTab = tab.key"
      >
        <div class="tab-face">
          <div class="tab-icon">{{ tab.icon }}</div>
          <div class="tab-text">{{ tab.label }}</div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格内容区域 -->
    <div class="content-cubism">
      <!-- 基本设置 -->
      <div v-if="activeTab === 'basic'" class="settings-fragment">
        <div class="fragment-header">
          <div class="header-geometry">基本设置</div>
        </div>
        
        <div class="form-cubism">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">系统名称</label>
              <input 
                type="text" 
                v-model="basicSettings.systemName" 
                class="form-input"
                placeholder="请输入系统名称"
              aria-label="输入字段">
            </div>
            
            <div class="form-group">
              <label class="form-label">联系电话</label>
              <input 
                type="tel" 
                v-model="basicSettings.contactPhone" 
                class="form-input"
                placeholder="请输入联系电话"
              aria-label="输入字段">
            </div>
          </div>

          <div class="form-row full-width">
            <div class="form-group">
              <label class="form-label">营业地址</label>
              <input 
                type="text" 
                v-model="basicSettings.businessAddress" 
                class="form-input"
                placeholder="请输入营业地址"
              aria-label="输入字段">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">营业开始时间</label>
              <input 
                type="time" 
                v-model="basicSettings.openTime" 
                class="form-input"
              aria-label="输入字段">
            </div>
            
            <div class="form-group">
              <label class="form-label">营业结束时间</label>
              <input 
                type="time" 
                v-model="basicSettings.closeTime" 
                class="form-input"
              aria-label="输入字段">
            </div>
          </div>

          <div class="action-row">
            <div class="action-cube save" @click="saveBasicSettings">
              <div class="cube-face">保存设置</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预约设置 -->
      <div v-if="activeTab === 'appointment'" class="settings-fragment">
        <div class="fragment-header">
          <div class="header-geometry">预约设置</div>
        </div>
        
        <div class="form-cubism">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">提前预约天数</label>
              <input 
                type="number" 
                v-model="appointmentSettings.advanceBookingDays" 
                class="form-input"
                placeholder="1-30天"
                min="1"
                max="30"
              aria-label="输入字段">
            </div>
            
            <div class="form-group">
              <label class="form-label">预约时间间隔</label>
              <select v-model="appointmentSettings.timeInterval" class="form-select">
                <option value="15">15分钟</option>
                <option value="30">30分钟</option>
                <option value="60">60分钟</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">允许取消预约</label>
              <div class="switch-geometry">
                <input 
                  type="checkbox" 
                  v-model="appointmentSettings.allowCancellation"
                  class="geometric-switch"
                  id="allowCancellation"
                />
                <label for="allowCancellation" class="switch-label">
                  {{ appointmentSettings.allowCancellation ? '允许' : '禁止' }}
                </label>
              </div>
            </div>
            
            <div class="form-group">
              <label class="form-label">取消提前时间</label>
              <input 
                type="number" 
                v-model="appointmentSettings.cancellationHours" 
                class="form-input"
                placeholder="小时"
                min="1"
                max="48"
              aria-label="输入字段">
            </div>
          </div>

          <div class="action-row">
            <div class="action-cube save" @click="saveAppointmentSettings">
              <div class="cube-face">保存设置</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 通知设置 -->
      <div v-if="activeTab === 'notification'" class="settings-fragment">
        <div class="fragment-header">
          <div class="header-geometry">通知设置</div>
        </div>
        
        <div class="notification-cubism">
          <div 
            v-for="item in notificationSettings" 
            :key="item.key"
            class="notification-cube"
          >
            <div class="notification-info">
              <div class="notification-title">{{ item.title }}</div>
              <div class="notification-desc">{{ item.description }}</div>
            </div>
            <div class="notification-switch">
              <input 
                type="checkbox" 
                v-model="item.enabled"
                class="geometric-switch"
                :id="item.key"
              />
              <label :for="item.key" class="switch-label">
                {{ item.enabled ? '开启' : '关闭' }}
              </label>
            </div>
          </div>

          <div class="action-row">
            <div class="action-cube save" @click="saveNotificationSettings">
              <div class="cube-face">保存设置</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 安全设置 -->
      <div v-if="activeTab === 'security'" class="settings-fragment">
        <div class="fragment-header">
          <div class="header-geometry">安全设置</div>
        </div>
        
        <div class="form-cubism">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">会话超时时间</label>
              <select v-model="securitySettings.sessionTimeout" class="form-select">
                <option value="30">30分钟</option>
                <option value="60">1小时</option>
                <option value="120">2小时</option>
                <option value="240">4小时</option>
              </select>
            </div>
            
            <div class="form-group">
              <label class="form-label">密码最小长度</label>
              <input 
                type="number" 
                v-model="securitySettings.minPasswordLength" 
                class="form-input"
                min="6"
                max="20"
              aria-label="输入字段">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">启用双因子认证</label>
              <div class="switch-geometry">
                <input 
                  type="checkbox" 
                  v-model="securitySettings.twoFactorAuth"
                  class="geometric-switch"
                  id="twoFactorAuth"
                />
                <label for="twoFactorAuth" class="switch-label">
                  {{ securitySettings.twoFactorAuth ? '启用' : '禁用' }}
                </label>
              </div>
            </div>
            
            <div class="form-group">
              <label class="form-label">登录失败锁定</label>
              <div class="switch-geometry">
                <input 
                  type="checkbox" 
                  v-model="securitySettings.loginLockout"
                  class="geometric-switch"
                  id="loginLockout"
                />
                <label for="loginLockout" class="switch-label">
                  {{ securitySettings.loginLockout ? '启用' : '禁用' }}
                </label>
              </div>
            </div>
          </div>

          <div class="action-row">
            <div class="action-cube save" @click="saveSecuritySettings">
              <div class="cube-face">保存设置</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

// 防抖函数 - 性能优化
let debounceTimer = null;
const debounce = (func, delay = 300) => {
  return (...args) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    debounceTimer = setTimeout(() => func.apply(this, args), delay);
  };
};

import { ref, reactive , nextTick, shallowRef, watchEffect } from 'vue';;

// 选项卡配置
const tabs = [
  { key: 'basic', label: '基本设置', icon: '⚙️' },
  { key: 'appointment', label: '预约设置', icon: '📅' },
  { key: 'notification', label: '通知设置', icon: '🔔' },
  { key: 'security', label: '安全设置', icon: '🔒' }
];

// 当前选项卡
const activeTab = ref('basic');

// 基本设置
const basicSettings = reactive({
  systemName: '壹心堂中医理疗管理系统',
  contactPhone: '************',
  businessAddress: '北京市朝阳区xxx街道xxx号',
  openTime: '09:00',
  closeTime: '21:00'
});

// 预约设置
const appointmentSettings = reactive({
  advanceBookingDays: 7,
  timeInterval: 30,
  allowCancellation: true,
  cancellationHours: 2
});

// 通知设置
const notificationSettings = reactive([
  {
    key: 'appointment_reminder',
    title: '预约提醒',
    description: '在预约时间前发送提醒通知',
    enabled: true
  },
  {
    key: 'payment_notification',
    title: '支付通知',
    description: '支付成功后发送确认通知',
    enabled: true
  },
  {
    key: 'system_maintenance',
    title: '系统维护',
    description: '系统维护时发送通知',
    enabled: false
  }
]);

// 安全设置
const securitySettings = reactive({
  sessionTimeout: 60,
  minPasswordLength: 8,
  twoFactorAuth: false,
  loginLockout: true
});

// 保存方法
const saveBasicSettings = () => {
  console.log('保存基本设置:', basicSettings);
};

const saveAppointmentSettings = () => {
  console.log('保存预约设置:', appointmentSettings);
};

const saveNotificationSettings = () => {
  console.log('保存通知设置:', notificationSettings);
};

const saveSecuritySettings = () => {
  console.log('保存安全设置:', securitySettings);
};
</script>

<style scoped>
/* 继承系统管理页面的毕加索风格样式 */
.picasso-settings {
  display: flex;
  position: fixed;
  inset: 0 0 0 180px;
  width: calc(100vw - 180px);
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;
  background: linear-gradient(45deg,
    #8e44ad 0%, #9b59b6 25%, #c8a2c8 50%, #dda0dd 75%, #e6e6fa 100%
  );
  background-size: 400% 400%;
  animation: picassoFlow 20s ease infinite;
  flex-direction: column;
}

@keyframes picassoFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 0% 50%; }
  100% { background-position: 50% 0%; }
}

/* 继承毕加索风格基础样式 */

/* 已删除无用的标题样式 */

/* 🎨 响应式设计断点 - 符合CI_CD_STANDARDS.md响应式设计规范 */

/* 平板设备 (768px - 1024px) */
@media (width <= 1024px) {
  .picasso-settings {
    padding: 15px;
  }

  .tabs-cubism {
    gap: 10px;
  }

  .tab-cube {
    min-width: 100px;
    padding: 12px;
  }

  .tab-text {
    font-size: 13px;
  }

  .form-input,
  .form-select,
  .form-textarea {
    padding: 10px;
    font-size: 14px;
  }
}

/* 移动端设备 (最大768px) */
@media (width <= 768px) {
  .picasso-settings {
    padding: 10px;
  }

  .tabs-cubism {
    flex-direction: column;
    gap: 8px;
  }

  .tab-cube {
    width: 100%;
    min-height: 60px;
    padding: 15px;
  }

  .tab-face {
    flex-direction: row;
    gap: 10px;
  }

  .tab-icon {
    font-size: 18px;
  }

  .tab-text {
    font-size: 14px;
  }

  .content-cubism {
    margin-top: 15px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .form-input,
  .form-select,
  .form-textarea {
    width: 100%;
    padding: 12px;
    font-size: 16px; /* 移动端增大字体 */
  }

  .form-group {
    width: 100%;
  }

  .action-buttons {
    flex-direction: column;
    gap: 10px;
  }

  .save-button,
  .reset-button {
    width: 100%;
    padding: 15px;
    font-size: 16px;
  }
}

/* 小屏幕移动端 (最大480px) */
@media (width <= 480px) {
  .picasso-settings {
    padding: 8px;
  }

  .tab-cube {
    min-height: 50px;
    padding: 12px;
  }

  .tab-icon {
    font-size: 16px;
  }

  .tab-text {
    font-size: 12px;
  }

  .fragment-header {
    padding: 10px;
  }

  .header-geometry {
    font-size: 16px;
  }

  .form-input,
  .form-select,
  .form-textarea {
    padding: 10px;
    font-size: 16px;
  }

  .save-button,
  .reset-button {
    padding: 12px;
    font-size: 14px;
  }
}
</style>
