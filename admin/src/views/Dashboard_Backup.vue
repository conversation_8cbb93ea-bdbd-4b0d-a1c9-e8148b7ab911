<template>
  <div class="picasso-dashboard">
    <!-- 毕加索风格统计卡片 -->
    <div class="stats-cubism">
      <div class="stat-cube today-appointments">
        <div class="cube-face front">
          <div class="stat-icon">📅</div>
          <div class="stat-value">{{ stats.todayAppointments }}</div>
          <div class="stat-label">今日预约</div>
        </div>
        <div class="cube-face back"></div>
        <div class="cube-shadow"></div>
      </div>

      <div class="stat-cube total-customers">
        <div class="cube-face front">
          <div class="stat-icon">👥</div>
          <div class="stat-value">{{ stats.totalCustomers }}</div>
          <div class="stat-label">客户总数</div>
        </div>
        <div class="cube-face back"></div>
        <div class="cube-shadow"></div>
      </div>

      <div class="stat-cube active-therapists">
        <div class="cube-face front">
          <div class="stat-icon">👨‍⚕️</div>
          <div class="stat-value">{{ stats.activeTherapists }}</div>
          <div class="stat-label">在职技师</div>
        </div>
        <div class="cube-face back"></div>
        <div class="cube-shadow"></div>
      </div>

      <div class="stat-cube today-revenue">
        <div class="cube-face front">
          <div class="stat-icon">💰</div>
          <div class="stat-value">¥{{ stats.todayRevenue }}</div>
          <div class="stat-label">今日收入</div>
        </div>
        <div class="cube-face back"></div>
        <div class="cube-shadow"></div>
      </div>
    </div>

    <!-- 毕加索风格图表区域 -->
    <div class="charts-cubism">
      <div class="chart-cube appointments-chart">
        <div class="chart-header">
          <div class="header-fragment">预约趋势</div>
        </div>
        <div class="chart-body">
          <div class="chart-placeholder">
            <div class="chart-bars">
              <div class="bar" :style="{ height: '60%' }"></div>
              <div class="bar" :style="{ height: '80%' }"></div>
              <div class="bar" :style="{ height: '45%' }"></div>
              <div class="bar" :style="{ height: '90%' }"></div>
              <div class="bar" :style="{ height: '70%' }"></div>
              <div class="bar" :style="{ height: '85%' }"></div>
              <div class="bar" :style="{ height: '65%' }"></div>
            </div>
          </div>
        </div>
      </div>

      <div class="chart-cube revenue-chart">
        <div class="chart-header">
          <div class="header-fragment">收入分析</div>
        </div>
        <div class="chart-body">
          <div class="chart-placeholder">
            <div class="pie-chart"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格活动列表 -->
    <div class="activities-cubism">
      <div class="activities-header">
        <div class="header-fragment">最近活动</div>
      </div>
      <div class="activities-body">
        <div 
          v-for="activity in activities" 
          :key="activity.id"
          class="activity-fragment"
        >
          <div class="activity-time">{{ activity.time }}</div>
          <div class="activity-content">{{ activity.content }}</div>
          <div class="activity-decoration"></div>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast通知组件 - 符合CI_CD_STANDARDS.md用户反馈规范 -->
  <div v-if="toastState.visible" class="toast-notification" :class="'toast-' + toastState.type">
    <div class="toast-content">
      <div class="toast-icon">
        <span v-if="toastState.type === 'success'">✅</span>
        <span v-else-if="toastState.type === 'error'">❌</span>
        <span v-else-if="toastState.type === 'warning'">⚠️</span>
      </div>
      <div class="toast-message">{{ toastState.message }}</div>
    </div>
  </div>
</template>

<script setup>
// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

import { ref, reactive, onMounted , nextTick, shallowRef, watchEffect } from 'vue';;

// 响应式数据
const stats = reactive({
  todayAppointments: 12,
  totalCustomers: 156,
  activeTherapists: 8,
  todayRevenue: 3280
});

const activities = ref([
  { id: 1, time: '10:30', content: '张女士完成颈椎推拿服务' },
  { id: 2, time: '11:15', content: '李先生预约明日全身推拿' },
  { id: 3, time: '12:00', content: '王医师上班签到' },
  { id: 4, time: '13:30', content: '新客户赵先生注册' },
  { id: 5, time: '14:15', content: '足疗保健服务完成' },
  { id: 6, time: '15:00', content: '技师绩效更新' }
]);

// Toast通知状态 - 符合CI_CD_STANDARDS.md用户反馈规范
const toastState = reactive({
  visible: false,
  message: '',
  type: 'success' // success, error, warning
});

// Toast通知函数 - 替代console日志
const showToast = (message, type = 'success') => {
  toastState.message = message;
  toastState.type = type;
  toastState.visible = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    toastState.visible = false;
  }, 3000);
};

// 模拟数据更新 - 符合CI_CD_STANDARDS.md错误处理规范
onMounted(() => {
  try {
    setInterval(() => {
      try {
        stats.todayAppointments += Math.floor(Math.random() * 2);
        stats.todayRevenue += Math.floor(Math.random() * 100);
      } catch (error) {
        console.error('数据更新失败:', error);
        showToast('数据更新异常', 'warning');
      }
    }, 5000);
  } catch (error) {
    console.error('仪表盘初始化失败:', error);
    showToast('仪表盘初始化失败', 'error');
  }
});
</script>

<style scoped>
/* 毕加索风格仪表盘 - 占满主内容区 */
.picasso-dashboard {
  display: grid;
  position: fixed;
  inset: 0 0 0 180px; /* 侧边栏宽度 */
  width: calc(100vw - 180px);
  height: 100vh;
  padding: 30px; /* 统一主内容区边距 */
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;

  /* 设置与全局背景相同的梵高风格紫色渐变，调整背景位置以匹配全局背景 */
  background: linear-gradient(180deg,
    #2d1b69 0%,    /* 深紫色（梵高星夜风格） */
    #3730a3 15%,   /* 靛蓝紫 */
    #4338ca 30%,   /* 中紫色 */
    #5b21b6 45%,   /* 深紫色 */
    #6b21a8 60%,   /* 紫色 */
    #7c2d92 75%,   /* 紫红色 */
    #86198f 90%,   /* 深紫红 */
    #701a75 100%   /* 最深紫色 */
  ) !important;
  background-attachment: fixed; /* 固定背景，避免位移 */
  grid-template:
    "stats stats" auto "charts activities" 1fr / 2fr 1fr;
  gap: 20px;
}

@keyframes picassoFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 0% 50%; }
  100% { background-position: 50% 0%; }
}

/* 已删除无用的标题样式 */

.subtitle-fragment {
  display: inline-block;
  padding: 5px 15px;
  border-radius: 0 20px;
  font-size: 1.1rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg, 
    rgb(200 162 200 / 80%), 
    rgb(32 178 170 / 60%), 
    rgb(255 255 255 / 90%)
  );
  box-shadow: 0 5px 15px rgb(0 0 0 / 20%);
  transform: skew(-5deg);
}

/* 毕加索风格统计卡片 */
.stats-cubism {
  display: grid;
  position: relative;
  z-index: 10;
  grid-area: stats;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-cube {
  position: relative;
  height: 120px;
  transform-style: preserve-3d;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-cube:hover {
  transform: rotateY(10deg) rotateX(5deg) scale(1.05);
}

.cube-face {
  display: flex;
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 20px 5px;
  font-weight: bold;
  color: white;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-shadow: 2px 2px 4px rgb(0 0 0 / 30%);
}

.cube-face.front {
  border: 3px solid;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(255 255 255 / 95%), rgb(255 255 255 / 80%));
  transform: translateZ(10px);
}

.cube-face.back {
  opacity: 0.3;
  transform: translateZ(-10px) rotateY(180deg);
}

.cube-shadow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 20px 5px;
  opacity: 0.3;
  transform: translateZ(-15px) translateX(5px) translateY(5px);
}

.today-appointments .cube-face.front {
  border-color: #ff6b6b;
}

.today-appointments .cube-face.back,
.today-appointments .cube-shadow {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
}

.total-customers .cube-face.front {
  border-color: #4ecdc4;
}

.total-customers .cube-face.back,
.total-customers .cube-shadow {
  background: linear-gradient(45deg, #4ecdc4, #45b7d1);
}

.active-therapists .cube-face.front {
  border-color: #f39c12;
}

.active-therapists .cube-face.back,
.active-therapists .cube-shadow {
  background: linear-gradient(45deg, #f39c12, #e67e22);
}

.today-revenue .cube-face.front {
  border-color: #27ae60;
}

.today-revenue .cube-face.back,
.today-revenue .cube-shadow {
  background: linear-gradient(45deg, #27ae60, #2ecc71);
}

.stat-icon {
  font-size: 2rem;
  margin-bottom: 8px;
  transform: rotate(10deg);
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 900;
  margin-bottom: 4px;
  transform: skew(-5deg);
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  transform: skew(3deg);
}

/* 毕加索风格图表区域 */
.charts-cubism {
  display: grid;
  position: relative;
  z-index: 10;
  grid-area: charts;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-cube {
  border-radius: 20px 5px;
  overflow: hidden;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 20px 40px rgb(32 178 170 / 30%);
  transform: perspective(1000px) rotateX(3deg);
}

.chart-header {
  padding: 15px;
  background: linear-gradient(45deg, rgb(255 107 107 / 30%), rgb(78 205 196 / 30%));
  border-bottom: 3px solid;
}

.appointments-chart .chart-header {
  border-color: #ff6b6b;
}

.revenue-chart .chart-header {
  border-color: #4ecdc4;
}

.header-fragment {
  font-size: 1.2rem;
  font-weight: 900;
  color: #2c3e50;
  text-transform: uppercase;
  letter-spacing: 1px;
  transform: skew(-2deg);
}

.chart-body {
  display: flex;
  height: 200px;
  padding: 20px;
  align-items: center;
  justify-content: center;
}

.chart-bars {
  display: flex;
  align-items: flex-end;
  gap: 8px;
  height: 100%;
}

.bar {
  width: 20px;
  border-radius: 4px 4px 0 0;
  background: linear-gradient(to top, #c8a2c8, #20b2aa, #4169e1);
  animation: barGrow 2s ease-out;
}

@keyframes barGrow {
  from { height: 0; }
}

.pie-chart {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: conic-gradient(
    #ff6b6b 0deg 90deg,
    #4ecdc4 90deg 180deg,
    #f39c12 180deg 270deg,
    #27ae60 270deg 360deg
  );
  animation: pieRotate 3s ease-out;
}

@keyframes pieRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 毕加索风格活动列表 */
.activities-cubism {
  position: relative;
  z-index: 10;
  border-radius: 20px 5px;
  overflow: hidden;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 20px 40px rgb(65 105 225 / 30%);
  transform: perspective(1000px) rotateY(-3deg);
  grid-area: activities;
}

.activities-header {
  padding: 15px;
  background: linear-gradient(45deg, rgb(65 105 225 / 30%), rgb(221 160 221 / 30%));
  border-bottom: 3px solid #4169e1;
}

.activities-body {
  height: calc(100% - 60px);
  padding: 10px;
  overflow-y: auto;

  /* 毕加索风格滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #4169e1 rgb(65 105 225 / 20%);
}

.activities-body::-webkit-scrollbar {
  width: 6px;
}

.activities-body::-webkit-scrollbar-track {
  border-radius: 6px;
  background: rgb(65 105 225 / 10%);
}

.activities-body::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: linear-gradient(45deg, #4169e1, #6495ed);
}

.activity-fragment {
  position: relative;
  padding: 12px;
  border-radius: 12px 0;
  background: linear-gradient(135deg,
    rgb(200 162 200 / 10%),
    rgb(32 178 170 / 10%),
    rgb(65 105 225 / 10%)
  );
  transform: skew(-1deg);
  transition: all 0.3s ease;
  margin-bottom: 8px;
  border-left: 4px solid;
}

.activity-fragment:nth-child(4n+1) {
  border-color: #c8a2c8;
}

.activity-fragment:nth-child(4n+2) {
  border-color: #20b2aa;
}

.activity-fragment:nth-child(4n+3) {
  border-color: #4169e1;
}

.activity-fragment:nth-child(4n+4) {
  border-color: #dda0dd;
}

.activity-fragment:hover {
  box-shadow: 0 3px 10px rgb(32 178 170 / 30%);
  transform: skew(-1deg) scale(1.02);
}

.activity-time {
  font-size: 0.8rem;
  font-weight: bold;
  color: #8e44ad;
  transform: skew(2deg);
  margin-bottom: 4px;
}

.activity-content {
  font-size: 0.9rem;
  font-weight: 600;
  color: #2c3e50;
  transform: skew(-1deg);
}

.activity-decoration {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s ease-in-out infinite;
}

.activity-fragment:nth-child(4n+1) .activity-decoration {
  background: #c8a2c8;
}

.activity-fragment:nth-child(4n+2) .activity-decoration {
  background: #20b2aa;
}

.activity-fragment:nth-child(4n+3) .activity-decoration {
  background: #4169e1;
}

.activity-fragment:nth-child(4n+4) .activity-decoration {
  background: #dda0dd;
}

@keyframes pulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

/* 响应式设计 */
@media (width <= 768px) {
  .picasso-dashboard {
    left: 0;
    width: 100vw;
    grid-template:
      "header" auto "stats" auto "charts" auto "activities" 1fr / 1fr;
    padding: 15px;
  }

  .stats-cubism {
    grid-template-columns: repeat(2, 1fr);
  }

  .charts-cubism {
    grid-template-columns: 1fr;
  }

  .title-layer {
    font-size: 2rem;
  }

  .subtitle-fragment {
    font-size: 1rem;
  }
}

/* 🎨 Toast通知组件样式 - 符合CI_CD_STANDARDS.md用户反馈规范 */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  max-width: 500px;
  min-width: 300px;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow:
    0 8px 32px rgb(0 0 0 / 20%),
    0 4px 16px rgb(0 0 0 / 10%);
  backdrop-filter: blur(10px);
  animation: slideInRight 0.3s ease-out;
}

.toast-success {
  border: 2px solid rgb(34 197 94 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(34 197 94 / 90%), rgb(22 163 74 / 90%));
}

.toast-error {
  border: 2px solid rgb(239 68 68 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(239 68 68 / 90%), rgb(220 38 38 / 90%));
}

.toast-warning {
  border: 2px solid rgb(245 158 11 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(245 158 11 / 90%), rgb(217 119 6 / 90%));
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toast-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.toast-message {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
