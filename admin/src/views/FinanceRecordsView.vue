<template>
  <div class="picasso-records">
<!-- 毕加索风格操作栏 -->
    <div class="action-toolbar">
      <div class="search-cubism">
        <input 
          type="text" 
          placeholder="搜索交易记录..."
          v-model="searchValue"
          @input="handleSearch"
          class="search-fragment"
        />
      </div>
      
      <div class="filter-cubism">
        <select 
          v-model="typeFilter"
          @change="handleFilterChange"
          class="filter-fragment"
        >
          <option value="">所有类型</option>
          <option value="income">收入</option>
          <option value="expense">支出</option>
        </select>

        <input 
          type="date" 
          v-model="dateFilter"
          @change="handleFilterChange"
          class="filter-fragment"
        />
      </div>

      <div class="action-cubism">
        <div class="action-cube refresh-cube" @click="loadRecords">
          <div class="cube-face">
            <span class="cube-icon">🔄</span>
            <span class="cube-text">刷新</span>
          </div>
        </div>
        <div class="action-cube add-cube" @click="showAddModal">
          <div class="cube-face">
            <span class="cube-icon">➕</span>
            <span class="cube-text">新增记录</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格数据表格 -->
    <div class="data-cubism">
      <div class="table-container">
        <!-- 表头 -->
        <div class="table-header">
          <div class="header-cell" style="flex: 0.5;">ID</div>
          <div class="header-cell" style="flex: 1;">类型</div>
          <div class="header-cell" style="flex: 2;">描述</div>
          <div class="header-cell" style="flex: 1.5;">金额</div>
          <div class="header-cell" style="flex: 1.5;">时间</div>
          <div class="header-cell" style="flex: 1;">分类</div>
          <div class="header-cell" style="flex: 1.5;">操作</div>
        </div>

        <!-- 数据行 -->
        <div class="table-body">
          <div 
            v-for="record in paginatedData" 
            :key="record.id"
            class="data-row"
            :class="'row-' + record.type"
          >
            <div class="data-cell" style="flex: 0.5;">
              <div class="cell-content">{{ record.id }}</div>
            </div>
            
            <div class="data-cell" style="flex: 1;">
              <div class="type-fragment" :class="'type-' + record.type">
                <div class="type-icon">{{ record.type === 'income' ? '💰' : '💸' }}</div>
                <div class="type-text">{{ record.type === 'income' ? '收入' : '支出' }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 2;">
              <div class="description-fragment">
                <div class="description-title">{{ record.title }}</div>
                <div class="description-note">{{ record.description }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1.5;">
              <div class="amount-fragment" :class="'amount-' + record.type">
                <div class="amount-value">{{ record.type === 'income' ? '+' : '-' }}¥{{ record.amount }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1.5;">
              <div class="time-fragment">
                <div class="date-part">{{ record.date }}</div>
                <div class="time-part">{{ record.time }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1;">
              <div class="category-fragment">
                <div class="category-tag">{{ record.category }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1.5;">
              <div class="action-fragments">
                <div class="action-btn edit" @click="handleEdit(record)">编辑</div>
                <div class="action-btn delete" @click="handleDelete(record)">删除</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 梵高风格分页组件 - 符合CI_CD_STANDARDS.md -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span class="total-info">
            共 <span class="highlight-number">{{ totalRecords }}</span> 条记录，
            第 <span class="highlight-number">{{ currentPage }}</span> /
            <span class="highlight-number">{{ totalPages }}</span> 页
          </span>
        </div>

        <div class="pagination-controls">
          <div class="page-size-selector">
            <label class="page-size-label">每页显示：</label>
            <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
              <option value="5">5条</option>
              <option value="10">10条</option>
              <option value="20">20条</option>
              <option value="50">50条</option>
            </select>
          </div>

          <div class="page-navigation" v-if="totalRecords > 0">
            <button
              class="page-btn prev-btn"
              @click="prevPage"
              :disabled="currentPage === 1"
             aria-label="操作按钮">
              ‹ 上一页
            </button>

            <div class="page-numbers">
              <button
                v-for="page in visiblePages"
                :key="page"
                class="page-btn page-number"
                :class="{ active: page === currentPage }"
                @click="goToPage(page)"
               aria-label="操作按钮">
                {{ page }}
              </button>
            </div>

            <button
              class="page-btn next-btn"
              @click="nextPage"
              :disabled="currentPage === totalPages"
             aria-label="操作按钮">
              下一页 ›
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格模态框 -->
    <div v-if="modalVisible" class="modal-overlay" @click="hideModal">
      <div class="modal-cubism" @click.stop>
        <div class="modal-header">
          <div class="header-title">{{ modalTitle }}</div>
          <div class="close-btn" @click="hideModal">×</div>
        </div>
        
        <div class="modal-body">
          <div class="form-cubism">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">交易类型</label>
                <select v-model="formState.type" class="form-select">
                  <option value="income">收入</option>
                  <option value="expense">支出</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">金额 <span class="required">*</span></label>
                <input
                  type="number"
                  v-model="formState.amount"
                  class="form-input"
                  :class="{ 'error': formErrors.amount }"
                  placeholder="请输入金额"
                  min="0"
                  step="0.01"
                  required
                aria-label="输入字段">
                <div v-if="formErrors.amount" class="error-message">{{ formErrors.amount }}</div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">标题 <span class="required">*</span></label>
                <input
                  type="text"
                  v-model="formState.title"
                  class="form-input"
                  :class="{ 'error': formErrors.title }"
                  placeholder="请输入标题"
                  required
                aria-label="输入字段">
                <div v-if="formErrors.title" class="error-message">{{ formErrors.title }}</div>
              </div>

              <div class="form-group">
                <label class="form-label">分类 <span class="required">*</span></label>
                <select
                  v-model="formState.category"
                  class="form-select"
                  :class="{ 'error': formErrors.category }"
                  required
                >
                  <option value="">请选择分类</option>
                  <option value="服务收入">服务收入</option>
                  <option value="产品销售">产品销售</option>
                  <option value="房租水电">房租水电</option>
                  <option value="设备采购">设备采购</option>
                  <option value="员工工资">员工工资</option>
                  <option value="其他">其他</option>
                </select>
                <div v-if="formErrors.category" class="error-message">{{ formErrors.category }}</div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">日期 <span class="required">*</span></label>
                <input
                  type="date"
                  v-model="formState.date"
                  class="form-input"
                  :class="{ 'error': formErrors.date }"
                  required
                aria-label="输入字段">
                <div v-if="formErrors.date" class="error-message">{{ formErrors.date }}</div>
              </div>
              
              <div class="form-group">
                <label class="form-label">时间</label>
                <input 
                  type="time" 
                  v-model="formState.time" 
                  class="form-input"
                aria-label="输入字段">
              </div>
            </div>

            <div class="form-row full-width">
              <div class="form-group">
                <label class="form-label">详细描述</label>
                <textarea 
                  v-model="formState.description" 
                  class="form-textarea"
                  placeholder="请输入详细描述..."
                  rows="3"
                ></textarea>
              </div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <div class="footer-actions">
            <div class="action-btn cancel" @click="hideModal">
              <div class="btn-face">取消</div>
            </div>
            <div class="action-btn confirm" @click="handleSubmit">
              <div class="btn-face">{{ confirmLoading ? '提交中...' : '确定' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast通知组件 - 符合CI_CD_STANDARDS.md用户反馈规范 -->
  <div v-if="toastState.visible" class="toast-notification" :class="'toast-' + toastState.type">
    <div class="toast-content">
      <div class="toast-icon">
        <span v-if="toastState.type === 'success'">✅</span>
        <span v-else-if="toastState.type === 'error'">❌</span>
        <span v-else-if="toastState.type === 'warning'">⚠️</span>
      </div>
      <div class="toast-message">{{ toastState.message }}</div>
    </div>
  </div>
</template>

<script setup>
// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

import { ref, reactive, computed, onMounted , nextTick, shallowRef, watchEffect } from 'vue';;

// 响应式数据
const records = ref([]);
const modalVisible = ref(false);
const modalTitle = ref('新增记录');
const confirmLoading = ref(false);
const searchValue = ref('');
const typeFilter = ref('');
const dateFilter = ref('');
const currentPage = ref(1);
const pageSize = ref(5); // 默认5条每页，符合开发规范

// 表单状态
const formState = reactive({
  id: null,
  type: 'income',
  title: '',
  description: '',
  amount: null,
  category: '',
  date: '',
  time: '',
});

// Toast通知状态 - 符合CI_CD_STANDARDS.md用户反馈规范
const toastState = reactive({
  visible: false,
  message: '',
  type: 'success' // success, error, warning
});

// Toast通知函数 - 替代console日志
const showToast = (message, type = 'success') => {
  toastState.message = message;
  toastState.type = type;
  toastState.visible = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    toastState.visible = false;
  }, 3000);
};

// 表单验证错误状态 - 符合CI_CD_STANDARDS.md表单验证规范
const formErrors = reactive({
  title: '',
  amount: '',
  category: '',
  date: ''
});

// 表单验证函数 - 符合CI_CD_STANDARDS.md表单验证规范
const validateForm = () => {
  // 清空之前的错误
  Object.assign(formErrors, {
    title: '',
    amount: '',
    category: '',
    date: ''
  });

  let isValid = true;

  // 验证标题
  if (!formState.title?.trim()) {
    formErrors.title = '请输入记录标题';
    isValid = false;
  }

  // 验证金额
  if (!formState.amount || formState.amount <= 0) {
    formErrors.amount = '请输入正确的金额';
    isValid = false;
  }

  // 验证分类
  if (!formState.category?.trim()) {
    formErrors.category = '请选择分类';
    isValid = false;
  }

  // 验证日期
  if (!formState.date?.trim()) {
    formErrors.date = '请选择日期';
    isValid = false;
  }

  return isValid;
};

// 加载状态管理 - 符合CI_CD_STANDARDS.md加载状态规范
const loadingStates = reactive({
  submitLoading: false,  // 提交加载状态
  dataLoading: false     // 数据加载状态
});

// 计算属性
const totalRecords = computed(() => records.value.length);
const totalPages = computed(() => Math.ceil(records.value.length / pageSize.value));
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return records.value.slice(start, end);
});

const visiblePages = computed(() => {
  const pages = [];
  const total = totalPages.value;
  const current = currentPage.value;
  
  for (let i = Math.max(1, current - 2); i <= Math.min(total, current + 2); i++) {
    pages.push(i);
  }
  return pages;
});

// 方法
const loadRecords = async () => {
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 500));

  records.value = [
    {
      id: 1,
      type: 'income',
      title: '颈椎推拿服务费',
      description: '张女士颈椎推拿服务',
      amount: 120,
      category: '服务收入',
      date: '2024-01-15',
      time: '10:30'
    },
    {
      id: 2,
      type: 'income',
      title: '全身推拿服务费',
      description: '李先生全身推拿服务',
      amount: 180,
      category: '服务收入',
      date: '2024-01-15',
      time: '11:15'
    },
    {
      id: 3,
      type: 'expense',
      title: '购买按摩油',
      description: '采购专业按摩精油10瓶',
      amount: 350,
      category: '设备采购',
      date: '2024-01-15',
      time: '12:00'
    },
    {
      id: 4,
      type: 'income',
      title: '足疗保健服务费',
      description: '王女士足疗保健服务',
      amount: 100,
      category: '服务收入',
      date: '2024-01-15',
      time: '13:30'
    },
    {
      id: 5,
      type: 'expense',
      title: '水电费',
      description: '1月份店铺水电费用',
      amount: 280,
      category: '房租水电',
      date: '2024-01-15',
      time: '14:15'
    }
  ];
};

const showAddModal = () => {
  modalTitle.value = '新增记录';
  Object.assign(formState, {
    id: null,
    type: 'income',
    title: '',
    description: '',
    amount: null,
    category: '',
    date: new Date().toISOString().split('T')[0],
    time: new Date().toTimeString().split(' ')[0].substring(0, 5)
  });
  modalVisible.value = true;
};

const hideModal = () => {
  modalVisible.value = false;
  confirmLoading.value = false;
};

const handleEdit = (record) => {
  modalTitle.value = '编辑记录';
  Object.assign(formState, record);
  modalVisible.value = true;
};

const handleDelete = (record) => {
  if (confirm('确定要删除这条记录吗？')) {
    const index = records.value.findIndex(item => item.id === record.id);
    if (index !== -1) {
      records.value.splice(index, 1);
    }
    console.log('删除记录:', record);
  }
};

const handleSearch = () => {
  console.log('搜索:', searchValue.value);
};

const handleFilterChange = () => {
  console.log('筛选:', { type: typeFilter.value, date: dateFilter.value });
};

const handleSubmit = async () => {
  try {
    // 表单验证 - 符合CI_CD_STANDARDS.md表单验证规范
    if (!validateForm()) {
      showToast('请检查输入内容', 'error');
      return;
    }

    confirmLoading.value = true;
    loadingStates.submitLoading = true;
    await new Promise(resolve => setTimeout(resolve, 1000));

    if (formState.id) {
      const index = records.value.findIndex(item => item.id === formState.id);
      if (index !== -1) {
        records.value[index] = { ...formState };
        showToast('财务记录更新成功', 'success');
      }
    } else {
      records.value.unshift({
        ...formState,
        id: Date.now()
      });
      showToast('财务记录添加成功', 'success');
    }

    hideModal();
  } catch (error) {
    console.error('财务记录操作失败:', error);
    showToast('操作失败，请重试', 'error');
  } finally {
    confirmLoading.value = false;
    loadingStates.submitLoading = false;
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

const handlePageSizeChange = () => {
  currentPage.value = 1; // 重置到第一页
};

// 初始化
onMounted(() => {
  loadRecords();
});
</script>

<style scoped>
/* 继承预约管理页面的毕加索风格样式 */
.picasso-records {
  display: flex;
  position: fixed;
  inset: 0 0 0 180px;
  width: calc(100vw - 180px);
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;
  background: linear-gradient(45deg,
    #8e44ad 0%, #9b59b6 25%, #c8a2c8 50%, #dda0dd 75%, #e6e6fa 100%
  );
  background-size: 400% 400%;
  animation: picassoFlow 20s ease infinite;
  flex-direction: column;
}

@keyframes picassoFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 0% 50%; }
  100% { background-position: 50% 0%; }
}

/* 继承毕加索风格基础样式 */

/* 已删除无用的标题样式 */

/* 毕加索风格操作栏 */
.action-toolbar {
  display: flex;
  position: relative;
  z-index: 10;
  gap: 20px;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.search-cubism {
  flex: 1;
}

.search-fragment {
  width: 100%;
  padding: 12px 20px;
  border: none;
  border: 2px solid rgb(255 182 193 / 40%);
  border-radius: 0 25px;
  font-size: 1rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg,
    rgb(255 182 193 / 80%),
    rgb(255 218 185 / 70%),
    rgb(255 255 255 / 90%)
  );
  box-shadow: 3px 3px 10px rgb(255 182 193 / 30%);
  transform: skew(-3deg);
  transition: all 0.3s ease;
}

.search-fragment:focus {
  box-shadow: 5px 5px 15px rgb(142 68 173 / 40%);
  transform: skew(-3deg) scale(1.02);
  outline: none;
}

.filter-cubism {
  display: flex;
  gap: 10px;
}

.filter-fragment {
  padding: 12px 15px;
  border: none;
  border: 2px solid rgb(173 216 230 / 40%);
  border-radius: 0 20px;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg,
    rgb(173 216 230 / 80%),
    rgb(135 206 235 / 70%),
    rgb(255 255 255 / 90%)
  );
  box-shadow: 3px 3px 10px rgb(173 216 230 / 30%);
  transform: skew(-3deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.filter-fragment:focus {
  box-shadow: 5px 5px 15px rgb(142 68 173 / 40%);
  transform: skew(-3deg) scale(1.02);
  outline: none;
}

.action-cubism {
  display: flex;
  gap: 10px;
}

.action-cube {
  width: 120px;
  height: 45px;
  border-radius: 15px 0;
  transform: perspective(600px) rotateX(15deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-cube:hover {
  transform: perspective(600px) rotateX(15deg) scale(1.05);
}

.refresh-cube {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
}

.add-cube {
  background: linear-gradient(45deg, #4ecdc4, #45b7d1);
}

.cube-face {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 15px 0;
  font-weight: bold;
  color: white;
  box-shadow: 0 5px 15px rgb(0 0 0 / 30%);
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.cube-icon {
  font-size: 1.2rem;
}

.cube-text {
  font-size: 0.9rem;
}

/* 毕加索风格数据表格 */
.data-cubism {
  display: flex;
  position: relative;
  z-index: 5;
  padding: 20px;
  border-radius: 20px;
  overflow: hidden;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 20px 40px rgb(155 89 182 / 30%);
  transform: perspective(1000px) rotateX(3deg);
  flex: 1;
  flex-direction: column;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-header {
  display: flex;
  padding: 15px 0;
  border-radius: 15px 15px 0 0;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
  margin-bottom: 10px;
  flex-shrink: 0;
}

.header-cell {
  display: flex;
  padding: 0 10px;
  font-size: 0.9rem;
  font-weight: 900;
  color: white;
  transform: rotate(1deg);
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 2px 2px 4px rgb(0 0 0 / 30%);
}

.table-body {
  max-height: calc(100vh - 320px); /* 减去头部、搜索栏、分页等空间 */

  /* 确保5行数据能够完整显示，避免内容被遮挡 */
  min-height: 350px; /* 5行 × 约70px行高 = 350px */
  flex: 1;
  overflow: hidden auto;

  /* 毕加索风格滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #9b59b6 rgb(155 89 182 / 20%);
}

.table-body::-webkit-scrollbar {
  width: 8px;
}

.table-body::-webkit-scrollbar-track {
  border-radius: 8px;
  background: linear-gradient(45deg, rgb(142 68 173 / 10%), rgb(155 89 182 / 10%));
}

.table-body::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: linear-gradient(45deg, #8e44ad, #9b59b6);
  transition: all 0.3s ease;
}

.table-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #639, #8e44ad);
}

.data-row {
  display: flex;
  min-height: 60px;
  border-radius: 10px;
  background: linear-gradient(90deg,
    rgb(142 68 173 / 10%),
    rgb(155 89 182 / 10%),
    rgb(200 162 200 / 10%)
  );
  transform: skew(-0.5deg);
  transition: all 0.3s ease;
  margin-bottom: 8px;
}

.data-row:hover {
  background: linear-gradient(90deg,
    rgb(142 68 173 / 20%),
    rgb(155 89 182 / 20%),
    rgb(200 162 200 / 20%)
  );
  box-shadow: 0 5px 15px rgb(155 89 182 / 30%);
  transform: skew(-0.5deg) scale(1.01);
}

.row-income {
  border-left: 4px solid #27ae60;
}

.row-expense {
  border-left: 4px solid #e74c3c;
}

.data-cell {
  display: flex;
  align-items: center;
  padding: 10px;
}

.cell-content {
  width: 100%;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
}

/* 毕加索风格财务类型 */
.type-fragment {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 12px 0;
  transform: skew(-1deg);
}

.type-income {
  border: 2px solid rgb(39 174 96 / 30%);
  background: linear-gradient(135deg, rgb(39 174 96 / 20%), rgb(255 255 255 / 80%));
}

.type-expense {
  border: 2px solid rgb(231 76 60 / 30%);
  background: linear-gradient(135deg, rgb(231 76 60 / 20%), rgb(255 255 255 / 80%));
}

.type-icon {
  font-size: 1.2rem;
  animation: typeFloat 3s ease-in-out infinite;
}

@keyframes typeFloat {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-2px); }
}

.type-text {
  font-size: 0.9rem;
  font-weight: bold;
}

.type-income .type-text {
  color: #27ae60;
}

.type-expense .type-text {
  color: #e74c3c;
}

/* 毕加索风格描述信息 */
.description-fragment {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.description-title {
  font-size: 1rem;
  font-weight: bold;
  color: #2c3e50;
  transform: skew(-1deg);
}

.description-note {
  font-size: 0.8rem;
  line-height: 1.3;
  color: #7f8c8d;
  transform: skew(1deg);
}

/* 毕加索风格金额显示 */
.amount-fragment {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px 12px;
  border-radius: 12px 0;
  transform: skew(-1deg);
}

.amount-income {
  border: 2px solid rgb(39 174 96 / 30%);
  background: linear-gradient(135deg, rgb(39 174 96 / 20%), rgb(255 255 255 / 80%));
}

.amount-expense {
  border: 2px solid rgb(231 76 60 / 30%);
  background: linear-gradient(135deg, rgb(231 76 60 / 20%), rgb(255 255 255 / 80%));
}

.amount-value {
  font-size: 1.1rem;
  font-weight: bold;
  animation: amountPulse 2s ease-in-out infinite;
}

@keyframes amountPulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.05); }
}

.amount-income .amount-value {
  color: #27ae60;
}

.amount-expense .amount-value {
  color: #e74c3c;
}

/* 毕加索风格时间显示 */
.time-fragment {
  display: flex;
  padding: 8px;
  border-radius: 12px 0;
  background: linear-gradient(135deg, rgb(142 68 173 / 20%), rgb(255 255 255 / 80%));
  transform: skew(-1deg);
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.date-part {
  font-size: 0.9rem;
  font-weight: bold;
  color: #8e44ad;
}

.time-part {
  font-size: 0.8rem;
  color: #7f8c8d;
}

/* 毕加索风格分类标签 */
.category-fragment {
  display: flex;
  align-items: center;
  justify-content: center;
}

.category-tag {
  padding: 6px 12px;
  border: 1px solid rgb(155 89 182 / 30%);
  border-radius: 12px 0;
  font-size: 0.8rem;
  font-weight: bold;
  color: #9b59b6;
  background: linear-gradient(135deg, rgb(155 89 182 / 20%), rgb(255 255 255 / 80%));
  transform: skew(-1deg);
}

/* 毕加索风格操作按钮 */
.action-fragments {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: center;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 8px 0;
  font-size: 0.8rem;
  font-weight: bold;
  color: white;
  transform: perspective(200px) rotateX(8deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-btn:hover {
  transform: perspective(200px) rotateX(8deg) scale(1.05);
}

.action-btn.edit {
  background: linear-gradient(45deg, #8e44ad, #9b59b6);
}

.action-btn.delete {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
}

/* 🎨 梵高风格分页组件 - 符合CI_CD_STANDARDS.md */
.pagination-container {
  display: flex;
  padding: 12px 18px;
  border: 2px solid rgb(192 132 252 / 40%);
  border-radius: 12px;
  background: linear-gradient(135deg, rgb(139 92 246 / 10%), rgb(168 85 247 / 10%));
  box-shadow:
    0 4px 8px rgb(139 92 246 / 20%),
    0 2px 4px rgb(192 132 252 / 10%);
  margin-top: 15px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  font-size: 14px;
  font-weight: 600;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.highlight-number {
  font-size: 16px;
  font-weight: 700;
  color: var(--van-gogh-accent);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 40%);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-label {
  font-size: 13px;
  font-weight: 600;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.page-size-select {
  padding: 6px 12px;
  border: 2px solid var(--van-gogh-accent);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-primary-dark), var(--van-gogh-secondary));
  box-shadow: 0 2px 4px rgb(139 92 246 / 30%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.page-size-select:hover {
  border-color: var(--van-gogh-primary);
  box-shadow: 0 4px 8px rgb(139 92 246 / 40%);
}

.page-size-select option {
  padding: 8px;
  color: white;
  background: var(--van-gogh-primary-dark);
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  padding: 8px 12px;
  border: 2px solid var(--van-gogh-primary);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-secondary), var(--van-gogh-secondary-dark));
  box-shadow: 0 2px 4px rgb(139 92 246 / 30%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.page-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--van-gogh-primary), var(--van-gogh-primary-dark));
  box-shadow: 0 4px 8px rgb(139 92 246 / 40%);
  transform: translateY(-1px);
}

.page-btn:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  border-color: #d1d5db;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.page-btn.active {
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-accent), var(--van-gogh-accent-dark));
  box-shadow:
    0 4px 8px rgb(244 114 182 / 40%),
    inset 1px 1px 2px rgb(255 255 255 / 30%);
  border-color: var(--van-gogh-accent-light);
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  min-width: 36px;
  text-align: center;
}

/* 毕加索风格模态框 */
.modal-overlay {
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background: rgb(142 68 173 / 70%);
  align-items: center;
  justify-content: center;
}

.modal-cubism {
  width: 85%;
  max-width: min(700px, calc(100vw - 220px));
  max-height: 80vh;
  border-radius: 20px 5px;
  overflow: hidden;
  background: linear-gradient(135deg, #8e44ad, #9b59b6, #c8a2c8, #dda0dd);
  box-shadow: 0 25px 50px rgb(155 89 182 / 40%);
  transform: perspective(800px) rotateY(3deg);
  background-size: 400% 400%;
  animation: modalFlow 8s ease infinite;
}

@keyframes modalFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

/* 响应式设计 */
@media (width <= 768px) {
  .picasso-records {
    left: 0;
    width: 100vw;
    padding: 15px;
  }

  .action-toolbar {
    flex-direction: column;
    gap: 15px;
  }

  .filter-cubism {
    flex-direction: column;
  }

  .action-cubism {
    justify-content: center;
  }

  .data-row {
    flex-direction: column;
    min-height: auto;
  }

  .data-cell {
    border-bottom: 1px solid rgb(142 68 173 / 20%);
    min-height: 40px;
  }

  .title-layer {
    font-size: 2rem;
  }

  .subtitle-fragment {
    font-size: 1rem;
  }

  .action-fragments {
    justify-content: center;
  }

  /* 移动端梵高分页样式 */
  .pagination-container {
    flex-direction: column;
    gap: 10px;
    padding: 12px 15px;
  }

  .pagination-controls {
    gap: 15px;
  }

  .page-navigation {
    gap: 6px;
  }

  .page-btn {
    padding: 6px 10px;
    font-size: 12px;
  }

  .page-number {
    min-width: 32px;
  }
}

/* 🎨 表单验证样式 - 符合CI_CD_STANDARDS.md表单验证规范 */
.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: #ef4444;
  box-shadow:
    0 0 0 3px rgb(239 68 68 / 20%),
    0 4px 12px rgb(239 68 68 / 20%);
}

.required {
  font-weight: bold;
  color: #ef4444;
}

.error-message {
  font-size: 0.85rem;
  font-weight: 500;
  color: #ef4444;
  margin-top: 4px;
}

/* 🎨 Toast通知组件样式 - 符合CI_CD_STANDARDS.md用户反馈规范 */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  max-width: 500px;
  min-width: 300px;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow:
    0 8px 32px rgb(0 0 0 / 20%),
    0 4px 16px rgb(0 0 0 / 10%);
  backdrop-filter: blur(10px);
  animation: slideInRight 0.3s ease-out;
}

.toast-success {
  border: 2px solid rgb(34 197 94 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(34 197 94 / 90%), rgb(22 163 74 / 90%));
}

.toast-error {
  border: 2px solid rgb(239 68 68 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(239 68 68 / 90%), rgb(220 38 38 / 90%));
}

.toast-warning {
  border: 2px solid rgb(245 158 11 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(245 158 11 / 90%), rgb(217 119 6 / 90%));
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toast-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.toast-message {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
