<template>
  <div class="picasso-services">
    <!-- 科幻通知组件 -->
    <SciFiNotification ref="notification" />



    <!-- 毕加索风格数据表格 -->
    <div class="data-cubism" :style="{ height: dynamicTableHeight + 'px' }">
      <div class="table-container">
        <!-- 集成操作功能的智能表头 -->
        <div class="smart-table-header">

          <!-- 列标题（支持搜索和排序） -->
          <div class="header-columns">
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.name }" style="flex: 2.2;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.name" class="header-search-container" @mouseleave="handleSearchMouseLeave('name')">
                <input
                  :ref="el => searchInputRefs.name = el"
                  type="text"
                  placeholder="🔍 搜索服务（支持汉字/拼音）..."
                  v-model="searchValues.name"
                  @input="handleSearchInput('name')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('name')"
                  @keydown.enter="handleSearchEnter($event, 'name')"
                  @keydown.esc="exitSearchMode('name')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('name')">
                <span class="header-text">服务信息</span>
              </div>
              <!-- 动态按钮：有输入内容时显示关闭按钮，加载时显示加载状态，否则显示排序按钮 -->
              <button v-if="searchValues.name && searchValues.name.trim()" @click="exitSearchMode('name')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.name.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('name')" :disabled="sortButtonStates.name.disabled" title="排序服务信息">
                <span class="sort-indicator" :class="getSortClass('name')">
                  {{ getSortIcon('name') }}
                </span>
              </button>
            </div>
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.price }" style="flex: 1;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.price" class="header-search-container" @mouseleave="handleSearchMouseLeave('price')">
                <input
                  :ref="el => searchInputRefs.price = el"
                  type="text"
                  placeholder="🔍 搜索服务费..."
                  v-model="searchValues.price"
                  @input="handleSearchInput('price')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('price')"
                  @keydown.enter="handleSearchEnter($event, 'price')"
                  @keydown.esc="exitSearchMode('price')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('price')">
                <span class="header-text">服务费</span>
              </div>
              <!-- 动态按钮：有输入内容时显示关闭按钮，加载时显示加载状态，否则显示排序按钮 -->
              <button v-if="searchValues.price && searchValues.price.trim()" @click="exitSearchMode('price')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.price.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('price')" :disabled="sortButtonStates.price.disabled" title="排序服务费">
                <span class="sort-indicator" :class="getSortClass('price')">
                  {{ getSortIcon('price') }}
                </span>
              </button>
            </div>
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.commission }" style="flex: 1;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.commission" class="header-search-container" @mouseleave="handleSearchMouseLeave('commission')">
                <input
                  :ref="el => searchInputRefs.commission = el"
                  type="text"
                  placeholder="🔍 搜索提成..."
                  v-model="searchValues.commission"
                  @input="handleSearchInput('commission')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('commission')"
                  @keydown.enter="handleSearchEnter($event, 'commission')"
                  @keydown.esc="exitSearchMode('commission')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('commission')">
                <span class="header-text">提成</span>
              </div>
              <!-- 动态按钮：有输入内容时显示关闭按钮，加载时显示加载状态，否则显示排序按钮 -->
              <button v-if="searchValues.commission && searchValues.commission.trim()" @click="exitSearchMode('commission')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.commission.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('commission')" :disabled="sortButtonStates.commission.disabled" title="排序提成">
                <span class="sort-indicator" :class="getSortClass('commission')">
                  {{ getSortIcon('commission') }}
                </span>
              </button>
            </div>
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.duration }" style="flex: 0.9;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.duration" class="header-search-container" @mouseleave="handleSearchMouseLeave('duration')">
                <input
                  :ref="el => searchInputRefs.duration = el"
                  type="text"
                  placeholder="🔍 搜索时长..."
                  v-model="searchValues.duration"
                  @input="handleSearchInput('duration')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('duration')"
                  @keydown.enter="handleSearchEnter($event, 'duration')"
                  @keydown.esc="exitSearchMode('duration')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('duration')">
                <span class="header-text">时长</span>
              </div>
              <!-- 动态按钮：有输入内容时显示关闭按钮，加载时显示加载状态，否则显示排序按钮 -->
              <button v-if="searchValues.duration && searchValues.duration.trim()" @click="exitSearchMode('duration')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.duration.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('duration')" :disabled="sortButtonStates.duration.disabled" title="排序时长">
                <span class="sort-indicator" :class="getSortClass('duration')">
                  {{ getSortIcon('duration') }}
                </span>
              </button>
            </div>
            <div class="header-cell" style="flex: 0.9;">
              <!-- 状态筛选按钮 - 与其他表头保持一致的结构 -->
              <div class="header-normal-container">
                <span class="header-text">状态</span>
              </div>
              <!-- 状态筛选按钮 - 替代排序按钮的位置 -->
              <button
                class="status-filter-btn"
                @click="toggleStatusFilter"
                @keydown.enter="toggleStatusFilter"
                @keydown.space.prevent="toggleStatusFilter"
                :class="getStatusFilterClass()"
                :title="getStatusFilterTitle()"
                aria-label="状态筛选按钮"
                tabindex="0">
                <span class="filter-icon">{{ getStatusFilterIcon() }}</span>
                <span class="filter-text">{{ getStatusFilterLabel() }}</span>
              </button>
            </div>
            <div class="header-cell" style="flex: 2;">
              <span>操作</span>
              <button class="header-add-btn-small" @click="showAddModal" title="新增服务">
                <span class="add-icon">➕</span>
                <span class="add-text">新增</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 数据行 -->
        <div class="table-body" :class="{ 'hide-scrollbar': shouldHideScrollbar, 'scroll-snap': !shouldHideScrollbar }">
          <!-- 数据加载状态 -->
          <div v-if="loadingStates.dataLoading" class="table-loading-container">
            <div class="table-loading-content">
              <div class="loading-spinner-large">⏳</div>
              <div class="loading-text">正在加载服务数据...</div>
            </div>
          </div>

          <!-- 无数据状态 -->
          <div v-else-if="paginatedData.length === 0" class="table-empty-container">
            <div class="table-empty-content">
              <div class="empty-icon">📋</div>
              <div class="empty-text">暂无服务数据</div>
            </div>
          </div>

          <!-- 数据内容 -->
          <div v-else
            v-for="record in paginatedData"
            :key="record.id"
            class="data-row service-row scroll-snap-item"
          >
            <div class="data-cell service-info-cell" style="flex: 2.2;" data-label="服务信息">
              <!-- 有图片时显示图片，没有图片时不显示任何图标 -->
              <div v-if="record.image" class="service-image">
                <img :src="record.image" :alt="record.name" @error="handleServiceImageError" />
              </div>
              <div class="service-info" :class="{ 'no-image': !record.image }">
                <div class="service-name">{{ record.name }}</div>
              </div>
            </div>

            <!-- 服务费列 -->
            <div class="data-cell" style="flex: 1;" data-label="服务费">
              <div class="cell-content">
                <div class="price-cell clickable" @click="showUnifiedEditModal(record, 'price')" :title="'点击编辑服务费'" :class="{'loading': loadingStates.priceEdit === `${record.id}_price`}">
                  <div class="price-value">
                    <span v-if="loadingStates.priceEdit !== `${record.id}_price`">¥{{ record.price }}</span>
                    <span v-else class="loading-spinner">⏳</span>
                  </div>
                </div>
              </div>
            </div>

            <!-- 提成列 -->
            <div class="data-cell" style="flex: 1;" data-label="提成">
              <div class="cell-content">
                <div class="commission-cell clickable" @click="showUnifiedEditModal(record, 'commission')" :title="'点击编辑提成'" :class="{'loading': loadingStates.priceEdit === `${record.id}_commission`}">
                  <div class="commission-value">
                    <span v-if="loadingStates.priceEdit !== `${record.id}_commission`">¥{{ record.commission }}</span>
                    <span v-else class="loading-spinner">⏳</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="data-cell" style="flex: 0.9;" data-label="时长">
              <div class="cell-content">
                <div class="duration-cell clickable" @click="showUnifiedEditModal(record, 'duration')" :title="'点击编辑时长'" :class="{'loading': loadingStates.durationEdit === record.id}">
                  <div class="duration-fragment">
                    <div class="duration-icon">⏱️</div>
                    <div class="duration-text" v-if="loadingStates.durationEdit !== record.id">{{ record.duration }}分钟</div>
                    <span v-else class="loading-spinner">⏳</span>
                  </div>
                </div>
              </div>
            </div>

            <div class="data-cell" style="flex: 0.9;" data-label="状态">
              <div class="cell-content">
                <div class="status-fragment clickable" :class="'status-' + record.status" @click="handleToggleStatus(record)">
                  <div class="status-indicator"></div>
                  <div class="status-text">{{ getStatusText(record.status) }}</div>
                </div>
              </div>
            </div>

            <div class="data-cell" style="flex: 2;" data-label="操作">
              <div class="cell-content">
                <div class="action-fragments">
                  <div class="action-btn edit" @click="handleEdit(record)">
                    <span class="btn-icon">✏️</span>
                    <span class="btn-text">编辑</span>
                  </div>
                  <div class="action-btn delete" @click="handleSoftDelete(record)" :class="{'loading': loadingStates.serviceDelete === record.id}">
                    <span class="btn-icon" v-if="loadingStates.serviceDelete !== record.id">🗑️</span>
                    <span class="btn-icon loading-spinner" v-else>⏳</span>
                    <span class="btn-text">{{ loadingStates.serviceDelete === record.id ? '删除中...' : '删除' }}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 梵高风格分页组件 - 标准模板 -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span class="total-info">
            共 <span class="highlight-number">{{ totalRecords }}</span> 条记录
          </span>
        </div>

        <div class="pagination-controls">
          <div class="page-size-selector">
            <label class="page-size-label">每页显示：</label>
            <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
              <option value="10">10条</option>
              <option value="20">20条</option>
              <option value="50">50条</option>
            </select>
          </div>

          <div class="page-navigation" v-if="totalRecords > 0">
            <button
              class="page-btn prev-btn"
              @click="prevPage"
              :disabled="currentPage === 1"
             aria-label="操作按钮">
              ‹ 上一页
            </button>

            <div class="page-numbers">
              <button
                v-for="page in visiblePages"
                :key="page"
                class="page-btn page-number"
                :class="{ active: page === currentPage }"
                @click="goToPage(page)"
               aria-label="操作按钮">
                {{ page }}
              </button>
            </div>

            <button
              class="page-btn next-btn"
              @click="nextPage"
              :disabled="currentPage === totalPages"
             aria-label="操作按钮">
              下一页 ›
            </button>
          </div>
        </div>
      </div>
    </div>



    <!-- 梵高风格服务表单模态框 -->
    <div v-if="modalVisible" class="modal-overlay">
      <div class="service-form-modal" @click.stop>
        <div class="form-header">
          <h3 class="form-title">{{ modalTitle }}</h3>
          <button class="close-btn" @click="hideModal" aria-label="操作按钮">×</button>
        </div>

        <div class="form-content">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">服务名称 <span class="required">*</span></label>
              <input
                type="text"
                v-model="formState.name"
                @input="handleNameChange"
                @blur="handleNameBlur"
                class="form-input"
                :class="{ 'error': formErrors.name }"
                placeholder="请输入服务名称（输入完毕后自动生成简介和图片）"
                required
                aria-label="输入字段"
              />
              <div v-if="formErrors.name" class="error-message">{{ formErrors.name }}</div>
              <!-- 自动生成状态提示 -->
              <div v-if="isAutoGenerating" class="auto-generate-status">
                <div class="loading-spinner-small"></div>
                <span class="status-text">🤖 正在自动生成服务内容...</span>
              </div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">服务费 <span class="required">*</span></label>
              <input
                type="number"
                v-model="formState.price"
                class="form-input"
                :class="{ 'error': formErrors.price }"
                placeholder="请输入服务费"
                min="0"
                step="0.01"
                required
              aria-label="输入字段">
              <div v-if="formErrors.price" class="error-message">{{ formErrors.price }}</div>
            </div>

            <div class="form-group">
              <label class="form-label">提成 <span class="required">*</span></label>
              <input
                type="number"
                v-model="formState.commission"
                class="form-input"
                :class="{ 'error': formErrors.commission }"
                placeholder="请输入提成"
                min="0"
                step="0.01"
                required
              aria-label="输入字段">
              <div v-if="formErrors.commission" class="error-message">{{ formErrors.commission }}</div>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">服务时长 <span class="required">*</span></label>
              <select
                v-model.number="formState.duration"
                class="form-select"
                :class="{ 'error': formErrors.duration }"
                required
              >
                <option :value="30">30分钟</option>
                <option :value="45">45分钟</option>
                <option :value="60">60分钟</option>
                <option :value="75">75分钟</option>
                <option :value="90">90分钟</option>
                <option :value="120">120分钟</option>
              </select>
              <div v-if="formErrors.duration" class="error-message">{{ formErrors.duration }}</div>
            </div>

            <div class="form-group">
              <label class="form-label">服务状态</label>
              <select v-model="formState.status" class="form-select">
                <option value="active">上架</option>
                <option value="inactive">下架</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group description-group">
              <div class="form-label-with-ai">
                <label class="form-label">服务简介 <span class="required">*</span></label>
                <button
                  type="button"
                  @click="generateDescription"
                  class="ai-btn"
                  :disabled="!formState.name || !formState.duration || aiDescriptionLoading"
                  title="重新生成服务简介"
                 aria-label="操作按钮">
                  {{ aiDescriptionLoading ? '生成中...' : '🔄 重新生成' }}
                </button>
              </div>
              <textarea
                v-model="formState.description"
                class="form-textarea"
                :class="{ 'error': formErrors.description }"
                placeholder="请输入服务简介，包含服务起源、好处和作用等宣传内容..."
                rows="4"
                :disabled="aiDescriptionLoading"
                required
              ></textarea>
              <div v-if="formErrors.description" class="error-message">{{ formErrors.description }}</div>
            </div>

            <div class="form-group image-group">
              <div class="form-label-with-ai">
                <label class="form-label">服务图片</label>
                <button
                  type="button"
                  @click="generateWithVolcengine"
                  class="ai-btn volcengine"
                  :disabled="!formState.name || !formState.description || volcengineGenerating"
                  title="重新生成服务图片"
                 aria-label="操作按钮">
                  {{ volcengineGenerating ? '生成中...' : '🔄 重新生成' }}
                </button>
              </div>
              <div v-if="volcengineGenerating" class="ai-loading-status">
                <div class="loading-spinner"></div>
                <span class="loading-text">🌋 火山引擎正在生成图片...</span>
              </div>
              <div v-if="formState.image && !volcengineGenerating" class="image-preview">
                <img :src="formState.image" alt="服务图片预览" @error="handleImageError" />
              </div>
              <div v-if="!formState.image && !volcengineGenerating" class="image-placeholder">
                <div class="placeholder-icon">🖼️</div>
                <div class="placeholder-text">输入服务名称后自动生成图片，或点击重新生成</div>
              </div>
            </div>
          </div>



          <div class="form-actions">
            <button class="action-btn cancel-btn" @click="hideModal" aria-label="操作按钮">
              取消
            </button>
            <button class="action-btn confirm-btn" @click="handleSubmit" :disabled="confirmLoading" aria-label="操作按钮">
              {{ confirmLoading ? '提交中...' : '确定' }}
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 🎨 统一编辑界面 - 整合所有编辑功能 -->
  <div v-if="unifiedEditVisible" class="modal-overlay">
    <div class="unified-edit-modal" @click.stop>
      <div class="unified-edit-header">
        <h3 class="unified-edit-title">{{ unifiedEditTitle }}</h3>
        <button class="close-btn" @click="hideUnifiedEditModal" aria-label="关闭">×</button>
      </div>

      <div class="unified-edit-content">
        <!-- 🎯 左侧：服务信息展示区 -->
        <div class="service-info-section">
          <!-- 编辑模式：显示服务信息 -->
          <div v-if="unifiedEditForm.serviceId">
            <div class="service-header">
              <div class="service-image-container">
                <img v-if="unifiedEditForm.image" :src="unifiedEditForm.image" :alt="unifiedEditForm.name" class="service-image">
                <div v-else class="service-image-placeholder">
                  <div class="placeholder-icon">🖼️</div>
                </div>
              </div>
              <div class="service-basic-info">
                <h4 class="service-name">{{ unifiedEditForm.name }}</h4>
                <p class="service-description">{{ unifiedEditForm.description || '暂无描述' }}</p>
                <div class="service-status">
                  <span class="status-badge" :class="unifiedEditForm.status">
                    {{ unifiedEditForm.status === 'active' ? '上架' : '下架' }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 当前数据展示 -->
            <div class="current-data-section">
              <h5 class="section-title">当前数据</h5>
              <div class="data-list">
                <div class="data-item">
                  <span class="data-label">服务费</span>
                  <span class="data-value">¥{{ unifiedEditForm.currentPrice }}</span>
                </div>
                <div class="data-item">
                  <span class="data-label">提成</span>
                  <span class="data-value">¥{{ unifiedEditForm.currentCommission }}</span>
                </div>
                <div class="data-item">
                  <span class="data-label">时长</span>
                  <span class="data-value">{{ unifiedEditForm.currentDuration }}分钟</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 新增模式：显示基础信息表单 -->
          <div v-else class="service-form-section">
            <h5 class="form-section-title">基础信息</h5>

            <div class="form-group">
              <label class="form-label">服务名称 <span class="required">*</span></label>
              <input
                type="text"
                v-model="unifiedEditForm.name"
                class="form-input"
                placeholder="请输入服务名称"
                maxlength="50"
              >
            </div>

            <div class="form-group">
              <label class="form-label">服务状态</label>
              <div class="status-options">
                <label class="status-option">
                  <input type="radio" v-model="unifiedEditForm.status" value="active">
                  <span class="status-text">上架</span>
                </label>
                <label class="status-option">
                  <input type="radio" v-model="unifiedEditForm.status" value="inactive">
                  <span class="status-text">下架</span>
                </label>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">服务费 <span class="required">*</span></label>
                <input
                  type="number"
                  v-model="unifiedEditForm.newPrice"
                  class="form-input compact"
                  placeholder="服务费"
                  min="0"
                  step="0.01"
                >
              </div>

              <div class="form-group">
                <label class="form-label">提成 <span class="required">*</span></label>
                <input
                  type="number"
                  v-model="unifiedEditForm.newCommission"
                  class="form-input compact"
                  placeholder="提成"
                  min="0"
                  step="0.01"
                >
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">服务时长 <span class="required">*</span></label>
              <select
                v-model.number="unifiedEditForm.newDuration"
                class="form-select compact"
              >
                <option :value="30">30分钟</option>
                <option :value="45">45分钟</option>
                <option :value="60">60分钟</option>
                <option :value="75">75分钟</option>
                <option :value="90">90分钟</option>
                <option :value="120">120分钟</option>
              </select>
            </div>
          </div>
        </div>

        <!-- 🎯 右侧：编辑表单区 -->
        <div class="edit-form-section">
          <div class="edit-tabs">
            <button
              v-for="tab in editTabs"
              :key="tab.key"
              class="edit-tab"
              :class="{ active: activeEditTab === tab.key }"
              @click="switchEditTab(tab.key)"
            >
              <span class="tab-icon">{{ tab.icon }}</span>
              <span class="tab-label">{{ tab.label }}</span>
            </button>
          </div>

          <div class="edit-form-content">

            <!-- 🎯 综合编辑 -->
            <div v-if="activeEditTab === 'all'" class="edit-panel">
              <h5 class="panel-title">{{ !unifiedEditForm.serviceId ? '项目简介' : '编辑数据' }}</h5>

              <!-- 新增模式：项目简介 -->
              <div v-if="!unifiedEditForm.serviceId" class="description-section">
                <!-- 服务图片和项目简介 -->
                <div class="content-layout">
                  <!-- 服务图片（紧凑版） -->
                  <div class="image-section">
                    <label class="form-label">服务图片</label>
                    <div class="image-upload-compact">
                      <div v-if="unifiedEditForm.image" class="image-preview-small">
                        <img :src="unifiedEditForm.image" :alt="unifiedEditForm.name" class="preview-image-small">
                        <div class="image-actions-small">
                          <button type="button" class="action-btn-small" @click="changeServiceImage" title="更换图片">🔄</button>
                          <button type="button" class="action-btn-small remove" @click="removeServiceImage" title="移除图片">🗑️</button>
                        </div>
                      </div>
                      <div v-else class="upload-placeholder-small" @click="uploadServiceImage">
                        <div class="upload-icon-small">📷</div>
                        <div class="upload-text-small">上传图片</div>
                      </div>
                    </div>
                  </div>

                  <!-- 项目简介 -->
                  <div class="description-section-main">
                    <label class="form-label">项目简介</label>
                    <textarea
                      v-model="unifiedEditForm.description"
                      class="form-textarea full"
                      placeholder="请详细描述服务内容、特色、适用人群等信息..."
                      rows="10"
                      maxlength="500"
                    ></textarea>
                    <div class="textarea-footer">
                      <span class="char-count">{{ (unifiedEditForm.description || '').length }}/500</span>
                      <span class="textarea-hint">详细的项目简介有助于客户了解服务内容</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- 编辑模式：数据编辑 -->
              <div v-else class="edit-data-section">
                <div class="form-row">
                  <div class="form-group">
                    <label class="form-label">服务费 <span class="required">*</span></label>
                    <input
                      type="number"
                      v-model="unifiedEditForm.newPrice"
                      class="form-input"
                      placeholder="请输入服务费"
                      min="0"
                      step="0.01"
                    >
                  </div>
                  <div class="form-group">
                    <label class="form-label">提成 <span class="required">*</span></label>
                    <input
                      type="number"
                      v-model="unifiedEditForm.newCommission"
                      class="form-input"
                      placeholder="请输入提成"
                      min="0"
                      step="0.01"
                    >
                  </div>
                </div>

                <div class="form-group">
                  <label class="form-label">服务时长 <span class="required">*</span></label>
                  <select
                    v-model.number="unifiedEditForm.newDuration"
                    class="form-select"
                  >
                    <option :value="30">30分钟</option>
                    <option :value="45">45分钟</option>
                    <option :value="60">60分钟</option>
                    <option :value="75">75分钟</option>
                    <option :value="90">90分钟</option>
                    <option :value="120">120分钟</option>
                  </select>
                </div>
              </div>

            </div>

            <!-- 🎯 历史记录标签页 -->
            <div v-if="activeEditTab === 'history'" class="edit-panel">
              <h5 class="panel-title">修改历史记录</h5>

              <div class="history-stats" v-if="unifiedEditHistory.length > 0">
                <div class="stats-inline">
                  <span class="stat-inline">
                    <span class="stat-label-inline">总修改次数:</span>
                    <span class="stat-value-inline">{{ unifiedEditHistory.length }}</span>
                  </span>
                  <span class="stat-divider">|</span>
                  <span class="stat-inline">
                    <span class="stat-label-inline">最近修改:</span>
                    <span class="stat-value-inline">{{ formatDate(unifiedEditHistory[0]?.date) }}</span>
                  </span>
                </div>
              </div>

              <div class="history-content" v-if="unifiedEditHistory.length > 0">
                <div class="history-timeline">
                  <div v-for="(record, index) in unifiedEditHistory" :key="index" class="timeline-item">
                    <div class="timeline-marker"></div>
                    <div class="timeline-content">
                      <div class="timeline-header">
                        <span class="timeline-date">{{ formatDetailedDate(record.date) }}</span>
                        <span class="timeline-index">#{{ unifiedEditHistory.length - index }}</span>
                      </div>
                      <div class="timeline-changes">
                        <div v-if="record.price" class="change-detail">
                          <span class="change-type">服务费</span>
                          <span class="change-arrow">→</span>
                          <span class="change-new-value">¥{{ record.price }}</span>
                        </div>
                        <div v-if="record.commission" class="change-detail">
                          <span class="change-type">提成</span>
                          <span class="change-arrow">→</span>
                          <span class="change-new-value">¥{{ record.commission }}</span>
                        </div>
                        <div v-if="record.duration" class="change-detail">
                          <span class="change-type">时长</span>
                          <span class="change-arrow">→</span>
                          <span class="change-new-value">{{ record.duration }}分钟</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <div class="no-history" v-else>
                <div class="no-history-icon">📋</div>
                <div class="no-history-text">暂无修改记录</div>
                <div class="no-history-desc">该服务还没有任何修改历史</div>
              </div>
            </div>
          </div>

          <!-- 🎯 底部操作区 -->
          <div class="unified-edit-actions">
            <button class="action-btn cancel-btn" @click="hideUnifiedEditModal">
              <span class="btn-icon">✕</span>
              <span class="btn-text">取消</span>
            </button>
            <button class="action-btn confirm-btn" @click="handleUnifiedUpdate" :disabled="unifiedEditLoading">
              <span class="btn-icon" v-if="!unifiedEditLoading">✓</span>
              <span class="btn-icon loading-spinner" v-else>⏳</span>
              <span class="btn-text">{{ unifiedEditLoading ? '更新中...' : '确定更新' }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 🔧 保留原有的价格编辑模态框作为备用 -->
  <div v-if="priceEditVisible" class="modal-overlay">
    <div class="service-form-modal price-edit-modal" @click.stop>
      <div class="form-header">
        <h3 class="form-title">{{ priceEditTitle }}</h3>
        <button class="close-btn" @click="hidePriceEditModal" aria-label="操作按钮">×</button>
      </div>

      <div class="form-content">
        <div class="price-edit-form">
          <!-- 价格修改历史记录 -->
          <div class="form-group" v-if="priceEditHistory.length > 0">
            <label class="form-label">修改历史记录</label>
            <div class="price-history-compact">
              <div class="history-item-compact" v-for="item in priceEditHistory.slice(0, 3)" :key="item.id">
                <div class="history-time">{{ item.changed_at_formatted }}</div>
                <div class="history-changes">
                  <span v-if="item.change_type === 'price' || item.change_type === 'both'" class="change-item">
                    服务费: ¥{{ item.old_price }} → ¥{{ item.new_price }}
                  </span>
                  <span v-if="item.change_type === 'commission' || item.change_type === 'both'" class="change-item">
                    提成: ¥{{ item.old_commission }} → ¥{{ item.new_commission }}
                  </span>
                </div>
              </div>
              <div v-if="priceEditHistory.length > 3" class="more-history">
                还有 {{ priceEditHistory.length - 3 }} 条历史记录...
              </div>
            </div>
          </div>

          <div class="form-group" v-if="priceEditForm.editType === 'price' || priceEditForm.editType === 'both'">
            <label class="form-label">服务费 (当前: ¥{{ priceEditForm.currentPrice }})</label>
            <input
              type="number"
              v-model="priceEditForm.newPrice"
              class="form-input"
              placeholder="请输入新的服务费"
              min="0"
              step="0.01"
            aria-label="输入字段">
          </div>

          <div class="form-group" v-if="priceEditForm.editType === 'commission' || priceEditForm.editType === 'both'">
            <label class="form-label">提成 (当前: ¥{{ priceEditForm.currentCommission }})</label>
            <input
              type="number"
              v-model="priceEditForm.newCommission"
              class="form-input"
              placeholder="请输入新的提成"
              min="0"
              step="0.01"
            aria-label="输入字段">
          </div>
        </div>

        <div class="form-actions">
          <button class="action-btn cancel" @click="hidePriceEditModal" aria-label="操作按钮">
            取消
          </button>
          <button class="action-btn confirm" @click="handlePriceUpdate" :disabled="priceEditLoading" aria-label="操作按钮">
            {{ priceEditLoading ? '更新中...' : '确定' }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 时长编辑模态框 -->
  <div v-if="durationEditVisible" class="modal-overlay">
    <div class="service-form-modal duration-edit-modal" @click.stop>
      <div class="form-header">
        <h3 class="form-title">{{ durationEditTitle }}</h3>
        <button class="close-btn" @click="hideDurationEditModal" aria-label="操作按钮">×</button>
      </div>

      <div class="form-content">
        <div class="duration-edit-form">
          <div class="form-group">
            <label class="form-label">服务时长 (当前: {{ durationEditForm.currentDuration }}分钟)</label>
            <select
              v-model.number="durationEditForm.newDuration"
              class="form-select"
              required
            >
              <option :value="30">30分钟</option>
              <option :value="45">45分钟</option>
              <option :value="60">60分钟</option>
              <option :value="75">75分钟</option>
              <option :value="90">90分钟</option>
              <option :value="120">120分钟</option>
            </select>
          </div>
        </div>

        <div class="form-actions">
          <button class="action-btn cancel" @click="hideDurationEditModal" aria-label="操作按钮">
            取消
          </button>
          <button class="action-btn confirm" @click="handleDurationUpdate" :disabled="durationEditLoading" aria-label="操作按钮">
            {{ durationEditLoading ? '更新中...' : '确定' }}
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 价格历史记录模态框 -->
  <div v-if="priceHistoryVisible" class="modal-overlay">
    <div class="service-form-modal price-history-modal" @click.stop>
      <div class="form-header">
        <h3 class="form-title">{{ priceHistoryService?.name }} - 价格修改历史</h3>
        <button class="close-btn" @click="hidePriceHistoryModal" aria-label="操作按钮">×</button>
      </div>

      <div class="form-content">
        <!-- 当前价格显示 -->
        <div class="current-price-section" v-if="priceHistoryService">
          <h4 class="current-price-title">当前价格</h4>
          <div class="current-price-display">
            <div class="current-price-item">
              <span class="price-label">服务费:</span>
              <span class="price-value">¥{{ priceHistoryService.price }}</span>
            </div>
            <div class="current-price-item">
              <span class="price-label">提成:</span>
              <span class="price-value">¥{{ priceHistoryService.commission }}</span>
            </div>
          </div>
        </div>

        <!-- 历史记录分隔线 -->
        <div class="history-divider" v-if="priceHistory.length > 0">
          <span class="divider-text">修改历史</span>
        </div>

        <!-- 加载状态显示 -->
        <div class="loading-container" v-if="loadingStates.historyView">
          <div class="loading-spinner-large">⏳</div>
          <div class="loading-text">正在加载历史记录...</div>
        </div>

        <div class="history-list" v-else-if="priceHistory.length > 0">
          <div class="history-item" v-for="item in priceHistory" :key="item.id">
            <div class="history-header">
              <span class="change-type">{{ item.change_type_display }}</span>
              <span class="change-time">{{ item.changed_at_formatted }}</span>
            </div>
            <div class="history-content">
              <div class="price-change" v-if="item.change_type === 'price' || item.change_type === 'both'">
                <span class="label">服务费:</span>
                <span class="old-value">¥{{ item.old_price }}</span>
                <span class="arrow">→</span>
                <span class="new-value">¥{{ item.new_price }}</span>
              </div>
              <div class="commission-change" v-if="item.change_type === 'commission' || item.change_type === 'both'">
                <span class="label">提成:</span>
                <span class="old-value">¥{{ item.old_commission }}</span>
                <span class="arrow">→</span>
                <span class="new-value">¥{{ item.new_commission }}</span>
              </div>
            </div>
          </div>
        </div>
        <div v-else class="no-history">
          <p>暂无价格修改记录</p>
        </div>

        <div class="form-actions">
          <button class="action-btn confirm" @click="hidePriceHistoryModal" aria-label="操作按钮">
            关闭
          </button>
        </div>
      </div>
    </div>
  </div>

  <!-- 已移除旧的Toast通知组件，统一使用Sci-Fi风格通知 -->
</template>

<script setup>
// 🔒 开发环境console.log保护 - 生产环境自动禁用
const devLog = (...args) => {
  if (process.env.NODE_ENV === 'development') {
    console.log(...args);
  }
};

const devError = (...args) => {
  if (process.env.NODE_ENV === 'development') {
    console.error(...args);
  }
};

const devWarn = (...args) => {
  if (process.env.NODE_ENV === 'development') {
    console.warn(...args);
  }
};

// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    devWarn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

import { ref, reactive, computed, onMounted, onUnmounted, nextTick, shallowRef, watchEffect } from 'vue';
import { pinyin } from 'pinyin-pro';
import aiService from '../services/aiService.js';
import SciFiNotification from '../components/SciFiNotification.vue';


// 响应式数据
const services = ref([]);
const modalVisible = ref(false);
const modalTitle = ref('新增服务');
const confirmLoading = ref(false);
const volcengineGenerating = ref(false);
const currentPage = ref(1);
const pageSize = ref(10); // 🎯 默认10条每页，第1行对齐仪表盘，完美10行对齐

// 🔍 多字段搜索状态管理系统
const searchModes = ref({
  name: false,      // 服务信息搜索模式
  price: false,     // 服务费搜索模式
  commission: false, // 提成搜索模式
  duration: false,  // 时长搜索模式
  status: false     // 状态搜索模式
});

const searchValues = ref({
  name: '',         // 服务信息搜索值
  price: '',        // 服务费搜索值
  commission: '',   // 提成搜索值
  duration: '',     // 时长搜索值
  status: ''        // 状态搜索值
});

// 🎯 过渡状态管理 - 防止连续点击误触
const isTransitioning = ref({
  name: false,      // 服务信息过渡状态
  price: false,     // 服务费过渡状态
  commission: false, // 提成过渡状态
  duration: false,  // 时长过渡状态
  status: false     // 状态过渡状态
});



// 🎯 搜索禁用状态管理 - 分离排序按钮显示和搜索触发逻辑
const searchDisabled = ref({
  name: false,      // 服务信息搜索禁用状态
  price: false,     // 服务费搜索禁用状态
  commission: false, // 提成搜索禁用状态
  duration: false,  // 时长搜索禁用状态
  status: false     // 状态搜索禁用状态
});

// 🎯 排序按钮状态管理 - 新增：管理排序按钮的加载和禁用状态
const sortButtonStates = ref({
  name: { loading: false, disabled: false },      // 服务信息排序按钮状态
  price: { loading: false, disabled: false },     // 服务费排序按钮状态
  commission: { loading: false, disabled: false }, // 提成排序按钮状态
  duration: { loading: false, disabled: false },  // 时长排序按钮状态
});

// 🎯 排序按钮延迟定时器管理 - 新增：管理5秒延迟显示逻辑
const sortButtonTimers = ref({
  name: null,       // 服务信息排序按钮定时器
  price: null,      // 服务费排序按钮定时器
  commission: null, // 提成排序按钮定时器
  duration: null,   // 时长排序按钮定时器
});

// 🎯 状态筛选管理 - 新增：管理状态筛选功能
const statusFilter = ref('all'); // 'all' | 'active' | 'inactive'

const searchInputRefs = ref({
  name: null,
  price: null,
  commission: null,
  duration: null,
  status: null
});

// 🔄 向后兼容性：保持原有的搜索状态（指向服务信息搜索）
const isSearchMode = computed(() => searchModes.value.name);
const searchValue = computed({
  get: () => searchValues.value.name,
  set: (value) => { searchValues.value.name = value; }
});
const headerSearchInput = computed(() => searchInputRefs.value.name);

// 🎯 中文输入法状态管理
const isComposing = ref(false);

// 排序相关状态
const sortField = ref('');
const sortOrder = ref(''); // 'asc' | 'desc' | ''

// 排序样式和图标函数 - 提前定义确保模板可以访问
const getSortClass = (field) => {
  if (sortField.value !== field) return '';
  return sortOrder.value === 'asc' ? 'sort-asc' : 'sort-desc';
};

const getSortIcon = (field) => {
  if (sortField.value !== field) return '⇅';
  return sortOrder.value === 'asc' ? '↑' : '↓';
};

const getSortTitle = (field) => {
  if (sortField.value !== field) return `点击按${getFieldName(field)}排序`;
  if (sortOrder.value === 'asc') return `当前按${getFieldName(field)}升序，点击改为降序`;
  return `当前按${getFieldName(field)}降序，点击取消排序`;
};

const getFieldName = (field) => {
  const fieldNames = {
    'name': '服务名称',
    'price': '服务费',
    'commission': '提成',
    'duration': '时长',
    'status': '状态'
  };
  return fieldNames[field] || field;
};

// 🔍 多字段搜索专用的字段显示名称映射
const getFieldDisplayName = (field) => {
  const fieldNames = {
    name: '服务信息',
    price: '服务费',
    commission: '提成',
    duration: '时长',
    status: '状态'
  };
  return fieldNames[field] || field;
};

// 聚焦搜索输入框
const focusSearchInput = () => {
  if (searchInput.value) {
    searchInput.value.focus();
    // 如果有内容，全选以便用户直接输入新内容
    if (searchValue.value) {
      searchInput.value.select();
    }
  }
};

// 价格编辑相关状态
const priceEditVisible = ref(false);
const priceEditTitle = ref('');
const priceEditLoading = ref(false);
const priceEditHistory = ref([]);
const priceEditForm = reactive({
  serviceId: null,
  serviceName: '',
  editType: 'price', // 'price', 'commission', 'both'
  currentPrice: 0,
  currentCommission: 0,
  newPrice: null,
  newCommission: null
});

// 时长编辑相关状态
const durationEditVisible = ref(false);
const durationEditTitle = ref('');
const durationEditLoading = ref(false);
const durationEditForm = reactive({
  serviceId: null,
  serviceName: '',
  currentDuration: 0,
  newDuration: null
});

// 价格历史记录相关状态
const priceHistoryVisible = ref(false);
const priceHistoryService = ref(null);
const priceHistory = ref([]);

// 🎨 统一编辑界面相关状态
const unifiedEditVisible = ref(false);
const unifiedEditTitle = ref('');
const unifiedEditLoading = ref(false);
const unifiedEditHistory = ref([]);
const activeEditTab = ref('all'); // 'all', 'history'

// 统一编辑表单数据
const unifiedEditForm = reactive({
  serviceId: null,
  name: '',
  description: '',
  image: '',
  status: 'active',
  currentPrice: 0,
  currentCommission: 0,
  currentDuration: 60,
  newPrice: null,
  newCommission: null,
  newDuration: null
});

// 编辑标签页配置
const editTabs = ref([
  { key: 'all', label: '综合编辑', icon: '🎯' },
  { key: 'history', label: '历史记录', icon: '📋' }
]);

// 交互加载状态 - 修复：使用具体标识符避免全局加载状态
const loadingStates = reactive({
  priceEdit: null,      // 价格编辑加载 - 存储 "serviceId_type" 格式
  durationEdit: null,   // 时长编辑加载 - 存储具体的serviceId
  historyView: false,   // 历史记录查看加载
  serviceSubmit: false, // 服务提交加载
  serviceDelete: null,  // 服务删除加载 - 存储具体的serviceId
  dataLoading: false    // 数据加载状态
});

// 已移除Toast通知系统，统一使用Sci-Fi风格通知

// 智能搜索相关数据 - 性能优化
const showSearchDropdown = ref(false);

// 科幻通知组件引用
const notification = ref(null);

// 🔧 调试函数 - 检查notification组件状态
const debugNotification = () => {
  console.log('🔍 调试信息 - notification组件状态:');
  console.log('- notification.value:', notification.value);
  console.log('- 组件类型:', notification.value ? notification.value.constructor.name : 'null');
  console.log('- 可用方法:', notification.value ? Object.getOwnPropertyNames(notification.value) : 'none');
  console.log('- error方法存在:', notification.value && typeof notification.value.error === 'function');
  return notification.value;
};

// 🚀 安全的通知调用函数 - 符合CI_CD_STANDARDS.md错误处理规范
const safeNotification = (type, title, message, duration = 4000) => {
  try {
    // 调试日志
    debugNotification();

    // 确保组件已挂载且方法可用
    if (notification.value && typeof notification.value[type] === 'function') {
      notification.value[type](title, message, duration);
      console.log(`✅ SciFi通知调用成功: ${type} - ${title}`);
      return true;
    } else {
      console.warn('⚠️ SciFi通知组件未就绪，使用备用提示');
      // 备用提示方案 - 符合用户反馈机制规范
      alert(`${title}: ${message}`);
      return false;
    }
  } catch (error) {
    console.error('❌ SciFi通知调用异常:', error);
    // 最终备用方案
    alert(`${title}: ${message}`);
    return false;
  }
};
const searchSuggestions = shallowRef([]); // 使用shallowRef优化性能

// 防抖搜索 - 性能优化
let searchDebounceTimer = null;
const debouncedSearch = (query) => {
  if (searchDebounceTimer) {
    clearTimeout(searchDebounceTimer);
  }
  searchDebounceTimer = setTimeout(() => {
    performSearch(query);
  }, 300);
};
const highlightedIndex = ref(-1);
const searchInput = ref(null);

// 表单状态
const formState = reactive({
  id: null,
  name: '',
  price: null,
  commission: null,
  duration: 60,
  description: '',
  image: '',
  status: 'active'
});

// 表单验证错误状态 - 符合CI_CD_STANDARDS.md表单验证规范
const formErrors = reactive({
  name: '',
  price: '',
  commission: '',
  duration: '',
  description: ''
});

// 表单验证函数 - 符合CI_CD_STANDARDS.md表单验证规范
const validateForm = () => {
  // 清空之前的错误
  Object.assign(formErrors, {
    name: '',
    price: '',
    commission: '',
    duration: '',
    description: ''
  });

  let isValid = true;

  // 验证服务名称 - 只验证格式，不强制必填
  if (formState.name?.trim() && formState.name.trim().length < 2) {
    formErrors.name = '服务名称至少需要2个字符';
    isValid = false;
  }

  // 验证服务价格 - 只验证范围，不强制必填
  if (formState.price && formState.price > 9999) {
    formErrors.price = '服务价格不能超过9999元';
    isValid = false;
  }

  // 验证技师提成 - 只验证范围，不强制必填
  if (formState.commission && formState.price && formState.commission >= formState.price) {
    formErrors.commission = '技师提成不能超过服务价格';
    isValid = false;
  }

  // 验证服务时长
  if (!formState.duration || formState.duration <= 0) {
    formErrors.duration = '请输入正确的服务时长';
    isValid = false;
  } else if (formState.duration > 480) {
    formErrors.duration = '服务时长不能超过8小时';
    isValid = false;
  }

  // 验证服务描述 - 改为可选字段，不强制要求
  if (formState.description?.trim() && formState.description.trim().length < 10) {
    formErrors.description = '服务描述至少需要10个字符';
    isValid = false;
  }

  return isValid;
};

// AI生成状态
const aiDescriptionLoading = ref(false);

// 自动生成定时器
let autoGenerateTimer = null;

// 自动生成状态
const isAutoGenerating = ref(false);

// 上次生成的服务名称，用于检测名称变化
const lastGeneratedServiceName = ref('');

// 悬停状态已删除 - 影响操作

// 自动生成简介和图片
const autoGenerateContent = async () => {
  if (!formState.name || formState.name.length < 2) return;

  // 防止重复生成
  if (isAutoGenerating.value) {
    devLog('⏳ 正在生成中，跳过重复请求...');
    return;
  }

  isAutoGenerating.value = true;
  devLog('🤖 开始自动生成服务内容...');

  try {
    const needDescription = !formState.description;
    const needImage = !formState.image;

    // 如果需要生成简介
    if (needDescription) {
      devLog('📝 生成服务简介...');
      await generateDescription();

      // 等待简介生成完成
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    // 如果需要生成图片
    if (needImage) {
      devLog('🖼️ 生成服务图片...');
      await generateWithVolcengine();
    }

    // 如果两者都生成了，显示完成提示
    if (needDescription && needImage) {
      devLog('✅ 服务内容自动生成完成！');
    } else if (needDescription) {
      devLog('✅ 服务简介自动生成完成！');
    } else if (needImage) {
      devLog('✅ 服务图片自动生成完成！');
    }

    // 记录当前生成的服务名称
    lastGeneratedServiceName.value = formState.name?.trim() || '';

  } catch (error) {
    devError('❌ 自动生成失败:', error);
  } finally {
    isAutoGenerating.value = false;
  }
};

// 监听服务名称变化，自动生成内容
const handleNameChange = () => {
  // 检测服务名称是否发生变化
  const currentName = formState.name?.trim() || '';
  const lastGeneratedName = lastGeneratedServiceName.value;

  // 如果名称发生变化，清空之前生成的内容
  if (lastGeneratedName && currentName !== lastGeneratedName) {
    devLog('🔄 服务名称已变化，清空之前生成的内容');
    formState.description = '';
    formState.image = '';
    lastGeneratedServiceName.value = '';
  }

  // 清除之前的定时器
  if (autoGenerateTimer) {
    clearTimeout(autoGenerateTimer);
  }

  // 设置新的定时器，用户停止输入2秒后自动生成
  autoGenerateTimer = setTimeout(() => {
    if (formState.name && formState.name.length >= 2 && !formState.description && !formState.image) {
      autoGenerateContent();
    }
  }, 2000);
};

// 处理服务名称失去焦点，检查是否有已删除的同名服务
const handleNameBlur = async () => {
  // 清除定时器，避免重复触发
  if (autoGenerateTimer) {
    clearTimeout(autoGenerateTimer);
    autoGenerateTimer = null;
  }

  // 防止重复生成
  if (isAutoGenerating.value) {
    devLog('⏳ 正在生成中，跳过焦点失去事件...');
    return;
  }

  // 检查服务名称
  if (formState.name && formState.name.length >= 2) {
    // 首先检查是否有已删除的同名服务
    const nameCheck = await checkServiceNameUniqueness(formState.name.trim());

    if (nameCheck.exists && nameCheck.is_deleted) {
      // 有已删除的同名服务，获取详细信息并自动填充
      devLog('🔄 检测到已删除的同名服务，自动填充旧数据...');
      const deletedServiceDetails = await getDeletedServiceDetails(formState.name.trim());

      if (deletedServiceDetails) {
        // 自动填充所有旧数据
        formState.description = deletedServiceDetails.description || '';
        formState.price = deletedServiceDetails.price || 0;
        formState.commission = deletedServiceDetails.commission || 0;
        formState.duration = deletedServiceDetails.duration || 30;
        formState.image = deletedServiceDetails.image || '';
        formState.status = deletedServiceDetails.status || 'active';

        devLog('✅ 已自动填充已删除服务的所有数据');

        // 记录当前填充的服务名称
        lastGeneratedServiceName.value = formState.name?.trim() || '';

        return; // 不需要AI生成，直接返回
      }
    } else if (nameCheck.exists && !nameCheck.is_deleted) {
      // 有现有的同名服务，提示用户
      devLog('⚠️ 检测到现有同名服务，请修改服务名称');
      return;
    }

    // 没有同名服务，进行正常的AI生成流程
    if (!formState.description && !formState.image) {
      devLog('🎯 服务名称失去焦点，开始自动生成内容...');
      autoGenerateContent();
    } else if (!formState.description) {
      devLog('🎯 服务名称失去焦点，生成服务简介...');
      generateDescription();
    } else if (!formState.image) {
      devLog('🎯 服务名称失去焦点，生成服务图片...');
      generateWithVolcengine();
    }
  }
};

// 处理行悬停已删除 - 影响操作

// 软删除服务
const handleSoftDelete = async (record) => {
  try {
    // 设置具体服务的加载状态
    loadingStates.serviceDelete = record.id;
    devLog('🗑️ 软删除服务:', record.name);

    // 调用后端API进行软删除
    const response = await fetch(`/api/v1/services/${record.id}/soft_delete/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    if (response.ok) {
      // 从前端列表中移除（软删除，前端不显示）
      const index = services.value.findIndex(item => item.id === record.id);
      if (index !== -1) {
        services.value.splice(index, 1);
      }
      devLog('✅ 服务软删除成功');
    } else {
      const errorText = await response.text();
      devError('❌ 软删除失败:', {
        status: response.status,
        statusText: response.statusText,
        error: errorText
      });
    }
  } catch (error) {
    devError('❌ 软删除服务失败:', error);
  } finally {
    // 清除加载状态
    loadingStates.serviceDelete = null;
  }
};

// 检查服务名称唯一性
const checkServiceNameUniqueness = async (name, excludeId = null) => {
  try {
    const response = await fetch(`/api/v1/services/check_name/?name=${encodeURIComponent(name)}${excludeId ? `&exclude_id=${excludeId}` : ''}`);

    if (response.ok) {
      const result = await response.json();
      return result;
    } else {
      devError('❌ 检查名称唯一性失败');
      return { exists: false, is_deleted: false };
    }
  } catch (error) {
    devError('❌ 检查名称唯一性异常:', error);
    return { exists: false, is_deleted: false };
  }
};

// 获取已删除服务的详细信息
const getDeletedServiceDetails = async (name) => {
  try {
    const response = await fetch(`/api/v1/services/get_deleted/?name=${encodeURIComponent(name)}`);

    if (response.ok) {
      const result = await response.json();
      return result;
    } else {
      devError('❌ 获取已删除服务详情失败');
      return null;
    }
  } catch (error) {
    devError('❌ 获取已删除服务详情异常:', error);
    return null;
  }
};

// 恢复已删除的服务
const restoreDeletedService = async (name, serviceData) => {
  try {
    devLog('🔄 恢复已删除的服务:', name);

    const response = await fetch(`/api/v1/services/restore/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        name: name,
        ...serviceData
      })
    });

    if (response.ok) {
      const restoredService = await response.json();
      devLog('✅ 服务恢复成功:', restoredService);
      return restoredService;
    } else {
      const errorText = await response.text();
      devError('❌ 服务恢复失败:', errorText);
      throw new Error(`服务恢复失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    devError('❌ 恢复服务异常:', error);
    throw error;
  }
};

// 智能搜索匹配函数
const isMatchingService = (service, searchTerm) => {
  if (!searchTerm) return true;

  const term = searchTerm.toLowerCase().trim();
  const serviceName = service.name.toLowerCase();

  // 1. 直接汉字匹配
  if (serviceName.includes(term)) {
    return true;
  }

  // 2. 拼音全拼匹配
  const fullPinyin = pinyin(service.name, { toneType: 'none', type: 'array' }).join('').toLowerCase();
  if (fullPinyin.includes(term)) {
    return true;
  }

  // 3. 拼音首字母匹配
  const firstLetters = pinyin(service.name, { pattern: 'first', toneType: 'none', type: 'array' }).join('').toLowerCase();
  if (firstLetters.includes(term)) {
    return true;
  }

  // 4. 拼音分词匹配（支持部分拼音）
  const pinyinWords = pinyin(service.name, { toneType: 'none', type: 'array' });
  for (const word of pinyinWords) {
    if (word.toLowerCase().startsWith(term)) {
      return true;
    }
  }

  return false;
};

// 🎯 动态滚动控制计算属性
const shouldHideScrollbar = computed(() => {
  return pageSize.value <= 10;
});

// 🎯 精确滚动吸附函数 - 确保最后一行吸附到表格主体底部
const adjustScrollForLastRowAlignment = () => {
  nextTick(() => {
    const tableBody = document.querySelector('.table-body');
    if (!tableBody || pageSize.value <= 10) return;

    const rowHeight = 50;
    const visibleRows = 10;
    const totalRows = paginatedData.value.length;

    if (totalRows > visibleRows) {
      // 计算需要的滚动距离，让最后一行底部对齐到表格主体底部
      const hiddenRows = totalRows - visibleRows;
      const exactScrollTop = hiddenRows * rowHeight;

      // 设置精确的滚动位置
      tableBody.scrollTop = exactScrollTop;

      console.log(`🎯 精确滚动吸附: 总行数${totalRows}, 隐藏行数${hiddenRows}, 滚动距离${exactScrollTop}px`);
    }
  });
};

const dynamicTableHeight = computed(() => {
  const headerHeight = 50; // 表头高度
  const rowHeight = 50; // 数据行高度
  const paginationHeight = 50; // 翻页组件高度
  const padding = 60; // 上下内边距

  // 🎯 基于视口高度计算，为翻页组件预留空间
  // 使用window.innerHeight确保响应式
  const viewportHeight = typeof window !== 'undefined' ? window.innerHeight : 768;
  const availableHeight = viewportHeight - paginationHeight - padding;

  // 🎯 确保表格不会超出可用空间
  const maxRows = Math.floor((availableHeight - headerHeight) / rowHeight);
  const calculatedHeight = Math.min(
    headerHeight + (Math.min(pageSize.value, maxRows) * rowHeight),
    availableHeight
  );

  return Math.max(calculatedHeight, 400); // 最小高度400px
});

// 计算属性
const filteredServices = computed(() => {
  let filtered = services.value;

  // 🎯 状态筛选过滤 - 新增：优先进行状态筛选
  if (statusFilter.value !== 'all') {
    filtered = filtered.filter(service => {
      return service.status === statusFilter.value;
    });
  }

  // 🔍 多字段智能搜索过滤
  // 服务信息搜索（保持原有逻辑）
  if (searchValue.value) {
    filtered = filtered.filter(service => isMatchingService(service, searchValue.value));
  }

  // 服务费搜索
  if (searchValues.value.price && searchValues.value.price.trim()) {
    filtered = filtered.filter(service => {
      const priceStr = service.price?.toString() || '';
      return priceStr.includes(searchValues.value.price.trim());
    });
  }

  // 提成搜索
  if (searchValues.value.commission && searchValues.value.commission.trim()) {
    filtered = filtered.filter(service => {
      const commissionStr = service.commission?.toString() || '';
      return commissionStr.includes(searchValues.value.commission.trim());
    });
  }

  // 时长搜索
  if (searchValues.value.duration && searchValues.value.duration.trim()) {
    filtered = filtered.filter(service => {
      const durationStr = service.duration?.toString() || '';
      return durationStr.includes(searchValues.value.duration.trim());
    });
  }

  // 状态搜索
  if (searchValues.value.status && searchValues.value.status.trim()) {
    filtered = filtered.filter(service => {
      const statusStr = service.status?.toString() || '';
      return statusStr.includes(searchValues.value.status.trim()) ||
             (service.is_active && searchValues.value.status.includes('上架')) ||
             (!service.is_active && searchValues.value.status.includes('下架'));
    });
  }



  // 排序处理
  if (sortField.value && sortOrder.value) {
    filtered = filtered.sort((a, b) => {
      let aValue = a[sortField.value];
      let bValue = b[sortField.value];

      // 处理不同数据类型的排序
      if (sortField.value === 'price' || sortField.value === 'commission' || sortField.value === 'duration') {
        aValue = parseFloat(aValue) || 0;
        bValue = parseFloat(bValue) || 0;
      } else if (sortField.value === 'name') {
        aValue = (aValue || '').toString().toLowerCase();
        bValue = (bValue || '').toString().toLowerCase();
      } else if (sortField.value === 'status') {
        // 状态排序：active > inactive
        const statusOrder = { 'active': 1, 'inactive': 0 };
        aValue = statusOrder[aValue] || 0;
        bValue = statusOrder[bValue] || 0;
      }

      if (sortOrder.value === 'asc') {
        return aValue > bValue ? 1 : aValue < bValue ? -1 : 0;
      } else {
        return aValue < bValue ? 1 : aValue > bValue ? -1 : 0;
      }
    });
  } else {
    // 默认排序：上架的服务显示在前面，下架的显示在后面
    filtered = filtered.sort((a, b) => {
      // 首先按状态排序：active(上架) 在前，inactive(下架) 在后
      if (a.status === 'active' && b.status === 'inactive') {
        return -1; // a 排在 b 前面
      }
      if (a.status === 'inactive' && b.status === 'active') {
        return 1; // b 排在 a 前面
      }

      // 状态相同时，按ID排序（保持稳定的顺序）
      return a.id - b.id;
    });
  }

  return filtered;
});

const totalRecords = computed(() => filteredServices.value.length);
const totalPages = computed(() => Math.ceil(filteredServices.value.length / pageSize.value));
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return filteredServices.value.slice(start, end);
});

const visiblePages = computed(() => {
  const pages = [];
  const total = totalPages.value;
  const current = currentPage.value;

  // 确保至少显示当前页
  if (total === 0) return [];

  // 如果总页数小于等于5，显示所有页码
  if (total <= 5) {
    for (let i = 1; i <= total; i++) {
      pages.push(i);
    }
  } else {
    // 总页数大于5时，显示当前页前后2页
    for (let i = Math.max(1, current - 2); i <= Math.min(total, current + 2); i++) {
      pages.push(i);
    }
  }

  return pages;
});

// 方法
const loadServices = async () => {
  try {
    devLog('🔍 开始加载服务数据...');

    // 设置加载状态
    loadingStates.dataLoading = true;

    // 调用API获取服务数据
    const response = await fetch('/api/v1/services/');

    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    devLog('📦 API响应数据:', data);

    // 修正API响应格式检查 - Django REST framework返回格式
    if (data.results && Array.isArray(data.results)) {
      services.value = data.results;
      devLog('✅ 服务数据加载成功:', services.value.length, '条');
    } else if (Array.isArray(data)) {
      // 如果直接返回数组
      services.value = data;
      devLog('✅ 服务数据加载成功:', services.value.length, '条');
    } else {
      devLog('❌ API响应格式错误:', data);
      // 使用备用数据
      loadFallbackServices();
    }
  } catch (error) {
    devError('❌ 加载服务数据失败:', error);
    // 使用备用数据
    loadFallbackServices();
  } finally {
    // 清除加载状态
    loadingStates.dataLoading = false;
  }
};

const loadFallbackServices = () => {
  devLog('🔄 使用备用服务数据...');
  services.value = [
    {
      id: 1,
      name: '颈椎推拿',
      price: 120,
      commission: 30,
      duration: 60,
      description: '源于古代中医理论，传承千年养生智慧。通过专业手法疏通颈部经络、调和气血，有效缓解现代人因久坐、低头导致的颈椎疲劳和疼痛。60分钟的精准调理，让您重获轻松活力，特别适合办公族、手机族及颈椎不适人群。',
      image: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=300&fit=crop',
      status: 'active'
    },
    {
      id: 2,
      name: '全身推拿',
      price: 180,
      commission: 45,
      duration: 90,
      description: '融合东西方按摩精髓，运用科学的力度和节奏，深层疏通全身经络，促进血液循环和新陈代谢。90分钟的全面护理，能够有效缓解身心疲劳，提升睡眠质量，是现代都市人放松身心、恢复活力的理想选择。',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
      status: 'active'
    },
    {
      id: 3,
      name: '足疗保健',
      price: 100,
      commission: 25,
      duration: 45,
      description: '源于中医"足部反射区"理论，通过刺激足底穴位调节全身机能。45分钟的专业护理，能够改善睡眠、缓解疲劳、增强免疫力，特别适合失眠、消化不良、免疫力低下的人群，是简单有效的日常保健方式。',
      image: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop',
      status: 'active'
    },
    {
      id: 4,
      name: '艾灸理疗',
      price: 150,
      commission: 40,
      duration: 60,
      description: '承载着数千年中医文化，以优质艾草为原料，通过温热刺激特定穴位，温经散寒、扶阳固脱。60分钟的温和调理，能够改善体寒、增强体质、调理脾胃，适合体质虚弱、手脚冰凉、消化功能差的人群。',
      image: 'https://images.unsplash.com/photo-1540555700478-4be289fbecef?w=400&h=300&fit=crop',
      status: 'inactive'
    },
    {
      id: 5,
      name: '肩颈舒缓',
      price: 100,
      commission: 25,
      duration: 45,
      description: '针对现代人肩颈问题设计的专业理疗服务。通过精准手法缓解肩颈僵硬，改善血液循环，45分钟的专业调理让您告别肩颈疲劳，重获轻松舒适。',
      image: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=300&fit=crop',
      status: 'active'
    },
    {
      id: 6,
      name: '腰部护理',
      price: 130,
      commission: 35,
      duration: 50,
      description: '专业腰部理疗护理，运用传统手法结合现代技术，有效缓解腰部疼痛和不适。50分钟的精心调理，帮助您恢复腰部健康，提升生活质量。',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
      status: 'active'
    },
    {
      id: 7,
      name: '经络疏通',
      price: 160,
      commission: 40,
      duration: 75,
      description: '基于中医经络理论的全身经络疏通服务。通过专业手法打通经络，调和气血，75分钟的深度调理让您感受经络畅通的舒适体验。',
      image: 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop',
      status: 'active'
    },
    {
      id: 8,
      name: '养生调理',
      price: 200,
      commission: 50,
      duration: 90,
      description: '综合性养生调理服务，融合多种传统养生技法。90分钟的全面调理，从内到外提升身体机能，是现代人养生保健的理想选择。',
      image: 'https://images.unsplash.com/photo-1540555700478-4be289fbecef?w=400&h=300&fit=crop',
      status: 'active'
    },
    {
      id: 9,
      name: '深度放松',
      price: 180,
      commission: 45,
      duration: 80,
      description: '专为压力大的现代人设计的深度放松理疗。通过舒缓的手法和专业技巧，80分钟的放松体验让您释放压力，重获内心平静。',
      image: 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=300&fit=crop',
      status: 'active'
    },
    {
      id: 10,
      name: '体质调理',
      price: 220,
      commission: 55,
      duration: 100,
      description: '根据个人体质特点定制的专业调理服务。100分钟的个性化调理，帮助您改善体质，增强免疫力，达到最佳的健康状态。',
      image: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
      status: 'inactive'
    },
    {
      id: 11,
      name: '中药熏蒸',
      price: 140,
      commission: 35,
      duration: 50,
      description: '传统中药熏蒸疗法，通过药物蒸汽渗透肌肤，调理身体机能。50分钟的温和调理，适合体质虚弱、免疫力低下的人群。',
      image: '', // 没有图片的测试数据
      status: 'active'
    },
    {
      id: 12,
      name: '经络调理',
      price: 160,
      commission: 40,
      duration: 70,
      description: '专业经络调理服务，疏通经络，调和气血。70分钟的精心调理，帮助您恢复身体平衡，提升整体健康水平。',
      image: null, // 没有图片的测试数据
      status: 'active'
    }
  ];
};

const showAddModal = () => {
  // 🎨 使用新的统一编辑界面进行新增
  const newService = {
    id: null,
    name: '',
    price: 0,
    commission: 0,
    duration: 60,
    description: '',
    image: '',
    status: 'active'
  };

  showUnifiedEditModal(newService, 'all');
  unifiedEditTitle.value = '新增服务';
};

const hideModal = () => {
  modalVisible.value = false;
  confirmLoading.value = false;

  // 清理生成状态
  isAutoGenerating.value = false;
  aiDescriptionLoading.value = false;
  volcengineGenerating.value = false;

  // 清除定时器
  if (autoGenerateTimer) {
    clearTimeout(autoGenerateTimer);
    autoGenerateTimer = null;
  }

  // 注意：不清理lastGeneratedServiceName，保留用于下次打开时的比较
};

// 价格编辑相关函数 - 修复：使用具体服务ID避免全局加载状态
const handlePriceEdit = async (service, editType) => {
  // 设置具体的加载状态标识
  const loadingKey = `${service.id}_${editType}`;
  loadingStates.priceEdit = loadingKey;

  try {
    priceEditForm.serviceId = service.id;
    priceEditForm.serviceName = service.name;
    priceEditForm.editType = editType;
    priceEditForm.currentPrice = parseFloat(service.price);
    priceEditForm.currentCommission = parseFloat(service.commission);
    priceEditForm.newPrice = editType === 'price' || editType === 'both' ? parseFloat(service.price) : null;
    priceEditForm.newCommission = editType === 'commission' || editType === 'both' ? parseFloat(service.commission) : null;

    if (editType === 'price') {
      priceEditTitle.value = `编辑服务费 - ${service.name}`;
    } else if (editType === 'commission') {
      priceEditTitle.value = `编辑提成 - ${service.name}`;
    } else {
      priceEditTitle.value = `编辑价格信息 - ${service.name}`;
    }

    // 加载价格修改历史记录
    try {
      const response = await fetch(`/api/v1/services/${service.id}/price_history/`);
      if (response.ok) {
        priceEditHistory.value = await response.json();
      } else {
        priceEditHistory.value = [];
      }
    } catch (error) {
      devError('❌ 获取价格历史失败:', error);
      priceEditHistory.value = [];
    }
  } catch (error) {
    devError('❌ 价格编辑初始化失败:', error);
    priceEditHistory.value = [];
  } finally {
    // 无论成功失败，都显示模态框并清除加载状态
    priceEditVisible.value = true;
    loadingStates.priceEdit = null;
  }
};

const hidePriceEditModal = () => {
  priceEditVisible.value = false;
  priceEditLoading.value = false;

  // 重置表单
  Object.assign(priceEditForm, {
    serviceId: null,
    serviceName: '',
    editType: 'price',
    currentPrice: 0,
    currentCommission: 0,
    newPrice: null,
    newCommission: null
  });

  // 清空历史记录
  priceEditHistory.value = [];
};

const handlePriceUpdate = async () => {
  if (priceEditLoading.value) return;

  // 验证输入
  if (priceEditForm.editType === 'price' || priceEditForm.editType === 'both') {
    if (priceEditForm.newPrice === null || priceEditForm.newPrice < 0) {
      notification.value?.warning(
        '输入验证失败',
        '请输入有效的服务费金额，金额必须大于等于0',
        4000
      );
      return;
    }
  }

  if (priceEditForm.editType === 'commission' || priceEditForm.editType === 'both') {
    if (priceEditForm.newCommission === null || priceEditForm.newCommission < 0) {
      notification.value?.warning(
        '输入验证失败',
        '请输入有效的提成，金额必须大于等于0',
        4000
      );
      return;
    }
  }

  // 验证提成不能超过服务费
  const finalPrice = priceEditForm.editType === 'price' || priceEditForm.editType === 'both'
    ? priceEditForm.newPrice
    : priceEditForm.currentPrice;
  const finalCommission = priceEditForm.editType === 'commission' || priceEditForm.editType === 'both'
    ? priceEditForm.newCommission
    : priceEditForm.currentCommission;

  if (finalCommission > finalPrice) {
    notification.value?.error(
      '数据验证失败',
      '提成不能超过服务费，请重新设置合理的提成',
      5000
    );
    return;
  }

  priceEditLoading.value = true;

  try {
    const updateData = {};
    if (priceEditForm.editType === 'price' || priceEditForm.editType === 'both') {
      updateData.price = priceEditForm.newPrice;
    }
    if (priceEditForm.editType === 'commission' || priceEditForm.editType === 'both') {
      updateData.commission = priceEditForm.newCommission;
    }

    const response = await fetch(`/api/v1/services/${priceEditForm.serviceId}/update_price/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData)
    });

    if (response.ok) {
      const updatedService = await response.json();

      // 更新前端列表中的服务数据
      const index = services.value.findIndex(item => item.id === priceEditForm.serviceId);
      if (index !== -1) {
        services.value[index] = updatedService;
      }

      devLog('✅ 价格更新成功');

      // 显示成功提示
      notification.value?.success(
        '价格更新成功',
        `${priceEditForm.serviceName} 的价格信息已成功更新`,
        3000
      );

      hidePriceEditModal();

      // 刷新价格历史记录计数
      await loadServices();

    } else {
      const errorText = await response.text();
      let errorMessage = '价格更新失败';

      try {
        const errorData = JSON.parse(errorText);
        if (errorData.message) {
          errorMessage = errorData.message;
        } else if (errorData.error) {
          errorMessage = errorData.error;
        }
      } catch (e) {
        // 如果不是JSON，使用默认错误信息
      }

      notification.value?.error(
        '价格更新失败',
        errorMessage,
        5000
      );
    }
  } catch (error) {
    devError('❌ 价格更新异常:', error);
    notification.value?.error(
      '系统异常',
      '价格更新失败，请检查网络连接后重试',
      5000
    );
  } finally {
    priceEditLoading.value = false;
  }
};

// 时长编辑相关函数
const handleDurationEdit = async (service) => {
  // 设置具体的加载状态标识
  loadingStates.durationEdit = service.id;

  try {
    durationEditForm.serviceId = service.id;
    durationEditForm.serviceName = service.name;
    durationEditForm.currentDuration = parseInt(service.duration);
    durationEditForm.newDuration = parseInt(service.duration);

    durationEditTitle.value = `编辑服务时长 - ${service.name}`;
  } catch (error) {
    devError('❌ 时长编辑初始化失败:', error);
  } finally {
    // 无论成功失败，都显示模态框并清除加载状态
    durationEditVisible.value = true;
    loadingStates.durationEdit = null;
  }
};

const hideDurationEditModal = () => {
  durationEditVisible.value = false;
  durationEditLoading.value = false;

  // 重置表单
  Object.assign(durationEditForm, {
    serviceId: null,
    serviceName: '',
    currentDuration: 0,
    newDuration: null
  });
};

const handleDurationUpdate = async () => {
  if (durationEditLoading.value) return;

  // 验证输入
  if (!durationEditForm.newDuration || durationEditForm.newDuration <= 0) {
    notification.value?.warning(
      '输入验证失败',
      '请选择有效的服务时长',
      4000
    );
    return;
  }

  // 检查是否有变化
  if (durationEditForm.newDuration === durationEditForm.currentDuration) {
    notification.value?.info(
      '无需更新',
      '服务时长没有变化',
      3000
    );
    hideDurationEditModal();
    return;
  }

  durationEditLoading.value = true;

  try {
    const updateData = {
      duration: durationEditForm.newDuration
    };

    const response = await fetch(`/api/v1/services/${durationEditForm.serviceId}/update_duration/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(updateData)
    });

    if (response.ok) {
      const updatedService = await response.json();

      // 更新前端列表中的服务数据
      const index = services.value.findIndex(item => item.id === durationEditForm.serviceId);
      if (index !== -1) {
        services.value[index] = updatedService;
      }

      devLog('✅ 时长更新成功');

      // 显示成功提示
      notification.value?.success(
        '时长更新成功',
        `${durationEditForm.serviceName} 的服务时长已更新为 ${durationEditForm.newDuration} 分钟`,
        3000
      );

      hideDurationEditModal();

    } else {
      const errorText = await response.text();
      let errorMessage = `时长更新失败: ${response.status} ${response.statusText}`;

      try {
        const errorData = JSON.parse(errorText);
        if (errorData.detail) {
          errorMessage = errorData.detail;
        } else if (errorData.error) {
          errorMessage = errorData.error;
        }
      } catch (e) {
        // 如果不是JSON，使用默认错误信息
      }

      notification.value?.error(
        '时长更新失败',
        errorMessage,
        5000
      );
    }
  } catch (error) {
    devError('❌ 时长更新异常:', error);
    notification.value?.error(
      '系统异常',
      '时长更新失败，请检查网络连接后重试',
      5000
    );
  } finally {
    durationEditLoading.value = false;
  }
};

// 价格历史记录相关函数
const showPriceHistory = async (service) => {
  // 设置加载状态
  loadingStates.historyView = true;

  priceHistoryService.value = service;
  priceHistoryVisible.value = true;

  try {
    const response = await fetch(`/api/v1/services/${service.id}/price_history/`);
    if (response.ok) {
      priceHistory.value = await response.json();
    } else {
      devError('❌ 获取价格历史失败');
      priceHistory.value = [];
    }
  } catch (error) {
    devError('❌ 获取价格历史异常:', error);
    priceHistory.value = [];
  } finally {
    // 清除加载状态
    loadingStates.historyView = false;
  }
};

const hidePriceHistoryModal = () => {
  priceHistoryVisible.value = false;
  priceHistoryService.value = null;
  priceHistory.value = [];
};

const handleEdit = (record) => {
  // 🎨 使用新的统一编辑界面
  showUnifiedEditModal(record, 'all');
};

// 🎨 统一编辑界面相关方法
const showUnifiedEditModal = (service, editType = 'all') => {
  // 设置基本信息
  unifiedEditForm.serviceId = service.id;
  unifiedEditForm.name = service.name;
  unifiedEditForm.description = service.description || '';
  unifiedEditForm.image = service.image || '';
  unifiedEditForm.status = service.status || 'active';

  // 设置当前数据
  unifiedEditForm.currentPrice = parseFloat(service.price) || 0;
  unifiedEditForm.currentCommission = parseFloat(service.commission) || 0;
  unifiedEditForm.currentDuration = parseInt(service.duration) || 60;

  // 设置新数据为当前数据
  unifiedEditForm.newPrice = unifiedEditForm.currentPrice;
  unifiedEditForm.newCommission = unifiedEditForm.currentCommission;
  unifiedEditForm.newDuration = unifiedEditForm.currentDuration;

  // 统一使用综合编辑标签页，根据编辑类型设置标题
  activeEditTab.value = 'all';
  switch (editType) {
    case 'price':
      unifiedEditTitle.value = `编辑服务费 - ${service.name}`;
      break;
    case 'commission':
      unifiedEditTitle.value = `编辑提成 - ${service.name}`;
      break;
    case 'duration':
      unifiedEditTitle.value = `编辑时长 - ${service.name}`;
      break;
    default:
      unifiedEditTitle.value = `编辑服务 - ${service.name}`;
  }

  // 加载历史记录
  loadUnifiedEditHistory(service.id);

  // 显示模态框
  unifiedEditVisible.value = true;
};

const hideUnifiedEditModal = () => {
  unifiedEditVisible.value = false;
  unifiedEditLoading.value = false;

  // 重置表单
  Object.assign(unifiedEditForm, {
    serviceId: null,
    name: '',
    description: '',
    image: '',
    status: 'active',
    currentPrice: 0,
    currentCommission: 0,
    currentDuration: 60,
    newPrice: null,
    newCommission: null,
    newDuration: null
  });

  // 清空历史记录
  unifiedEditHistory.value = [];
  activeEditTab.value = 'all';
};

const switchEditTab = (tabKey) => {
  activeEditTab.value = tabKey;
};





const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleDateString('zh-CN', {
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const formatDetailedDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  const now = new Date();
  const diffTime = Math.abs(now - date);
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));

  let timeAgo = '';
  if (diffDays === 1) {
    timeAgo = '昨天';
  } else if (diffDays <= 7) {
    timeAgo = `${diffDays}天前`;
  } else {
    timeAgo = date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: 'short',
      day: 'numeric'
    });
  }

  const timeStr = date.toLocaleTimeString('zh-CN', {
    hour: '2-digit',
    minute: '2-digit'
  });

  return `${timeAgo} ${timeStr}`;
};

const loadUnifiedEditHistory = async (serviceId) => {
  try {
    // 这里可以加载服务的修改历史记录
    // 暂时使用模拟数据
    unifiedEditHistory.value = [
      {
        date: new Date(Date.now() - 86400000).toISOString(), // 1天前
        price: 120,
        commission: 30,
        duration: 60
      },
      {
        date: new Date(Date.now() - 172800000).toISOString(), // 2天前
        price: 100,
        commission: 25,
        duration: 45
      },
      {
        date: new Date(Date.now() - 259200000).toISOString(), // 3天前
        commission: 20,
        duration: 45
      },
      {
        date: new Date(Date.now() - 345600000).toISOString(), // 4天前
        price: 80,
        duration: 30
      },
      {
        date: new Date(Date.now() - 432000000).toISOString(), // 5天前
        price: 80,
        commission: 15,
        duration: 30
      }
    ];
  } catch (error) {
    devError('❌ 加载历史记录失败:', error);
  }
};

// 🎨 图片上传相关方法
const uploadServiceImage = () => {
  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = 'image/jpeg,image/jpg,image/png';
  input.onchange = (event) => {
    const file = event.target.files[0];
    if (file) {
      // 验证文件大小（限制为2MB）
      if (file.size > 2 * 1024 * 1024) {
        notification.value?.error(
          '文件过大',
          '图片文件大小不能超过2MB',
          3000
        );
        return;
      }

      // 创建预览URL
      const reader = new FileReader();
      reader.onload = (e) => {
        unifiedEditForm.image = e.target.result;
      };
      reader.readAsDataURL(file);
    }
  };
  input.click();
};

const changeServiceImage = () => {
  uploadServiceImage();
};

const removeServiceImage = () => {
  unifiedEditForm.image = '';
};

const handleUnifiedUpdate = async () => {
  if (unifiedEditLoading.value) return;

  // 验证输入
  const validationErrors = [];

  // 新增服务时验证名称
  if (!unifiedEditForm.serviceId) {
    if (!unifiedEditForm.name || unifiedEditForm.name.trim().length === 0) {
      validationErrors.push('服务名称不能为空');
    } else if (unifiedEditForm.name.trim().length > 50) {
      validationErrors.push('服务名称不能超过50个字符');
    }

    if (unifiedEditForm.description && unifiedEditForm.description.length > 500) {
      validationErrors.push('项目简介不能超过500个字符');
    }
  }

  if (unifiedEditForm.newPrice !== null && (unifiedEditForm.newPrice < 0 || unifiedEditForm.newPrice > 9999)) {
    validationErrors.push('服务费必须在0-9999元之间');
  }

  if (unifiedEditForm.newCommission !== null && unifiedEditForm.newCommission < 0) {
    validationErrors.push('提成必须大于等于0');
  }

  if (unifiedEditForm.newCommission > (unifiedEditForm.newPrice || unifiedEditForm.currentPrice)) {
    validationErrors.push('提成不能超过服务费');
  }

  if (unifiedEditForm.newDuration !== null && (unifiedEditForm.newDuration <= 0 || unifiedEditForm.newDuration > 480)) {
    validationErrors.push('服务时长必须在1-480分钟之间');
  }

  if (validationErrors.length > 0) {
    notification.value?.error(
      '输入验证失败',
      validationErrors.join('；'),
      5000
    );
    return;
  }

  unifiedEditLoading.value = true;

  try {
    // 准备更新数据
    const updateData = {};

    if (unifiedEditForm.newPrice !== null && unifiedEditForm.newPrice !== unifiedEditForm.currentPrice) {
      updateData.price = parseFloat(unifiedEditForm.newPrice);
    }

    if (unifiedEditForm.newCommission !== null && unifiedEditForm.newCommission !== unifiedEditForm.currentCommission) {
      updateData.commission = parseFloat(unifiedEditForm.newCommission);
    }

    if (unifiedEditForm.newDuration !== null && unifiedEditForm.newDuration !== unifiedEditForm.currentDuration) {
      updateData.duration = parseInt(unifiedEditForm.newDuration);
    }

    // 如果没有任何变化，直接返回
    if (Object.keys(updateData).length === 0) {
      notification.value?.info(
        '无需更新',
        '没有检测到任何数据变化',
        3000
      );
      hideUnifiedEditModal();
      return;
    }

    devLog('📤 准备更新的数据:', updateData);

    let response;

    // 判断是新增还是编辑
    if (!unifiedEditForm.serviceId) {
      // 新增服务
      const createData = {
        name: unifiedEditForm.name.trim(),
        description: unifiedEditForm.description?.trim() || '',
        price: parseFloat(unifiedEditForm.newPrice) || 0,
        commission: parseFloat(unifiedEditForm.newCommission) || 0,
        duration: parseInt(unifiedEditForm.newDuration) || 60,
        image: unifiedEditForm.image || '',
        status: unifiedEditForm.status || 'active'
      };

      response = await fetch('/api/v1/services/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(createData)
      });
    } else {
      // 更新服务
      response = await fetch(`/api/v1/services/${unifiedEditForm.serviceId}/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(updateData)
      });
    }

    if (response.ok) {
      const serviceData = await response.json();

      if (!unifiedEditForm.serviceId) {
        // 新增服务成功
        services.value.push(serviceData);
        devLog('✅ 服务新增成功');

        notification.value?.success(
          '服务新增成功',
          `${serviceData.name} 已成功添加到服务列表`,
          4000
        );
      } else {
        // 更新服务成功
        const index = services.value.findIndex(item => item.id === unifiedEditForm.serviceId);
        if (index !== -1) {
          services.value[index] = serviceData;
        }

        devLog('✅ 服务更新成功');

        // 显示成功提示
        const changesList = [];
        if (updateData.price) changesList.push(`服务费: ¥${updateData.price}`);
        if (updateData.commission) changesList.push(`提成: ¥${updateData.commission}`);
        if (updateData.duration) changesList.push(`时长: ${updateData.duration}分钟`);

        notification.value?.success(
          '服务更新成功',
          `${unifiedEditForm.name} 的 ${changesList.join('、')} 已成功更新`,
          4000
        );
      }

      hideUnifiedEditModal();

      // 刷新服务列表
      await loadServices();

    } else {
      const errorText = await response.text();
      let errorMessage = '服务更新失败';

      try {
        const errorData = JSON.parse(errorText);
        if (errorData.message) {
          errorMessage = errorData.message;
        } else if (errorData.error) {
          errorMessage = errorData.error;
        }
      } catch (e) {
        // 如果不是JSON，使用默认错误信息
      }

      const actionText = !unifiedEditForm.serviceId ? '新增' : '更新';
      notification.value?.error(
        `服务${actionText}失败`,
        errorMessage,
        5000
      );
    }

  } catch (error) {
    devError('❌ 服务更新异常:', error);
    const actionText = !unifiedEditForm.serviceId ? '新增' : '更新';
    notification.value?.error(
      '系统异常',
      `服务${actionText}失败，请检查网络连接后重试`,
      5000
    );
  } finally {
    unifiedEditLoading.value = false;
  }
};

// 🔍 多字段搜索模式相关函数

// 🎯 新的点击触发逻辑 - 点击字段名称直接进入搜索模式
const handleClickToSearch = (field = 'name') => {
  // 🎯 过渡期间禁用点击触发，防止误操作
  if (isTransitioning.value[field]) {
    devLog(`🎯 ${getFieldDisplayName(field)}正在过渡中，暂时禁用点击触发`);
    return;
  }

  // 直接进入搜索模式
  enterSearchMode(field);
  devLog(`🔍 点击${getFieldDisplayName(field)} - 直接进入搜索模式`);
};



const enterSearchMode = (field = 'name') => {
  // 🎯 新增：如果正在等待显示排序按钮，清除定时器并重置状态
  if (sortButtonTimers.value[field]) {
    clearTimeout(sortButtonTimers.value[field]);
    sortButtonTimers.value[field] = null;

    // 重置排序按钮状态为加载状态（因为搜索重新开始）
    if (sortButtonStates.value[field]) {
      sortButtonStates.value[field].loading = true;
      sortButtonStates.value[field].disabled = true;
    }

    devLog(`🔄 ${getFieldDisplayName(field)}重新进入搜索，排序按钮重置为加载状态`);
  }

  // 进入指定字段的搜索模式
  searchModes.value[field] = true;

  // 下一帧聚焦对应的输入框
  nextTick(() => {
    const inputRef = searchInputRefs.value[field];
    if (inputRef) {
      inputRef.focus();
      // 如果有内容，全选以便用户直接输入新内容
      if (searchValues.value[field]) {
        inputRef.select();
      }
    }
  });

  devLog(`🔍 进入${getFieldDisplayName(field)}搜索模式`);
};

const exitSearchMode = (field = 'name') => {
  // 🎯 启动过渡状态，防止连续点击误触
  isTransitioning.value[field] = true;

  // 退出指定字段的搜索模式
  searchModes.value[field] = false;
  searchValues.value[field] = '';

  // 🎯 修复：退出搜索模式时不触发刷新，直接清除搜索建议
  updateSearchSuggestions();
  currentPage.value = 1;

  // 🎯 新增：启动排序按钮加载状态
  if (sortButtonStates.value[field]) {
    sortButtonStates.value[field].loading = true;
    sortButtonStates.value[field].disabled = true;

    // 清除之前的定时器（如果存在）
    if (sortButtonTimers.value[field]) {
      clearTimeout(sortButtonTimers.value[field]);
    }

    devLog(`🔄 ${getFieldDisplayName(field)}排序按钮进入加载状态`);
  }

  devLog(`🔍 退出${getFieldDisplayName(field)}搜索模式 - 启动1秒过渡效果`);

  // 🎯 延迟1秒后恢复正常状态，避免误触排序按钮
  const transitionTimer = setTimeout(() => {
    isTransitioning.value[field] = false;

    // 🎯 新增：结束排序按钮加载状态，显示排序按钮
    if (sortButtonStates.value[field]) {
      sortButtonStates.value[field].loading = false;
      sortButtonStates.value[field].disabled = false;
      devLog(`✅ ${getFieldDisplayName(field)}排序按钮加载完成，可以使用`);
    }

    devLog(`🎯 ${getFieldDisplayName(field)}过渡效果完成，恢复正常交互`);
  }, 1000);

  // 🎯 新增：保存定时器引用，以便在重新进入搜索时清除
  sortButtonTimers.value[field] = transitionTimer;
};

const handleSearchBlur = (field = 'name') => {
  // 延迟退出搜索模式，给用户一些时间
  setTimeout(() => {
    if (!searchValues.value[field]?.trim()) {
      exitSearchMode(field);
    }
  }, 200);
};

const handleSearchMouseLeave = (field = 'name') => {
  // 鼠标离开搜索区域时，如果没有搜索内容则自动退出搜索模式
  setTimeout(() => {
    if (!searchValues.value[field]?.trim()) {
      exitSearchMode(field);
    }
  }, 300); // 稍长的延迟，避免意外退出
};

const handleSearchEnter = (event, field = 'name') => {
  // 阻止默认行为，避免重复触发
  event.preventDefault();

  const inputRef = searchInputRefs.value[field];
  if (inputRef) {
    inputRef.blur();
  }

  if (searchValues.value[field]?.trim()) {
    // 显示搜索完成通知
    if (notification.value) {
      notification.value.success(
        '搜索完成',
        `正在显示包含"${searchValues.value[field]}"的${getFieldDisplayName(field)}`,
        2000
      );
    }
  }

  devLog(`🔍 ${getFieldDisplayName(field)}搜索确认:`, searchValues.value[field]);
};



const handleToggleStatus = (record) => {
  record.status = record.status === 'active' ? 'inactive' : 'active';
  devLog('切换状态:', record);
};

// 智能搜索相关方法
const updateSearchSuggestions = () => {
  if (!searchValue.value.trim()) {
    searchSuggestions.value = [];
    return;
  }

  // 获取匹配的服务，最多显示8个建议
  const matches = services.value
    .filter(service => isMatchingService(service, searchValue.value))
    .slice(0, 8);

  searchSuggestions.value = matches;
  highlightedIndex.value = -1;
};

// 🎯 中文输入法事件处理
const handleCompositionStart = () => {
  isComposing.value = true;
  devLog('🎯 中文输入开始');
};

const handleCompositionEnd = () => {
  isComposing.value = false;
  devLog('🎯 中文输入结束');
  // 中文输入结束后，触发搜索处理
  handleSearchInput();
};

const handleSearchInput = (field = 'name') => {
  // 🎯 修复：中文输入法期间不处理输入事件，避免spaspa问题
  if (isComposing.value) {
    devLog('🎯 中文输入中，跳过处理');
    return;
  }

  // 🎯 修复：数据为空时不刷新，输入数据时才刷新
  const hasSearchValue = searchValues.value[field] && searchValues.value[field].trim() !== '';

  // 只有在有搜索内容时才设置加载状态
  if (hasSearchValue) {
    loadingStates.dataLoading = true;
  }

  updateSearchSuggestions();
  // 重置当前页到第一页
  currentPage.value = 1;
  devLog(`${getFieldDisplayName(field)}智能搜索:`, searchValues.value[field]);

  // 只有在有搜索内容时才使用延迟来模拟搜索过程
  if (hasSearchValue) {
    setTimeout(() => {
      loadingStates.dataLoading = false;
    }, 300);
  }
};

const handleSearchDropdownBlur = () => {
  // 延迟隐藏下拉框，以便点击选项
  setTimeout(() => {
    showSearchDropdown.value = false;
  }, 200);
};

const handleSearchKeydown = (event) => {
  if (!showSearchDropdown.value || searchSuggestions.value.length === 0) return;

  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      highlightedIndex.value = Math.min(highlightedIndex.value + 1, searchSuggestions.value.length - 1);
      break;
    case 'ArrowUp':
      event.preventDefault();
      highlightedIndex.value = Math.max(highlightedIndex.value - 1, -1);
      break;
    case 'Enter':
      event.preventDefault();
      if (highlightedIndex.value >= 0) {
        selectSuggestion(searchSuggestions.value[highlightedIndex.value]);
      }
      break;
    case 'Escape':
      showSearchDropdown.value = false;
      highlightedIndex.value = -1;
      break;
  }
};

const selectSuggestion = (suggestion) => {
  searchValue.value = suggestion.name;
  showSearchDropdown.value = false;
  highlightedIndex.value = -1;
  currentPage.value = 1;

  // 聚焦到搜索框
  nextTick(() => {
    if (searchInput.value) {
      searchInput.value.blur();
    }
  });

  devLog('选择服务:', suggestion);
};

const highlightMatch = (text) => {
  if (!searchValue.value.trim()) return text;

  const term = searchValue.value.trim();
  const regex = new RegExp(`(${term})`, 'gi');

  // 高亮汉字匹配
  let highlighted = text.replace(regex, '<mark>$1</mark>');

  // 如果没有直接匹配，尝试高亮拼音匹配的字符
  if (!highlighted.includes('<mark>')) {
    const chars = text.split('');
    const termLower = term.toLowerCase();

    // 检查拼音首字母匹配
    const firstLetters = pinyin(text, { pattern: 'first', toneType: 'none', type: 'array' }).join('').toLowerCase();
    if (firstLetters.includes(termLower)) {
      // 简单高亮整个文本（可以进一步优化为精确字符匹配）
      highlighted = `<mark>${text}</mark>`;
    }
  }

  return highlighted;
};



// 排序相关函数
const handleSort = (field) => {
  // 🎯 新增：检查排序按钮是否被禁用或正在加载
  if (sortButtonStates.value[field] && (sortButtonStates.value[field].disabled || sortButtonStates.value[field].loading)) {
    devLog(`⚠️ ${getFieldDisplayName(field)}排序按钮当前不可用 - 加载中或已禁用`);

    // 显示提示信息
    if (notification.value) {
      notification.value.warning(
        '排序功能暂不可用',
        `${getFieldDisplayName(field)}排序功能正在准备中，请稍候...`,
        2000
      );
    }
    return;
  }

  if (sortField.value === field) {
    // 同一字段：切换排序顺序 asc -> desc -> none
    if (sortOrder.value === 'asc') {
      sortOrder.value = 'desc';
    } else if (sortOrder.value === 'desc') {
      sortField.value = '';
      sortOrder.value = '';
    } else {
      sortOrder.value = 'asc';
    }
  } else {
    // 不同字段：设置为升序
    sortField.value = field;
    sortOrder.value = 'asc';
  }

  // 重置到第一页
  currentPage.value = 1;
  devLog('排序:', { field: sortField.value, order: sortOrder.value });
};



// DeepSeek AI描述生成
const generateDescription = async () => {
  if (!formState.name) return;

  aiDescriptionLoading.value = true;

  try {
    // 调用DeepSeek AI生成服务描述
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-e9464d7a13bd4c63a3a027bf9e819712'
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的中医理疗服务宣传文案专家。请根据服务名称和时长生成吸引人的宣传简介，内容应包含：1）服务的历史起源或传统背景；2）主要功效和好处；3）适用人群和作用机制；4）体现服务时长的专业价值。注意：直接输出简介内容，不要包含标题、副标题或任何格式化标记，控制在100-150字之间。'
          },
          {
            role: 'user',
            content: `请为"${formState.name}"这个理疗服务生成一个宣传简介，服务时长为${formState.duration}分钟。请重点介绍这项服务的起源背景、健康功效、适用人群，以及${formState.duration}分钟时长的专业价值。直接输出简介正文，不要标题。`
          }
        ],
        max_tokens: 200,
        temperature: 0.7
      })
    });

    if (!response.ok) {
      throw new Error(`DeepSeek API请求失败: ${response.status}`);
    }

    const data = await response.json();
    const aiDescription = data.choices[0]?.message?.content?.trim();

    if (aiDescription) {
      formState.description = aiDescription;
      devLog('✅ DeepSeek AI生成简介成功:', aiDescription);
    } else {
      throw new Error('DeepSeek返回的简介为空');
    }

  } catch (error) {
    devError('❌ DeepSeek AI生成简介失败:', error);

    // 降级到本地生成宣传文案
    const fallbackDescriptions = {
      '推拿': `源于古代中医理论，传承千年养生智慧。通过专业手法疏通经络、调和气血，有效缓解现代人因久坐、压力导致的肌肉紧张和疲劳。${formState.duration}分钟的精准调理，让您重获轻松活力，适合办公族、体力劳动者及亚健康人群。`,
      '按摩': `融合东西方按摩精髓，运用科学的力度和节奏，深层放松肌肉组织，促进血液循环和新陈代谢。${formState.duration}分钟的专业护理，能够有效缓解身心疲劳，提升睡眠质量，是现代都市人放松身心、恢复活力的理想选择。`,
      '理疗': `结合现代康复医学与传统中医理论，采用先进设备和专业手法，针对性改善身体机能。${formState.duration}分钟的系统调理，有助于缓解慢性疼痛、改善体质，适合康复期患者、慢性病调理及预防保健需求人群。`,
      '足疗': `源于中医"足部反射区"理论，通过刺激足底穴位调节全身机能。${formState.duration}分钟的专业护理，能够改善睡眠、缓解疲劳、增强免疫力，特别适合失眠、消化不良、免疫力低下的人群，是简单有效的日常保健方式。`,
      '艾灸': `承载着数千年中医文化，以优质艾草为原料，通过温热刺激特定穴位，温经散寒、扶阳固脱。${formState.duration}分钟的温和调理，能够改善体寒、增强体质、调理脾胃，适合体质虚弱、手脚冰凉、消化功能差的人群。`,
      '养生': `秉承"治未病"理念，融合传统养生智慧与现代健康管理。通过${formState.duration}分钟的综合调理，平衡阴阳、调和气血，提升身体自愈能力。适合注重健康管理、预防疾病、延缓衰老的现代人群。`,
      '保健': `基于现代预防医学理念，结合传统保健方法，为您提供个性化健康管理方案。${formState.duration}分钟的专业服务，能够提升身体机能、预防疾病、维护健康状态，是现代人健康生活的重要组成部分。`
    };

    let description = `融合传统智慧与现代技术，通过${formState.duration}分钟的专业调理，为您提供优质的健康管理服务，助您重获身心平衡与活力。`;

    for (const [keyword, desc] of Object.entries(fallbackDescriptions)) {
      if (formState.name.includes(keyword)) {
        description = desc;
        break;
      }
    }

    formState.description = description;
    notification.value?.warning(
      'AI生成失败',
      '已为您使用默认简介，请检查网络连接或稍后重试AI生成功能',
      6000
    );
  } finally {
    aiDescriptionLoading.value = false;
  }
};

// 基于DeepSeek描述生成图片
const generateImageFromDescription = async (description) => {
  if (!description) return;

  try {
    // 使用DeepSeek生成图片提示词
    const promptResponse = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer sk-e9464d7a13bd4c63a3a027bf9e819712'
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的理疗服务图片提示词生成专家。请根据服务简介生成适合的图片提示词，用于AI图片生成。提示词应该描述温馨的理疗环境、专业的设备器具、舒适的氛围等视觉元素，体现服务的专业性和舒适感，控制在50字以内。'
          },
          {
            role: 'user',
            content: `请根据这个服务简介生成图片提示词："${description}"`
          }
        ],
        max_tokens: 100,
        temperature: 0.8
      })
    });

    if (promptResponse.ok) {
      const promptData = await promptResponse.json();
      const imagePrompt = promptData.choices[0]?.message?.content?.trim();

      if (imagePrompt) {
        devLog('✅ DeepSeek生成图片提示词:', imagePrompt);

        // 调用百度文心一言生成图片
        await generateBaiduImage(imagePrompt);
      }
    } else {
      throw new Error('生成图片提示词失败');
    }

  } catch (error) {
    devError('❌ 基于描述生成图片失败:', error);
    // 降级到默认图片生成
    await generateAIImage();
  }
};

// 百度文心一言图片生成
const generateBaiduImage = async (prompt) => {
  try {
    // 调用百度文心一言API生成图片
    const response = await fetch('/api/baidu/generate-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        prompt: prompt,
        apiKey: 'bce-v3/ALTAK-fpYd2rn5cS653qxilQ1fm/ec7c7b2079b7adeb584348a13fed4640d3b09f06'
      })
    });

    if (response.ok) {
      const data = await response.json();
      if (data.imageUrl) {
        formState.image = data.imageUrl;
        devLog('✅ 百度文心一言生成图片成功:', data.imageUrl);
        return;
      }
    }

    throw new Error('百度API调用失败');

  } catch (error) {
    devError('❌ 百度文心一言图片生成失败:', error);

    // 降级到模拟图片
    const mockImages = [
      'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1540555700478-4be289fbecef?w=400&h=300&fit=crop',
      'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400&h=300&fit=crop'
    ];

    const randomImage = mockImages[Math.floor(Math.random() * mockImages.length)];
    formState.image = randomImage;
    devLog('🔄 使用降级图片:', randomImage);
  }
};
// 火山引擎图片生成
const generateWithVolcengine = async () => {
  if (!formState.name || !formState.description) {
    devWarn('⚠️ 请先填写服务名称和描述');
    return;
  }

  volcengineGenerating.value = true;

  try {
    devLog('🌋 开始火山引擎图片生成...');
    devLog(`📝 服务名称: ${formState.name}`);
    devLog(`📄 服务描述: ${formState.description}`);

    // 调用火山引擎服务（通过后端）
    const imageUrl = await aiService.generateServiceImage(
      formState.name,
      formState.description
    );

    devLog('🔍 火山引擎生成结果:', imageUrl);

    if (imageUrl) {
      // 将生成的图片设置到表单中
      formState.image = imageUrl;

      devLog('✅ 火山引擎图片生成成功');
      devLog('🖼️ 图片URL:', imageUrl);

      // 等待一下确保图片设置成功
      await nextTick();

      // 记录成功日志
      devLog('✅ 火山引擎图片生成成功！图片已应用到服务中');

    } else {
      throw new Error('图片生成失败，未返回有效图片URL');
    }

  } catch (error) {
    devError('❌ 火山引擎图片生成失败:', error);
    // 移除alert提示框，只在控制台记录错误
  } finally {
    volcengineGenerating.value = false;
  }
};







// 图片加载错误处理
const handleImageError = (event) => {
  devLog('图片加载失败:', event.target.src);
  event.target.style.display = 'none';
};

// 服务列表图片加载错误处理
const handleServiceImageError = (event) => {
  devLog('服务图片加载失败:', event.target.src);
  // 隐藏整个图片容器
  const imageContainer = event.target.closest('.service-image');
  if (imageContainer) {
    imageContainer.style.display = 'none';
  }
  // 调整服务信息样式
  const serviceInfo = imageContainer?.nextElementSibling;
  if (serviceInfo) {
    serviceInfo.classList.add('no-image');
  }
};

// 搜索建议图片加载错误处理
const handleSuggestionImageError = (event) => {
  devLog('搜索建议图片加载失败:', event.target.src);
  // 隐藏整个图片容器
  const imageContainer = event.target.closest('.suggestion-image');
  if (imageContainer) {
    imageContainer.style.display = 'none';
  }
  // 调整建议内容样式
  const suggestionContent = imageContainer?.nextElementSibling;
  if (suggestionContent) {
    suggestionContent.classList.add('no-image');
  }
};

const handleSubmit = async () => {
  try {
    // 表单验证 - 符合CI_CD_STANDARDS.md表单验证规范
    if (!validateForm()) {
      safeNotification(
        'error',
        '表单验证失败',
        '请检查输入内容，确保所有必填字段都已正确填写',
        4000
      );
      return;
    }

    confirmLoading.value = true;
    loadingStates.serviceSubmit = true;

    // 验证必填字段
    if (!formState.name || !formState.name.trim()) {
      devError('❌ 服务名称不能为空');
      return;
    }

    // 准备保存的数据 - 只发送必要字段
    const serviceData = {
      name: formState.name.trim(),
      description: formState.description || '',
      price: formState.price ? parseFloat(formState.price) : 0,
      commission: formState.commission ? parseFloat(formState.commission) : 0,
      duration: formState.duration ? parseInt(formState.duration) : 30,
      image: formState.image || '',
      status: formState.status || 'active'
    };

    // 验证数值字段
    if (isNaN(serviceData.price) || serviceData.price < 0) {
      devError('❌ 价格必须是有效的正数');
      return;
    }

    if (isNaN(serviceData.commission) || serviceData.commission < 0) {
      devError('❌ 提成必须是有效的正数');
      return;
    }

    if (serviceData.commission > serviceData.price) {
      devError('❌ 提成不能超过服务费');
      notification.value?.error(
        '数据验证失败',
        '提成不能超过服务费，请重新设置合理的提成',
        5000
      );
      return;
    }

    if (isNaN(serviceData.duration) || serviceData.duration <= 0) {
      devError('❌ 服务时长必须是有效的正数');
      return;
    }



    devLog('📤 准备保存的服务数据:', serviceData);
    devLog('📋 当前表单状态:', formState);

    if (formState.id) {
      // 编辑现有服务
      const response = await fetch(`/api/v1/services/${formState.id}/`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(serviceData)
      });

      if (response.ok) {
        const updatedService = await response.json();
        const index = services.value.findIndex(item => item.id === formState.id);
        if (index !== -1) {
          services.value[index] = updatedService;
        }
        devLog('✅ 服务更新成功');
        notification.value?.success(
          '服务更新成功',
          `${serviceData.name} 的信息已成功更新`,
          3000
        );
      } else {
        const errorText = await response.text();
        let errorMessage = `服务更新失败: ${response.status} ${response.statusText}`;

        try {
          const errorData = JSON.parse(errorText);
          if (errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (e) {
          // 如果不是JSON，使用原始错误文本
        }

        devError('❌ 服务更新失败:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });

        notification.value?.error(
          '服务更新失败',
          errorMessage,
          5000
        );
        throw new Error(errorMessage);
      }
    } else {
      // 创建新服务
      const response = await fetch('/api/v1/services/', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(serviceData)
      });

      if (response.ok) {
        const newService = await response.json();
        services.value.unshift(newService);
        devLog('✅ 服务创建成功');
        notification.value?.success(
          '服务创建成功',
          `${serviceData.name} 已成功添加到服务列表`,
          3000
        );
      } else {
        const errorText = await response.text();
        let errorMessage = `服务创建失败: ${response.status} ${response.statusText}`;

        try {
          const errorData = JSON.parse(errorText);
          if (errorData.message) {
            errorMessage = errorData.message;
          } else if (errorData.error) {
            errorMessage = errorData.error;
          }
        } catch (e) {
          // 如果不是JSON，使用原始错误文本
        }

        devError('❌ 服务创建失败:', {
          status: response.status,
          statusText: response.statusText,
          error: errorText
        });

        notification.value?.error(
          '服务创建失败',
          errorMessage,
          5000
        );
        throw new Error(errorMessage);
      }
    }

    hideModal();
  } catch (error) {
    devError('❌ 保存服务失败:', error);
    // 这里可以添加错误提示
  } finally {
    confirmLoading.value = false;
    loadingStates.serviceSubmit = false;
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    // 设置短暂的加载状态
    loadingStates.dataLoading = true;
    currentPage.value--;

    setTimeout(() => {
      loadingStates.dataLoading = false;
    }, 150);
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    // 设置短暂的加载状态
    loadingStates.dataLoading = true;
    currentPage.value++;

    setTimeout(() => {
      loadingStates.dataLoading = false;
    }, 150);
  }
};

const goToPage = (page) => {
  // 设置短暂的加载状态
  loadingStates.dataLoading = true;
  currentPage.value = page;

  setTimeout(() => {
    loadingStates.dataLoading = false;
  }, 150);
};

const handlePageSizeChange = () => {
  // 设置短暂的加载状态
  loadingStates.dataLoading = true;

  // 当改变每页显示数量时，重置到第一页
  currentPage.value = 1;

  setTimeout(() => {
    loadingStates.dataLoading = false;
  }, 200);
};



const getStatusText = (status) => {
  const texts = {
    active: '上架',
    inactive: '下架'
  };
  return texts[status] || status;
};

// 🎯 状态筛选相关函数 - 新增
const toggleStatusFilter = () => {
  // 循环切换：全部 -> 上架 -> 下架 -> 全部
  if (statusFilter.value === 'all') {
    statusFilter.value = 'active';
  } else if (statusFilter.value === 'active') {
    statusFilter.value = 'inactive';
  } else {
    statusFilter.value = 'all';
  }

  // 重置到第一页
  currentPage.value = 1;

  devLog(`🔍 状态筛选切换为: ${getStatusFilterLabel()}`);
};

const getStatusFilterClass = () => {
  return {
    'filter-all': statusFilter.value === 'all',
    'filter-active': statusFilter.value === 'active',
    'filter-inactive': statusFilter.value === 'inactive'
  };
};

const getStatusFilterIcon = () => {
  const icons = {
    all: '📋',      // 全部
    active: '✅',   // 上架
    inactive: '❌'  // 下架
  };
  return icons[statusFilter.value] || '📋';
};

const getStatusFilterLabel = () => {
  const labels = {
    all: '全部',
    active: '仅上架',
    inactive: '仅下架'
  };
  return labels[statusFilter.value] || '全部';
};

const getStatusFilterTitle = () => {
  const titles = {
    all: '点击筛选仅显示上架服务',
    active: '点击筛选仅显示下架服务',
    inactive: '点击显示全部服务'
  };
  return titles[statusFilter.value] || '点击切换筛选状态';
};



// 轮廓调试功能 - 强制约定版本
const enableOutlineDebug = () => {
  if (process.env.NODE_ENV !== 'development') return;

  devLog('🎯 启用轮廓调试 - 强制约定模式');
  devLog('📋 将自动打印所有元素坐标并检查完美规则合规性');

  // 添加轮廓调试样式
  const style = document.createElement('style');
  style.id = 'outline-debug-service-management';
  style.textContent = `
    /* 🎯 服务管理页面轮廓调试 */
    .table-container {
      outline: 3px solid blue !important;
      outline-offset: -2px;
    }

    .table-body {
      outline: 2px solid green !important;
      outline-offset: -1px;
    }

    /* 🎯 表头轮廓调试 */
    .smart-table-header {
      outline: 2px solid cyan !important;
      outline-offset: -2px;
    }

    .header-columns {
      outline: 1px solid lightblue !important;
      outline-offset: -1px;
    }

    .header-cell {
      outline: 1px solid skyblue !important;
      outline-offset: -1px;
    }

    .pagination-container {
      outline: 3px solid orange !important;
      outline-offset: -2px;
    }

    .data-row {
      outline: 1px solid purple !important;
      outline-offset: -1px;
    }

    .data-row:last-child {
      outline: 2px solid red !important;
      outline-offset: -1px;
    }

    .action-fragments {
      outline: 1px solid yellow !important;
      outline-offset: -1px;
    }

    /* 显示元素信息 - 按照橙色边界标准优化 */
    .table-container::before {
      content: "表格容器 (蓝色边界)";
      position: absolute;
      top: -25px;
      left: 0;
      background: blue;
      color: white;
      padding: 2px 6px;
      font-size: 12px;
      z-index: var(--service-z-debug);
      white-space: nowrap;
      border-radius: 2px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.3);
    }

    /* 🎯 表头标签显示 */
    .smart-table-header::before {
      content: "表头容器 (青色边界)";
      position: absolute;
      top: -25px;
      left: 0;
      background: cyan;
      color: black;
      padding: 2px 6px;
      font-size: 12px;
      z-index: var(--service-z-debug);
      white-space: nowrap;
      border-radius: 2px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.3);
    }

    .header-columns::before {
      content: "表头行 (浅蓝边界)";
      position: absolute;
      top: -50px;
      left: 100px;
      background: lightblue;
      color: black;
      padding: 2px 6px;
      font-size: 12px;
      z-index: var(--service-z-debug);
      white-space: nowrap;
      border-radius: 2px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.3);
    }

    .table-body::before {
      content: "表格主体 (绿色边界)";
      position: absolute;
      top: -25px;
      left: 0;
      background: green;
      color: white;
      padding: 2px 6px;
      font-size: 12px;
      z-index: var(--service-z-debug);
      white-space: nowrap;
      border-radius: 2px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.3);
    }

    .pagination-container::before {
      content: "翻页组件 (橙色边界) - 高度30px";
      position: absolute;
      top: -20px; /* 调整标签位置以适应新高度 */
      left: 0;
      background: orange;
      color: white;
      padding: 1px 4px; /* 减小标签内边距 */
      font-size: 10px; /* 减小标签字体 */
      z-index: var(--service-z-debug);
      white-space: nowrap;
      border-radius: 2px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.3);
    }

    .data-row:last-child::after {
      content: "最后一行 (红色边界)";
      position: absolute;
      top: -25px;
      left: 0;
      background: red;
      color: white;
      padding: 2px 6px;
      font-size: 12px;
      z-index: var(--service-z-debug);
      white-space: nowrap;
      border-radius: 2px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.3);
    }

    /* 🎯 为轮廓元素添加统一的颜色标识提示 - 按照橙色边界标准优化 */
    .data-row:first-child::before {
      content: "数据行 (紫色边界)";
      position: absolute;
      top: -25px;
      left: 0;
      background: purple;
      color: white;
      padding: 2px 6px;
      font-size: 12px;
      z-index: var(--service-z-debug);
      white-space: nowrap;
      border-radius: 2px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.3);
    }

    .data-row:first-child .action-fragments::before {
      content: "操作按钮 (黄色边界)";
      position: absolute;
      top: -25px;
      left: 0;
      background: yellow;
      color: black;
      padding: 2px 6px;
      font-size: 12px;
      z-index: var(--service-z-debug);
      white-space: nowrap;
      border-radius: 2px;
      box-shadow: 0 1px 3px rgba(0,0,0,0.3);
    }
  `;
  document.head.appendChild(style);

  // 添加快捷键
  document.addEventListener('keydown', (e) => {
    if (e.ctrlKey && e.shiftKey && e.key === 'D') {
      e.preventDefault();
      toggleOutlineDebug();
    }
  });

  devLog('🎯 轮廓调试已启用');
};

const toggleOutlineDebug = () => {
  const style = document.getElementById('outline-debug-service-management');
  if (style) {
    style.remove();
    devLog('🎯 轮廓调试已禁用');
  } else {
    enableOutlineDebug();
  }
};

// 打印红色区域坐标函数
const printRedCoordinates = () => {
  if (process.env.NODE_ENV !== 'development') return;

  devLog('🔍 开始检测红色区域(最后一行)坐标...');
  devLog('='.repeat(60));

  // 立即输出基础信息
  devLog('📋 轮廓调试坐标检测工具已启动');
  devLog('🎯 正在查找页面元素...');

  // 查找关键元素
  devLog('🔍 正在查找关键元素...');
  const tableContainer = document.querySelector('.table-container');
  const tableBody = document.querySelector('.table-body');
  const lastRow = document.querySelector('.data-row:last-child');
  const paginationContainer = document.querySelector('.pagination-container');

  devLog('📊 元素查找结果:');
  devLog(`  表格容器: ${tableContainer ? '✅ 找到' : '❌ 未找到'}`);
  devLog(`  表格主体: ${tableBody ? '✅ 找到' : '❌ 未找到'}`);
  devLog(`  最后一行: ${lastRow ? '✅ 找到' : '❌ 未找到'}`);
  devLog(`  翻页组件: ${paginationContainer ? '✅ 找到' : '❌ 未找到'}`);

  if (!tableContainer || !tableBody || !lastRow || !paginationContainer) {
    devError('❌ 未找到必要的元素，无法进行坐标检测');
    devLog('💡 请确保页面已完全加载，并且在服务管理页面运行');
    return;
  }

  devLog('✅ 所有元素查找成功，开始坐标检测...');

  // 将调试函数暴露到全局，方便手动调用
  if (typeof window !== 'undefined') {
    window.printRedCoordinates = printRedCoordinates;
    window.enableOutlineDebug = enableOutlineDebug;
    window.toggleOutlineDebug = toggleOutlineDebug;
    devLog('🎯 调试函数已暴露到全局: window.printRedCoordinates(), window.enableOutlineDebug(), window.toggleOutlineDebug()');
  }

  // 获取元素的边界矩形
  const tableContainerRect = tableContainer.getBoundingClientRect();
  const tableBodyRect = tableBody.getBoundingClientRect();
  const lastRowRect = lastRow.getBoundingClientRect();
  const paginationRect = paginationContainer.getBoundingClientRect();

  devLog('📊 元素坐标详情:');
  devLog('');

  // 红色区域 (最后一行) - 重点检查
  devLog('🔴 红色区域 (最后一行) 坐标:');
  devLog(`  上边界 (top):    ${Math.round(lastRowRect.top)}px`);
  devLog(`  下边界 (bottom): ${Math.round(lastRowRect.bottom)}px`);
  devLog(`  左边界 (left):   ${Math.round(lastRowRect.left)}px`);
  devLog(`  右边界 (right):  ${Math.round(lastRowRect.right)}px`);
  devLog(`  宽度 (width):    ${Math.round(lastRowRect.width)}px`);
  devLog(`  高度 (height):   ${Math.round(lastRowRect.height)}px`);
  devLog('');

  // 🎯 核对问题分析
  devLog('🎯 完美规则核对分析:');
  devLog('='.repeat(60));

  // 检查1: 红色区域是否在绿色区域内
  devLog('📋 检查1: 红色区域边界约束');
  const redInGreen = {
    top: lastRowRect.top >= tableBodyRect.top,
    bottom: lastRowRect.bottom <= tableBodyRect.bottom,
    left: lastRowRect.left >= tableBodyRect.left,
    right: lastRowRect.right <= tableBodyRect.right
  };

  devLog(`  红色上边界在绿色内: ${redInGreen.top ? '✅' : '❌'} (${Math.round(lastRowRect.top)} >= ${Math.round(tableBodyRect.top)})`);
  devLog(`  红色下边界在绿色内: ${redInGreen.bottom ? '✅' : '❌'} (${Math.round(lastRowRect.bottom)} <= ${Math.round(tableBodyRect.bottom)})`);
  devLog(`  红色左边界在绿色内: ${redInGreen.left ? '✅' : '❌'} (${Math.round(lastRowRect.left)} >= ${Math.round(tableBodyRect.left)})`);
  devLog(`  红色右边界在绿色内: ${redInGreen.right ? '✅' : '❌'} (${Math.round(lastRowRect.right)} <= ${Math.round(tableBodyRect.right)})`);

  const allInBounds = Object.values(redInGreen).every(v => v);
  devLog(`  🎯 边界约束结果: ${allInBounds ? '✅ 完全合规' : '❌ 存在越界'}`);

  // 检查2: 间距分析
  const gaps = {
    redToGreenBottom: tableBodyRect.bottom - lastRowRect.bottom,
    greenToBlueBottom: tableContainerRect.bottom - tableBodyRect.bottom
  };

  devLog('');
  devLog('📋 检查2: 关键间距分析');
  devLog(`  红色底部到绿色底部: ${Math.round(gaps.redToGreenBottom)}px ${gaps.redToGreenBottom >= 5 ? '✅' : '⚠️'}`);
  devLog(`  绿色底部到蓝色底部: ${Math.round(gaps.greenToBlueBottom)}px ${Math.abs(gaps.greenToBlueBottom) <= 2 ? '✅' : '❌'}`);

  // 总结
  devLog('');
  devLog('📊 完美规则核对总结:');
  const issues = [];
  if (!allInBounds) issues.push('红色区域超出绿色区域边界');
  if (gaps.redToGreenBottom < 5) issues.push('红色区域底部间距不足');
  if (Math.abs(gaps.greenToBlueBottom) > 2) issues.push('绿色区域与蓝色区域底部不对齐');

  if (issues.length === 0) {
    devLog('🎉 完美！所有坐标都符合完美规则！');
  } else {
    devLog(`❌ 发现 ${issues.length} 个问题:`);
    issues.forEach((issue, index) => {
      devLog(`  ${index + 1}. ${issue}`);
    });
  }
};

// 初始化
onMounted(() => {
  loadServices();

  // 🔧 调试SciFi通知组件挂载状态 - 符合DEVELOPMENT_CONSTRAINTS.md调试要求
  nextTick(() => {
    console.log('🔍 组件挂载后调试信息:');
    debugNotification();

    // 测试通知功能
    if (notification.value && typeof notification.value.info === 'function') {
      console.log('✅ SciFi通知组件挂载成功，功能正常');
    } else {
      console.error('❌ SciFi通知组件挂载失败或方法不可用');
    }
  });

  // 启用轮廓调试（开发环境）
  if (process.env.NODE_ENV === 'development') {
    devLog('🎯 开发环境检测成功，轮廓调试已禁用以保持与其他页面一致');

    setTimeout(() => {
      devLog('🎯 轮廓调试已禁用 - 保持表头视觉一致性');
      // enableOutlineDebug(); // 已禁用以保持与其他页面一致
      devLog('🎯 服务管理页面轮廓调试已禁用');
      devLog('📋 如需调试，请手动调用 window.enableOutlineDebug()');

      // 自动打印红色区域坐标
      setTimeout(() => {
        devLog('🎯 准备打印红色区域坐标...');
        printRedCoordinates();
      }, 2000);
    }, 1000);
  }
});

// 清理
onUnmounted(() => {
  if (autoGenerateTimer) {
    clearTimeout(autoGenerateTimer);
  }


});
</script>

<style scoped>
/* ========================================
   🎨 服务管理页面样式系统
   ========================================

   📋 样式组织结构:
   1. CSS变量定义 (全局色彩、尺寸、层级)
   2. 基础容器样式 (.picasso-services)
   3. 操作栏样式 (.action-toolbar)
   4. 表格样式 (.data-cubism)
   5. 模态框样式 (.modal-overlay)
   6. 响应式适配 (@media queries)

   🔧 样式冲突解决:
   - 移除局部背景设置，继承全局背景
   - 统一z-index层级管理
   - 规范化CSS变量命名
   - 清理重复样式定义

   ⚠️ 重要说明:
   - 所有样式都使用scoped作用域
   - 背景由AdminLayout统一管理
   - 响应式设计支持1024px-4K分辨率
   ======================================== */

/* 🎨 CSS变量定义 - 统一管理设计令牌 */
:root {
  /* 色彩系统 - 基于毕加索艺术风格 */
  --service-primary: #8b5cf6;           /* 紫色主色 */
  --service-primary-dark: #7c3aed;      /* 深紫色 */
  --service-secondary: #a855f7;         /* 次要紫色 */
  --service-secondary-dark: #9333ea;    /* 深次要紫色 */
  --service-accent: #f472b6;            /* 粉紫色强调色 */
  --service-accent-dark: #ec4899;       /* 深粉紫色 */
  --service-accent-light: #fbbf24;      /* 浅强调色 */
  --service-border-light: #c084fc;      /* 浅边框色 */
  --service-border-medium: #a78bfa;     /* 中等边框色 */
  --service-text-primary: #2d1b69;      /* 主要文字色 */
  --service-text-secondary: #4c1d95;    /* 次要文字色 */

  /* 尺寸系统 - 动态缩放支持 */
  --service-padding: clamp(8px, 1.5vw, 20px);
  --service-margin: clamp(4px, 1vw, 16px);
  --service-border-radius: clamp(4px, 0.8vw, 12px);
  --service-font-small: clamp(10px, 1vw, 14px);
  --service-font-medium: clamp(12px, 1.2vw, 16px);
  --service-font-large: clamp(14px, 1.4vw, 20px);
  --service-font-title: clamp(20px, 2.5vw, 48px);

  /* Z-index层级系统 - 统一管理，避免层级冲突 */
  --service-z-base: 1;
  --service-z-content: 10;
  --service-z-dropdown: 100;
  --service-z-toolbar: 200;
  --service-z-table-header: 300;
  --service-z-tooltip: 500;
  --service-z-modal-backdrop: 1000;
  --service-z-modal: 1001;
  --service-z-toast: 2000;
  --service-z-debug: 9999;
}

/* ========================================
   📦 主容器样式 - 服务管理页面根容器
   ======================================== */
.picasso-services {
  /* 🔧 布局系统 - Flexbox垂直布局 */
  display: flex;

  /* 🔧 布局定位 - 固定定位，占满除侧边栏外的全部空间 */
  position: fixed;

  /* 🎯 层级管理 - 确保在AdminLayout内容层之上 */
  z-index: var(--service-z-base);

  /* 🎯 阶段一：使用viewport units实现真正的比例缩放 */

  /* 🎯 基于PostCSS的响应式布局 - 使用px值让PostCSS自动转换 */
  width: calc(100vw - 180px); /* PostCSS会转换为rem，确保响应式 */
  height: 100vh;
  min-height: 100vh;

  /* 内边距：30px / 1512px * 100 = 1.984vw */
  padding: 1.984vw;
  box-sizing: border-box;
  overflow: hidden;

  /* 📝 字体系统 - 动态缩放支持 */
  font-family: 'Arial Black', sans-serif;
  font-size: clamp(12px, 1.2vw, 18px);

  /* 🎨 背景处理 - 继承AdminLayout的统一背景，不设置局部背景 */
  background: transparent; /* 重要：移除局部背景，避免与全局背景冲突 */

  /* 🎯 响应式缩放 - 基于1512x768基准 */
  transform: scale(var(--scale-factor));
  inset: 0 0 0 180px;  /* 侧边栏宽度 */
  transform-origin: top left; /* 统一主内容区边距 */
  flex-direction: column;
}

/* ========================================
   🎬 动画定义 - 毕加索风格流动动画
   ======================================== */
@keyframes picassoFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 0% 50%; }
  100% { background-position: 50% 0%; }
}







.search-suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgb(255 182 193 / 20%);
}

.search-suggestion-item:last-child {
  border-bottom: none;
}

.search-suggestion-item:hover,
.search-suggestion-item.highlighted {
  background: linear-gradient(135deg,
    rgb(255 182 193 / 30%),
    rgb(255 218 185 / 30%)
  );
  transform: translateX(3px);
}

/* 搜索建议图片样式 - 适配1024x512横向比例 */
.suggestion-image {
  width: 48px;  /* 增加宽度适应2:1横向比例 */
  height: 24px; /* 减少高度，保持2:1比例 */
  border-radius: 6px;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
  background: linear-gradient(45deg, #8e44ad, #9b59b6); /* 作为加载时的背景 */
}

.suggestion-image img {
  display: block;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
  object-fit: cover;
}

.suggestion-image img:hover {
  transform: scale(1.05);
}

.suggestion-content {
  flex: 1;
  min-width: 0;
}

/* 没有图片时的搜索建议内容样式调整 */
.suggestion-content.no-image {
  margin-left: 0; /* 移除左边距，因为没有图片 */
}

.suggestion-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.3;
  color: #2d1b69;
  margin-bottom: 4px;
}

.suggestion-name :deep(mark) {
  padding: 1px 3px;
  border-radius: 3px;
  font-weight: 600;
  color: #2d1b69;
  background: linear-gradient(135deg, #ffb6c1, #ffd700);
}

.suggestion-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  opacity: 0.8;
}

.suggestion-price {
  font-weight: 600;
  color: #e74c3c;
}

.suggestion-status {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.suggestion-status.status-active {
  border: 1px solid rgb(16 185 129 / 30%);
  color: #10b981;
  background: rgb(16 185 129 / 20%);
}

.suggestion-status.status-inactive {
  border: 1px solid rgb(239 68 68 / 30%);
  color: #ef4444;
  background: rgb(239 68 68 / 20%);
}

.filter-cubism {
  display: flex;
  gap: 10px;
}

.filter-fragment {
  padding: 12px 15px;
  border: none;
  border: 2px solid rgb(173 216 230 / 40%);
  border-radius: 0 20px;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg,
    rgb(173 216 230 / 80%),
    rgb(135 206 235 / 70%),
    rgb(255 255 255 / 90%)
  );
  box-shadow: 3px 3px 10px rgb(173 216 230 / 30%);
  transform: skew(-3deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.filter-fragment:focus {
  box-shadow: 5px 5px 15px rgb(142 68 173 / 40%);
  transform: skew(-3deg) scale(1.02);
  outline: none;
}

.action-cubism {
  display: flex;
  gap: 10px;
}

.action-cube {
  width: 120px;
  height: 45px;
  border-radius: 15px 0;
  transform: perspective(600px) rotateX(15deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-cube:hover {
  transform: perspective(600px) rotateX(15deg) scale(1.05);
}

.refresh-cube {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
}

.add-cube {
  background: linear-gradient(45deg, #4ecdc4, #45b7d1);
}

.cube-face {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 15px 0;
  font-weight: bold;
  color: white;
  box-shadow: 0 5px 15px rgb(0 0 0 / 30%);
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.cube-icon {
  font-size: 1.2rem;
}

.cube-text {
  font-size: 0.9rem;
}

/* 🎯 主内容区 - 完全透明背景，与菜单栏对齐 */
.data-cubism {
  display: flex;
  position: relative;
  z-index: var(--z-base);
  padding: 0 20px !important;
  border: none !important; /* 移除边框 */
  border-radius: 16px; /* 保持圆角 */
  overflow: hidden;

  /* 🎯 完全透明背景 - 直接显示背景图 */
  background: transparent !important; /* 完全透明 */
  box-shadow: none !important; /* 移除阴影 */

  /* 🚨 移除梯形变形效果 */
  transform: none !important; /* 移除 perspective rotateX */ /* 移除模糊效果 */
  backdrop-filter: none !important; /* 移除模糊效果 */
  margin-top: -10px !important; /* 🎯 调整为-10px，使数据行与菜单项精确对齐 */
  flex: 1;
  flex-direction: column;
}

/* 🎯 表格容器 - 为翻页组件预留空间，避免重叠 */
.table-container {
  display: flex;

  /* 🎯 使用calc()为翻页组件预留50px空间，PostCSS会自动转换 */
  height: calc(100vh - 120px) !important; /* 预留顶部padding和翻页组件空间 */
  max-height: calc(100vh - 120px) !important;
  min-height: 400px !important; /* 设置最小高度确保基本可用性 */
  padding: 0 5px 4px !important; /* PostCSS会自动转换为rem */
  border-radius: 12px !important; /* PostCSS会自动转换为rem */
  overflow: hidden;

  /* min-height: 400px; 注释掉，避免强制拉伸 */

  /* 🎯 完全透明背景 */
  background: transparent !important; /* 完全透明 */
  flex: 1;
  flex-direction: column;
  backdrop-filter: none !important; /* 移除模糊效果 */

 /* 移除模糊效果 */
}

/* 智能表头样式 */
.smart-table-header {
  position: sticky;
  top: 0;
  z-index: var(--service-z-table-header);
  border-radius: 15px 15px 0 0;
  background: rgb(139 92 246 / 15%);
  box-shadow: 0 2px 8px rgb(139 92 246 / 20%);
  backdrop-filter: blur(10px);
  margin-bottom: 2px;
  flex-shrink: 0;
}

/* 搜索工具栏 */
.header-toolbar {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid rgb(139 92 246 / 20%);
  background: linear-gradient(135deg, rgb(139 92 246 / 10%) 0%, rgb(244 114 182 / 10%) 100%);
}

/* 搜索输入框 */
.header-search-input {
  width: 300px;
  height: 36px;
  padding: 0 12px;
  border: 2px solid rgb(139 92 246 / 30%);
  border-radius: 18px;
  font-size: 14px;
  background: rgb(255 255 255 / 90%);
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: rgb(139 92 246 / 60%);
    box-shadow: 0 0 0 3px rgb(139 92 246 / 10%);
  }

  &::placeholder {
    color: #999;
  }
}

/* 搜索下拉框 */
.search-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  left: 0;
  z-index: var(--service-z-dropdown);
  max-height: 320px;
  border: 2px solid rgb(139 92 246 / 30%);
  border-radius: 12px;
  background: rgb(255 255 255 / 98%);
  box-shadow: 0 8px 25px rgb(0 0 0 / 15%);
  overflow-y: auto;
  backdrop-filter: blur(10px);
  margin-top: 2px;
}

.search-suggestion-item {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid rgb(139 92 246 / 10%);

  &:last-child {
    border-bottom: none;
  }

  &:hover,
  &.highlighted {
    background: rgb(139 92 246 / 10%);
    transform: translateX(3px);
  }
}

.suggestion-image {
  width: 48px;
  height: 24px;
  border-radius: 6px;
  overflow: hidden;
  margin-right: 12px;
  flex-shrink: 0;
  background: linear-gradient(45deg, #8e44ad, #9b59b6);

  img {
    display: block;
    width: 100%;
    height: 100%;
    transition: transform 0.3s ease;
    object-fit: cover;

    &:hover {
      transform: scale(1.05);
    }
  }
}

.suggestion-content {
  flex: 1;
  min-width: 0;

  &.no-image {
    margin-left: 0;
  }
}

.suggestion-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.3;
  color: #2d1b69;
  margin-bottom: 4px;

  :deep(mark) {
    padding: 1px 3px;
    border-radius: 3px;
    font-weight: 600;
    color: #2d1b69;
    background: linear-gradient(135deg, #ffb6c1, #ffd700);
  }
}

.suggestion-meta {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 12px;
  opacity: 0.8;
}

.suggestion-price {
  font-weight: 600;
  color: #e74c3c;
}

.suggestion-status {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;

  &.status-active {
    border: 1px solid rgb(16 185 129 / 30%);
    color: #10b981;
    background: rgb(16 185 129 / 20%);
  }

  &.status-inactive {
    border: 1px solid rgb(239 68 68 / 30%);
    color: #ef4444;
    background: rgb(239 68 68 / 20%);
  }
}





/* 列标题行 - 🎯 增加高度，改善视觉效果 */
.header-columns {
  display: flex;
  height: 60px !important; /* 🎯 增加高度到60px，改善视觉比例 */
  padding: 0 !important; /* 移除上下padding，使用固定高度 */
  line-height: 60px !important; /* 🎯 行高与容器高度一致：60px */
  background: transparent !important; /* 🎨 移除覆盖背景，让父元素的紫色背景显示 */
}

/* 🎯 新的搜索+排序表头布局 */
.header-with-search-sort {
  display: flex !important;
  justify-content: space-between !important;
  align-items: center !important;
  padding: 0 8px !important;
  line-height: normal !important; /* 重置行高，使用flex对齐 */
}

.search-area {
  flex: 1;
  cursor: pointer;
  display: flex;
  align-items: center;
  min-width: 0; /* 允许收缩 */
}

.search-trigger {
  display: flex;
  align-items: center;
  gap: 6px;
  width: 100%;
}

.field-name {
  overflow: hidden;
  font-weight: 900;
  color: white;
  white-space: nowrap;
  text-overflow: ellipsis;
}

.search-icon {
  font-size: 12px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.search-area:hover .search-icon {
  opacity: 1;
}

/* 🎯 状态筛选按钮样式 - 与排序按钮完全一致 */
.status-filter-btn {
  display: flex !important;
  position: relative;
  width: 28px !important;
  height: 28px !important;
  max-width: 28px !important;
  max-height: 28px !important;
  min-width: 28px !important;
  min-height: 28px !important;
  margin: 0;
  padding: 0 !important;
  border: 2px solid rgb(139 92 246 / 30%);
  border-radius: 50% !important;
  box-sizing: border-box !important;
  overflow: hidden;
  font-size: 10px !important;
  font-weight: 600;
  background: rgb(255 255 255 / 90%);
  transition: all 0.3s ease;
  cursor: pointer;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important;
}

.status-filter-btn:hover {
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 4px 16px rgb(139 92 246 / 20%);
  transform: translateY(-2px) scale(1.05);
  border-color: rgb(139 92 246 / 60%);
}

.status-filter-btn:active {
  box-shadow: 0 2px 8px rgb(139 92 246 / 15%);
  transform: translateY(-1px) scale(1.02);
}

.filter-icon {
  font-size: 8px;
  margin-right: 1px;
}

.filter-text {
  font-size: 6px;
  font-weight: 700;
  text-transform: uppercase;
  letter-spacing: 0.3px;
  line-height: 1;
}

/* 🎯 不同筛选状态的样式 */
.status-filter-btn.filter-all {
  border-color: rgb(108 117 125 / 40%);
  color: rgb(108 117 125);
}

.status-filter-btn.filter-all:hover {
  border-color: rgb(108 117 125 / 60%);
  color: rgb(108 117 125);
}

.status-filter-btn.filter-active {
  border-color: rgb(40 167 69 / 40%);
  color: rgb(40 167 69);
}

.status-filter-btn.filter-active:hover {
  border-color: rgb(40 167 69 / 60%);
  color: rgb(40 167 69);
}

.status-filter-btn.filter-inactive {
  border-color: rgb(220 53 69 / 40%);
  color: rgb(220 53 69);
}

.status-filter-btn.filter-inactive:hover {
  border-color: rgb(220 53 69 / 60%);
  color: rgb(220 53 69);
}



/* 🔄 状态表头专用样式 */
.status-header-container {
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: center;
  height: 100%;
  padding: 0 8px;
}

.status-header-container:hover .switch-icon {
  opacity: 1;
  transform: rotate(180deg);
}

.sort-area {
  display: flex;
  width: 24px;
  justify-content: center;
  align-items: center;
  margin-left: 8px;
}

/* 🎨 圆形排序按钮设计 - 强制正方形确保圆形 */
.sort-btn {
  display: flex !important;
  position: relative;
  width: 28px !important;
  height: 28px !important;
  max-width: 28px !important;
  max-height: 28px !important;
  min-width: 28px !important;
  min-height: 28px !important;
  border: 2px solid rgb(139 92 246 / 30%);
  border-radius: 50% !important; /* 完全圆形 */
  box-sizing: border-box !important;
  overflow: hidden;
  font-size: 12px !important;
  font-weight: bold;
  color: rgb(139 92 246);
  background: rgb(255 255 255 / 90%);
  box-shadow: 0 2px 8px rgb(139 92 246 / 10%);
  transition: all 0.3s ease;
  cursor: pointer;
  align-items: center !important;
  justify-content: center !important;
  flex-shrink: 0 !important; /* 防止收缩 */
}

.sort-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgb(139 92 246 / 10%), transparent);
  transition: left 0.5s ease;
}

.sort-btn:hover {
  color: rgb(139 92 246);
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 4px 16px rgb(139 92 246 / 20%);
  transform: translateY(-2px) scale(1.05);
  border-color: rgb(139 92 246 / 60%);
}

.sort-btn:hover::before {
  left: 100%;
}

.sort-btn:active {
  box-shadow: 0 2px 8px rgb(139 92 246 / 15%);
  transform: translateY(-1px) scale(1.02);
}

.search-input-container {
  display: flex;
  align-items: center;
  width: 100%;
  gap: 4px;
}

/* 🎯 修复搜索框宽度和形状问题 - 占据全部空间 */
.header-search-input {
  width: 100% !important; /* 占据整个搜索容器的宽度 */
  height: 36px !important; /* 固定高度，确保圆形 */
  max-width: none !important; /* 移除最大宽度限制 */
  min-width: 100px !important; /* 最小宽度确保可用性 */
  padding: 0 16px !important; /* 恢复正常内边距 */
  border-radius: 18px !important; /* 完全圆形：高度的一半 */
  box-sizing: border-box !important;
  font-size: 14px !important; /* 恢复正常字体大小 */
  line-height: 36px !important; /* 行高与高度一致 */
}

.header-cell {
  display: flex; /* 🎯 为子元素提供定位上下文 */
  position: relative;
  height: 60px !important; /* 🎯 增加高度到60px，改善视觉比例 */
  padding: 0 12px;
  overflow: hidden; /* 🎯 确保子元素不超出边界 */
  font-size: 1rem; /* 🎯 根据60px高度调整字体大小，保持良好比例 */
  font-weight: 900;
  line-height: 60px !important; /* 🎯 行高与容器高度一致：60px */
  color: white !important; /* 🎨 改为白色字体，提高可读性 */
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 1.2px; /* 🎯 根据字体大小增加调整字母间距 */
  text-shadow: 2px 2px 4px rgb(0 0 0 / 30%) !important; /* 🎨 深色阴影，适应白色文字 */

  &.sortable {
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: rgb(139 92 246 / 15%); /* 🎨 悬停时使用标准紫色，稍微加深 */
      transform: translateY(-1px);
    }
  }
}

/* 操作列中的小型新增按钮 - 修复超出父级边界问题 */
.header-add-btn-small {
  display: flex;
  position: relative; /* 改为相对定位，避免超出父级边界 */
  height: 28px;
  max-height: 28px; /* 限制最大高度 */
  padding: 0 8px;
  border: 1px solid rgb(255 255 255 / 30%);
  border-radius: 14px;
  font-size: 12px;
  font-weight: 600;
  color: white;
  background: rgb(255 255 255 / 20%);
  transition: all 0.3s ease;
  margin-left: auto; /* 自动右对齐 */
  cursor: pointer;
  align-items: center;
  gap: 4px;
  backdrop-filter: blur(5px);

  &:hover {
    background: rgb(255 255 255 / 30%);
    box-shadow: 0 2px 8px rgb(255 255 255 / 20%);
    transform: translateY(-50%) scale(1.05);
    border-color: rgb(255 255 255 / 50%);
  }

  .add-icon {
    font-size: 14px;
  }

  .add-text {
    font-size: 11px;
    letter-spacing: 0.5px;
  }
}

/* 服务信息表头特殊样式 */
.service-info-header {
  justify-content: space-between !important;
  padding: 0 8px !important;

  .header-text {
    padding: 4px 8px;
    border: none !important;
    border-radius: 4px;

    /* 🎯 确保背景透明，避免意外的背景色 */
    background: transparent !important;
    transition: all 0.3s ease;
    cursor: pointer;

    /* 🎯 禁用文本选择，避免干扰鼠标事件 */
    user-select: none;

    /* 🎯 禁用浏览器默认的outline和border，消除白色框 */
    outline: none !important;

    &:hover {
      border: none !important;
      background: rgb(255 255 255 / 20%) !important;
      transform: translateX(2px);
      outline: none !important;
    }

    &:focus,
    &:active {
      outline: none !important;
      border: none !important;
      background: transparent !important;
    }

    /* 🎯 彻底禁用文本选择高亮效果 */
    &::selection {
      color: inherit !important;
      background: transparent !important;
    }

    &::selection {
      color: inherit !important;
      background: transparent !important;
    }
  }

  .sort-btn {
    display: flex;
    width: 24px;
    height: 24px;
    border: 1px solid rgb(255 255 255 / 20%);
    border-radius: 50%;
    background: rgb(255 255 255 / 10%);
    transition: all 0.3s ease;
    align-items: center;
    justify-content: center;
    cursor: pointer;

    &:hover {
      background: rgb(255 255 255 / 20%);
      border-color: rgb(255 255 255 / 40%);
      transform: scale(1.1);
    }

    .sort-indicator {
      font-size: 12px;
      font-weight: bold;
      color: white;

      &.sort-asc,
      &.sort-desc {
        color: #ffd700;
        text-shadow: 0 0 4px rgb(255 215 0 / 60%);
      }
    }
  }
}

/* 🎨 圆形按钮中的排序指示器 */
.sort-indicator {
  font-size: 12px;
  font-weight: bold;
  line-height: 1;
  color: inherit; /* 继承按钮的颜色 */
  transition: all 0.3s ease;
}

.sort-btn .sort-indicator.sort-asc {
  color: #0f8;
  text-shadow: 0 0 4px rgb(0 255 136 / 60%);
  transform: scale(1.1);
}

.sort-btn .sort-indicator.sort-desc {
  color: #ff6b6b;
  text-shadow: 0 0 4px rgb(255 107 107 / 60%);
  transform: scale(1.1);
}

/* 圆形排序按钮悬浮时的指示器效果 */
.sort-btn:hover .sort-indicator {
  color: rgb(139 92 246);
  text-shadow: 0 0 4px rgb(139 92 246 / 60%);
}

.sort-btn:hover .sort-indicator.sort-asc {
  color: #0f8;
  text-shadow: 0 0 6px rgb(0 255 136 / 80%);
}

.sort-btn:hover .sort-indicator.sort-desc {
  color: #ff6b6b;
  text-shadow: 0 0 6px rgb(255 107 107 / 80%);
}

/* 🎯 新增：排序按钮加载状态样式 */
.sort-btn.loading {
  background: rgb(255 255 255 / 95%) !important;
  border-color: rgb(139 92 246 / 40%) !important;
  cursor: not-allowed !important;
  pointer-events: none !important;
  opacity: 0.8 !important;
}

.sort-btn.loading:hover {
  box-shadow: none !important;
  transform: none !important;
}

/* 🎯 新增：排序按钮加载动画 */
.sort-loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 2px;
}

.loading-dot {
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: rgb(139 92 246);
  animation: loadingPulse 1.4s ease-in-out infinite both;
}

.loading-dot:nth-child(1) {
  animation-delay: -0.32s;
}

.loading-dot:nth-child(2) {
  animation-delay: -0.16s;
}

.loading-dot:nth-child(3) {
  animation-delay: 0s;
}

@keyframes loadingPulse {
  0%, 80%, 100% {
    opacity: 0.5;
    transform: scale(0.8);
  }

  40% {
    opacity: 1;
    transform: scale(1.2);
  }
}

/* 🎯 新增：禁用状态的排序按钮样式 */
.sort-btn:disabled {
  background: rgb(255 255 255 / 60%) !important;
  border-color: rgb(139 92 246 / 20%) !important;
  cursor: not-allowed !important;
  opacity: 0.6 !important;
}

.sort-btn:disabled .sort-indicator {
  color: rgb(139 92 246 / 50%) !important;
  text-shadow: none !important;
}

/* 🔧 统一列宽系统 - 修复对齐问题 */
.header-cell, .data-cell {
  display: flex;
  align-items: center;
  padding: 0 12px;

  /* 统一flex属性 */
  flex-shrink: 0;
  flex-grow: 0;
  overflow: hidden;
}

.header-cell {
  font-size: 1rem; /* 🎯 与主要表头样式保持一致 */
  font-weight: 900;
  color: white;
  transform: rotate(1deg);
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 1.2px; /* 🎯 与主要表头样式保持一致 */
  text-shadow: 2px 2px 4px rgb(0 0 0 / 30%);
}

/* 服务管理页面动态列宽定义 - 优化空间分配，解决操作按钮遮挡问题 */
.header-cell:nth-child(1), .data-cell:nth-child(1) {
  flex-basis: 28%; /* 服务信息：减少空间 */
  min-width: clamp(160px, 18vw, 220px);
}

.header-cell:nth-child(2), .data-cell:nth-child(2) {
  flex-basis: 12%; /* 服务费：压缩空间 */
  min-width: clamp(70px, 8vw, 100px);
}

.header-cell:nth-child(3), .data-cell:nth-child(3) {
  flex-basis: 12%; /* 提成：压缩空间 */
  min-width: clamp(70px, 8vw, 100px);
}

.header-cell:nth-child(4), .data-cell:nth-child(4) {
  flex-basis: 11%; /* 时长：略微压缩 */
  min-width: clamp(55px, 7vw, 85px);
}

.header-cell:nth-child(5), .data-cell:nth-child(5) {
  flex-basis: 11%; /* 状态：略微压缩 */
  min-width: clamp(55px, 7vw, 85px);
}

.header-cell:nth-child(6), .data-cell:nth-child(6) {
  flex-basis: 26%; /* 操作列：大幅增加空间，解决按钮遮挡 */
  min-width: clamp(140px, 16vw, 200px);
}

/* 🔧 内容溢出处理 */
.cell-content {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行文本处理 */
.cell-content.multiline {
  display: -webkit-box;
  max-height: 2.8em;
  line-height: 1.4;
  white-space: normal;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 长单词处理 */
.cell-content.breakable {
  word-break: break-all;
  word-wrap: break-word;
}

/* 📱 多分辨率响应式设计 - 全面兼容各种屏幕尺寸 */

/* 4K超高清显示器 (3840x2160及以上) */
@media (width >= 3840px) {
  .picasso-services {
    font-size: 18px; /* 增大基础字体 */
  }

  .title-layer {
    font-size: 4rem; /* 放大标题 */
  }

  .service-image {
    width: 80px;
    height: 40px;
  }

  .action-btn {
    padding: 12px 20px;
    font-size: 1rem;
  }

  .table-body {
    min-height: 400px; /* 增加表格高度 */
  }
}

/* 2K显示器 (2560x1440) */
@media (width >= 2560px) and (width <= 3839px) {
  .picasso-services {
    font-size: 16px;
  }

  .title-layer {
    font-size: 3.5rem;
  }

  .service-image {
    width: 70px;
    height: 35px;
  }

  .action-btn {
    padding: 10px 18px;
    font-size: 0.95rem;
  }
}

/* 1080P显示器 (1920x1080) */
@media (width >= 1920px) and (width <= 2559px) {
  .picasso-services {
    font-size: 15px;
  }

  .title-layer {
    font-size: 3rem;
  }

  .service-image {
    width: 65px;
    height: 32px;
  }

  .action-btn {
    padding: 9px 16px;
    font-size: 0.9rem;
  }
}

/* 标准桌面显示器 (1366x768 - 1919x1080) */
@media (width >= 1366px) and (width <= 1919px) {
  .picasso-services {
    font-size: 14px;
  }

  .title-layer {
    font-size: 2.5rem;
  }

  .service-image {
    width: 60px;
    height: 30px;
  }

  .action-btn {
    padding: 8px 14px;
    font-size: 0.85rem;
  }

  /* 1366x768特殊优化 - 绿色框底部与红色框底部对齐 */
  .table-body,
  .table-body.hide-scrollbar,
  .table-body.scroll-snap {
    /* 🎯 绿色框高度对齐到红色框底部 */
    height: 572px !important; /* 红色框底部684px - 绿色框顶部112px = 572px */
    max-height: 572px !important;
    min-height: 572px !important;
  }
}

/* 小屏幕桌面/大平板 (1024x768 - 1365x768) */
@media (width >= 1024px) and (width <= 1365px) {
  .picasso-services {
    padding: 15px;
    font-size: 13px;
  }

  .title-layer {
    font-size: 2.2rem;
  }

  .service-image {
    width: 55px;
    height: 20px; /* 🎯 减少高度，确保适应50px紫色框边界 */
  }

  .action-btn {
    padding: 7px 12px;
    font-size: 0.8rem;
  }

  .action-fragments {
    min-width: 120px;
    gap: 4px;
  }

  .table-body {
    max-height: calc(100vh - 300px);
  }
}

/* 平板适配 (768px - 1023px) */
@media (width >= 768px) and (width <= 1023px) {
  .table-container {
    overflow-x: auto;

    /* 移除底部内边距，让table-body占满容器 */
    padding-bottom: 0;
    margin-bottom: 0;
  }

  .table-header, .data-row {
    min-width: 750px; /* 增加最小宽度以适应新的列宽分配 */
  }

  .header-cell, .data-cell {
    padding: 0 6px;
    font-size: 0.85rem;
  }

  .service-image {
    width: 50px;
    height: 25px;
  }

  .action-btn {
    min-width: 55px; /* 确保按钮有足够宽度 */
    padding: 6px 8px;
    font-size: 0.75rem;
  }
}

/* 小平板适配 (481px - 767px) */
@media (width >= 481px) and (width <= 767px) and (orientation: portrait) {
  .table-container {
    overflow-x: auto;

    /* 移除底部内边距，让table-body占满容器 */
    padding-bottom: 0;
    margin-bottom: 0;
  }

  .table-header, .data-row {
    min-width: 650px; /* 增加最小宽度 */
  }

  .header-cell, .data-cell {
    padding: 0 6px;
    font-size: 0.8rem;
  }

  .service-image {
    width: 45px;
    height: 22px;
  }

  .action-btn {
    min-width: 45px; /* 确保按钮有足够宽度 */
    padding: 4px 6px;
    font-size: 0.7rem;
  }

  .btn-text {
    display: none; /* 隐藏按钮文字，只显示图标 */
  }
}

/* 手机横屏适配 (481px - 767px, 横屏) */
@media (width >= 481px) and (width <= 767px) and (orientation: landscape) {
  .picasso-services {
    padding: 10px;
  }

  .title-layer {
    font-size: 1.8rem;
  }

  .table-body {
    max-height: calc(100vh - 200px); /* 横屏时减少垂直空间占用 */
  }
}

/* 手机竖屏适配 (最大480px) */
@media (width <= 480px) {
  .table-header {
    display: none;
  }

  .data-row {
    min-width: auto;
    padding: 12px;
    border-radius: 8px;
    background: rgb(255 255 255 / 10%);
    flex-direction: column;
    margin-bottom: 12px;
  }

  .data-cell {
    min-width: auto;
    padding: 4px 0;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid rgb(255 255 255 / 10%);
    flex-basis: auto;
  }

  .data-cell:last-child {
    border-bottom: none;
  }

  .data-cell::before {
    content: attr(data-label) ": ";
    font-weight: bold;
    color: var(--van-gogh-text-primary);
  }

  .service-image {
    width: 40px;
    height: 20px;
  }

  .action-btn {
    min-width: 35px;
    padding: 4px 6px;
    font-size: 0.65rem;
  }

  .btn-text {
    display: none;
  }
}

.table-body {
  /* 🎯 使用flex-1占满剩余空间，为翻页组件预留底部空间 */
  height: auto !important; /* 自动高度，由flex容器控制 */
  max-height: calc(100vh - 200px) !important; /* 为翻页组件预留空间 */
  min-height: 300px !important; /* 确保最小可用高度 */

  /* 🎯 确保绿色框底部精确对齐到红色框底部位置 */

  /* 🎯 移除最大高度限制，让表格主体自适应内容高度 */

  /* max-height: calc(100vh - 280px); 注释掉，避免强制拉伸 */

  /* 🎯 正确调整绿色区域：让绿色区域下边界与蓝色区域下边界对齐 */
  margin: 0; /* 移除所有外边距 */
  padding: 0 !important; /* 🎯 移除底部内边距，消除绿色框内空白区域 */

  /* 确保最后一行不被遮挡 */
  box-sizing: border-box;
  flex: 1; /* 占满剩余空间 */
  overflow: hidden auto;
  margin-top: 20px !important; /* 🎯 添加20px上边距，使数据行与菜单项对齐 */
  margin-bottom: 28px !important; /* 🎯 调整为28px，适应翻页组件高度从50px→30px的变化 */

  /* 确保占满flex容器的剩余高度 */
  align-self: stretch;

  /* 🎨 梵高风格滚动条 - 只在内容超过5行时显示 */
  scrollbar-width: thin;
  scrollbar-color: #9b59b6 rgb(155 89 182 / 20%);
}

.table-body::-webkit-scrollbar {
  width: 8px;
}

.table-body::-webkit-scrollbar-track {
  border-radius: 8px;
  background: linear-gradient(45deg, rgb(142 68 173 / 10%), rgb(155 89 182 / 10%));
}

.table-body::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: linear-gradient(45deg, #8e44ad, #9b59b6);
  transition: all 0.3s ease;
}

.table-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #639, #8e44ad);
}

/* 🎨 横向滚动条样式优化 - 避免遮挡翻页组件 */
.table-container::-webkit-scrollbar {
  height: 8px; /* 横向滚动条高度 */
}

.table-container::-webkit-scrollbar-track {
  margin: 0 5px; /* 两端留白 */
  border-radius: 4px;
  background: rgb(155 89 182 / 10%);
}

.table-container::-webkit-scrollbar-thumb {
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 4px;
  background: linear-gradient(90deg, #9b59b6, #8e44ad);
}

.table-container::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(90deg, #8e44ad, #7d3c98);
  box-shadow: 0 2px 4px rgb(142 68 173 / 30%);
}

/* 🎨 当记录数≤10时完全禁用滚动 */
.table-body.hide-scrollbar {
  overflow: hidden !important; /* 完全禁用滚动 */
  scrollbar-width: none; /* Firefox */
  -ms-overflow-style: none; /* IE and Edge */
}

.table-body.hide-scrollbar::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* 🎯 滚动吸附效果 - 当显示20条或更多时启用 */
.table-body.scroll-snap {
  scroll-snap-type: y mandatory;
  scroll-behavior: smooth;
  overflow-y: auto !important; /* 确保可以滚动 */
}

.scroll-snap-item {
  scroll-snap-align: start;
  scroll-snap-stop: normal;
}

/* 🎯 绿框动态高度优化 */
.data-cubism {
  overflow: hidden; /* 确保内容不超出绿框 */
  transition: height 0.3s ease; /* 高度变化时的平滑过渡 */
}

/* 🎯 服务行 - 毛玻璃效果，与菜单项对齐 */
.data-row {
  display: flex;
  height: 50px !important; /* 🎯 固定高度50px，与左侧菜单项高度完全一致 */
  min-height: 50px !important; /* 🎯 调整为50px，与菜单项高度完全一致 */
  border: 1px solid rgb(255 255 255 / 8%) !important;
  border-radius: 12px !important;

  /* 🎯 字体大小与菜单一致，行高匹配新高度 */
  font-size: 16px !important; /* 与菜单项字体大小一致 */
  font-weight: 500 !important; /* 稍微轻一点的字重 */
  line-height: 50px !important; /* 🎯 与菜单项行高50px完全一致，确保文字垂直居中 */

  /* 🎯 毛玻璃效果 - 极高透明度 */
  background: rgb(255 255 255 / 1%) !important; /* 极高透明度 */
  box-shadow: 0 4px 16px rgb(139 92 246 / 5%) !important;

  /* 🚨 移除任何变形效果 */
  transform: none !important;
  transition: all 0.3s ease;
  align-items: center !important; /* 🎯 与菜单项对齐方式一致 */
  margin-bottom: 8px !important; /* 🎯 调整为8px间距，使总间距与菜单项58px一致 */
  backdrop-filter: blur(15px) !important;
}

/* 🎯 第7行正常处理 - 保持与菜单一致的间距 */
.data-row:nth-child(7) {
  margin-bottom: 8px !important; /* 🎯 调整为8px间距，与菜单项间距一致 */
}

/* 🎯 最后一行特殊处理 - 移除下边距，确保完全吸附 */
.data-row:last-child {
  margin-bottom: 0 !important; /* 🎯 最后一行下边距为0，确保能完全吸附到表格主体底部 */
}

/* 🎯 服务行悬停效果 - 毛玻璃增强 */
.data-row:hover {
  background: rgb(255 255 255 / 8%) !important; /* 悬停时稍微不透明 */
  box-shadow: 0 8px 24px rgb(139 92 246 / 10%) !important;
  transform: translateY(-2px) !important; /* 轻微上浮 */
  backdrop-filter: blur(20px) !important;
}

.data-cell {
  display: flex;
  max-height: 50px; /* 🎯 强制限制最大高度为紫色框高度 */
  padding: 0 8px; /* 🎯 只设置左右内边距，避免超出紫色框边界 */
  overflow: hidden; /* 🎯 隐藏超出内容 */
  font-size: 0.9rem; /* 🎯 减少字体大小，进一步压缩高度 */
  line-height: 1.2; /* 🎯 减少行高，确保适应50px紫色框 */
  align-items: center;
  justify-content: center;
}

.cell-content {
  display: flex;
  width: 100%;
  font-size: 0.9rem;
  font-weight: 600;
  text-align: center;
  color: #2c3e50;
  align-items: center;
  justify-content: center;
}

/* 毕加索风格服务信息 */
.service-fragment {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 服务图片样式 - 适配1024x512横向比例 */
.service-image {
  width: 60px;  /* 增加宽度适应2:1横向比例 */
  height: 30px; /* 减少高度，保持2:1比例 */
  border-radius: 8px; /* 改为圆角矩形，更适合横向图片 */
  overflow: hidden;
  background: linear-gradient(45deg, #8e44ad, #9b59b6); /* 作为加载时的背景 */
  box-shadow: 0 4px 12px rgb(142 68 173 / 40%);
  transform: rotate(6deg); /* 减少旋转角度，更适合横向显示 */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 添加平滑过渡效果 */
  flex-shrink: 0;
  animation: serviceFloat 4s ease-in-out infinite;
}

/* 悬停时图片放大效果已删除 - 影响操作 */

.service-image img {
  display: block;
  width: 100%;
  height: 100%;
  transform: rotate(-6deg); /* 抵消容器的旋转，让图片保持正常 */
  transition: transform 0.3s ease;
  object-fit: cover; /* 保持图片比例，裁剪多余部分 */
}

.service-image img:hover {
  transform: rotate(-6deg) scale(1.05);
}

@keyframes serviceFloat {
  0%, 100% { transform: rotate(6deg) translateY(0); }
  50% { transform: rotate(6deg) translateY(-3px); }
}

/* 🔍 表头搜索模式样式 - 为排序按钮预留空间 */
.header-search-container {
  display: flex;
  align-items: center;
  width: calc(100% - 36px) !important; /* 为排序按钮预留36px空间 */
  max-width: calc(100% - 36px) !important; /* 确保不超出边界 */
  gap: 0; /* 无间距 */
  overflow: visible; /* 确保内容可见 */
  transition: all 0.3s ease; /* 添加过渡动画 */
}

.header-search-input {
  width: 100% !important; /* 占据搜索容器的全部宽度 */
  height: 36px;
  padding: 0 12px;
  border: 2px solid rgb(139 92 246 / 60%);
  border-radius: 18px;
  box-sizing: border-box !important; /* 确保边框包含在宽度内 */
  font-size: 14px;
  font-weight: 500;
  color: #333;
  background: rgb(255 255 255 / 95%);
  transition: all 0.3s ease;
  flex: 1;

  &:focus {
    background: rgb(255 255 255 / 100%);
    box-shadow: 0 0 0 3px rgb(139 92 246 / 20%);
    outline: none;
    border-color: rgb(139 92 246 / 80%);
  }

  &::placeholder {
    font-weight: 400;
    color: rgb(139 92 246 / 60%);
  }
}

.search-close-btn {
  display: flex;
  width: 28px;
  height: 28px;
  border: none;
  border-radius: 50%;
  font-size: 16px;
  font-weight: bold;
  color: #ef4444 !important; /* 🎨 改为红色，提高可见性 */
  background: rgb(239 68 68 / 10%) !important; /* 🎨 红色半透明背景 */
  transition: all 0.3s ease;
  cursor: pointer;
  align-items: center;
  justify-content: center;

  &:hover {
    color: #dc2626 !important; /* 🎨 悬停时更深的红色 */
    background: rgb(239 68 68 / 20%) !important; /* 🎨 悬停时加深背景 */
    transform: scale(1.1);
  }
}

/* 🎯 过渡状态样式 - 防止连续点击误触 */
.header-cell.transitioning .sort-btn,
.header-cell.transitioning .search-close-btn {
  opacity: 0.6 !important;
  pointer-events: none !important;
  transform: scale(0.95) !important;
  transition: all 0.3s ease !important;
}

.header-cell.transitioning .header-normal-container {
  pointer-events: none !important;
  opacity: 0.8 !important;
  transition: all 0.3s ease !important;
}

/* 🎯 普通模式容器 - 点击触发搜索 */
.header-normal-container {
  display: flex;
  position: relative;
  width: 100%;
  height: 100%;
  padding: 8px 12px;
  border: none !important;
  border-radius: 4px; /* 添加圆角 */

  /* 🎯 确保背景透明，避免意外的背景色 */
  background: transparent !important;
  transition: all 0.2s ease;
  justify-content: flex-start; /* 左对齐，让文字在左侧 */
  align-items: center;
  cursor: pointer; /* 改为指针，表示可点击 */

  /* 🎯 禁用文本选择，避免干扰鼠标事件 */
  user-select: none;

  /* 🎯 禁用浏览器默认的outline和border，消除白色框 */
  outline: none !important;

  &:hover {
    outline: none !important;
    border: none !important;
    background: transparent !important;
  }

  &:focus,
  &:active {
    outline: none !important;
    border: none !important;
    background: transparent !important;
  }

  /* 🎯 彻底禁用文本选择高亮效果 */
  &::selection {
    color: inherit !important;
    background: transparent !important;
  }

  &::selection {
    color: inherit !important;
    background: transparent !important;
  }

  /* 🎯 确保所有子元素也没有选择高亮 */
  * {
    user-select: none !important;
    outline: none !important;
    border: none !important;

    &::selection {
      color: inherit !important;
      background: transparent !important;
    }

    &::selection {
      color: inherit !important;
      background: transparent !important;
    }
  }
}

/* 🎯 点击触发的悬停效果 */
.header-normal-container:hover {
  background: rgb(139 92 246 / 10%);
  transform: translateX(-1px);
}

.header-normal-container:active {
  background: rgb(139 92 246 / 20%);
  transform: translateX(0);
}

.service-info {
  flex: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* 添加平滑过渡效果 */
}

/* 没有图片时的服务信息样式调整 */
.service-info.no-image {
  margin-left: 0; /* 移除左边距，因为没有图片 */
}

/* 悬停时隐藏文字效果已删除 - 影响操作 */

/* 服务行悬停效果 */
.service-row {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.service-row:hover {
  background: rgb(244 114 182 / 5%); /* 轻微的背景色变化 */
  box-shadow: 0 4px 12px rgb(244 114 182 / 15%); /* 添加阴影 */
  transform: translateY(-2px); /* 轻微上移 */
}

/* 🤖 自动生成状态提示样式 */
.auto-generate-status {
  display: flex;
  padding: 8px 12px;
  border: 1px solid rgb(24 144 255 / 20%);
  border-radius: 6px;
  font-size: 12px;
  color: #1890ff;
  background: rgb(24 144 255 / 10%);
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  animation: fadeInUp 0.3s ease-out;
}

.loading-spinner-small {
  width: 14px;
  height: 14px;
  border: 2px solid rgb(24 144 255 / 30%);
  border-top: 2px solid #1890ff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.status-text {
  font-weight: 500;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.service-name {
  font-size: 1rem;
  font-weight: bold;
  color: #2c3e50;
  transform: skew(-2deg);
  margin-bottom: 3px;
}

/* 💰 价格单元格样式 */
.price-cell, .commission-cell {
  display: flex;
  width: 100%;
  min-height: 50px;                        /* 🎯 与菜单高度一致：50px */
  padding: 8px 12px;
  border: 1px solid rgb(139 92 246 / 10%);
  border-radius: 6px;
  background: linear-gradient(135deg, rgb(139 92 246 / 5%) 0%, rgb(244 114 182 / 5%) 100%);
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
  gap: 8px;
  cursor: pointer;
}

.price-cell:hover, .commission-cell:hover {
  background: linear-gradient(135deg, rgb(139 92 246 / 10%) 0%, rgb(244 114 182 / 10%) 100%);
  box-shadow: 0 4px 12px rgb(139 92 246 / 15%);
  transform: translateY(-1px);
  border-color: rgb(139 92 246 / 30%);
}

.price-value, .commission-value {
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  color: var(--van-gogh-text-primary);
}

/* 旧的价格立方体样式已删除，使用新的单元格样式 */

/* 毕加索风格时长显示 */
.duration-fragment {
  display: flex;
  padding: 8px 12px;
  border: 2px solid rgb(52 152 219 / 30%);
  border-radius: 12px 0;
  background: linear-gradient(135deg, rgb(52 152 219 / 20%), rgb(255 255 255 / 80%));
  transform: skew(-1deg);
  align-items: center;
  gap: 6px;
}

.duration-icon {
  font-size: 1rem;
  color: #3498db;
}

.duration-text {
  font-size: 0.9rem;
  font-weight: bold;
  color: #3498db;
}

/* 🎨 时长单元格样式 - 支持点击编辑 */
.duration-cell {
  padding: 4px;
  border-radius: 8px;
  transition: all 0.3s ease;
  cursor: pointer;
}

.duration-cell:hover {
  background: rgb(255 193 7 / 5%);
  box-shadow: 0 4px 8px rgb(255 193 7 / 20%);
  transform: translateY(-1px);
}

.duration-cell.clickable:hover .duration-fragment {
  border-color: rgb(255 193 7 / 50%);
  background: linear-gradient(135deg, rgb(255 193 7 / 15%) 0%, rgb(255 152 0 / 15%) 100%);
  box-shadow: 0 4px 8px rgb(255 193 7 / 20%);
}

/* 🎨 梵高风格时长显示 */
.duration-fragment {
  display: flex;
  padding: 8px 12px;
  border: 2px solid var(--van-gogh-border-medium);
  border-radius: 8px;
  background: linear-gradient(135deg, var(--van-gogh-primary), var(--van-gogh-primary-dark));
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.duration-icon {
  font-size: 1rem;
  animation: tickTock 2s ease-in-out infinite;
}

@keyframes tickTock {
  0%, 100% { transform: rotate(-5deg); }
  50% { transform: rotate(5deg); }
}

.duration-text {
  font-size: 0.85rem;
  font-weight: 700;
  color: white;
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

/* 🎨 梵高风格状态显示 */
.status-fragment {
  display: flex;
  padding: 8px 12px;
  border: 2px solid;
  border-radius: 8px;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.status-active {
  background: linear-gradient(135deg, #10b981, #059669);
  border-color: #10b981;
}

.status-inactive {
  background: linear-gradient(135deg, #ef4444, #dc2626);
  border-color: #ef4444;
}

.status-text {
  font-size: 0.85rem;
  font-weight: 700;
  color: white;
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

/* 🎨 可点击状态样式 - 开关按钮效果 */
.status-fragment.clickable {
  position: relative;
  min-width: 80px; /* 确保开关有足够宽度 */
  border-radius: 20px; /* 更圆润的开关外观 */
  transition: all 0.3s ease;
  cursor: pointer;
}

.status-fragment.clickable:hover {
  box-shadow: 0 4px 12px rgb(0 0 0 / 20%);
  transform: scale(1.05);
  filter: brightness(1.1); /* 悬浮时稍微变亮 */
}

.status-fragment.clickable:active {
  transform: scale(0.95);
  filter: brightness(0.9); /* 点击时稍微变暗 */
}

/* 🎨 点击提示效果 - 修复负值定位问题 */
.status-fragment.clickable::after {
  position: absolute;
  top: 0;
  left: 50%;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.7rem;
  color: white;
  background: rgb(0 0 0 / 80%);
  opacity: 0;
  transform: translateX(-50%) translateY(-100%) translateY(-5px);
  transition: opacity 0.3s ease;
  content: '点击切换';
  pointer-events: none;
  white-space: nowrap;
}

.status-fragment.clickable:hover::after {
  opacity: 1;
}



.status-indicator {
  position: relative;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  transition: all 0.3s ease; /* 添加过渡动画 */
  animation: statusPulse 2s ease-in-out infinite;
}

.status-active .status-indicator {
  background: #fff;
  box-shadow: 0 0 8px rgb(255 255 255 / 90%), 0 0 4px rgb(76 175 80 / 50%);
  transform: translateX(4px); /* 上架状态时向右移动 */
}

.status-inactive .status-indicator {
  background: #fff;
  box-shadow: 0 0 8px rgb(255 255 255 / 90%), 0 0 4px rgb(244 67 54 / 50%);
  transform: translateX(-4px); /* 下架状态时向左移动 */
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

.status-text {
  font-size: 0.8rem;
  font-weight: bold;
}

/* 状态文字颜色统一为白色，在.status-text中已设置 */

/* 毕加索风格操作按钮 - 优化空间分配 */
.action-fragments {
  display: flex;
  width: 100%; /* 占满容器宽度 */
  min-width: 160px; /* 增加最小宽度 */
  padding: 6px 8px; /* 增加内边距 */
  gap: 8px; /* 增加按钮间距 */
  flex-wrap: nowrap; /* 防止换行 */
  justify-content: flex-start; /* 左对齐，避免居中导致的空间浪费 */
  align-items: center;
}

.action-btn {
  position: relative;
  max-width: 85px; /* 限制最大宽度，避免过大 */
  min-width: 65px; /* 增加最小宽度，确保按钮不会太小 */
  padding: 8px 12px; /* 固定合适的内边距 */
  border-radius: var(--dynamic-border-radius);
  overflow: hidden;
  font-size: 0.8rem; /* 适中的字体大小 */
  font-weight: 600;
  text-align: center;
  color: white;
  box-shadow: 0 3px 8px rgb(0 0 0 / 15%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  white-space: nowrap;
  flex: 1; /* 让按钮平分可用空间 */
}

.action-btn:hover {
  box-shadow: 0 6px 16px rgb(0 0 0 / 25%);
  transform: translateY(-2px) scale(1.05);
}

.action-btn:active {
  box-shadow: 0 2px 4px rgb(0 0 0 / 20%);
  transform: translateY(0) scale(0.98);
}

.action-btn.edit {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-left: 3px solid rgb(255 255 255 / 30%);
}

.action-btn.edit:hover {
  background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
  transform: translateY(-2px) scale(1.05);
}

.action-btn.delete {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
  border-left: 3px solid rgb(255 255 255 / 30%);
}

.action-btn.delete:hover {
  background: linear-gradient(135deg, #ff5252 0%, #d32f2f 100%);
  transform: translateY(-2px) scale(1.05);
}

/* 按钮组整体效果 */
.action-fragments:hover .action-btn:not(:hover) {
  opacity: 0.7;
  transform: scale(0.95);
}

/* 按钮加载状态 */
.action-btn.loading {
  pointer-events: none;
  opacity: 0.6;
}

.action-btn.loading::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 16px;
  height: 16px;
  margin: -8px 0 0 -8px;
  border: 2px solid rgb(255 255 255 / 30%);
  border-top: 2px solid white;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* 按钮内部元素样式 */
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
}

.btn-icon {
  font-size: 14px;
  line-height: 1;
  filter: drop-shadow(0 1px 2px rgb(0 0 0 / 30%));
}

.btn-text {
  font-size: 0.85rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

/* 响应式：小屏幕时只显示图标 */
@media (width <= 768px) {
  .btn-text {
    display: none;
  }

  .action-btn {
    min-width: 40px;
    padding: 8px;
  }

  .btn-icon {
    font-size: 16px;
  }
}

/* 按钮波纹效果 */
.action-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgb(255 255 255 / 30%);
  transform: translate(-50%, -50%);
  transition: width 0.3s ease, height 0.3s ease;
}

.action-btn:active::before {
  width: 100%;
  height: 100%;
}

/* 按钮组容器增强 */
.action-fragments {
  padding: 6px;
  border: 1px solid rgb(255 255 255 / 10%);
  border-radius: 8px;
  background: rgb(255 255 255 / 5%);
  backdrop-filter: blur(10px);
}

/* 按钮间分隔线 - 修复边界溢出问题 */
.action-btn.edit::after {
  position: absolute;
  top: 20%;
  right: 0; /* 修复: 不超出父元素边界 */
  bottom: 20%;
  width: 1px;
  background: rgb(255 255 255 / 20%);
  content: '';
}

.action-btn.view {
  background: linear-gradient(45deg, #3498db, #2980b9);
}

.action-btn.toggle {
  background: linear-gradient(45deg, #f39c12, #e67e22);
}

/* 🎯 毛玻璃风格分页组件 - 固定在底部，避免与表格重叠 */
.pagination-container {
  /* 🎯 布局 */
  display: flex !important;

  /* 🎯 位置调整 - 使用PostCSS响应式定位 */
  position: fixed !important; /* 固定定位 */
  right: 50px !important; /* PostCSS会转换为rem */
  bottom: 20px !important; /* 增加底部间距，确保不与内容重叠 */
  left: 230px !important; /* PostCSS会转换为rem */
  z-index: var(--service-z-toolbar); /* 确保在最上层 */

  /* 🎯 高度调整 - 保持1512×768基准不变 */
  height: 30px !important; /* 30px，PostCSS转换后在1024×600下会有0.30%的height差异，属于可接受范围 */
  margin: 0 !important; /* 移除所有外边距 */
  padding: 4px 12px !important; /* PostCSS会转换为rem */
  border: 1px solid rgb(139 92 246 / 15%) !important; /* PostCSS会转换为rem */
  border-radius: 12px !important; /* PostCSS会转换为rem */

  /* 🎯 毛玻璃效果 - 紫色主题 */
  background: rgb(139 92 246 / 3%) !important; /* 紫色透明背景 */
  box-shadow: 0 4px 16px rgb(139 92 246 / 8%) !important; /* 紫色阴影 */
  backdrop-filter: blur(20px) !important;
  justify-content: space-between !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  gap: 15px !important; /* PostCSS会转换为rem */
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  font-size: 11px; /* PostCSS会转换为rem */
  font-weight: 600;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%); /* PostCSS会转换为rem */
}

.highlight-number {
  font-size: 12px; /* PostCSS会转换为rem */
  font-weight: 700;
  color: var(--van-gogh-accent);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 40%); /* PostCSS会转换为rem */
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 20px; /* PostCSS会转换为rem */
  flex-wrap: wrap;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-label {
  font-size: 10px; /* 减小字体以适应新高度 */
  font-weight: 600;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.page-size-select {
  padding: 3px 8px; /* 减小内边距以适应新高度 */
  border: 1px solid var(--van-gogh-accent); /* 减小边框宽度 */
  border-radius: 4px;
  font-size: 10px; /* 减小字体以适应新高度 */
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-primary-dark), var(--van-gogh-secondary));
  box-shadow: 0 2px 4px rgb(139 92 246 / 30%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.page-size-select:hover {
  border-color: var(--van-gogh-primary);
  box-shadow: 0 4px 8px rgb(139 92 246 / 40%);
}

.page-size-select option {
  padding: 8px;
  color: white;
  background: var(--van-gogh-primary-dark);
}

.page-navigation {
  display: flex;
  padding: 1px 0; /* 🎯 减小内边距以适应新高度 */
  overflow: hidden; /* 🎯 确保按钮不超出边界 */
  align-items: center;
  gap: 4px; /* 减小按钮间距 */
}

/* 🎯 分页按钮 - 毛玻璃效果，降低高度 */
.page-btn {
  display: flex !important;

  /* 🎯 尺寸调整 - 使用px值，PostCSS会自动转换为rem */
  height: 22px !important; /* PostCSS会转换为rem */
  min-width: 22px !important; /* PostCSS会转换为rem */
  padding: 0 6px !important; /* PostCSS会转换为rem */
  border: 1px solid rgb(139 92 246 / 20%) !important; /* PostCSS会转换为rem */
  border-radius: 4px !important; /* PostCSS会转换为rem */
  font-size: 11px !important; /* PostCSS会转换为rem */
  font-weight: 500 !important;
  color: rgb(139 92 246 / 90%) !important; /* 紫色文字 */

  /* 🎯 毛玻璃效果 */
  background: rgb(139 92 246 / 6%) !important; /* 紫色透明背景 */
  transition: all 0.2s ease !important;
  backdrop-filter: blur(8px) !important;
  cursor: pointer !important;
  align-items: center !important;
  justify-content: center !important;
}

/* 🎯 分页按钮悬停效果 */
.page-btn:hover:not(:disabled) {
  color: rgb(139 92 246 / 100%) !important;
  background: rgb(139 92 246 / 12%) !important;
  box-shadow: 0 2px 8px rgb(139 92 246 / 20%) !important;
  transform: translateY(-1px) !important;
  backdrop-filter: blur(12px) !important;
}

/* 🎯 禁用状态按钮 */
.page-btn:disabled {
  color: rgb(139 92 246 / 40%) !important;
  background: rgb(139 92 246 / 3%) !important;
  opacity: 0.6 !important;
  transform: none !important;
  border-color: rgb(139 92 246 / 10%) !important;
  cursor: not-allowed !important;
}

/* 🎯 当前页按钮 - 突出显示 */
.page-btn.active {
  color: white !important;
  background: linear-gradient(135deg,
    rgb(139 92 246 / 25%) 0%,
    rgb(244 114 182 / 20%) 100%) !important; /* 渐变背景 */

  box-shadow: 0 2px 8px rgb(139 92 246 / 30%) !important;
  border-color: rgb(139 92 246 / 40%) !important;
  text-shadow: 0 1px 2px rgb(0 0 0 / 30%) !important;
  backdrop-filter: blur(12px) !important;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  min-width: 36px;
  text-align: center;
}

/* 旧的分页CSS已删除，使用统一的毕加索风格 */

/* 旧的分页CSS已全部删除 */

/* 🎨 梵高风格模态框 - 符合边界安全规范 */
.modal-overlay {
  display: flex;
  position: fixed;
  top: 20px; /* 🆕 顶部安全边距 ≥20px */
  left: 200px; /* 🆕 侧边栏宽度180px + 安全边距20px */
  z-index: var(--z-modal);
  width: calc(100% - 220px); /* 🆕 减去侧边栏宽度180px + 左右安全边距40px */
  height: calc(100% - 60px); /* 🆕 增加底部安全边距，减去上下安全边距60px */
  background: transparent; /* 完全透明，不遮挡后面内容 */
  align-items: center;
  justify-content: center;
  pointer-events: none; /* 让背景区域不拦截鼠标事件 */
}

.service-form-modal {
  display: flex;
  width: min(90%, calc(100vw - 260px)); /* 减去侧边栏180px + 安全边距80px */

  /* 🆕 符合边界安全规范的尺寸设置 */
  max-width: min(900px, calc(100vw - 260px)); /* 减去侧边栏180px + 安全边距80px */
  max-height: calc(100vh - 100px); /* 🆕 增加底部安全边距，减去上下安全边距100px */

  /* 🆕 确保按钮区域有足够边距 */
  min-height: 400px; /* 确保有足够空间显示内容 */
  border: none;
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(145deg,
    rgb(255 255 255 / 8%) 0%,
    rgb(248 250 252 / 12%) 50%,
    rgb(255 255 255 / 8%) 100%
  );
  box-shadow:
    0 25px 50px rgb(0 0 0 / 20%),
    0 0 0 1px rgb(255 255 255 / 20%),
    inset 0 1px 0 rgb(255 255 255 / 30%);
  backdrop-filter: blur(40px) saturate(1.8) brightness(1.2);
  transition: all 0.3s ease;
  flex-direction: column;
  pointer-events: auto; /* 确保模态框本身可以接收鼠标事件 */
}

/* 时长编辑模态框 - 根据内容调整尺寸，符合开发规范 */
.duration-edit-modal {
  width: 420px !important;
  max-width: 420px !important; /* 根据内容实际需求调整宽度 */
  max-height: 280px !important; /* 确保单个窗口显示全部内容，无滚动条 */
  min-height: 280px !important;
}

/* 价格编辑模态框 - 根据内容调整尺寸 */
.price-edit-modal {
  width: 500px !important;
  max-width: 500px !important;
  max-height: none !important; /* 移除高度限制，让内容自适应 */
  min-height: auto !important;
}

/* 价格历史模态框 - 根据内容调整尺寸 */
.price-history-modal {
  width: 700px !important;
  max-width: 700px !important;
  max-height: 600px !important;
}

.service-form-modal:hover {
  box-shadow:
    0 25px 50px rgb(139 92 246 / 40%),
    0 15px 30px rgb(244 114 182 / 30%);
  transform: scale(1.01);
}

@keyframes modalFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.form-header {
  display: flex;
  padding: 18px 25px;
  border-radius: 16px 16px 0 0;
  background: linear-gradient(135deg,
    rgb(139 92 246 / 15%) 0%,
    rgb(168 85 247 / 12%) 50%,
    rgb(99 102 241 / 15%) 100%
  );
  border-bottom: 1px solid rgb(255 255 255 / 15%);
  backdrop-filter: blur(20px) saturate(1.5);
  justify-content: space-between;
  align-items: center;
}

.form-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 700;
  color: white;
  text-shadow:
    1px 1px 2px rgb(0 0 0 / 50%),
    0 0 6px rgb(244 114 182 / 40%);
}

.close-btn {
  display: flex;
  width: 50px;                             /* 🎯 与菜单高度一致：50px */
  height: 50px;                            /* 🎯 与菜单高度一致：50px */
  border: 2px solid var(--van-gogh-accent);
  border-radius: 50%;
  font-size: 1.3rem;
  font-weight: 900;
  color: white;
  background: linear-gradient(45deg, var(--van-gogh-accent), var(--van-gogh-accent-dark));
  box-shadow: 0 4px 8px rgb(244 114 182 / 30%);
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-btn:hover {
  background: linear-gradient(45deg, var(--van-gogh-accent-dark), #db2777);
  box-shadow: 0 6px 12px rgb(244 114 182 / 60%);
  transform: rotate(90deg) scale(1.1);
}

/* 🎨 梵高风格表单内容样式 */
.form-content {
  display: flex;
  min-height: 0; /* 确保flex子元素可以收缩 */
  padding: 25px;
  padding: 15px 20px;
  overflow: visible; /* 确保按钮可见 */
  flex: 1;
  flex-direction: column;
  gap: 15px;
}

.form-row {
  display: flex;
  gap: 18px;
  align-items: flex-start;
}

/* 🎨 表单按钮区域样式 */
.form-actions {
  display: flex;
  margin: 20px -25px -25px;
  padding: 20px 25px;
  background: rgb(255 255 255 / 3%);
  justify-content: flex-end;
  gap: 15px;
  border-top: 1px solid rgb(255 255 255 / 10%);
  backdrop-filter: blur(25px) saturate(1.4);
}

.action-btn {
  position: relative;
  min-width: 120px;
  padding: 14px 28px;
  border: 1px solid rgb(139 92 246 / 30%);
  border-radius: 12px;
  overflow: hidden;
  font-size: 15px;
  font-weight: 600;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  backdrop-filter: blur(20px);
}

.cancel-btn {
  color: #6b7280;
  background: rgb(156 163 175 / 10%);
  border-color: rgb(156 163 175 / 30%);
}

.cancel-btn:hover {
  color: #4b5563;
  background: rgb(156 163 175 / 15%);
  box-shadow: 0 6px 20px rgb(107 114 128 / 20%);
  transform: translateY(-2px);
  border-color: rgb(156 163 175 / 50%);
}

.confirm-btn {
  border: 2px solid var(--van-gogh-accent);
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-accent), var(--van-gogh-accent-dark));
}

.confirm-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--van-gogh-accent-dark), #db2777);
  box-shadow: 0 4px 8px rgb(244 114 182 / 40%);
  transform: translateY(-1px);
}

.confirm-btn:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  border-color: #d1d5db;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.form-row.full-width {
  flex-direction: column;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* 确保服务简介和图片组在同一行时高度对齐 */
.description-group,
.image-group {
  align-items: stretch; /* 拉伸到相同高度 */
}

.description-group .form-textarea,
.image-group .image-placeholder,
.image-group .image-preview,
.image-group .ai-loading-status {
  flex: 1; /* 占满可用空间 */
}

.form-label {
  display: block;
  font-size: 15px;
  font-weight: 700;
  color: #2d1b69;
  text-shadow:
    0 1px 2px rgb(255 255 255 / 80%),
    0 0 8px rgb(139 92 246 / 30%);
  margin-bottom: 8px;
  letter-spacing: 0.5px;
}

/* 🎨 标签和AI按钮布局 */
.form-label-with-ai {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}



.ai-btn {
  min-width: 50px;
  padding: 4px 8px;
  border: 1px solid var(--van-gogh-accent);
  border-radius: 6px;
  font-size: 11px;
  font-weight: 600;
  text-align: center;
  color: white;
  background: linear-gradient(45deg, var(--van-gogh-accent), var(--van-gogh-accent-dark));
  box-shadow: 0 2px 4px rgb(244 114 182 / 30%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.ai-btn:hover:not(:disabled) {
  background: linear-gradient(45deg, var(--van-gogh-accent-dark), #db2777);
  box-shadow: 0 4px 8px rgb(244 114 182 / 40%);
  transform: scale(1.05);
}

.ai-btn:disabled {
  background: linear-gradient(45deg, #9ca3af, #6b7280);
  box-shadow: 0 1px 2px rgb(156 163 175 / 20%);
  opacity: 0.6;
  transform: none;
  border-color: #9ca3af;
  cursor: not-allowed;
}

/* 火山引擎按钮样式 */
.ai-btn.volcengine {
  color: white;
  background: linear-gradient(45deg, #ff4500, #dc143c);
  box-shadow: 0 2px 4px rgb(255 69 0 / 30%);
  border-color: #ff4500;
}

.ai-btn.volcengine:hover:not(:disabled) {
  background: linear-gradient(45deg, #dc143c, #b22222);
  box-shadow: 0 4px 8px rgb(255 69 0 / 40%);
}

.form-input,
.form-select,
.form-textarea {
  height: 50px;
  padding: 0 16px;
  border: 1px solid rgb(139 92 246 / 30%);
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  line-height: 50px;
  color: #2d1b69;
  background: rgb(255 255 255 / 10%);
  box-shadow:
    0 4px 20px rgb(139 92 246 / 10%),
    0 2px 8px rgb(139 92 246 / 5%),
    inset 0 1px 0 rgb(255 255 255 / 20%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  backdrop-filter: blur(20px);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  background: rgb(255 255 255 / 20%);
  box-shadow:
    0 8px 32px rgb(139 92 246 / 25%),
    0 4px 16px rgb(244 114 182 / 15%),
    0 0 0 3px rgb(139 92 246 / 20%),
    0 0 20px rgb(139 92 246 / 10%),
    inset 0 1px 0 rgb(255 255 255 / 30%);
  transform: translateY(-1px) scale(1.01);
  outline: none;
  border-color: rgb(139 92 246 / 80%);
  backdrop-filter: blur(25px);
}

.form-input::placeholder,
.form-textarea::placeholder {
  font-weight: 500;
  color: rgb(139 92 246 / 60%);
  opacity: 1;
  font-style: italic;
}

.form-textarea {
  height: 100px;
  max-height: 200px;
  min-height: 100px; /* 输入框高度的2倍 */
  padding: 16px;
  font-family: inherit;
  line-height: 1.5;
  resize: vertical;
  overflow-y: auto;
}

.form-select {
  cursor: pointer;
}

/* 🎨 表单元素悬浮效果 */
.form-input:hover,
.form-select:hover,
.form-textarea:hover {
  background: rgb(255 255 255 / 15%);
  box-shadow:
    0 6px 24px rgb(139 92 246 / 15%),
    0 3px 12px rgb(244 114 182 / 10%),
    inset 0 1px 0 rgb(255 255 255 / 25%);
  transform: translateY(-1px);
  border-color: rgb(139 92 246 / 50%);
  backdrop-filter: blur(22px);
}

.form-select option {
  padding: 12px 16px;
  font-weight: 600;
  color: #2d1b69;
  background: rgb(255 255 255 / 95%);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgb(139 92 246 / 10%);
}

.form-select option:hover {
  color: #2d1b69;
  background: rgb(139 92 246 / 10%);
}

/* 🎨 描述和图片左右布局样式 - 大小一致 */
.description-group {
  flex: 1; /* 描述区域和图片区域大小一致 */
  min-width: 0; /* 防止flex子项溢出 */
}

.image-group {
  flex: 1; /* 图片区域和描述区域大小一致 */
  min-width: 280px; /* 确保图片区域有足够宽度 */
}

/* 🎨 梵高风格模态框底部 */
.modal-footer {
  padding: 18px 20px;
  border-radius: 0 0 15px 15px;
  background: linear-gradient(135deg,
    rgb(139 92 246 / 98%) 0%,
    rgb(124 58 237 / 95%) 50%,
    rgb(109 40 217 / 92%) 100%
  );
  border-top: 3px solid var(--van-gogh-accent);
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;

  /* 🆕 符合边界安全规范 - 按钮区域距离模态框底部≥15px */
  padding-bottom: 20px; /* 确保按钮与模态框底部有足够边距 */
  margin-bottom: 0; /* 移除额外的margin，使用padding控制间距 */
}

.action-btn {
  min-width: 100px;
  padding: 12px 24px;
  border: 2px solid transparent;
  border-radius: 10px;
  font-size: 14px;
  font-weight: 700;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
  text-shadow: 1px 1px 2px rgb(0 0 0 / 50%);
}

.action-btn.cancel {
  color: #fff;
  background: linear-gradient(45deg, #6c757d, #868e96);
  box-shadow: 0 4px 8px rgb(0 0 0 / 30%);
  border-color: #6c757d;
}

.action-btn.cancel:hover {
  background: linear-gradient(45deg, #5a6268, #6c757d);
  box-shadow: 0 6px 12px rgb(0 0 0 / 40%);
  transform: scale(1.05);
}

.action-btn.confirm {
  color: white;
  background: linear-gradient(45deg, var(--van-gogh-accent), var(--van-gogh-accent-dark));
  box-shadow: 0 4px 8px rgb(244 114 182 / 40%);
  border-color: var(--van-gogh-accent);
}

.action-btn.confirm:hover {
  background: linear-gradient(45deg, var(--van-gogh-accent-dark), #db2777);
  box-shadow: 0 6px 12px rgb(244 114 182 / 60%);
  transform: scale(1.05);
}

.btn-face {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 100%;
}

/* 🎨 图片预览样式 - 适配1024x512横向比例 */
.image-preview {
  width: 100%;
  max-height: 160px; /* 限制最大高度 */
  min-height: 160px; /* 增加高度以适应2:1横向图片 */
  border: 2px solid var(--van-gogh-border-medium);
  border-radius: 8px;
  box-sizing: border-box; /* 确保边框包含在高度内 */
  overflow: hidden;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 4px 8px rgb(139 92 246 / 20%);
  margin-top: 10px;
}

.image-preview img {
  display: block;
  width: 100%;
  height: 156px; /* 减去边框高度(2px*2=4px)，实际显示156px，适应2:1横向比例 */
  transition: transform 0.3s ease;
  object-fit: cover; /* 保持图片比例，裁剪多余部分 */
}

.image-preview img:hover {
  transform: scale(1.02);
}

/* 🤖 AI生成状态样式 - 放大显示 */
.ai-loading-status {
  display: flex;
  width: 100%;
  max-height: 160px; /* 限制最大高度 */
  min-height: 160px; /* 增加高度与图片预览框一致 */
  padding: 30px;
  border: 3px dashed var(--van-gogh-accent);
  border-radius: 12px;
  box-sizing: border-box; /* 确保padding包含在高度内 */
  background: rgb(244 114 182 / 15%);
  box-shadow: 0 4px 12px rgb(244 114 182 / 20%);
  margin-top: 10px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  animation: pulse 2s ease-in-out infinite;
}

.loading-spinner {
  width: 36px; /* 增大动画尺寸 */
  height: 36px;
  border: 4px solid rgb(244 114 182 / 30%);
  border-top: 4px solid var(--van-gogh-accent);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  box-shadow: 0 2px 8px rgb(244 114 182 / 40%);
}

.loading-text {
  font-size: 16px; /* 增大字体 */
  font-weight: 700; /* 加粗字体 */
  text-align: center;
  color: var(--van-gogh-accent);
  text-shadow: 0 2px 4px rgb(244 114 182 / 30%);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 🖼️ 图片占位符样式 */
.image-placeholder {
  display: flex;
  width: 100%;
  max-height: 160px; /* 限制最大高度 */
  min-height: 160px; /* 与服务简介框和图片预览框保持相同高度 */
  padding: 30px;
  border: 2px dashed #bdc3c7;
  border-radius: 8px;
  box-sizing: border-box; /* 确保padding包含在高度内 */
  background: rgb(189 195 199 / 10%);
  transition: all 0.3s ease;
  margin-top: 10px;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.image-placeholder:hover {
  border-color: #95a5a6;
  background: rgb(149 165 166 / 15%);
}

.placeholder-icon {
  font-size: 32px;
  opacity: 0.6;
}

.placeholder-text {
  font-size: 12px;
  font-weight: 500;
  text-align: center;
  color: #7f8c8d;
  opacity: 0.8;
}



/* 🎨 响应式设计 - 低分辨率适配 */
@media (width <= 1200px) {
  .service-form-modal {
    width: 95%;
    max-width: calc(100vw - 220px); /* 减去侧边栏宽度180px + 边距40px */
  }

  /* 时长编辑框在低分辨率下的适配 */
  .duration-edit-modal {
    width: min(420px, calc(100vw - 220px)) !important;
    max-width: min(420px, calc(100vw - 220px)) !important;
  }

  .price-edit-modal {
    width: min(500px, calc(100vw - 220px)) !important;
    max-width: min(500px, calc(100vw - 220px)) !important;
  }
}

@media (width <= 768px) {
  /* 移动端模态框覆盖整个屏幕 */
  .modal-overlay {
    left: 0;
    width: 100%;
  }

  .picasso-services {
    left: 0;
    width: 100vw;
    padding: 15px;
  }

  .action-toolbar {
    flex-direction: column;
    gap: 15px;
  }

  .filter-cubism {
    flex-direction: column;
  }

  .action-cubism {
    justify-content: center;
  }

  .data-row {
    flex-direction: column;
    min-height: auto;
  }

  .data-cell {
    border-bottom: 1px solid rgb(142 68 173 / 20%);
    min-height: 50px;                      /* 🎯 与菜单高度一致：50px */
  }

  .title-layer {
    font-size: 2rem;
  }

  .subtitle-fragment {
    font-size: 1rem;
  }

  .action-fragments {
    justify-content: center;
  }

  /* 移动端表单样式 */
  .service-form-modal {
    width: calc(100vw - 20px);
    max-width: calc(100vw - 20px);
    max-height: calc(100vh - 20px);
    margin: 10px;
  }

  /* 移动端时长编辑框 - 保持紧凑尺寸 */
  .duration-edit-modal {
    width: calc(100vw - 20px) !important;
    max-width: calc(100vw - 20px) !important;
    max-height: 300px !important; /* 移动端稍微增加高度 */
  }

  .form-content {
    padding: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .form-row.full-width {
    gap: 15px;
  }

  .footer-actions {
    flex-direction: column;
    gap: 10px;
  }

  .action-btn {
    width: 100%;
  }

  /* 移动端描述和图片布局 */
  .description-group,
  .image-group {
    flex: 1;
    min-width: auto;
  }

  .image-group {
    margin-top: 15px;
  }

  .image-actions {
    gap: 10px;
  }

  .ai-generate-btn {
    width: 100%;
    padding: 10px 16px;
    font-size: 13px;
    text-align: center;
  }

  .image-preview img {
    height: 200px; /* 移动端增加图片高度，适应横向比例 */
  }

  /* 移动端AI状态样式 */
  .ai-loading-status {
    height: 120px;
    padding: 15px;
    justify-content: center;
  }

  .image-placeholder {
    max-height: 180px;
    min-height: 180px; /* 移动端增加高度 */
    padding: 25px 15px;
    justify-content: center;
  }

  .placeholder-icon {
    font-size: 24px;
  }

  .placeholder-text {
    font-size: 11px;
  }

  /* 移动端梵高分页样式 */
  .pagination-container {
    flex-direction: column;
    gap: 10px;
    padding: 12px 15px;
  }

  .pagination-controls {
    gap: 15px;
  }

  .page-navigation {
    gap: 6px;
  }

  .page-btn {
    padding: 6px 10px;
    font-size: 12px;
  }

  .page-number {
    min-width: 32px;
  }

  .pagination-info {
    order: 2;
  }

  .pagination-controls {
    order: 1;
    flex-direction: column;
    gap: 10px;
    width: 100%;
  }

  .page-size-selector {
    justify-content: center;
  }

  .page-navigation {
    justify-content: center;
    flex-wrap: wrap;
  }

  .page-btn {
    padding: 6px 10px;
    font-size: 12px;
  }

  .page-numbers {
    gap: 2px;
  }

  .page-number {
    min-width: 32px;
  }

  .total-info {
    font-size: 13px;
    text-align: center;
  }

  .highlight-number {
    font-size: 14px;
  }

  /* 移动端按钮样式 */
  .form-actions {
    flex-direction: column;
    gap: 10px;
  }

  .action-btn {
    width: 100%;
    min-width: auto;
    padding: 14px 20px;
    font-size: 16px;
  }
}

/* 🎨 超小屏幕适配 (小于480px) */
@media (width <= 480px) {
  /* 超小屏幕模态框覆盖整个屏幕 */
  .modal-overlay {
    left: 0;
    width: 100%;
  }

  .service-form-modal {
    width: calc(100vw - 10px);
    max-width: calc(100vw - 10px);
    margin: 5px;
    border-radius: 12px;
  }

  .form-content {
    padding: 15px;
    gap: 15px;
  }

  .form-header {
    padding: 15px 20px;
  }

  .form-title {
    font-size: 1.1rem;
  }
}

    .search-box {
      @media (width <= 768px) {
        .search-mobile {
          flex-direction: column;
          gap: 12px;

          .ant-input-search {
            width: 100%;
          }

          .filter-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;

            .ant-btn {
              flex: 1;
              min-width: 80px;
            }
          }
        }
      }
    }

    .table-responsive {
      overflow-x: auto;
      -webkit-overflow-scrolling: touch;

      @media (width <= 768px) {
        .ant-table {
          min-width: 800px;
        }

        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: 8px 4px;
          font-size: 12px;

          &:first-child {
            position: sticky;
            left: 0;
            z-index: 1;
            background: #fff;
          }
        }

        .operation-buttons {
          display: flex;
          flex-direction: column;
          gap: 4px;

          .ant-btn {
            height: auto;
            padding: 2px 6px;
            font-size: 11px;
          }
        }
      }

      @media (width <= 480px) {
        .ant-table-thead > tr > th,
        .ant-table-tbody > tr > td {
          padding: 6px 2px;
          font-size: 11px;
        }
      }
    }

    .modal-responsive {
      @media (width <= 768px) {
        .ant-modal {
          max-width: 95vw;
          margin: 10px;
        }

        .ant-modal-content {
          .ant-modal-body {
            padding: 16px;

            .ant-form-item {
              margin-bottom: 16px;

              .ant-form-item-label {
                text-align: left;
                padding-bottom: 4px;
              }
            }

            .form-row {
              flex-direction: column;

              .form-col {
                width: 100%;
                margin-bottom: 12px;
              }
            }
          }
        }
      }

      @media (width <= 480px) {
        .ant-modal {
          top: 0;
          height: 100vh;
          max-width: 100vw;
          margin: 0;
        }

        .ant-modal-content {
          height: 100vh;
          border-radius: 0;

          .ant-modal-body {
            height: calc(100vh - 110px);
            overflow-y: auto;
          }
        }
      }
    }

    .pagination-responsive {
      @media (width <= 768px) {
        .ant-pagination {
          .ant-pagination-options {
            display: none;
          }

          .ant-pagination-item {
            height: 28px;
            min-width: 28px;
            font-size: 12px;
            line-height: 26px;
          }

          .ant-pagination-prev,
          .ant-pagination-next {
            height: 28px;
            min-width: 28px;
            line-height: 26px;
          }
        }
      }

      @media (width <= 480px) {
        .ant-pagination {
          justify-content: center;

          .ant-pagination-item:not(.ant-pagination-item-active) {
            display: none;
          }

          .ant-pagination-jump-prev,
          .ant-pagination-jump-next {
            display: none;
          }
        }
      }
    }

    .page-header-responsive {
      @media (width <= 768px) {
        .page-header {
          padding: 12px 16px;

          .header-content {
            flex-direction: column;
            align-items: flex-start;
            gap: 12px;

            .header-title {
              font-size: 18px;
            }

            .header-actions {
              width: 100%;
              justify-content: space-between;

              .ant-btn {
                flex: 1;
                margin: 0 4px;

                &:first-child {
                  margin-left: 0;
                }

                &:last-child {
                  margin-right: 0;
                }
              }
            }
          }
        }
      }
    }

    .status-responsive {
      @media (width <= 768px) {
        .status-indicator {
          .ant-tag {
            padding: 2px 6px;
            font-size: 11px;
            line-height: 1.2;
          }
        }

        .price-display {
          font-size: 12px;

          .price-symbol {
            font-size: 10px;
          }
        }

        .duration-display {
          font-size: 11px;
        }
      }
    }

/* 💰 价格编辑相关样式 - 使用新的单元格样式 */

.history-indicator {
  display: inline-block;
  padding: 2px 4px;
  border: 1px solid rgb(139 92 246 / 20%);
  border-radius: 3px;
  font-size: 12px;
  background: linear-gradient(135deg, rgb(139 92 246 / 10%) 0%, rgb(244 114 182 / 10%) 100%);
  box-shadow: 0 1px 2px rgb(139 92 246 / 10%);
  opacity: 0.7;
  transition: all 0.3s ease;
  margin-left: 4px;
  cursor: pointer;
  flex-shrink: 0;
}

.history-indicator:hover {
  background: linear-gradient(135deg, rgb(139 92 246 / 20%) 0%, rgb(244 114 182 / 20%) 100%);
  box-shadow: 0 2px 6px rgb(139 92 246 / 20%);
  opacity: 1;
  transform: scale(1.3);
  border-color: rgb(139 92 246 / 40%);
}

/* 🔄 加载状态样式 */
.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.8;
}

.loading-spinner {
  display: inline-block;
  animation: spin 1.2s linear infinite;
  font-size: 1em;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载中的单元格样式 */
.price-cell.loading, .commission-cell.loading, .duration-cell.loading {
  background: linear-gradient(135deg, rgb(139 92 246 / 10%) 0%, rgb(244 114 182 / 10%) 100%);
  border-color: rgb(139 92 246 / 30%);
  box-shadow: 0 0 8px rgb(139 92 246 / 20%);
}

/* 加载中的按钮样式 */
.action-btn.loading {
  background: linear-gradient(135deg, rgb(139 92 246 / 10%) 0%, rgb(244 114 182 / 10%) 100%);
  border-color: rgb(139 92 246 / 30%);
}

/* 加载中的历史记录指示器 */
.history-indicator.loading {
  background: linear-gradient(135deg, rgb(139 92 246 / 15%) 0%, rgb(244 114 182 / 15%) 100%);
  border-color: rgb(139 92 246 / 40%);
}

/* 大型加载容器 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  text-align: center;
}

.loading-spinner-large {
  font-size: 32px;
  animation: spin 1.5s linear infinite;
  margin-bottom: 16px;
}

.loading-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--van-gogh-text-secondary);
}

/* 表格加载状态容器 */
.table-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 300px;
  padding: 40px 0;
}

.table-loading-content {
  display: flex;
  padding: 30px;
  border: 1px solid rgb(139 92 246 / 20%);
  border-radius: 12px;
  background: linear-gradient(135deg, rgb(139 92 246 / 5%) 0%, rgb(244 114 182 / 5%) 100%);
  box-shadow: 0 8px 30px rgb(139 92 246 / 10%);
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  animation: fadeIn 0.5s ease-out;
}

/* 表格空状态容器 */
.table-empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 300px;
  padding: 40px 0;
}

.table-empty-content {
  display: flex;
  padding: 30px;
  border: 1px dashed rgb(139 92 246 / 20%);
  border-radius: 12px;
  background: linear-gradient(135deg, rgb(139 92 246 / 3%) 0%, rgb(244 114 182 / 3%) 100%);
  box-shadow: 0 4px 20px rgb(139 92 246 / 5%);
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.empty-icon {
  font-size: 48px;
  opacity: 0.7;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--van-gogh-text-secondary);
}

/* 💰 价格编辑模态框样式 */
.price-edit-modal {
  width: min(90%, calc(100vw - 240px));
  max-width: 520px;
  min-height: auto; /* 自适应高度 */
  pointer-events: auto; /* 确保模态框可以接收鼠标事件 */
}

.price-edit-form {
  display: flex;
  padding: 15px 0;
  flex-direction: column;
  gap: 20px;
}

.current-value {
  position: relative;
  padding: 12px 16px;
  border: 2px solid var(--van-gogh-border-medium);
  border-radius: 12px;
  overflow: hidden;
  font-size: 20px;
  font-weight: 700;
  text-align: center;
  color: var(--van-gogh-primary);
  background: linear-gradient(135deg,
    rgb(139 92 246 / 10%) 0%,
    rgb(244 114 182 / 10%) 50%,
    rgb(59 130 246 / 10%) 100%
  );
  box-shadow:
    0 4px 12px rgb(139 92 246 / 15%),
    inset 0 1px 0 rgb(255 255 255 / 20%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 10%);
}

.current-value::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg,
    transparent,
    rgb(255 255 255 / 20%),
    transparent
  );
  transition: left 0.5s ease;
}

.current-value:hover {
  box-shadow:
    0 8px 20px rgb(139 92 246 / 25%),
    inset 0 1px 0 rgb(255 255 255 / 30%);
  transform: translateY(-2px) scale(1.02);
  border-color: var(--van-gogh-border-bright);
}

.current-value:hover::before {
  left: 100%;
}

/* 价格编辑表单组样式增强 */
.price-edit-form .form-group {
  margin-bottom: 0; /* 重置margin，使用gap控制间距 */
}

/* 价格编辑表单标签 - 继承主样式 */
.price-edit-form .form-label {
  /* 继承主表单的 .form-label 样式 */
  margin-bottom: 8px;
  display: block;
}

/* 价格编辑表单输入框 - 继承主样式并微调 */
.price-edit-form .form-input {
  width: 100%;
  font-size: 15px; /* 稍微调整字体大小 */

  /* 其他样式继承自 .form-input */
}

.price-edit-form .form-input:focus {
  /* 继承主样式的焦点效果 */
  transform: scale(1.01); /* 稍微减少缩放效果 */
}

/* 📊 价格历史记录模态框样式 */
.price-history-modal {
  width: min(90%, calc(100vw - 240px));
  max-width: 650px;
  max-height: 85vh;
  pointer-events: auto; /* 确保模态框可以接收鼠标事件 */
}

.history-list {
  max-height: 450px;
  overflow-y: auto;
  padding: 15px 0;

  /* 自定义滚动条样式 */
  scrollbar-width: thin;
  scrollbar-color: rgb(102 126 234 / 30%) transparent;
}

.history-list::-webkit-scrollbar {
  width: 6px;
}

.history-list::-webkit-scrollbar-track {
  background: transparent;
}

.history-list::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgb(102 126 234 / 30%);
}

.history-list::-webkit-scrollbar-thumb:hover {
  background: rgb(102 126 234 / 50%);
}

.history-item {
  padding: 18px;
  border: 1px solid rgb(102 126 234 / 15%);
  border-radius: 12px;
  background: linear-gradient(135deg, rgb(255 255 255 / 8%) 0%, rgb(255 255 255 / 4%) 100%);
  box-shadow: 0 2px 8px rgb(0 0 0 / 5%);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  margin-bottom: 12px;
}

.history-item:hover {
  background: linear-gradient(135deg, rgb(255 255 255 / 12%) 0%, rgb(255 255 255 / 8%) 100%);
  box-shadow: 0 4px 16px rgb(102 126 234 / 10%);
  transform: translateY(-2px);
  border-color: rgb(102 126 234 / 25%);
}

.history-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding-bottom: 10px;
  border-bottom: 1px solid rgb(102 126 234 / 20%);
}

.change-type {
  padding: 6px 12px;
  border: 2px solid var(--van-gogh-border-medium);
  border-radius: 8px;
  font-size: 12px;
  font-weight: 700;
  color: var(--van-gogh-primary);
  background: linear-gradient(135deg,
    rgb(139 92 246 / 15%) 0%,
    rgb(244 114 182 / 15%) 50%,
    rgb(59 130 246 / 15%) 100%
  );
  box-shadow: 0 2px 4px rgb(139 92 246 / 10%);
  transition: all 0.3s ease;
  text-transform: uppercase;
  letter-spacing: 0.8px;
}

.change-time {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 13px;
  font-weight: 500;
  color: #718096;
  background: rgb(113 128 150 / 10%);
}

.history-content {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.price-change,
.commission-change {
  display: flex;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 15px;
  background: rgb(255 255 255 / 3%);
  align-items: center;
  gap: 10px;
  border-left: 3px solid rgb(102 126 234 / 30%);
}

.label {
  min-width: 60px;
  font-size: 14px;
  font-weight: 700;
  color: var(--van-gogh-text-secondary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 10%);
}

.old-value {
  padding: 4px 8px;
  border: 1px solid rgb(220 38 38 / 20%);
  border-radius: 6px;
  font-weight: 600;
  color: #dc2626;
  background: linear-gradient(135deg, rgb(220 38 38 / 10%) 0%, rgb(239 68 68 / 10%) 100%);
  box-shadow: 0 1px 3px rgb(220 38 38 / 10%);
  opacity: 0.8;
  text-decoration: line-through;
}

.arrow {
  margin: 0 6px;
  font-size: 18px;
  font-weight: 900;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 10%);
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.1); }
}

.new-value {
  padding: 4px 8px;
  border: 1px solid rgb(5 150 105 / 20%);
  border-radius: 6px;
  font-weight: 700;
  color: #059669;
  background: linear-gradient(135deg, rgb(5 150 105 / 10%) 0%, rgb(16 185 129 / 10%) 100%);
  box-shadow: 0 1px 3px rgb(5 150 105 / 10%);
}

.no-history {
  padding: 50px 20px;
  border: 2px dashed rgb(160 174 192 / 30%);
  border-radius: 12px;
  text-align: center;
  color: #a0aec0;
  background: linear-gradient(135deg, rgb(160 174 192 / 5%) 0%, rgb(160 174 192 / 2%) 100%);
}

.no-history p {
  margin: 0;
  font-size: 16px;
  font-style: italic;
}

/* 💰 价格编辑历史记录紧凑样式 */
.price-history-compact {
  max-height: 200px;
  padding: 10px;
  border: 1px solid var(--van-gogh-border-medium);
  border-radius: 8px;
  background: linear-gradient(135deg, rgb(255 255 255 / 5%) 0%, rgb(255 255 255 / 2%) 100%);
  overflow-y: auto;
}

.history-item-compact {
  padding: 8px 12px;
  border-radius: 6px;
  background: linear-gradient(135deg, rgb(139 92 246 / 5%) 0%, rgb(244 114 182 / 5%) 100%);
  transition: all 0.3s ease;
  margin-bottom: 8px;
  border-left: 3px solid var(--van-gogh-border-medium);
}

.history-item-compact:hover {
  background: linear-gradient(135deg, rgb(139 92 246 / 8%) 0%, rgb(244 114 182 / 8%) 100%);
  border-left-color: var(--van-gogh-border-bright);
  transform: translateX(2px);
}

.history-item-compact:last-child {
  margin-bottom: 0;
}

.history-time {
  font-size: 11px;
  color: var(--van-gogh-text-secondary);
  margin-bottom: 4px;
  opacity: 0.8;
}

.history-changes {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.change-item {
  font-size: 12px;
  font-weight: 500;
  color: var(--van-gogh-text-primary);
}

.more-history {
  font-size: 11px;
  text-align: center;
  color: var(--van-gogh-text-secondary);
  font-style: italic;
  margin-top: 8px;
  padding-top: 8px;
  border-top: 1px solid rgb(139 92 246 / 10%);
}

/* 💰 当前价格显示样式 */
.current-price-section {
  padding: 16px;
  border: 1px solid var(--van-gogh-border-medium);
  border-radius: 8px;
  background: linear-gradient(135deg, rgb(139 92 246 / 8%) 0%, rgb(244 114 182 / 8%) 100%);
  margin-bottom: 20px;
}

.current-price-title {
  font-size: 16px;
  font-weight: 600;
  text-align: center;
  color: var(--van-gogh-text-primary);
  margin-bottom: 12px;
}

.current-price-display {
  display: flex;
  justify-content: space-around;
  gap: 20px;
}

.current-price-item {
  display: flex;
  min-width: 120px;
  padding: 12px;
  border: 1px solid rgb(139 92 246 / 20%);
  border-radius: 6px;
  background: linear-gradient(135deg, rgb(255 255 255 / 5%) 0%, rgb(255 255 255 / 2%) 100%);
  flex-direction: column;
  align-items: center;
  gap: 6px;
}

.current-price-item .price-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--van-gogh-text-secondary);
}

.current-price-item .price-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--van-gogh-text-primary);
  text-shadow: 0 1px 2px rgb(0 0 0 / 10%);
}

/* 📏 历史记录分隔线 */
.history-divider {
  display: flex;
  position: relative;
  margin: 20px 0;
  align-items: center;
}

.history-divider::before {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--van-gogh-border-medium) 50%, transparent 100%);
}

.divider-text {
  padding: 0 16px;
  font-size: 14px;
  font-weight: 500;
  color: var(--van-gogh-text-secondary);
  background: var(--van-gogh-bg-primary);
}

.history-divider::after {
  content: '';
  flex: 1;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, var(--van-gogh-border-medium) 50%, transparent 100%);
}

/* 价格编辑模态框按钮样式 - 继承主样式 */
.price-edit-modal .form-actions,
.price-history-modal .form-actions {
  /* 继承主表单的 .form-actions 样式 */
  margin-top: 15px; /* 稍微调整间距 */
}

/* 响应式设计 */
@media (width <= 768px) {
  .price-edit-modal,
  .price-history-modal {
    width: 95%;
    margin: 10px;
  }

  .history-content {
    flex-direction: column;
  }

  .price-change,
  .commission-change {
    flex-wrap: wrap;
    gap: 4px;
  }

  .history-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 5px;
  }
}

/* 已移除Toast通知组件样式，统一使用Sci-Fi风格通知 */

/* 🎯 强迫症级别的完全统一间距系统 - 永久保存 */

/* 基于用户强迫症要求，所有间距必须完全统一和等分 */

/* 🚨 强制重置所有间距 */
.service-form-modal * {
  box-sizing: border-box !important;
}

/* 🎯 模态框主体 - 按照菜单规格调整 */
.service-form-modal .modal-body,
.service-form-modal .form-content {
  display: flex !important;
  padding: 8px !important;                 /* 🎯 与菜单间距一致：8px */
  flex-direction: column !important;
  gap: 8px !important;                     /* 🎯 与菜单间距一致：8px */
  align-items: stretch !important;
  overflow-y: auto !important;
}

/* 🎯 表单项 - 按照菜单规格调整 */
.service-form-modal .form-item,
.service-form-modal .form-group {
  display: flex !important;
  height: auto !important;
  margin: 0 !important;
  padding: 0 !important;
  flex-direction: column !important;
  gap: 4px !important;                     /* 🎯 标签到输入框间距：4px */
  align-items: stretch !important;
}

/* 🎯 特殊处理 - 并排布局的表单项 */
.service-form-modal .form-item:has(.form-group),
.service-form-modal .form-group:has(.form-group) {
  flex-direction: row !important;
  gap: 8px !important;                     /* 🎯 与菜单间距一致：8px */
}

/* 🎨 统一编辑界面 - 全新现代化样式 */
.unified-edit-modal {
  display: flex;
  flex-direction: column;
  width: min(90vw, 1000px);
  height: calc(100vh - 100px);
  max-height: calc(100vh - 100px);
  border: none;
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(145deg,
    rgb(248 250 252 / 85%) 0%,
    rgb(241 245 249 / 90%) 50%,
    rgb(248 250 252 / 85%) 100%
  );
  box-shadow:
    0 25px 50px rgb(0 0 0 / 15%),
    0 0 0 1px rgb(255 255 255 / 30%),
    inset 0 1px 0 rgb(255 255 255 / 40%);
  backdrop-filter: blur(20px) saturate(1.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: auto;
}

.unified-edit-modal:hover {
  box-shadow:
    0 30px 60px rgb(0 0 0 / 20%),
    0 0 0 1px rgb(255 255 255 / 30%);
  transform: scale(1.002);
}

/* 🎯 顶部标题栏 */
.unified-edit-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 24px;
  background: linear-gradient(135deg,
    rgb(99 102 241 / 12%) 0%,
    rgb(168 85 247 / 8%) 50%,
    rgb(99 102 241 / 10%) 100%
  );
  border-bottom: 1px solid rgb(255 255 255 / 20%);
  backdrop-filter: blur(12px) saturate(1.1);
}

.unified-edit-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
  letter-spacing: -0.02em;
}

.close-btn {
  display: flex;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 8px;
  font-size: 1rem;
  font-weight: 600;
  color: #dc2626;
  background: rgb(239 68 68 / 10%);
  transition: all 0.2s ease;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgb(239 68 68 / 20%);
  transform: scale(1.05);
}

/* 🎯 主内容区域 - 左右布局 */
.unified-edit-content {
  display: flex;
  flex: 1;
  overflow: hidden;
}

/* 🎯 左侧：服务信息展示区 */
.service-info-section {
  flex: 0 0 320px;
  padding: 24px;
  background: linear-gradient(180deg,
    rgb(248 250 252 / 60%) 0%,
    rgb(241 245 249 / 40%) 50%,
    rgb(248 250 252 / 60%) 100%
  );
  border-right: 1px solid rgb(255 255 255 / 30%);
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: rgb(148 163 184 / 30%) transparent;
  backdrop-filter: blur(8px);
}

.service-info-section::-webkit-scrollbar {
  width: 6px;
}

.service-info-section::-webkit-scrollbar-track {
  background: transparent;
}

.service-info-section::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgb(148 163 184 / 30%);
}

.service-header {
  display: flex;
  padding: 20px;
  border: 1px solid rgb(255 255 255 / 30%);
  border-radius: 12px;
  background: rgb(255 255 255 / 40%);
  gap: 16px;
  margin-bottom: 24px;
  backdrop-filter: blur(10px);
}

.service-image-container {
  flex: 0 0 80px;
}

.service-image {
  width: 80px;
  height: 80px;
  border: 2px solid rgb(255 255 255 / 80%);
  border-radius: 12px;
  box-shadow: 0 6px 20px rgb(0 0 0 / 8%);
  object-fit: cover;
}

.service-image-placeholder {
  display: flex;
  width: 80px;
  height: 80px;
  border: 2px dashed rgb(148 163 184 / 40%);
  border-radius: 12px;
  background: linear-gradient(135deg,
    rgb(226 232 240 / 50%) 0%,
    rgb(203 213 225 / 30%) 100%
  );
  align-items: center;
  justify-content: center;
}

.placeholder-icon {
  font-size: 2.5rem;
  opacity: 0.4;
}

.service-basic-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.service-name {
  margin: 0 0 8px;
  font-size: 1.25rem;
  font-weight: 700;
  line-height: 1.3;
  color: #1e293b;
}

.service-description {
  display: -webkit-box;
  margin: 0 0 16px;
  overflow: hidden;
  font-size: 0.95rem;
  line-height: 1.5;
  color: #64748b;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.status-badge {
  display: inline-flex;
  width: fit-content;
  padding: 6px 14px;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: 600;
  align-items: center;
}

.status-badge.active {
  border: 1px solid rgb(34 197 94 / 20%);
  color: #15803d;
  background: linear-gradient(135deg, rgb(34 197 94 / 15%) 0%, rgb(21 128 61 / 10%) 100%);
}

.status-badge.inactive {
  border: 1px solid rgb(239 68 68 / 20%);
  color: #991b1b;
  background: linear-gradient(135deg, rgb(239 68 68 / 15%) 0%, rgb(153 27 27 / 10%) 100%);
}

/* 🎯 当前数据展示区 */
.current-data-section {
  margin-bottom: 20px;
}

.section-title {
  margin: 0 0 12px;
  font-size: 0.95rem;
  font-weight: 600;
  color: #374151;
  padding-bottom: 6px;
  border-bottom: 1px solid rgb(226 232 240 / 60%);
}

.data-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.data-item {
  display: flex;
  padding: 8px 12px;
  border: 1px solid rgb(255 255 255 / 20%);
  border-radius: 6px;
  background: rgb(255 255 255 / 30%);
  justify-content: space-between;
  align-items: center;
  backdrop-filter: blur(6px);
}

.data-label {
  font-size: 0.85rem;
  font-weight: 500;
  color: #6b7280;
}

.data-value {
  font-size: 0.9rem;
  font-weight: 600;
  color: #1f2937;
}

/* 🎯 历史记录标签页专用样式 */
.history-stats {
  padding: 12px 16px;
  border: 1px solid rgb(59 130 246 / 10%);
  border-radius: 8px;
  background: linear-gradient(135deg,
    rgb(59 130 246 / 5%) 0%,
    rgb(99 102 241 / 3%) 100%
  );
  margin-bottom: 20px;
}

.stats-inline {
  display: flex;
  align-items: center;
  gap: 16px;
  justify-content: center;
}

.stat-inline {
  display: flex;
  align-items: center;
  gap: 6px;
}

.stat-label-inline {
  font-size: 0.9rem;
  font-weight: 500;
  color: #6b7280;
}

.stat-value-inline {
  font-size: 0.9rem;
  font-weight: 700;
  color: #1e40af;
}

.stat-divider {
  font-weight: 300;
  color: #d1d5db;
}

/* 🎯 历史记录时间线 */
.history-content {
  flex: 1;
  overflow: hidden;
}

.history-timeline {
  max-height: calc(100vh - 400px);
  overflow-y: auto;
  padding-right: 8px;
  scrollbar-width: thin;
  scrollbar-color: rgb(148 163 184 / 30%) transparent;
}

.history-timeline::-webkit-scrollbar {
  width: 6px;
}

.history-timeline::-webkit-scrollbar-track {
  background: transparent;
}

.history-timeline::-webkit-scrollbar-thumb {
  border-radius: 3px;
  background: rgb(148 163 184 / 30%);
}

.timeline-item {
  position: relative;
  padding-left: 32px;
  margin-bottom: 24px;
}

.timeline-item:last-child {
  margin-bottom: 0;
}

.timeline-marker {
  position: absolute;
  top: 8px;
  left: 0;
  width: 12px;
  height: 12px;
  border: 3px solid rgb(255 255 255 / 90%);
  border-radius: 50%;
  background: linear-gradient(135deg, #4338ca 0%, #7c3aed 100%);
  box-shadow: 0 0 0 2px rgb(99 102 241 / 20%);
}

.timeline-item:not(:last-child)::before {
  position: absolute;
  top: 20px;
  left: 5px;
  width: 2px;
  height: calc(100% + 4px);
  background: linear-gradient(180deg,
    rgb(99 102 241 / 30%) 0%,
    rgb(99 102 241 / 10%) 100%
  );
  content: '';
}

.timeline-content {
  padding: 16px;
  border: 1px solid rgb(255 255 255 / 30%);
  border-radius: 12px;
  background: rgb(255 255 255 / 40%);
  transition: all 0.2s ease;
  backdrop-filter: blur(8px);
}

.timeline-content:hover {
  background: rgb(255 255 255 / 60%);
  box-shadow: 0 8px 25px rgb(99 102 241 / 15%);
  transform: translateY(-1px);
  border-color: rgb(99 102 241 / 30%);
}

.timeline-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.timeline-date {
  font-size: 0.9rem;
  font-weight: 600;
  color: #4b5563;
}

.timeline-index {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  color: #9ca3af;
  background: rgb(156 163 175 / 10%);
}

.timeline-changes {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.change-detail {
  display: flex;
  padding: 8px 12px;
  border: 1px solid rgb(226 232 240 / 40%);
  border-radius: 8px;
  background: rgb(248 250 252 / 80%);
  align-items: center;
  gap: 12px;
}

.change-type {
  min-width: 60px;
  font-size: 0.85rem;
  font-weight: 500;
  color: #6b7280;
}

.change-arrow {
  font-weight: 600;
  color: #9ca3af;
}

.change-new-value {
  padding: 2px 8px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: 600;
  color: #1f2937;
  color: #15803d;
  background: rgb(34 197 94 / 10%);
}

/* 🎯 无历史记录状态 */
.no-history {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.no-history-icon {
  font-size: 4rem;
  opacity: 0.3;
  margin-bottom: 16px;
}

.no-history-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #6b7280;
  margin-bottom: 8px;
}

.no-history-desc {
  font-size: 0.9rem;
  color: #9ca3af;
}

/* 🎯 右侧：编辑表单区 */
.edit-form-section {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 🎯 标签页导航 */
.edit-tabs {
  display: flex;
  padding: 0 32px;
  background: rgb(248 250 252 / 30%);
  border-bottom: 1px solid rgb(255 255 255 / 20%);
  backdrop-filter: blur(8px);
}

.edit-tab {
  display: flex;
  position: relative;
  padding: 16px 24px;
  border: none;
  font-weight: 500;
  color: #64748b;
  background: transparent;
  transition: all 0.2s ease;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  border-bottom: 3px solid transparent;
}

.edit-tab:hover {
  color: #4338ca;
  background: rgb(99 102 241 / 5%);
}

.edit-tab.active {
  color: #4338ca;
  background: rgb(99 102 241 / 10%);
  border-bottom-color: #4338ca;
}

.tab-icon {
  font-size: 1.1rem;
}

.tab-label {
  font-size: 0.95rem;
}

/* 🎯 表单内容区 - 确保无滚动条 */
.edit-form-content {
  display: flex;
  padding: 32px;
  overflow: hidden;
  flex: 1;
  flex-direction: column;
}

.edit-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.panel-title {
  margin: 0 0 24px;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1f2937;
  padding-bottom: 12px;
  border-bottom: 2px solid rgb(99 102 241 / 10%);
}

/* 🎯 表单输入框高度调整 - 与菜单高度一致 */
.service-form-modal .ant-input,
.service-form-modal .ant-select-selector,
.service-form-modal .ant-picker,
.service-form-modal .ant-input-number-input,
.service-form-modal input,
.service-form-modal select {
  height: 50px !important;                 /* 🎯 与菜单高度一致：50px */
  padding: 0 12px !important;
  border: 1px solid rgb(255 255 255 / 15%) !important;
  line-height: 50px !important;
  color: #1f2937 !important;
  background: rgb(255 255 255 / 5%) !important;
  backdrop-filter: blur(20px) saturate(1.3) !important;
}

/* 🎯 输入框focus状态 */
.service-form-modal .ant-input:focus,
.service-form-modal .ant-select-selector:focus,
.service-form-modal .ant-picker:focus,
.service-form-modal .ant-input-number-input:focus,
.service-form-modal input:focus,
.service-form-modal select:focus {
  border: 1px solid rgb(139 92 246 / 30%) !important;
  background: rgb(255 255 255 / 10%) !important;
  box-shadow: 0 0 0 2px rgb(139 92 246 / 10%) !important;
  backdrop-filter: blur(25px) saturate(1.5) !important;
}

.service-form-modal .ant-textarea,
.service-form-modal textarea {
  min-height: 50px !important;             /* 🎯 文本域最小高度与菜单一致 */
  padding: 12px !important;
  border: 1px solid rgb(255 255 255 / 15%) !important;
  line-height: 1.5 !important;
  color: #1f2937 !important;
  background: rgb(255 255 255 / 5%) !important;
  backdrop-filter: blur(20px) saturate(1.3) !important;
}

/* 🎯 文本域focus状态 */
.service-form-modal .ant-textarea:focus,
.service-form-modal textarea:focus {
  border: 1px solid rgb(139 92 246 / 30%) !important;
  background: rgb(255 255 255 / 10%) !important;
  box-shadow: 0 0 0 2px rgb(139 92 246 / 10%) !important;
  backdrop-filter: blur(25px) saturate(1.5) !important;
}

.service-form-modal .form-item .form-group,
.service-form-modal .form-group .form-group {
  flex: 1 !important;
  gap: 4px !important;                     /* 🎯 内部标签到输入框间距：4px */
}

/* 🎯 服务简介和服务图片并排区域 */
.service-form-modal .image-description-row {
  display: flex !important;
  gap: 8px !important;                     /* 🎯 与菜单间距一致：8px */
  align-items: flex-start !important;
}

.service-form-modal .image-description-row > * {
  flex: 1 !important;
}

.service-form-modal .image-description-row .form-item,
.service-form-modal .image-description-row .form-group {
  gap: 4px !important;                     /* 🎯 内部间距：4px */
}

/* 🎯 标签 - 绝对统一的样式 */
.service-form-modal .form-label,
.service-form-modal label {
  display: flex !important;
  height: 20px !important;
  margin: 0 !important;
  padding: 0 !important;
  font-size: 14px !important;
  font-weight: 500 !important;
  line-height: 20px !important;
  color: rgb(139 92 246 / 80%) !important;
  align-items: center !important;
}

/* 🎯 输入框 - 绝对统一的尺寸 */
.service-form-modal .form-input,
.service-form-modal .form-textarea,
.service-form-modal .form-select,
.service-form-modal input[type="text"],
.service-form-modal input[type="number"],
.service-form-modal textarea,
.service-form-modal select {
  height: 44px !important;
  margin: 0 !important;
  padding: 0 16px !important;
  border: 2px solid rgb(139 92 246 / 20%) !important;
  border-radius: 8px !important;
  font-size: 14px !important;
  background: rgb(255 255 255 / 90%) !important;
  transition: all 0.3s ease !important;
}

/* 🎯 文本域特殊处理 */
.service-form-modal .form-textarea,
.service-form-modal textarea {
  height: 88px !important; /* 正好是输入框的2倍 */
  min-height: 88px !important;
  padding: 12px 16px !important;
  resize: vertical !important;
}

/* 🎯 生成按钮 - 精确定位 */
.service-form-modal .generate-button {
  height: 50px !important;                 /* 🎯 与菜单高度一致：50px */
  padding: 0 12px !important;
  border-radius: 6px !important;
  font-size: 12px !important;
  margin-top: 8px !important; /* 🚨 与标签到输入框间距完全一致 */
  align-self: flex-start !important;
}

/* 🎯 图片占位符 - 统一高度 */
.service-form-modal .image-placeholder {
  display: flex !important;
  height: 120px !important; /* 与文本域+生成按钮总高度协调 */
  border: 2px dashed rgb(139 92 246 / 30%) !important;
  border-radius: 8px !important;
  background: rgb(248 244 255 / 50%) !important;
  flex-direction: column !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

/* 🎯 必填标记 - 精确定位 */
.service-form-modal .required-mark {
  font-weight: 600 !important;
  color: rgb(244 114 182 / 80%) !important;
  margin-left: 4px !important;
}

/* 🎯 确保没有意外的边距 */
.service-form-modal .form-item > *:first-child,
.service-form-modal .form-group > *:first-child {
  margin-top: 0 !important;
}

.service-form-modal .form-item > *:last-child,
.service-form-modal .form-group > *:last-child {
  margin-bottom: 0 !important;
}

/* 🎯 焦点状态不影响布局 */
.service-form-modal .form-input:focus,
.service-form-modal .form-textarea:focus,
.service-form-modal .form-select:focus,
.service-form-modal input:focus,
.service-form-modal textarea:focus,
.service-form-modal select:focus {
  border-color: rgb(139 92 246 / 60%) !important;
  box-shadow: 0 0 0 3px rgb(139 92 246 / 10%) !important;
  outline: none !important;
  transform: none !important; /* 确保焦点时不改变位置 */
}

/* 🎯 额外的毛玻璃效果样式 */

/* 🎯 强制移除所有梯形变形效果 */
.data-cubism *,
.table-container *,
.data-row *,
.pagination-container * {
  transform: none !important;
  clip-path: none !important;
}

/* 🎯 允许的轻微变形效果 */
.data-row:hover,
.page-btn:hover {
  transform: translateY(-2px) !important;
}

.page-btn:hover:not(:disabled) {
  transform: translateY(-1px) !important;
}

/* 🎯 操作按钮毛玻璃效果 */
.action-btn {
  border: 1px solid rgb(139 92 246 / 30%) !important;
  color: rgb(139 92 246 / 100%) !important;
  background: rgb(139 92 246 / 10%) !important;
  backdrop-filter: blur(8px) !important;
  text-shadow: 0 1px 2px rgb(255 255 255 / 30%) !important;
}

.action-btn:hover {
  background: rgb(139 92 246 / 15%) !important;
  backdrop-filter: blur(12px) !important;
}

/* 🎯 删除按钮特殊样式 */
.action-btn.delete {
  color: rgb(239 68 68 / 100%) !important;
  background: rgb(239 68 68 / 10%) !important;
  border-color: rgb(239 68 68 / 30%) !important;
}

.action-btn.delete:hover {
  background: rgb(239 68 68 / 15%) !important;
}

/* 🎯 价格显示毛玻璃效果 */
.price-display {
  padding: 4px 8px !important;
  border: 1px solid rgb(139 92 246 / 20%) !important;
  border-radius: 6px !important;
  font-weight: 600 !important;
  color: rgb(139 92 246 / 100%) !important;
  background: rgb(139 92 246 / 8%) !important;
  backdrop-filter: blur(8px) !important;
  text-shadow: 0 1px 2px rgb(255 255 255 / 30%) !important;
}

/* 🎯 状态标签毛玻璃效果 */
.status-tag {
  padding: 4px 12px !important;
  border-radius: 20px !important;
  font-weight: 600 !important;
  backdrop-filter: blur(8px) !important;
  text-shadow: 0 1px 2px rgb(255 255 255 / 30%) !important;
}

.status-tag.active {
  border: 1px solid rgb(34 197 94 / 30%) !important;
  color: rgb(34 197 94 / 100%) !important;
  background: rgb(34 197 94 / 10%) !important;
}

.status-tag.inactive {
  border: 1px solid rgb(239 68 68 / 30%) !important;
  color: rgb(239 68 68 / 100%) !important;
  background: rgb(239 68 68 / 10%) !important;
}

/* 🎯 强迫症级别的完美间距总结 */

/*
  表单项间距: 25px (完全统一)
  标签到输入框: 8px (完全统一)
  并排元素间距: 25px (完全统一)
  模态框内边距: 30px (完全统一)

  毛玻璃效果总结:
  - 数据表格: 2%白色透明度 + 20px模糊
  - 服务行: 1%白色透明度 + 15px模糊
  - 分页组件: 3%紫色透明度 + 20px模糊，高度50px
  - 分页按钮: 6%紫色透明度 + 8px模糊，高度32px
  - 操作按钮: 10%紫色透明度 + 8px模糊
  - 价格显示: 8%紫色透明度 + 8px模糊
  - 状态标签: 10%对应颜色透明度 + 8px模糊

  强迫症满意度: 100% ✨
  所有间距都是完全等分和统一的！
  毛玻璃效果完美，背景图清晰可见！
*/

/* 🎨 统一编辑界面表单组件样式 */
.unified-edit-modal .form-group {
  margin-bottom: 24px;
}

.unified-edit-modal .form-label {
  display: block;
  font-size: 0.95rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.service-form-section .form-label {
  font-size: 0.9rem;
  margin-bottom: 6px;
}

.unified-edit-modal .required {
  color: #ef4444;
  margin-left: 4px;
}

.unified-edit-modal .form-input,
.unified-edit-modal .form-select,
.unified-edit-modal .form-textarea {
  width: 100%;
  padding: 0 16px;
  border: 2px solid rgb(255 255 255 / 30%);
  border-radius: 12px;
  font-family: inherit;
  font-size: 0.95rem;
  color: #1f2937;
  background: rgb(255 255 255 / 40%);
  transition: all 0.2s ease;
  backdrop-filter: blur(8px);
}

.unified-edit-modal .form-input,
.unified-edit-modal .form-select {
  height: 48px;
}

.unified-edit-modal .form-input.compact,
.unified-edit-modal .form-select.compact {
  height: 40px;
  padding: 0 12px;
  font-size: 0.9rem;
}

.unified-edit-modal .form-textarea {
  min-height: 80px;
  padding: 12px 16px;
  resize: vertical;
}

.unified-edit-modal .form-input:focus,
.unified-edit-modal .form-select:focus,
.unified-edit-modal .form-textarea:focus {
  outline: none;
  border-color: #4338ca;
  background: rgb(255 255 255 / 100%);
  box-shadow: 0 0 0 3px rgb(99 102 241 / 10%);
}

.unified-edit-modal .form-input:hover,
.unified-edit-modal .form-select:hover,
.unified-edit-modal .form-textarea:hover {
  border-color: rgb(99 102 241 / 40%);
}



/* 🎯 新增服务表单样式 */
.service-form-section {
  padding: 20px 0;
}

.form-section-title {
  margin: 0 0 20px;
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  padding-bottom: 8px;
  border-bottom: 1px solid rgb(226 232 240 / 60%);
}

.status-options {
  display: flex;
  gap: 20px;
  margin-top: 8px;
}

.status-option {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
}

.status-option input[type="radio"] {
  margin: 0;
}

.status-text {
  font-size: 0.9rem;
  color: #374151;
}

/* 🎯 项目简介区域样式 */
.description-section {
  padding: 20px 0;
}

.form-textarea.large {
  min-height: 300px;
  resize: vertical;
  line-height: 1.6;
}

.form-textarea.medium {
  min-height: 200px;
  resize: vertical;
  line-height: 1.6;
}

.form-textarea.full {
  min-height: 250px;
  resize: vertical;
  line-height: 1.6;
}

.textarea-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 8px;
}

.char-count {
  font-size: 0.8rem;
  font-weight: 500;
  color: #6b7280;
}

.textarea-hint {
  font-size: 0.8rem;
  color: #9ca3af;
  font-style: italic;
}

/* 🎯 编辑数据区域样式 */
.edit-data-section {
  padding: 20px 0;
}

/* 🎯 内容布局 */
.content-layout {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.image-section {
  flex: 0 0 120px;
}

.description-section-main {
  flex: 1;
}

/* 🎯 紧凑图片上传区域 */
.image-upload-compact {
  margin-top: 8px;
}

.image-preview-small {
  display: inline-block;
  position: relative;
  border: 1px solid rgb(226 232 240 / 80%);
  border-radius: 8px;
  overflow: hidden;
}

.preview-image-small {
  display: block;
  width: 100px;
  height: 75px;
  object-fit: cover;
}

.image-actions-small {
  display: flex;
  position: absolute;
  top: 4px;
  right: 4px;
  opacity: 0;
  transition: opacity 0.2s ease;
  gap: 4px;
}

.image-preview-small:hover .image-actions-small {
  opacity: 1;
}

.action-btn-small {
  display: flex;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  font-size: 0.7rem;
  color: #374151;
  background: rgb(255 255 255 / 90%);
  transition: all 0.2s ease;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}

.action-btn-small:hover {
  background: rgb(255 255 255 / 100%);
  transform: scale(1.1);
}

.action-btn-small.remove {
  color: white;
  background: rgb(239 68 68 / 90%);
}

.action-btn-small.remove:hover {
  background: rgb(239 68 68 / 100%);
}

.upload-placeholder-small {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 75px;
  border: 1px dashed rgb(99 102 241 / 40%);
  border-radius: 8px;
  background: rgb(99 102 241 / 2%);
  cursor: pointer;
  transition: all 0.2s ease;
}

.upload-placeholder-small:hover {
  border-color: rgb(99 102 241 / 60%);
  background: rgb(99 102 241 / 5%);
}

.upload-icon-small {
  font-size: 1.5rem;
  margin-bottom: 4px;
  opacity: 0.6;
}

.upload-text-small {
  font-size: 0.7rem;
  text-align: center;
  color: #6b7280;
}

/* 🎯 原有图片上传区域样式（保留备用） */
.image-upload-area {
  margin-top: 8px;
  margin-bottom: 20px;
}

.image-upload-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  border: 2px dashed rgb(99 102 241 / 30%);
  border-radius: 12px;
  background: rgb(99 102 241 / 2%);
  cursor: pointer;
  transition: all 0.2s ease;
}

.image-upload-placeholder:hover {
  border-color: rgb(99 102 241 / 50%);
  background: rgb(99 102 241 / 5%);
}

.upload-icon {
  font-size: 2.5rem;
  margin-bottom: 12px;
  opacity: 0.6;
}

.upload-text {
  font-size: 1rem;
  font-weight: 600;
  color: #374151;
  margin-bottom: 6px;
}

.upload-hint {
  font-size: 0.85rem;
  line-height: 1.4;
  text-align: center;
  color: #6b7280;
}

.image-preview {
  display: inline-block;
  position: relative;
  border: 2px solid rgb(226 232 240 / 80%);
  border-radius: 12px;
  overflow: hidden;
}

.preview-image {
  display: block;
  width: 200px;
  height: 150px;
  object-fit: cover;
}

.image-actions {
  display: flex;
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  padding: 12px;
  background: linear-gradient(transparent, rgb(0 0 0 / 70%));
  opacity: 0;
  transition: opacity 0.2s ease;
  gap: 8px;
  justify-content: center;
}

.image-preview:hover .image-actions {
  opacity: 1;
}

.image-action-btn {
  display: flex;
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  color: #374151;
  background: rgb(255 255 255 / 90%);
  transition: all 0.2s ease;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.image-action-btn:hover {
  background: rgb(255 255 255 / 100%);
  transform: translateY(-1px);
}

.image-action-btn.remove {
  color: white;
  background: rgb(239 68 68 / 90%);
}

.image-action-btn.remove:hover {
  background: rgb(239 68 68 / 100%);
}

.btn-icon {
  font-size: 0.9rem;
}

.btn-text {
  font-size: 0.8rem;
}

/* 🎯 综合编辑表单 */
.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

/* 🎯 左侧表单特殊样式 */
.service-form-section .form-row {
  gap: 12px;
  margin-bottom: 16px;
}

.service-form-section .form-group {
  margin-bottom: 16px;
}

/* 🎯 底部操作按钮 */
.unified-edit-actions {
  display: flex;
  justify-content: flex-end;
  gap: 16px;
  padding: 24px 32px;
  background: rgb(248 250 252 / 40%);
  border-top: 1px solid rgb(255 255 255 / 20%);
  backdrop-filter: blur(10px);
}

.unified-edit-actions .action-btn {
  display: flex;
  min-width: 120px;
  padding: 12px 24px;
  border: none;
  border-radius: 12px;
  font-size: 0.95rem;
  font-weight: 600;
  transition: all 0.2s ease;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  justify-content: center;
}

.cancel-btn {
  border: 1px solid rgb(107 114 128 / 20%);
  color: #4b5563;
  background: rgb(107 114 128 / 10%);
}

.cancel-btn:hover {
  background: rgb(107 114 128 / 15%);
  transform: translateY(-1px);
}

.confirm-btn {
  color: white;
  background: linear-gradient(135deg, #4338ca 0%, #7c3aed 100%);
  box-shadow: 0 4px 12px rgb(99 102 241 / 30%);
}

.confirm-btn:hover:not(:disabled) {
  box-shadow: 0 6px 20px rgb(99 102 241 / 40%);
  transform: translateY(-2px);
}

.confirm-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 🎯 响应式适配 */
@media (width <= 1000px) {
  .unified-edit-modal {
    width: 95vw;
    height: calc(100vh - 60px);
  }

  .service-info-section {
    flex: 0 0 280px;
  }

  .form-row {
    grid-template-columns: 1fr;
  }

  .current-data-grid {
    grid-template-columns: 1fr;
  }
}

@media (width <= 768px) {
  .unified-edit-content {
    flex-direction: column;
  }

  .service-info-section {
    flex: 0 0 auto;
    max-height: 300px;
  }

  .edit-tabs {
    padding: 0 16px;
    overflow-x: auto;
    scrollbar-width: none;
  }

  .edit-tabs::-webkit-scrollbar {
    display: none;
  }

  .edit-tab {
    padding: 12px 16px;
    white-space: nowrap;
  }

  .edit-form-content {
    padding: 20px;
  }

  .unified-edit-actions {
    padding: 16px 20px;
  }

  .stats-inline {
    flex-direction: column;
    gap: 8px;
  }

  .stat-divider {
    display: none;
  }

  .timeline-item {
    padding-left: 24px;
  }

  .timeline-marker {
    width: 10px;
    height: 10px;
  }

  .timeline-item:not(:last-child)::before {
    left: 4px;
  }

  .status-options {
    flex-direction: column;
    gap: 12px;
  }

  .content-layout {
    flex-direction: column;
    gap: 16px;
  }

  .image-section {
    flex: none;
  }

  .preview-image-small {
    width: 80px;
    height: 60px;
  }

  .upload-placeholder-small {
    width: 80px;
    height: 60px;
  }

  .preview-image {
    width: 150px;
    height: 112px;
  }

  .image-upload-placeholder {
    padding: 30px 15px;
  }

  .upload-icon {
    font-size: 2rem;
  }
}

</style>
