<template>
  <div class="picasso-finance">
<!-- 毕加索风格标题区域 -->

    <!-- 毕加索风格财务统计卡片 -->
    <div class="finance-stats-cubism">
      <div class="stat-cube today-revenue">
        <div class="cube-face front">
          <div class="stat-icon">💰</div>
          <div class="stat-value">¥{{ financeStats.todayRevenue }}</div>
          <div class="stat-label">今日收入</div>
        </div>
        <div class="cube-face back"></div>
        <div class="cube-shadow"></div>
      </div>

      <div class="stat-cube month-revenue">
        <div class="cube-face front">
          <div class="stat-icon">📊</div>
          <div class="stat-value">¥{{ financeStats.monthRevenue }}</div>
          <div class="stat-label">本月收入</div>
        </div>
        <div class="cube-face back"></div>
        <div class="cube-shadow"></div>
      </div>

      <div class="stat-cube total-expenses">
        <div class="cube-face front">
          <div class="stat-icon">💸</div>
          <div class="stat-value">¥{{ financeStats.totalExpenses }}</div>
          <div class="stat-label">本月支出</div>
        </div>
        <div class="cube-face back"></div>
        <div class="cube-shadow"></div>
      </div>

      <div class="stat-cube net-profit">
        <div class="cube-face front">
          <div class="stat-icon">📈</div>
          <div class="stat-value">¥{{ financeStats.netProfit }}</div>
          <div class="stat-label">净利润</div>
        </div>
        <div class="cube-face back"></div>
        <div class="cube-shadow"></div>
      </div>
    </div>

    <!-- 毕加索风格图表区域 -->
    <div class="charts-cubism">
      <div class="chart-cube revenue-trend">
        <div class="chart-header">
          <div class="header-fragment">收入趋势</div>
        </div>
        <div class="chart-body">
          <div class="chart-placeholder">
            <div class="trend-chart">
              <div class="trend-line"></div>
              <div class="trend-points">
                <div class="point" v-for="i in 7" :key="i" :style="{ left: (i-1) * 15 + '%', bottom: Math.random() * 60 + 20 + '%' }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="chart-cube expense-breakdown">
        <div class="chart-header">
          <div class="header-fragment">支出分析</div>
        </div>
        <div class="chart-body">
          <div class="chart-placeholder">
            <div class="pie-chart">
              <div class="pie-slice slice-1" style="

--angle: 120deg; --color: #c8a2c8;"></div>
              <div class="pie-slice slice-2" style="

--angle: 90deg; --color: #20b2aa;"></div>
              <div class="pie-slice slice-3" style="

--angle: 80deg; --color: #4169e1;"></div>
              <div class="pie-slice slice-4" style="

--angle: 70deg; --color: #dda0dd;"></div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格快速操作 -->
    <div class="quick-actions-cubism">
      <div class="actions-header">
        <div class="header-fragment">快速操作</div>
      </div>
      <div class="actions-body">
        <div
          class="action-cube"
          @click="addIncome"
          :class="{ 'disabled': loadingStates.addIncome }"
        >
          <div class="action-face">
            <div class="action-icon">💰</div>
            <div class="action-text">
              {{ loadingStates.addIncome ? '处理中...' : '记录收入' }}
            </div>
          </div>
        </div>

        <div
          class="action-cube"
          @click="addExpense"
          :class="{ 'disabled': loadingStates.addExpense }"
        >
          <div class="action-face">
            <div class="action-icon">💸</div>
            <div class="action-text">
              {{ loadingStates.addExpense ? '处理中...' : '记录支出' }}
            </div>
          </div>
        </div>

        <div
          class="action-cube"
          @click="viewReports"
          :class="{ 'disabled': loadingStates.viewReports }"
        >
          <div class="action-face">
            <div class="action-icon">📊</div>
            <div class="action-text">
              {{ loadingStates.viewReports ? '处理中...' : '查看报表' }}
            </div>
          </div>
        </div>

        <div
          class="action-cube"
          @click="exportData"
          :class="{ 'disabled': loadingStates.exportData }"
        >
          <div class="action-face">
            <div class="action-icon">📤</div>
            <div class="action-text">
              {{ loadingStates.exportData ? '导出中...' : '导出数据' }}
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格最近交易 -->
    <div class="recent-transactions-cubism">
      <div class="transactions-header">
        <div class="header-fragment">最近交易</div>
      </div>
      <div class="transactions-body">
        <div 
          v-for="transaction in recentTransactions" 
          :key="transaction.id"
          class="transaction-fragment"
          :class="'transaction-' + transaction.type"
        >
          <div class="transaction-icon">{{ transaction.type === 'income' ? '💰' : '💸' }}</div>
          <div class="transaction-info">
            <div class="transaction-title">{{ transaction.title }}</div>
            <div class="transaction-time">{{ transaction.time }}</div>
          </div>
          <div class="transaction-amount" :class="transaction.type">
            {{ transaction.type === 'income' ? '+' : '-' }}¥{{ transaction.amount }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast通知组件 - 符合CI_CD_STANDARDS.md用户反馈规范 -->
  <div v-if="toastState.visible" class="toast-notification" :class="'toast-' + toastState.type">
    <div class="toast-content">
      <div class="toast-icon">
        <span v-if="toastState.type === 'success'">✅</span>
        <span v-else-if="toastState.type === 'error'">❌</span>
        <span v-else-if="toastState.type === 'warning'">⚠️</span>
      </div>
      <div class="toast-message">{{ toastState.message }}</div>
    </div>
  </div>
</template>

<script setup>
// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

import { ref, reactive, onMounted , nextTick, shallowRef, watchEffect } from 'vue';;
import { useRouter } from 'vue-router';

// 路由
const router = useRouter();

// 响应式数据
const financeStats = reactive({
  todayRevenue: 3280,
  monthRevenue: 45600,
  totalExpenses: 18900,
  netProfit: 26700
});

const recentTransactions = ref([
  { id: 1, type: 'income', title: '颈椎推拿服务费', amount: 120, time: '10:30' },
  { id: 2, type: 'income', title: '全身推拿服务费', amount: 180, time: '11:15' },
  { id: 3, type: 'expense', title: '购买按摩油', amount: 350, time: '12:00' },
  { id: 4, type: 'income', title: '足疗保健服务费', amount: 100, time: '13:30' },
  { id: 5, type: 'expense', title: '水电费', amount: 280, time: '14:15' },
  { id: 6, type: 'income', title: '艾灸理疗服务费', amount: 150, time: '15:00' }
]);

// Toast通知状态 - 符合CI_CD_STANDARDS.md用户反馈规范
const toastState = reactive({
  visible: false,
  message: '',
  type: 'success' // success, error, warning
});

// Toast通知函数 - 替代console日志
const showToast = (message, type = 'success') => {
  toastState.message = message;
  toastState.type = type;
  toastState.visible = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    toastState.visible = false;
  }, 3000);
};

// 加载状态管理 - 符合CI_CD_STANDARDS.md加载状态规范
const loadingStates = reactive({
  addIncome: false,    // 添加收入加载状态
  addExpense: false,   // 添加支出加载状态
  viewReports: false,  // 查看报表加载状态
  exportData: false    // 导出数据加载状态
});

// 方法 - 符合CI_CD_STANDARDS.md错误处理规范
const addIncome = async () => {
  try {
    loadingStates.addIncome = true;
    router.push('/finance/records');
    showToast('跳转到财务记录页面', 'success');
  } catch (error) {
    console.error('页面跳转失败:', error);
    showToast('页面跳转失败，请重试', 'error');
  } finally {
    loadingStates.addIncome = false;
  }
};

const addExpense = async () => {
  try {
    loadingStates.addExpense = true;
    router.push('/finance/records');
    showToast('跳转到财务记录页面', 'success');
  } catch (error) {
    console.error('页面跳转失败:', error);
    showToast('页面跳转失败，请重试', 'error');
  } finally {
    loadingStates.addExpense = false;
  }
};

const viewReports = async () => {
  try {
    loadingStates.viewReports = true;
    router.push('/finance/reports');
    showToast('跳转到财务报表页面', 'success');
  } catch (error) {
    console.error('页面跳转失败:', error);
    showToast('页面跳转失败，请重试', 'error');
  } finally {
    loadingStates.viewReports = false;
  }
};

const exportData = async () => {
  try {
    loadingStates.exportData = true;
    console.log('导出数据');
    // 模拟导出操作
    await new Promise(resolve => setTimeout(resolve, 1000));
    showToast('数据导出成功', 'success');
  } catch (error) {
    console.error('数据导出失败:', error);
    showToast('数据导出失败，请重试', 'error');
  } finally {
    loadingStates.exportData = false;
  }
};

// 模拟数据更新 - 符合CI_CD_STANDARDS.md错误处理规范
onMounted(() => {
  try {
    setInterval(() => {
      try {
        financeStats.todayRevenue += Math.floor(Math.random() * 200);
      } catch (error) {
        console.error('数据更新失败:', error);
        showToast('数据更新异常', 'warning');
      }
    }, 8000);
  } catch (error) {
    console.error('财务概览初始化失败:', error);
    showToast('财务概览初始化失败', 'error');
  }
});
</script>

<style scoped>
/* 毕加索风格财务概览 - 占满主内容区 */
.picasso-finance {
  display: grid;
  position: fixed;
  inset: 0 0 0 180px; /* 侧边栏宽度 */
  width: calc(100vw - 180px);
  height: 100vh;
  padding: 30px; /* 统一主内容区边距 */
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;

  /* 设置与全局背景相同的梵高风格紫色渐变，调整背景位置以匹配全局背景 */
  background: linear-gradient(180deg,
    #2d1b69 0%,    /* 深紫色（梵高星夜风格） */
    #3730a3 15%,   /* 靛蓝紫 */
    #4338ca 30%,   /* 中紫色 */
    #5b21b6 45%,   /* 深紫色 */
    #6b21a8 60%,   /* 紫色 */
    #7c2d92 75%,   /* 紫红色 */
    #86198f 90%,   /* 深紫红 */
    #701a75 100%   /* 最深紫色 */
  ) !important;
  background-attachment: fixed; /* 固定背景，避免位移 */
  grid-template:
    "stats stats" auto "charts actions" 1fr "transactions transactions" auto / 2fr 1fr;
  gap: 20px;
}

@keyframes picassoFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 0% 50%; }
  100% { background-position: 50% 0%; }
}

/* 继承仪表盘的毕加索风格样式 */

/* 已删除无用的标题样式 */

/* 毕加索风格财务统计卡片 */
.finance-stats-cubism {
  display: grid;
  position: relative;
  z-index: 10;
  grid-area: stats;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
}

.stat-cube {
  position: relative;
  height: 140px;
  transform-style: preserve-3d;
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-cube:hover {
  transform: rotateY(10deg) rotateX(5deg) scale(1.05);
}

.cube-face {
  display: flex;
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 20px 5px;
  font-weight: bold;
  color: white;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-shadow: 2px 2px 4px rgb(0 0 0 / 30%);
}

.cube-face.front {
  border: 3px solid;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(255 255 255 / 95%), rgb(255 255 255 / 80%));
  transform: translateZ(10px);
}

.cube-face.back {
  opacity: 0.3;
  transform: translateZ(-10px) rotateY(180deg);
}

.cube-shadow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 20px 5px;
  opacity: 0.3;
  transform: translateZ(-15px) translateX(5px) translateY(5px);
}

.today-revenue .cube-face.front {
  border-color: #ff6b6b;
}

.today-revenue .cube-face.back,
.today-revenue .cube-shadow {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
}

.month-revenue .cube-face.front {
  border-color: #4ecdc4;
}

.month-revenue .cube-face.back,
.month-revenue .cube-shadow {
  background: linear-gradient(45deg, #4ecdc4, #45b7d1);
}

.total-expenses .cube-face.front {
  border-color: #f39c12;
}

.total-expenses .cube-face.back,
.total-expenses .cube-shadow {
  background: linear-gradient(45deg, #f39c12, #e67e22);
}

.net-profit .cube-face.front {
  border-color: #27ae60;
}

.net-profit .cube-face.back,
.net-profit .cube-shadow {
  background: linear-gradient(45deg, #27ae60, #2ecc71);
}

.stat-icon {
  font-size: 2.2rem;
  margin-bottom: 10px;
  transform: rotate(10deg);
}

.stat-value {
  font-size: 1.8rem;
  font-weight: 900;
  margin-bottom: 6px;
  transform: skew(-5deg);
}

.stat-label {
  font-size: 1rem;
  opacity: 0.9;
  transform: skew(3deg);
}

/* 毕加索风格图表区域 */
.charts-cubism {
  display: grid;
  position: relative;
  z-index: 10;
  grid-area: charts;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.chart-cube {
  border-radius: 20px 5px;
  overflow: hidden;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 20px 40px rgb(243 156 18 / 30%);
  transform: perspective(1000px) rotateX(3deg);
}

.chart-header {
  padding: 15px;
  background: linear-gradient(45deg, rgb(255 107 107 / 30%), rgb(78 205 196 / 30%));
  border-bottom: 3px solid;
}

.revenue-trend .chart-header {
  border-color: #ff6b6b;
}

.expense-breakdown .chart-header {
  border-color: #4ecdc4;
}

.header-fragment {
  font-size: 1.2rem;
  font-weight: 900;
  color: #2c3e50;
  text-transform: uppercase;
  letter-spacing: 1px;
  transform: skew(-2deg);
}

.chart-body {
  display: flex;
  height: 200px;
  padding: 20px;
  align-items: center;
  justify-content: center;
}

.trend-chart {
  position: relative;
  width: 100%;
  height: 100%;
}

.trend-line {
  position: absolute;
  top: 50%;
  right: 0;
  left: 0;
  height: 2px;
  border-radius: 2px;
  background: linear-gradient(90deg, #f39c12, #27ae60, #e74c3c);
  transform: translateY(-50%);
}

.trend-points {
  position: relative;
  width: 100%;
  height: 100%;
}

.point {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #f39c12;
  transform: translate(-50%, 50%);
  animation: pointPulse 2s ease-in-out infinite;
}

@keyframes pointPulse {
  0%, 100% { transform: translate(-50%, 50%) scale(1); }
  50% { transform: translate(-50%, 50%) scale(1.3); }
}

.pie-chart {
  position: relative;
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: conic-gradient(
    #ff6b6b 0deg 90deg,
    #4ecdc4 90deg 180deg,
    #f39c12 180deg 270deg,
    #27ae60 270deg 360deg
  );
  animation: pieRotate 3s ease-out;
}

@keyframes pieRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 毕加索风格快速操作 */
.quick-actions-cubism {
  position: relative;
  z-index: 10;
  border-radius: 20px 5px;
  overflow: hidden;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 20px 40px rgb(142 68 173 / 30%);
  transform: perspective(1000px) rotateY(-3deg);
  grid-area: actions;
}

.actions-header {
  padding: 15px;
  background: linear-gradient(45deg, rgb(142 68 173 / 30%), rgb(155 89 182 / 30%));
  border-bottom: 3px solid #8e44ad;
}

.actions-body {
  display: grid;
  padding: 20px;
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
}

.action-cube {
  height: 80px;
  border-radius: 15px 0;
  transform: perspective(400px) rotateX(10deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-cube:hover {
  transform: perspective(400px) rotateX(10deg) scale(1.05);
}

.action-cube:nth-child(1) {
  background: linear-gradient(45deg, #f39c12, #e67e22);
}

.action-cube:nth-child(2) {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
}

.action-cube:nth-child(3) {
  background: linear-gradient(45deg, #27ae60, #2ecc71);
}

.action-cube:nth-child(4) {
  background: linear-gradient(45deg, #3498db, #2980b9);
}

.action-face {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 15px 0;
  font-weight: bold;
  color: white;
  box-shadow: 0 5px 15px rgb(0 0 0 / 30%);
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.action-icon {
  font-size: 1.5rem;
  transform: rotate(5deg);
}

.action-text {
  font-size: 0.9rem;
  transform: skew(-2deg);
}

/* 毕加索风格最近交易 */
.recent-transactions-cubism {
  position: relative;
  z-index: 10;
  border-radius: 20px 5px;
  overflow: hidden;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 20px 40px rgb(52 152 219 / 30%);
  transform: perspective(1000px) rotateX(2deg);
  grid-area: transactions;
}

.transactions-header {
  padding: 15px;
  background: linear-gradient(45deg, rgb(52 152 219 / 30%), rgb(41 128 185 / 30%));
  border-bottom: 3px solid #3498db;
}

.transactions-body {
  max-height: 300px;
  padding: 15px;
  overflow-y: auto;

  /* 毕加索风格滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #3498db rgb(52 152 219 / 20%);
}

.transactions-body::-webkit-scrollbar {
  width: 6px;
}

.transactions-body::-webkit-scrollbar-track {
  border-radius: 6px;
  background: rgb(52 152 219 / 10%);
}

.transactions-body::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: linear-gradient(45deg, #3498db, #2980b9);
}

.transaction-fragment {
  display: flex;
  padding: 12px;
  border-radius: 12px 0;
  background: linear-gradient(135deg,
    rgb(52 152 219 / 10%),
    rgb(243 156 18 / 10%),
    rgb(255 255 255 / 80%)
  );
  transform: skew(-1deg);
  transition: all 0.3s ease;
  align-items: center;
  gap: 12px;
  margin-bottom: 8px;
  border-left: 4px solid;
}

.transaction-fragment:hover {
  box-shadow: 0 3px 10px rgb(52 152 219 / 30%);
  transform: skew(-1deg) scale(1.02);
}

.transaction-income {
  border-color: #27ae60;
}

.transaction-expense {
  border-color: #e74c3c;
}

.transaction-icon {
  font-size: 1.5rem;
  transform: rotate(8deg);
}

.transaction-info {
  flex: 1;
}

.transaction-title {
  font-size: 0.9rem;
  font-weight: bold;
  color: #2c3e50;
  transform: skew(1deg);
  margin-bottom: 2px;
}

.transaction-time {
  font-size: 0.8rem;
  color: #7f8c8d;
  transform: skew(-1deg);
}

.transaction-amount {
  font-size: 1rem;
  font-weight: bold;
  transform: skew(-2deg);
}

.transaction-amount.income {
  color: #27ae60;
}

.transaction-amount.expense {
  color: #e74c3c;
}

/* 响应式设计 */
@media (width <= 768px) {
  .picasso-finance {
    left: 0;
    width: 100vw;
    grid-template:
      "header" auto "stats" auto "charts" auto "actions" auto "transactions" 1fr / 1fr;
    padding: 15px;
  }

  .finance-stats-cubism {
    grid-template-columns: repeat(2, 1fr);
  }

  .charts-cubism {
    grid-template-columns: 1fr;
  }

  .actions-body {
    grid-template-columns: 1fr;
  }

  .title-layer {
    font-size: 2rem;
  }

  .subtitle-fragment {
    font-size: 1rem;
  }
}

/* 🎨 按钮禁用状态 - 符合CI_CD_STANDARDS.md加载状态规范 */
.action-cube.disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: not-allowed;
}

/* 🎨 Toast通知组件样式 - 符合CI_CD_STANDARDS.md用户反馈规范 */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  max-width: 500px;
  min-width: 300px;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow:
    0 8px 32px rgb(0 0 0 / 20%),
    0 4px 16px rgb(0 0 0 / 10%);
  backdrop-filter: blur(10px);
  animation: slideInRight 0.3s ease-out;
}

.toast-success {
  border: 2px solid rgb(34 197 94 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(34 197 94 / 90%), rgb(22 163 74 / 90%));
}

.toast-error {
  border: 2px solid rgb(239 68 68 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(239 68 68 / 90%), rgb(220 38 38 / 90%));
}

.toast-warning {
  border: 2px solid rgb(245 158 11 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(245 158 11 / 90%), rgb(217 119 6 / 90%));
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toast-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.toast-message {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
