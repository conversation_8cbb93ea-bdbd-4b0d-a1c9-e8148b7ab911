<template>
  <div class="appointments-view">
    <a-layout>
      <a-layout-sider
        v-model:collapsed="collapsed"
        class="admin-sider"
        :trigger="null"
        collapsible
      >
        <div class="logo">
          <h2 v-if="!collapsed">壹心堂管理系统</h2>
          <h2 v-else>壹</h2>
        </div>
        <a-menu v-model:selectedKeys="selectedKeys" theme="dark" mode="inline">
          <a-menu-item key="dashboard">
            <template #icon>
              <DashboardOutlined />
            </template>
            <span>工作台</span>
            <router-link to="/"></router-link>
          </a-menu-item>
          <a-menu-item key="appointments">
            <template #icon>
              <CalendarOutlined />
            </template>
            <span>预约管理</span>
            <router-link to="/appointments"></router-link>
          </a-menu-item>
          <a-menu-item key="customers">
            <template #icon>
              <TeamOutlined />
            </template>
            <span>客户管理</span>
            <router-link to="/customers"></router-link>
          </a-menu-item>
          <a-menu-item key="services">
            <template #icon>
              <AppstoreOutlined />
            </template>
            <span>服务项目</span>
            <router-link to="/services"></router-link>
          </a-menu-item>
          <a-menu-item key="employees">
            <template #icon>
              <UserOutlined />
            </template>
            <span>员工管理</span>
            <router-link to="/employees"></router-link>
          </a-menu-item>
          <a-menu-item key="finance">
            <template #icon>
              <PayCircleOutlined />
            </template>
            <span>财务记账</span>
            <router-link to="/finance"></router-link>
          </a-menu-item>
        </a-menu>
      </a-layout-sider>
      <a-layout>
        <a-layout-header class="admin-header">
          <a-button
            type="text"
            :icon="collapsed ? h(MenuUnfoldOutlined) : h(MenuFoldOutlined)"
            @click="() => (collapsed = !collapsed)"
            class="trigger"
          />
          <div class="header-right">
            <a-dropdown>
              <a class="admin-user"> <a-avatar icon="user" /> 管理员 </a>
              <template #overlay>
                <a-menu>
                  <a-menu-item key="logout" @click="handleLogout"
                    >退出登录</a-menu-item
                  >
                </a-menu>
              </template>
            </a-dropdown>
          </div>
        </a-layout-header>
        <a-layout-content class="admin-content">
          <div class="appointments-content">
            <div class="page-header">
              <h2>预约管理</h2>
              <a-button
                type="primary"
                @click="showCreateModal"
                :disabled="loadingStates.createSubmit"
                >新增预约</a-button
              >
            </div>

            <!-- 筛选条件 -->
            <a-card class="filter-card">
              <a-form layout="inline" :model="filterForm">
                <a-form-item label="预约状态">
                  <a-select
                    v-model:value="filterForm.status"
                    style="width: 120px"
                    @change="handleFilter"
                  >
                    <a-select-option value="">全部</a-select-option>
                    <a-select-option value="pending">待确认</a-select-option>
                    <a-select-option value="confirmed">已确认</a-select-option>
                    <a-select-option value="completed">已完成</a-select-option>
                    <a-select-option value="cancelled">已取消</a-select-option>
                  </a-select>
                </a-form-item>
                <a-form-item label="预约日期">
                  <a-date-picker
                    v-model:value="filterForm.date"
                    @change="handleFilter"
                  />
                </a-form-item>
                <a-form-item label="客户姓名">
                  <a-input
                    v-model:value="filterForm.customerName"
                    placeholder="请输入客户姓名"
                    @change="handleFilter"
                  />
                </a-form-item>
                <a-form-item>
                  <a-button
                    @click="resetFilter"
                    :disabled="loadingStates.createSubmit"
                  >重置</a-button>
                </a-form-item>
              </a-form>
            </a-card>

            <!-- 预约列表 -->
            <a-card>
              <SmartTable
                :columns="columns"
                :data-source="appointments"
                :loading="loading"
                :pagination="pagination"
                @change="handleTableChange"
                :auto-format="true"
                :format-options="formatOptions"
                size="middle"
                row-key="id"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.key === 'status'">
                    <a-tag :color="getStatusColor(record.status)">
                      {{ getStatusText(record.status) }}
                    </a-tag>
                  </template>
                  <template v-else-if="column.key === 'action'">
                    <a-space>
                      <a-button
                        size="small"
                        @click="viewDetail(record)"
                        :disabled="loadingStates.createSubmit"
                        >详情</a-button
                      >
                      <a-button
                        size="small"
                        type="primary"
                        v-if="record.status === 'pending'"
                        @click="confirmAppointment(record)"
                        :disabled="loadingStates.createSubmit"
                      >
                        确认
                      </a-button>
                      <a-button
                        size="small"
                        type="primary"
                        v-if="record.status === 'confirmed'"
                        @click="completeAppointment(record)"
                      >
                        完成
                      </a-button>
                      <a-button
                        size="small"
                        danger
                        v-if="['pending', 'confirmed'].includes(record.status)"
                        @click="cancelAppointment(record)"
                      >
                        取消
                      </a-button>
                    </a-space>
                  </template>
                </template>
              </SmartTable>
            </a-card>
          </div>
        </a-layout-content>
      </a-layout>
    </a-layout>

    <!-- 创建预约模态框 -->
    <a-modal
      v-model:visible="createModalVisible"
      title="新增预约"
      @ok="handleCreateSubmit"
      @cancel="handleCreateCancel"
      :confirm-loading="loadingStates.createSubmit"
      :ok-button-props="{ disabled: loadingStates.createSubmit }"
    >
      <a-form
        :model="createForm"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-form-item label="客户" required>
          <a-select
            v-model:value="createForm.customerId"
            placeholder="请选择客户"
          >
            <a-select-option
              v-for="customer in customers"
              :key="customer.id"
              :value="customer.id"
            >
              {{ customer.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="服务项目" required>
          <a-select
            v-model:value="createForm.serviceId"
            placeholder="请选择服务项目"
          >
            <a-select-option
              v-for="service in services"
              :key="service.id"
              :value="service.id"
            >
              {{ service.name }} - ¥{{ service.price }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="技师" required>
          <a-select
            v-model:value="createForm.therapistId"
            placeholder="请选择技师"
          >
            <a-select-option
              v-for="therapist in therapists"
              :key="therapist.id"
              :value="therapist.id"
            >
              {{ therapist.name }}
            </a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="预约日期" required>
          <a-date-picker
            v-model:value="createForm.appointmentDate"
            style="width: 100%"
          />
        </a-form-item>
        <a-form-item label="预约时间" required>
          <a-time-picker
            v-model:value="createForm.appointmentTime"
            style="width: 100%"
            format="HH:mm"
          />
        </a-form-item>
        <a-form-item label="备注">
          <a-textarea
            v-model:value="createForm.notes"
            placeholder="请输入备注信息"
          />
        </a-form-item>
      </a-form>
    </a-modal>

    <!-- Toast通知组件 - 符合CI_CD_STANDARDS.md用户反馈规范 -->
    <div v-if="toastState.visible" class="toast-notification" :class="'toast-' + toastState.type">
      <div class="toast-content">
        <div class="toast-icon">
          <span v-if="toastState.type === 'success'">✅</span>
          <span v-else-if="toastState.type === 'error'">❌</span>
          <span v-else-if="toastState.type === 'warning'">⚠️</span>
        </div>
        <div class="toast-message">{{ toastState.message }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

import { ref, reactive, onMounted, h , nextTick, shallowRef, watchEffect } from 'vue';;
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import SmartTable from '@/components/SmartTable.vue';
import {
  DashboardOutlined,
  MenuUnfoldOutlined,
  MenuFoldOutlined,
  CalendarOutlined,
  TeamOutlined,
  UserOutlined,
  AppstoreOutlined,
  PayCircleOutlined
} from '@ant-design/icons-vue';

const router = useRouter()
const collapsed = ref(false)
const selectedKeys = ref(['appointments'])
const loading = ref(false)
const createModalVisible = ref(false)

// 加载状态管理 - 符合CI_CD_STANDARDS.md加载状态规范
const loadingStates = reactive({
  createSubmit: false  // 创建提交加载状态
});

// 筛选表单
const filterForm = reactive({
  status: '',
  date: null,
  customerName: '',
})

// 创建表单
const createForm = reactive({
  customerId: null,
  serviceId: null,
  therapistId: null,
  appointmentDate: null,
  appointmentTime: null,
  notes: '',
})

// Toast通知状态 - 符合CI_CD_STANDARDS.md用户反馈规范
const toastState = reactive({
  visible: false,
  message: '',
  type: 'success' // success, error, warning
});

// Toast通知函数 - 替代console日志
const showToast = (message, type = 'success') => {
  toastState.message = message;
  toastState.type = type;
  toastState.visible = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    toastState.visible = false;
  }, 3000);
};

// 表单验证错误状态 - 符合CI_CD_STANDARDS.md表单验证规范
const formErrors = reactive({
  customerId: '',
  serviceId: '',
  therapistId: '',
  appointmentDate: '',
  appointmentTime: ''
});

// 表单验证函数 - 符合CI_CD_STANDARDS.md表单验证规范
const validateForm = () => {
  // 清空之前的错误
  Object.assign(formErrors, {
    customerId: '',
    serviceId: '',
    therapistId: '',
    appointmentDate: '',
    appointmentTime: ''
  });

  let isValid = true;

  // 验证客户
  if (!createForm.customerId) {
    formErrors.customerId = '请选择客户';
    isValid = false;
  }

  // 验证服务项目
  if (!createForm.serviceId) {
    formErrors.serviceId = '请选择服务项目';
    isValid = false;
  }

  // 验证技师
  if (!createForm.therapistId) {
    formErrors.therapistId = '请选择技师';
    isValid = false;
  }

  // 验证预约日期
  if (!createForm.appointmentDate) {
    formErrors.appointmentDate = '请选择预约日期';
    isValid = false;
  }

  // 验证预约时间
  if (!createForm.appointmentTime) {
    formErrors.appointmentTime = '请选择预约时间';
    isValid = false;
  }

  return isValid;
};

// 预约列表
const appointments = ref([])

// 表格格式化选项
const formatOptions = ref({
  appointmentDate: { format: 'date' },
  appointmentTime: { format: 'time' },
  created_at: { format: 'relative' },
  price: { unit: '元' }
})
const customers = ref([])
const services = ref([])
const therapists = ref([])

// 分页配置
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showSizeChanger: true,
  showQuickJumper: true
});

// 表格列配置
const columns = [
  {
    title: '预约编号',
    dataIndex: 'appointmentNo',
    key: 'appointmentNo',
  },
  {
    title: '客户姓名',
    dataIndex: 'customerName',
    key: 'customerName',
  },
  {
    title: '服务项目',
    dataIndex: 'serviceName',
    key: 'serviceName',
  },
  {
    title: '技师',
    dataIndex: 'therapistName',
    key: 'therapistName',
  },
  {
    title: '预约时间',
    dataIndex: 'appointmentDateTime',
    key: 'appointmentDateTime',
  },
  {
    title: '状态',
    dataIndex: 'status',
    key: 'status',
  },
  {
    title: '操作',
    key: 'action',
  }
];

// 加载预约列表
const loadAppointments = async () => {
  loading.value = true
  try {
    // 模拟API调用
    const mockData = [
      {
        id: '1',
        appointmentNo: 'AP20241201001',
        customerName: '张三',
        serviceName: '全身推拿',
        therapistName: '李医师',
        appointmentDateTime: '2024-12-05 14:00',
        status: 'confirmed',
      },
      {
        id: '2',
        appointmentNo: 'AP20241201002',
        customerName: '李四',
        serviceName: '颈椎调理',
        therapistName: '王医师',
        appointmentDateTime: '2024-12-05 15:30',
        status: 'pending',
      }
    ];

    appointments.value = mockData
    pagination.total = mockData.length
  } catch (error) {
    message.error('加载预约列表失败');
  } finally {
    loading.value = false
  }
}

// 状态颜色映射
const getStatusColor = (status) => {
  const colorMap = {
    pending: 'orange',
    confirmed: 'purple',
    completed: 'green',
    cancelled: 'red',
  }
  return colorMap[status] || 'default';
}

// 状态文本映射
const getStatusText = (status) => {
  const textMap = {
    pending: '待确认',
    confirmed: '已确认',
    completed: '已完成',
    cancelled: '已取消',
  }
  return textMap[status] || status
};

// 筛选处理
const handleFilter = () => {
  loadAppointments()
};

// 重置筛选
const resetFilter = () => {
  Object.assign(filterForm, {
    status: '',
    date: null,
    customerName: '',
  })
  loadAppointments()
};

// 表格变化处理
const handleTableChange = (pag) => {
  pagination.current = pag.current
  pagination.pageSize = pag.pageSize
  loadAppointments()
};

// 显示创建模态框
const showCreateModal = () => {
  createModalVisible.value = true
};

// 处理创建提交 - 符合CI_CD_STANDARDS.md表单验证规范
const handleCreateSubmit = async () => {
  try {
    // 表单验证
    if (!validateForm()) {
      showToast('请检查输入内容', 'error');
      return;
    }

    loadingStates.createSubmit = true;

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000));

    message.success('预约创建成功');
    showToast('预约创建成功', 'success');
    createModalVisible.value = false;
    loadAppointments();
  } catch (error) {
    console.error('预约创建失败:', error);
    message.error('预约创建失败');
    showToast('预约创建失败，请重试', 'error');
  } finally {
    loadingStates.createSubmit = false;
  }
};

// 处理创建取消
const handleCreateCancel = () => {
  createModalVisible.value = false
  Object.assign(createForm, {
    customerId: null,
    serviceId: null,
    therapistId: null,
    appointmentDate: null,
    appointmentTime: null,
    notes: '',
  })
};

// 查看详情
const viewDetail = (record) => {
  message.info(`查看预约 ${record.appointmentNo} 的详情`)
};

// 确认预约
const confirmAppointment = (record) => {
  message.success(`预约 ${record.appointmentNo} 已确认`)
  loadAppointments()
};

// 完成预约
const completeAppointment = (record) => {
  message.success(`预约 ${record.appointmentNo} 已完成`)
  loadAppointments()
};

// 取消预约
const cancelAppointment = (record) => {
  message.success(`预约 ${record.appointmentNo} 已取消`)
  loadAppointments()
};

// 退出登录
const handleLogout = () => {
  localStorage.removeItem('token');
  router.push('/login');
}

onMounted(() => {
  loadAppointments()
});
</script>

<style lang="scss" scoped>
.appointments-view {
  height: 100%;

  .admin-sider {
    height: 100vh;
    overflow: auto;

    .logo {
      display: flex;
      height: 64px;
      color: white;
      background: #001529;
      align-items: center;
      justify-content: center;

      h2 {
        margin: 0;
        color: white;
      }
    }
  }

  .admin-header {
    display: flex;
    padding: 0 16px;
    background: #fff;
    box-shadow: 0 1px 4px rgb(0 21 41 / 8%);
    justify-content: space-between;
    align-items: center;

    .trigger {
      font-size: 18px;
      cursor: pointer;
    }

    .header-right {
      .admin-user {
        display: flex;
        align-items: center;
        gap: 8px;
        color: #333;
      }
    }
  }

  .admin-content {
    min-height: 280px;
    margin: 24px;
    padding: 24px;
    overflow: hidden; // 取消右侧滚动条
    background: #fff;
  }

  .appointments-content {
    .page-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;

      h2 {
        margin: 0;
      }
    }

    .filter-card {
      margin-bottom: 16px;
    }

    // 表格滚动条样式
    :deep(.ant-table-wrapper) {
      .ant-table-container {
        .ant-table-body {
          max-height: 60vh; // 限制表格高度
          overflow-y: auto; // 表格内容区域滚动

          // 自定义滚动条样式
          &::-webkit-scrollbar {
            width: 6px;
          }

          &::-webkit-scrollbar-track {
            border-radius: 3px;
            background: #f1f1f1;
          }

          &::-webkit-scrollbar-thumb {
            border-radius: 3px;
            background: #c1c1c1;

            &:hover {
              background: #a8a8a8;
            }
          }
        }
      }
    }
  }
}

/* 🎨 Toast通知组件样式 - 符合CI_CD_STANDARDS.md用户反馈规范 */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  max-width: 500px;
  min-width: 300px;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow:
    0 8px 32px rgb(0 0 0 / 20%),
    0 4px 16px rgb(0 0 0 / 10%);
  backdrop-filter: blur(10px);
  animation: slideInRight 0.3s ease-out;
}

.toast-success {
  border: 2px solid rgb(34 197 94 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(34 197 94 / 90%), rgb(22 163 74 / 90%));
}

.toast-error {
  border: 2px solid rgb(239 68 68 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(239 68 68 / 90%), rgb(220 38 38 / 90%));
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toast-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.toast-message {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 🎨 响应式设计断点 - 符合CI_CD_STANDARDS.md响应式设计规范 */

/* 平板设备 (768px - 1024px) */
@media (width <= 1024px) {
  .appointments-container {
    padding: 15px;
  }

  .search-form {
    gap: 10px;
  }

  .search-form .ant-input,
  .search-form .ant-select {
    font-size: 14px;
  }

  .ant-btn {
    padding: 8px 16px;
    font-size: 14px;
  }

  .ant-table {
    font-size: 13px;
  }
}

/* 移动端设备 (最大768px) */
@media (width <= 768px) {
  .appointments-container {
    padding: 10px;
  }

  .search-form {
    flex-direction: column;
    gap: 12px;
  }

  .search-form .ant-form-item {
    margin-bottom: 0;
    width: 100%;
  }

  .search-form .ant-input,
  .search-form .ant-select {
    width: 100%;
    padding: 12px;
    font-size: 16px; /* 移动端增大字体 */
  }

  .search-form .ant-btn {
    width: 100%;
    height: 48px;
    font-size: 16px;
  }

  .ant-table {
    font-size: 12px;
  }

  .ant-table-thead > tr > th {
    padding: 8px 4px;
    font-size: 12px;
  }

  .ant-table-tbody > tr > td {
    padding: 8px 4px;
    font-size: 12px;
  }

  .ant-btn-sm {
    padding: 4px 8px;
    font-size: 11px;
  }

  .ant-modal {
    max-width: calc(100vw - 20px);
    margin: 10px;
  }

  .ant-modal-content {
    border-radius: 8px;
  }

  .ant-form-item-label {
    font-size: 14px;
  }

  .ant-input,
  .ant-select-selector {
    padding: 12px;
    font-size: 16px;
  }

  .toast-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
  }
}

/* 小屏幕移动端 (最大480px) */
@media (width <= 480px) {
  .appointments-container {
    padding: 8px;
  }

  .ant-table-wrapper {
    overflow-x: auto;
  }

  .ant-table {
    min-width: 600px; /* 确保表格在小屏幕上可滚动 */
    font-size: 11px;
  }

  .ant-table-thead > tr > th {
    padding: 6px 2px;
    font-size: 10px;
    white-space: nowrap;
  }

  .ant-table-tbody > tr > td {
    padding: 6px 2px;
    font-size: 10px;
  }

  .ant-btn-sm {
    padding: 2px 6px;
    font-size: 10px;
  }

  .ant-modal {
    max-width: calc(100vw - 10px);
    margin: 5px;
  }

  .ant-form-item-label {
    font-size: 12px;
  }

  .ant-input,
  .ant-select-selector {
    padding: 10px;
    font-size: 16px;
  }
}
</style>
