<template>
  <div class="picasso-services">
    <!-- 科幻通知组件 -->
    <SciFiNotification ref="notification" />

    <!-- 毕加索风格数据表格 -->
    <div class="data-cubism" :style="{ height: dynamicTableHeight + 'px' }">
      <div class="table-container">
        <!-- 集成操作功能的智能表头 -->
        <div class="smart-table-header">
          <!-- 列标题（支持搜索和排序） -->
          <div class="header-columns">
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.name }" style="flex: 2.2;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.name" class="header-search-container" @mouseleave="handleSearchMouseLeave('name')">
                <input
                  :ref="el => searchInputRefs.name = el"
                  type="text"
                  placeholder="🔍 搜索客户信息..."
                  v-model="searchValues.name"
                  @input="handleSearchInput('name')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('name')"
                  @keydown.enter="handleSearchEnter($event, 'name')"
                  @keydown.esc="exitSearchMode('name')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('name')">
                <span class="header-text">客户信息</span>
              </div>
              <!-- 动态按钮 -->
              <button v-if="searchValues.name && searchValues.name.trim()" @click="exitSearchMode('name')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.name.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('name')" :disabled="sortButtonStates.name.disabled" title="排序客户信息">
                <span class="sort-indicator" :class="getSortClass('name')">
                  {{ getSortIcon('name') }}
                </span>
              </button>
            </div>
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.phone }" style="flex: 1;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.phone" class="header-search-container" @mouseleave="handleSearchMouseLeave('phone')">
                <input
                  :ref="el => searchInputRefs.phone = el"
                  type="text"
                  placeholder="🔍 搜索联系方式..."
                  v-model="searchValues.phone"
                  @input="handleSearchInput('phone')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('phone')"
                  @keydown.enter="handleSearchEnter($event, 'phone')"
                  @keydown.esc="exitSearchMode('phone')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('phone')">
                <span class="header-text">联系方式</span>
              </div>
              <!-- 动态按钮 -->
              <button v-if="searchValues.phone && searchValues.phone.trim()" @click="exitSearchMode('phone')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.phone.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('phone')" :disabled="sortButtonStates.phone.disabled" title="排序联系方式">
                <span class="sort-indicator" :class="getSortClass('phone')">
                  {{ getSortIcon('phone') }}
                </span>
              </button>
            </div>
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.rating }" style="flex: 1;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.rating" class="header-search-container" @mouseleave="handleSearchMouseLeave('rating')">
                <input
                  :ref="el => searchInputRefs.rating = el"
                  type="text"
                  placeholder="🔍 搜索客户等级..."
                  v-model="searchValues.rating"
                  @input="handleSearchInput('rating')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('rating')"
                  @keydown.enter="handleSearchEnter($event, 'rating')"
                  @keydown.esc="exitSearchMode('rating')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('rating')">
                <span class="header-text">客户等级</span>
              </div>
              <!-- 动态按钮 -->
              <button v-if="searchValues.rating && searchValues.rating.trim()" @click="exitSearchMode('rating')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.rating.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('rating')" :disabled="sortButtonStates.rating.disabled" title="排序客户等级">
                <span class="sort-indicator" :class="getSortClass('rating')">
                  {{ getSortIcon('rating') }}
                </span>
              </button>
            </div>
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.joinDate }" style="flex: 1.5;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.joinDate" class="header-search-container" @mouseleave="handleSearchMouseLeave('joinDate')">
                <input
                  :ref="el => searchInputRefs.joinDate = el"
                  type="text"
                  placeholder="🔍 搜索积分余额..."
                  v-model="searchValues.joinDate"
                  @input="handleSearchInput('joinDate')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('joinDate')"
                  @keydown.enter="handleSearchEnter($event, 'joinDate')"
                  @keydown.esc="exitSearchMode('joinDate')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('joinDate')">
                <span class="header-text">积分余额</span>
              </div>
              <!-- 动态按钮 -->
              <button v-if="searchValues.joinDate && searchValues.joinDate.trim()" @click="exitSearchMode('joinDate')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.joinDate.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('joinDate')" :disabled="sortButtonStates.joinDate.disabled" title="排序积分余额">
                <span class="sort-indicator" :class="getSortClass('joinDate')">
                  {{ getSortIcon('joinDate') }}
                </span>
              </button>
            </div>
            <div class="header-cell" style="flex: 0.8;">
              <div class="header-normal-container">
                <span class="header-text">状态</span>
              </div>
              <!-- 排序按钮 -->
              <button v-if="sortButtonStates.status && sortButtonStates.status.loading" class="sort-btn loading" disabled title="排序中...">
                <span class="loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('status')" :disabled="sortButtonStates.status && sortButtonStates.status.disabled" title="排序状态">
                <span class="sort-indicator" :class="getSortClass('status')">
                  {{ getSortIcon('status') }}
                </span>
              </button>
            </div>
            <div class="header-cell" style="flex: 1.5;">
              <span>操作</span>
              <button class="header-add-btn-small" @click="showAddModal" title="新增客户">
                <span class="add-icon">➕</span>
                <span class="add-text">新增</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 数据行容器 -->
        <div class="table-body" :style="{ height: (dynamicTableHeight - 60) + 'px' }">
          <!-- 加载状态 -->
          <div v-if="loadingStates.dataLoading" class="table-loading">
            <div class="loading-content">
              <div class="loading-spinner">⏳</div>
              <div class="loading-text">正在加载客户数据...</div>
            </div>
          </div>

          <!-- 空数据状态 -->
          <div v-else-if="paginatedData.length === 0" class="table-empty">
            <div class="empty-content">
              <div class="empty-icon">👤</div>
              <div class="empty-text">暂无客户数据</div>
              <div class="empty-hint">点击"新增"按钮添加第一个客户</div>
            </div>
          </div>

          <!-- 数据行 -->
          <div v-else v-for="(item, index) in paginatedData" :key="item.id" class="data-row">
            <!-- 客户信息 -->
            <div class="data-cell" style="flex: 2.2;">
              <div class="customer-info">
                <div class="customer-avatar">
                  <div class="avatar-placeholder">{{ item.name?.charAt(0) || '客' }}</div>
                </div>
                <div class="customer-details">
                  <div class="customer-name">{{ item.name }}</div>
                  <div class="customer-id">ID: {{ item.id }}</div>
                </div>
              </div>
            </div>

            <!-- 联系方式 -->
            <div class="data-cell" style="flex: 1;">
              <div class="contact-info">
                <div class="phone">{{ item.phone }}</div>
                <div class="email" v-if="item.email">{{ item.email }}</div>
              </div>
            </div>

            <!-- 客户等级 -->
            <div class="data-cell" style="flex: 1;">
              <div class="level-info">
                <div class="level-badge" :class="'level-' + item.level">
                  {{ getLevelText(item.level) }}
                </div>
              </div>
            </div>

            <!-- 积分余额 -->
            <div class="data-cell" style="flex: 1.5;">
              <div class="points-info">
                <div class="points-amount">{{ item.points }} 分</div>
              </div>
            </div>

            <!-- 状态 -->
            <div class="data-cell" style="flex: 0.8;">
              <div class="status-container">
                <span class="status-badge" :class="item.status">
                  {{ getStatusText(item.status) }}
                </span>
              </div>
            </div>

            <!-- 操作 -->
            <div class="data-cell" style="flex: 1.5;">
              <div class="action-buttons">
                <button class="action-btn-small primary" @click="editCustomer(item)">
                  <span class="btn-icon">✏️</span>
                  <span class="btn-text">编辑</span>
                </button>
                <button class="action-btn-small secondary" @click="viewCustomer(item)">
                  <span class="btn-icon">👁️</span>
                  <span class="btn-text">查看</span>
                </button>
                <button class="action-btn-small danger" @click="deleteCustomer(item)">
                  <span class="btn-icon">🗑️</span>
                  <span class="btn-text">删除</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 翻页组件 -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span class="total-info">
            共 <span class="highlight-number">{{ totalRecords }}</span> 条记录
          </span>
        </div>

        <div class="pagination-controls">
          <div class="page-size-selector">
            <label class="page-size-label">每页显示：</label>
            <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
              <option value="10">10条</option>
              <option value="20">20条</option>
              <option value="50">50条</option>
            </select>
          </div>

          <div class="page-navigation" v-if="totalRecords > 0">
            <button
              class="page-btn prev-btn"
              @click="prevPage"
              :disabled="currentPage === 1"
              aria-label="上一页">
              ‹ 上一页
            </button>

            <div class="page-numbers">
              <button
                v-for="page in visiblePages"
                :key="page"
                class="page-btn page-number"
                :class="{ active: page === currentPage }"
                @click="goToPage(page)"
                aria-label="页码">
                {{ page }}
              </button>
            </div>

            <button
              class="page-btn next-btn"
              @click="nextPage"
              :disabled="currentPage === totalPages"
              aria-label="下一页">
              下一页 ›
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import SciFiNotification from '@/components/SciFiNotification.vue';
import { universalMatch, createSearchFilter } from '@/utils/universalSearch.js';

export default {
  name: '客户Management',
  components: {
    SciFiNotification
  },
  setup() {
    // 基础响应式数据
    const notification = ref(null);
    const dynamicTableHeight = ref(600);
    
    // 🎯 搜索功能数据 - 与服务管理页面保持完全一致的字段名
    const searchModes = reactive({
      name: false,
      phone: false,
      rating: false,
      joinDate: false
    });

    const searchValues = reactive({
      name: '',
      phone: '',
      rating: '',
      joinDate: ''
    });

    const searchInputRefs = reactive({
      name: null,
      phone: null,
      rating: null,
      joinDate: null
    });
    
    // 🎯 排序功能数据 - 与服务管理页面保持完全一致的字段名
    const sortButtonStates = reactive({
      name: { loading: false, disabled: false },
      phone: { loading: false, disabled: false },
      rating: { loading: false, disabled: false },
      joinDate: { loading: false, disabled: false }
    });
    
    // 🎯 过渡动画数据 - 与服务管理页面保持完全一致的字段名
    const isTransitioning = reactive({
      name: false,
      phone: false,
      rating: false,
      joinDate: false
    });
    
    // 数据和状态
    const loadingStates = reactive({
      dataLoading: false,
      saving: false
    });

    // customers数据
    const customers = ref([
      {
        id: "C001",
        name: "张小美",
        phone: "13800138001",
        email: "<EMAIL>",
        level: 3,
        points: 1580,
        status: "active"
      },
      {
        id: "C002",
        name: "李先生",
        phone: "13800138002",
        email: "<EMAIL>",
        level: 2,
        points: 890,
        status: "active"
      }
    ]);
    
    // 计算属性
    const filteredData = computed(() => {
      return customers.value;
    });
    
    const paginatedData = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      return filteredData.value.slice(start, end);
    });
    
    const currentPage = ref(1);
    const pageSize = ref(10);
    const totalRecords = computed(() => filteredData.value.length);
    const totalPages = computed(() => Math.ceil(totalRecords.value / pageSize.value));
    
    // 方法定义
    const handleClickToSearch = (field) => {
      searchModes[field] = true;
      nextTick(() => {
        if (searchInputRefs[field]) {
          searchInputRefs[field].focus();
        }
      });
    };
    
    const handleSearchInput = (field) => {
      // 实现搜索逻辑
    };
    
    const exitSearchMode = (field) => {
      searchModes[field] = false;
      searchValues[field] = '';
    };

    const handleSearchBlur = (field) => {
      // 处理搜索框失焦
      setTimeout(() => {
        if (searchValues[field] && searchValues[field].trim()) {
          // 如果有搜索内容，保持搜索模式
        } else {
          // 如果没有搜索内容，退出搜索模式
          exitSearchMode(field);
        }
      }, 200);
    };

    const handleSearchMouseLeave = (field) => {
      // 处理鼠标离开搜索区域
      if (!searchValues[field] || !searchValues[field].trim()) {
        // 如果没有搜索内容，退出搜索模式
        exitSearchMode(field);
      }
    };

    const handleCompositionStart = () => {
      // 处理中文输入开始
    };

    const handleCompositionEnd = () => {
      // 处理中文输入结束
    };

    const handleSearchEnter = (event, field) => {
      // 处理回车搜索
      event.preventDefault();
      exitSearchMode(field);
    };

    const handleSort = (field) => {
      // 实现排序逻辑
    };
    
    const getSortClass = (field) => {
      return '';
    };
    
    const getSortIcon = (field) => {
      return '↕';
    };
    
    const toggleStatusFilter = () => {
      // 实现状态过滤逻辑
    };
    
    const getStatusFilterClass = () => {
      return '';
    };
    
    const getStatusFilterTitle = () => {
      return '状态筛选';
    };
    
    const getStatusFilterIcon = () => {
      return '🔽';
    };
    
    const getStatusFilterLabel = () => {
      return '全部';
    };
    
    const showAddModal = () => {
      // 实现新增模态框逻辑
      console.log('显示新增客户模态框');
    };

    const editCustomer = (customer) => {
      console.log('编辑客户:', customer.name);
    };

    const viewCustomer = (customer) => {
      console.log('查看客户:', customer.name);
    };

    const deleteCustomer = (customer) => {
      if (confirm(`确定要删除客户"${customer.name}"吗？`)) {
        const index = customers.value.findIndex(c => c.id === customer.id);
        if (index > -1) {
          customers.value.splice(index, 1);
          console.log('客户删除成功');
        }
      }
    };

    const getLevelText = (level) => {
      const levelMap = {
        1: '普通会员',
        2: '银卡会员',
        3: '金卡会员',
        4: '钻石会员',
        5: 'VIP会员'
      };
      return levelMap[level] || `${level}级会员`;
    };

    const getStatusText = (status) => {
      const statusMap = {
        active: '活跃',
        inactive: '非活跃',
        blocked: '已冻结'
      };
      return statusMap[status] || status;
    };

    const handlePageSizeChange = () => {
      currentPage.value = 1; // 重置到第一页
    };

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page;
      }
    };

    const prevPage = () => {
      if (currentPage.value > 1) {
        currentPage.value--;
      }
    };

    const nextPage = () => {
      if (currentPage.value < totalPages.value) {
        currentPage.value++;
      }
    };

    const visiblePages = computed(() => {
      const pages = [];
      const start = Math.max(1, currentPage.value - 2);
      const end = Math.min(totalPages.value, currentPage.value + 2);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      return pages;
    });
    
    // 生命周期
    onMounted(() => {
      // 初始化逻辑
    });
    
    return {
      // 数据
      notification,
      dynamicTableHeight,
      searchModes,
      searchValues,
      searchInputRefs,
      sortButtonStates,
      isTransitioning,
      loadingStates,
      customers,
      filteredData,
      paginatedData,
      currentPage,
      pageSize,
      totalRecords,
      totalPages,
      visiblePages,

      // 方法
      handleClickToSearch,
      handleSearchInput,
      exitSearchMode,
      handleSearchBlur,
      handleSearchMouseLeave,
      handleCompositionStart,
      handleCompositionEnd,
      handleSearchEnter,
      handleSort,
      getSortClass,
      getSortIcon,
      toggleStatusFilter,
      getStatusFilterClass,
      getStatusFilterTitle,
      getStatusFilterIcon,
      getStatusFilterLabel,
      showAddModal,
      editCustomer,
      viewCustomer,
      deleteCustomer,
      getLevelText,
      getStatusText,
      handlePageSizeChange,
      goToPage,
      prevPage,
      nextPage
    };
  }
};
</script>

<style scoped>
@import url('@/styles/management-common.css');

/* 🎯 CSS变量定义 - 基于服务管理页面标准 */
:root {
  /* 毛玻璃效果标准 */
  --glass-bg-primary: rgb(255 255 255 / 8%);
  --glass-bg-secondary: rgb(255 255 255 / 5%);
  --glass-bg-input: rgb(255 255 255 / 3%);
  --glass-border: rgb(255 255 255 / 15%);
  --glass-blur-standard: blur(25px) saturate(1.5);
  --glass-blur-heavy: blur(40px) saturate(1.8) brightness(1.2);

  /* Z-index层级系统 */
  --service-z-base: 1;
  --service-z-content: 10;
  --service-z-dropdown: 100;
  --service-z-toolbar: 200;
  --service-z-table-header: 300;
  --service-z-tooltip: 500;
  --service-z-modal-backdrop: 1000;
  --service-z-modal: 1001;
  --service-z-toast: 2000;
  --service-z-debug: 9999;
}

/* 主容器样式 - 与服务管理页面完全一致 */
.picasso-services {
  display: flex;
  position: fixed;
  z-index: var(--service-z-base);
  width: calc(100vw - 180px);
  height: 100vh;
  min-height: 100vh;
  padding: 1.984vw;
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;
  font-size: clamp(12px, 1.2vw, 18px);
  background: transparent;
  transform: scale(var(--scale-factor));
  inset: 0 0 0 180px;
  transform-origin: top left;
  flex-direction: column;
}

/* 数据表格容器 */
.data-cubism {
  display: flex;
  position: relative;
  z-index: var(--service-z-base);
  padding: 0 20px;
  border-radius: 16px;
  overflow: hidden;
  background: transparent;
  flex: 1;
  flex-direction: column;
  margin-top: -10px;
}

/* 表格容器 */
.table-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: calc(100vh - 120px);
  max-height: calc(100vh - 120px);
  min-height: 400px;
  padding: 0 5px 4px;
  border-radius: 12px;
  overflow: hidden;
  background: transparent;
}

/* 智能表头 */
.smart-table-header {
  position: sticky;
  top: 0;
  z-index: var(--service-z-table-header);
  border-radius: 15px 15px 0 0;
  background: rgb(139 92 246 / 15%);
  box-shadow: 0 2px 8px rgb(139 92 246 / 20%);
  backdrop-filter: blur(10px);
  margin-bottom: 2px;
}

/* 表头列 */
.header-columns {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 8px;
}

.header-cell {
  display: flex;
  position: relative;
  padding: 0 8px;
  align-items: center;
  justify-content: space-between;
}

.header-text {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
}

/* 搜索功能样式 */
.header-search-container {
  flex: 1;
  position: relative;
}

.header-search-input {
  width: 100%;
  height: 32px;
  padding: 0 12px;
  border: 1px solid var(--glass-border);
  border-radius: 6px;
  font-size: 0.85rem;
  color: var(--text-primary);
  background: var(--glass-bg-input);
  transition: all 0.2s ease;
  backdrop-filter: var(--glass-blur-standard);
}

.header-search-input:focus {
  outline: none;
  border-color: rgb(139 92 246 / 30%);
  background: rgb(255 255 255 / 10%);
  box-shadow: 0 0 0 2px rgb(139 92 246 / 10%);
}

.header-normal-container {
  flex: 1;
  cursor: pointer;
  padding: 8px 0;
  transition: all 0.2s ease;
}

.header-normal-container:hover {
  color: rgb(139 92 246 / 80%);
}

/* 排序按钮 */
.sort-btn {
  display: flex;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  color: var(--text-primary);
  background: rgb(255 255 255 / 10%);
  transition: all 0.2s ease;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}

.sort-btn:hover {
  background: rgb(139 92 246 / 20%);
  transform: scale(1.1);
}

.sort-indicator {
  font-size: 0.8rem;
}

/* 搜索关闭按钮 */
.search-close-btn {
  display: flex;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  font-size: 1rem;
  font-weight: bold;
  color: #dc2626;
  background: rgb(239 68 68 / 10%);
  transition: all 0.2s ease;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}

.search-close-btn:hover {
  background: rgb(239 68 68 / 20%);
  transform: scale(1.1);
}

/* 状态过滤按钮 */
.status-filter-btn {
  display: flex;
  padding: 4px 8px;
  border: 1px solid var(--glass-border);
  border-radius: 6px;
  font-size: 0.8rem;
  color: var(--text-primary);
  background: var(--glass-bg-secondary);
  transition: all 0.2s ease;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.status-filter-btn:hover {
  background: rgb(139 92 246 / 10%);
  border-color: rgb(139 92 246 / 30%);
}

/* 新增按钮 */
.header-add-btn-small {
  display: flex;
  padding: 6px 10px;
  border: 1px solid rgb(139 92 246 / 20%);
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  color: #4f46e5;
  background: rgb(139 92 246 / 10%);
  transition: all 0.2s ease;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.header-add-btn-small:hover {
  background: rgb(139 92 246 / 20%);
  box-shadow: 0 4px 12px rgb(139 92 246 / 20%);
  transform: translateY(-1px);
}

/* 表格主体 */
.table-body {
  flex: 1;
  overflow-y: auto;
  padding: 0 8px;
}

.data-row {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  border-bottom: 1px solid rgb(255 255 255 / 10%);
  transition: all 0.2s ease;
}

.data-row:hover {
  background: rgb(255 255 255 / 5%);
  backdrop-filter: blur(20px) saturate(1.4);
}

.data-row:last-child {
  border-bottom: none;
}

/* 过渡动画 */
.transitioning {
  transition: all 0.3s ease;
}

/* 加载动画 */
.sort-loading-indicator {
  display: flex;
  gap: 2px;
}

.loading-dot {
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: currentcolor;
  animation: loadingDots 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDots {
  0%, 80%, 100% { opacity: 0.3; }
  40% { opacity: 1; }
}


/* 数据展示样式 */
.data-cell {
  display: flex;
  align-items: center;
  padding: 0 8px;
  font-size: 0.9rem;
}

.cell-content {
  font-weight: 500;
  color: #1f2937;
}

/* 翻页组件样式 */
.pagination-container {
  display: flex !important;
  position: fixed !important;
  right: 50px !important;
  bottom: 20px !important;
  left: 230px !important;
  z-index: var(--service-z-toolbar);
  height: 30px !important;
  margin: 0 !important;
  padding: 4px 12px !important;
  border: 1px solid rgb(139 92 246 / 15%) !important;
  border-radius: 12px !important;
  background: rgb(139 92 246 / 3%) !important;
  box-shadow: 0 4px 16px rgb(139 92 246 / 8%) !important;
  backdrop-filter: blur(20px) !important;
  justify-content: space-between !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  gap: 15px !important;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  font-size: 11px;
  font-weight: 600;
  color: #4f46e5;
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.highlight-number {
  font-size: 12px;
  font-weight: 700;
  color: #7c3aed;
  text-shadow: 1px 1px 2px rgb(0 0 0 / 40%);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-label {
  font-size: 10px;
  font-weight: 500;
  color: #6b7280;
}

.page-size-select {
  padding: 2px 6px;
  border: 1px solid rgb(139 92 246 / 20%);
  border-radius: 4px;
  font-size: 10px;
  color: #4f46e5;
  background: rgb(255 255 255 / 10%);
  cursor: pointer;
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  padding: 4px 8px;
  border: 1px solid rgb(139 92 246 / 20%);
  border-radius: 4px;
  font-size: 10px;
  color: #4f46e5;
  background: rgb(255 255 255 / 10%);
  transition: all 0.2s ease;
  cursor: pointer;
}

.page-btn:hover:not(:disabled) {
  background: rgb(139 92 246 / 10%);
  transform: translateY(-1px);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-btn.active {
  font-weight: 600;
  color: #4f46e5;
  background: rgb(139 92 246 / 20%);
}

.page-numbers {
  display: flex;
  gap: 4px;
}

/* 响应式设计 */
@media (width <= 768px) {
  .picasso-services {
    padding: 16px;
    font-size: 14px;
  }

  .header-columns {
    flex-direction: column;
    gap: 12px;
  }

  .header-cell {
    width: 100%;
    justify-content: space-between;
  }
}
</style>