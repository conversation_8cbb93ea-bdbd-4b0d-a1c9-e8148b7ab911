<template>
  <div class="picasso-customers">
    <!-- 毕加索风格操作栏 -->
    <div class="action-toolbar">
      <div class="search-cubism">
        <input 
          type="text" 
          placeholder="搜索客户姓名或电话..."
          v-model="searchValue"
          @input="handleSearch"
          class="search-fragment"
        />
      </div>
      
      <div class="filter-cubism">
        <select 
          v-model="genderFilter"
          @change="handleFilterChange"
          class="filter-fragment"
        >
          <option value="">所有性别</option>
          <option value="male">男</option>
          <option value="female">女</option>
        </select>

        <select 
          v-model="levelFilter"
          @change="handleFilterChange"
          class="filter-fragment"
        >
          <option value="">所有等级</option>
          <option value="1">普通客户</option>
          <option value="2">银卡客户</option>
          <option value="3">金卡客户</option>
          <option value="4">白金客户</option>
          <option value="5">钻石客户</option>
        </select>
      </div>

      <div class="action-cubism">
        <div class="action-cube refresh-cube" @click="loadCustomers">
          <div class="cube-face">
            <span class="cube-icon">🔄</span>
            <span class="cube-text">刷新</span>
          </div>
        </div>
        <div class="action-cube add-cube" @click="showAddModal">
          <div class="cube-face">
            <span class="cube-icon">➕</span>
            <span class="cube-text">新增客户</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格数据表格 -->
    <div class="data-cubism">
      <div class="table-container">
        <!-- 表头 -->
        <div class="table-header">
          <div class="header-cell" style="flex: 0.5;">ID</div>
          <div class="header-cell" style="flex: 2;">客户信息</div>
          <div class="header-cell" style="flex: 0.8;">性别</div>
          <div class="header-cell" style="flex: 1.2;">等级</div>
          <div class="header-cell" style="flex: 1.5;">健康状况</div>
          <div class="header-cell" style="flex: 1;">就诊次数</div>
          <div class="header-cell" style="flex: 1;">状态</div>
          <div class="header-cell" style="flex: 1.8;">操作</div>
        </div>

        <!-- 数据行 -->
        <div class="table-body">
          <!-- 数据加载状态 -->
          <div v-if="loadingStates.dataLoading" class="table-loading-container">
            <div class="table-loading-content">
              <div class="loading-spinner-large">⏳</div>
              <div class="loading-text">正在加载客户数据...</div>
            </div>
          </div>

          <!-- 无数据状态 -->
          <div v-else-if="paginatedData.length === 0" class="table-empty-container">
            <div class="table-empty-content">
              <div class="empty-icon">👥</div>
              <div class="empty-text">暂无客户数据</div>
            </div>
          </div>

          <!-- 数据内容 -->
          <div v-else
            v-for="record in paginatedData"
            :key="record.id"
            class="data-row"
          >
            <div class="data-cell" style="flex: 0.5;" data-label="ID">
              <div class="cell-content">{{ record.id }}</div>
            </div>

            <div class="data-cell" style="flex: 2;" data-label="客户信息">
              <div class="cell-content">
                <div class="customer-fragment">
                  <div class="customer-avatar">{{ record.name?.charAt(0) }}</div>
                  <div class="customer-info">
                    <div class="customer-name">{{ record.name }}</div>
                    <div class="customer-phone">{{ record.phone }}</div>
                  </div>
                </div>
              </div>
            </div>

            <div class="data-cell" style="flex: 0.8;" data-label="性别">
              <div class="cell-content">
                <div class="gender-fragment" :class="'gender-' + record.gender">
                  <div class="gender-icon">{{ record.gender === 'male' ? '👨' : '👩' }}</div>
                  <div class="gender-text">{{ record.gender === 'male' ? '男' : '女' }}</div>
                </div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1.2;" data-label="等级">
              <div class="cell-content">
                <div class="level-fragment">
                  <div class="level-stars">
                    <span v-for="i in 5" :key="i" class="star" :class="{ active: i <= record.level }">⭐</span>
                  </div>
                  <div class="level-text">{{ getLevelText(record.level) }}</div>
                </div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1.5;" data-label="健康状况">
              <div class="cell-content">
                <div class="health-fragments">
                  <div
                    v-for="condition in record.health_conditions"
                    :key="condition"
                    class="health-tag"
                  >
                    {{ condition }}
                  </div>
                </div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1;" data-label="到访次数">
              <div class="cell-content">
                <div class="visits-fragment">
                  <div class="visits-number">{{ record.total_visits }}</div>
                  <div class="visits-label">次</div>
                </div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1;" data-label="状态">
              <div class="cell-content">
                <div class="status-fragment" :class="'status-' + record.status">
                  <div class="status-indicator"></div>
                  <div class="status-text">{{ getStatusText(record.status) }}</div>
                </div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1.8;">
              <div class="action-fragments">
                <div class="action-btn edit" @click="handleEdit(record)">编辑</div>
                <div class="action-btn view" @click="handleView(record)">查看</div>
                <div class="action-btn toggle" @click="handleToggleStatus(record)">
                  {{ record.status === 'active' ? '禁用' : '启用' }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 梵高风格分页组件 - 符合CI_CD_STANDARDS.md -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span class="total-info">
            共 <span class="highlight-number">{{ totalRecords }}</span> 条记录，
            第 <span class="highlight-number">{{ currentPage }}</span> /
            <span class="highlight-number">{{ totalPages }}</span> 页
          </span>
        </div>

        <div class="pagination-controls">
          <div class="page-size-selector">
            <label class="page-size-label">每页显示：</label>
            <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
              <option value="5">5条</option>
              <option value="10">10条</option>
              <option value="20">20条</option>
              <option value="50">50条</option>
            </select>
          </div>

          <div class="page-navigation" v-if="totalRecords > 0">
            <button
              class="page-btn prev-btn"
              @click="prevPage"
              :disabled="currentPage === 1"
             aria-label="操作按钮">
              ‹ 上一页
            </button>

            <div class="page-numbers">
              <button
                v-for="page in visiblePages"
                :key="page"
                class="page-btn page-number"
                :class="{ active: page === currentPage }"
                @click="goToPage(page)"
               aria-label="操作按钮">
                {{ page }}
              </button>
            </div>

            <button
              class="page-btn next-btn"
              @click="nextPage"
              :disabled="currentPage === totalPages"
             aria-label="操作按钮">
              下一页 ›
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格模态框 -->
    <div v-if="modalVisible" class="modal-overlay" @click="hideModal">
      <div class="modal-cubism" @click.stop>
        <div class="modal-header">
          <div class="header-title">{{ modalTitle }}</div>
          <div class="close-btn" @click="hideModal">×</div>
        </div>
        
        <div class="modal-body">
          <div class="form-cubism">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">客户姓名 <span class="required">*</span></label>
                <input
                  type="text"
                  v-model="formState.name"
                  class="form-input"
                  :class="{ 'error': formErrors.name }"
                  placeholder="请输入客户姓名"
                  required
                  aria-label="输入字段"
                />
                <div v-if="formErrors.name" class="error-message">{{ formErrors.name }}</div>
              </div>
              
              <div class="form-group">
                <label class="form-label">联系电话 <span class="required">*</span></label>
                <input
                  type="tel"
                  v-model="formState.phone"
                  class="form-input"
                  :class="{ 'error': formErrors.phone }"
                  placeholder="请输入联系电话"
                  required
                aria-label="输入字段">
                <div v-if="formErrors.phone" class="error-message">{{ formErrors.phone }}</div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">性别 <span class="required">*</span></label>
                <select
                  v-model="formState.gender"
                  class="form-select"
                  :class="{ 'error': formErrors.gender }"
                  required
                >
                  <option value="">请选择性别</option>
                  <option value="male">男</option>
                  <option value="female">女</option>
                </select>
                <div v-if="formErrors.gender" class="error-message">{{ formErrors.gender }}</div>
              </div>

              <div class="form-group">
                <label class="form-label">年龄 <span class="required">*</span></label>
                <input
                  type="number"
                  v-model="formState.age"
                  class="form-input"
                  :class="{ 'error': formErrors.age }"
                  placeholder="请输入年龄"
                  min="1"
                  max="120"
                  required
                aria-label="输入字段">
                <div v-if="formErrors.age" class="error-message">{{ formErrors.age }}</div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">客户等级</label>
                <select v-model="formState.level" class="form-select">
                  <option value="1">普通客户</option>
                  <option value="2">银卡客户</option>
                  <option value="3">金卡客户</option>
                  <option value="4">白金客户</option>
                  <option value="5">钻石客户</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">紧急联系人</label>
                <input 
                  type="text" 
                  v-model="formState.emergency_contact" 
                  class="form-input"
                  placeholder="紧急联系人姓名"
                aria-label="输入字段">
              </div>
            </div>

            <div class="form-row full-width">
              <div class="form-group">
                <label class="form-label">健康状况</label>
                <textarea 
                  v-model="formState.health_notes" 
                  class="form-textarea"
                  placeholder="请描述客户的健康状况和注意事项..."
                  rows="3"
                ></textarea>
              </div>
            </div>

            <div class="form-row full-width">
              <div class="form-group">
                <label class="form-label">备注信息</label>
                <textarea 
                  v-model="formState.notes" 
                  class="form-textarea"
                  placeholder="其他备注信息..."
                  rows="2"
                ></textarea>
              </div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <div class="footer-actions">
            <div class="action-btn cancel" @click="hideModal">
              <div class="btn-face">取消</div>
            </div>
            <div class="action-btn confirm" @click="handleSubmit">
              <div class="btn-face">{{ confirmLoading ? '提交中...' : '确定' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast通知组件 - 符合CI_CD_STANDARDS.md用户反馈规范 -->
  <div v-if="toastState.visible" class="toast-notification" :class="'toast-' + toastState.type">
    <div class="toast-content">
      <div class="toast-icon">
        <span v-if="toastState.type === 'success'">✅</span>
        <span v-else-if="toastState.type === 'error'">❌</span>
        <span v-else-if="toastState.type === 'warning'">⚠️</span>
      </div>
      <div class="toast-message">{{ toastState.message }}</div>
    </div>
  </div>
</template>

<script setup>
// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

import { ref, reactive, computed, onMounted, nextTick, shallowRef , watchEffect } from 'vue';;

// 响应式数据
const customers = ref([]);
const modalVisible = ref(false);
const modalTitle = ref('新增客户');
const searchValue = ref('');
const genderFilter = ref('');
const levelFilter = ref('');

// 交互加载状态 - 参考服务管理模板
const loadingStates = reactive({
  dataLoading: false,      // 数据加载状态
  customerEdit: false,     // 客户编辑加载状态
  customerSubmit: false,   // 客户提交加载状态
  customerDelete: false,   // 客户删除加载状态
  statusToggle: false      // 状态切换加载状态
});
const currentPage = ref(1);
const pageSize = ref(5); // 默认5条每页，符合开发规范

// 表单状态
const formState = reactive({
  id: null,
  name: '',
  phone: '',
  gender: '',
  age: null,
  level: 1,
  emergency_contact: '',
  health_notes: '',
  notes: '',
  status: 'active'
});

// Toast通知状态 - 符合CI_CD_STANDARDS.md用户反馈规范
const toastState = reactive({
  visible: false,
  message: '',
  type: 'success' // success, error, warning
});

// Toast通知函数 - 替代console日志
const showToast = (message, type = 'success') => {
  toastState.message = message;
  toastState.type = type;
  toastState.visible = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    toastState.visible = false;
  }, 3000);
};

// 表单验证错误状态 - 符合CI_CD_STANDARDS.md表单验证规范
const formErrors = reactive({
  name: '',
  phone: '',
  gender: '',
  age: ''
});

// 表单验证函数 - 符合CI_CD_STANDARDS.md表单验证规范
const validateForm = () => {
  // 清空之前的错误
  Object.assign(formErrors, {
    name: '',
    phone: '',
    gender: '',
    age: ''
  });

  let isValid = true;

  // 验证客户姓名
  if (!formState.name?.trim()) {
    formErrors.name = '请输入客户姓名';
    isValid = false;
  } else if (formState.name.trim().length < 2) {
    formErrors.name = '客户姓名至少2个字符';
    isValid = false;
  }

  // 验证联系电话
  if (!formState.phone?.trim()) {
    formErrors.phone = '请输入联系电话';
    isValid = false;
  } else if (!/^1[3-9]\d{9}$/.test(formState.phone)) {
    formErrors.phone = '请输入正确的手机号码';
    isValid = false;
  }

  // 验证性别
  if (!formState.gender?.trim()) {
    formErrors.gender = '请选择性别';
    isValid = false;
  }

  // 验证年龄
  if (!formState.age || formState.age < 1 || formState.age > 120) {
    formErrors.age = '请输入正确的年龄';
    isValid = false;
  }

  return isValid;
};

// 计算属性
const totalRecords = computed(() => customers.value.length);
const totalPages = computed(() => Math.ceil(customers.value.length / pageSize.value));
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return customers.value.slice(start, end);
});

const visiblePages = computed(() => {
  const pages = [];
  const total = totalPages.value;
  const current = currentPage.value;

  for (let i = Math.max(1, current - 2); i <= Math.min(total, current + 2); i++) {
    pages.push(i);
  }
  return pages;
});

// 方法
const loadCustomers = async () => {
  try {
    console.log('🔍 开始加载客户数据...');

    // 设置加载状态
    loadingStates.dataLoading = true;

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));

    customers.value = [
    {
      id: 1,
      name: '张女士',
      phone: '13800138001',
      gender: 'female',
      age: 35,
      level: 3,
      emergency_contact: '张先生',
      health_conditions: ['颈椎不适', '失眠'],
      total_visits: 15,
      status: 'active',
      created_date: '2024-01-10'
    },
    {
      id: 2,
      name: '李先生',
      phone: '13900139002',
      gender: 'male',
      age: 42,
      level: 2,
      emergency_contact: '李女士',
      health_conditions: ['腰椎疼痛'],
      total_visits: 8,
      status: 'active',
      created_date: '2024-01-12'
    },
    {
      id: 3,
      name: '王女士',
      phone: '13700137003',
      gender: 'female',
      age: 28,
      level: 4,
      emergency_contact: '王先生',
      health_conditions: ['足部疲劳', '肩膀酸痛'],
      total_visits: 25,
      status: 'active',
      created_date: '2024-01-08'
    }
  ];

    console.log('✅ 客户数据加载成功');
  } catch (error) {
    console.error('❌ 加载客户数据失败:', error);
    // 可以在这里添加错误处理逻辑
  } finally {
    // 清除加载状态
    loadingStates.dataLoading = false;
  }
};

const showAddModal = () => {
  modalTitle.value = '新增客户';
  Object.assign(formState, {
    id: null,
    name: '',
    phone: '',
    gender: '',
    age: null,
    level: 1,
    emergency_contact: '',
    health_notes: '',
    notes: '',
    status: 'active'
  });
  modalVisible.value = true;
};

const hideModal = () => {
  modalVisible.value = false;
  confirmLoading.value = false;
};

const handleEdit = (record) => {
  modalTitle.value = '编辑客户';
  Object.assign(formState, {
    ...record,
    health_notes: record.health_conditions?.join(', ') || ''
  });
  modalVisible.value = true;
};

const handleView = (record) => {
  console.log('查看客户:', record);
};

const handleToggleStatus = (record) => {
  record.status = record.status === 'active' ? 'inactive' : 'active';
  console.log('切换状态:', record);
};

const handleSearch = () => {
  console.log('搜索:', searchValue.value);
};

const handleFilterChange = () => {
  console.log('筛选:', { gender: genderFilter.value, level: levelFilter.value });
};

const handleSubmit = async () => {
  try {
    // 表单验证 - 符合CI_CD_STANDARDS.md表单验证规范
    if (!validateForm()) {
      showToast('请检查输入内容', 'error');
      return;
    }

    confirmLoading.value = true;
    await new Promise(resolve => setTimeout(resolve, 1000));

    const healthConditions = formState.health_notes ?
      formState.health_notes.split(',').map(item => item.trim()) : [];

    if (formState.id) {
      const index = customers.value.findIndex(item => item.id === formState.id);
      if (index !== -1) {
        customers.value[index] = {
          ...formState,
          health_conditions: healthConditions
        };
        showToast('客户信息更新成功', 'success');
      }
    } else {
      customers.value.unshift({
        ...formState,
        id: Date.now(),
        health_conditions: healthConditions,
        total_visits: 0,
        created_date: new Date().toISOString().split('T')[0]
      });
      showToast('客户添加成功', 'success');
    }

    hideModal();
  } catch (error) {
    console.error('客户操作失败:', error);
    showToast('操作失败，请重试', 'error');
  } finally {
    confirmLoading.value = false;
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

const handlePageSizeChange = () => {
  currentPage.value = 1; // 重置到第一页
};

const getLevelText = (level) => {
  const texts = {
    1: '普通客户',
    2: '银卡客户',
    3: '金卡客户',
    4: '白金客户',
    5: '钻石客户'
  };
  return texts[level] || '普通客户';
};

const getStatusText = (status) => {
  const texts = {
    active: '正常',
    inactive: '禁用'
  };
  return texts[status] || status;
};

// 初始化
onMounted(() => {
  loadCustomers();
});
</script>

<style scoped>
/* 继承预约管理页面的毕加索风格样式 */
.picasso-customers {
  display: flex;
  position: fixed;
  inset: 0 0 0 180px;
  width: calc(100vw - 180px);
  height: 100vh;
  padding: 30px; /* 统一主内容区边距 */
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;

  /* 设置与全局背景相同的梵高风格紫色渐变，调整背景位置以匹配全局背景 */
  background: linear-gradient(180deg,
    #2d1b69 0%,    /* 深紫色（梵高星夜风格） */
    #3730a3 15%,   /* 靛蓝紫 */
    #4338ca 30%,   /* 中紫色 */
    #5b21b6 45%,   /* 深紫色 */
    #6b21a8 60%,   /* 紫色 */
    #7c2d92 75%,   /* 紫红色 */
    #86198f 90%,   /* 深紫红 */
    #701a75 100%   /* 最深紫色 */
  ) !important;
  background-attachment: fixed; /* 固定背景，避免位移 */
  flex-direction: column;
}

@keyframes picassoFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 0% 50%; }
  100% { background-position: 50% 0%; }
}

/* 继承预约管理页面的完整毕加索风格样式 */

/* 已删除无用的标题样式 */

/* 毕加索风格操作栏 */
.action-toolbar {
  display: flex;
  position: relative;
  z-index: 10;
  gap: 20px;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.search-cubism {
  flex: 1;
}

.search-fragment {
  width: 100%;
  padding: 12px 20px;
  border: none;
  border: 2px solid rgb(255 182 193 / 40%);
  border-radius: 0 25px;
  font-size: 1rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg,
    rgb(255 182 193 / 80%),
    rgb(255 218 185 / 70%),
    rgb(255 255 255 / 90%)
  );
  box-shadow: 3px 3px 10px rgb(255 182 193 / 30%);
  transform: skew(-3deg);
  transition: all 0.3s ease;
}

.search-fragment:focus {
  box-shadow: 5px 5px 15px rgb(65 105 225 / 40%);
  transform: skew(-3deg) scale(1.02);
  outline: none;
}

.filter-cubism {
  display: flex;
  gap: 10px;
}

.filter-fragment {
  padding: 12px 15px;
  border: none;
  border: 2px solid rgb(173 216 230 / 40%);
  border-radius: 0 20px;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg,
    rgb(173 216 230 / 80%),
    rgb(135 206 235 / 70%),
    rgb(255 255 255 / 90%)
  );
  box-shadow: 3px 3px 10px rgb(173 216 230 / 30%);
  transform: skew(-3deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.filter-fragment:focus {
  box-shadow: 5px 5px 15px rgb(65 105 225 / 40%);
  transform: skew(-3deg) scale(1.02);
  outline: none;
}

.action-cubism {
  display: flex;
  gap: 10px;
}

.action-cube {
  width: 120px;
  height: 45px;
  border-radius: 15px 0;
  transform: perspective(600px) rotateX(15deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-cube:hover {
  transform: perspective(600px) rotateX(15deg) scale(1.05);
}

.refresh-cube {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
}

.add-cube {
  background: linear-gradient(45deg, #4ecdc4, #45b7d1);
}

.cube-face {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 15px 0;
  font-weight: bold;
  color: white;
  box-shadow: 0 5px 15px rgb(0 0 0 / 30%);
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.cube-icon {
  font-size: 1.2rem;
}

.cube-text {
  font-size: 0.9rem;
}

/* 毕加索风格数据表格 */
.data-cubism {
  display: flex;
  position: relative;
  z-index: 5;
  padding: 20px;
  border-radius: 20px;
  overflow: hidden;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 20px 40px rgb(32 178 170 / 30%);
  transform: perspective(1000px) rotateX(3deg);
  flex: 1;
  flex-direction: column;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 🎨 表头样式 - 修复对齐问题 */
.table-header {
  display: flex;
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 15px 0;
  border-radius: 15px 15px 0 0;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
  margin-bottom: 10px;

  /* 预留滚动条宽度 */
  padding-right: var(--scrollbar-width, 0);
  flex-shrink: 0;
}

/* 🔧 统一列宽系统 - 修复对齐问题 */
.header-cell, .data-cell {
  display: flex;
  align-items: center;
  padding: 0 12px;

  /* 统一flex属性 */
  flex-shrink: 0;
  flex-grow: 0;
  overflow: hidden;
}

.header-cell {
  font-size: 0.9rem;
  font-weight: 900;
  color: white;
  transform: rotate(1deg);
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 2px 2px 4px rgb(0 0 0 / 30%);
}

/* 客户管理页面具体列宽定义 */
.header-cell:nth-child(1), .data-cell:nth-child(1) {
  flex-basis: 8%;
  min-width: 60px;
}

.header-cell:nth-child(2), .data-cell:nth-child(2) {
  flex-basis: 25%;
  min-width: 180px;
}

.header-cell:nth-child(3), .data-cell:nth-child(3) {
  flex-basis: 10%;
  min-width: 80px;
}

.header-cell:nth-child(4), .data-cell:nth-child(4) {
  flex-basis: 15%;
  min-width: 100px;
}

.header-cell:nth-child(5), .data-cell:nth-child(5) {
  flex-basis: 20%;
  min-width: 120px;
}

.header-cell:nth-child(6), .data-cell:nth-child(6) {
  flex-basis: 12%;
  min-width: 80px;
}

.header-cell:nth-child(7), .data-cell:nth-child(7) {
  flex-basis: 10%;
  min-width: 80px;
}

.table-body {
  max-height: calc(100vh - 320px); /* 减去头部、搜索栏、分页等空间 */

  /* 确保5行数据能够完整显示，避免内容被遮挡 */
  min-height: 350px; /* 5行 × 约70px行高 = 350px */
  flex: 1;
  overflow: hidden auto;

  /* 毕加索风格滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #c8a2c8 rgb(200 162 200 / 20%);
}

.table-body::-webkit-scrollbar {
  width: 8px;
}

.table-body::-webkit-scrollbar-track {
  border-radius: 8px;
  background: linear-gradient(45deg, rgb(200 162 200 / 10%), rgb(32 178 170 / 10%));
}

.table-body::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: linear-gradient(45deg, #c8a2c8, #20b2aa);
  transition: all 0.3s ease;
}

.table-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #9370db, #4169e1);
}

.data-row {
  display: flex;
  height: 50px !important; /* 🎯 固定高度50px，与左侧菜单项高度完全一致 */
  min-height: 50px !important; /* 🎯 调整为50px，与菜单项高度完全一致 */
  border-radius: 10px;
  background: linear-gradient(90deg,
    rgb(200 162 200 / 10%),
    rgb(32 178 170 / 10%),
    rgb(65 105 225 / 10%)
  );
  transition: all 0.3s ease;
  margin-bottom: 8px; /* 🎯 保持8px间距，与菜单项间距一致 */
}

.data-row:hover {
  background: linear-gradient(90deg,
    rgb(200 162 200 / 20%),
    rgb(32 178 170 / 20%),
    rgb(65 105 225 / 20%)
  );
  box-shadow: 0 4px 12px rgb(32 178 170 / 30%);
  transform: scale(1.02);
}

.data-cell {
  display: flex;
  max-height: 50px; /* 🎯 强制限制最大高度为50px */
  padding: 0 10px; /* 🎯 调整内边距，只保留左右内边距以适应50px高度 */
  align-items: center;
  justify-content: center;
}

.cell-content {
  display: flex;
  width: 100%;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  color: #2c3e50;
  align-items: center;
  justify-content: center;
}

/* 毕加索风格客户信息 */
.customer-fragment {
  display: flex;
  align-items: center;
  gap: 12px;
}

.customer-avatar {
  display: flex;
  width: 40px;
  height: 40px;
  border-radius: 50% 20%;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  background: linear-gradient(45deg, #c8a2c8, #dda0dd);
  box-shadow: 0 4px 12px rgb(200 162 200 / 40%);
  transform: rotate(12deg);
  align-items: center;
  justify-content: center;
  animation: avatarFloat 4s ease-in-out infinite;
}

@keyframes avatarFloat {
  0%, 100% { transform: rotate(12deg) translateY(0); }
  50% { transform: rotate(12deg) translateY(-3px); }
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 1rem;
  font-weight: bold;
  color: #2c3e50;
  transform: skew(-2deg);
  margin-bottom: 3px;
}

.customer-phone {
  font-size: 0.8rem;
  color: #7f8c8d;
  transform: skew(1deg);
}

/* 毕加索风格性别信息 */
.gender-fragment {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 10px;
  border-radius: 12px 0;
  transform: skew(-1deg);
}

.gender-male {
  border: 2px solid rgb(52 152 219 / 30%);
  background: linear-gradient(135deg, rgb(52 152 219 / 20%), rgb(255 255 255 / 80%));
}

.gender-female {
  border: 2px solid rgb(231 76 60 / 30%);
  background: linear-gradient(135deg, rgb(231 76 60 / 20%), rgb(255 255 255 / 80%));
}

.gender-icon {
  font-size: 1.1rem;
}

.gender-text {
  font-size: 0.9rem;
  font-weight: bold;
  color: #2c3e50;
}

/* 毕加索风格等级信息 */
.level-fragment {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 4px;
}

.level-stars {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 0.8rem;
  opacity: 0.3;
  transition: all 0.3s ease;
}

.star.active {
  opacity: 1;
  animation: starTwinkle 2s ease-in-out infinite;
}

@keyframes starTwinkle {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.level-text {
  font-size: 0.8rem;
  font-weight: bold;
  color: #8e44ad;
  transform: skew(-1deg);
}

/* 毕加索风格健康状况 */
.health-fragments {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
}

.health-tag {
  padding: 3px 8px;
  border: 1px solid rgb(39 174 96 / 30%);
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: bold;
  color: #27ae60;
  background: linear-gradient(135deg, rgb(39 174 96 / 20%), rgb(255 255 255 / 80%));
  transform: skew(-1deg);
}

/* 毕加索风格就诊次数 */
.visits-fragment {
  display: flex;
  padding: 8px;
  border-radius: 12px 0;
  background: linear-gradient(135deg, rgb(142 68 173 / 20%), rgb(255 255 255 / 80%));
  transform: skew(-1deg);
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.visits-number {
  font-size: 1.2rem;
  font-weight: bold;
  color: #8e44ad;
}

.visits-label {
  font-size: 0.7rem;
  color: #7f8c8d;
}

/* 毕加索风格状态 */
.status-fragment {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 12px 0;
  transform: skew(-1deg);
}

.status-active {
  border: 2px solid rgb(39 174 96 / 30%);
  background: linear-gradient(135deg, rgb(39 174 96 / 20%), rgb(46 204 113 / 10%));
}

.status-inactive {
  border: 2px solid rgb(149 165 166 / 30%);
  background: linear-gradient(135deg, rgb(149 165 166 / 20%), rgb(189 195 199 / 10%));
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-active .status-indicator {
  background: #27ae60;
}

.status-inactive .status-indicator {
  background: #95a5a6;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

.status-text {
  font-size: 0.8rem;
  font-weight: bold;
}

.status-active .status-text {
  color: #27ae60;
}

.status-inactive .status-text {
  color: #95a5a6;
}

/* 毕加索风格操作按钮 */
.action-fragments {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: center;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 8px 0;
  font-size: 0.8rem;
  font-weight: bold;
  color: white;
  transform: perspective(200px) rotateX(8deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-btn:hover {
  transform: perspective(200px) rotateX(8deg) scale(1.05);
}

.action-btn.edit {
  background: linear-gradient(45deg, #c8a2c8, #dda0dd);
}

.action-btn.view {
  background: linear-gradient(45deg, #4169e1, #6495ed);
}

.action-btn.toggle {
  background: linear-gradient(45deg, #f39c12, #e67e22);
}

/* 🎨 梵高风格分页组件 - 符合CI_CD_STANDARDS.md */
.pagination-container {
  display: flex;
  padding: 12px 18px;
  border: 2px solid rgb(192 132 252 / 40%);
  border-radius: 12px;
  background: linear-gradient(135deg, rgb(139 92 246 / 10%), rgb(168 85 247 / 10%));
  box-shadow:
    0 4px 8px rgb(139 92 246 / 20%),
    0 2px 4px rgb(192 132 252 / 10%);
  margin-top: 15px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  font-size: 14px;
  font-weight: 600;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.highlight-number {
  font-size: 16px;
  font-weight: 700;
  color: var(--van-gogh-accent);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 40%);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-label {
  font-size: 13px;
  font-weight: 600;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.page-size-select {
  padding: 6px 12px;
  border: 2px solid var(--van-gogh-accent);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-primary-dark), var(--van-gogh-secondary));
  box-shadow: 0 2px 4px rgb(139 92 246 / 30%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.page-size-select:hover {
  border-color: var(--van-gogh-primary);
  box-shadow: 0 4px 8px rgb(139 92 246 / 40%);
}

.page-size-select option {
  padding: 8px;
  color: white;
  background: var(--van-gogh-primary-dark);
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  padding: 8px 12px;
  border: 2px solid var(--van-gogh-primary);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-secondary), var(--van-gogh-secondary-dark));
  box-shadow: 0 2px 4px rgb(139 92 246 / 30%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.page-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--van-gogh-primary), var(--van-gogh-primary-dark));
  box-shadow: 0 4px 8px rgb(139 92 246 / 40%);
  transform: translateY(-1px);
}

.page-btn:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  border-color: #d1d5db;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.page-btn.active {
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-accent), var(--van-gogh-accent-dark));
  box-shadow:
    0 4px 8px rgb(244 114 182 / 40%),
    inset 1px 1px 2px rgb(255 255 255 / 30%);
  border-color: var(--van-gogh-accent-light);
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  min-width: 36px;
  text-align: center;
}

/* 毕加索风格模态框 */
.modal-overlay {
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background: rgb(65 105 225 / 70%);
  align-items: center;
  justify-content: center;
}

.modal-cubism {
  width: 85%;
  max-width: min(700px, calc(100vw - 220px));
  max-height: 80vh;
  border-radius: 20px 5px;
  overflow: hidden;
  background: linear-gradient(135deg, #c8a2c8, #20b2aa, #4169e1, #dda0dd);
  box-shadow: 0 25px 50px rgb(32 178 170 / 40%);
  transform: perspective(800px) rotateY(3deg);
  background-size: 400% 400%;
  animation: modalFlow 8s ease infinite;
}

@keyframes modalFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: rgb(255 255 255 / 95%);
  border-bottom: 3px solid #c8a2c8;
}

.header-title {
  font-size: 1.4rem;
  font-weight: 900;
  color: #2c3e50;
  text-transform: uppercase;
  letter-spacing: 1px;
  transform: skew(-2deg);
}

.close-btn {
  display: flex;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  background: linear-gradient(45deg, #c8a2c8, #20b2aa);
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-btn:hover {
  background: linear-gradient(45deg, #4169e1, #6495ed);
  transform: rotate(90deg) scale(1.1);
}

.modal-body {
  max-height: 60vh;
  padding: 25px;
  background: rgb(255 255 255 / 95%);
  overflow-y: auto;

  /* 模态框滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #c8a2c8 rgb(200 162 200 / 20%);
}

.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  border-radius: 6px;
  background: rgb(200 162 200 / 10%);
}

.modal-body::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: linear-gradient(45deg, #c8a2c8, #20b2aa);
}

/* 毕加索风格表单 */
.form-cubism {
  transform: perspective(600px) rotateX(2deg);
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-row.full-width .form-group {
  flex: 1;
}

.form-group {
  flex: 1;
  position: relative;
}

.form-label {
  display: block;
  width: fit-content;
  padding: 4px 10px;
  border-radius: 8px 0;
  font-size: 0.9rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(200 162 200 / 80%), rgb(255 255 255 / 90%));
  transform: skew(-3deg);
  margin-bottom: 8px;
}

.form-input, .form-select, .form-textarea {
  width: 100%;
  height: 50px;                            /* 🎯 与菜单高度一致：50px */
  padding: 0 15px;                         /* 🎯 调整内边距适应新高度 */
  border: none;
  border-radius: 0 12px;
  font-size: 1rem;
  font-weight: 600;
  line-height: 50px;                       /* 🎯 与菜单行高一致 */
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(200 162 200 / 80%), rgb(255 255 255 / 90%));
  box-shadow: 3px 3px 10px rgb(0 0 0 / 10%);
  transform: skew(-1deg);
  transition: all 0.3s ease;
}

.form-textarea {
  height: auto !important;                 /* 文本域保持自适应高度 */
  min-height: 50px !important;             /* 最小高度与菜单一致 */
  padding: 12px 15px !important;           /* 文本域保持原有内边距 */
  line-height: 1.5 !important;             /* 文本域使用正常行高 */
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  background: linear-gradient(135deg, rgb(200 162 200 / 90%), rgb(255 255 255 / 95%));
  box-shadow: 5px 5px 15px rgb(65 105 225 / 30%);
  transform: skew(-1deg) scale(1.02);
  outline: none;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  padding: 20px 25px;
  background: rgb(255 255 255 / 95%);
  border-top: 3px solid #20b2aa;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.footer-actions .action-btn {
  padding: 12px 25px;
  border-radius: 20px 5px;
  font-weight: bold;
  transform: perspective(400px) rotateX(5deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.footer-actions .action-btn.cancel {
  color: white;
  background: linear-gradient(45deg, #95a5a6, #bdc3c7);
}

.footer-actions .action-btn.confirm {
  color: white;
  background: linear-gradient(45deg, #20b2aa, #4169e1);
}

.footer-actions .action-btn:hover {
  transform: perspective(400px) rotateX(5deg) scale(1.05);
}

.btn-face {
  font-size: 1rem;
}

/* 响应式设计 */
@media (width <= 768px) {
  .picasso-customers {
    left: 0;
    width: 100vw;
    padding: 15px;
  }

  .action-toolbar {
    flex-direction: column;
    gap: 15px;
  }

  .filter-cubism {
    flex-direction: column;
  }

  .action-cubism {
    justify-content: center;
  }

  .data-row {
    flex-direction: column;
    min-height: auto;
  }

  .data-cell {
    border-bottom: 1px solid rgb(200 162 200 / 20%);
    min-height: 50px;                      /* 🎯 与菜单高度一致：50px */
  }

  .modal-cubism {
    width: 95%;
    margin: 10px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .header-cell, .cell-content {
    font-size: 0.8rem;
  }

  .title-layer {
    font-size: 2rem;
  }

  .subtitle-fragment {
    font-size: 1rem;
  }

  .action-fragments {
    justify-content: center;
  }
}

/* 🔧 内容溢出处理 - 参考服务管理模板 */
.cell-content {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行文本处理 */
.cell-content.multiline {
  display: -webkit-box;
  max-height: 2.8em;
  line-height: 1.4;
  white-space: normal;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 长单词处理 */
.cell-content.breakable {
  word-break: break-all;
  word-wrap: break-word;
}

/* 📱 响应式设计 - 符合CI_CD_STANDARDS.md */

/* 平板适配 */
@media (width <= 768px) {
  .table-container {
    overflow-x: auto;
  }

  .table-header, .data-row {
    min-width: 600px;
  }

  .header-cell, .data-cell {
    padding: 0 8px;
  }
}

/* 手机适配 */
@media (width <= 480px) {
  .table-header {
    display: none;
  }

  .data-row {
    min-width: auto;
    padding: 12px;
    border-radius: 8px;
    background: rgb(255 255 255 / 10%);
    flex-direction: column;
    margin-bottom: 12px;
  }

  .data-cell {
    min-width: auto;
    padding: 4px 0;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid rgb(255 255 255 / 10%);
    flex-basis: auto;
  }

  .data-cell:last-child {
    border-bottom: none;
  }

  .data-cell::before {
    content: attr(data-label);
    font-weight: bold;
    color: var(--van-gogh-text-primary);
  }
}

/* 🎨 表单验证样式 - 符合CI_CD_STANDARDS.md表单验证规范 */
.form-input.error,
.form-select.error,
.form-textarea.error {
  border-color: #ef4444;
  box-shadow:
    0 0 0 3px rgb(239 68 68 / 20%),
    0 4px 12px rgb(239 68 68 / 20%);
}

.required {
  font-weight: bold;
  color: #ef4444;
}

.error-message {
  font-size: 0.85rem;
  font-weight: 500;
  color: #ef4444;
  margin-top: 4px;
}

/* 🎨 Toast通知组件样式 - 符合CI_CD_STANDARDS.md用户反馈规范 */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  max-width: 500px;
  min-width: 300px;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow:
    0 8px 32px rgb(0 0 0 / 20%),
    0 4px 16px rgb(0 0 0 / 10%);
  backdrop-filter: blur(10px);
  animation: slideInRight 0.3s ease-out;
}

.toast-success {
  border: 2px solid rgb(34 197 94 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(34 197 94 / 90%), rgb(22 163 74 / 90%));
}

.toast-error {
  border: 2px solid rgb(239 68 68 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(239 68 68 / 90%), rgb(220 38 38 / 90%));
}

.toast-warning {
  border: 2px solid rgb(245 158 11 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(245 158 11 / 90%), rgb(217 119 6 / 90%));
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toast-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.toast-message {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
