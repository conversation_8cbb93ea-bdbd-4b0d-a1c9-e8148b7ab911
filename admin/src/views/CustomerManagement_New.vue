<template>
  <div class="customer-management-container">
    <!-- 🎯 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">客户管理</h1>
          <p class="page-subtitle">管理客户信息、等级积分和消费记录</p>
        </div>
        <div class="header-right">
          <div class="header-stats">
            <div class="stat-item">
              <span class="stat-value">{{ totalCustomers }}</span>
              <span class="stat-label">总客户数</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ vipCustomers }}</span>
              <span class="stat-label">VIP客户</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ monthlyNewCustomers }}</span>
              <span class="stat-label">本月新增</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎯 操作工具栏 -->
    <div class="toolbar-container">
      <div class="toolbar-left">
        <div class="search-container">
          <input 
            type="text" 
            placeholder="搜索客户姓名或电话..."
            v-model="searchValue"
            @input="handleSearch"
            class="search-input"
          />
          <div class="search-icon">🔍</div>
        </div>
        
        <div class="filter-container">
          <select
            v-model="genderFilter"
            @change="handleFilterChange"
            class="filter-select"
          >
            <option value="">全部性别</option>
            <option value="male">男</option>
            <option value="female">女</option>
          </select>
        </div>
        
        <div class="filter-container">
          <select
            v-model="levelFilter"
            @change="handleFilterChange"
            class="filter-select"
          >
            <option value="">全部等级</option>
            <option value="1">普通客户</option>
            <option value="2">银卡客户</option>
            <option value="3">金卡客户</option>
            <option value="4">白金客户</option>
            <option value="5">钻石客户</option>
          </select>
        </div>
      </div>

      <div class="toolbar-right">
        <button class="action-btn secondary" @click="loadCustomers">
          <span class="btn-icon">🔄</span>
          <span class="btn-text">刷新</span>
        </button>
        <button class="action-btn primary" @click="showAddModal">
          <span class="btn-icon">➕</span>
          <span class="btn-text">新增客户</span>
        </button>
      </div>
    </div>

    <!-- 🎯 数据表格 -->
    <div class="table-container">
      <div class="smart-table">
        <!-- 表格头部 -->
        <div class="smart-table-header">
          <div class="header-cell" style="flex: 1.5;">
            <span class="header-text">客户信息</span>
          </div>
          <div class="header-cell" style="flex: 1.2;">
            <span class="header-text">联系方式</span>
          </div>
          <div class="header-cell" style="flex: 1;">
            <span class="header-text">客户等级</span>
          </div>
          <div class="header-cell" style="flex: 1.2;">
            <span class="header-text">积分余额</span>
          </div>
          <div class="header-cell" style="flex: 1.2;">
            <span class="header-text">消费统计</span>
          </div>
          <div class="header-cell" style="flex: 1.2;">
            <span class="header-text">注册时间</span>
          </div>
          <div class="header-cell" style="flex: 1.5;">
            <span class="header-text">操作</span>
          </div>
        </div>

        <!-- 表格主体 -->
        <div class="smart-table-body">
          <!-- 加载状态 -->
          <div v-if="loadingStates.dataLoading" class="table-loading">
            <div class="loading-content">
              <div class="loading-spinner">⏳</div>
              <div class="loading-text">正在加载客户数据...</div>
            </div>
          </div>

          <!-- 空数据状态 -->
          <div v-else-if="paginatedData.length === 0" class="table-empty">
            <div class="empty-content">
              <div class="empty-icon">👥</div>
              <div class="empty-text">暂无客户数据</div>
              <div class="empty-hint">点击"新增客户"按钮添加第一个客户</div>
            </div>
          </div>

          <!-- 数据行 -->
          <div v-else
            v-for="record in paginatedData"
            :key="record.id"
            class="table-row"
          >
            <!-- 客户信息 -->
            <div class="table-cell" style="flex: 1.5;">
              <div class="customer-info">
                <div class="customer-avatar">
                  <img v-if="record.avatar" :src="record.avatar" :alt="record.name" />
                  <div v-else class="avatar-placeholder">{{ record.name?.charAt(0) || '客' }}</div>
                </div>
                <div class="customer-details">
                  <div class="customer-name">{{ record.name }}</div>
                  <div class="customer-id">ID: {{ record.id }}</div>
                  <div class="customer-gender">{{ getGenderText(record.gender) }}</div>
                </div>
              </div>
            </div>

            <!-- 联系方式 -->
            <div class="table-cell" style="flex: 1.2;">
              <div class="contact-info">
                <div class="phone">{{ record.phone }}</div>
                <div class="email" v-if="record.email">{{ record.email }}</div>
              </div>
            </div>

            <!-- 客户等级 -->
            <div class="table-cell" style="flex: 1;">
              <div class="level-container">
                <span class="level-badge" :class="'level-' + record.level">
                  {{ getLevelText(record.level) }}
                </span>
              </div>
            </div>

            <!-- 积分余额 -->
            <div class="table-cell" style="flex: 1.2;">
              <div class="points-info">
                <div class="points-balance">{{ record.points || 0 }} 积分</div>
                <div class="points-trend" v-if="record.pointsChange">
                  <span :class="record.pointsChange > 0 ? 'trend-up' : 'trend-down'">
                    {{ record.pointsChange > 0 ? '+' : '' }}{{ record.pointsChange }}
                  </span>
                </div>
              </div>
            </div>

            <!-- 消费统计 -->
            <div class="table-cell" style="flex: 1.2;">
              <div class="consumption-info">
                <div class="total-amount">¥{{ record.totalAmount || 0 }}</div>
                <div class="visit-count">{{ record.visitCount || 0 }}次消费</div>
              </div>
            </div>

            <!-- 注册时间 -->
            <div class="table-cell" style="flex: 1.2;">
              <div class="date-info">
                <div class="register-date">{{ formatDate(record.registerDate) }}</div>
                <div class="member-duration">会员 {{ calculateMemberDuration(record.registerDate) }}</div>
              </div>
            </div>

            <!-- 操作 -->
            <div class="table-cell" style="flex: 1.5;">
              <div class="action-buttons">
                <button class="action-btn-small primary" @click="editCustomer(record)">
                  <span class="btn-icon">✏️</span>
                  <span class="btn-text">编辑</span>
                </button>
                <button class="action-btn-small secondary" @click="viewCustomer(record)">
                  <span class="btn-icon">👁️</span>
                  <span class="btn-text">查看</span>
                </button>
                <button class="action-btn-small warning" @click="managePoints(record)">
                  <span class="btn-icon">💎</span>
                  <span class="btn-text">积分</span>
                </button>
                <button class="action-btn-small danger" @click="deleteCustomer(record)">
                  <span class="btn-icon">🗑️</span>
                  <span class="btn-text">删除</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎯 分页组件 -->
    <div class="pagination-container">
      <div class="pagination-info">
        <span>共 {{ totalRecords }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页</span>
      </div>
      <div class="pagination-controls">
        <button class="page-btn" @click="goToPage(1)" :disabled="currentPage === 1">首页</button>
        <button class="page-btn" @click="goToPage(currentPage - 1)" :disabled="currentPage === 1">上一页</button>
        <div class="page-numbers">
          <button v-for="page in visiblePages" 
                  :key="page" 
                  class="page-btn" 
                  :class="{ active: page === currentPage }"
                  @click="goToPage(page)">
            {{ page }}
          </button>
        </div>
        <button class="page-btn" @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages">下一页</button>
        <button class="page-btn" @click="goToPage(totalPages)" :disabled="currentPage === totalPages">末页</button>
      </div>
    </div>

    <!-- 🎯 新增/编辑客户模态框 -->
    <div v-if="showModal" class="modal-overlay" @click="closeModal">
      <div class="customer-form-modal" @click.stop>
        <div class="form-header">
          <h3 class="form-title">{{ isEditing ? '编辑客户' : '新增客户' }}</h3>
          <button class="close-btn" @click="closeModal">✕</button>
        </div>
        
        <div class="form-content">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">客户姓名 <span class="required">*</span></label>
              <input type="text" v-model="formData.name" class="form-input" placeholder="请输入客户姓名" />
            </div>
            <div class="form-group">
              <label class="form-label">手机号码 <span class="required">*</span></label>
              <input type="tel" v-model="formData.phone" class="form-input" placeholder="请输入手机号码" />
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">性别</label>
              <select v-model="formData.gender" class="form-select">
                <option value="male">男</option>
                <option value="female">女</option>
              </select>
            </div>
            <div class="form-group">
              <label class="form-label">客户等级</label>
              <select v-model="formData.level" class="form-select">
                <option value="1">普通客户</option>
                <option value="2">银卡客户</option>
                <option value="3">金卡客户</option>
                <option value="4">白金客户</option>
                <option value="5">钻石客户</option>
              </select>
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">邮箱地址</label>
              <input type="email" v-model="formData.email" class="form-input" placeholder="请输入邮箱地址" />
            </div>
            <div class="form-group">
              <label class="form-label">初始积分</label>
              <input type="number" v-model="formData.points" class="form-input" placeholder="0" min="0" />
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">备注信息</label>
            <textarea v-model="formData.notes" class="form-textarea" rows="3" placeholder="请输入备注信息"></textarea>
          </div>
        </div>
        
        <div class="form-actions">
          <button class="action-btn secondary" @click="closeModal">取消</button>
          <button class="action-btn primary" @click="saveCustomer">{{ isEditing ? '更新' : '保存' }}</button>
        </div>
      </div>
    </div>

    <!-- 🎯 积分管理模态框 -->
    <div v-if="showPointsModal" class="modal-overlay" @click="closePointsModal">
      <div class="points-modal" @click.stop>
        <div class="form-header">
          <h3 class="form-title">积分管理 - {{ currentCustomer?.name }}</h3>
          <button class="close-btn" @click="closePointsModal">✕</button>
        </div>
        
        <div class="form-content">
          <div class="current-points">
            <div class="points-display">
              <span class="points-label">当前积分：</span>
              <span class="points-value">{{ currentCustomer?.points || 0 }}</span>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">操作类型</label>
            <select v-model="pointsOperation.type" class="form-select">
              <option value="add">增加积分</option>
              <option value="subtract">扣减积分</option>
              <option value="set">设置积分</option>
            </select>
          </div>
          
          <div class="form-group">
            <label class="form-label">积分数量</label>
            <input type="number" v-model="pointsOperation.amount" class="form-input" placeholder="请输入积分数量" min="0" />
          </div>
          
          <div class="form-group">
            <label class="form-label">操作原因</label>
            <textarea v-model="pointsOperation.reason" class="form-textarea" rows="2" placeholder="请输入操作原因"></textarea>
          </div>
        </div>
        
        <div class="form-actions">
          <button class="action-btn secondary" @click="closePointsModal">取消</button>
          <button class="action-btn primary" @click="savePointsOperation">确认操作</button>
        </div>
      </div>
    </div>

    <!-- 🎯 通知组件 -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      <div class="notification-content">
        <span class="notification-icon">{{ getNotificationIcon(notification.type) }}</span>
        <span class="notification-message">{{ notification.message }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';

export default {
  name: 'CustomerManagement',
  setup() {
    // 🎯 响应式数据
    const searchValue = ref('');
    const genderFilter = ref('');
    const levelFilter = ref('');
    const showModal = ref(false);
    const showPointsModal = ref(false);
    const isEditing = ref(false);
    const currentPage = ref(1);
    const pageSize = ref(10);
    const currentCustomer = ref(null);

    // 🎯 加载状态
    const loadingStates = reactive({
      dataLoading: false,
      saving: false
    });

    // 🎯 客户数据
    const customers = ref([
      {
        id: 'C001',
        name: '张小美',
        phone: '13800138001',
        email: '<EMAIL>',
        gender: 'female',
        level: 3,
        points: 1580,
        pointsChange: 50,
        totalAmount: 3200,
        visitCount: 12,
        registerDate: '2023-01-15',
        avatar: '',
        notes: '常客，喜欢按摩服务'
      },
      {
        id: 'C002',
        name: '李先生',
        phone: '13800138002',
        email: '<EMAIL>',
        gender: 'male',
        level: 2,
        points: 890,
        pointsChange: -20,
        totalAmount: 1800,
        visitCount: 8,
        registerDate: '2023-03-20',
        avatar: '',
        notes: '偏好足疗项目'
      },
      {
        id: 'C003',
        name: '王女士',
        phone: '13800138003',
        email: '<EMAIL>',
        gender: 'female',
        level: 5,
        points: 3200,
        pointsChange: 100,
        totalAmount: 8500,
        visitCount: 25,
        registerDate: '2022-08-10',
        avatar: '',
        notes: 'VIP客户，服务要求较高'
      },
      {
        id: 'C004',
        name: '陈小姐',
        phone: '13800138004',
        email: '<EMAIL>',
        gender: 'female',
        level: 1,
        points: 120,
        pointsChange: 0,
        totalAmount: 280,
        visitCount: 2,
        registerDate: '2024-01-05',
        avatar: '',
        notes: '新客户'
      }
    ]);

    // 🎯 表单数据
    const formData = reactive({
      id: '',
      name: '',
      phone: '',
      email: '',
      gender: 'female',
      level: 1,
      points: 0,
      totalAmount: 0,
      visitCount: 0,
      registerDate: new Date().toISOString().split('T')[0],
      avatar: '',
      notes: ''
    });

    // 🎯 积分操作数据
    const pointsOperation = reactive({
      type: 'add',
      amount: 0,
      reason: ''
    });

    // 🎯 通知系统
    const notification = reactive({
      show: false,
      type: 'success',
      message: ''
    });

    // 🎯 计算属性
    const filteredData = computed(() => {
      let filtered = customers.value;

      // 搜索过滤
      if (searchValue.value) {
        const search = searchValue.value.toLowerCase();
        filtered = filtered.filter(item =>
          item.name.toLowerCase().includes(search) ||
          item.phone.includes(search)
        );
      }

      // 性别过滤
      if (genderFilter.value) {
        filtered = filtered.filter(item => item.gender === genderFilter.value);
      }

      // 等级过滤
      if (levelFilter.value) {
        filtered = filtered.filter(item => item.level === parseInt(levelFilter.value));
      }

      return filtered;
    });

    const paginatedData = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      return filteredData.value.slice(start, end);
    });

    const totalRecords = computed(() => filteredData.value.length);
    const totalPages = computed(() => Math.ceil(totalRecords.value / pageSize.value));
    const totalCustomers = computed(() => customers.value.length);
    const vipCustomers = computed(() => customers.value.filter(c => c.level >= 4).length);
    const monthlyNewCustomers = computed(() => {
      const thisMonth = new Date().toISOString().slice(0, 7);
      return customers.value.filter(c => c.registerDate.startsWith(thisMonth)).length;
    });

    const visiblePages = computed(() => {
      const pages = [];
      const start = Math.max(1, currentPage.value - 2);
      const end = Math.min(totalPages.value, currentPage.value + 2);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      return pages;
    });

    // 🎯 方法定义
    const handleSearch = () => {
      currentPage.value = 1;
    };

    const handleFilterChange = () => {
      currentPage.value = 1;
    };

    const loadCustomers = async () => {
      loadingStates.dataLoading = true;
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        showNotification('success', '客户数据刷新成功');
      } catch (error) {
        showNotification('error', '刷新失败，请重试');
      } finally {
        loadingStates.dataLoading = false;
      }
    };

    const showAddModal = () => {
      isEditing.value = false;
      resetFormData();
      showModal.value = true;
    };

    const editCustomer = (customer) => {
      isEditing.value = true;
      Object.assign(formData, customer);
      showModal.value = true;
    };

    const viewCustomer = (customer) => {
      // 实现查看客户详情
      showNotification('info', `查看客户：${customer.name}`);
    };

    const managePoints = (customer) => {
      currentCustomer.value = customer;
      pointsOperation.type = 'add';
      pointsOperation.amount = 0;
      pointsOperation.reason = '';
      showPointsModal.value = true;
    };

    const deleteCustomer = (customer) => {
      if (confirm(`确定要删除客户"${customer.name}"吗？`)) {
        const index = customers.value.findIndex(c => c.id === customer.id);
        if (index > -1) {
          customers.value.splice(index, 1);
          showNotification('success', '客户删除成功');
        }
      }
    };

    const saveCustomer = async () => {
      if (!formData.name || !formData.phone) {
        showNotification('error', '请填写必填字段');
        return;
      }

      loadingStates.saving = true;
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (isEditing.value) {
          const index = customers.value.findIndex(c => c.id === formData.id);
          if (index > -1) {
            customers.value[index] = { ...formData };
          }
          showNotification('success', '客户信息更新成功');
        } else {
          const newCustomer = {
            ...formData,
            id: 'C' + String(Date.now()).slice(-3).padStart(3, '0')
          };
          customers.value.push(newCustomer);
          showNotification('success', '客户添加成功');
        }

        closeModal();
      } catch (error) {
        showNotification('error', '保存失败，请重试');
      } finally {
        loadingStates.saving = false;
      }
    };

    const savePointsOperation = async () => {
      if (!pointsOperation.amount || pointsOperation.amount <= 0) {
        showNotification('error', '请输入有效的积分数量');
        return;
      }

      try {
        const customer = currentCustomer.value;
        const index = customers.value.findIndex(c => c.id === customer.id);

        if (index > -1) {
          let newPoints = customer.points;

          switch (pointsOperation.type) {
            case 'add':
              newPoints += parseInt(pointsOperation.amount);
              break;
            case 'subtract':
              newPoints = Math.max(0, newPoints - parseInt(pointsOperation.amount));
              break;
            case 'set':
              newPoints = parseInt(pointsOperation.amount);
              break;
          }

          customers.value[index].points = newPoints;
          customers.value[index].pointsChange = newPoints - customer.points;

          showNotification('success', '积分操作成功');
          closePointsModal();
        }
      } catch (error) {
        showNotification('error', '积分操作失败，请重试');
      }
    };

    const closeModal = () => {
      showModal.value = false;
      resetFormData();
    };

    const closePointsModal = () => {
      showPointsModal.value = false;
      currentCustomer.value = null;
    };

    const resetFormData = () => {
      Object.assign(formData, {
        id: '',
        name: '',
        phone: '',
        email: '',
        gender: 'female',
        level: 1,
        points: 0,
        totalAmount: 0,
        visitCount: 0,
        registerDate: new Date().toISOString().split('T')[0],
        avatar: '',
        notes: ''
      });
    };

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page;
      }
    };

    const formatDate = (dateString) => {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN');
    };

    const calculateMemberDuration = (registerDate) => {
      if (!registerDate) return '-';
      const start = new Date(registerDate);
      const now = new Date();
      const diffTime = Math.abs(now - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      const months = Math.floor(diffDays / 30);
      return months > 0 ? `${months}个月` : `${diffDays}天`;
    };

    const getGenderText = (gender) => {
      return gender === 'male' ? '男' : '女';
    };

    const getLevelText = (level) => {
      const levelMap = {
        1: '普通客户',
        2: '银卡客户',
        3: '金卡客户',
        4: '白金客户',
        5: '钻石客户'
      };
      return levelMap[level] || '普通客户';
    };

    const showNotification = (type, message) => {
      notification.type = type;
      notification.message = message;
      notification.show = true;

      setTimeout(() => {
        notification.show = false;
      }, 3000);
    };

    const getNotificationIcon = (type) => {
      const iconMap = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
      };
      return iconMap[type] || 'ℹ️';
    };

    // 🎯 生命周期
    onMounted(() => {
      loadCustomers();
    });

    return {
      // 数据
      searchValue,
      genderFilter,
      levelFilter,
      showModal,
      showPointsModal,
      isEditing,
      currentPage,
      pageSize,
      currentCustomer,
      loadingStates,
      customers,
      formData,
      pointsOperation,
      notification,

      // 计算属性
      filteredData,
      paginatedData,
      totalRecords,
      totalPages,
      totalCustomers,
      vipCustomers,
      monthlyNewCustomers,
      visiblePages,

      // 方法
      handleSearch,
      handleFilterChange,
      loadCustomers,
      showAddModal,
      editCustomer,
      viewCustomer,
      managePoints,
      deleteCustomer,
      saveCustomer,
      savePointsOperation,
      closeModal,
      closePointsModal,
      goToPage,
      formatDate,
      calculateMemberDuration,
      getGenderText,
      getLevelText,
      showNotification,
      getNotificationIcon
    };
  }
};
</script>

<style scoped>
/* 🎯 CSS变量定义 - 基于服务管理页面标准 */
:root {
  /* Z-index层级系统 */
  --customer-z-base: 1;
  --customer-z-content: 10;
  --customer-z-dropdown: 100;
  --customer-z-toolbar: 200;
  --customer-z-table-header: 300;
  --customer-z-tooltip: 500;
  --customer-z-modal-backdrop: 1000;
  --customer-z-modal: 1001;
  --customer-z-toast: 2000;

  /* 毛玻璃效果标准 */
  --glass-bg-primary: rgba(255, 255, 255, 0.08);
  --glass-bg-secondary: rgba(255, 255, 255, 0.05);
  --glass-bg-input: rgba(255, 255, 255, 0.03);
  --glass-border: rgba(255, 255, 255, 0.15);
  --glass-blur-standard: blur(25px) saturate(1.5);
  --glass-blur-heavy: blur(40px) saturate(1.8) brightness(1.2);

  /* 品牌色彩 */
  --brand-primary: rgba(139, 92, 246, 0.15);
  --brand-secondary: rgba(168, 85, 247, 0.12);
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
}

/* 🎯 主容器 */
.customer-management-container {
  padding: 24px;
  min-height: 100vh;
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.05) 0%,
    rgba(168, 85, 247, 0.03) 100%
  );
}

/* 🎯 页面头部 */
.page-header {
  margin-bottom: 24px;
  padding: 24px;
  background: var(--glass-bg-primary);
  border-radius: 16px;
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-blur-standard);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.page-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  text-align: center;
  padding: 12px 16px;
  background: var(--glass-bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(15px) saturate(1.3);
  min-width: 80px;
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--brand-primary);
  margin-bottom: 4px;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* 🎯 工具栏 */
.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 20px;
  background: var(--glass-bg-primary);
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-blur-standard);
}

.toolbar-left {
  display: flex;
  gap: 16px;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 🎯 搜索框 */
.search-container {
  position: relative;
  width: 300px;
}

.search-input {
  width: 100%;
  height: 40px;
  padding: 0 40px 0 16px;
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  background: var(--glass-bg-input);
  backdrop-filter: blur(20px) saturate(1.3);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: rgba(139, 92, 246, 0.3);
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
}

.search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
}

/* 🎯 过滤器 */
.filter-container {
  width: 130px;
}

.filter-select {
  width: 100%;
  height: 40px;
  padding: 0 12px;
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  background: var(--glass-bg-input);
  backdrop-filter: blur(20px) saturate(1.3);
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: rgba(139, 92, 246, 0.3);
  background: rgba(255, 255, 255, 0.1);
}

/* 🎯 操作按钮 */
.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(15px) saturate(1.3);
}

.action-btn.primary {
  background: var(--brand-primary);
  color: #4f46e5;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.action-btn.primary:hover {
  background: rgba(139, 92, 246, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
}

.action-btn.secondary {
  background: var(--glass-bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-icon {
  font-size: 1rem;
}

.btn-text {
  font-size: 0.9rem;
}

/* 🎯 数据表格 */
.table-container {
  background: var(--glass-bg-primary);
  border-radius: 16px;
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-blur-standard);
  overflow: hidden;
  margin-bottom: 24px;
}

.smart-table {
  width: 100%;
}

.smart-table-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: var(--brand-primary);
  border-bottom: 1px solid var(--glass-border);
  backdrop-filter: blur(15px) saturate(1.3);
  position: sticky;
  top: 0;
  z-index: var(--customer-z-table-header);
}

.header-cell {
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.header-text {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
}

.smart-table-body {
  max-height: 600px;
  overflow-y: auto;
}

/* 🎯 表格行 */
.table-row {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
}

.table-row:hover {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px) saturate(1.4);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  display: flex;
  align-items: center;
  padding: 0 8px;
  font-size: 0.9rem;
}

/* 🎯 客户信息 */
.customer-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.customer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background: var(--brand-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.customer-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  color: #4f46e5;
  font-weight: 600;
  font-size: 1rem;
}

.customer-details {
  flex: 1;
}

.customer-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.customer-id {
  font-size: 0.8rem;
  color: var(--text-secondary);
  margin-bottom: 2px;
}

.customer-gender {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* 🎯 联系信息 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.phone {
  font-weight: 500;
  color: var(--text-primary);
}

.email {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 🎯 客户等级标签 */
.level-container {
  display: flex;
  justify-content: center;
}

.level-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  backdrop-filter: blur(10px) saturate(1.2);
}

.level-badge.level-1 {
  background: rgba(107, 114, 128, 0.1);
  color: #4b5563;
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.level-badge.level-2 {
  background: rgba(156, 163, 175, 0.1);
  color: #6b7280;
  border: 1px solid rgba(156, 163, 175, 0.2);
}

.level-badge.level-3 {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.level-badge.level-4 {
  background: rgba(168, 85, 247, 0.1);
  color: #7c3aed;
  border: 1px solid rgba(168, 85, 247, 0.2);
}

.level-badge.level-5 {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* 🎯 积分信息 */
.points-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.points-balance {
  font-weight: 600;
  color: var(--text-primary);
}

.points-trend {
  font-size: 0.8rem;
}

.trend-up {
  color: #059669;
}

.trend-down {
  color: #dc2626;
}

/* 🎯 消费信息 */
.consumption-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.total-amount {
  font-weight: 600;
  color: var(--text-primary);
}

.visit-count {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 🎯 日期信息 */
.date-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.register-date {
  font-weight: 500;
  color: var(--text-primary);
}

.member-duration {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 🎯 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
}

.action-btn-small {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 5px 8px;
  border: none;
  border-radius: 6px;
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px) saturate(1.2);
}

.action-btn-small.primary {
  background: var(--brand-primary);
  color: #4f46e5;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.action-btn-small.primary:hover {
  background: rgba(139, 92, 246, 0.2);
  transform: translateY(-1px);
}

.action-btn-small.secondary {
  background: var(--glass-bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
}

.action-btn-small.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.action-btn-small.warning {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.action-btn-small.warning:hover {
  background: rgba(245, 158, 11, 0.15);
  transform: translateY(-1px);
}

.action-btn-small.danger {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.action-btn-small.danger:hover {
  background: rgba(239, 68, 68, 0.15);
  transform: translateY(-1px);
}

/* 🎯 加载和空状态 */
.table-loading,
.table-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
}

.loading-content,
.empty-content {
  text-align: center;
}

.loading-spinner {
  font-size: 2rem;
  margin-bottom: 12px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1rem;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.empty-hint {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* 🎯 分页组件 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--glass-bg-primary);
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-blur-standard);
  margin-bottom: 24px;
}

.pagination-info {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid var(--glass-border);
  border-radius: 6px;
  background: var(--glass-bg-secondary);
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px) saturate(1.2);
}

.page-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.page-btn.active {
  background: var(--brand-primary);
  color: #4f46e5;
  border-color: rgba(139, 92, 246, 0.2);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

/* 🎯 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--customer-z-modal-backdrop);
  padding: 20px;
}

.customer-form-modal,
.points-modal {
  width: min(90vw, 600px);
  max-height: calc(100vh - 40px);
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(248, 250, 252, 0.12) 50%,
    rgba(255, 255, 255, 0.08) 100%
  );
  border-radius: 16px;
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-blur-heavy);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  z-index: var(--customer-z-modal);
  overflow: hidden;
}

.points-modal {
  width: min(90vw, 500px);
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--brand-primary);
  border-bottom: 1px solid var(--glass-border);
  backdrop-filter: blur(15px) saturate(1.3);
}

.form-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.form-content {
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  flex: 1;
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 6px;
}

.required {
  color: #dc2626;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  background: var(--glass-bg-input);
  backdrop-filter: blur(15px) saturate(1.2);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: rgba(139, 92, 246, 0.3);
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.03);
  border-top: 1px solid var(--glass-border);
  backdrop-filter: blur(20px) saturate(1.3);
}

/* 🎯 积分管理特殊样式 */
.current-points {
  margin-bottom: 20px;
  padding: 16px;
  background: var(--glass-bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(15px) saturate(1.3);
}

.points-display {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 1.1rem;
}

.points-label {
  color: var(--text-secondary);
}

.points-value {
  font-weight: 700;
  color: var(--brand-primary);
  font-size: 1.3rem;
}

/* 🎯 通知组件 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 8px;
  backdrop-filter: blur(20px) saturate(1.4);
  z-index: var(--customer-z-toast);
  animation: slideIn 0.3s ease;
}

.notification.success {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
  color: #059669;
}

.notification.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: #dc2626;
}

.notification.warning {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
  color: #d97706;
}

.notification.info {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  color: #2563eb;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-icon {
  font-size: 1rem;
}

.notification-message {
  font-size: 0.9rem;
  font-weight: 500;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 🎯 响应式设计 */
@media (max-width: 768px) {
  .customer-management-container {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .toolbar-container {
    flex-direction: column;
    gap: 16px;
  }

  .toolbar-left {
    width: 100%;
    justify-content: center;
    flex-wrap: wrap;
  }

  .search-container {
    width: 100%;
    max-width: 300px;
  }

  .filter-container {
    width: 120px;
  }

  .header-stats {
    flex-direction: column;
    gap: 12px;
  }

  .form-row {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }

  .table-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
    padding: 12px 16px;
  }

  .table-cell {
    width: 100%;
    justify-content: space-between;
    padding: 4px 0;
  }

  .table-cell::before {
    content: attr(data-label);
    font-weight: 600;
    color: var(--text-secondary);
    font-size: 0.8rem;
  }
}
</style>
