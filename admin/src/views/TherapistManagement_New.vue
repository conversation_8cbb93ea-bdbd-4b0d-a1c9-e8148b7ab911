<template>
  <div class="therapist-management-container">
    <!-- 🎯 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">技师管理</h1>
          <p class="page-subtitle">管理技师信息、绩效评价和工作状态</p>
        </div>
        <div class="header-right">
          <div class="header-stats">
            <div class="stat-item">
              <span class="stat-value">{{ totalTherapists }}</span>
              <span class="stat-label">总技师数</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ activeTherapists }}</span>
              <span class="stat-label">在职技师</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎯 操作工具栏 -->
    <div class="toolbar-container">
      <div class="toolbar-left">
        <div class="search-container">
          <input 
            type="text" 
            placeholder="搜索技师姓名或电话..."
            v-model="searchValue"
            @input="handleSearch"
            class="search-input"
          />
          <div class="search-icon">🔍</div>
        </div>
        
        <div class="filter-container">
          <select
            v-model="statusFilter"
            @change="handleFilterChange"
            class="filter-select"
          >
            <option value="">全部状态</option>
            <option value="active">在职</option>
            <option value="vacation">休假</option>
            <option value="inactive">离职</option>
          </select>
        </div>
      </div>

      <div class="toolbar-right">
        <button class="action-btn secondary" @click="loadTherapists">
          <span class="btn-icon">🔄</span>
          <span class="btn-text">刷新</span>
        </button>
        <button class="action-btn primary" @click="showAddModal">
          <span class="btn-icon">➕</span>
          <span class="btn-text">新增技师</span>
        </button>
      </div>
    </div>

    <!-- 🎯 数据表格 -->
    <div class="table-container">
      <div class="smart-table">
        <!-- 表格头部 -->
        <div class="smart-table-header">
          <div class="header-cell" style="flex: 1.5;">
            <span class="header-text">技师信息</span>
          </div>
          <div class="header-cell" style="flex: 1.5;">
            <span class="header-text">联系方式</span>
          </div>
          <div class="header-cell" style="flex: 2;">
            <span class="header-text">绩效评价</span>
          </div>
          <div class="header-cell" style="flex: 1.5;">
            <span class="header-text">入职时间</span>
          </div>
          <div class="header-cell" style="flex: 1;">
            <span class="header-text">状态</span>
          </div>
          <div class="header-cell" style="flex: 1.8;">
            <span class="header-text">操作</span>
          </div>
        </div>

        <!-- 表格主体 -->
        <div class="smart-table-body">
          <!-- 加载状态 -->
          <div v-if="loadingStates.dataLoading" class="table-loading">
            <div class="loading-content">
              <div class="loading-spinner">⏳</div>
              <div class="loading-text">正在加载技师数据...</div>
            </div>
          </div>

          <!-- 空数据状态 -->
          <div v-else-if="paginatedData.length === 0" class="table-empty">
            <div class="empty-content">
              <div class="empty-icon">👨‍⚕️</div>
              <div class="empty-text">暂无技师数据</div>
              <div class="empty-hint">点击"新增技师"按钮添加第一个技师</div>
            </div>
          </div>

          <!-- 数据行 -->
          <div v-else
            v-for="record in paginatedData"
            :key="record.id"
            class="table-row"
          >
            <!-- 技师信息 -->
            <div class="table-cell" style="flex: 1.5;">
              <div class="therapist-info">
                <div class="therapist-avatar">
                  <img v-if="record.avatar" :src="record.avatar" :alt="record.name" />
                  <div v-else class="avatar-placeholder">{{ record.name?.charAt(0) || '技' }}</div>
                </div>
                <div class="therapist-details">
                  <div class="therapist-name">{{ record.name }}</div>
                  <div class="therapist-id">ID: {{ record.id }}</div>
                </div>
              </div>
            </div>

            <!-- 联系方式 -->
            <div class="table-cell" style="flex: 1.5;">
              <div class="contact-info">
                <div class="phone">{{ record.phone }}</div>
                <div class="email" v-if="record.email">{{ record.email }}</div>
              </div>
            </div>

            <!-- 绩效评价 -->
            <div class="table-cell" style="flex: 2;">
              <div class="performance-info">
                <div class="rating-stars">
                  <span v-for="i in 5" :key="i" 
                        class="star" 
                        :class="{ active: i <= record.rating }">⭐</span>
                </div>
                <div class="rating-text">{{ record.rating }}/5 分</div>
                <div class="performance-stats">
                  <span class="stat">本月服务: {{ record.monthlyServices || 0 }}次</span>
                </div>
              </div>
            </div>

            <!-- 入职时间 -->
            <div class="table-cell" style="flex: 1.5;">
              <div class="date-info">
                <div class="join-date">{{ formatDate(record.joinDate) }}</div>
                <div class="work-duration">工作 {{ calculateWorkDuration(record.joinDate) }}</div>
              </div>
            </div>

            <!-- 状态 -->
            <div class="table-cell" style="flex: 1;">
              <div class="status-container">
                <span class="status-badge" :class="record.status">
                  {{ getStatusText(record.status) }}
                </span>
              </div>
            </div>

            <!-- 操作 -->
            <div class="table-cell" style="flex: 1.8;">
              <div class="action-buttons">
                <button class="action-btn-small primary" @click="editTherapist(record)">
                  <span class="btn-icon">✏️</span>
                  <span class="btn-text">编辑</span>
                </button>
                <button class="action-btn-small secondary" @click="viewTherapist(record)">
                  <span class="btn-icon">👁️</span>
                  <span class="btn-text">查看</span>
                </button>
                <button class="action-btn-small danger" @click="deleteTherapist(record)">
                  <span class="btn-icon">🗑️</span>
                  <span class="btn-text">删除</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎯 分页组件 -->
    <div class="pagination-container">
      <div class="pagination-info">
        <span>共 {{ totalRecords }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页</span>
      </div>
      <div class="pagination-controls">
        <button class="page-btn" @click="goToPage(1)" :disabled="currentPage === 1">首页</button>
        <button class="page-btn" @click="goToPage(currentPage - 1)" :disabled="currentPage === 1">上一页</button>
        <div class="page-numbers">
          <button v-for="page in visiblePages" 
                  :key="page" 
                  class="page-btn" 
                  :class="{ active: page === currentPage }"
                  @click="goToPage(page)">
            {{ page }}
          </button>
        </div>
        <button class="page-btn" @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages">下一页</button>
        <button class="page-btn" @click="goToPage(totalPages)" :disabled="currentPage === totalPages">末页</button>
      </div>
    </div>

    <!-- 🎯 新增/编辑技师模态框 -->
    <div v-if="showModal" class="modal-overlay" @click="closeModal">
      <div class="therapist-form-modal" @click.stop>
        <div class="form-header">
          <h3 class="form-title">{{ isEditing ? '编辑技师' : '新增技师' }}</h3>
          <button class="close-btn" @click="closeModal">✕</button>
        </div>
        
        <div class="form-content">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">技师姓名 <span class="required">*</span></label>
              <input type="text" v-model="formData.name" class="form-input" placeholder="请输入技师姓名" />
            </div>
            <div class="form-group">
              <label class="form-label">手机号码 <span class="required">*</span></label>
              <input type="tel" v-model="formData.phone" class="form-input" placeholder="请输入手机号码" />
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">邮箱地址</label>
              <input type="email" v-model="formData.email" class="form-input" placeholder="请输入邮箱地址" />
            </div>
            <div class="form-group">
              <label class="form-label">工作状态</label>
              <select v-model="formData.status" class="form-select">
                <option value="active">在职</option>
                <option value="vacation">休假</option>
                <option value="inactive">离职</option>
              </select>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">技师简介</label>
            <textarea v-model="formData.description" class="form-textarea" rows="3" placeholder="请输入技师简介"></textarea>
          </div>
        </div>
        
        <div class="form-actions">
          <button class="action-btn secondary" @click="closeModal">取消</button>
          <button class="action-btn primary" @click="saveTherapist">{{ isEditing ? '更新' : '保存' }}</button>
        </div>
      </div>
    </div>

    <!-- 🎯 通知组件 -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      <div class="notification-content">
        <span class="notification-icon">{{ getNotificationIcon(notification.type) }}</span>
        <span class="notification-message">{{ notification.message }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';

export default {
  name: 'TherapistManagement',
  setup() {
    // 🎯 响应式数据
    const searchValue = ref('');
    const statusFilter = ref('');
    const showModal = ref(false);
    const isEditing = ref(false);
    const currentPage = ref(1);
    const pageSize = ref(10);

    // 🎯 加载状态
    const loadingStates = reactive({
      dataLoading: false,
      saving: false
    });

    // 🎯 技师数据
    const therapists = ref([
      {
        id: 'T001',
        name: '张美丽',
        phone: '13800138001',
        email: '<EMAIL>',
        status: 'active',
        rating: 5,
        joinDate: '2023-01-15',
        monthlyServices: 45,
        avatar: '',
        description: '资深按摩技师，擅长中式按摩和足疗'
      },
      {
        id: 'T002',
        name: '李小芳',
        phone: '13800138002',
        email: '<EMAIL>',
        status: 'active',
        rating: 4,
        joinDate: '2023-03-20',
        monthlyServices: 38,
        avatar: '',
        description: '专业美容师，精通面部护理和身体护理'
      },
      {
        id: 'T003',
        name: '王师傅',
        phone: '13800138003',
        email: '<EMAIL>',
        status: 'vacation',
        rating: 5,
        joinDate: '2022-08-10',
        monthlyServices: 0,
        avatar: '',
        description: '中医推拿专家，有20年从业经验'
      }
    ]);

    // 🎯 表单数据
    const formData = reactive({
      id: '',
      name: '',
      phone: '',
      email: '',
      status: 'active',
      description: '',
      rating: 5,
      joinDate: new Date().toISOString().split('T')[0],
      monthlyServices: 0,
      avatar: ''
    });

    // 🎯 通知系统
    const notification = reactive({
      show: false,
      type: 'success',
      message: ''
    });

    // 🎯 计算属性
    const filteredData = computed(() => {
      let filtered = therapists.value;

      // 搜索过滤
      if (searchValue.value) {
        const search = searchValue.value.toLowerCase();
        filtered = filtered.filter(item =>
          item.name.toLowerCase().includes(search) ||
          item.phone.includes(search)
        );
      }

      // 状态过滤
      if (statusFilter.value) {
        filtered = filtered.filter(item => item.status === statusFilter.value);
      }

      return filtered;
    });

    const paginatedData = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      return filteredData.value.slice(start, end);
    });

    const totalRecords = computed(() => filteredData.value.length);
    const totalPages = computed(() => Math.ceil(totalRecords.value / pageSize.value));
    const totalTherapists = computed(() => therapists.value.length);
    const activeTherapists = computed(() => therapists.value.filter(t => t.status === 'active').length);

    const visiblePages = computed(() => {
      const pages = [];
      const start = Math.max(1, currentPage.value - 2);
      const end = Math.min(totalPages.value, currentPage.value + 2);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      return pages;
    });

    // 🎯 方法定义
    const handleSearch = () => {
      currentPage.value = 1;
    };

    const handleFilterChange = () => {
      currentPage.value = 1;
    };

    const loadTherapists = async () => {
      loadingStates.dataLoading = true;
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        showNotification('success', '技师数据刷新成功');
      } catch (error) {
        showNotification('error', '刷新失败，请重试');
      } finally {
        loadingStates.dataLoading = false;
      }
    };

    const showAddModal = () => {
      isEditing.value = false;
      resetFormData();
      showModal.value = true;
    };

    const editTherapist = (therapist) => {
      isEditing.value = true;
      Object.assign(formData, therapist);
      showModal.value = true;
    };

    const viewTherapist = (therapist) => {
      // 实现查看技师详情
      showNotification('info', `查看技师：${therapist.name}`);
    };

    const deleteTherapist = (therapist) => {
      if (confirm(`确定要删除技师"${therapist.name}"吗？`)) {
        const index = therapists.value.findIndex(t => t.id === therapist.id);
        if (index > -1) {
          therapists.value.splice(index, 1);
          showNotification('success', '技师删除成功');
        }
      }
    };

    const saveTherapist = async () => {
      if (!formData.name || !formData.phone) {
        showNotification('error', '请填写必填字段');
        return;
      }

      loadingStates.saving = true;
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (isEditing.value) {
          const index = therapists.value.findIndex(t => t.id === formData.id);
          if (index > -1) {
            therapists.value[index] = { ...formData };
          }
          showNotification('success', '技师信息更新成功');
        } else {
          const newTherapist = {
            ...formData,
            id: 'T' + String(Date.now()).slice(-3).padStart(3, '0')
          };
          therapists.value.push(newTherapist);
          showNotification('success', '技师添加成功');
        }

        closeModal();
      } catch (error) {
        showNotification('error', '保存失败，请重试');
      } finally {
        loadingStates.saving = false;
      }
    };

    const closeModal = () => {
      showModal.value = false;
      resetFormData();
    };

    const resetFormData = () => {
      Object.assign(formData, {
        id: '',
        name: '',
        phone: '',
        email: '',
        status: 'active',
        description: '',
        rating: 5,
        joinDate: new Date().toISOString().split('T')[0],
        monthlyServices: 0,
        avatar: ''
      });
    };

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page;
      }
    };

    const formatDate = (dateString) => {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN');
    };

    const calculateWorkDuration = (joinDate) => {
      if (!joinDate) return '-';
      const start = new Date(joinDate);
      const now = new Date();
      const diffTime = Math.abs(now - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      const months = Math.floor(diffDays / 30);
      return months > 0 ? `${months}个月` : `${diffDays}天`;
    };

    const getStatusText = (status) => {
      const statusMap = {
        active: '在职',
        vacation: '休假',
        inactive: '离职'
      };
      return statusMap[status] || status;
    };

    const showNotification = (type, message) => {
      notification.type = type;
      notification.message = message;
      notification.show = true;

      setTimeout(() => {
        notification.show = false;
      }, 3000);
    };

    const getNotificationIcon = (type) => {
      const iconMap = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
      };
      return iconMap[type] || 'ℹ️';
    };

    // 🎯 生命周期
    onMounted(() => {
      loadTherapists();
    });

    return {
      // 数据
      searchValue,
      statusFilter,
      showModal,
      isEditing,
      currentPage,
      pageSize,
      loadingStates,
      therapists,
      formData,
      notification,

      // 计算属性
      filteredData,
      paginatedData,
      totalRecords,
      totalPages,
      totalTherapists,
      activeTherapists,
      visiblePages,

      // 方法
      handleSearch,
      handleFilterChange,
      loadTherapists,
      showAddModal,
      editTherapist,
      viewTherapist,
      deleteTherapist,
      saveTherapist,
      closeModal,
      goToPage,
      formatDate,
      calculateWorkDuration,
      getStatusText,
      showNotification,
      getNotificationIcon
    };
  }
};
</script>

<style scoped>
/* 🎯 CSS变量定义 - 基于服务管理页面标准 */
:root {
  /* Z-index层级系统 */
  --therapist-z-base: 1;
  --therapist-z-content: 10;
  --therapist-z-dropdown: 100;
  --therapist-z-toolbar: 200;
  --therapist-z-table-header: 300;
  --therapist-z-tooltip: 500;
  --therapist-z-modal-backdrop: 1000;
  --therapist-z-modal: 1001;
  --therapist-z-toast: 2000;

  /* 毛玻璃效果标准 */
  --glass-bg-primary: rgba(255, 255, 255, 0.08);
  --glass-bg-secondary: rgba(255, 255, 255, 0.05);
  --glass-bg-input: rgba(255, 255, 255, 0.03);
  --glass-border: rgba(255, 255, 255, 0.15);
  --glass-blur-standard: blur(25px) saturate(1.5);
  --glass-blur-heavy: blur(40px) saturate(1.8) brightness(1.2);

  /* 品牌色彩 */
  --brand-primary: rgba(139, 92, 246, 0.15);
  --brand-secondary: rgba(168, 85, 247, 0.12);
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
}

/* 🎯 主容器 */
.therapist-management-container {
  padding: 24px;
  min-height: 100vh;
  background: linear-gradient(135deg,
    rgba(139, 92, 246, 0.05) 0%,
    rgba(168, 85, 247, 0.03) 100%
  );
}

/* 🎯 页面头部 */
.page-header {
  margin-bottom: 24px;
  padding: 24px;
  background: var(--glass-bg-primary);
  border-radius: 16px;
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-blur-standard);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  margin: 0 0 8px 0;
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  -webkit-background-clip: text;
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.page-subtitle {
  font-size: 1rem;
  color: var(--text-secondary);
  margin: 0;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  text-align: center;
  padding: 12px 16px;
  background: var(--glass-bg-secondary);
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  backdrop-filter: blur(15px) saturate(1.3);
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--brand-primary);
  margin-bottom: 4px;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* 🎯 工具栏 */
.toolbar-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 16px 20px;
  background: var(--glass-bg-primary);
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-blur-standard);
}

.toolbar-left {
  display: flex;
  gap: 16px;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 🎯 搜索框 */
.search-container {
  position: relative;
  width: 300px;
}

.search-input {
  width: 100%;
  height: 40px;
  padding: 0 40px 0 16px;
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  background: var(--glass-bg-input);
  backdrop-filter: blur(20px) saturate(1.3);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: rgba(139, 92, 246, 0.3);
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
}

.search-icon {
  position: absolute;
  right: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--text-secondary);
  pointer-events: none;
}

/* 🎯 过滤器 */
.filter-container {
  width: 150px;
}

.filter-select {
  width: 100%;
  height: 40px;
  padding: 0 12px;
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  background: var(--glass-bg-input);
  backdrop-filter: blur(20px) saturate(1.3);
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.filter-select:focus {
  outline: none;
  border-color: rgba(139, 92, 246, 0.3);
  background: rgba(255, 255, 255, 0.1);
}

/* 🎯 操作按钮 */
.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(15px) saturate(1.3);
}

.action-btn.primary {
  background: var(--brand-primary);
  color: #4f46e5;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.action-btn.primary:hover {
  background: rgba(139, 92, 246, 0.2);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.2);
}

.action-btn.secondary {
  background: var(--glass-bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.btn-icon {
  font-size: 1rem;
}

.btn-text {
  font-size: 0.9rem;
}

/* 🎯 数据表格 */
.table-container {
  background: var(--glass-bg-primary);
  border-radius: 16px;
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-blur-standard);
  overflow: hidden;
  margin-bottom: 24px;
}

.smart-table {
  width: 100%;
}

.smart-table-header {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  background: var(--brand-primary);
  border-bottom: 1px solid var(--glass-border);
  backdrop-filter: blur(15px) saturate(1.3);
  position: sticky;
  top: 0;
  z-index: var(--therapist-z-table-header);
}

.header-cell {
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.header-text {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
}

.smart-table-body {
  max-height: 600px;
  overflow-y: auto;
}

/* 🎯 表格行 */
.table-row {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  transition: all 0.2s ease;
}

.table-row:hover {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(20px) saturate(1.4);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  display: flex;
  align-items: center;
  padding: 0 8px;
  font-size: 0.9rem;
}

/* 🎯 技师信息 */
.therapist-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.therapist-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background: var(--brand-primary);
  display: flex;
  align-items: center;
  justify-content: center;
}

.therapist-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  color: #4f46e5;
  font-weight: 600;
  font-size: 1rem;
}

.therapist-details {
  flex: 1;
}

.therapist-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.therapist-id {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 🎯 联系信息 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.phone {
  font-weight: 500;
  color: var(--text-primary);
}

.email {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 🎯 绩效信息 */
.performance-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 0.8rem;
  opacity: 0.3;
  transition: opacity 0.2s ease;
}

.star.active {
  opacity: 1;
}

.rating-text {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--text-primary);
}

.performance-stats {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* 🎯 日期信息 */
.date-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.join-date {
  font-weight: 500;
  color: var(--text-primary);
}

.work-duration {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 🎯 状态标签 */
.status-container {
  display: flex;
  justify-content: center;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  backdrop-filter: blur(10px) saturate(1.2);
}

.status-badge.active {
  background: rgba(34, 197, 94, 0.1);
  color: #059669;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.status-badge.vacation {
  background: rgba(245, 158, 11, 0.1);
  color: #d97706;
  border: 1px solid rgba(245, 158, 11, 0.2);
}

.status-badge.inactive {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

/* 🎯 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn-small {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px) saturate(1.2);
}

.action-btn-small.primary {
  background: var(--brand-primary);
  color: #4f46e5;
  border: 1px solid rgba(139, 92, 246, 0.2);
}

.action-btn-small.primary:hover {
  background: rgba(139, 92, 246, 0.2);
  transform: translateY(-1px);
}

.action-btn-small.secondary {
  background: var(--glass-bg-secondary);
  color: var(--text-primary);
  border: 1px solid var(--glass-border);
}

.action-btn-small.secondary:hover {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.action-btn-small.danger {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.action-btn-small.danger:hover {
  background: rgba(239, 68, 68, 0.15);
  transform: translateY(-1px);
}

/* 🎯 加载和空状态 */
.table-loading,
.table-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
}

.loading-content,
.empty-content {
  text-align: center;
}

.loading-spinner {
  font-size: 2rem;
  margin-bottom: 12px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1rem;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.empty-hint {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* 🎯 分页组件 */
.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  background: var(--glass-bg-primary);
  border-radius: 12px;
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-blur-standard);
  margin-bottom: 24px;
}

.pagination-info {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid var(--glass-border);
  border-radius: 6px;
  background: var(--glass-bg-secondary);
  color: var(--text-primary);
  font-size: 0.9rem;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px) saturate(1.2);
}

.page-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.page-btn.active {
  background: var(--brand-primary);
  color: #4f46e5;
  border-color: rgba(139, 92, 246, 0.2);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

/* 🎯 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  backdrop-filter: blur(8px);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: var(--therapist-z-modal-backdrop);
  padding: 20px;
}

.therapist-form-modal {
  width: min(90vw, 600px);
  max-height: calc(100vh - 40px);
  background: linear-gradient(145deg,
    rgba(255, 255, 255, 0.08) 0%,
    rgba(248, 250, 252, 0.12) 50%,
    rgba(255, 255, 255, 0.08) 100%
  );
  border-radius: 16px;
  border: 1px solid var(--glass-border);
  backdrop-filter: var(--glass-blur-heavy);
  box-shadow:
    0 25px 50px rgba(0, 0, 0, 0.15),
    0 0 0 1px rgba(255, 255, 255, 0.2),
    inset 0 1px 0 rgba(255, 255, 255, 0.3);
  z-index: var(--therapist-z-modal);
  overflow: hidden;
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--brand-primary);
  border-bottom: 1px solid var(--glass-border);
  backdrop-filter: blur(15px) saturate(1.3);
}

.form-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: scale(1.1);
}

.form-content {
  padding: 24px;
  max-height: 400px;
  overflow-y: auto;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  flex: 1;
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 6px;
}

.required {
  color: #dc2626;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  background: var(--glass-bg-input);
  backdrop-filter: blur(15px) saturate(1.2);
  color: var(--text-primary);
  font-size: 0.9rem;
  transition: all 0.2s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: rgba(139, 92, 246, 0.3);
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 2px rgba(139, 92, 246, 0.1);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  background: rgba(255, 255, 255, 0.03);
  border-top: 1px solid var(--glass-border);
  backdrop-filter: blur(20px) saturate(1.3);
}

/* 🎯 通知组件 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  padding: 12px 16px;
  border-radius: 8px;
  backdrop-filter: blur(20px) saturate(1.4);
  z-index: var(--therapist-z-toast);
  animation: slideIn 0.3s ease;
}

.notification.success {
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
  color: #059669;
}

.notification.error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
  color: #dc2626;
}

.notification.warning {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.2);
  color: #d97706;
}

.notification.info {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.2);
  color: #2563eb;
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-icon {
  font-size: 1rem;
}

.notification-message {
  font-size: 0.9rem;
  font-weight: 500;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

/* 🎯 响应式设计 */
@media (max-width: 768px) {
  .therapist-management-container {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .toolbar-container {
    flex-direction: column;
    gap: 16px;
  }

  .toolbar-left {
    width: 100%;
    justify-content: center;
  }

  .search-container {
    width: 100%;
    max-width: 300px;
  }

  .header-stats {
    flex-direction: column;
    gap: 12px;
  }

  .form-row {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}
</style>
