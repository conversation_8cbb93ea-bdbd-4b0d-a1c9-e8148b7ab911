<template>
  <!-- Wavy Login Form 容器 -->
  <div class="wavy-login-container">
    <!-- 波浪背景动画 -->
    <div class="wave-bg">
      <div class="wave wave1"></div>
      <div class="wave wave2"></div>
      <div class="wave wave3"></div>
    </div>

    <!-- 登录表单卡片 -->
    <div class="login-card">
      <!-- Logo区域 -->
      <div class="brand-section">
        <div class="logo-container">
          <img src="@/assets/images/logo.png" alt="Logo" class="brand-logo" />
        </div>
      </div>
      <!-- 表单区域 -->
      <div class="login-form-container">
        <!-- 账号密码登录表单 -->
        <!-- 开发环境提示 - 已禁用以避免影响测试结果 -->
        <!-- <div v-if="isDevelopment" class="dev-hint">
          <a-alert
            message="开发环境登录提示"
            description="用户名: admin，密码: admin123"
            type="info"
            show-icon
            style="margin-bottom: 16px;"
          />
        </div> -->

        <div class="password-login">
          <a-form
            :model="formState"
            name="login-form"
            @finish="onFinish"
            @finishFailed="onFinishFailed"
            layout="vertical"
          >
          <a-form-item
            name="username"
            class="form-item"
            :rules="[
              { required: true, message: '请输入用户名' },
              { min: 2, message: '用户名至少需要2个字符' },
              { max: 50, message: '用户名不能超过50个字符' }
            ]"
          >
            <a-input
              ref="usernameInput"
              v-model:value="formState.username"
              placeholder="用户名"
              size="large"
              class="zen-input"
              autocomplete="username"
              @keydown.enter="focusPasswordInput"
              @blur="validateUsername"
            >
              <template #prefix>
                <UserOutlined class="input-icon" />
              </template>
            </a-input>
          </a-form-item>

          <a-form-item
            name="password"
            class="form-item"
            :rules="[
              { required: true, message: '请输入密码' },
              { min: 6, message: '密码至少需要6个字符' },
              { max: 100, message: '密码不能超过100个字符' }
            ]"
          >
            <a-input-password
              ref="passwordInput"
              v-model:value="formState.password"
              placeholder="密码"
              size="large"
              class="zen-input"
              autocomplete="current-password"
              @keydown.enter="handleEnterSubmit"
              @blur="validatePassword"
            >
              <template #prefix>
                <LockOutlined class="input-icon" />
              </template>
            </a-input-password>
          </a-form-item>



          <a-form-item class="login-button-item">
            <a-button
              :loading="loading"
              :disabled="loading || !formState.username || !formState.password"
              type="primary"
              html-type="submit"
              class="zen-button"
              size="large"
              block
              :title="loading ? '正在登录...' : '点击登录'"
            >
              <span v-if="!loading">进入系统</span>
              <span v-else>登录中...</span>
            </a-button>
          </a-form-item>

          <!-- 登录按钮下方的版权信息 -->
          <div class="login-footer-inline">
            <p>© {{ new Date().getFullYear() }} 壹心堂 Yixintang</p>
            <p class="version-info">管理系统-专业版</p>
          </div>
          </a-form>
        </div>


      </div>
    </div>
  </div>
</template>

<script setup>
// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

// 防抖函数 - 性能优化
let debounceTimer = null;
const debounce = (func, delay = 300) => {
  return (...args) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    debounceTimer = setTimeout(() => func.apply(this, args), delay);
  };
};

import { useUserStore } from '@/store';
import {
    LockOutlined,
    UserOutlined
} from '@ant-design/icons-vue';
import { message } from 'ant-design-vue';
import { reactive, ref , nextTick, shallowRef, watchEffect, onMounted, onUnmounted } from 'vue';;
import { useRoute, useRouter } from 'vue-router';

const router = useRouter()
const route = useRoute()
const userStore = useUserStore()
const loading = ref(false)
const showPassword = ref(false)

// 输入框引用
const usernameInput = ref(null)
const passwordInput = ref(null)

// 开发环境检测
const isDevelopment = ref(process.env.NODE_ENV === 'development')

// 登录表单不需要复杂的前端验证，后端会处理验证逻辑

// 表单数据
const formState = reactive({
  username: '',
  password: '',
  remember: false
});

// 切换密码显示
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 登录成功处理
const onFinish = async (values) => {
  console.log('🔐 登录表单提交:', values);

  // 防止重复提交
  if (loading.value) {
    return;
  }

  loading.value = true;

  try {
    // 调用store的登录方法
    const success = await userStore.login({
      username: values.username.trim(),
      password: values.password
    });

    if (success) {
      console.log('✅ 登录成功，准备跳转');

      // 记住用户名（如果勾选了记住我）
      if (formState.value.remember) {
        localStorage.setItem('remembered_username', values.username.trim());
      } else {
        localStorage.removeItem('remembered_username');
      }

      // 如果有重定向参数，跳转到相应页面，否则跳转到仪表盘
      const redirect = route.query.redirect || '/dashboard';

      // 延迟跳转，让用户看到成功提示
      setTimeout(() => {
        router.push(redirect);
      }, 500);
    } else {
      console.log('❌ 登录失败');
      // 错误信息已在store中显示

      // 清空密码字段
      formState.password = '';
    }
  } catch (error) {
    console.error('❌ 登录异常:', error);

    // 根据错误类型显示不同的提示
    if (error.code === 'NETWORK_ERROR') {
      message.error('网络连接失败，请检查网络设置');
    } else if (error.response?.status === 429) {
      message.error('登录尝试过于频繁，请稍后再试');
    } else {
      message.error('登录失败，请稍后再试');
    }

    // 清空密码字段
    formState.password = '';
  } finally {
    loading.value = false;
  }
};

// 登录失败处理（现在主要用于调试）
const onFinishFailed = (errorInfo) => {
  console.log('❌ 表单提交失败:', errorInfo);

  // 显示表单验证错误提示
  if (errorInfo.errorFields && errorInfo.errorFields.length > 0) {
    const firstError = errorInfo.errorFields[0];
    message.error(firstError.errors[0] || '请检查输入信息');
  }
};

// 忘记密码处理
const handleForgotPassword = () => {
  message.info('请联系系统管理员重置密码：13210583333');
};

// Enter键导航：用户名框Enter键跳转到密码框
const focusPasswordInput = () => {
  console.log('🔄 用户名Enter键，跳转到密码框');
  nextTick(() => {
    passwordInput.value?.focus();
  });
};

// Enter键提交：密码框Enter键提交登录
const handleEnterSubmit = () => {
  console.log('🔑 密码框Enter键，提交登录');
  // 触发表单提交
  const formElement = document.querySelector('form[name="login-form"]');
  if (formElement) {
    // 创建并触发submit事件
    const submitEvent = new Event('submit', { cancelable: true, bubbles: true });
    formElement.dispatchEvent(submitEvent);
  }
};

// 表单验证：用户名验证
const validateUsername = () => {
  if (!formState.username) {
    message.warning('请输入用户名');
    return;
  }
  if (formState.username.length < 2) {
    message.warning('用户名至少需要2个字符');
    return;
  }
  if (formState.username.length > 50) {
    message.warning('用户名不能超过50个字符');
    return;
  }
  console.log('✅ 用户名验证通过');
};

// 表单验证：密码验证
const validatePassword = () => {
  if (!formState.password) {
    message.warning('请输入密码');
    return;
  }
  if (formState.password.length < 6) {
    message.warning('密码至少需要6个字符');
    return;
  }
  if (formState.password.length > 100) {
    message.warning('密码不能超过100个字符');
    return;
  }
  console.log('✅ 密码验证通过');
};

// 页面加载时自动聚焦到用户名输入框
onMounted(() => {
  console.log('🎯 登录页面加载完成，自动聚焦到用户名输入框');
  nextTick(() => {
    usernameInput.value?.focus();
  });

  // SVG光斑特效 - 鼠标跟随interactive光斑
  const interBubble = document.querySelector('.interactive');
  if (interBubble) {
    let curX = 0;
    let curY = 0;
    let tgX = 0;
    let tgY = 0;

    function move() {
      curX += (tgX - curX) / 20;
      curY += (tgY - curY) / 20;
      interBubble.style.transform = `translate(${Math.round(curX)}px, ${Math.round(curY)}px)`;
      requestAnimationFrame(() => {
        move();
      });
    }

    const handleMouseMove = (event) => {
      tgX = event.clientX;
      tgY = event.clientY;
    };

    window.addEventListener('mousemove', handleMouseMove);
    move();

    // 组件卸载时清理监听器
    onUnmounted(() => {
      window.removeEventListener('mousemove', handleMouseMove);
    });
  }
});


</script>

<style scoped>
/*
=============================================================================
Wavy Login Form - 波浪登录表单样式
=============================================================================
作者: Augment Agent
创建时间: 2025-07-19
最后修改: 2025-07-19
描述: 基于CodePen Wavy Login Form实现的波浪动画登录表单
特点: 浅紫色渐变背景 + 3层波浪动画 + 浮动登录卡片 + 圆角输入框
技术: 纯CSS动画，无JavaScript依赖
兼容性: 现代浏览器，支持CSS3动画和渐变
=============================================================================
*/

/*
-----------------------------------------------------------------------------
CSS变量定义 - 统一管理颜色和尺寸
-----------------------------------------------------------------------------
用途: 便于维护和主题切换，所有颜色值集中管理
修改日志: 2025-07-19 创建浅紫色主题变量
-----------------------------------------------------------------------------
*/
:root {
  /* 波浪动画颜色 - 用于3层波浪的渐变效果 */
  --wave-color-1: #667eea;    /* 主波浪色 - 蓝紫色 */
  --wave-color-2: #764ba2;    /* 次波浪色 - 深紫色 */
  --wave-color-3: #f093fb;    /* 装饰波浪色 - 粉紫色 */

  /* 背景渐变颜色 - 主容器的渐变背景 */
  --bg-gradient-start: #667eea;  /* 渐变起始色 - 蓝紫色 */
  --bg-gradient-end: #f093fb;    /* 渐变结束色 - 粉紫色 */

  /* 卡片和文本颜色 - 登录卡片相关颜色 */
  --card-bg: rgb(255 255 255 / 95%);  /* 卡片背景 - 半透明白色 */
  --text-primary: #333;        /* 主要文本色 - 深灰色 */
  --text-secondary: #666;      /* 次要文本色 - 中灰色 */

  /* 表单元素颜色 - 输入框和按钮颜色 */
  --input-border: #ddd;        /* 输入框边框色 - 浅灰色 */
  --button-bg: #667eea;        /* 按钮背景色 - 蓝紫色 */
}

/*
-----------------------------------------------------------------------------
主容器样式 - Wavy Login Form 的根容器
-----------------------------------------------------------------------------
功能: 创建全屏的波浪登录表单背景容器
技术: CSS Flexbox布局 + 线性渐变背景
效果: 浅紫色渐变背景，居中显示登录卡片
修改日志:
  - 2025-07-19 创建：实现全屏容器和渐变背景
  - 2025-07-19 修复：通过JavaScript强制设置背景渐变
兼容性: 现代浏览器，支持CSS3渐变和Flexbox
性能: 使用GPU加速的渐变，性能良好
-----------------------------------------------------------------------------
*/
.wavy-login-container {
  /* 渐变方向: 135度对角线渐变，从蓝紫色(#667eea)到粉紫色(#f093fb) */

  /* 布局设置 - Flexbox居中 */
  display: flex;          /* 弹性布局 */

  /* 定位和层级 - 作为根容器 */
  position: relative;     /* 相对定位，为子元素提供定位上下文 */

  /* 全屏尺寸设置 - 占满整个视口 */
  width: 100vw;           /* 视口宽度100% */
  height: 100vh;          /* 视口高度100% */
  overflow: hidden;       /* 隐藏溢出内容，确保波浪动画不超出边界 */

  /* 背景渐变 - 浅紫色主题 */
  background: linear-gradient(135deg, var(--bg-gradient-start) 0%, var(--bg-gradient-end) 100%);
  align-items: center;    /* 垂直居中 */
  justify-content: center; /* 水平居中 */
}

/* 波浪背景动画 */
.wave-bg {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.wave {
  position: absolute;
  top: 0;
  left: 0;
  width: 200%;
  height: 100%;
  background: linear-gradient(45deg, transparent, rgb(255 255 255 / 10%), transparent);
  animation: wave-animation 6s ease-in-out infinite;
}

.wave1 {
  animation-delay: 0s;
  opacity: 0.7;
}

.wave2 {
  animation-delay: 2s;
  opacity: 0.5;
}

.wave3 {
  animation-delay: 4s;
  opacity: 0.3;
}

/* 登录卡片 */
.login-card {
  position: relative;
  z-index: 10;
  width: 90%;
  max-width: 400px;
  padding: 40px;
  border: 1px solid rgb(255 255 255 / 30%);
  border-radius: 20px;
  background: var(--card-bg);
  box-shadow: 0 20px 40px rgb(0 0 0 / 10%);
  backdrop-filter: blur(20px);
  animation: card-float 3s ease-in-out infinite;
}

/* Logo区域 */
.brand-section {
  text-align: center;
  margin-bottom: 30px;
}

.brand-logo {
  width: 80px;
  height: 80px;
  object-fit: contain;
  margin-bottom: 15px;
  filter: drop-shadow(0 4px 8px rgb(102 126 234 / 30%));
}

/* 表单样式 */
.login-form-container {
  margin-bottom: 20px;
}

/* 输入框样式 */
.form-item {
  margin-bottom: 20px;
}

.form-item .ant-input,
.form-item .ant-input-password {
  padding: 12px 20px;
  border: 2px solid var(--input-border);
  border-radius: 25px;
  font-size: 16px;
  background: rgb(255 255 255 / 90%);
  transition: all 0.3s ease;
}

.form-item .ant-input:focus,
.form-item .ant-input-password:focus,
.form-item .ant-input-focused,
.form-item .ant-input-password-focused {
  border-color: var(--button-bg);
  box-shadow: 0 0 0 3px rgb(102 126 234 / 10%);
  transform: translateY(-2px);
}

/* 按钮样式 */
.form-item .ant-btn {
  position: relative;
  height: auto;
  padding: 12px 30px;
  border: none;
  border-radius: 25px;
  overflow: hidden;
  font-size: 16px;
  font-weight: 600;
  background: linear-gradient(45deg, var(--button-bg), var(--wave-color-3));
  transition: all 0.3s ease;
}

.form-item .ant-btn:hover {
  box-shadow: 0 10px 20px rgb(102 126 234 / 30%);
  transform: translateY(-2px);
}

.form-item .ant-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgb(255 255 255 / 30%), transparent);
  transition: left 0.5s ease;
}

.form-item .ant-btn:hover::before {
  left: 100%;
}

/* 动画关键帧 */
@keyframes wave-animation {
  0% {
    transform: translateX(-100%) skewX(-15deg);
  }

  50% {
    transform: translateX(-50%) skewX(0deg);
  }

  100% {
    transform: translateX(0%) skewX(15deg);
  }
}

@keyframes card-float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

/* 版权信息 */
.login-footer-inline {
  font-size: 12px;
  text-align: center;
  color: var(--text-secondary);
  margin-top: 20px;
}

.login-footer-inline p {
  margin: 2px 0;
}
</style>
