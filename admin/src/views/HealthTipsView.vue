<template>
  <div class="picasso-health-tips">
<!-- 毕加索风格操作栏 -->
    <div class="action-toolbar">
      <div class="search-cubism">
        <input 
          type="text" 
          placeholder="搜索健康小贴士..."
          v-model="searchValue"
          @input="handleSearch"
          class="search-fragment"
        />
      </div>
      
      <div class="filter-cubism">
        <select 
          v-model="categoryFilter"
          @change="handleFilterChange"
          class="filter-fragment"
        >
          <option value="">所有分类</option>
          <option value="颈椎保健">颈椎保健</option>
          <option value="腰椎护理">腰椎护理</option>
          <option value="足部保养">足部保养</option>
          <option value="日常养生">日常养生</option>
          <option value="饮食调理">饮食调理</option>
        </select>

        <select 
          v-model="statusFilter"
          @change="handleFilterChange"
          class="filter-fragment"
        >
          <option value="">所有状态</option>
          <option value="published">已发布</option>
          <option value="draft">草稿</option>
        </select>
      </div>

      <div class="action-cubism">
        <div class="action-cube refresh-cube" @click="loadTips">
          <div class="cube-face">
            <span class="cube-icon">🔄</span>
            <span class="cube-text">刷新</span>
          </div>
        </div>
        <div class="action-cube add-cube" @click="showAddModal">
          <div class="cube-face">
            <span class="cube-icon">➕</span>
            <span class="cube-text">新增贴士</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 数据表格 - 符合CI_CD_STANDARDS.md -->
    <div class="table-container" :class="{ 'hide-scrollbar': paginatedData.length <= 5 }">
      <!-- 加载状态 -->
      <div v-if="loadingStates.dataLoading" class="table-loading-container">
        <div class="loading-spinner-large"></div>
        <span>加载中...</span>
      </div>

      <!-- 空状态 -->
      <div v-else-if="paginatedData.length === 0" class="table-empty-container">
        <div class="empty-icon">📝</div>
        <div class="empty-text">暂无健康贴士</div>
      </div>

      <!-- 表头 -->
      <div v-else class="table-header">
        <div class="header-cell" style="flex: 2;">标题</div>
        <div class="header-cell" style="flex: 1.5;">分类</div>
        <div class="header-cell" style="flex: 1;">状态</div>
        <div class="header-cell" style="flex: 1.5;">创建时间</div>
        <div class="header-cell" style="flex: 1.5;">操作</div>
      </div>

      <!-- 数据行 -->
      <div
        v-for="tip in paginatedData"
        :key="tip.id"
        class="data-row"
      >
        <!-- 标题列 -->
        <div class="data-cell" style="flex: 2;" data-label="标题">
          <div class="cell-content">
            <div class="tip-title">{{ tip.title }}</div>
            <div class="tip-summary">{{ tip.summary }}</div>
          </div>
        </div>

        <!-- 分类列 -->
        <div class="data-cell" style="flex: 1.5;" data-label="分类">
          <div class="cell-content">
            <span class="category-tag" :class="'category-' + tip.category.replace(/\s+/g, '-')">
              {{ tip.category }}
            </span>
          </div>
        </div>

        <!-- 状态列 -->
        <div class="data-cell" style="flex: 1;" data-label="状态">
          <div class="cell-content">
            <span class="status-indicator" :class="'status-' + tip.status">
              <span class="status-dot"></span>
              {{ tip.status === 'published' ? '已发布' : '草稿' }}
            </span>
          </div>
        </div>

        <!-- 创建时间列 -->
        <div class="data-cell" style="flex: 1.5;" data-label="创建时间">
          <div class="cell-content">
            <span class="date-text">{{ tip.created_date }}</span>
          </div>
        </div>

        <!-- 操作列 -->
        <div class="data-cell" style="flex: 1.5;" data-label="操作">
          <div class="cell-content">
            <div class="action-fragments">
              <button class="action-btn view" @click="handleView(tip)" title="查看详情" aria-label="操作按钮">
                <span class="btn-icon">👁️</span>
                <span class="btn-text">查看</span>
              </button>
              <button class="action-btn edit" @click="handleEdit(tip)" title="编辑" aria-label="操作按钮">
                <span class="btn-icon">✏️</span>
                <span class="btn-text">编辑</span>
              </button>
              <button class="action-btn toggle" @click="handleToggleStatus(tip)" title="切换状态" aria-label="操作按钮">
                <span class="btn-icon">🔄</span>
                <span class="btn-text">{{ tip.status === 'published' ? '下架' : '发布' }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 梵高风格分页组件 - 符合CI_CD_STANDARDS.md -->
    <div class="pagination-container">
      <div class="pagination-info">
        <span class="total-info">
          共 <span class="highlight-number">{{ totalRecords }}</span> 条记录，
          第 <span class="highlight-number">{{ currentPage }}</span> /
          <span class="highlight-number">{{ totalPages }}</span> 页
        </span>
      </div>

      <div class="pagination-controls">
        <div class="page-size-selector">
          <label class="page-size-label">每页显示：</label>
          <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
            <option value="5">5条</option>
            <option value="10">10条</option>
            <option value="20">20条</option>
            <option value="50">50条</option>
          </select>
        </div>

        <div class="page-navigation" v-if="totalRecords > 0">
          <button
            class="page-btn prev-btn"
            @click="prevPage"
            :disabled="currentPage === 1"
           aria-label="操作按钮">
            ‹ 上一页
          </button>

          <div class="page-numbers">
            <button
              v-for="page in visiblePages"
              :key="page"
              class="page-btn page-number"
              :class="{ active: page === currentPage }"
              @click="goToPage(page)"
             aria-label="操作按钮">
              {{ page }}
            </button>
          </div>

          <button
            class="page-btn next-btn"
            @click="nextPage"
            :disabled="currentPage === totalPages"
           aria-label="操作按钮">
            下一页 ›
          </button>
        </div>
      </div>
    </div>

    <!-- 毕加索风格模态框 -->
    <div v-if="modalVisible" class="modal-overlay" @click="hideModal">
      <div class="modal-cubism" @click.stop>
        <div class="modal-header">
          <div class="header-title">{{ modalTitle }}</div>
          <div class="close-btn" @click="hideModal">×</div>
        </div>
        
        <div class="modal-body">
          <div class="form-cubism">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">标题 <span class="required">*</span></label>
                <input
                  type="text"
                  v-model="formState.title"
                  class="form-input"
                  :class="{ 'error': formErrors.title }"
                  placeholder="请输入标题"
                  required
                aria-label="输入字段">
                <div v-if="formErrors.title" class="error-message">{{ formErrors.title }}</div>
              </div>

              <div class="form-group">
                <label class="form-label">分类 <span class="required">*</span></label>
                <select
                  v-model="formState.category"
                  class="form-select"
                  :class="{ 'error': formErrors.category }"
                  required
                >
                  <option value="">请选择分类</option>
                  <option value="颈椎保健">颈椎保健</option>
                  <option value="腰椎护理">腰椎护理</option>
                  <option value="足部保养">足部保养</option>
                  <option value="日常养生">日常养生</option>
                  <option value="饮食调理">饮食调理</option>
                </select>
                <div v-if="formErrors.category" class="error-message">{{ formErrors.category }}</div>
              </div>
            </div>

            <div class="form-row full-width">
              <div class="form-group">
                <label class="form-label">摘要 <span class="required">*</span></label>
                <textarea
                  v-model="formState.summary"
                  class="form-textarea"
                  :class="{ 'error': formErrors.summary }"
                  placeholder="请输入摘要..."
                  rows="2"
                  required
                ></textarea>
                <div v-if="formErrors.summary" class="error-message">{{ formErrors.summary }}</div>
              </div>
            </div>

            <div class="form-row full-width">
              <div class="form-group">
                <label class="form-label">详细内容 <span class="required">*</span></label>
                <textarea
                  v-model="formState.content"
                  class="form-textarea"
                  :class="{ 'error': formErrors.content }"
                  placeholder="请输入详细内容..."
                  rows="8"
                  required
                ></textarea>
                <div v-if="formErrors.content" class="error-message">{{ formErrors.content }}</div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">状态</label>
                <select v-model="formState.status" class="form-select">
                  <option value="draft">草稿</option>
                  <option value="published">发布</option>
                </select>
              </div>
              
              <div class="form-group">
                <label class="form-label">排序权重</label>
                <input 
                  type="number" 
                  v-model="formState.sort_order" 
                  class="form-input"
                  placeholder="数字越大越靠前"
                  min="0"
                aria-label="输入字段">
              </div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <div class="footer-actions">
            <div class="action-btn cancel" @click="hideModal">
              <div class="btn-face">取消</div>
            </div>
            <div class="action-btn confirm" @click="handleSubmit">
              <div class="btn-face">{{ confirmLoading ? '提交中...' : '确定' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格查看模态框 -->
    <div v-if="viewModalVisible" class="modal-overlay" @click="hideViewModal">
      <div class="view-modal-cubism" @click.stop>
        <div class="modal-header">
          <div class="header-title">{{ selectedTip?.title }}</div>
          <div class="close-btn" @click="hideViewModal">×</div>
        </div>
        
        <div class="modal-body">
          <div class="tip-detail-cubism">
            <div class="detail-meta">
              <div class="meta-item">
                <span class="meta-label">分类：</span>
                <span class="meta-value">{{ selectedTip?.category }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">状态：</span>
                <span class="meta-value" :class="'status-' + selectedTip?.status">
                  {{ selectedTip?.status === 'published' ? '已发布' : '草稿' }}
                </span>
              </div>
              <div class="meta-item">
                <span class="meta-label">创建时间：</span>
                <span class="meta-value">{{ selectedTip?.created_date }}</span>
              </div>
              <div class="meta-item">
                <span class="meta-label">浏览次数：</span>
                <span class="meta-value">{{ selectedTip?.views }}</span>
              </div>
            </div>
            
            <div class="detail-summary">
              <div class="summary-title">摘要</div>
              <div class="summary-content">{{ selectedTip?.summary }}</div>
            </div>
            
            <div class="detail-content">
              <div class="content-title">详细内容</div>
              <div class="content-text">{{ selectedTip?.content }}</div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <div class="footer-actions">
            <div class="action-btn close" @click="hideViewModal">
              <div class="btn-face">关闭</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast通知组件 - 符合CI_CD_STANDARDS.md用户反馈规范 -->
  <div v-if="toastState.visible" class="toast-notification" :class="'toast-' + toastState.type">
    <div class="toast-content">
      <div class="toast-icon">
        <span v-if="toastState.type === 'success'">✅</span>
        <span v-else-if="toastState.type === 'error'">❌</span>
        <span v-else-if="toastState.type === 'warning'">⚠️</span>
      </div>
      <div class="toast-message">{{ toastState.message }}</div>
    </div>
  </div>
</template>

<script setup>
// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

import { ref, reactive, computed, onMounted , nextTick, shallowRef, watchEffect } from 'vue';;

// 响应式数据
const tips = ref([]);
const modalVisible = ref(false);
const viewModalVisible = ref(false);
const modalTitle = ref('新增贴士');
const confirmLoading = ref(false);
const searchValue = ref('');
const categoryFilter = ref('');
const statusFilter = ref('');
const currentPage = ref(1);
const pageSize = ref(5); // 默认5条每页，符合开发规范

// 加载状态管理 - 符合CI_CD_STANDARDS.md
const loadingStates = reactive({
  dataLoading: false,
  submitLoading: false,
  deleteLoading: false
});

// Toast通知状态 - 符合CI_CD_STANDARDS.md用户反馈规范
const toastState = reactive({
  visible: false,
  message: '',
  type: 'success' // success, error, warning
});

// Toast通知函数 - 替代console日志
const showToast = (message, type = 'success') => {
  toastState.message = message;
  toastState.type = type;
  toastState.visible = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    toastState.visible = false;
  }, 3000);
};

const selectedTip = ref(null);

// 表单状态
const formState = reactive({
  id: null,
  title: '',
  category: '',
  summary: '',
  content: '',
  status: 'draft',
  sort_order: 0
});

// 表单验证错误状态 - 符合CI_CD_STANDARDS.md表单验证规范
const formErrors = reactive({
  title: '',
  category: '',
  summary: '',
  content: ''
});

// 表单验证函数 - 符合CI_CD_STANDARDS.md表单验证规范
const validateForm = () => {
  // 清空之前的错误
  Object.assign(formErrors, {
    title: '',
    category: '',
    summary: '',
    content: ''
  });

  let isValid = true;

  // 验证标题
  if (!formState.title?.trim()) {
    formErrors.title = '请输入健康贴士标题';
    isValid = false;
  } else if (formState.title.trim().length < 5) {
    formErrors.title = '标题至少需要5个字符';
    isValid = false;
  }

  // 验证分类
  if (!formState.category?.trim()) {
    formErrors.category = '请选择分类';
    isValid = false;
  }

  // 验证摘要
  if (!formState.summary?.trim()) {
    formErrors.summary = '请输入摘要';
    isValid = false;
  } else if (formState.summary.trim().length < 10) {
    formErrors.summary = '摘要至少需要10个字符';
    isValid = false;
  }

  // 验证内容
  if (!formState.content?.trim()) {
    formErrors.content = '请输入详细内容';
    isValid = false;
  } else if (formState.content.trim().length < 20) {
    formErrors.content = '内容至少需要20个字符';
    isValid = false;
  }

  return isValid;
};

// 计算属性
const totalRecords = computed(() => tips.value.length);
const totalPages = computed(() => Math.ceil(tips.value.length / pageSize.value));
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return tips.value.slice(start, end);
});

const visiblePages = computed(() => {
  const pages = [];
  const total = totalPages.value;
  const current = currentPage.value;
  
  for (let i = Math.max(1, current - 2); i <= Math.min(total, current + 2); i++) {
    pages.push(i);
  }
  return pages;
});

// 方法
const loadTips = async () => {
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 500));
  
  tips.value = [
    {
      id: 1,
      title: '颈椎保健的日常小贴士',
      category: '颈椎保健',
      summary: '长期伏案工作容易导致颈椎问题，学会正确的颈椎保健方法很重要。',
      content: '1. 保持正确坐姿，头部与颈部保持自然直立...\n2. 每小时起身活动5-10分钟...\n3. 选择合适高度的枕头...',
      status: 'published',
      created_date: '2024-01-15',
      views: 156,
      sort_order: 10
    },
    {
      id: 2,
      title: '腰椎护理的重要性',
      category: '腰椎护理',
      summary: '腰椎是人体的重要支撑结构，正确的护理方法能预防腰椎疾病。',
      content: '腰椎护理包括正确的坐姿、适当的运动、合理的睡眠姿势等多个方面...',
      status: 'published',
      created_date: '2024-01-14',
      views: 89,
      sort_order: 8
    },
    {
      id: 3,
      title: '足部按摩的好处',
      category: '足部保养',
      summary: '足部按摩不仅能缓解疲劳，还能促进血液循环，改善睡眠质量。',
      content: '足部按摩的具体方法和注意事项...',
      status: 'draft',
      created_date: '2024-01-13',
      views: 45,
      sort_order: 5
    }
  ];
};

const showAddModal = () => {
  modalTitle.value = '新增贴士';
  Object.assign(formState, {
    id: null,
    title: '',
    category: '',
    summary: '',
    content: '',
    status: 'draft',
    sort_order: 0
  });
  modalVisible.value = true;
};

const hideModal = () => {
  modalVisible.value = false;
  confirmLoading.value = false;
};

const hideViewModal = () => {
  viewModalVisible.value = false;
  selectedTip.value = null;
};

const handleEdit = (tip) => {
  modalTitle.value = '编辑贴士';
  Object.assign(formState, tip);
  modalVisible.value = true;
};

const handleView = (tip) => {
  selectedTip.value = tip;
  viewModalVisible.value = true;
};

const handleToggleStatus = (tip) => {
  tip.status = tip.status === 'published' ? 'draft' : 'published';
  console.log('切换状态:', tip);
};

const handleSearch = () => {
  console.log('搜索:', searchValue.value);
};

const handleFilterChange = () => {
  console.log('筛选:', { category: categoryFilter.value, status: statusFilter.value });
};

const handleSubmit = async () => {
  try {
    // 表单验证 - 符合CI_CD_STANDARDS.md表单验证规范
    if (!validateForm()) {
      showToast('请检查输入内容', 'error');
      return;
    }

    confirmLoading.value = true;
    await new Promise(resolve => setTimeout(resolve, 1000));

    if (formState.id) {
      const index = tips.value.findIndex(item => item.id === formState.id);
      if (index !== -1) {
        tips.value[index] = { ...formState };
        showToast('健康贴士更新成功', 'success');
      }
    } else {
      tips.value.unshift({
        ...formState,
        id: Date.now(),
        created_date: new Date().toISOString().split('T')[0],
        views: 0
      });
      showToast('健康贴士添加成功', 'success');
    }

    hideModal();
  } catch (error) {
    console.error('健康贴士操作失败:', error);
    showToast('操作失败，请重试', 'error');
  } finally {
    confirmLoading.value = false;
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

const handlePageSizeChange = () => {
  currentPage.value = 1; // 重置到第一页
};

// 初始化
onMounted(() => {
  loadTips();
});
</script>

<style scoped>
/* 毕加索风格健康小贴士 - 占满主内容区 */
.picasso-health-tips {
  display: flex;
  position: fixed;
  inset: 0 0 0 180px; /* 侧边栏宽度 */
  width: calc(100vw - 180px);
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;
  background: linear-gradient(45deg,
    #8e44ad 0%, #9b59b6 25%, #c8a2c8 50%, #dda0dd 75%, #e6e6fa 100%
  );
  background-size: 400% 400%;
  animation: picassoFlow 20s ease infinite;
  flex-direction: column;
}

@keyframes picassoFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 0% 50%; }
  100% { background-position: 50% 0%; }
}

/* 继承毕加索风格基础样式 */

/* 已删除无用的标题样式 */

/* 毕加索风格操作栏 */
.action-toolbar {
  display: flex;
  position: relative;
  z-index: 10;
  gap: 20px;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.search-cubism {
  flex: 1;
}

.search-fragment {
  width: 100%;
  padding: 12px 20px;
  border: none;
  border-radius: 0 25px;
  font-size: 1rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg,
    rgb(200 162 200 / 90%),
    rgb(32 178 170 / 70%),
    rgb(255 255 255 / 80%)
  );
  box-shadow: 3px 3px 10px rgb(0 0 0 / 20%);
  transform: skew(-3deg);
  transition: all 0.3s ease;
}

.search-fragment:focus {
  box-shadow: 5px 5px 15px rgb(65 105 225 / 40%);
  transform: skew(-3deg) scale(1.02);
  outline: none;
}

.filter-cubism {
  display: flex;
  gap: 10px;
}

.filter-fragment {
  padding: 12px 15px;
  border: none;
  border-radius: 0 20px;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg,
    rgb(200 162 200 / 90%),
    rgb(32 178 170 / 70%),
    rgb(255 255 255 / 80%)
  );
  box-shadow: 3px 3px 10px rgb(0 0 0 / 20%);
  transform: skew(-3deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.filter-fragment:focus {
  box-shadow: 5px 5px 15px rgb(65 105 225 / 40%);
  transform: skew(-3deg) scale(1.02);
  outline: none;
}

.action-cubism {
  display: flex;
  gap: 10px;
}

.action-cube {
  width: 120px;
  height: 45px;
  border-radius: 15px 0;
  transform: perspective(600px) rotateX(15deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-cube:hover {
  transform: perspective(600px) rotateX(15deg) scale(1.05);
}

.refresh-cube {
  background: linear-gradient(45deg, #c8a2c8, #dda0dd);
}

.add-cube {
  background: linear-gradient(45deg, #20b2aa, #00ced1);
}

.cube-face {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 15px 0;
  font-weight: bold;
  color: white;
  box-shadow: 0 5px 15px rgb(0 0 0 / 30%);
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.cube-icon {
  font-size: 1.2rem;
}

.cube-text {
  font-size: 0.9rem;
}

/* 毕加索风格卡片网格 */
.tips-grid-cubism {
  display: grid;
  position: relative;
  z-index: 5;
  padding: 10px;
  grid-template-columns: repeat(auto-fill, minmax(320px, 1fr));
  gap: 20px;
  flex: 1;
  overflow-y: auto;

  /* 毕加索风格滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #c8a2c8 rgb(200 162 200 / 20%);
}

.tips-grid-cubism::-webkit-scrollbar {
  width: 8px;
}

.tips-grid-cubism::-webkit-scrollbar-track {
  border-radius: 8px;
  background: linear-gradient(45deg, rgb(200 162 200 / 10%), rgb(32 178 170 / 10%));
}

.tips-grid-cubism::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: linear-gradient(45deg, #c8a2c8, #20b2aa);
  transition: all 0.3s ease;
}

.tips-grid-cubism::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #9370db, #4169e1);
}

.tip-card-cube {
  position: relative;
  height: 280px;
  transform-style: preserve-3d;
  transition: all 0.3s ease;
  cursor: pointer;
}

.tip-card-cube:hover {
  transform: rotateY(5deg) rotateX(3deg) scale(1.02);
}

.card-face {
  display: flex;
  position: absolute;
  width: 100%;
  height: 100%;
  padding: 20px;
  border: 3px solid;
  border-radius: 20px 5px;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 10px 30px rgb(0 0 0 / 20%);
  transform: translateZ(5px);
  flex-direction: column;
}

.category-颈椎保健 .card-face {
  border-color: #c8a2c8;
}

.category-腰椎护理 .card-face {
  border-color: #20b2aa;
}

.category-足部保养 .card-face {
  border-color: #4169e1;
}

.category-日常养生 .card-face {
  border-color: #f39c12;
}

.category-饮食调理 .card-face {
  border-color: #27ae60;
}

.card-shadow {
  position: absolute;
  width: 100%;
  height: 100%;
  border-radius: 20px 5px;
  background: linear-gradient(45deg, rgb(200 162 200 / 30%), rgb(32 178 170 / 30%));
  opacity: 0.4;
  transform: translateZ(-10px) translateX(8px) translateY(8px);
}

.tip-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.tip-category {
  padding: 4px 12px;
  border-radius: 12px 0;
  font-size: 0.8rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(200 162 200 / 80%), rgb(255 255 255 / 90%));
  transform: skew(-3deg);
}

.tip-status {
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.7rem;
  font-weight: bold;
  transform: skew(2deg);
}

.status-published {
  color: #27ae60;
  background: linear-gradient(135deg, rgb(39 174 96 / 80%), rgb(255 255 255 / 90%));
}

.status-draft {
  color: #95a5a6;
  background: linear-gradient(135deg, rgb(149 165 166 / 80%), rgb(255 255 255 / 90%));
}

.tip-content {
  flex: 1;
  margin-bottom: 15px;
}

.tip-title {
  font-size: 1.2rem;
  font-weight: bold;
  line-height: 1.3;
  color: #2c3e50;
  transform: skew(-1deg);
  margin-bottom: 10px;
}

.tip-summary {
  font-size: 0.9rem;
  line-height: 1.5;
  color: #7f8c8d;
  transform: skew(0.5deg);
}

.tip-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
  padding-top: 10px;
  border-top: 2px solid rgb(200 162 200 / 30%);
}

.tip-date {
  font-size: 0.8rem;
  font-weight: bold;
  color: #8e44ad;
  transform: skew(-2deg);
}

.tip-views {
  font-size: 0.8rem;
  font-weight: bold;
  color: #3498db;
  transform: skew(2deg);
}

.tip-actions {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 8px 0;
  font-size: 0.8rem;
  font-weight: bold;
  color: white;
  transform: perspective(200px) rotateX(8deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-btn:hover {
  transform: perspective(200px) rotateX(8deg) scale(1.05);
}

.action-btn.edit {
  background: linear-gradient(45deg, #c8a2c8, #dda0dd);
}

.action-btn.view {
  background: linear-gradient(45deg, #4169e1, #6495ed);
}

.action-btn.toggle {
  background: linear-gradient(45deg, #f39c12, #e67e22);
}

/* 🎨 梵高风格分页组件 - 符合CI_CD_STANDARDS.md */
.pagination-container {
  display: flex;
  padding: 12px 18px;
  border: 2px solid rgb(192 132 252 / 40%);
  border-radius: 12px;
  background: linear-gradient(135deg, rgb(139 92 246 / 10%), rgb(168 85 247 / 10%));
  box-shadow:
    0 4px 8px rgb(139 92 246 / 20%),
    0 2px 4px rgb(192 132 252 / 10%);
  margin-top: 15px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  font-size: 14px;
  font-weight: 600;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.highlight-number {
  font-size: 16px;
  font-weight: 700;
  color: var(--van-gogh-accent);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 40%);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-label {
  font-size: 13px;
  font-weight: 600;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.page-size-select {
  padding: 6px 12px;
  border: 2px solid var(--van-gogh-accent);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-primary-dark), var(--van-gogh-secondary));
  box-shadow: 0 2px 4px rgb(139 92 246 / 30%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.page-size-select:hover {
  border-color: var(--van-gogh-primary);
  box-shadow: 0 4px 8px rgb(139 92 246 / 40%);
}

.page-size-select option {
  padding: 8px;
  color: white;
  background: var(--van-gogh-primary-dark);
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  padding: 8px 12px;
  border: 2px solid var(--van-gogh-primary);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-secondary), var(--van-gogh-secondary-dark));
  box-shadow: 0 2px 4px rgb(139 92 246 / 30%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.page-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--van-gogh-primary), var(--van-gogh-primary-dark));
  box-shadow: 0 4px 8px rgb(139 92 246 / 40%);
  transform: translateY(-1px);
}

.page-btn:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  border-color: #d1d5db;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.page-btn.active {
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-accent), var(--van-gogh-accent-dark));
  box-shadow:
    0 4px 8px rgb(244 114 182 / 40%),
    inset 1px 1px 2px rgb(255 255 255 / 30%);
  border-color: var(--van-gogh-accent-light);
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  min-width: 36px;
  text-align: center;
}

/* 毕加索风格模态框 */
.modal-overlay {
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background: rgb(65 105 225 / 70%);
  align-items: center;
  justify-content: center;
}

.modal-cubism, .view-modal-cubism {
  width: 85%;
  max-width: min(700px, calc(100vw - 220px));
  max-height: 80vh;
  border-radius: 20px 5px;
  overflow: hidden;
  background: linear-gradient(135deg, #c8a2c8, #20b2aa, #4169e1, #dda0dd);
  box-shadow: 0 25px 50px rgb(32 178 170 / 40%);
  transform: perspective(800px) rotateY(3deg);
  background-size: 400% 400%;
  animation: modalFlow 8s ease infinite;
}

@keyframes modalFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: rgb(255 255 255 / 95%);
  border-bottom: 3px solid #c8a2c8;
}

.header-title {
  font-size: 1.4rem;
  font-weight: 900;
  color: #2c3e50;
  text-transform: uppercase;
  letter-spacing: 1px;
  transform: skew(-2deg);
}

.close-btn {
  display: flex;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  background: linear-gradient(45deg, #c8a2c8, #20b2aa);
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-btn:hover {
  background: linear-gradient(45deg, #4169e1, #6495ed);
  transform: rotate(90deg) scale(1.1);
}

.modal-body {
  max-height: 60vh;
  padding: 25px;
  background: rgb(255 255 255 / 95%);
  overflow-y: auto;

  /* 模态框滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #c8a2c8 rgb(200 162 200 / 20%);
}

.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  border-radius: 6px;
  background: rgb(200 162 200 / 10%);
}

.modal-body::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: linear-gradient(45deg, #c8a2c8, #20b2aa);
}

/* 毕加索风格表单 */
.form-cubism {
  transform: perspective(600px) rotateX(2deg);
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-row.full-width .form-group {
  flex: 1;
}

.form-group {
  flex: 1;
  position: relative;
}

.form-label {
  display: block;
  width: fit-content;
  padding: 4px 10px;
  border-radius: 8px 0;
  font-size: 0.9rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(200 162 200 / 80%), rgb(255 255 255 / 90%));
  transform: skew(-3deg);
  margin-bottom: 8px;
}

.form-input, .form-select, .form-textarea {
  width: 100%;
  padding: 12px 15px;
  border: none;
  border-radius: 0 12px;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(200 162 200 / 80%), rgb(255 255 255 / 90%));
  box-shadow: 3px 3px 10px rgb(0 0 0 / 10%);
  transform: skew(-1deg);
  transition: all 0.3s ease;
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  background: linear-gradient(135deg, rgb(200 162 200 / 90%), rgb(255 255 255 / 95%));
  box-shadow: 5px 5px 15px rgb(65 105 225 / 30%);
  transform: skew(-1deg) scale(1.02);
  outline: none;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  padding: 20px 25px;
  background: rgb(255 255 255 / 95%);
  border-top: 3px solid #20b2aa;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.footer-actions .action-btn {
  padding: 12px 25px;
  border-radius: 20px 5px;
  font-weight: bold;
  transform: perspective(400px) rotateX(5deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.footer-actions .action-btn.cancel {
  color: white;
  background: linear-gradient(45deg, #95a5a6, #bdc3c7);
}

.footer-actions .action-btn.confirm {
  color: white;
  background: linear-gradient(45deg, #20b2aa, #4169e1);
}

.footer-actions .action-btn.close {
  color: white;
  background: linear-gradient(45deg, #95a5a6, #bdc3c7);
}

.footer-actions .action-btn:hover {
  transform: perspective(400px) rotateX(5deg) scale(1.05);
}

.btn-face {
  font-size: 1rem;
}

/* 毕加索风格详情显示 */
.tip-detail-cubism {
  transform: perspective(600px) rotateX(2deg);
}

.detail-meta {
  display: grid;
  padding: 15px;
  border-radius: 12px 0;
  background: linear-gradient(135deg, rgb(200 162 200 / 10%), rgb(255 255 255 / 80%));
  transform: skew(-1deg);
  grid-template-columns: repeat(2, 1fr);
  gap: 15px;
  margin-bottom: 20px;
}

.meta-item {
  display: flex;
  gap: 8px;
}

.meta-label {
  font-weight: bold;
  color: #8e44ad;
  transform: skew(2deg);
}

.meta-value {
  color: #2c3e50;
  transform: skew(-1deg);
}

.detail-summary, .detail-content {
  margin-bottom: 20px;
}

.summary-title, .content-title {
  padding: 8px 15px;
  border-radius: 10px 0;
  font-size: 1.2rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(32 178 170 / 20%), rgb(255 255 255 / 80%));
  transform: skew(-2deg);
  margin-bottom: 10px;
  border-left: 4px solid #20b2aa;
}

.summary-content, .content-text {
  padding: 15px;
  border-radius: 0 12px;
  line-height: 1.6;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(65 105 225 / 10%), rgb(255 255 255 / 90%));
  transform: skew(0.5deg);
}

/* 响应式设计 */
@media (width <= 768px) {
  .picasso-health-tips {
    left: 0;
    width: 100vw;
    padding: 15px;
  }

  .action-toolbar {
    flex-direction: column;
    gap: 15px;
  }

  .filter-cubism {
    flex-direction: column;
  }

  .action-cubism {
    justify-content: center;
  }

  .tips-grid-cubism {
    grid-template-columns: 1fr;
  }

  .modal-cubism, .view-modal-cubism {
    width: 95%;
    margin: 10px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .detail-meta {
    grid-template-columns: 1fr;
  }

  .title-layer {
    font-size: 2rem;
  }

  .subtitle-fragment {
    font-size: 1rem;
  }

  /* 移动端梵高分页样式 */
  .pagination-container {
    flex-direction: column;
    gap: 10px;
    padding: 12px 15px;
  }

  .pagination-controls {
    gap: 15px;
  }

  .page-navigation {
    gap: 6px;
  }

  .page-btn {
    padding: 6px 10px;
    font-size: 12px;
  }

  .page-number {
    min-width: 32px;
  }
}

/* 🔧 加载状态标准 - 符合CI_CD_STANDARDS.md */
.table-loading-container {
  display: flex;
  min-height: 300px;
  padding: 40px 0;
  color: var(--van-gogh-text-secondary);
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.loading-spinner-large {
  width: 32px;
  height: 32px;
  border: 3px solid rgb(139 92 246 / 30%);
  border-top: 3px solid var(--van-gogh-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 12px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 🎨 Toast通知组件样式 - 符合CI_CD_STANDARDS.md用户反馈规范 */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  max-width: 500px;
  min-width: 300px;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow:
    0 8px 32px rgb(0 0 0 / 20%),
    0 4px 16px rgb(0 0 0 / 10%);
  backdrop-filter: blur(10px);
  animation: slideInRight 0.3s ease-out;
}

.toast-success {
  border: 2px solid rgb(34 197 94 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(34 197 94 / 90%), rgb(22 163 74 / 90%));
}

.toast-error {
  border: 2px solid rgb(239 68 68 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(239 68 68 / 90%), rgb(220 38 38 / 90%));
}

.toast-warning {
  border: 2px solid rgb(245 158 11 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(245 158 11 / 90%), rgb(217 119 6 / 90%));
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toast-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.toast-message {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
