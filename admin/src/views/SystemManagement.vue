<template>
  <div class="picasso-system">
    <!-- 选项卡 -->
    <div class="tabs-cubism">
      <div
        v-for="tab in tabs"
        :key="tab.key"
        class="tab-cube"
        :class="{ active: activeTab === tab.key }"
        @click="activeTab = tab.key"
      >
        <div class="tab-face">
          <div class="tab-icon">{{ tab.icon }}</div>
          <div class="tab-text">{{ tab.label }}</div>
        </div>
      </div>
    </div>

    <!-- 内容区域 -->
    <div class="content-cubism">
      <!-- 基本设置 -->
      <div v-if="activeTab === 'basic'" class="settings-fragment">
        <div class="fragment-header">
          <div class="header-geometry">基本设置</div>
        </div>
        
        <div class="form-cubism">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">系统名称</label>
              <input 
                type="text" 
                v-model="basicSettings.systemName" 
                class="form-input"
                placeholder="请输入系统名称"
              aria-label="输入字段">
            </div>
            
            <div class="form-group">
              <label class="form-label">联系电话</label>
              <input 
                type="tel" 
                v-model="basicSettings.contactPhone" 
                class="form-input"
                placeholder="请输入联系电话"
              aria-label="输入字段">
            </div>
          </div>

          <div class="form-row full-width">
            <div class="form-group">
              <label class="form-label">营业地址</label>
              <input 
                type="text" 
                v-model="basicSettings.businessAddress" 
                class="form-input"
                placeholder="请输入营业地址"
              aria-label="输入字段">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">营业开始时间</label>
              <input 
                type="time" 
                v-model="basicSettings.openTime" 
                class="form-input"
              aria-label="输入字段">
            </div>
            
            <div class="form-group">
              <label class="form-label">营业结束时间</label>
              <input 
                type="time" 
                v-model="basicSettings.closeTime" 
                class="form-input"
              aria-label="输入字段">
            </div>
          </div>

          <div class="action-row">
            <div class="action-cube save" @click="saveBasicSettings">
              <div class="cube-face">保存设置</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 预约设置 -->
      <div v-if="activeTab === 'appointment'" class="settings-fragment">
        <div class="fragment-header">
          <div class="header-geometry">预约设置</div>
        </div>
        
        <div class="form-cubism">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">提前预约天数</label>
              <input 
                type="number" 
                v-model="appointmentSettings.advanceBookingDays" 
                class="form-input"
                placeholder="1-30天"
                min="1"
                max="30"
              aria-label="输入字段">
            </div>
            
            <div class="form-group">
              <label class="form-label">预约时间间隔</label>
              <select v-model="appointmentSettings.timeInterval" class="form-select">
                <option value="15">15分钟</option>
                <option value="30">30分钟</option>
                <option value="60">60分钟</option>
              </select>
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">允许取消预约</label>
              <div class="switch-geometry">
                <input 
                  type="checkbox" 
                  v-model="appointmentSettings.allowCancellation"
                  class="geometric-switch"
                  id="allowCancellation"
                />
                <label for="allowCancellation" class="switch-label">
                  {{ appointmentSettings.allowCancellation ? '允许' : '禁止' }}
                </label>
              </div>
            </div>
            
            <div class="form-group">
              <label class="form-label">取消提前时间</label>
              <input 
                type="number" 
                v-model="appointmentSettings.cancellationHours" 
                class="form-input"
                placeholder="小时"
                min="1"
                max="48"
              aria-label="输入字段">
            </div>
          </div>

          <div class="action-row">
            <div class="action-cube save" @click="saveAppointmentSettings">
              <div class="cube-face">保存设置</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 通知设置 -->
      <div v-if="activeTab === 'notification'" class="settings-fragment">
        <div class="fragment-header">
          <div class="header-geometry">通知设置</div>
        </div>
        
        <div class="notification-cubism">
          <div 
            v-for="item in notificationSettings" 
            :key="item.key"
            class="notification-cube"
          >
            <div class="notification-info">
              <div class="notification-title">{{ item.title }}</div>
              <div class="notification-desc">{{ item.description }}</div>
            </div>
            <div class="notification-switch">
              <input 
                type="checkbox" 
                v-model="item.enabled"
                class="geometric-switch"
                :id="item.key"
              />
              <label :for="item.key" class="switch-label">
                {{ item.enabled ? '开启' : '关闭' }}
              </label>
            </div>
          </div>

          <div class="action-row">
            <div class="action-cube save" @click="saveNotificationSettings">
              <div class="cube-face">保存设置</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 安全设置 -->
      <div v-if="activeTab === 'security'" class="settings-fragment">
        <div class="fragment-header">
          <div class="header-geometry">安全设置</div>
        </div>
        
        <div class="form-cubism">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">会话超时时间</label>
              <select v-model="securitySettings.sessionTimeout" class="form-select">
                <option value="30">30分钟</option>
                <option value="60">1小时</option>
                <option value="120">2小时</option>
                <option value="240">4小时</option>
              </select>
            </div>
            
            <div class="form-group">
              <label class="form-label">密码最小长度</label>
              <input 
                type="number" 
                v-model="securitySettings.minPasswordLength" 
                class="form-input"
                min="6"
                max="20"
              aria-label="输入字段">
            </div>
          </div>

          <div class="form-row">
            <div class="form-group">
              <label class="form-label">启用双因子认证</label>
              <div class="switch-geometry">
                <input 
                  type="checkbox" 
                  v-model="securitySettings.twoFactorAuth"
                  class="geometric-switch"
                  id="twoFactorAuth"
                />
                <label for="twoFactorAuth" class="switch-label">
                  {{ securitySettings.twoFactorAuth ? '启用' : '禁用' }}
                </label>
              </div>
            </div>
            
            <div class="form-group">
              <label class="form-label">登录失败锁定</label>
              <div class="switch-geometry">
                <input 
                  type="checkbox" 
                  v-model="securitySettings.loginLockout"
                  class="geometric-switch"
                  id="loginLockout"
                />
                <label for="loginLockout" class="switch-label">
                  {{ securitySettings.loginLockout ? '启用' : '禁用' }}
                </label>
              </div>
            </div>
          </div>

          <div class="action-row">
            <div class="action-cube save" @click="saveSecuritySettings">
              <div class="cube-face">保存设置</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 系统工具 -->
      <div v-if="activeTab === 'tools'" class="settings-fragment">
        <div class="fragment-header">
          <div class="header-geometry">系统工具</div>
        </div>

        <div class="tools-cubism">
          <div class="tool-cube" @click="goToSystemLogs">
            <div class="tool-face">
              <div class="tool-icon">📋</div>
              <div class="tool-title">系统日志</div>
              <div class="tool-desc">查看系统运行日志和错误记录</div>
            </div>
          </div>

          <div class="tool-cube" @click="goToHealthTips">
            <div class="tool-face">
              <div class="tool-icon">💡</div>
              <div class="tool-title">健康小贴士</div>
              <div class="tool-desc">管理健康知识和保健建议</div>
            </div>
          </div>

          <div class="tool-cube" @click="goToFinanceRecords">
            <div class="tool-face">
              <div class="tool-icon">💰</div>
              <div class="tool-title">财务记录</div>
              <div class="tool-desc">查看详细的收支流水记录</div>
            </div>
          </div>

          <div class="tool-cube" @click="goToFinanceReports">
            <div class="tool-face">
              <div class="tool-icon">📊</div>
              <div class="tool-title">财务报表</div>
              <div class="tool-desc">生成和查看财务分析报表</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

// 防抖函数 - 性能优化
let debounceTimer = null;
const debounce = (func, delay = 300) => {
  return (...args) => {
    if (debounceTimer) {
      clearTimeout(debounceTimer);
    }
    debounceTimer = setTimeout(() => func.apply(this, args), delay);
  };
};

import { ref, reactive , nextTick, shallowRef, watchEffect } from 'vue';;
import { useRouter } from 'vue-router';

// 路由
const router = useRouter();

// 选项卡配置
const tabs = [
  { key: 'basic', label: '基本设置', icon: '⚙️' },
  { key: 'appointment', label: '预约设置', icon: '📅' },
  { key: 'notification', label: '通知设置', icon: '🔔' },
  { key: 'security', label: '安全设置', icon: '🔒' },
  { key: 'tools', label: '系统工具', icon: '🛠️' }
];

// 当前选项卡
const activeTab = ref('basic');

// 基本设置
const basicSettings = reactive({
  systemName: '壹心堂中医理疗管理系统',
  contactPhone: '************',
  businessAddress: '北京市朝阳区xxx街道xxx号',
  openTime: '09:00',
  closeTime: '21:00'
});

// 预约设置
const appointmentSettings = reactive({
  advanceBookingDays: 7,
  timeInterval: 30,
  allowCancellation: true,
  cancellationHours: 2
});

// 通知设置
const notificationSettings = reactive([
  {
    key: 'appointment_reminder',
    title: '预约提醒',
    description: '在预约时间前发送提醒通知',
    enabled: true
  },
  {
    key: 'payment_notification',
    title: '支付通知',
    description: '支付成功后发送确认通知',
    enabled: true
  },
  {
    key: 'system_maintenance',
    title: '系统维护',
    description: '系统维护时发送通知',
    enabled: false
  }
]);

// 安全设置
const securitySettings = reactive({
  sessionTimeout: 60,
  minPasswordLength: 8,
  twoFactorAuth: false,
  loginLockout: true
});

// 保存方法
const saveBasicSettings = () => {
  console.log('保存基本设置:', basicSettings);
};

const saveAppointmentSettings = () => {
  console.log('保存预约设置:', appointmentSettings);
};

const saveNotificationSettings = () => {
  console.log('保存通知设置:', notificationSettings);
};

const saveSecuritySettings = () => {
  console.log('保存安全设置:', securitySettings);
};

// 导航方法
const goToSystemLogs = () => {
  router.push('/system/logs');
};

const goToHealthTips = () => {
  router.push('/health-tips');
};

const goToFinanceRecords = () => {
  router.push('/finance/records');
};

const goToFinanceReports = () => {
  router.push('/finance/reports');
};
</script>

<style scoped>
/* 毕加索风格系统管理 - 占满主内容区 */
.picasso-system {
  display: flex;
  position: fixed;
  inset: 0 0 0 180px; /* 侧边栏宽度 */
  width: calc(100vw - 180px);
  height: 100vh;
  padding: 30px; /* 统一主内容区边距 */
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;

  /* 设置与全局背景相同的梵高风格紫色渐变，调整背景位置以匹配全局背景 */
  background: linear-gradient(180deg,
    #2d1b69 0%,    /* 深紫色（梵高星夜风格） */
    #3730a3 15%,   /* 靛蓝紫 */
    #4338ca 30%,   /* 中紫色 */
    #5b21b6 45%,   /* 深紫色 */
    #6b21a8 60%,   /* 紫色 */
    #7c2d92 75%,   /* 紫红色 */
    #86198f 90%,   /* 深紫红 */
    #701a75 100%   /* 最深紫色 */
  ) !important;
  background-attachment: fixed; /* 固定背景，避免位移 */
  flex-direction: column;
}

@keyframes picassoFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 0% 50%; }
  100% { background-position: 50% 0%; }
}

/* 已删除无用的标题样式 */

/* 毕加索风格选项卡 */
.tabs-cubism {
  display: flex;
  position: relative;
  z-index: 10;
  max-width: 800px; /* 限制最大宽度，避免选项卡过宽 */
  gap: 15px;
  justify-content: space-between; /* 改为均匀分布，确保宽度一致 */
  margin-bottom: 20px;
  flex-shrink: 0;
  margin-left: auto;
  margin-right: auto;
}

.tab-cube {
  max-width: 180px; /* 最大宽度确保不会太宽 */
  min-width: 120px; /* 最小宽度确保内容不会太挤 */
  transform: perspective(600px) rotateX(10deg);
  transition: all 0.3s ease;
  cursor: pointer;
  flex: 1; /* 统一宽度：每个选项卡占用相等空间 */
}

.tab-cube:hover {
  transform: perspective(600px) rotateX(10deg) scale(1.05);
}

.tab-cube.active {
  transform: perspective(600px) rotateX(10deg) scale(1.1);
}

.tab-face {
  display: flex;
  width: 100%; /* 占满父容器宽度 */
  padding: 12px 20px;
  border: 3px solid;
  border-radius: 15px 5px;
  box-sizing: border-box; /* 包含padding和border在内的宽度计算 */
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(255 255 255 / 90%), rgb(255 255 255 / 70%));
  box-shadow: 0 8px 20px rgb(0 0 0 / 20%);
  align-items: center;
  justify-content: center; /* 内容居中对齐 */
  gap: 8px;
}

.tab-cube:nth-child(1) .tab-face {
  border-color: #ff6b6b;
}

.tab-cube:nth-child(2) .tab-face {
  border-color: #4ecdc4;
}

.tab-cube:nth-child(3) .tab-face {
  border-color: #f39c12;
}

.tab-cube:nth-child(4) .tab-face {
  border-color: #27ae60;
}

.tab-cube.active .tab-face {
  background: linear-gradient(135deg, rgb(255 255 255 / 100%), rgb(255 255 255 / 90%));
  box-shadow: 0 12px 30px rgb(0 0 0 / 30%);
}

.tab-icon {
  font-size: 1.2rem;
  transform: rotate(5deg);
}

.tab-text {
  font-size: 0.9rem;
  transform: skew(-2deg);
}

/* 毕加索风格内容区域 */
.content-cubism {
  position: relative;
  z-index: 5;
  padding: 25px; /* 减少内容区边距，避免与主容器双重边距 */
  border-radius: 20px;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 20px 40px rgb(32 178 170 / 30%);
  transform: perspective(1000px) rotateX(3deg);
  flex: 1;
  overflow-y: auto;
}

.settings-fragment {
  animation: fragmentSlide 0.5s ease-out;
}

@keyframes fragmentSlide {
  from {
    opacity: 0;
    transform: translateX(-20px) skew(-5deg);
  }

  to {
    opacity: 1;
    transform: translateX(0) skew(0deg);
  }
}

.fragment-header {
  margin-bottom: 20px;
}

.header-geometry {
  display: inline-block;
  padding: 10px 20px;
  border-radius: 0 15px;
  font-size: 1.5rem;
  font-weight: 900;
  color: #2c3e50;
  background: linear-gradient(135deg,
    rgb(200 162 200 / 30%),
    rgb(32 178 170 / 20%),
    rgb(255 255 255 / 80%)
  );
  transform: skew(-3deg);
  text-transform: uppercase;
  letter-spacing: 2px;
  border-left: 5px solid #c8a2c8;
}

/* 毕加索风格表单 */
.form-cubism {
  transform: perspective(600px) rotateX(2deg);
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-row.full-width .form-group {
  flex: 1;
}

.form-group {
  flex: 1;
  position: relative;
}

.form-label {
  display: block;
  width: fit-content;
  padding: 4px 10px;
  border-radius: 8px 0;
  font-size: 0.9rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(200 162 200 / 80%), rgb(255 255 255 / 90%));
  transform: skew(-3deg);
  margin-bottom: 8px;
}

.form-input, .form-select {
  width: 100%;
  padding: 12px 15px;
  border: none;
  border-radius: 0 12px;
  font-size: 1rem;
  font-weight: 600;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(200 162 200 / 80%), rgb(255 255 255 / 90%));
  box-shadow: 3px 3px 10px rgb(0 0 0 / 10%);
  transform: skew(-1deg);
  transition: all 0.3s ease;
}

.form-input:focus, .form-select:focus {
  background: linear-gradient(135deg, rgb(200 162 200 / 90%), rgb(255 255 255 / 95%));
  box-shadow: 5px 5px 15px rgb(65 105 225 / 30%);
  transform: skew(-1deg) scale(1.02);
  outline: none;
}

/* 毕加索风格开关 */
.switch-geometry {
  display: flex;
  align-items: center;
  gap: 10px;
}

.geometric-switch {
  position: relative;
  width: 50px;
  height: 25px;
  border-radius: 25px;
  background: linear-gradient(45deg, #bdc3c7, #95a5a6);
  transform: skew(-5deg);
  transition: all 0.3s ease;
  appearance: none;
  cursor: pointer;
}

.geometric-switch:checked {
  background: linear-gradient(45deg, #c8a2c8, #20b2aa);
}

.geometric-switch::before {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 21px;
  height: 21px;
  border-radius: 50%;
  background: white;
  box-shadow: 0 2px 5px rgb(0 0 0 / 20%);
  transition: all 0.3s ease;
  content: '';
}

.geometric-switch:checked::before {
  transform: translateX(25px);
}

.switch-label {
  font-weight: bold;
  color: #2c3e50;
  transform: skew(2deg);
}

/* 毕加索风格通知设置 */
.notification-cubism {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.notification-cube {
  display: flex;
  padding: 15px;
  border-radius: 12px 0;
  background: linear-gradient(135deg,
    rgb(200 162 200 / 10%),
    rgb(32 178 170 / 10%),
    rgb(255 255 255 / 80%)
  );
  transform: skew(-1deg);
  transition: all 0.3s ease;
  justify-content: space-between;
  align-items: center;
  border-left: 4px solid;
}

.notification-cube:nth-child(3n+1) {
  border-color: #c8a2c8;
}

.notification-cube:nth-child(3n+2) {
  border-color: #20b2aa;
}

.notification-cube:nth-child(3n+3) {
  border-color: #4169e1;
}

.notification-cube:hover {
  box-shadow: 0 5px 15px rgb(32 178 170 / 20%);
  transform: skew(-1deg) scale(1.02);
}

.notification-info {
  flex: 1;
}

.notification-title {
  font-weight: bold;
  color: #2c3e50;
  margin-bottom: 4px;
  transform: skew(1deg);
}

.notification-desc {
  font-size: 0.9rem;
  color: #7f8c8d;
  transform: skew(-1deg);
}

.notification-switch {
  margin-left: 20px;
}

/* 毕加索风格操作按钮 */
.action-row {
  margin-top: 30px;
  display: flex;
  justify-content: center;
}

.action-cube {
  transform: perspective(600px) rotateX(15deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-cube:hover {
  transform: perspective(600px) rotateX(15deg) scale(1.05);
}

.action-cube.save {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
}

.cube-face {
  padding: 15px 30px;
  border-radius: 15px 0;
  font-size: 1.1rem;
  font-weight: bold;
  color: white;
  box-shadow: 0 8px 20px rgb(0 0 0 / 30%);
  letter-spacing: 1px;
}

/* 响应式设计 */
@media (width <= 768px) {
  .picasso-system {
    left: 0;
    width: 100vw;
    padding: 15px;
  }

  .tabs-cubism {
    flex-wrap: wrap;
    gap: 10px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .notification-cube {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .notification-switch {
    margin-left: 0;
  }

  .title-layer {
    font-size: 2rem;
  }

  .subtitle-fragment {
    font-size: 1rem;
  }

  .tools-cubism {
    grid-template-columns: 1fr;
  }
}

/* 毕加索风格系统工具 */
.tools-cubism {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  padding: 20px 0;
}

.tool-cube {
  height: 120px;
  border: 2px solid rgb(65 105 225 / 40%);
  border-radius: 15px 0;
  background: linear-gradient(135deg,
    rgb(200 162 200 / 30%),
    rgb(32 178 170 / 30%),
    rgb(65 105 225 / 30%)
  );
  transform: perspective(600px) rotateX(10deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.tool-cube:hover {
  box-shadow: 0 10px 25px rgb(32 178 170 / 40%);
  transform: perspective(600px) rotateX(10deg) scale(1.05);
}

.tool-cube:nth-child(1) {
  border-color: #ff6b6b;
  background: linear-gradient(135deg,
    rgb(255 107 107 / 30%),
    rgb(255 255 255 / 80%)
  );
}

.tool-cube:nth-child(2) {
  border-color: #4ecdc4;
  background: linear-gradient(135deg,
    rgb(78 205 196 / 30%),
    rgb(255 255 255 / 80%)
  );
}

.tool-cube:nth-child(3) {
  border-color: #f39c12;
  background: linear-gradient(135deg,
    rgb(243 156 18 / 30%),
    rgb(255 255 255 / 80%)
  );
}

.tool-cube:nth-child(4) {
  border-color: #27ae60;
  background: linear-gradient(135deg,
    rgb(39 174 96 / 30%),
    rgb(255 255 255 / 80%)
  );
}

.tool-face {
  display: flex;
  width: 100%;
  height: 100%;
  padding: 15px;
  border-radius: 15px 0;
  text-align: center;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.tool-icon {
  font-size: 2rem;
  transform: rotate(5deg);
}

.tool-title {
  font-size: 1.1rem;
  font-weight: bold;
  color: #2c3e50;
  transform: skew(-2deg);
}

.tool-desc {
  font-size: 0.8rem;
  line-height: 1.3;
  color: #7f8c8d;
  transform: skew(1deg);
}
</style>
