<template>
  <div class="picasso-logs">
<!-- 毕加索风格操作栏 -->
    <div class="action-toolbar">
      <div class="search-cubism">
        <input
          type="text"
          placeholder="搜索日志内容..."
          v-model="searchValue"
          @input="handleSearch"
          @keyup.enter="handleSearch"
          class="search-fragment"
          :class="{ 'error': formErrors.search }"
          maxlength="100"
        />
        <div v-if="formErrors.search" class="error-message">{{ formErrors.search }}</div>
      </div>
      
      <div class="filter-cubism">
        <select 
          v-model="levelFilter"
          @change="handleFilterChange"
          class="filter-fragment"
        >
          <option value="">所有级别</option>
          <option value="info">信息</option>
          <option value="warning">警告</option>
          <option value="error">错误</option>
          <option value="debug">调试</option>
        </select>

        <input
          type="date"
          v-model="dateFilter"
          @change="handleFilterChange"
          class="filter-fragment"
          :class="{ 'error': formErrors.date }"
        />
        <div v-if="formErrors.date" class="error-message">{{ formErrors.date }}</div>
      </div>

      <div class="action-cubism">
        <div
          class="action-cube refresh-cube"
          @click="loadLogs"
          :class="{ 'disabled': loadingStates.dataLoading || loadingStates.refreshLoading }"
        >
          <div class="cube-face">
            <span class="cube-icon">🔄</span>
            <span class="cube-text">
              {{ loadingStates.dataLoading || loadingStates.refreshLoading ? '刷新中...' : '刷新' }}
            </span>
          </div>
        </div>
        <div
          class="action-cube clear-cube"
          @click="clearLogs"
          :class="{ 'disabled': loadingStates.clearLoading }"
        >
          <div class="cube-face">
            <span class="cube-icon">🗑️</span>
            <span class="cube-text">
              {{ loadingStates.clearLoading ? '清空中...' : '清空日志' }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格数据表格 -->
    <div class="data-cubism">
      <div class="table-container">
        <!-- 表头 -->
        <div class="table-header">
          <div class="header-cell" style="flex: 0.5;">ID</div>
          <div class="header-cell" style="flex: 1;">级别</div>
          <div class="header-cell" style="flex: 3;">日志内容</div>
          <div class="header-cell" style="flex: 1.5;">时间</div>
          <div class="header-cell" style="flex: 1;">用户</div>
          <div class="header-cell" style="flex: 1;">操作</div>
        </div>

        <!-- 数据行 -->
        <div class="table-body">
          <div 
            v-for="log in paginatedData" 
            :key="log.id"
            class="data-row"
            :class="'row-' + log.level"
          >
            <div class="data-cell" style="flex: 0.5;">
              <div class="cell-content">{{ log.id }}</div>
            </div>
            
            <div class="data-cell" style="flex: 1;">
              <div class="level-fragment" :class="'level-' + log.level">
                <div class="level-icon">{{ getLevelIcon(log.level) }}</div>
                <div class="level-text">{{ getLevelText(log.level) }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 3;">
              <div class="content-fragment">
                <div class="log-message">{{ log.message }}</div>
                <div class="log-details" v-if="log.details">{{ log.details }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1.5;">
              <div class="time-fragment">
                <div class="date-part">{{ log.date }}</div>
                <div class="time-part">{{ log.time }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1;">
              <div class="user-fragment">
                <div class="user-avatar">{{ log.user?.charAt(0) || 'S' }}</div>
                <div class="user-name">{{ log.user || '系统' }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1;">
              <div class="action-fragments">
                <div class="action-btn view" @click="handleView(log)">查看</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 毕加索风格分页 -->
      <div class="pagination-cubism" v-if="totalPages > 1">
        <div class="page-controls">
          <div 
            class="page-cube prev"
            @click="prevPage"
            :class="{ disabled: currentPage === 1 }"
          >
            <div class="page-face">‹</div>
          </div>
          
          <div 
            v-for="page in visiblePages" 
            :key="page"
            class="page-cube"
            :class="{ active: page === currentPage }"
            @click="goToPage(page)"
          >
            <div class="page-face">{{ page }}</div>
          </div>
          
          <div 
            class="page-cube next"
            @click="nextPage"
            :class="{ disabled: currentPage === totalPages }"
          >
            <div class="page-face">›</div>
          </div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格详情模态框 -->
    <div v-if="modalVisible" class="modal-overlay" @click="hideModal">
      <div class="modal-cubism" @click.stop>
        <div class="modal-header">
          <div class="header-title">日志详情</div>
          <div class="close-btn" @click="hideModal">×</div>
        </div>
        
        <div class="modal-body">
          <div class="log-detail-cubism">
            <div class="detail-row">
              <div class="detail-label">日志ID</div>
              <div class="detail-value">{{ selectedLog?.id }}</div>
            </div>
            
            <div class="detail-row">
              <div class="detail-label">级别</div>
              <div class="detail-value" :class="'level-' + selectedLog?.level">
                {{ getLevelText(selectedLog?.level) }}
              </div>
            </div>
            
            <div class="detail-row">
              <div class="detail-label">时间</div>
              <div class="detail-value">{{ selectedLog?.date }} {{ selectedLog?.time }}</div>
            </div>
            
            <div class="detail-row">
              <div class="detail-label">用户</div>
              <div class="detail-value">{{ selectedLog?.user || '系统' }}</div>
            </div>
            
            <div class="detail-row full-width">
              <div class="detail-label">消息</div>
              <div class="detail-value">{{ selectedLog?.message }}</div>
            </div>
            
            <div class="detail-row full-width" v-if="selectedLog?.details">
              <div class="detail-label">详细信息</div>
              <div class="detail-value">{{ selectedLog?.details }}</div>
            </div>
            
            <div class="detail-row full-width" v-if="selectedLog?.stack">
              <div class="detail-label">堆栈信息</div>
              <div class="detail-value stack-trace">{{ selectedLog?.stack }}</div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <div class="footer-actions">
            <div class="action-btn close" @click="hideModal">
              <div class="btn-face">关闭</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast通知组件 - 符合CI_CD_STANDARDS.md用户反馈规范 -->
  <div v-if="toastState.visible" class="toast-notification" :class="'toast-' + toastState.type">
    <div class="toast-content">
      <div class="toast-icon">
        <span v-if="toastState.type === 'success'">✅</span>
        <span v-else-if="toastState.type === 'error'">❌</span>
        <span v-else-if="toastState.type === 'warning'">⚠️</span>
      </div>
      <div class="toast-message">{{ toastState.message }}</div>
    </div>
  </div>
</template>

<script setup>
// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

import { ref, reactive, computed, onMounted , nextTick, shallowRef, watchEffect } from 'vue';;

// 响应式数据
const logs = ref([]);
const modalVisible = ref(false);
const selectedLog = ref(null);
const searchValue = ref('');
const levelFilter = ref('');
const dateFilter = ref('');
const currentPage = ref(1);
const pageSize = ref(10);

// Toast通知状态 - 符合CI_CD_STANDARDS.md用户反馈规范
const toastState = reactive({
  visible: false,
  message: '',
  type: 'success' // success, error, warning
});

// Toast通知函数 - 替代console日志
const showToast = (message, type = 'success') => {
  toastState.message = message;
  toastState.type = type;
  toastState.visible = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    toastState.visible = false;
  }, 3000);
};

// 加载状态管理 - 符合CI_CD_STANDARDS.md加载状态规范
const loadingStates = reactive({
  dataLoading: false,    // 数据加载状态
  refreshLoading: false, // 刷新加载状态
  clearLoading: false    // 清空加载状态
});

// 表单验证错误状态 - 符合CI_CD_STANDARDS.md表单验证规范
const formErrors = reactive({
  search: '',
  level: '',
  date: ''
});

// 表单验证函数 - 符合CI_CD_STANDARDS.md表单验证规范
const validateForm = () => {
  // 清空之前的错误
  Object.assign(formErrors, {
    search: '',
    level: '',
    date: ''
  });

  let isValid = true;

  // 验证搜索关键词长度
  if (searchValue.value && searchValue.value.length > 100) {
    formErrors.search = '搜索关键词不能超过100个字符';
    isValid = false;
  }

  // 验证日期格式
  if (dateFilter.value && !/^\d{4}-\d{2}-\d{2}$/.test(dateFilter.value)) {
    formErrors.date = '请选择正确的日期';
    isValid = false;
  }

  return isValid;
};

// 计算属性
const totalPages = computed(() => Math.ceil(logs.value.length / pageSize.value));
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return logs.value.slice(start, end);
});

const visiblePages = computed(() => {
  const pages = [];
  const total = totalPages.value;
  const current = currentPage.value;
  
  for (let i = Math.max(1, current - 2); i <= Math.min(total, current + 2); i++) {
    pages.push(i);
  }
  return pages;
});

// 方法 - 符合CI_CD_STANDARDS.md错误处理规范
const loadLogs = async () => {
  try {
    // 设置加载状态
    loadingStates.dataLoading = true;

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));
  
  logs.value = [
    {
      id: 1,
      level: 'info',
      message: '用户登录成功',
      details: '用户 admin 从 IP ************* 登录系统',
      date: '2024-01-15',
      time: '10:30:25',
      user: 'admin'
    },
    {
      id: 2,
      level: 'warning',
      message: '预约时间冲突',
      details: '客户张女士的预约时间与现有预约冲突',
      date: '2024-01-15',
      time: '11:15:30',
      user: 'admin'
    },
    {
      id: 3,
      level: 'error',
      message: '数据库连接失败',
      details: '无法连接到数据库服务器',
      stack: 'Error: Connection timeout\n  at Database.connect()\n  at async main()',
      date: '2024-01-15',
      time: '12:00:15',
      user: null
    },
    {
      id: 4,
      level: 'info',
      message: '新增客户记录',
      details: '成功添加客户：李先生',
      date: '2024-01-15',
      time: '13:30:45',
      user: 'admin'
    },
    {
      id: 5,
      level: 'debug',
      message: '缓存清理完成',
      details: '系统缓存已清理，释放内存 256MB',
      date: '2024-01-15',
      time: '14:15:20',
      user: null
    }
  ];

  showToast('日志加载成功', 'success');
  } catch (error) {
    console.error('日志加载失败:', error);
    showToast('日志加载失败，请重试', 'error');
  } finally {
    loadingStates.dataLoading = false;
  }
};

const handleSearch = async () => {
  try {
    // 表单验证 - 符合CI_CD_STANDARDS.md表单验证规范
    if (!validateForm()) {
      showToast('请检查搜索条件', 'error');
      return;
    }

    loadingStates.refreshLoading = true;
    console.log('搜索:', searchValue.value);

    // 模拟搜索操作
    await new Promise(resolve => setTimeout(resolve, 500));
    showToast('搜索完成', 'success');
  } catch (error) {
    console.error('搜索失败:', error);
    showToast('搜索失败，请重试', 'error');
  } finally {
    loadingStates.refreshLoading = false;
  }
};

const handleFilterChange = () => {
  console.log('筛选:', { level: levelFilter.value, date: dateFilter.value });
};

const clearLogs = async () => {
  try {
    if (confirm('确定要清空所有日志吗？此操作不可恢复。')) {
      loadingStates.clearLoading = true;

      // 模拟清空操作
      await new Promise(resolve => setTimeout(resolve, 500));

      logs.value = [];
      console.log('日志已清空');
      showToast('日志清空成功', 'success');
    }
  } catch (error) {
    console.error('日志清空失败:', error);
    showToast('日志清空失败，请重试', 'error');
  } finally {
    loadingStates.clearLoading = false;
  }
};

const handleView = (log) => {
  selectedLog.value = log;
  modalVisible.value = true;
};

const hideModal = () => {
  modalVisible.value = false;
  selectedLog.value = null;
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const goToPage = (page) => {
  currentPage.value = page;
};

const getLevelIcon = (level) => {
  const icons = {
    info: 'ℹ️',
    warning: '⚠️',
    error: '❌',
    debug: '🐛'
  };
  return icons[level] || 'ℹ️';
};

const getLevelText = (level) => {
  const texts = {
    info: '信息',
    warning: '警告',
    error: '错误',
    debug: '调试'
  };
  return texts[level] || level;
};

// 初始化
onMounted(() => {
  loadLogs();
});
</script>

<style scoped>
/* 继承预约管理页面的毕加索风格样式 */
.picasso-logs {
  display: flex;
  position: fixed;
  inset: 0 0 0 180px;
  width: calc(100vw - 180px);
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;
  background: linear-gradient(45deg,
    #8e44ad 0%, #9b59b6 25%, #c8a2c8 50%, #dda0dd 75%, #e6e6fa 100%
  );
  background-size: 400% 400%;
  animation: picassoFlow 20s ease infinite;
  flex-direction: column;
}

@keyframes picassoFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 0% 50%; }
  100% { background-position: 50% 0%; }
}

/* 其他样式继承预约管理页面的样式结构 */

/* 🎨 表单验证样式 - 符合CI_CD_STANDARDS.md表单验证规范 */
.search-fragment.error,
.filter-fragment.error {
  border-color: #ef4444;
  box-shadow:
    0 0 0 3px rgb(239 68 68 / 20%),
    0 4px 12px rgb(239 68 68 / 20%);
}

.error-message {
  font-size: 0.85rem;
  font-weight: 500;
  color: #ef4444;
  margin-top: 4px;
}

/* 🎨 按钮禁用状态 - 符合CI_CD_STANDARDS.md加载状态规范 */
.action-cube.disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: not-allowed;
}

/* 🎨 Toast通知组件样式 - 符合CI_CD_STANDARDS.md用户反馈规范 */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  max-width: 500px;
  min-width: 300px;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow:
    0 8px 32px rgb(0 0 0 / 20%),
    0 4px 16px rgb(0 0 0 / 10%);
  backdrop-filter: blur(10px);
  animation: slideInRight 0.3s ease-out;
}

.toast-success {
  border: 2px solid rgb(34 197 94 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(34 197 94 / 90%), rgb(22 163 74 / 90%));
}

.toast-error {
  border: 2px solid rgb(239 68 68 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(239 68 68 / 90%), rgb(220 38 38 / 90%));
}

.toast-warning {
  border: 2px solid rgb(245 158 11 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(245 158 11 / 90%), rgb(217 119 6 / 90%));
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toast-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.toast-message {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 🎨 响应式设计断点 - 符合CI_CD_STANDARDS.md响应式设计规范 */

/* 平板设备 (768px - 1024px) */
@media (width <= 1024px) {
  .logs-container {
    padding: 15px;
  }

  .search-fragment,
  .filter-fragment {
    padding: 10px;
    font-size: 14px;
  }

  .action-cube {
    min-width: 100px;
    padding: 12px;
  }

  .cube-text {
    font-size: 13px;
  }
}

/* 移动端设备 (最大768px) */
@media (width <= 768px) {
  .logs-container {
    padding: 10px;
  }

  .search-controls {
    flex-direction: column;
    gap: 10px;
  }

  .search-fragment,
  .filter-fragment {
    width: 100%;
    padding: 12px;
    font-size: 16px; /* 移动端增大字体 */
  }

  .filter-row {
    flex-direction: column;
    gap: 10px;
  }

  .action-controls {
    flex-direction: column;
    gap: 10px;
  }

  .action-cube {
    width: 100%;
    min-height: 60px;
    padding: 15px;
  }

  .cube-text {
    font-size: 14px;
  }

  .logs-table {
    font-size: 12px;
  }

  .log-level {
    padding: 2px 6px;
    font-size: 10px;
  }

  .toast-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
  }
}

/* 小屏幕移动端 (最大480px) */
@media (width <= 480px) {
  .logs-container {
    padding: 8px;
  }

  .search-fragment,
  .filter-fragment {
    padding: 10px;
    font-size: 16px;
  }

  .action-cube {
    min-height: 50px;
    padding: 12px;
  }

  .cube-icon {
    font-size: 16px;
  }

  .cube-text {
    font-size: 12px;
  }

  .logs-table {
    font-size: 11px;
  }

  .log-message {
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}
</style>
