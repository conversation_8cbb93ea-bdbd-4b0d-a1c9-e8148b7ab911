<template>
  <div class="picasso-reports">
<!-- 毕加索风格报表选择 -->
    <div class="report-selector-cubism">
      <div 
        v-for="report in reportTypes" 
        :key="report.key"
        class="report-cube"
        :class="{ active: activeReport === report.key }"
        @click="activeReport = report.key"
      >
        <div class="report-face">
          <div class="report-icon">{{ report.icon }}</div>
          <div class="report-text">{{ report.label }}</div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格报表内容 -->
    <div class="report-content-cubism">
      <!-- 收入报表 -->
      <div v-if="activeReport === 'income'" class="report-fragment">
        <div class="fragment-header">
          <div class="header-geometry">收入分析报表</div>
        </div>
        
        <div class="report-charts">
          <div class="chart-cube income-trend">
            <div class="chart-header">月度收入趋势</div>
            <div class="chart-body">
              <div class="line-chart">
                <div class="chart-line"></div>
                <div class="chart-points">
                  <div class="point" v-for="i in 12" :key="i" 
                       :style="{ left: (i-1) * 8.33 + '%', bottom: Math.random() * 60 + 20 + '%' }">
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="chart-cube income-category">
            <div class="chart-header">收入来源分布</div>
            <div class="chart-body">
              <div class="pie-chart">
                <div class="pie-slice" style="

--angle: 180deg; --color: #c8a2c8;"></div>
                <div class="pie-slice" style="

--angle: 120deg; --color: #20b2aa;"></div>
                <div class="pie-slice" style="

--angle: 60deg; --color: #4169e1;"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 支出报表 -->
      <div v-if="activeReport === 'expense'" class="report-fragment">
        <div class="fragment-header">
          <div class="header-geometry">支出分析报表</div>
        </div>
        
        <div class="report-charts">
          <div class="chart-cube expense-breakdown">
            <div class="chart-header">支出分类统计</div>
            <div class="chart-body">
              <div class="bar-chart">
                <div class="bar" style="height: 80%;">
                  <div class="bar-label">房租</div>
                </div>
                <div class="bar" style="height: 60%;">
                  <div class="bar-label">工资</div>
                </div>
                <div class="bar" style="height: 40%;">
                  <div class="bar-label">设备</div>
                </div>
                <div class="bar" style="height: 30%;">
                  <div class="bar-label">其他</div>
                </div>
              </div>
            </div>
          </div>
          
          <div class="chart-cube expense-trend">
            <div class="chart-header">支出趋势分析</div>
            <div class="chart-body">
              <div class="area-chart">
                <div class="area-fill"></div>
                <div class="area-line"></div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 利润报表 -->
      <div v-if="activeReport === 'profit'" class="report-fragment">
        <div class="fragment-header">
          <div class="header-geometry">利润分析报表</div>
        </div>
        
        <div class="profit-summary">
          <div class="summary-cube gross-profit">
            <div class="summary-icon">📊</div>
            <div class="summary-value">¥{{ profitData.grossProfit }}</div>
            <div class="summary-label">毛利润</div>
          </div>
          
          <div class="summary-cube net-profit">
            <div class="summary-icon">💰</div>
            <div class="summary-value">¥{{ profitData.netProfit }}</div>
            <div class="summary-label">净利润</div>
          </div>
          
          <div class="summary-cube profit-margin">
            <div class="summary-icon">📈</div>
            <div class="summary-value">{{ profitData.profitMargin }}%</div>
            <div class="summary-label">利润率</div>
          </div>
        </div>
      </div>

      <!-- 对比报表 -->
      <div v-if="activeReport === 'comparison'" class="report-fragment">
        <div class="fragment-header">
          <div class="header-geometry">同期对比报表</div>
        </div>
        
        <div class="comparison-charts">
          <div class="chart-cube year-comparison">
            <div class="chart-header">年度对比</div>
            <div class="chart-body">
              <div class="comparison-bars">
                <div class="comparison-group">
                  <div class="bar current" style="height: 70%;">
                    <div class="bar-value">今年</div>
                  </div>
                  <div class="bar previous" style="height: 60%;">
                    <div class="bar-value">去年</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格导出操作 -->
    <div class="export-actions-cubism">
      <div
        class="export-cube"
        @click="exportPDF"
        :class="{ 'disabled': loadingStates.pdfExport }"
      >
        <div class="export-face">
          <div class="export-icon">📄</div>
          <div class="export-text">
            {{ loadingStates.pdfExport ? '导出中...' : '导出PDF' }}
          </div>
        </div>
      </div>

      <div
        class="export-cube"
        @click="exportExcel"
        :class="{ 'disabled': loadingStates.excelExport }"
      >
        <div class="export-face">
          <div class="export-icon">📊</div>
          <div class="export-text">
            {{ loadingStates.excelExport ? '导出中...' : '导出Excel' }}
          </div>
        </div>
      </div>

      <div
        class="export-cube"
        @click="printReport"
        :class="{ 'disabled': loadingStates.printReport }"
      >
        <div class="export-face">
          <div class="export-icon">🖨️</div>
          <div class="export-text">
            {{ loadingStates.printReport ? '打印中...' : '打印报表' }}
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast通知组件 - 符合CI_CD_STANDARDS.md用户反馈规范 -->
  <div v-if="toastState.visible" class="toast-notification" :class="'toast-' + toastState.type">
    <div class="toast-content">
      <div class="toast-icon">
        <span v-if="toastState.type === 'success'">✅</span>
        <span v-else-if="toastState.type === 'error'">❌</span>
        <span v-else-if="toastState.type === 'warning'">⚠️</span>
      </div>
      <div class="toast-message">{{ toastState.message }}</div>
    </div>
  </div>
</template>

<script setup>
// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

import { ref, reactive , nextTick, shallowRef, watchEffect } from 'vue';;

// 报表类型
const reportTypes = [
  { key: 'income', label: '收入报表', icon: '💰' },
  { key: 'expense', label: '支出报表', icon: '💸' },
  { key: 'profit', label: '利润报表', icon: '📈' },
  { key: 'comparison', label: '对比报表', icon: '📊' }
];

// 当前选中的报表
const activeReport = ref('income');

// 利润数据
const profitData = reactive({
  grossProfit: 45600,
  netProfit: 26700,
  profitMargin: 58.6
});

// Toast通知状态 - 符合CI_CD_STANDARDS.md用户反馈规范
const toastState = reactive({
  visible: false,
  message: '',
  type: 'success' // success, error, warning
});

// Toast通知函数 - 替代console日志
const showToast = (message, type = 'success') => {
  toastState.message = message;
  toastState.type = type;
  toastState.visible = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    toastState.visible = false;
  }, 3000);
};

// 加载状态管理 - 符合CI_CD_STANDARDS.md加载状态规范
const loadingStates = reactive({
  pdfExport: false,    // PDF导出加载状态
  excelExport: false,  // Excel导出加载状态
  printReport: false   // 打印报表加载状态
});

// 方法 - 符合CI_CD_STANDARDS.md错误处理规范
const exportPDF = async () => {
  try {
    loadingStates.pdfExport = true;
    console.log('导出PDF');
    // 模拟导出操作
    await new Promise(resolve => setTimeout(resolve, 1000));
    showToast('PDF导出成功', 'success');
  } catch (error) {
    console.error('PDF导出失败:', error);
    showToast('PDF导出失败，请重试', 'error');
  } finally {
    loadingStates.pdfExport = false;
  }
};

const exportExcel = async () => {
  try {
    loadingStates.excelExport = true;
    console.log('导出Excel');
    // 模拟导出操作
    await new Promise(resolve => setTimeout(resolve, 1000));
    showToast('Excel导出成功', 'success');
  } catch (error) {
    console.error('Excel导出失败:', error);
    showToast('Excel导出失败，请重试', 'error');
  } finally {
    loadingStates.excelExport = false;
  }
};

const printReport = async () => {
  try {
    loadingStates.printReport = true;
    console.log('打印报表');
    // 模拟打印操作
    await new Promise(resolve => setTimeout(resolve, 500));
    showToast('报表打印成功', 'success');
  } catch (error) {
    console.error('报表打印失败:', error);
    showToast('报表打印失败，请重试', 'error');
  } finally {
    loadingStates.printReport = false;
  }
};
</script>

<style scoped>
/* 继承毕加索风格样式 */
.picasso-reports {
  display: flex;
  position: fixed;
  inset: 0 0 0 180px;
  width: calc(100vw - 180px);
  height: 100vh;
  padding: 20px;
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;
  background: linear-gradient(45deg,
    #8e44ad 0%, #9b59b6 25%, #c8a2c8 50%, #dda0dd 75%, #e6e6fa 100%
  );
  background-size: 400% 400%;
  animation: picassoFlow 20s ease infinite;
  flex-direction: column;
}

@keyframes picassoFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 0% 50%; }
  100% { background-position: 50% 0%; }
}

/* 继承毕加索风格基础样式 */

/* 已删除无用的标题样式 */

/* 毕加索风格报表选择器 */
.report-selector-cubism {
  display: flex;
  position: relative;
  z-index: 10;
  gap: 15px;
  justify-content: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.report-cube {
  width: 120px;
  height: 80px;
  border-radius: 15px 0;
  transform: perspective(600px) rotateX(15deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.report-cube:hover {
  transform: perspective(600px) rotateX(15deg) scale(1.05);
}

.report-cube:nth-child(1) {
  background: linear-gradient(45deg, #27ae60, #2ecc71);
}

.report-cube:nth-child(2) {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
}

.report-cube:nth-child(3) {
  background: linear-gradient(45deg, #8e44ad, #9b59b6);
}

.report-cube:nth-child(4) {
  background: linear-gradient(45deg, #3498db, #2980b9);
}

.report-cube.active {
  box-shadow: 0 10px 25px rgb(142 68 173 / 40%);
  transform: perspective(600px) rotateX(15deg) scale(1.1);
}

.report-face {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 15px 0;
  font-weight: bold;
  color: white;
  box-shadow: 0 5px 15px rgb(0 0 0 / 30%);
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.report-icon {
  font-size: 1.5rem;
  transform: rotate(5deg);
}

.report-text {
  font-size: 0.9rem;
  transform: skew(-2deg);
}

/* 毕加索风格报表内容 */
.report-content-cubism {
  position: relative;
  z-index: 5;
  padding: 20px;
  border-radius: 20px;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 20px 40px rgb(155 89 182 / 30%);
  transform: perspective(1000px) rotateX(3deg);
  flex: 1;
  overflow-y: auto;

  /* 毕加索风格滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #9b59b6 rgb(155 89 182 / 20%);
}

.report-content-cubism::-webkit-scrollbar {
  width: 8px;
}

.report-content-cubism::-webkit-scrollbar-track {
  border-radius: 8px;
  background: linear-gradient(45deg, rgb(142 68 173 / 10%), rgb(155 89 182 / 10%));
}

.report-content-cubism::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: linear-gradient(45deg, #8e44ad, #9b59b6);
  transition: all 0.3s ease;
}

.report-content-cubism::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #639, #8e44ad);
}

.report-fragment {
  margin-bottom: 20px;
}

.fragment-header {
  margin-bottom: 20px;
  text-align: center;
}

.header-geometry {
  display: inline-block;
  padding: 10px 20px;
  border: 2px solid rgb(142 68 173 / 30%);
  border-radius: 15px 0;
  font-size: 1.5rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(142 68 173 / 20%), rgb(255 255 255 / 80%));
  transform: skew(-2deg);
}

/* 毕加索风格图表 */
.report-charts {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.chart-cube {
  padding: 15px;
  border: 2px solid rgb(142 68 173 / 20%);
  border-radius: 15px 0;
  background: rgb(255 255 255 / 90%);
  box-shadow: 0 10px 25px rgb(142 68 173 / 20%);
  transform: perspective(600px) rotateX(5deg);
}

.chart-header {
  padding: 8px 15px;
  border-radius: 10px 0;
  font-size: 1.1rem;
  font-weight: bold;
  text-align: center;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(142 68 173 / 10%), rgb(255 255 255 / 80%));
  transform: skew(-2deg);
  margin-bottom: 15px;
}

.chart-body {
  display: flex;
  height: 150px;
  align-items: center;
  justify-content: center;
}

/* 线性图表 */
.line-chart {
  position: relative;
  width: 100%;
  height: 100%;
}

.chart-line {
  position: absolute;
  top: 50%;
  right: 0;
  left: 0;
  height: 2px;
  border-radius: 2px;
  background: linear-gradient(90deg, #8e44ad, #9b59b6, #c8a2c8);
  transform: translateY(-50%);
}

.chart-points {
  position: relative;
  width: 100%;
  height: 100%;
}

.point {
  position: absolute;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #8e44ad;
  transform: translate(-50%, -50%);
  animation: pointPulse 2s ease-in-out infinite;
}

@keyframes pointPulse {
  0%, 100% { transform: translate(-50%, -50%) scale(1); }
  50% { transform: translate(-50%, -50%) scale(1.3); }
}

/* 饼图 */
.pie-chart {
  position: relative;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: conic-gradient(
    #8e44ad 0deg 120deg,
    #9b59b6 120deg 240deg,
    #c8a2c8 240deg 360deg
  );
  animation: pieRotate 3s ease-out;
}

@keyframes pieRotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 柱状图 */
.bar-chart {
  display: flex;
  align-items: flex-end;
  gap: 10px;
  height: 100%;
  justify-content: center;
}

.bar {
  display: flex;
  position: relative;
  width: 20px;
  border-radius: 4px 4px 0 0;
  background: linear-gradient(to top, #8e44ad, #9b59b6);
  animation: barGrow 2s ease-out;
  align-items: flex-end;
  justify-content: center;
}

@keyframes barGrow {
  from { height: 0; }
}

.bar-label {
  position: absolute;
  bottom: -20px;
  font-size: 0.7rem;
  color: #7f8c8d;
  transform: rotate(-45deg);
  white-space: nowrap;
}

/* 面积图 */
.area-chart {
  position: relative;
  width: 100%;
  height: 100%;
}

.area-fill {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 60%;
  border-radius: 10px 10px 0 0;
  background: linear-gradient(to top, rgb(142 68 173 / 30%), rgb(155 89 182 / 10%));
}

.area-line {
  position: absolute;
  top: 40%;
  right: 0;
  left: 0;
  height: 2px;
  border-radius: 2px;
  background: linear-gradient(90deg, #8e44ad, #9b59b6);
}

/* 毕加索风格利润摘要 */
.profit-summary {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 20px;
  margin-bottom: 20px;
}

.summary-cube {
  height: 120px;
  border-radius: 15px 0;
  transform: perspective(600px) rotateX(10deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.summary-cube:hover {
  transform: perspective(600px) rotateX(10deg) scale(1.05);
}

.gross-profit {
  background: linear-gradient(45deg, #27ae60, #2ecc71);
}

.net-profit {
  background: linear-gradient(45deg, #8e44ad, #9b59b6);
}

.profit-margin {
  background: linear-gradient(45deg, #3498db, #2980b9);
}

.summary-cube {
  display: flex;
  font-weight: bold;
  color: white;
  box-shadow: 0 10px 25px rgb(0 0 0 / 30%);
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.summary-icon {
  font-size: 2rem;
  transform: rotate(5deg);
}

.summary-value {
  font-size: 1.5rem;
  font-weight: 900;
  transform: skew(-2deg);
}

.summary-label {
  font-size: 0.9rem;
  opacity: 0.9;
  transform: skew(2deg);
}

/* 毕加索风格对比图表 */
.comparison-charts {
  display: flex;
  justify-content: center;
}

.comparison-bars {
  display: flex;
  gap: 20px;
  align-items: flex-end;
  height: 150px;
}

.comparison-group {
  display: flex;
  gap: 10px;
  align-items: flex-end;
}

.comparison-group .bar {
  position: relative;
  width: 30px;
  border-radius: 4px 4px 0 0;
  animation: barGrow 2s ease-out;
}

.comparison-group .bar.current {
  background: linear-gradient(to top, #8e44ad, #9b59b6);
}

.comparison-group .bar.previous {
  background: linear-gradient(to top, #95a5a6, #bdc3c7);
}

.bar-value {
  position: absolute;
  bottom: -25px;
  left: 50%;
  font-size: 0.7rem;
  color: #7f8c8d;
  transform: translateX(-50%);
  white-space: nowrap;
}

/* 毕加索风格导出操作 */
.export-actions-cubism {
  display: flex;
  position: relative;
  z-index: 10;
  gap: 15px;
  justify-content: center;
  margin-top: 20px;
  flex-shrink: 0;
}

.export-cube {
  width: 120px;
  height: 60px;
  border-radius: 15px 0;
  transform: perspective(600px) rotateX(15deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.export-cube:hover {
  transform: perspective(600px) rotateX(15deg) scale(1.05);
}

.export-cube:nth-child(1) {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
}

.export-cube:nth-child(2) {
  background: linear-gradient(45deg, #27ae60, #2ecc71);
}

.export-cube:nth-child(3) {
  background: linear-gradient(45deg, #3498db, #2980b9);
}

.export-face {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 15px 0;
  font-weight: bold;
  color: white;
  box-shadow: 0 5px 15px rgb(0 0 0 / 30%);
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.export-icon {
  font-size: 1.2rem;
  transform: rotate(5deg);
}

.export-text {
  font-size: 0.8rem;
  transform: skew(-2deg);
}

/* 响应式设计 */
@media (width <= 768px) {
  .picasso-reports {
    left: 0;
    width: 100vw;
    padding: 15px;
  }

  .report-selector-cubism {
    flex-direction: column;
    align-items: center;
  }

  .report-charts {
    grid-template-columns: 1fr;
  }

  .profit-summary {
    grid-template-columns: 1fr;
  }

  .export-actions-cubism {
    flex-direction: column;
    align-items: center;
  }

  .title-layer {
    font-size: 2rem;
  }

  .subtitle-fragment {
    font-size: 1rem;
  }
}

/* 🎨 按钮禁用状态 - 符合CI_CD_STANDARDS.md加载状态规范 */
.export-cube.disabled {
  opacity: 0.6;
  pointer-events: none;
  cursor: not-allowed;
}

/* 🎨 Toast通知组件样式 - 符合CI_CD_STANDARDS.md用户反馈规范 */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  max-width: 500px;
  min-width: 300px;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow:
    0 8px 32px rgb(0 0 0 / 20%),
    0 4px 16px rgb(0 0 0 / 10%);
  backdrop-filter: blur(10px);
  animation: slideInRight 0.3s ease-out;
}

.toast-success {
  border: 2px solid rgb(34 197 94 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(34 197 94 / 90%), rgb(22 163 74 / 90%));
}

.toast-error {
  border: 2px solid rgb(239 68 68 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(239 68 68 / 90%), rgb(220 38 38 / 90%));
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toast-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.toast-message {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
