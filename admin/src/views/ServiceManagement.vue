<template>
  <div class="picasso-services">
    <!-- 科幻通知组件 -->
    <SciFiNotification ref="notification" />

    <!-- 毕加索风格数据表格 -->
    <div class="data-cubism" :style="{ height: dynamicTableHeight + 'px' }">
      <div class="table-container">
        <!-- 集成操作功能的智能表头 -->
        <div class="smart-table-header">
          <!-- 列标题（支持搜索和排序） -->
          <div class="header-columns">
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.name }" style="flex: 2.2;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.name" class="header-search-container" @mouseleave="handleSearchMouseLeave('name')">
                <input
                  :ref="el => searchInputRefs.name = el"
                  type="text"
                  placeholder="🔍 搜索服务信息..."
                  v-model="searchValues.name"
                  @input="handleSearchInput('name')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('name')"
                  @keydown.enter="handleSearchEnter($event, 'name')"
                  @keydown.esc="exitSearchMode('name')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('name')">
                <span class="header-text">服务信息</span>
              </div>
              <!-- 动态按钮 -->
              <button v-if="searchValues.name && searchValues.name.trim()" @click="exitSearchMode('name')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.name.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('name')" :disabled="sortButtonStates.name.disabled" title="排序服务信息">
                <span class="sort-indicator" :class="getSortClass('name')">
                  {{ getSortIcon('name') }}
                </span>
              </button>
              <!-- 🎯 列宽调整拖拽手柄 -->
              <div class="column-resizer" @mousedown="startResize($event, 'name')" title="拖拽调整列宽"></div>
            </div>
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.phone }" style="flex: 1;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.phone" class="header-search-container" @mouseleave="handleSearchMouseLeave('phone')">
                <input
                  :ref="el => searchInputRefs.phone = el"
                  type="text"
                  placeholder="🔍 搜索服务费..."
                  v-model="searchValues.price"
                  @input="handleSearchInput('phone')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('phone')"
                  @keydown.enter="handleSearchEnter($event, 'phone')"
                  @keydown.esc="exitSearchMode('phone')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('phone')">
                <span class="header-text">服务费</span>
              </div>
              <!-- 动态按钮 -->
              <button v-if="searchValues.phone && searchValues.phone.trim()" @click="exitSearchMode('phone')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.phone.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('price')" :disabled="sortButtonStates.phone.disabled" title="排序服务费">
                <span class="sort-indicator" :class="getSortClass('price')">
                  {{ getSortIcon('price') }}
                </span>
              </button>
              <!-- 🎯 列宽调整拖拽手柄 -->
              <div class="column-resizer" @mousedown="startResize($event, 'phone')" title="拖拽调整列宽"></div>
            </div>
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.rating }" style="flex: 1;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.rating" class="header-search-container" @mouseleave="handleSearchMouseLeave('rating')">
                <input
                  :ref="el => searchInputRefs.rating = el"
                  type="text"
                  placeholder="🔍 搜索时长..."
                  v-model="searchValues.duration"
                  @input="handleSearchInput('rating')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('rating')"
                  @keydown.enter="handleSearchEnter($event, 'rating')"
                  @keydown.esc="exitSearchMode('rating')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('rating')">
                <span class="header-text">服务时长</span>
              </div>
              <!-- 动态按钮 -->
              <button v-if="searchValues.rating && searchValues.rating.trim()" @click="exitSearchMode('rating')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.rating.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('duration')" :disabled="sortButtonStates.rating.disabled" title="排序服务时长">
                <span class="sort-indicator" :class="getSortClass('duration')">
                  {{ getSortIcon('duration') }}
                </span>
              </button>
              <!-- 🎯 列宽调整拖拽手柄 -->
              <div class="column-resizer" @mousedown="startResize($event, 'rating')" title="拖拽调整列宽"></div>
            </div>
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.joinDate }" style="flex: 1.5;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.joinDate" class="header-search-container" @mouseleave="handleSearchMouseLeave('joinDate')">
                <input
                  :ref="el => searchInputRefs.joinDate = el"
                  type="text"
                  placeholder="🔍 搜索服务描述..."
                  v-model="searchValues.joinDate"
                  @input="handleSearchInput('joinDate')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('joinDate')"
                  @keydown.enter="handleSearchEnter($event, 'joinDate')"
                  @keydown.esc="exitSearchMode('joinDate')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('joinDate')">
                <span class="header-text">服务描述</span>
              </div>
              <!-- 动态按钮 -->
              <button v-if="searchValues.joinDate && searchValues.joinDate.trim()" @click="exitSearchMode('joinDate')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.joinDate.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('description')" :disabled="sortButtonStates.joinDate.disabled" title="排序服务描述">
                <span class="sort-indicator" :class="getSortClass('description')">
                  {{ getSortIcon('description') }}
                </span>
              </button>
              <!-- 🎯 列宽调整拖拽手柄 -->
              <div class="column-resizer" @mousedown="startResize($event, 'joinDate')" title="拖拽调整列宽"></div>
            </div>
            <div class="header-cell" style="flex: 0.8;">
              <div class="header-normal-container">
                <span class="header-text">状态</span>
              </div>
              <!-- 排序按钮 -->
              <button v-if="sortButtonStates.status && sortButtonStates.status.loading" class="sort-btn loading" disabled title="排序中...">
                <span class="loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('status')" :disabled="sortButtonStates.status && sortButtonStates.status.disabled" title="排序状态">
                <span class="sort-indicator" :class="getSortClass('status')">
                  {{ getSortIcon('status') }}
                </span>
              </button>
              <!-- 🎯 列宽调整拖拽手柄 -->
              <div class="column-resizer" @mousedown="startResize($event, 'status')" title="拖拽调整列宽"></div>
            </div>
            <div class="header-cell" style="flex: 1.5;">
              <div class="header-normal-container">
                <span class="header-text">操作</span>
              </div>
              <button class="header-add-btn-small" @click="showAddModal" title="新增服务">
                <span class="add-icon">➕</span>
                <span class="add-text">新增</span>
              </button>
            </div>
          </div>
        </div>

        <!-- 数据行容器 -->
        <div class="table-body" :style="{ height: (dynamicTableHeight - 60) + 'px' }">
          <!-- 加载状态 -->
          <div v-if="loadingStates.dataLoading" class="table-loading">
            <div class="loading-content">
              <div class="loading-spinner">⏳</div>
              <div class="loading-text">正在加载技师数据...</div>
            </div>
          </div>

          <!-- 空数据状态 -->
          <div v-else-if="paginatedData.length === 0" class="table-empty">
            <div class="empty-content">
              <div class="empty-icon">👨‍⚕️</div>
              <div class="empty-text">暂无技师数据</div>
              <div class="empty-hint">点击"新增"按钮添加第一个技师</div>
            </div>
          </div>

          <!-- 数据行 -->
          <div v-else v-for="(item, index) in paginatedData" :key="item.id" class="data-row">
            <!-- 服务信息 -->
            <div class="data-cell" style="flex: 2.2;">
              <div class="service-info">
                <div class="service-icon">
                  <div class="icon-placeholder">{{ item.name?.charAt(0) || '服' }}</div>
                </div>
                <div class="service-details">
                  <div class="service-name">{{ item.name }}</div>
                  <div class="service-id">ID: {{ item.id }}</div>
                  <div class="service-category">{{ item.category }}</div>
                </div>
              </div>
            </div>

            <!-- 服务费 -->
            <div class="data-cell" style="flex: 1;">
              <div class="price-info">
                <div class="price">¥{{ item.price }}</div>
              </div>
            </div>

            <!-- 服务时长 -->
            <div class="data-cell" style="flex: 1;">
              <div class="duration-info">
                <div class="duration-text">{{ item.duration }} 分钟</div>
              </div>
            </div>

            <!-- 服务描述 -->
            <div class="data-cell" style="flex: 1.5;">
              <div class="description-info">
                <div class="description-text">{{ item.description.substring(0, 15) }}{{ item.description.length > 15 ? '...' : '' }}</div>
              </div>
            </div>

            <!-- 状态 -->
            <div class="data-cell" style="flex: 0.8;">
              <div class="status-container">
                <span class="status-badge" :class="item.status">
                  {{ getStatusText(item.status) }}
                </span>
              </div>
            </div>

            <!-- 操作 -->
            <div class="data-cell" style="flex: 1.5;">
              <div class="action-buttons">
                <button class="action-btn-small primary" @click="editService(item)">
                  <span class="btn-icon">✏️</span>
                  <span class="btn-text">编辑</span>
                </button>
                <button class="action-btn-small danger" @click="deleteService(item)">
                  <span class="btn-icon">🗑️</span>
                  <span class="btn-text">删除</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 翻页组件 -->
      <div class="pagination-container">
        <div class="pagination-controls">
          <div class="pagination-info">
            <span class="total-info">
              共 <span class="highlight-number">{{ totalRecords }}</span> 条记录
            </span>
          </div>

          <div class="page-size-selector">
            <label class="page-size-label">每页显示：</label>
            <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
              <option value="10">10条</option>
              <option value="20">20条</option>
              <option value="50">50条</option>
            </select>
          </div>

          <div class="page-navigation" v-if="totalRecords > 0">
            <button
              class="page-btn prev-btn"
              @click="prevPage"
              :disabled="currentPage === 1"
              aria-label="上一页">
              ‹ 上一页
            </button>

            <div class="page-numbers">
              <button
                v-for="page in visiblePages"
                :key="page"
                class="page-btn page-number"
                :class="{ active: page === currentPage }"
                @click="goToPage(page)"
                aria-label="页码">
                {{ page }}
              </button>
            </div>

            <button
              class="page-btn next-btn"
              @click="nextPage"
              :disabled="currentPage === totalPages"
              aria-label="下一页">
              下一页 ›
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎯 新增/编辑服务模态框 -->
    <div v-if="showModal" class="modal-overlay" @click="closeModal">
      <div class="modal-container" @click.stop>
        <div class="modal-header">
          <h3 class="modal-title">{{ isEditing ? '编辑服务' : '新增服务' }}</h3>
          <button class="modal-close-btn" @click="closeModal">×</button>
        </div>

        <div class="modal-body">
          <form @submit.prevent="saveService">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">服务名称</label>
                <input
                  v-model="serviceForm.name"
                  type="text"
                  class="form-input"
                  placeholder="请输入服务名称"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">服务分类</label>
                <select v-model="serviceForm.category" class="form-select" required>
                  <option value="">请选择分类</option>
                  <option value="按摩服务">按摩服务</option>
                  <option value="足疗服务">足疗服务</option>
                  <option value="美容服务">美容服务</option>
                  <option value="理疗服务">理疗服务</option>
                  <option value="养生服务">养生服务</option>
                </select>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">服务价格</label>
                <input
                  v-model.number="serviceForm.price"
                  type="number"
                  class="form-input"
                  placeholder="请输入价格"
                  min="0"
                  step="0.01"
                  required
                />
              </div>
              <div class="form-group">
                <label class="form-label">服务时长（分钟）</label>
                <input
                  v-model.number="serviceForm.duration"
                  type="number"
                  class="form-input"
                  placeholder="请输入时长"
                  min="1"
                  required
                />
              </div>
            </div>

            <div class="form-group">
              <label class="form-label">服务描述</label>
              <textarea
                v-model="serviceForm.description"
                class="form-textarea"
                placeholder="请输入服务描述"
                rows="3"
                required
              ></textarea>
            </div>

            <div class="form-group">
              <label class="form-label">服务状态</label>
              <select v-model="serviceForm.status" class="form-select" required>
                <option value="active">可用</option>
                <option value="inactive">停用</option>
                <option value="maintenance">维护中</option>
              </select>
            </div>
          </form>
        </div>

        <div class="modal-footer">
          <button type="button" class="btn-cancel" @click="closeModal">取消</button>
          <button type="button" class="btn-save" @click="saveService">{{ isEditing ? '保存' : '新增' }}</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import SciFiNotification from '@/components/SciFiNotification.vue';
import { universalMatch, createSearchFilter } from '@/utils/universalSearch.js';

export default {
  name: 'ServiceManagement',
  components: {
    SciFiNotification
  },
  setup() {
    // 基础响应式数据
    const notification = ref(null);
    const dynamicTableHeight = ref(600);
    
    // 搜索功能数据
    const searchModes = reactive({
      name: false,
      phone: false,
      rating: false,
      joinDate: false
    });
    
    const searchValues = reactive({
      name: '',
      phone: '',
      rating: '',
      joinDate: ''
    });
    
    const searchInputRefs = reactive({
      name: null,
      phone: null,
      rating: null,
      joinDate: null
    });

    // 🎯 搜索防抖超时处理
    const searchInputTimeout = ref(null);

    // 🎯 排序状态管理
    const sortBy = ref('');
    const sortOrder = ref('asc'); // 'asc' | 'desc'
    
    // 排序功能数据
    const sortButtonStates = reactive({
      name: { loading: false, disabled: false },
      phone: { loading: false, disabled: false },
      rating: { loading: false, disabled: false },
      joinDate: { loading: false, disabled: false },
      status: { loading: false, disabled: false }  // 🎯 添加状态排序支持
    });
    
    // 过渡动画数据
    const isTransitioning = reactive({
      name: false,
      phone: false,
      rating: false,
      joinDate: false
    });
    
    // 数据和状态
    const loadingStates = reactive({
      dataLoading: false,
      saving: false
    });

    // 服务数据 - 10条完整测试数据，确保与菜单完美对齐
    const services = ref([
      {
        id: 'S001',
        name: '中式按摩',
        price: 88,
        duration: 45,
        category: '按摩服务',
        status: 'active',
        description: '传统中式按摩，舒缓疲劳，促进血液循环'
      },
      {
        id: 'S002',
        name: '足疗保健',
        price: 108,
        duration: 60,
        category: '足疗服务',
        status: 'active',
        description: '专业足疗服务，缓解足部疲劳，改善睡眠'
      },
      {
        id: 'S003',
        name: '面部护理',
        price: 128,
        duration: 75,
        category: '美容服务',
        status: 'active',
        description: '深层清洁面部，补水保湿，延缓衰老'
      },
      {
        id: 'S004',
        name: '颈椎调理',
        price: 148,
        duration: 90,
        category: '理疗服务',
        status: 'active',
        description: '专业颈椎调理，缓解颈部僵硬'
      },
      {
        id: 'S005',
        name: '腰部推拿',
        price: 168,
        duration: 105,
        category: '养生服务',
        status: 'active',
        description: '腰部深层推拿，缓解腰部疼痛'
      },
      {
        id: 'S006',
        name: '肩部按摩',
        price: 188,
        duration: 120,
        category: '按摩服务',
        status: 'active',
        description: '肩部专业按摩，放松肩颈肌肉'
      },
      {
        id: 'S007',
        name: '全身SPA',
        price: 208,
        duration: 135,
        category: '足疗服务',
        status: 'active',
        description: '全身放松SPA，身心舒缓体验'
      },
      {
        id: 'S008',
        name: '艾灸理疗',
        price: 228,
        duration: 150,
        category: '美容服务',
        status: 'active',
        description: '传统艾灸理疗，温经通络'
      },
      {
        id: 'S009',
        name: '拔罐刮痧',
        price: 248,
        duration: 165,
        category: '理疗服务',
        status: 'active',
        description: '传统拔罐刮痧，排毒养颜'
      },
      {
        id: 'S010',
        name: '经络疏通',
        price: 268,
        duration: 180,
        category: '养生服务',
        status: 'active',
        description: '经络疏通调理，平衡气血'
      }
    ]);

    // 🎯 搜索选项配置 - 统一的搜索规范
    const searchOptions = {
      exactMatch: false,        // 不要求精确匹配
      caseSensitive: false,     // 不区分大小写
      minSimilarity: 0.6,       // 模糊匹配相似度阈值
      enableFuzzy: true,        // 启用模糊匹配
      enablePinyin: true,       // 启用拼音匹配
      debug: false              // 生产环境关闭调试
    };

    // 🎯 全面搜索过滤逻辑 - 使用通用搜索工具
    const filteredData = computed(() => {
      let filtered = services.value;

      try {
        // 服务信息搜索 (name字段) - 支持全面搜索
        if (searchValues.name && searchValues.name.trim()) {
          const search = searchValues.name.trim();
          console.log(`🔍 服务信息搜索: "${search}"`);

          filtered = createSearchFilter(
            filtered,
            search,
            ['name', 'id', 'category', 'description'],
            { ...searchOptions, debug: true }
          );
        }

        // 服务费搜索 (price字段) - 支持多种价格格式
        if (searchValues.price && searchValues.price.trim()) {
          const search = searchValues.price.trim();
          console.log(`💰 服务费搜索: "${search}"`);

          filtered = filtered.filter(item => {
            const priceVariants = [
              item.price.toString(),
              `¥${item.price}`,
              `${item.price}元`,
              `${item.price}块`,
              `${item.price}rmb`,
              `${item.price}yuan`,
              `${Math.floor(item.price/100)*100}-${Math.ceil(item.price/100)*100}`
            ];

            return priceVariants.some(variant =>
              universalMatch(variant, search, searchOptions)
            );
          });
        }

        // 服务时长搜索 (duration字段) - 支持多种时间格式
        if (searchValues.duration && searchValues.duration.trim()) {
          const search = searchValues.duration.trim();
          console.log(`⏰ 服务时长搜索: "${search}"`);

          filtered = filtered.filter(item => {
            const durationVariants = [
              item.duration.toString(),
              `${item.duration}分钟`,
              `${item.duration}分`,
              `${item.duration}min`,
              `${item.duration}mins`,
              `${item.duration}minute`,
              `${item.duration}minutes`,
              `${Math.floor(item.duration/60)}小时`,
              `${Math.floor(item.duration/30)*30}-${Math.ceil(item.duration/30)*30}分钟`
            ];

            return durationVariants.some(variant =>
              universalMatch(variant, search, searchOptions)
            );
          });
        }

        // 服务描述搜索 (joinDate字段映射到description) - 支持全面搜索
        if (searchValues.joinDate && searchValues.joinDate.trim()) {
          const search = searchValues.joinDate.trim();
          console.log(`📝 服务描述搜索: "${search}"`);

          filtered = createSearchFilter(
            filtered,
            search,
            ['description', 'category', 'name'],
            searchOptions
          );
        }

        console.log(`🎯 搜索完成，找到 ${filtered.length} 条结果`);

        // 🎯 应用排序
        if (sortBy.value) {
          console.log(`🔄 应用排序: ${sortBy.value} ${sortOrder.value}`);

          filtered.sort((a, b) => {
            let aValue, bValue;

            // 根据字段获取比较值
            switch (sortBy.value) {
              case 'name':
                aValue = a.name || '';
                bValue = b.name || '';
                break;
              case 'price':
                aValue = parseFloat(a.price) || 0;
                bValue = parseFloat(b.price) || 0;
                break;
              case 'duration':
                aValue = parseInt(a.duration) || 0;
                bValue = parseInt(b.duration) || 0;
                break;
              case 'description':
                aValue = a.description || '';
                bValue = b.description || '';
                break;
              case 'category':
                aValue = a.category || '';
                bValue = b.category || '';
                break;
              case 'status':
                aValue = a.status || '';
                bValue = b.status || '';
                break;
              default:
                aValue = a[sortBy.value] || '';
                bValue = b[sortBy.value] || '';
            }

            // 字符串比较
            if (typeof aValue === 'string' && typeof bValue === 'string') {
              const result = aValue.localeCompare(bValue, 'zh-CN');
              return sortOrder.value === 'asc' ? result : -result;
            }

            // 数字比较
            if (typeof aValue === 'number' && typeof bValue === 'number') {
              const result = aValue - bValue;
              return sortOrder.value === 'asc' ? result : -result;
            }

            // 混合类型比较
            const result = String(aValue).localeCompare(String(bValue), 'zh-CN');
            return sortOrder.value === 'asc' ? result : -result;
          });

          console.log(`✅ 排序完成，共 ${filtered.length} 条记录`);
        }

      } catch (error) {
        console.error('搜索过滤出错:', error);
        // 出错时返回原始数据，避免页面崩溃
        return services.value;
      }

      return filtered;
    });

    const paginatedData = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      return filteredData.value.slice(start, end);
    });

    const currentPage = ref(1);
    const pageSize = ref(10);
    const totalRecords = computed(() => filteredData.value.length);
    const totalPages = computed(() => Math.ceil(totalRecords.value / pageSize.value));
    
    // 方法定义
    const handleClickToSearch = (field) => {
      searchModes[field] = true;
      nextTick(() => {
        if (searchInputRefs[field]) {
          searchInputRefs[field].focus();
        }
      });
    };
    
    // 🎯 增强的搜索输入处理 - 支持中英文实时搜索
    const handleSearchInput = (field) => {
      try {
        // 如果正在中文输入，不触发搜索
        if (isComposing.value) {
          console.log('🎯 中文输入中，跳过搜索');
          return;
        }

        // 实时搜索逻辑
        console.log(`🔍 搜索字段: ${field}, 搜索内容: "${searchValues[field]}"`);

        // 重置到第一页
        currentPage.value = 1;

        // 防抖处理 - 避免频繁搜索
        if (searchInputTimeout.value) {
          clearTimeout(searchInputTimeout.value);
        }

        searchInputTimeout.value = setTimeout(() => {
          // 搜索完成后的回调
          const resultCount = filteredData.value.length;
          console.log(`✅ 搜索完成，找到 ${resultCount} 条结果`);

          // 如果没有结果，显示友好提示
          if (resultCount === 0 && searchValues[field] && searchValues[field].trim()) {
            console.log('💡 搜索提示: 未找到匹配结果，请尝试其他关键词');
          }
        }, 300);

      } catch (error) {
        console.error('搜索输入处理出错:', error);
        // 显示用户友好的错误提示
        if (notification.value) {
          notification.value.error('搜索出现问题，请重试');
        }
      }
    };
    
    const exitSearchMode = (field) => {
      searchModes[field] = false;
      searchValues[field] = '';
    };

    const handleSearchBlur = (field) => {
      // 处理搜索框失焦
      setTimeout(() => {
        if (searchValues[field] && searchValues[field].trim()) {
          // 如果有搜索内容，保持搜索模式
        } else {
          // 如果没有搜索内容，退出搜索模式
          exitSearchMode(field);
        }
      }, 200);
    };

    const handleSearchMouseLeave = (field) => {
      // 处理鼠标离开搜索区域
      if (!searchValues[field] || !searchValues[field].trim()) {
        // 如果没有搜索内容，退出搜索模式
        exitSearchMode(field);
      }
    };

    // 🎯 中文输入法状态管理
    const isComposing = ref(false);

    const handleCompositionStart = () => {
      // 处理中文输入开始
      isComposing.value = true;
      console.log('🎯 中文输入法开始');
    };

    const handleCompositionEnd = (event) => {
      // 处理中文输入结束
      isComposing.value = false;
      console.log('🎯 中文输入法结束:', event.target.value);

      // 中文输入完成后触发搜索
      const field = Object.keys(searchInputRefs).find(key =>
        searchInputRefs[key] === event.target
      );
      if (field) {
        handleSearchInput(field);
      }
    };

    // 🎯 搜索回车键处理
    const handleSearchEnter = (event, field) => {
      try {
        event.preventDefault();
        console.log(`⏎ 回车搜索: ${field} = "${searchValues[field]}"`);

        // 立即执行搜索，不等待防抖
        if (searchInputTimeout.value) {
          clearTimeout(searchInputTimeout.value);
        }

        currentPage.value = 1;
        const resultCount = filteredData.value.length;
        console.log(`✅ 回车搜索完成，找到 ${resultCount} 条结果`);

        // 失焦输入框
        if (searchInputRefs[field]) {
          searchInputRefs[field].blur();
        }

      } catch (error) {
        console.error('回车搜索处理出错:', error);
      }
    };
    
    // 🎯 排序处理函数 - 完整的排序功能实现
    const handleSort = (field) => {
      try {
        console.log(`🔄 排序字段: ${field}`);

        // 如果点击的是当前排序字段，切换排序方向
        if (sortBy.value === field) {
          sortOrder.value = sortOrder.value === 'asc' ? 'desc' : 'asc';
        } else {
          // 如果是新字段，设置为升序
          sortBy.value = field;
          sortOrder.value = 'asc';
        }

        console.log(`✅ 排序设置: ${field} ${sortOrder.value}`);

        // 重置到第一页
        currentPage.value = 1;

      } catch (error) {
        console.error('排序处理出错:', error);
        if (notification.value) {
          notification.value.error('排序功能出现问题，请重试');
        }
      }
    };

    // 🎯 获取排序样式类
    const getSortClass = (field) => {
      if (sortBy.value !== field) return '';
      return sortOrder.value === 'asc' ? 'sort-asc' : 'sort-desc';
    };

    // 🎯 获取排序图标
    const getSortIcon = (field) => {
      if (sortBy.value !== field) return '↕';
      return sortOrder.value === 'asc' ? '↑' : '↓';
    };
    



    const formatDate = (dateString) => {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN');
    };

    const calculateWorkDuration = (joinDate) => {
      if (!joinDate) return '-';
      const start = new Date(joinDate);
      const now = new Date();
      const diffTime = Math.abs(now - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      const months = Math.floor(diffDays / 30);
      return months > 0 ? `${months}个月` : `${diffDays}天`;
    };

    const getStatusText = (status) => {
      const statusMap = {
        active: '可用',
        inactive: '停用',
        maintenance: '维护中'
      };
      return statusMap[status] || status;
    };

    // 翻页相关方法
    const visiblePages = computed(() => {
      const pages = [];
      const start = Math.max(1, currentPage.value - 2);
      const end = Math.min(totalPages.value, currentPage.value + 2);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      return pages;
    });

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page;
      }
    };

    const prevPage = () => {
      if (currentPage.value > 1) {
        currentPage.value--;
      }
    };

    const nextPage = () => {
      if (currentPage.value < totalPages.value) {
        currentPage.value++;
      }
    };

    const handlePageSizeChange = () => {
      currentPage.value = 1; // 重置到第一页
    };
    
    // 🎯 列宽调整相关数据
    const columnWidths = reactive({
      name: 2.2,      // 服务信息
      phone: 1,       // 服务费
      rating: 1,      // 服务时长
      joinDate: 1.5,  // 服务描述
      status: 0.8     // 状态
      // 操作列不可调整，固定为1.5
    });

    const isResizing = ref(false);
    const resizingColumn = ref(null);
    const startX = ref(0);
    const startWidth = ref(0);
    const minColumnWidth = 30; // 最小列宽30px

    // 🎯 模态框相关数据
    const showModal = ref(false);
    const isEditing = ref(false);
    const editingServiceId = ref(null);

    const serviceForm = reactive({
      name: '',
      category: '',
      price: 0,
      duration: 0,
      description: '',
      status: 'active'
    });

    // 🎯 模态框操作方法
    const showAddModal = () => {
      isEditing.value = false;
      editingServiceId.value = null;
      resetForm();
      showModal.value = true;
      if (notification.value) {
        notification.value.info('打开新增服务表单');
      }
    };

    const editService = (service) => {
      isEditing.value = true;
      editingServiceId.value = service.id;

      // 填充表单数据
      serviceForm.name = service.name;
      serviceForm.category = service.category;
      serviceForm.price = service.price;
      serviceForm.duration = service.duration;
      serviceForm.description = service.description;
      serviceForm.status = service.status;

      showModal.value = true;
      if (notification.value) {
        notification.value.info(`编辑服务: ${service.name}`);
      }
    };

    const closeModal = () => {
      showModal.value = false;
      resetForm();
    };

    const resetForm = () => {
      serviceForm.name = '';
      serviceForm.category = '';
      serviceForm.price = 0;
      serviceForm.duration = 0;
      serviceForm.description = '';
      serviceForm.status = 'active';
    };

    const saveService = () => {
      // 表单验证
      if (!serviceForm.name || !serviceForm.category || !serviceForm.price || !serviceForm.duration || !serviceForm.description) {
        if (notification.value) {
          notification.value.error('请填写所有必填字段');
        }
        return;
      }

      if (isEditing.value) {
        // 编辑现有服务
        const serviceIndex = services.value.findIndex(s => s.id === editingServiceId.value);
        if (serviceIndex !== -1) {
          services.value[serviceIndex] = {
            ...services.value[serviceIndex],
            ...serviceForm,
            updatedAt: new Date().toISOString()
          };
          if (notification.value) {
            notification.value.success(`服务 "${serviceForm.name}" 更新成功`);
          }
        }
      } else {
        // 新增服务
        const newService = {
          id: 'S' + String(services.value.length + 1).padStart(3, '0'),
          ...serviceForm,
          createdAt: new Date().toISOString(),
          updatedAt: new Date().toISOString()
        };
        services.value.push(newService);
        if (notification.value) {
          notification.value.success(`服务 "${serviceForm.name}" 添加成功`);
        }
      }

      closeModal();
    };

    // 🎯 列宽调整方法
    const startResize = (event, columnName) => {
      event.preventDefault();
      isResizing.value = true;
      resizingColumn.value = columnName;
      startX.value = event.clientX;
      startWidth.value = columnWidths[columnName];

      // 添加全局事件监听器
      document.addEventListener('mousemove', handleResize);
      document.addEventListener('mouseup', stopResize);
      document.body.style.cursor = 'col-resize';
      document.body.style.userSelect = 'none';

      if (notification.value) {
        notification.value.info(`开始调整 ${getColumnDisplayName(columnName)} 列宽`);
      }
    };

    const handleResize = (event) => {
      if (!isResizing.value || !resizingColumn.value) return;

      const deltaX = event.clientX - startX.value;
      const containerWidth = document.querySelector('.header-columns')?.offsetWidth || 1000;
      const deltaFlex = (deltaX / containerWidth) * 8; // 8是总flex值的近似

      let newWidth = startWidth.value + deltaFlex;

      // 确保最小宽度（转换为flex值）
      const minFlexWidth = minColumnWidth / containerWidth * 8;
      newWidth = Math.max(newWidth, minFlexWidth);

      // 确保最大宽度不超过合理范围
      newWidth = Math.min(newWidth, 4);

      columnWidths[resizingColumn.value] = newWidth;

      // 更新DOM中的flex值
      updateColumnStyles();
    };

    const stopResize = () => {
      if (isResizing.value && resizingColumn.value) {
        if (notification.value) {
          notification.value.success(`${getColumnDisplayName(resizingColumn.value)} 列宽调整完成`);
        }
      }

      isResizing.value = false;
      resizingColumn.value = null;

      // 移除全局事件监听器
      document.removeEventListener('mousemove', handleResize);
      document.removeEventListener('mouseup', stopResize);
      document.body.style.cursor = '';
      document.body.style.userSelect = '';
    };

    const updateColumnStyles = () => {
      // 更新表头列的flex值
      const headerCells = document.querySelectorAll('.header-cell');
      const dataCells = document.querySelectorAll('.data-cell');

      Object.keys(columnWidths).forEach((columnName, index) => {
        const flexValue = columnWidths[columnName];

        // 更新表头
        if (headerCells[index]) {
          headerCells[index].style.flex = flexValue.toString();
        }

        // 更新数据行
        dataCells.forEach((row, rowIndex) => {
          const cellsInRow = row.parentElement?.querySelectorAll('.data-cell');
          if (cellsInRow && cellsInRow[index]) {
            cellsInRow[index].style.flex = flexValue.toString();
          }
        });
      });
    };

    const getColumnDisplayName = (columnName) => {
      const nameMap = {
        name: '服务信息',
        phone: '服务费',
        rating: '服务时长',
        joinDate: '服务描述',
        status: '状态'
      };
      return nameMap[columnName] || columnName;
    };

    const deleteService = (service) => {
      if (confirm(`确定要删除服务 "${service.name}" 吗？`)) {
        const index = services.value.findIndex(s => s.id === service.id);
        if (index !== -1) {
          services.value.splice(index, 1);
          if (notification.value) {
            notification.value.success(`服务 "${service.name}" 删除成功`);
          }
        }
      }
    };

    // 生命周期
    onMounted(() => {
      // 初始化逻辑
    });
    
    return {
      // 数据
      notification,
      dynamicTableHeight,
      searchModes,
      searchValues,
      searchInputRefs,
      sortButtonStates,
      isTransitioning,
      loadingStates,
      services,
      filteredData,
      paginatedData,
      currentPage,
      pageSize,
      totalRecords,
      totalPages,
      visiblePages,
      sortBy,
      sortOrder,

      // 方法
      handleClickToSearch,
      handleSearchInput,
      handleSearchEnter,
      exitSearchMode,
      handleSearchBlur,
      handleSearchMouseLeave,
      handleCompositionStart,
      handleCompositionEnd,
      isComposing,
      searchInputTimeout,
      handleSort,
      getSortClass,
      getSortIcon,

      // 列宽调整相关
      columnWidths,
      isResizing,
      startResize,

      // 模态框相关
      showModal,
      isEditing,
      serviceForm,
      showAddModal,
      editService,
      deleteService,
      closeModal,
      saveService,
      formatDate,
      calculateWorkDuration,
      getStatusText,
      goToPage,
      prevPage,
      nextPage,
      handlePageSizeChange
    };
  }
};
</script>

<style scoped>
/* 🎯 CSS变量定义 - 基于服务管理页面标准 */
:root {
  /* 毛玻璃效果标准 */
  --glass-bg-primary: rgb(255 255 255 / 8%);
  --glass-bg-secondary: rgb(255 255 255 / 5%);
  --glass-bg-input: rgb(255 255 255 / 3%);
  --glass-border: rgb(255 255 255 / 15%);
  --glass-blur-standard: blur(25px) saturate(1.5);
  --glass-blur-heavy: blur(40px) saturate(1.8) brightness(1.2);

  /* 🎯 Z-index层级系统 - 与通用样式保持一致 */
  --z-base: 1;
  --z-content: 10;
  --z-dropdown: 100;
  --z-toolbar: 200;
  --z-table-header: 300;
  --z-tooltip: 500;
  --z-modal-backdrop: 1000;
  --z-modal: 1001;
  --z-toast: 2000;
  --z-debug: 9999;
}

/* 主容器样式 - 与服务管理页面完全一致 */
.picasso-services {
  display: flex;
  position: fixed;
  z-index: var(--z-base);
  width: calc(100vw - 180px);
  height: 100vh;
  min-height: 100vh;
  padding: 1.984vw;
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;
  font-size: clamp(12px, 1.2vw, 18px);
  background: transparent;
  transform: scale(var(--scale-factor));
  inset: 0 0 0 180px;
  transform-origin: top left;
  flex-direction: column;
}

/* 数据表格容器 */
.data-cubism {
  display: flex;
  position: relative;
  z-index: var(--z-base);
  padding: 0 20px;
  border-radius: 16px;
  overflow: hidden;
  background: transparent;
  flex: 1;
  flex-direction: column;
  margin-top: -10px;
}

/* 表格容器 */
.table-container {
  display: flex;
  flex: 1;
  flex-direction: column;
  height: calc(100vh - 120px);
  max-height: calc(100vh - 120px);
  min-height: 400px;
  padding: 0 5px 4px;
  border-radius: 12px;
  overflow: hidden;
  background: transparent;
}

/* 智能表头 */
.smart-table-header {
  position: sticky;
  top: 0;
  z-index: var(--z-table-header);
  border-radius: 15px 15px 0 0;
  background: rgb(139 92 246 / 15%);
  box-shadow: 0 2px 8px rgb(139 92 246 / 20%);
  backdrop-filter: blur(10px);
  margin-bottom: 2px;
}

/* 表头列 */
.header-columns {
  display: flex;
  align-items: center;
  padding: 12px 16px;
  gap: 8px;
}

.header-cell {
  display: flex;
  position: relative;
  padding: 0 8px;
  align-items: center;
  justify-content: space-between;
}

.header-text {
  font-size: 0.9rem;
  font-weight: 600;
  color: rgb(255 255 255 / 95%);  /* 🎯 高对比度白色文字，确保在紫色背景下清晰可见 */
  text-shadow: 0 1px 2px rgb(0 0 0 / 30%);  /* 🎯 添加文字阴影增强可读性 */
}

/* 搜索功能样式 */
.header-search-container {
  flex: 1;
  position: relative;
}

.header-search-input {
  width: 100%;
  height: 32px;
  padding: 0 12px;
  border: 1px solid var(--glass-border);
  border-radius: 6px;
  font-size: 0.85rem;
  color: rgb(255 255 255 / 90%);  /* 🎯 高对比度白色文字 */
  background: var(--glass-bg-input);
  transition: all 0.2s ease;
  backdrop-filter: var(--glass-blur-standard);
}

/* 🎯 搜索输入框占位符样式 */
.header-search-input::placeholder {
  color: rgb(255 255 255 / 60%);  /* 🎯 占位符使用半透明白色 */
}

.header-search-input:focus {
  outline: none;
  border-color: rgb(139 92 246 / 30%);
  background: rgb(255 255 255 / 10%);
  box-shadow: 0 0 0 2px rgb(139 92 246 / 10%);
}

.header-normal-container {
  flex: 1;
  cursor: pointer;
  padding: 8px 0;
  transition: all 0.2s ease;
}

.header-normal-container:hover {
  color: rgb(139 92 246 / 80%);
}

/* 排序按钮 */
.sort-btn {
  display: flex;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 4px;
  color: rgb(255 255 255 / 80%);  /* 🎯 高对比度白色文字 */
  background: rgb(255 255 255 / 10%);
  transition: all 0.2s ease;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}

.sort-btn:hover {
  background: rgb(139 92 246 / 20%);
  transform: scale(1.1);
}

.sort-indicator {
  font-size: 0.9rem;  /* 🎯 统一字体大小与表头文字一致 */
  font-weight: 600;
  transition: all 0.2s ease;
}

/* 🎯 排序状态样式 */
.sort-indicator.sort-asc {
  color: #059669;
  transform: scale(1.2);
}

.sort-indicator.sort-desc {
  color: #dc2626;
  transform: scale(1.2);
}

/* 搜索关闭按钮 */
.search-close-btn {
  display: flex;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  font-size: 1rem;
  font-weight: bold;
  color: #dc2626;
  background: rgb(239 68 68 / 10%);
  transition: all 0.2s ease;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}

.search-close-btn:hover {
  background: rgb(239 68 68 / 20%);
  transform: scale(1.1);
}



/* 新增按钮 */
.header-add-btn-small {
  display: flex;
  padding: 6px 10px;
  border: 1px solid rgb(139 92 246 / 20%);
  border-radius: 6px;
  font-size: 0.9rem;  /* 🎯 统一字体大小与表头文字一致 */
  font-weight: 500;
  color: #4f46e5;
  background: rgb(139 92 246 / 10%);
  transition: all 0.2s ease;
  align-items: center;
  gap: 4px;
  cursor: pointer;
}

.header-add-btn-small:hover {
  background: rgb(139 92 246 / 20%);
  box-shadow: 0 4px 12px rgb(139 92 246 / 20%);
  transform: translateY(-1px);
}

/* 表格主体 */
.table-body {
  flex: 1;
  overflow-y: auto;
  padding: 0 8px;
}

/* 🎯 数据行基础样式 - 现代化单行布局 */
.data-row {
  display: flex;
  height: 50px;
  min-width: 0; /* 允许flex收缩 */
  padding: 12px 16px;
  overflow: hidden; /* 防止内容溢出 */
  line-height: 1.2; /* 使用正常行高 */
  transition: all 0.2s ease;
  align-items: center;
  margin-bottom: 0;
  border-bottom: 1px solid rgb(255 255 255 / 10%);
}

.data-row:hover {
  background: rgb(255 255 255 / 5%);
  backdrop-filter: blur(20px) saturate(1.4);
}

.data-row:last-child {
  border-bottom: none;
}

/* 过渡动画 */
.transitioning {
  transition: all 0.3s ease;
}

/* 加载动画 */
.sort-loading-indicator {
  display: flex;
  gap: 2px;
}

.loading-dot {
  width: 3px;
  height: 3px;
  border-radius: 50%;
  background: currentcolor;
  animation: loadingDots 1.4s infinite ease-in-out;
}

.loading-dot:nth-child(1) { animation-delay: -0.32s; }
.loading-dot:nth-child(2) { animation-delay: -0.16s; }

@keyframes loadingDots {
  0%, 80%, 100% { opacity: 0.3; }
  40% { opacity: 1; }
}

/* 🎯 现代化数据展示样式 - 遵循现代开发规范 */
.data-cell {
  display: flex;
  min-width: 0; /* 允许flex子元素收缩 */
  padding: 0 8px;
  overflow: hidden; /* 防止内容溢出 */
  font-size: 0.9rem;
  align-items: center;
}

/* 🎯 服务信息样式 - 完美单行布局 */
.service-info {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  min-width: 0; /* 关键：允许flex收缩 */
}

.service-icon {
  display: flex;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: rgb(139 92 246 / 15%);
  flex-shrink: 0; /* 图标不收缩 */
  align-items: center;
  justify-content: center;
}

.icon-placeholder {
  font-size: 1rem;
  font-weight: 600;
  color: #4f46e5;
}

.service-details {
  flex: 1;
  min-width: 0; /* 关键：允许文本容器收缩 */
  overflow: hidden;
}

.service-name {
  overflow: hidden;
  font-weight: 600;
  line-height: 1.2;
  color: rgb(255 255 255 / 95%); /* 🎯 修复：使用高对比度白色，确保在紫色背景下清晰可见 */
  text-shadow: 0 1px 2px rgb(0 0 0 / 30%); /* 🎯 添加文字阴影增强可读性 */
  margin-bottom: 2px;

  /* 🎯 现代文本溢出处理 */
  white-space: nowrap;
  text-overflow: ellipsis;
}

.service-id {
  overflow: hidden;
  font-size: 0.8rem;
  color: rgb(255 255 255 / 70%); /* 🎯 修复：使用半透明白色，确保在紫色背景下清晰可见 */
  margin-bottom: 1px;

  /* 🎯 现代文本溢出处理 */
  white-space: nowrap;
  text-overflow: ellipsis;
}

.service-category {
  overflow: hidden;
  font-size: 0.8rem;
  font-weight: 500; /* 🎯 增加字重，提高可读性 */
  color: rgb(168 85 247 / 90%); /* 🎯 修复：使用亮紫色，确保在紫色背景下清晰可见 */

  /* 🎯 现代文本溢出处理 */
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 🎯 价格信息样式 */
.price-info {
  width: 100%;
  min-width: 0;
}

.price {
  overflow: hidden;
  font-size: 1rem;
  font-weight: 600;
  color: rgb(34 197 94 / 90%); /* 🎯 修复：使用亮绿色，确保在紫色背景下清晰可见 */
  text-shadow: 0 1px 2px rgb(0 0 0 / 30%); /* 🎯 添加文字阴影增强可读性 */

  /* 🎯 现代文本溢出处理 */
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 🎯 时长信息样式 */
.duration-info {
  width: 100%;
  min-width: 0;
}

.duration-text {
  overflow: hidden;
  font-weight: 500;
  color: rgb(255 255 255 / 90%); /* 🎯 修复：使用高对比度白色，确保在紫色背景下清晰可见 */

  /* 🎯 现代文本溢出处理 */
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 🎯 描述信息样式 */
.description-info {
  width: 100%;
  min-width: 0;
}

.description-text {
  overflow: hidden;
  font-size: 0.85rem;
  color: rgb(255 255 255 / 80%); /* 🎯 修复：使用半透明白色，确保在紫色背景下清晰可见 */

  /* 🎯 现代文本溢出处理 */
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 🎯 状态容器样式 */
.status-container {
  display: flex;
  width: 100%;
  min-width: 0;
  justify-content: center;
}

.status-badge {
  max-width: 100%;
  padding: 4px 8px;
  border-radius: 12px;
  overflow: hidden;
  font-size: 0.75rem;
  font-weight: 500;

  /* 🎯 现代文本溢出处理 */
  white-space: nowrap;
  text-overflow: ellipsis;
}

.status-badge.available {
  font-weight: 600; /* 🎯 增加字重，提高可读性 */
  color: #047857; /* 🎯 优化：使用更深的绿色，提高对比度 */
  background: rgb(34 197 94 / 20%); /* 🎯 优化：增加背景透明度 */
}

.status-badge.unavailable {
  font-weight: 600; /* 🎯 增加字重，提高可读性 */
  color: #b91c1c; /* 🎯 优化：使用更深的红色，提高对比度 */
  background: rgb(239 68 68 / 20%); /* 🎯 优化：增加背景透明度 */
}

/* 🎯 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
  width: 100%;
  min-width: 0;
  justify-content: flex-start;
}

.action-btn-small {
  display: flex;
  min-width: 0;
  padding: 6px 10px;
  border: none;
  border-radius: 6px;
  overflow: hidden;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s ease;
  align-items: center;
  gap: 4px;
  cursor: pointer;

  /* 🎯 现代文本溢出处理 */
  white-space: nowrap;
  text-overflow: ellipsis;
  flex-shrink: 1;
}

.action-btn-small.primary {
  font-weight: 500; /* 🎯 增加字重，提高可读性 */
  color: #1d4ed8; /* 🎯 优化：使用更深的蓝色，提高对比度 */
  background: rgb(59 130 246 / 20%); /* 🎯 优化：增加背景透明度 */
}

.action-btn-small.secondary {
  font-weight: 500; /* 🎯 增加字重，提高可读性 */
  color: #374151; /* 🎯 优化：使用更深的灰色，提高对比度 */
  background: rgb(107 114 128 / 20%); /* 🎯 优化：增加背景透明度 */
}

.action-btn-small.danger {
  font-weight: 500; /* 🎯 增加字重，提高可读性 */
  color: #b91c1c; /* 🎯 优化：使用更深的红色，提高对比度 */
  background: rgb(239 68 68 / 20%); /* 🎯 优化：增加背景透明度 */
}

.action-btn-small:hover {
  box-shadow: 0 2px 8px rgb(0 0 0 / 10%);
  transform: translateY(-1px);
}

.btn-icon {
  flex-shrink: 0;
}

.btn-text {
  min-width: 0;
  overflow: hidden;

  /* 🎯 现代文本溢出处理 */
  white-space: nowrap;
  text-overflow: ellipsis;
}

/* 技师信息样式 */
.therapist-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.therapist-avatar {
  display: flex;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background: rgb(139 92 246 / 15%);
  align-items: center;
  justify-content: center;
}

.therapist-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  font-size: 1rem;
  font-weight: 600;
  color: #4f46e5;
}

.therapist-details {
  flex: 1;
}

.therapist-name {
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 2px;
}

.therapist-id {
  font-size: 0.8rem;
  color: #6b7280;
}

/* 联系信息样式 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.phone {
  font-weight: 500;
  color: #1f2937;
}

.email {
  font-size: 0.8rem;
  color: #6b7280;
}

/* 绩效信息样式 */
.performance-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 0.8rem;
  opacity: 0.3;
  transition: opacity 0.2s ease;
}

.star.active {
  opacity: 1;
}

.rating-text {
  font-size: 0.8rem;
  font-weight: 500;
  color: #1f2937;
}

.performance-stats {
  font-size: 0.75rem;
  color: #6b7280;
}

/* 日期信息样式 */
.date-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.join-date {
  font-weight: 500;
  color: #1f2937;
}

.work-duration {
  font-size: 0.8rem;
  color: #6b7280;
}

/* 状态标签样式 */
.status-container {
  display: flex;
  justify-content: center;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  backdrop-filter: blur(10px) saturate(1.2);
}

.status-badge.active {
  border: 1px solid rgb(34 197 94 / 20%);
  color: #059669;
  background: rgb(34 197 94 / 10%);
}

.status-badge.vacation {
  border: 1px solid rgb(245 158 11 / 20%);
  color: #d97706;
  background: rgb(245 158 11 / 10%);
}

.status-badge.inactive {
  border: 1px solid rgb(239 68 68 / 20%);
  color: #dc2626;
  background: rgb(239 68 68 / 10%);
}

/* 操作按钮样式 */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn-small {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px) saturate(1.2);
}

.action-btn-small.primary {
  border: 1px solid rgb(139 92 246 / 20%);
  color: #4f46e5;
  background: rgb(139 92 246 / 15%);
}

.action-btn-small.primary:hover {
  background: rgb(139 92 246 / 20%);
  transform: translateY(-1px);
}

.action-btn-small.secondary {
  border: 1px solid rgb(255 255 255 / 15%);
  color: #1f2937;
  background: rgb(255 255 255 / 5%);
}

.action-btn-small.secondary:hover {
  background: rgb(255 255 255 / 10%);
  transform: translateY(-1px);
}

.action-btn-small.danger {
  border: 1px solid rgb(239 68 68 / 20%);
  color: #dc2626;
  background: rgb(239 68 68 / 10%);
}

.action-btn-small.danger:hover {
  background: rgb(239 68 68 / 15%);
  transform: translateY(-1px);
}

/* 加载和空状态样式 */
.table-loading,
.table-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
}

.loading-content,
.empty-content {
  text-align: center;
}

.loading-spinner {
  font-size: 2rem;
  margin-bottom: 12px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1rem;
  color: #6b7280;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 8px;
}

.empty-hint {
  font-size: 0.9rem;
  color: #6b7280;
}

/* 翻页组件样式 */
.pagination-container {
  display: flex !important;
  position: fixed !important;
  right: 50px !important;
  bottom: 20px !important;
  left: auto !important;
  z-index: var(--z-modal);
  width: auto !important;
  height: 30px !important;
  margin: 0 !important;
  padding: 4px 12px !important;
  border: 1px solid rgb(139 92 246 / 15%) !important;
  border-radius: 12px !important;
  background: rgb(139 92 246 / 3%) !important;
  box-shadow: 0 4px 16px rgb(139 92 246 / 8%) !important;
  backdrop-filter: blur(20px) !important;
  justify-content: flex-end !important;
  align-items: center !important;
  flex-wrap: nowrap !important;
  gap: 15px !important;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  font-size: 11px;
  font-weight: 600;
  color: #4f46e5;
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
  margin-right: 20px;
}

.highlight-number {
  font-size: 12px;
  font-weight: 700;
  color: #7c3aed;
  text-shadow: 1px 1px 2px rgb(0 0 0 / 40%);
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-label {
  font-size: 10px;
  font-weight: 500;
  color: #6b7280;
}

.page-size-select {
  padding: 2px 6px;
  border: 1px solid rgb(139 92 246 / 20%);
  border-radius: 4px;
  font-size: 10px;
  color: #4f46e5;
  background: rgb(255 255 255 / 10%);
  cursor: pointer;
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  padding: 4px 8px;
  border: 1px solid rgb(139 92 246 / 20%);
  border-radius: 4px;
  font-size: 10px;
  color: #4f46e5;
  background: rgb(255 255 255 / 10%);
  transition: all 0.2s ease;
  cursor: pointer;
}

.page-btn:hover:not(:disabled) {
  background: rgb(139 92 246 / 10%);
  transform: translateY(-1px);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-btn.active {
  font-weight: 600;
  color: #4f46e5;
  background: rgb(139 92 246 / 20%);
}

.page-numbers {
  display: flex;
  gap: 4px;
}





/* 🎯 确保数据容器无滚动条 - 用户同意的设置 */
.data-cubism {
  overflow: hidden;
}

.picasso-services {
  height: 100vh;
  overflow: hidden;
}



/* 🎯 表格与菜单完美对齐 - 用户确认的优化效果 */
.data-row:nth-child(1) {
  transform: translateY(14.14px);
}

.data-row:nth-child(2) {
  transform: translateY(22.14px);
}

.data-row:nth-child(3) {
  transform: translateY(30.14px);
}

.data-row:nth-child(4) {
  transform: translateY(38.14px);
}

.data-row:nth-child(5) {
  transform: translateY(46.14px);
}

.data-row:nth-child(6) {
  transform: translateY(54.14px);
}

.data-row:nth-child(7) {
  transform: translateY(62.14px);
}

.data-row:nth-child(8) {
  transform: translateY(70.14px);
}

.data-row:nth-child(9) {
  transform: translateY(78.14px);
}

.data-row:nth-child(10) {
  transform: translateY(86.14px);
}

/* 🎯 容器高度优化 - 解决最后一行显示问题 */
.table-body {
  height: 620px;
  max-height: 620px;
  overflow: hidden;
  overflow-y: hidden;
}

.table-container {
  height: 680px;
  max-height: none;
  overflow: visible;
}

.data-cubism {
  height: 730px;
  max-height: none;
  overflow: hidden;
}

/* 🎯 翻页组件右对齐优化 - 用户确认的效果 */
.pagination-container {
  display: flex;
  position: fixed;
  right: 50px;
  bottom: 15px;
  left: auto;
  z-index: var(--z-tooltip); /* 🎯 修复：降低z-index，避免遮挡模态框 */
  width: auto;
  height: 30px;
  margin: 0;
  padding: 6px 12px;
  border: 1px solid rgb(139 92 246 / 15%);
  border-radius: 8px;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 2px 10px rgb(0 0 0 / 20%);
  backdrop-filter: blur(15px);
  justify-content: flex-end;
  align-items: center;
  flex-wrap: nowrap;
  gap: 15px;
}



/* 🎯 最后一行底部边距优化 */
.data-row:last-child {
  margin-bottom: 10px;
  position: relative;
  z-index: 1;
}

/* 🎯 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(8px);
  z-index: 9999; /* 🎯 修复：使用最高z-index确保模态框在最顶层 */
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.modal-container {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 16px;
  border: 1px solid rgba(255, 255, 255, 0.2);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  width: 100%;
  max-width: 600px;
  max-height: 90vh;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
  z-index: 10000; /* 🎯 确保模态框内容在最顶层 */
  position: relative;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20px 24px;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(139, 92, 246, 0.1);
}

.modal-title {
  margin: 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #1f2937;
}

.modal-close-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.2);
  color: #6b7280;
  font-size: 1.2rem;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close-btn:hover {
  background: rgba(239, 68, 68, 0.2);
  color: #dc2626;
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  flex: 1;
  margin-bottom: 16px;
}

.form-label {
  display: block;
  margin-bottom: 6px;
  font-size: 0.9rem;
  font-weight: 500;
  color: #374151;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid rgba(209, 213, 219, 0.8);
  border-radius: 8px;
  font-size: 0.9rem;
  color: #1f2937;
  background: rgba(255, 255, 255, 0.8);
  transition: all 0.2s ease;
  box-sizing: border-box;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: #6366f1;
  box-shadow: 0 0 0 3px rgba(99, 102, 241, 0.1);
  background: rgba(255, 255, 255, 0.95);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  background: rgba(249, 250, 251, 0.8);
}

.btn-cancel,
.btn-save {
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-cancel {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
  border: 1px solid rgba(107, 114, 128, 0.3);
}

.btn-cancel:hover {
  background: rgba(107, 114, 128, 0.2);
  color: #4b5563;
}

.btn-save {
  background: linear-gradient(135deg, #6366f1, #8b5cf6);
  color: white;
  box-shadow: 0 2px 8px rgba(99, 102, 241, 0.3);
}

.btn-save:hover {
  background: linear-gradient(135deg, #5b5bd6, #7c3aed);
  box-shadow: 0 4px 12px rgba(99, 102, 241, 0.4);
  transform: translateY(-1px);
}

/* 🎯 列宽调整拖拽手柄样式 */
.column-resizer {
  position: absolute;
  top: 0;
  right: 0;
  width: 4px;
  height: 100%;
  background: transparent;
  cursor: col-resize;
  z-index: var(--z-tooltip);
  transition: background-color 0.2s ease;
}

.column-resizer:hover {
  background: rgba(139, 92, 246, 0.5);
}

.column-resizer:active {
  background: rgba(139, 92, 246, 0.8);
}

/* 🎯 调整时的视觉反馈 */
.header-cell {
  position: relative;
}

.header-cell:hover .column-resizer {
  background: rgba(139, 92, 246, 0.3);
}

/* 🎯 拖拽时的全局样式 */
body.resizing {
  cursor: col-resize !important;
  user-select: none !important;
}

body.resizing * {
  cursor: col-resize !important;
  user-select: none !important;
}

/* 响应式设计 */
@media (width <= 768px) {
  .picasso-services {
    padding: 16px;
    font-size: 14px;
  }

  .header-columns {
    flex-direction: column;
    gap: 12px;
  }

  .header-cell {
    width: 100%;
    justify-content: space-between;
  }
}
</style>