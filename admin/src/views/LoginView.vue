<template>
  <!-- Code<PERSON><PERSON> Wavy Login Form - 完整原版效果 -->
  <div class="wavy-login-form">
    <!-- 波浪背景容器 -->
    <div class="wave-container">
      <svg class="waves" xmlns="http://www.w3.org/2000/svg" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto">
        <defs>
          <path id="gentle-wave" d="m-160 44c30 0 58-18 88-18s 58 18 88 18 58-18 88-18 58 18 88 18 v44h-352z" />
        </defs>
        <g class="parallax">
          <use href="#gentle-wave" x="48" y="0" fill="rgba(255,255,255,0.7)" />
          <use href="#gentle-wave" x="48" y="3" fill="rgba(255,255,255,0.5)" />
          <use href="#gentle-wave" x="48" y="5" fill="rgba(255,255,255,0.3)" />
          <use href="#gentle-wave" x="48" y="7" fill="#fff" />
        </g>
      </svg>
    </div>

    <!-- 登录表单容器 -->
    <div class="login-container">
      <div class="login-box">
        <!-- 登录表单 -->
        <form class="login-form" @submit.prevent="handleLogin">
          <!-- 用户名输入框 -->
          <div class="input-container">
            <input
              type="text"
              v-model="loginData.username"
              class="form-input"
              required
              ref="usernameInput"
              @keyup.enter="focusPassword"
            />
            <label class="form-label">用户名</label>
            <span class="form-highlight"></span>
            <span class="form-bar"></span>
          </div>

          <!-- 密码输入框 -->
          <div class="input-container">
            <input
              type="password"
              v-model="loginData.password"
              class="form-input"
              required
              ref="passwordInput"
              @keyup.enter="handleLogin"
            />
            <label class="form-label">密码</label>
            <span class="form-highlight"></span>
            <span class="form-bar"></span>
          </div>

          <!-- 登录按钮 -->
          <button
            type="submit"
            class="login-btn"
            :disabled="!isFormValid || loading"
          >
            <span v-if="!loading">登录</span>
            <span v-else>登录中...</span>
          </button>
        </form>

        <!-- 品牌信息 -->
        <div class="brand-info">
          <p>© 2025 壹心堂 Yixintang</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { useUserStore } from '@/store';
import { message } from 'ant-design-vue';

export default {
  name: 'WavyLoginForm',
  setup() {
    const router = useRouter();
    const userStore = useUserStore();
    
    // 响应式数据
    const loading = ref(false);
    const usernameInput = ref(null);
    const passwordInput = ref(null);
    
    // 登录表单数据
    const loginData = reactive({
      username: '',
      password: ''
    });
    
    // 表单验证
    const isFormValid = computed(() => {
      return loginData.username.trim().length >= 2 && 
             loginData.password.trim().length >= 6;
    });
    
    // 聚焦到密码框
    const focusPassword = () => {
      nextTick(() => {
        passwordInput.value?.focus();
      });
    };
    
    // 登录处理
    const handleLogin = async () => {
      if (!isFormValid.value) {
        message.error('请填写完整的登录信息');
        return;
      }

      loading.value = true;

      try {
        // 调用登录API (Store中已处理消息提示)
        const success = await userStore.login({
          username: loginData.username.trim(),
          password: loginData.password.trim()
        });

        if (success) {
          // 登录成功，跳转到仪表板
          router.push('/dashboard');
        }
        // 错误消息已在Store中处理，无需重复显示
      } catch (error) {
        console.error('登录错误:', error);
        message.error('登录失败，请稍后重试');
      } finally {
        loading.value = false;
      }
    };
    
    // 页面加载完成后聚焦到用户名输入框
    onMounted(() => {
      nextTick(() => {
        usernameInput.value?.focus();
      });
    });
    
    return {
      loading,
      loginData,
      isFormValid,
      usernameInput,
      passwordInput,
      focusPassword,
      handleLogin
    };
  }
};
</script>

<style scoped>
/*
=============================================================================
CodePen Wavy Login Form - 完整原版效果
=============================================================================
作者: 基于CodePen banik/dgQvWO
重构: Augment Agent
创建时间: 2025-07-19
描述: 完全按照CodePen原版的Wavy Login Form实现
特点: SVG波浪动画 + Material Design输入框 + 渐变背景
技术: SVG动画 + CSS3变换 + Vue3集成
=============================================================================
*/

/* 重置和基础样式 */
* {
  box-sizing: border-box;
}

/* 主容器 - 全屏渐变背景 */
.wavy-login-form {
  display: flex;
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  align-items: center;
  justify-content: center;
}

/* SVG波浪动画容器 */
.wave-container {
  position: absolute;
  bottom: 0;
  left: 0;
  width: 100%;
  height: 15vh;
  max-height: 150px;
  min-height: 100px;
  margin-bottom: -7px;
}

/* SVG波浪样式 */
.waves {
  position: relative;
  width: 100%;
  height: 15vh;
  max-height: 150px;
  min-height: 100px;
  margin-bottom: -7px;
}

/* 波浪动画 */
.parallax > use {
  animation: move-forever 25s cubic-bezier(.55,.5,.45,.5) infinite;
}

.parallax > use:nth-child(1) {
  animation-delay: -2s;
  animation-duration: 7s;
}

.parallax > use:nth-child(2) {
  animation-delay: -3s;
  animation-duration: 10s;
}

.parallax > use:nth-child(3) {
  animation-delay: -4s;
  animation-duration: 13s;
}

.parallax > use:nth-child(4) {
  animation-delay: -5s;
  animation-duration: 20s;
}

@keyframes move-forever {
  0% {
    transform: translate3d(-90px,0,0);
  }

  100% {
    transform: translate3d(85px,0,0);
  }
}

/* 登录容器 */
.login-container {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 400px;
  padding: 20px;
}

/* 登录框 */
.login-box {
  padding: 40px 30px;
  border: 1px solid rgb(255 255 255 / 30%);
  border-radius: 15px;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 15px 35px rgb(0 0 0 / 10%);
  backdrop-filter: blur(20px);
}

/* 登录标题已删除 - 去掉"Please Sign In" */

/* 表单样式 */
.login-form {
  margin-bottom: 20px;
}

/* 输入框容器 */
.input-container {
  position: relative;
  margin-bottom: 30px;
}

/* 输入框样式 */
.form-input {
  width: 100%;
  padding: 10px 0;
  border: none;
  font-size: 16px;
  color: #333;
  background: transparent;
  transition: border-color 0.3s ease;
  border-bottom: 1px solid #ddd;
  outline: none;
}

.form-input:focus {
  border-bottom-color: #667eea;
}

/* 标签样式 */
.form-label {
  position: absolute;
  top: 10px;
  left: 0;
  font-size: 16px;
  color: #999;
  pointer-events: none;
  transition: 0.3s ease all;
}

.form-input:focus ~ .form-label,
.form-input:valid ~ .form-label {
  top: -20px;
  font-size: 12px;
  color: #667eea;
}

/* 高亮条 */
.form-highlight {
  position: absolute;
  top: 25%;
  left: 0;
  width: 100px;
  height: 60%;
  opacity: 0.5;
  pointer-events: none;
}

/* 底部条 */
.form-bar {
  display: block;
  position: relative;
  width: 100%;
}

.form-bar::before,
.form-bar::after {
  position: absolute;
  bottom: 1px;
  width: 0;
  height: 2px;
  background: #667eea;
  transition: 0.3s ease all;
  content: '';
}

.form-bar::before {
  left: 50%;
}

.form-bar::after {
  right: 50%;
}

.form-input:focus ~ .form-bar::before,
.form-input:focus ~ .form-bar::after {
  width: 50%;
}

/* 登录按钮 */
.login-btn {
  width: 100%;
  padding: 15px;
  border: none;
  border-radius: 25px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background: linear-gradient(45deg, #667eea, #764ba2);
  transition: all 0.3s ease;
  cursor: pointer;
  margin-top: 20px;
}

.login-btn:hover {
  box-shadow: 0 10px 20px rgb(102 126 234 / 30%);
  transform: translateY(-2px);
}

.login-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

/* 品牌信息 */
.brand-info {
  font-size: 12px;
  text-align: center;
  color: #666;
  margin-top: 20px;
}

/* 响应式设计 */
@media (width <= 480px) {
  .login-box {
    margin: 20px;
    padding: 30px 20px;
  }

  .login-title {
    font-size: 24px;
  }

  .form-input {
    font-size: 14px;
  }

  .login-btn {
    padding: 12px;
    font-size: 14px;
  }
}
</style>
.wave-background {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
