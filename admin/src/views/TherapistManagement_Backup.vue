<template>
  <div class="picasso-services">
    <!-- 科幻通知组件 -->
    <SciFiNotification ref="notification" />

    <!-- 毕加索风格数据表格 -->
    <div class="data-cubism" :style="{ height: dynamicTableHeight + 'px' }">
      <div class="table-container">
        <!-- 集成操作功能的智能表头 -->
        <div class="smart-table-header">
          <!-- 第二行：列标题（支持排序） -->
          <div class="header-columns">
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.name }" style="flex: 2.2;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.name" class="header-search-container" @mouseleave="handleSearchMouseLeave('name')">
                <input
                  :ref="el => searchInputRefs.name = el"
                  type="text"
                  placeholder="🔍 搜索技师（支持汉字/拼音）..."
                  v-model="searchValues.name"
                  @input="handleSearchInput('name')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('name')"
                  @keydown.enter="handleSearchEnter($event, 'name')"
                  @keydown.esc="exitSearchMode('name')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('name')">
                <span class="header-text">技师信息</span>
              </div>
              <!-- 动态按钮：有输入内容时显示关闭按钮，加载时显示加载状态，否则显示排序按钮 -->
              <button v-if="searchValues.name && searchValues.name.trim()" @click="exitSearchMode('name')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.name.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('name')" :disabled="sortButtonStates.name.disabled" title="排序技师信息">
                <span class="sort-indicator" :class="getSortClass('name')">
                  {{ getSortIcon('name') }}
                </span>
              </button>
            </div>
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.phone }" style="flex: 1;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.phone" class="header-search-container" @mouseleave="handleSearchMouseLeave('phone')">
                <input
                  :ref="el => searchInputRefs.phone = el"
                  type="text"
                  placeholder="🔍 搜索电话..."
                  v-model="searchValues.phone"
                  @input="handleSearchInput('phone')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('phone')"
                  @keydown.enter="handleSearchEnter($event, 'phone')"
                  @keydown.esc="exitSearchMode('phone')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('phone')">
                <span class="header-text">联系方式</span>
              </div>
              <!-- 动态按钮：有输入内容时显示关闭按钮，加载时显示加载状态，否则显示排序按钮 -->
              <button v-if="searchValues.phone && searchValues.phone.trim()" @click="exitSearchMode('phone')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.phone.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('phone')" :disabled="sortButtonStates.phone.disabled" title="排序联系方式">
                <span class="sort-indicator" :class="getSortClass('phone')">
                  {{ getSortIcon('phone') }}
                </span>
              </button>
            </div>
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.rating }" style="flex: 1;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.rating" class="header-search-container" @mouseleave="handleSearchMouseLeave('rating')">
                <input
                  :ref="el => searchInputRefs.rating = el"
                  type="text"
                  placeholder="🔍 搜索评分..."
                  v-model="searchValues.rating"
                  @input="handleSearchInput('rating')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('rating')"
                  @keydown.enter="handleSearchEnter($event, 'rating')"
                  @keydown.esc="exitSearchMode('rating')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('rating')">
                <span class="header-text">绩效评价</span>
              </div>
              <!-- 动态按钮：有输入内容时显示关闭按钮，加载时显示加载状态，否则显示排序按钮 -->
              <button v-if="searchValues.rating && searchValues.rating.trim()" @click="exitSearchMode('rating')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.rating.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('rating')" :disabled="sortButtonStates.rating.disabled" title="排序绩效评价">
                <span class="sort-indicator" :class="getSortClass('rating')">
                  {{ getSortIcon('rating') }}
                </span>
              </button>
            </div>
            <div class="header-cell service-info-header" :class="{ transitioning: isTransitioning.joinDate }" style="flex: 0.9;">
              <!-- 搜索模式 -->
              <div v-if="searchModes.joinDate" class="header-search-container" @mouseleave="handleSearchMouseLeave('joinDate')">
                <input
                  :ref="el => searchInputRefs.joinDate = el"
                  type="text"
                  placeholder="🔍 搜索入职时间..."
                  v-model="searchValues.joinDate"
                  @input="handleSearchInput('joinDate')"
                  @compositionstart="handleCompositionStart"
                  @compositionend="handleCompositionEnd"
                  @blur="handleSearchBlur('joinDate')"
                  @keydown.enter="handleSearchEnter($event, 'joinDate')"
                  @keydown.esc="exitSearchMode('joinDate')"
                  class="header-search-input"
                  autocomplete="off"
                />
              </div>
              <!-- 普通模式 -->
              <div v-else class="header-normal-container"
                   @click="handleClickToSearch('joinDate')">
                <span class="header-text">入职时间</span>
              </div>
              <!-- 动态按钮：有输入内容时显示关闭按钮，加载时显示加载状态，否则显示排序按钮 -->
              <button v-if="searchValues.joinDate && searchValues.joinDate.trim()" @click="exitSearchMode('joinDate')" class="search-close-btn" title="退出搜索">×</button>
              <button v-else-if="sortButtonStates.joinDate.loading" class="sort-btn loading" disabled title="正在准备排序功能...">
                <span class="sort-loading-indicator">
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                  <span class="loading-dot"></span>
                </span>
              </button>
              <button v-else class="sort-btn" @click="handleSort('joinDate')" :disabled="sortButtonStates.joinDate.disabled" title="排序入职时间">
                <span class="sort-indicator" :class="getSortClass('joinDate')">
                  {{ getSortIcon('joinDate') }}
                </span>
              </button>
            </div>
            <div class="header-cell" style="flex: 0.9;">
              <!-- 状态筛选按钮 - 与其他表头保持一致的结构 -->
              <div class="header-normal-container">
                <span class="header-text">状态</span>
              </div>
              <!-- 状态筛选按钮 - 替代排序按钮的位置 -->
              <button
                class="status-filter-btn"
                @click="toggleStatusFilter"
                @keydown.enter="toggleStatusFilter"
                @keydown.space.prevent="toggleStatusFilter"
                :class="getStatusFilterClass()"
                :title="getStatusFilterTitle()"
                aria-label="状态筛选按钮"
                tabindex="0">
                <span class="filter-icon">{{ getStatusFilterIcon() }}</span>
                <span class="filter-text">{{ getStatusFilterLabel() }}</span>
              </button>
            </div>
            <div class="header-cell" style="flex: 2;">
              <span>操作</span>
              <button class="header-add-btn-small" @click="showAddModal" title="新增技师">
                <span class="add-icon">➕</span>
                <span class="add-text">新增</span>
              </button>
            </div>
          </div>
        </div>

    <!-- 🎯 数据表格 -->
    <div class="table-container">
      <div class="smart-table">
        <!-- 表格头部 -->
        <div class="smart-table-header">
          <div class="header-cell" style="flex: 1.5;">
            <span class="header-text">技师信息</span>
          </div>
          <div class="header-cell" style="flex: 1.5;">
            <span class="header-text">联系方式</span>
          </div>
          <div class="header-cell" style="flex: 2;">
            <span class="header-text">绩效评价</span>
          </div>
          <div class="header-cell" style="flex: 1.5;">
            <span class="header-text">入职时间</span>
          </div>
          <div class="header-cell" style="flex: 1;">
            <span class="header-text">状态</span>
          </div>
          <div class="header-cell" style="flex: 1.8;">
            <span class="header-text">操作</span>
          </div>
        </div>

        <!-- 表格主体 -->
        <div class="smart-table-body">
          <!-- 加载状态 -->
          <div v-if="loadingStates.dataLoading" class="table-loading">
            <div class="loading-content">
              <div class="loading-spinner">⏳</div>
              <div class="loading-text">正在加载技师数据...</div>
            </div>
          </div>

          <!-- 空数据状态 -->
          <div v-else-if="paginatedData.length === 0" class="table-empty">
            <div class="empty-content">
              <div class="empty-icon">👨‍⚕️</div>
              <div class="empty-text">暂无技师数据</div>
              <div class="empty-hint">点击"新增技师"按钮添加第一个技师</div>
            </div>
          </div>

          <!-- 数据行 -->
          <div v-else
            v-for="record in paginatedData"
            :key="record.id"
            class="table-row"
          >
            <!-- 技师信息 -->
            <div class="table-cell" style="flex: 1.5;">
              <div class="therapist-info">
                <div class="therapist-avatar">
                  <img v-if="record.avatar" :src="record.avatar" :alt="record.name" />
                  <div v-else class="avatar-placeholder">{{ record.name?.charAt(0) || '技' }}</div>
                </div>
                <div class="therapist-details">
                  <div class="therapist-name">{{ record.name }}</div>
                  <div class="therapist-id">ID: {{ record.id }}</div>
                </div>
              </div>
            </div>

            <!-- 联系方式 -->
            <div class="table-cell" style="flex: 1.5;">
              <div class="contact-info">
                <div class="phone">{{ record.phone }}</div>
                <div class="email" v-if="record.email">{{ record.email }}</div>
              </div>
            </div>

            <!-- 绩效评价 -->
            <div class="table-cell" style="flex: 2;">
              <div class="performance-info">
                <div class="rating-stars">
                  <span v-for="i in 5" :key="i" 
                        class="star" 
                        :class="{ active: i <= record.rating }">⭐</span>
                </div>
                <div class="rating-text">{{ record.rating }}/5 分</div>
                <div class="performance-stats">
                  <span class="stat">本月服务: {{ record.monthlyServices || 0 }}次</span>
                </div>
              </div>
            </div>

            <!-- 入职时间 -->
            <div class="table-cell" style="flex: 1.5;">
              <div class="date-info">
                <div class="join-date">{{ formatDate(record.joinDate) }}</div>
                <div class="work-duration">工作 {{ calculateWorkDuration(record.joinDate) }}</div>
              </div>
            </div>

            <!-- 状态 -->
            <div class="table-cell" style="flex: 1;">
              <div class="status-container">
                <span class="status-badge" :class="record.status">
                  {{ getStatusText(record.status) }}
                </span>
              </div>
            </div>

            <!-- 操作 -->
            <div class="table-cell" style="flex: 1.8;">
              <div class="action-buttons">
                <button class="action-btn-small primary" @click="editTherapist(record)">
                  <span class="btn-icon">✏️</span>
                  <span class="btn-text">编辑</span>
                </button>
                <button class="action-btn-small secondary" @click="viewTherapist(record)">
                  <span class="btn-icon">👁️</span>
                  <span class="btn-text">查看</span>
                </button>
                <button class="action-btn-small danger" @click="deleteTherapist(record)">
                  <span class="btn-icon">🗑️</span>
                  <span class="btn-text">删除</span>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎯 分页组件 -->
    <div class="pagination-container">
      <div class="pagination-info">
        <span>共 {{ totalRecords }} 条记录，第 {{ currentPage }} / {{ totalPages }} 页</span>
      </div>
      <div class="pagination-controls">
        <button class="page-btn" @click="goToPage(1)" :disabled="currentPage === 1">首页</button>
        <button class="page-btn" @click="goToPage(currentPage - 1)" :disabled="currentPage === 1">上一页</button>
        <div class="page-numbers">
          <button v-for="page in visiblePages" 
                  :key="page" 
                  class="page-btn" 
                  :class="{ active: page === currentPage }"
                  @click="goToPage(page)">
            {{ page }}
          </button>
        </div>
        <button class="page-btn" @click="goToPage(currentPage + 1)" :disabled="currentPage === totalPages">下一页</button>
        <button class="page-btn" @click="goToPage(totalPages)" :disabled="currentPage === totalPages">末页</button>
      </div>
    </div>

    <!-- 🎯 新增/编辑技师模态框 -->
    <div v-if="showModal" class="modal-overlay" @click="closeModal">
      <div class="therapist-form-modal" @click.stop>
        <div class="form-header">
          <h3 class="form-title">{{ isEditing ? '编辑技师' : '新增技师' }}</h3>
          <button class="close-btn" @click="closeModal">✕</button>
        </div>
        
        <div class="form-content">
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">技师姓名 <span class="required">*</span></label>
              <input type="text" v-model="formData.name" class="form-input" placeholder="请输入技师姓名" />
            </div>
            <div class="form-group">
              <label class="form-label">手机号码 <span class="required">*</span></label>
              <input type="tel" v-model="formData.phone" class="form-input" placeholder="请输入手机号码" />
            </div>
          </div>
          
          <div class="form-row">
            <div class="form-group">
              <label class="form-label">邮箱地址</label>
              <input type="email" v-model="formData.email" class="form-input" placeholder="请输入邮箱地址" />
            </div>
            <div class="form-group">
              <label class="form-label">工作状态</label>
              <select v-model="formData.status" class="form-select">
                <option value="active">在职</option>
                <option value="vacation">休假</option>
                <option value="inactive">离职</option>
              </select>
            </div>
          </div>
          
          <div class="form-group">
            <label class="form-label">技师简介</label>
            <textarea v-model="formData.description" class="form-textarea" rows="3" placeholder="请输入技师简介"></textarea>
          </div>
        </div>
        
        <div class="form-actions">
          <button class="action-btn secondary" @click="closeModal">取消</button>
          <button class="action-btn primary" @click="saveTherapist">{{ isEditing ? '更新' : '保存' }}</button>
        </div>
      </div>
    </div>

    <!-- 🎯 通知组件 -->
    <div v-if="notification.show" class="notification" :class="notification.type">
      <div class="notification-content">
        <span class="notification-icon">{{ getNotificationIcon(notification.type) }}</span>
        <span class="notification-message">{{ notification.message }}</span>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted } from 'vue';

export default {
  name: 'TherapistManagement',
  setup() {
    // 🎯 响应式数据
    const searchValue = ref('');
    const statusFilter = ref('');
    const showModal = ref(false);
    const isEditing = ref(false);
    const currentPage = ref(1);
    const pageSize = ref(10);

    // 🎯 加载状态
    const loadingStates = reactive({
      dataLoading: false,
      saving: false
    });

    // 🎯 技师数据
    const therapists = ref([
      {
        id: 'T001',
        name: '张美丽',
        phone: '13800138001',
        email: '<EMAIL>',
        status: 'active',
        rating: 5,
        joinDate: '2023-01-15',
        monthlyServices: 45,
        avatar: '',
        description: '资深按摩技师，擅长中式按摩和足疗'
      },
      {
        id: 'T002',
        name: '李小芳',
        phone: '13800138002',
        email: '<EMAIL>',
        status: 'active',
        rating: 4,
        joinDate: '2023-03-20',
        monthlyServices: 38,
        avatar: '',
        description: '专业美容师，精通面部护理和身体护理'
      },
      {
        id: 'T003',
        name: '王师傅',
        phone: '13800138003',
        email: '<EMAIL>',
        status: 'vacation',
        rating: 5,
        joinDate: '2022-08-10',
        monthlyServices: 0,
        avatar: '',
        description: '中医推拿专家，有20年从业经验'
      }
    ]);

    // 🎯 表单数据
    const formData = reactive({
      id: '',
      name: '',
      phone: '',
      email: '',
      status: 'active',
      description: '',
      rating: 5,
      joinDate: new Date().toISOString().split('T')[0],
      monthlyServices: 0,
      avatar: ''
    });

    // 🎯 通知系统
    const notification = reactive({
      show: false,
      type: 'success',
      message: ''
    });

    // 🎯 计算属性
    const filteredData = computed(() => {
      let filtered = therapists.value;

      // 搜索过滤
      if (searchValue.value) {
        const search = searchValue.value.toLowerCase();
        filtered = filtered.filter(item =>
          item.name.toLowerCase().includes(search) ||
          item.phone.includes(search)
        );
      }

      // 状态过滤
      if (statusFilter.value) {
        filtered = filtered.filter(item => item.status === statusFilter.value);
      }

      return filtered;
    });

    const paginatedData = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      return filteredData.value.slice(start, end);
    });

    const totalRecords = computed(() => filteredData.value.length);
    const totalPages = computed(() => Math.ceil(totalRecords.value / pageSize.value));
    const totalTherapists = computed(() => therapists.value.length);
    const activeTherapists = computed(() => therapists.value.filter(t => t.status === 'active').length);

    const visiblePages = computed(() => {
      const pages = [];
      const start = Math.max(1, currentPage.value - 2);
      const end = Math.min(totalPages.value, currentPage.value + 2);

      for (let i = start; i <= end; i++) {
        pages.push(i);
      }

      return pages;
    });

    // 🎯 方法定义
    const handleSearch = () => {
      currentPage.value = 1;
    };

    const handleFilterChange = () => {
      currentPage.value = 1;
    };

    const loadTherapists = async () => {
      loadingStates.dataLoading = true;
      try {
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 1000));
        showNotification('success', '技师数据刷新成功');
      } catch (error) {
        showNotification('error', '刷新失败，请重试');
      } finally {
        loadingStates.dataLoading = false;
      }
    };

    const showAddModal = () => {
      isEditing.value = false;
      resetFormData();
      showModal.value = true;
    };

    const editTherapist = (therapist) => {
      isEditing.value = true;
      Object.assign(formData, therapist);
      showModal.value = true;
    };

    const viewTherapist = (therapist) => {
      // 实现查看技师详情
      showNotification('info', `查看技师：${therapist.name}`);
    };

    const deleteTherapist = (therapist) => {
      if (confirm(`确定要删除技师"${therapist.name}"吗？`)) {
        const index = therapists.value.findIndex(t => t.id === therapist.id);
        if (index > -1) {
          therapists.value.splice(index, 1);
          showNotification('success', '技师删除成功');
        }
      }
    };

    const saveTherapist = async () => {
      if (!formData.name || !formData.phone) {
        showNotification('error', '请填写必填字段');
        return;
      }

      loadingStates.saving = true;
      try {
        await new Promise(resolve => setTimeout(resolve, 1000));

        if (isEditing.value) {
          const index = therapists.value.findIndex(t => t.id === formData.id);
          if (index > -1) {
            therapists.value[index] = { ...formData };
          }
          showNotification('success', '技师信息更新成功');
        } else {
          const newTherapist = {
            ...formData,
            id: 'T' + String(Date.now()).slice(-3).padStart(3, '0')
          };
          therapists.value.push(newTherapist);
          showNotification('success', '技师添加成功');
        }

        closeModal();
      } catch (error) {
        showNotification('error', '保存失败，请重试');
      } finally {
        loadingStates.saving = false;
      }
    };

    const closeModal = () => {
      showModal.value = false;
      resetFormData();
    };

    const resetFormData = () => {
      Object.assign(formData, {
        id: '',
        name: '',
        phone: '',
        email: '',
        status: 'active',
        description: '',
        rating: 5,
        joinDate: new Date().toISOString().split('T')[0],
        monthlyServices: 0,
        avatar: ''
      });
    };

    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page;
      }
    };

    const formatDate = (dateString) => {
      if (!dateString) return '-';
      const date = new Date(dateString);
      return date.toLocaleDateString('zh-CN');
    };

    const calculateWorkDuration = (joinDate) => {
      if (!joinDate) return '-';
      const start = new Date(joinDate);
      const now = new Date();
      const diffTime = Math.abs(now - start);
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      const months = Math.floor(diffDays / 30);
      return months > 0 ? `${months}个月` : `${diffDays}天`;
    };

    const getStatusText = (status) => {
      const statusMap = {
        active: '在职',
        vacation: '休假',
        inactive: '离职'
      };
      return statusMap[status] || status;
    };

    const showNotification = (type, message) => {
      notification.type = type;
      notification.message = message;
      notification.show = true;

      setTimeout(() => {
        notification.show = false;
      }, 3000);
    };

    const getNotificationIcon = (type) => {
      const iconMap = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
      };
      return iconMap[type] || 'ℹ️';
    };

    // 🎯 生命周期
    onMounted(() => {
      loadTherapists();
    });

    return {
      // 数据
      searchValue,
      statusFilter,
      showModal,
      isEditing,
      currentPage,
      pageSize,
      loadingStates,
      therapists,
      formData,
      notification,

      // 计算属性
      filteredData,
      paginatedData,
      totalRecords,
      totalPages,
      totalTherapists,
      activeTherapists,
      visiblePages,

      // 方法
      handleSearch,
      handleFilterChange,
      loadTherapists,
      showAddModal,
      editTherapist,
      viewTherapist,
      deleteTherapist,
      saveTherapist,
      closeModal,
      goToPage,
      formatDate,
      calculateWorkDuration,
      getStatusText,
      showNotification,
      getNotificationIcon
    };
  }
};
</script>

<style scoped>
/* 🎯 CSS变量定义 - 基于服务管理页面标准 */
:root {
  /* Z-index层级系统 */
  --therapist-z-base: 1;
  --therapist-z-content: 10;
  --therapist-z-dropdown: 100;
  --therapist-z-toolbar: 200;
  --therapist-z-table-header: 300;
  --therapist-z-tooltip: 500;
  --therapist-z-modal-backdrop: 1000;
  --therapist-z-modal: 1001;
  --therapist-z-toast: 2000;

  /* 毛玻璃效果标准 */
  --glass-bg-primary: rgb(255 255 255 / 8%);
  --glass-bg-secondary: rgb(255 255 255 / 5%);
  --glass-bg-input: rgb(255 255 255 / 3%);
  --glass-border: rgb(255 255 255 / 15%);
  --glass-blur-standard: blur(25px) saturate(1.5);
  --glass-blur-heavy: blur(40px) saturate(1.8) brightness(1.2);

  /* 品牌色彩 */
  --brand-primary: rgb(139 92 246 / 15%);
  --brand-secondary: rgb(168 85 247 / 12%);
  --text-primary: #1f2937;
  --text-secondary: #6b7280;
}

/* 🎯 主容器 */
.therapist-management-container {
  min-height: 100vh;
  padding: 24px;
  background: linear-gradient(135deg,
    rgb(139 92 246 / 5%) 0%,
    rgb(168 85 247 / 3%) 100%
  );
}

/* 🎯 页面头部 */
.page-header {
  padding: 24px;
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  background: var(--glass-bg-primary);
  margin-bottom: 24px;
  backdrop-filter: var(--glass-blur-standard);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.header-left {
  flex: 1;
}

.page-title {
  margin: 0 0 8px;
  font-size: 2rem;
  font-weight: 700;
  color: var(--text-primary);
  background: linear-gradient(135deg, #4f46e5 0%, #7c3aed 100%);
  background-clip: text;
  -webkit-text-fill-color: transparent;
}

.page-subtitle {
  margin: 0;
  font-size: 1rem;
  color: var(--text-secondary);
}

.header-right {
  display: flex;
  align-items: center;
}

.header-stats {
  display: flex;
  gap: 24px;
}

.stat-item {
  padding: 12px 16px;
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  text-align: center;
  background: var(--glass-bg-secondary);
  backdrop-filter: blur(15px) saturate(1.3);
}

.stat-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--brand-primary);
  margin-bottom: 4px;
}

.stat-label {
  display: block;
  font-size: 0.875rem;
  color: var(--text-secondary);
}

/* 🎯 工具栏 */
.toolbar-container {
  display: flex;
  padding: 16px 20px;
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  background: var(--glass-bg-primary);
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  backdrop-filter: var(--glass-blur-standard);
}

.toolbar-left {
  display: flex;
  gap: 16px;
  align-items: center;
}

.toolbar-right {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* 🎯 搜索框 */
.search-container {
  position: relative;
  width: 300px;
}

.search-input {
  width: 100%;
  height: 40px;
  padding: 0 40px 0 16px;
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  font-size: 0.9rem;
  color: var(--text-primary);
  background: var(--glass-bg-input);
  transition: all 0.2s ease;
  backdrop-filter: blur(20px) saturate(1.3);
}

.search-input:focus {
  outline: none;
  border-color: rgb(139 92 246 / 30%);
  background: rgb(255 255 255 / 10%);
  box-shadow: 0 0 0 2px rgb(139 92 246 / 10%);
}

.search-icon {
  position: absolute;
  top: 50%;
  right: 12px;
  color: var(--text-secondary);
  transform: translateY(-50%);
  pointer-events: none;
}

/* 🎯 过滤器 */
.filter-container {
  width: 150px;
}

.filter-select {
  width: 100%;
  height: 40px;
  padding: 0 12px;
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  font-size: 0.9rem;
  color: var(--text-primary);
  background: var(--glass-bg-input);
  transition: all 0.2s ease;
  backdrop-filter: blur(20px) saturate(1.3);
  cursor: pointer;
}

.filter-select:focus {
  outline: none;
  border-color: rgb(139 92 246 / 30%);
  background: rgb(255 255 255 / 10%);
}

/* 🎯 操作按钮 */
.action-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 10px 16px;
  border: none;
  border-radius: 8px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(15px) saturate(1.3);
}

.action-btn.primary {
  border: 1px solid rgb(139 92 246 / 20%);
  color: #4f46e5;
  background: var(--brand-primary);
}

.action-btn.primary:hover {
  background: rgb(139 92 246 / 20%);
  box-shadow: 0 4px 12px rgb(139 92 246 / 20%);
  transform: translateY(-1px);
}

.action-btn.secondary {
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  background: var(--glass-bg-secondary);
}

.action-btn.secondary:hover {
  background: rgb(255 255 255 / 10%);
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
  transform: translateY(-1px);
}

.btn-icon {
  font-size: 1rem;
}

.btn-text {
  font-size: 0.9rem;
}

/* 🎯 数据表格 */
.table-container {
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  overflow: hidden;
  background: var(--glass-bg-primary);
  backdrop-filter: var(--glass-blur-standard);
  margin-bottom: 24px;
}

.smart-table {
  width: 100%;
}

.smart-table-header {
  display: flex;
  position: sticky;
  top: 0;
  z-index: var(--therapist-z-table-header);
  padding: 16px 20px;
  background: var(--brand-primary);
  align-items: center;
  border-bottom: 1px solid var(--glass-border);
  backdrop-filter: blur(15px) saturate(1.3);
}

.header-cell {
  display: flex;
  align-items: center;
  padding: 0 8px;
}

.header-text {
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--text-primary);
}

.smart-table-body {
  max-height: 600px;
  overflow-y: auto;
}

/* 🎯 表格行 */
.table-row {
  display: flex;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid rgb(255 255 255 / 10%);
  transition: all 0.2s ease;
}

.table-row:hover {
  background: rgb(255 255 255 / 5%);
  backdrop-filter: blur(20px) saturate(1.4);
}

.table-row:last-child {
  border-bottom: none;
}

.table-cell {
  display: flex;
  align-items: center;
  padding: 0 8px;
  font-size: 0.9rem;
}

/* 🎯 技师信息 */
.therapist-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.therapist-avatar {
  display: flex;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  overflow: hidden;
  background: var(--brand-primary);
  align-items: center;
  justify-content: center;
}

.therapist-avatar img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.avatar-placeholder {
  font-size: 1rem;
  font-weight: 600;
  color: #4f46e5;
}

.therapist-details {
  flex: 1;
}

.therapist-name {
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.therapist-id {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 🎯 联系信息 */
.contact-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.phone {
  font-weight: 500;
  color: var(--text-primary);
}

.email {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 🎯 绩效信息 */
.performance-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.rating-stars {
  display: flex;
  gap: 2px;
}

.star {
  font-size: 0.8rem;
  opacity: 0.3;
  transition: opacity 0.2s ease;
}

.star.active {
  opacity: 1;
}

.rating-text {
  font-size: 0.8rem;
  font-weight: 500;
  color: var(--text-primary);
}

.performance-stats {
  font-size: 0.75rem;
  color: var(--text-secondary);
}

/* 🎯 日期信息 */
.date-info {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.join-date {
  font-weight: 500;
  color: var(--text-primary);
}

.work-duration {
  font-size: 0.8rem;
  color: var(--text-secondary);
}

/* 🎯 状态标签 */
.status-container {
  display: flex;
  justify-content: center;
}

.status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 500;
  text-align: center;
  backdrop-filter: blur(10px) saturate(1.2);
}

.status-badge.active {
  border: 1px solid rgb(34 197 94 / 20%);
  color: #059669;
  background: rgb(34 197 94 / 10%);
}

.status-badge.vacation {
  border: 1px solid rgb(245 158 11 / 20%);
  color: #d97706;
  background: rgb(245 158 11 / 10%);
}

.status-badge.inactive {
  border: 1px solid rgb(239 68 68 / 20%);
  color: #dc2626;
  background: rgb(239 68 68 / 10%);
}

/* 🎯 操作按钮组 */
.action-buttons {
  display: flex;
  gap: 8px;
}

.action-btn-small {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border: none;
  border-radius: 6px;
  font-size: 0.8rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  backdrop-filter: blur(10px) saturate(1.2);
}

.action-btn-small.primary {
  border: 1px solid rgb(139 92 246 / 20%);
  color: #4f46e5;
  background: var(--brand-primary);
}

.action-btn-small.primary:hover {
  background: rgb(139 92 246 / 20%);
  transform: translateY(-1px);
}

.action-btn-small.secondary {
  border: 1px solid var(--glass-border);
  color: var(--text-primary);
  background: var(--glass-bg-secondary);
}

.action-btn-small.secondary:hover {
  background: rgb(255 255 255 / 10%);
  transform: translateY(-1px);
}

.action-btn-small.danger {
  border: 1px solid rgb(239 68 68 / 20%);
  color: #dc2626;
  background: rgb(239 68 68 / 10%);
}

.action-btn-small.danger:hover {
  background: rgb(239 68 68 / 15%);
  transform: translateY(-1px);
}

/* 🎯 加载和空状态 */
.table-loading,
.table-empty {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60px 20px;
}

.loading-content,
.empty-content {
  text-align: center;
}

.loading-spinner {
  font-size: 2rem;
  margin-bottom: 12px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1rem;
  color: var(--text-secondary);
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-text {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-primary);
  margin-bottom: 8px;
}

.empty-hint {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

/* 🎯 分页组件 */
.pagination-container {
  display: flex;
  padding: 16px 20px;
  border: 1px solid var(--glass-border);
  border-radius: 12px;
  background: var(--glass-bg-primary);
  justify-content: space-between;
  align-items: center;
  backdrop-filter: var(--glass-blur-standard);
  margin-bottom: 24px;
}

.pagination-info {
  font-size: 0.9rem;
  color: var(--text-secondary);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  padding: 8px 12px;
  border: 1px solid var(--glass-border);
  border-radius: 6px;
  font-size: 0.9rem;
  color: var(--text-primary);
  background: var(--glass-bg-secondary);
  transition: all 0.2s ease;
  cursor: pointer;
  backdrop-filter: blur(10px) saturate(1.2);
}

.page-btn:hover:not(:disabled) {
  background: rgb(255 255 255 / 10%);
  transform: translateY(-1px);
}

.page-btn.active {
  color: #4f46e5;
  background: var(--brand-primary);
  border-color: rgb(139 92 246 / 20%);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

/* 🎯 模态框 */
.modal-overlay {
  display: flex;
  position: fixed;
  inset: 0;
  z-index: var(--therapist-z-modal-backdrop);
  padding: 20px;
  background: rgb(0 0 0 / 50%);
  backdrop-filter: blur(8px);
  justify-content: center;
  align-items: center;
}

.therapist-form-modal {
  z-index: var(--therapist-z-modal);
  width: min(90vw, 600px);
  max-height: calc(100vh - 40px);
  border: 1px solid var(--glass-border);
  border-radius: 16px;
  overflow: hidden;
  background: linear-gradient(145deg,
    rgb(255 255 255 / 8%) 0%,
    rgb(248 250 252 / 12%) 50%,
    rgb(255 255 255 / 8%) 100%
  );
  box-shadow:
    0 25px 50px rgb(0 0 0 / 15%),
    0 0 0 1px rgb(255 255 255 / 20%),
    inset 0 1px 0 rgb(255 255 255 / 30%);
  backdrop-filter: var(--glass-blur-heavy);
}

.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  background: var(--brand-primary);
  border-bottom: 1px solid var(--glass-border);
  backdrop-filter: blur(15px) saturate(1.3);
}

.form-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-primary);
}

.close-btn {
  display: flex;
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  font-size: 1.2rem;
  color: var(--text-primary);
  background: rgb(255 255 255 / 10%);
  transition: all 0.2s ease;
  cursor: pointer;
  align-items: center;
  justify-content: center;
}

.close-btn:hover {
  background: rgb(255 255 255 / 20%);
  transform: scale(1.1);
}

.form-content {
  max-height: 400px;
  padding: 24px;
  overflow-y: auto;
}

.form-row {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.form-group {
  flex: 1;
  margin-bottom: 16px;
}

.form-label {
  display: block;
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: 6px;
}

.required {
  color: #dc2626;
}

.form-input,
.form-select,
.form-textarea {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--glass-border);
  border-radius: 8px;
  font-size: 0.9rem;
  color: var(--text-primary);
  background: var(--glass-bg-input);
  transition: all 0.2s ease;
  backdrop-filter: blur(15px) saturate(1.2);
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
  outline: none;
  border-color: rgb(139 92 246 / 30%);
  background: rgb(255 255 255 / 10%);
  box-shadow: 0 0 0 2px rgb(139 92 246 / 10%);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding: 20px 24px;
  background: rgb(255 255 255 / 3%);
  border-top: 1px solid var(--glass-border);
  backdrop-filter: blur(20px) saturate(1.3);
}

/* 🎯 通知组件 */
.notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: var(--therapist-z-toast);
  padding: 12px 16px;
  border-radius: 8px;
  backdrop-filter: blur(20px) saturate(1.4);
  animation: slideIn 0.3s ease;
}

.notification.success {
  border: 1px solid rgb(34 197 94 / 20%);
  color: #059669;
  background: rgb(34 197 94 / 10%);
}

.notification.error {
  border: 1px solid rgb(239 68 68 / 20%);
  color: #dc2626;
  background: rgb(239 68 68 / 10%);
}

.notification.warning {
  border: 1px solid rgb(245 158 11 / 20%);
  color: #d97706;
  background: rgb(245 158 11 / 10%);
}

.notification.info {
  border: 1px solid rgb(59 130 246 / 20%);
  color: #2563eb;
  background: rgb(59 130 246 / 10%);
}

.notification-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.notification-icon {
  font-size: 1rem;
}

.notification-message {
  font-size: 0.9rem;
  font-weight: 500;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 🎯 响应式设计 */
@media (width <= 768px) {
  .therapist-management-container {
    padding: 16px;
  }

  .header-content {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .toolbar-container {
    flex-direction: column;
    gap: 16px;
  }

  .toolbar-left {
    width: 100%;
    justify-content: center;
  }

  .search-container {
    width: 100%;
    max-width: 300px;
  }

  .header-stats {
    flex-direction: column;
    gap: 12px;
  }

  .form-row {
    flex-direction: column;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .pagination-container {
    flex-direction: column;
    gap: 12px;
    text-align: center;
  }
}
</style>
