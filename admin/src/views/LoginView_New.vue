<template>
  <!-- 完整的Wavy Login Form - 基于CodePen原版设计 -->
  <div class="wavy-container">
    <!-- 波浪动画背景层 -->
    <div class="wave-background">
      <!-- 多层波浪动画 -->
      <div class="wave wave-1"></div>
      <div class="wave wave-2"></div>
      <div class="wave wave-3"></div>
      <div class="wave wave-4"></div>
    </div>
    
    <!-- 登录表单主体 -->
    <div class="login-form-wrapper">
      <div class="login-form-container">
        <!-- 品牌Logo -->
        <div class="brand-logo-section">
          <img src="@/assets/images/logo.png" alt="壹心堂Logo" class="brand-logo" />
          <h1 class="brand-title">壹心堂</h1>
          <p class="brand-subtitle">管理系统</p>
        </div>
        
        <!-- 登录表单 -->
        <form class="wavy-form" @submit.prevent="handleLogin">
          <div class="input-group">
            <input 
              type="text" 
              v-model="loginData.username"
              placeholder="用户名"
              class="wavy-input"
              required
              ref="usernameInput"
              @keyup.enter="focusPassword"
            />
            <label class="input-label">用户名</label>
          </div>
          
          <div class="input-group">
            <input 
              type="password" 
              v-model="loginData.password"
              placeholder="密码"
              class="wavy-input"
              required
              ref="passwordInput"
              @keyup.enter="handleLogin"
            />
            <label class="input-label">密码</label>
          </div>
          
          <button 
            type="submit" 
            class="wavy-submit-btn"
            :disabled="!isFormValid || loading"
          >
            <span v-if="!loading">进入系统</span>
            <span v-else>登录中...</span>
            <div class="btn-wave"></div>
          </button>
        </form>
        
        <!-- 版权信息 -->
        <div class="copyright-info">
          <p>© 2025 壹心堂 Yixintang</p>
          <p>管理系统-专业版</p>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, computed, onMounted, nextTick } from 'vue';
import { useRouter } from 'vue-router';
import { useStore } from 'vuex';
import { message } from 'ant-design-vue';

export default {
  name: 'WavyLoginForm',
  setup() {
    const router = useRouter();
    const store = useStore();
    
    // 响应式数据
    const loading = ref(false);
    const usernameInput = ref(null);
    const passwordInput = ref(null);
    
    // 登录表单数据
    const loginData = reactive({
      username: '',
      password: ''
    });
    
    // 表单验证
    const isFormValid = computed(() => {
      return loginData.username.trim().length >= 2 && 
             loginData.password.trim().length >= 6;
    });
    
    // 聚焦到密码框
    const focusPassword = () => {
      nextTick(() => {
        passwordInput.value?.focus();
      });
    };
    
    // 登录处理
    const handleLogin = async () => {
      if (!isFormValid.value) {
        message.error('请填写完整的登录信息');
        return;
      }
      
      loading.value = true;
      
      try {
        // 调用登录API
        const response = await store.dispatch('auth/login', {
          username: loginData.username.trim(),
          password: loginData.password.trim()
        });
        
        if (response.success) {
          message.success('登录成功！');
          router.push('/dashboard');
        } else {
          message.error(response.message || '登录失败，请检查用户名和密码');
        }
      } catch (error) {
        console.error('登录错误:', error);
        message.error('登录失败，请稍后重试');
      } finally {
        loading.value = false;
      }
    };
    
    // 页面加载完成后聚焦到用户名输入框
    onMounted(() => {
      nextTick(() => {
        usernameInput.value?.focus();
      });
    });
    
    return {
      loading,
      loginData,
      isFormValid,
      usernameInput,
      passwordInput,
      focusPassword,
      handleLogin
    };
  }
};
</script>

<style scoped>
/* 
=============================================================================
Wavy Login Form - 完整的波浪登录表单样式
=============================================================================
作者: Augment Agent
创建时间: 2025-07-19
描述: 基于CodePen Wavy Login Form的完整重构版本
特点: 垂直波浪动画 + 浮动登录表单 + 现代设计
技术: 纯CSS动画 + Vue3响应式
兼容性: 现代浏览器，支持CSS3动画和变换
=============================================================================
*/

/* 
-----------------------------------------------------------------------------
CSS变量定义 - Wavy Login Form主题
-----------------------------------------------------------------------------
*/
:root {
  /* 波浪动画颜色 */
  --wave-primary: #667eea;
  --wave-secondary: #764ba2;
  --wave-accent: #f093fb;
  --wave-light: rgb(255 255 255 / 10%);
  
  /* 背景渐变 */
  --bg-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  
  /* 表单颜色 */
  --form-bg: rgb(255 255 255 / 95%);
  --input-border: #e1e5e9;
  --input-focus: #667eea;
  --text-primary: #2d3748;
  --text-secondary: #718096;
  
  /* 动画时长 */
  --wave-duration: 8s;
  --form-transition: 0.3s ease;
}

/* 
-----------------------------------------------------------------------------
主容器 - 全屏波浪背景
-----------------------------------------------------------------------------
*/
.wavy-container {
  display: flex;
  position: relative;
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  background: var(--bg-gradient);
  align-items: center;
  justify-content: center;
}

/* 
-----------------------------------------------------------------------------
波浪背景动画层
-----------------------------------------------------------------------------
*/
.wave-background {
  position: absolute;
  top: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 100%;
  overflow: hidden;
}

.wave {
  position: absolute;
  top: 0;
  left: -100%;
  width: 200%;
  height: 100%;
  background: linear-gradient(90deg, 
    transparent, 
    var(--wave-light), 
    transparent
  );
  animation: wave-flow var(--wave-duration) ease-in-out infinite;
}

.wave-1 {
  animation-delay: 0s;
  opacity: 0.8;
}

.wave-2 {
  animation-delay: 2s;
  opacity: 0.6;
}

.wave-3 {
  animation-delay: 4s;
  opacity: 0.4;
}

.wave-4 {
  animation-delay: 6s;
  opacity: 0.2;
}

/* 
-----------------------------------------------------------------------------
登录表单容器
-----------------------------------------------------------------------------
*/
.login-form-wrapper {
  position: relative;
  z-index: 10;
  width: 100%;
  max-width: 400px;
  padding: 20px;
}

.login-form-container {
  padding: 40px 30px;
  border: 1px solid rgb(255 255 255 / 30%);
  border-radius: 20px;
  background: var(--form-bg);
  box-shadow: 0 20px 40px rgb(0 0 0 / 10%);
  backdrop-filter: blur(20px);
  animation: form-float 6s ease-in-out infinite;
}

/* 
-----------------------------------------------------------------------------
品牌Logo区域
-----------------------------------------------------------------------------
*/
.brand-logo-section {
  text-align: center;
  margin-bottom: 30px;
}

.brand-logo {
  width: 80px;
  height: 80px;
  object-fit: contain;
  margin-bottom: 15px;
  filter: drop-shadow(0 4px 8px rgb(102 126 234 / 30%));
}

.brand-title {
  margin: 10px 0 5px;
  font-size: 24px;
  font-weight: 600;
  color: var(--text-primary);
}

.brand-subtitle {
  margin: 0;
  font-size: 14px;
  color: var(--text-secondary);
}

/* 
-----------------------------------------------------------------------------
动画关键帧定义
-----------------------------------------------------------------------------
*/
@keyframes wave-flow {
  0% {
    left: -100%;
    transform: skewX(-15deg);
  }

  50% {
    left: -50%;
    transform: skewX(0deg);
  }

  100% {
    left: 0%;
    transform: skewX(15deg);
  }
}

@keyframes form-float {
  0%, 100% {
    transform: translateY(0);
  }

  50% {
    transform: translateY(-10px);
  }
}

/*
-----------------------------------------------------------------------------
表单输入框样式 - Wavy风格
-----------------------------------------------------------------------------
*/
.wavy-form {
  margin-bottom: 20px;
}

.input-group {
  position: relative;
  margin-bottom: 25px;
}

.wavy-input {
  width: 100%;
  padding: 15px 20px;
  border: 2px solid var(--input-border);
  border-radius: 25px;
  font-size: 16px;
  background: rgb(255 255 255 / 90%);
  transition: var(--form-transition);
  outline: none;
}

.wavy-input:focus {
  border-color: var(--input-focus);
  box-shadow: 0 0 0 3px rgb(102 126 234 / 10%);
  transform: translateY(-2px);
}

.wavy-input::placeholder {
  color: var(--text-secondary);
  transition: var(--form-transition);
}

.wavy-input:focus::placeholder {
  opacity: 0.7;
}

.input-label {
  position: absolute;
  top: -10px;
  left: 20px;
  padding: 0 10px;
  font-size: 12px;
  font-weight: 500;
  color: var(--text-secondary);
  background: var(--form-bg);
  opacity: 0;
  transform: translateY(10px);
  transition: var(--form-transition);
}

.wavy-input:focus + .input-label,
.wavy-input:not(:placeholder-shown) + .input-label {
  color: var(--input-focus);
  opacity: 1;
  transform: translateY(0);
}

/*
-----------------------------------------------------------------------------
提交按钮样式 - 波浪效果
-----------------------------------------------------------------------------
*/
.wavy-submit-btn {
  position: relative;
  width: 100%;
  padding: 15px 30px;
  border: none;
  border-radius: 25px;
  overflow: hidden;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background: linear-gradient(45deg, var(--wave-primary), var(--wave-accent));
  transition: var(--form-transition);
  cursor: pointer;
  margin-bottom: 20px;
}

.wavy-submit-btn:hover {
  box-shadow: 0 10px 20px rgb(102 126 234 / 30%);
  transform: translateY(-2px);
}

.wavy-submit-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
  transform: none;
}

.btn-wave {
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgb(255 255 255 / 30%), transparent);
  transition: left 0.5s ease;
}

.wavy-submit-btn:hover .btn-wave {
  left: 100%;
}

/*
-----------------------------------------------------------------------------
版权信息样式
-----------------------------------------------------------------------------
*/
.copyright-info {
  font-size: 12px;
  line-height: 1.4;
  text-align: center;
  color: var(--text-secondary);
}

.copyright-info p {
  margin: 2px 0;
}

/*
-----------------------------------------------------------------------------
响应式设计
-----------------------------------------------------------------------------
*/
@media (width <= 480px) {
  .login-form-container {
    margin: 20px;
    padding: 30px 20px;
  }

  .brand-logo {
    width: 60px;
    height: 60px;
  }

  .brand-title {
    font-size: 20px;
  }

  .wavy-input {
    padding: 12px 16px;
    font-size: 14px;
  }

  .wavy-submit-btn {
    padding: 12px 24px;
    font-size: 14px;
  }
}
</style>
