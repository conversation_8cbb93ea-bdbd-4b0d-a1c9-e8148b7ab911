<template>
  <div class="therapist-management-container">
    <!-- 🎯 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <div class="header-left">
          <h1 class="page-title">技师管理</h1>
          <p class="page-subtitle">管理技师信息、绩效评价和工作状态</p>
        </div>
        <div class="header-right">
          <div class="header-stats">
            <div class="stat-item">
              <span class="stat-value">{{ totalTherapists }}</span>
              <span class="stat-label">总技师数</span>
            </div>
            <div class="stat-item">
              <span class="stat-value">{{ activeTherapists }}</span>
              <span class="stat-label">在职技师</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 🎯 操作工具栏 -->
    <div class="toolbar-container">
      <div class="toolbar-left">
        <div class="search-container">
          <input
            type="text"
            placeholder="搜索技师姓名或电话..."
            v-model="searchValue"
            @input="handleSearch"
            class="search-input"
          />
          <div class="search-icon">🔍</div>
        </div>

        <div class="filter-container">
          <select
            v-model="statusFilter"
            @change="handleFilterChange"
            class="filter-select"
          >
            <option value="">全部状态</option>
            <option value="active">在职</option>
            <option value="vacation">休假</option>
            <option value="inactive">离职</option>
          </select>
        </div>
      </div>

      <div class="toolbar-right">
        <button class="action-btn secondary" @click="loadTherapists">
          <span class="btn-icon">🔄</span>
          <span class="btn-text">刷新</span>
        </button>
        <button class="action-btn primary" @click="showAddModal">
          <span class="btn-icon">➕</span>
          <span class="btn-text">新增技师</span>
        </button>
      </div>
    </div>

    <!-- 🎯 数据表格 -->
    <div class="table-container">
      <div class="smart-table">
        <!-- 表格头部 -->
        <div class="smart-table-header">
          <div class="header-cell" style="flex: 1.5;">
            <span class="header-text">技师信息</span>
          </div>
          <div class="header-cell" style="flex: 1.5;">
            <span class="header-text">联系方式</span>
          </div>
          <div class="header-cell" style="flex: 2;">
            <span class="header-text">绩效评价</span>
          </div>
          <div class="header-cell" style="flex: 1.5;">
            <span class="header-text">入职时间</span>
          </div>
          <div class="header-cell" style="flex: 1;">
            <span class="header-text">状态</span>
          </div>
          <div class="header-cell" style="flex: 1.8;">
            <span class="header-text">操作</span>
          </div>
        </div>

        <!-- 表格主体 -->
        <div class="smart-table-body">
          <!-- 加载状态 -->
          <div v-if="loadingStates.dataLoading" class="table-loading">
            <div class="loading-content">
              <div class="loading-spinner">⏳</div>
              <div class="loading-text">正在加载技师数据...</div>
            </div>
          </div>

          <!-- 空数据状态 -->
          <div v-else-if="paginatedData.length === 0" class="table-empty">
            <div class="empty-content">
              <div class="empty-icon">👨‍⚕️</div>
              <div class="empty-text">暂无技师数据</div>
              <div class="empty-hint">点击"新增技师"按钮添加第一个技师</div>
            </div>
          </div>

          <!-- 数据行 -->
          <div v-else
            v-for="record in paginatedData"
            :key="record.id"
            class="table-row"
          >
            <div class="data-cell" style="flex: 1.5;" data-label="姓名">
              <div class="cell-content">
                <div class="therapist-name">{{ record.name }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1.5;" data-label="手机号">
              <div class="cell-content">
                <div class="therapist-phone">{{ record.phone }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 2;" data-label="绩效评价">
              <div class="cell-content">
                <div class="performance-fragment">
                  <div class="rating-stars">
                    <span v-for="i in 5" :key="i" class="star" :class="{ active: i <= record.rating }">⭐</span>
                  </div>
                  <div class="performance-stats">
                    <div class="stat-item">
                      <span class="stat-value">{{ record.total_services }}</span>
                      <span class="stat-label">服务</span>
                    </div>
                    <div class="stat-item">
                      <span class="stat-value">{{ record.satisfaction }}%</span>
                      <span class="stat-label">满意度</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1.5;" data-label="创建时间">
              <div class="cell-content">
                <div class="created-time">{{ formatDate(record.created_at) }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1;" data-label="状态">
              <div class="cell-content">
                <div class="status-fragment" :class="'status-' + record.status">
                  <div class="status-indicator"></div>
                  <div class="status-text">{{ getStatusText(record.status) }}</div>
                </div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1.8;" data-label="操作">
              <div class="cell-content">
                <div class="action-fragments">
                  <div class="action-btn edit" @click="handleEdit(record)" :class="{'loading': loadingStates.therapistEdit}">
                    <span v-if="!loadingStates.therapistEdit">编辑</span>
                    <span v-else class="loading-spinner">⏳</span>
                  </div>
                  <div class="action-btn toggle" @click="handleToggleStatus(record)" :class="{'loading': loadingStates.statusToggle}">
                    <span v-if="!loadingStates.statusToggle">
                      {{ record.status === 'active' ? '休假' : '上班' }}
                    </span>
                    <span v-else class="loading-spinner">⏳</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 梵高风格分页组件 - 统一标准 -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span class="total-info">
            共 <span class="highlight-number">{{ totalRecords }}</span> 条记录，
            第 <span class="highlight-number">{{ currentPage }}</span> /
            <span class="highlight-number">{{ totalPages }}</span> 页
          </span>
        </div>

        <div class="pagination-controls">
          <div class="page-size-selector">
            <label class="page-size-label">每页显示：</label>
            <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
              <option value="5">5条</option>
              <option value="10">10条</option>
              <option value="20">20条</option>
              <option value="50">50条</option>
            </select>
          </div>

          <div class="page-navigation" v-if="totalRecords > 0">
            <button
              class="page-btn prev-btn"
              @click="prevPage"
              :disabled="currentPage === 1"
             aria-label="操作按钮">
              ‹ 上一页
            </button>

            <div class="page-numbers">
              <button
                v-for="page in visiblePages"
                :key="page"
                class="page-btn page-number"
                :class="{ active: page === currentPage }"
                @click="goToPage(page)"
               aria-label="操作按钮">
                {{ page }}
              </button>
            </div>

            <button
              class="page-btn next-btn"
              @click="nextPage"
              :disabled="currentPage === totalPages"
             aria-label="操作按钮">
              下一页 ›
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 标准模态框 - 参考服务管理模板 -->
    <div v-if="modalVisible" class="modal-overlay">
      <div class="form-modal" @click.stop>
        <div class="form-header">
          <h3 class="form-title">{{ modalTitle }}</h3>
          <button class="close-btn" @click="hideModal" aria-label="操作按钮">×</button>
        </div>

        <div class="form-content">
          <div class="form-cubism">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">技师姓名 <span class="required">*</span></label>
                <input
                  type="text"
                  v-model="formState.name"
                  class="form-input"
                  :class="{ 'error': formErrors.name }"
                  placeholder="请输入技师姓名"
                  required
                  @keyup.enter="handleSubmit"
                  aria-label="输入字段"
                />
                <div v-if="formErrors.name" class="error-message">{{ formErrors.name }}</div>
              </div>

              <div class="form-group">
                <label class="form-label">联系电话 <span class="required">*</span></label>
                <input
                  type="tel"
                  v-model="formState.phone"
                  class="form-input"
                  :class="{ 'error': formErrors.phone }"
                  placeholder="请输入联系电话"
                  required
                  @keyup.enter="handleSubmit"
                aria-label="输入字段">
                <div v-if="formErrors.phone" class="error-message">{{ formErrors.phone }}</div>
              </div>
            </div>

            <!-- 工作时间字段已删除 -->



            <div class="form-row full-width">
              <div class="form-group">
                <label class="form-label">个人简介</label>
                <textarea 
                  v-model="formState.bio" 
                  class="form-textarea"
                  placeholder="请输入技师的个人简介和工作经验..."
                  rows="3"
                ></textarea>
              </div>
            </div>
          </div>
        </div>
        
        <div class="form-actions">
          <button class="action-btn cancel-btn" @click="hideModal" aria-label="操作按钮">
            取消
          </button>
          <button class="action-btn confirm-btn" @click="handleSubmit" :disabled="loadingStates.therapistSubmit" aria-label="操作按钮">
            {{ loadingStates.therapistSubmit ? '提交中...' : '确定' }}
          </button>
        </div>
      </div>
    </div>

    <!-- Toast通知组件 - 符合CI_CD_STANDARDS.md用户反馈规范 -->
    <div v-if="toastState.visible" class="toast-notification" :class="'toast-' + toastState.type">
      <div class="toast-content">
        <div class="toast-icon">
          <span v-if="toastState.type === 'success'">✅</span>
          <span v-else-if="toastState.type === 'error'">❌</span>
          <span v-else-if="toastState.type === 'warning'">⚠️</span>
        </div>
        <div class="toast-message">{{ toastState.message }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

import { ref, reactive, computed, onMounted , nextTick, shallowRef, watchEffect } from 'vue';;

// 响应式数据
const therapists = ref([]);
const modalVisible = ref(false);
const modalTitle = ref('新增技师');
// confirmLoading已删除，使用loadingStates.therapistSubmit
const searchValue = ref('');
// 等级筛选器已删除
const statusFilter = ref('');

// 交互加载状态 - 参考服务管理模板
const loadingStates = reactive({
  dataLoading: false,      // 数据加载状态
  therapistEdit: false,    // 技师编辑加载状态
  therapistSubmit: false,  // 技师提交加载状态
  therapistDelete: false,  // 技师删除加载状态
  statusToggle: false      // 状态切换加载状态
});

// Toast通知状态 - 符合CI_CD_STANDARDS.md用户反馈规范
const toastState = reactive({
  visible: false,
  message: '',
  type: 'success' // success, error, warning
});

// Toast通知函数 - 替代console日志
const showToast = (message, type = 'success') => {
  toastState.message = message;
  toastState.type = type;
  toastState.visible = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    toastState.visible = false;
  }, 3000);
};
const currentPage = ref(1);
const pageSize = ref(5); // 默认5条每页，符合开发规范

// 表单状态
const formState = reactive({
  id: null,
  name: '',
  phone: '',
  bio: '',
  status: 'active'
});

// 表单验证错误状态
const formErrors = reactive({
  name: '',
  phone: ''
});

// 计算属性
const totalRecords = computed(() => therapists.value.length);
const totalPages = computed(() => Math.ceil(therapists.value.length / pageSize.value));
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return therapists.value.slice(start, end);
});

const visiblePages = computed(() => {
  const pages = [];
  const total = totalPages.value;
  const current = currentPage.value;

  for (let i = Math.max(1, current - 2); i <= Math.min(total, current + 2); i++) {
    pages.push(i);
  }
  return pages;
});

// 方法
const loadTherapists = async () => {
  try {
    console.log('🔍 开始加载技师数据...');

    // 设置加载状态
    loadingStates.dataLoading = true;

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 500));

    therapists.value = [
    {
      id: 1,
      name: '王医师',
      phone: '13800138001',
      rating: 5,
      total_services: 156,
      satisfaction: 98,
      work_days: ['周一', '周二', '周三', '周四', '周五'],
      status: 'active',
      bio: '从事中医理疗工作15年，擅长颈椎腰椎疾病治疗',
      created_at: '2020-03-15T09:00:00Z'
    },
    {
      id: 2,
      name: '李医师',
      phone: '13900139002',
      rating: 5,
      total_services: 203,
      satisfaction: 99,
      work_days: ['周二', '周三', '周四', '周五', '周六'],
      status: 'active',
      bio: '中医世家传人，精通各种传统理疗技法',
      created_at: '2019-08-20T10:00:00Z'
    },
    {
      id: 3,
      name: '张医师',
      phone: '13700137003',
      rating: 4,
      total_services: 89,
      satisfaction: 95,
      work_days: ['周三', '周四', '周五', '周六', '周日'],
      status: 'vacation',
      bio: '专业足疗技师，注重客户体验',
      created_at: '2021-12-10T14:00:00Z'
    }
  ];

    console.log('✅ 技师数据加载成功');
  } catch (error) {
    console.error('❌ 加载技师数据失败:', error);
    // 可以在这里添加错误处理逻辑
  } finally {
    // 清除加载状态
    loadingStates.dataLoading = false;
  }
};

// 表单验证函数 - 符合CI_CD_STANDARDS.md规范
const validateForm = () => {
  // 清空之前的错误
  Object.assign(formErrors, {
    name: '',
    phone: ''
  });

  let isValid = true;

  // 验证技师姓名
  if (!formState.name.trim()) {
    formErrors.name = '请输入技师姓名';
    isValid = false;
  } else if (formState.name.trim().length < 2) {
    formErrors.name = '技师姓名至少2个字符';
    isValid = false;
  } else if (formState.name.trim().length > 20) {
    formErrors.name = '技师姓名不能超过20个字符';
    isValid = false;
  }

  // 验证联系电话
  if (!formState.phone.trim()) {
    formErrors.phone = '请输入联系电话';
    isValid = false;
  } else if (!/^1[3-9]\d{9}$/.test(formState.phone)) {
    formErrors.phone = '请输入正确的手机号码';
    isValid = false;
  }

  return isValid;
};

const showAddModal = () => {
  modalTitle.value = '新增技师';
  Object.assign(formState, {
    id: null,
    name: '',
    phone: '',
    bio: '',
    status: 'active'
  });

  // 清空错误状态
  Object.assign(formErrors, {
    name: '',
    phone: ''
  });

  modalVisible.value = true;
};

const hideModal = () => {
  modalVisible.value = false;
  // 清除所有加载状态
  loadingStates.therapistSubmit = false;
};

const handleEdit = (record) => {
  modalTitle.value = '编辑技师';

  // 数据回填 - 符合CI_CD_STANDARDS.md规范
  Object.assign(formState, {
    ...record
  });

  // 清空错误状态
  Object.assign(formErrors, {
    name: '',
    phone: ''
  });

  modalVisible.value = true;
};

// 排班功能已删除

const handleToggleStatus = async (record) => {
  try {
    console.log('🔄 开始切换技师状态...');

    // 设置加载状态
    loadingStates.statusToggle = true;

    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 300));

    if (record.status === 'active') {
      record.status = 'vacation';
      showToast('技师已设置为休假状态', 'success');
    } else {
      record.status = 'active';
      showToast('技师已设置为上班状态', 'success');
    }

    console.log('✅ 技师状态切换成功:', record);
  } catch (error) {
    console.error('❌ 技师状态切换失败:', error);
    showToast('状态切换失败，请重试', 'error');
  } finally {
    // 清除加载状态
    loadingStates.statusToggle = false;
  }
};

const handleSearch = () => {
  console.log('搜索:', searchValue.value);
};

const handleFilterChange = () => {
  console.log('筛选:', { status: statusFilter.value });
};

// 防抖处理 - 避免重复提交
let submitTimeout = null;

const handleSubmit = async () => {
  try {
    // 防抖处理
    if (submitTimeout) {
      clearTimeout(submitTimeout);
    }

    // 表单验证 - 符合CI_CD_STANDARDS.md规范
    if (!validateForm()) {
      showToast('表单验证失败，请检查输入内容', 'error');
      return;
    }

    // 设置加载状态
    loadingStates.therapistSubmit = true;

    console.log('💾 开始提交技师信息...');

    // 防抖延迟
    await new Promise(resolve => setTimeout(resolve, 300));

      // 模拟API调用延迟
      await new Promise(resolve => setTimeout(resolve, 1000));

      if (formState.id) {
        // 编辑模式 - 符合CI_CD_STANDARDS.md数据流转规范
        const index = therapists.value.findIndex(item => item.id === formState.id);
        if (index !== -1) {
          therapists.value[index] = {
            ...formState,
            updated_at: new Date().toISOString()
          };
        }
        showToast('技师信息更新成功', 'success');
      } else {
        // 新增模式 - 符合CI_CD_STANDARDS.md数据流转规范
        therapists.value.unshift({
          ...formState,
          id: Date.now(),
          rating: 4,
          total_services: 0,
          satisfaction: 90,
          work_days: ['周一', '周二', '周三', '周四', '周五'],
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        });
        showToast('技师添加成功', 'success');
      }

    hideModal();
    console.log('✅ 技师信息提交成功');
  } catch (error) {
    console.error('❌ 技师信息提交失败:', error);
    showToast('操作失败，请重试', 'error');
  } finally {
    // 清除加载状态
    loadingStates.therapistSubmit = false;
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

const handlePageSizeChange = () => {
  currentPage.value = 1; // 重置到第一页
};

// 等级相关函数已删除

const getStatusText = (status) => {
  const texts = {
    active: '在职',
    vacation: '休假',
    inactive: '离职'
  };
  return texts[status] || status;
};

// 格式化日期函数
const formatDate = (dateString) => {
  if (!dateString) return '未知';

  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) return '无效日期';

    return date.toLocaleDateString('zh-CN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    }).replace(/\//g, '-');
  } catch (error) {
    console.error('日期格式化错误:', error);
    return '格式错误';
  }
};

// 初始化
onMounted(() => {
  loadTherapists();
});
</script>

<style scoped>
/* 继承毕加索风格样式 */
.picasso-therapists {
  display: flex;
  position: fixed;
  inset: 0 0 0 180px;
  width: calc(100vw - 180px);
  height: 100vh;
  padding: 30px; /* 统一主内容区边距 */
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;

  /* 设置与全局背景相同的梵高风格紫色渐变，调整背景位置以匹配全局背景 */
  background: linear-gradient(180deg,
    #2d1b69 0%,    /* 深紫色（梵高星夜风格） */
    #3730a3 15%,   /* 靛蓝紫 */
    #4338ca 30%,   /* 中紫色 */
    #5b21b6 45%,   /* 深紫色 */
    #6b21a8 60%,   /* 紫色 */
    #7c2d92 75%,   /* 紫红色 */
    #86198f 90%,   /* 深紫红 */
    #701a75 100%   /* 最深紫色 */
  ) !important;
  background-attachment: fixed; /* 固定背景，避免位移 */
  flex-direction: column;
}

@keyframes picassoFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 0% 50%; }
  100% { background-position: 50% 0%; }
}

/* 继承毕加索风格基础样式 */

/* 已删除无用的标题样式 */

/* 毕加索风格操作栏 */
.action-toolbar {
  display: flex;
  position: relative;
  z-index: 10;
  gap: 20px;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.search-cubism {
  flex: 1;
}

.search-fragment {
  width: 100%;
  padding: 12px 20px;
  border: none;
  border: 2px solid rgb(255 182 193 / 40%);
  border-radius: 0 25px;
  font-size: 1rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg,
    rgb(255 182 193 / 80%),
    rgb(255 218 185 / 70%),
    rgb(255 255 255 / 90%)
  );
  box-shadow: 3px 3px 10px rgb(255 182 193 / 30%);
  transform: skew(-3deg);
  transition: all 0.3s ease;
}

.search-fragment:focus {
  box-shadow: 5px 5px 15px rgb(142 68 173 / 40%);
  transform: skew(-3deg) scale(1.02);
  outline: none;
}

.filter-cubism {
  display: flex;
  gap: 10px;
}

.filter-fragment {
  padding: 12px 15px;
  border: none;
  border: 2px solid rgb(173 216 230 / 40%);
  border-radius: 0 20px;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg,
    rgb(173 216 230 / 80%),
    rgb(135 206 235 / 70%),
    rgb(255 255 255 / 90%)
  );
  box-shadow: 3px 3px 10px rgb(173 216 230 / 30%);
  transform: skew(-3deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.filter-fragment:focus {
  box-shadow: 5px 5px 15px rgb(142 68 173 / 40%);
  transform: skew(-3deg) scale(1.02);
  outline: none;
}

.action-cubism {
  display: flex;
  gap: 10px;
}

.action-cube {
  width: 120px;
  height: 45px;
  border-radius: 15px 0;
  transform: perspective(600px) rotateX(15deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-cube:hover {
  transform: perspective(600px) rotateX(15deg) scale(1.05);
}

.refresh-cube {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
}

.add-cube {
  background: linear-gradient(45deg, #4ecdc4, #45b7d1);
}

.cube-face {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 15px 0;
  font-weight: bold;
  color: white;
  box-shadow: 0 5px 15px rgb(0 0 0 / 30%);
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.cube-icon {
  font-size: 1.2rem;
}

.cube-text {
  font-size: 0.9rem;
}

/* 毕加索风格数据表格 */
.data-cubism {
  display: flex;
  position: relative;
  z-index: 5;
  padding: 20px;
  border-radius: 20px;
  overflow: hidden;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 20px 40px rgb(155 89 182 / 30%);
  transform: perspective(1000px) rotateX(3deg);
  flex: 1;
  flex-direction: column;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* 🎨 表头样式 - 修复对齐问题 */
.table-header {
  display: flex;
  position: sticky;
  top: 0;
  z-index: 10;
  padding: 15px 0;
  border-radius: 15px 15px 0 0;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
  margin-bottom: 10px;

  /* 预留滚动条宽度 */
  padding-right: var(--scrollbar-width, 0);
  flex-shrink: 0;
}

/* 🔧 统一列宽系统 - 修复对齐问题 */
.header-cell, .data-cell {
  display: flex;
  align-items: center;
  padding: 0 12px;

  /* 统一flex属性 */
  flex-shrink: 0;
  flex-grow: 0;
  overflow: hidden;
}

.header-cell {
  font-size: 0.9rem;
  font-weight: 900;
  color: white;
  transform: rotate(1deg);
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 2px 2px 4px rgb(0 0 0 / 30%);
}

/* 技师管理页面具体列宽定义 */
.header-cell:nth-child(1), .data-cell:nth-child(1) {
  flex-basis: 30%;
  min-width: 180px;
}

.header-cell:nth-child(2), .data-cell:nth-child(2) {
  flex-basis: 25%;
  min-width: 150px;
}

.header-cell:nth-child(3), .data-cell:nth-child(3) {
  flex-basis: 18%;
  min-width: 120px;
}

.header-cell:nth-child(4), .data-cell:nth-child(4) {
  flex-basis: 12%;
  min-width: 80px;
}

.header-cell:nth-child(5), .data-cell:nth-child(5) {
  flex-basis: 15%;
  min-width: 120px;
}

/* 🔧 内容溢出处理 */
.cell-content {
  width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 多行文本处理 */
.cell-content.multiline {
  display: -webkit-box;
  max-height: 2.8em;
  line-height: 1.4;
  white-space: normal;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 长单词处理 */
.cell-content.breakable {
  word-break: break-all;
  word-wrap: break-word;
}

/* 📱 响应式设计 - 符合CI_CD_STANDARDS.md */

/* 平板适配 */
@media (width <= 768px) {
  .table-container {
    overflow-x: auto;
  }

  .table-header, .data-row {
    min-width: 600px;
  }

  .header-cell, .data-cell {
    padding: 0 8px;
  }
}

/* 手机适配 */
@media (width <= 480px) {
  .table-header {
    display: none;
  }

  .data-row {
    min-width: auto;
    padding: 12px;
    border-radius: 8px;
    background: rgb(255 255 255 / 10%);
    flex-direction: column;
    margin-bottom: 12px;
  }

  .data-cell {
    min-width: auto;
    padding: 4px 0;
    flex-direction: row;
    justify-content: space-between;
    border-bottom: 1px solid rgb(255 255 255 / 10%);
    flex-basis: auto;
  }

  .data-cell:last-child {
    border-bottom: none;
  }

  .data-cell::before {
    content: attr(data-label);
    font-weight: bold;
    color: var(--van-gogh-text-primary);
  }
}

.table-body {
  max-height: calc(100vh - 320px); /* 减去头部、搜索栏、分页等空间 */

  /* 确保5行数据能够完整显示，避免内容被遮挡 */
  min-height: 350px; /* 5行 × 约70px行高 = 350px */
  flex: 1;
  overflow: hidden auto;

  /* 毕加索风格滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #9b59b6 rgb(155 89 182 / 20%);
}

.table-body::-webkit-scrollbar {
  width: 8px;
}

.table-body::-webkit-scrollbar-track {
  border-radius: 8px;
  background: linear-gradient(45deg, rgb(142 68 173 / 10%), rgb(155 89 182 / 10%));
}

.table-body::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: linear-gradient(45deg, #8e44ad, #9b59b6);
  transition: all 0.3s ease;
}

.table-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #639, #8e44ad);
}

/* 🔄 表格加载状态容器 - 参考服务管理模板 */
.table-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 300px;
  padding: 40px 0;
}

.table-loading-content {
  display: flex;
  padding: 30px;
  border: 1px solid rgb(139 92 246 / 20%);
  border-radius: 12px;
  background: linear-gradient(135deg, rgb(139 92 246 / 5%) 0%, rgb(244 114 182 / 5%) 100%);
  box-shadow: 0 8px 30px rgb(139 92 246 / 10%);
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  animation: fadeIn 0.5s ease-out;
}

.loading-spinner-large {
  font-size: 32px;
  animation: spin 1.5s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 🎨 Toast通知组件样式 - 符合CI_CD_STANDARDS.md用户反馈规范 */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  max-width: 500px;
  min-width: 300px;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow:
    0 8px 32px rgb(0 0 0 / 20%),
    0 4px 16px rgb(0 0 0 / 10%);
  backdrop-filter: blur(10px);
  animation: slideInRight 0.3s ease-out;
}

.toast-success {
  border: 2px solid rgb(34 197 94 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(34 197 94 / 90%), rgb(22 163 74 / 90%));
}

.toast-error {
  border: 2px solid rgb(239 68 68 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(239 68 68 / 90%), rgb(220 38 38 / 90%));
}

.toast-warning {
  border: 2px solid rgb(245 158 11 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(245 158 11 / 90%), rgb(217 119 6 / 90%));
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toast-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.toast-message {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.loading-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--van-gogh-text-secondary);
}

/* 📋 表格空状态容器 */
.table-empty-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 300px;
  padding: 40px 0;
}

.table-empty-content {
  display: flex;
  padding: 30px;
  border: 1px dashed rgb(139 92 246 / 20%);
  border-radius: 12px;
  background: linear-gradient(135deg, rgb(139 92 246 / 3%) 0%, rgb(244 114 182 / 3%) 100%);
  box-shadow: 0 4px 20px rgb(139 92 246 / 5%);
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
}

.empty-icon {
  font-size: 48px;
  opacity: 0.7;
}

.empty-text {
  font-size: 16px;
  font-weight: 500;
  color: var(--van-gogh-text-secondary);
}

/* 🔄 加载状态样式 - 参考服务管理模板 */
.loading {
  position: relative;
  pointer-events: none;
  opacity: 0.8;
}

.loading-spinner {
  display: inline-block;
  animation: spin 1.2s linear infinite;
  font-size: 1em;
}

/* 加载中的按钮样式 */
.action-btn.loading {
  background: linear-gradient(135deg, rgb(139 92 246 / 10%) 0%, rgb(244 114 182 / 10%) 100%);
  border-color: rgb(139 92 246 / 30%);
}

/* 📱 响应式设计 - 符合CI_CD_STANDARDS.md */
@media (width <= 768px) {
  .modal-overlay {
    left: 0;
    width: 100%;
  }

  .form-modal {
    width: calc(100vw - 20px);
    max-width: calc(100vw - 20px);
    margin: 10px;
  }

  .table-container {
    overflow-x: auto;
  }
}

@media (width <= 480px) {
  .modal-overlay {
    padding: 10px;
  }

  .form-modal {
    width: calc(100vw - 10px);
    max-width: calc(100vw - 10px);
    margin: 5px;
    border-radius: 12px;
  }

  .action-fragments {
    flex-direction: column;
    gap: 8px;
  }

  .action-btn {
    width: 100%;
    text-align: center;
  }
}

.data-row {
  display: flex;
  height: 50px !important; /* 🎯 固定高度50px，与左侧菜单项高度完全一致 */
  min-height: 50px !important; /* 🎯 调整为50px，与菜单项高度完全一致 */
  border-radius: 10px;
  background: linear-gradient(90deg,
    rgb(142 68 173 / 10%),
    rgb(155 89 182 / 10%),
    rgb(200 162 200 / 10%)
  );
  transition: all 0.3s ease;
  margin-bottom: 8px; /* 🎯 保持8px间距，与菜单项间距一致 */
}

.data-row:hover {
  background: linear-gradient(90deg,
    rgb(142 68 173 / 20%),
    rgb(155 89 182 / 20%),
    rgb(200 162 200 / 20%)
  );
  box-shadow: 0 4px 12px rgb(155 89 182 / 30%);
  transform: scale(1.02);
}

.data-cell {
  display: flex;
  max-height: 50px; /* 🎯 强制限制最大高度为50px */
  padding: 0 10px; /* 🎯 调整内边距，只保留左右内边距以适应50px高度 */
  align-items: center;
  justify-content: center;
}

.cell-content {
  display: flex;
  width: 100%;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  color: #2c3e50;
  align-items: center;
  justify-content: center;
}

/* 技师信息样式简化 - 去掉头像和复杂布局 */

.therapist-name {
  font-size: 1rem;
  font-weight: bold;
  color: #2c3e50;
  transform: skew(-2deg);
  margin-bottom: 3px;
}

.therapist-phone {
  font-size: 0.8rem;
  color: #7f8c8d;
  transform: skew(1deg);
}

/* 字段优化完成 - 符合CI_CD_STANDARDS.md */

/* 等级相关样式已删除 */

/* 毕加索风格绩效评价 */
.performance-fragment {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.rating-stars {
  display: flex;
  gap: 2px;
  justify-content: center;
}

.star {
  font-size: 0.9rem;
  opacity: 0.3;
  transition: all 0.3s ease;
}

.star.active {
  opacity: 1;
  animation: starTwinkle 2s ease-in-out infinite;
}

@keyframes starTwinkle {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.performance-stats {
  display: flex;
  gap: 8px;
  justify-content: center;
}

.stat-item {
  display: flex;
  padding: 4px 8px;
  border-radius: 6px;
  background: linear-gradient(135deg, rgb(142 68 173 / 10%), rgb(255 255 255 / 80%));
  transform: skew(-1deg);
  flex-direction: column;
  align-items: center;
}

.stat-value {
  font-size: 0.9rem;
  font-weight: bold;
  color: #8e44ad;
}

.stat-label {
  font-size: 0.7rem;
  color: #7f8c8d;
}

/* 工作时间相关样式已删除 */

/* 创建时间样式简化 */
.created-time {
  font-size: 0.9rem;
  font-weight: 500;
  text-align: center;
  color: #3498db;
}

/* 毕加索风格状态 */
.status-fragment {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 12px 0;
  transform: skew(-1deg);
}

.status-active {
  border: 2px solid rgb(39 174 96 / 30%);
  background: linear-gradient(135deg, rgb(39 174 96 / 20%), rgb(46 204 113 / 10%));
}

.status-vacation {
  border: 2px solid rgb(243 156 18 / 30%);
  background: linear-gradient(135deg, rgb(243 156 18 / 20%), rgb(255 193 7 / 10%));
}

.status-inactive {
  border: 2px solid rgb(149 165 166 / 30%);
  background: linear-gradient(135deg, rgb(149 165 166 / 20%), rgb(189 195 199 / 10%));
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-active .status-indicator {
  background: #27ae60;
}

.status-vacation .status-indicator {
  background: #f39c12;
}

.status-inactive .status-indicator {
  background: #95a5a6;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

.status-text {
  font-size: 0.8rem;
  font-weight: bold;
}

.status-active .status-text {
  color: #27ae60;
}

.status-vacation .status-text {
  color: #f39c12;
}

.status-inactive .status-text {
  color: #95a5a6;
}

/* 毕加索风格操作按钮 */
.action-fragments {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: center;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 8px 0;
  font-size: 0.8rem;
  font-weight: bold;
  color: white;
  transform: perspective(200px) rotateX(8deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-btn:hover {
  transform: perspective(200px) rotateX(8deg) scale(1.05);
}

.action-btn.edit {
  background: linear-gradient(45deg, #8e44ad, #9b59b6);
}

/* 排班按钮样式已删除 */

.action-btn.toggle {
  background: linear-gradient(45deg, #f39c12, #e67e22);
}

/* 🎨 模态框样式 - 参考服务管理模板 */
.modal-overlay {
  display: flex;
  position: fixed;
  inset: 0;
  z-index: 1000;
  background: rgb(0 0 0 / 60%);
  justify-content: center;
  align-items: center;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.3s ease-out;
}

.form-modal {
  position: relative;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  border: 2px solid rgb(139 92 246 / 20%);
  border-radius: 20px;
  overflow: hidden;
  background: linear-gradient(135deg, rgb(255 255 255 / 95%) 0%, rgb(248 250 252 / 95%) 100%);
  box-shadow:
    0 20px 60px rgb(139 92 246 / 30%),
    0 8px 30px rgb(0 0 0 / 20%),
    inset 0 1px 0 rgb(255 255 255 / 80%);
  backdrop-filter: blur(10px);
}

.form-header {
  display: flex;
  padding: 20px 30px;
  border-radius: 18px 18px 0 0;
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-primary) 0%, var(--van-gogh-secondary) 100%);
  box-shadow: 0 4px 20px rgb(139 92 246 / 30%);
  justify-content: space-between;
  align-items: center;
}

.form-title {
  margin: 0;
  font-size: 1.4rem;
  font-weight: 700;
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.close-btn {
  display: flex;
  width: 36px;
  height: 36px;
  border: 2px solid rgb(255 255 255 / 30%);
  border-radius: 50%;
  font-size: 20px;
  font-weight: bold;
  color: white;
  background: rgb(255 255 255 / 20%);
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  backdrop-filter: blur(10px);
}

.close-btn:hover {
  background: rgb(255 255 255 / 30%);
  border-color: rgb(255 255 255 / 50%);
  transform: scale(1.1);
}

.form-content {
  max-height: 60vh;
  padding: 30px;
  overflow-y: auto;
}

.form-cubism {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-row {
  display: flex;
  gap: 20px;
  align-items: flex-start;
}

.form-row.full-width {
  flex-direction: column;
}

.form-group {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-label {
  font-size: 0.95rem;
  font-weight: 600;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 10%);
}

.form-input,
.form-textarea {
  height: 50px;                            /* 🎯 与菜单高度一致：50px */
  padding: 0 16px;                         /* 🎯 调整内边距适应新高度 */
  border: 2px solid rgb(139 92 246 / 20%);
  border-radius: 12px;
  font-size: 1rem;
  line-height: 50px;                       /* 🎯 与菜单行高一致 */
  background: linear-gradient(135deg, rgb(255 255 255 / 90%) 0%, rgb(248 250 252 / 90%) 100%);
  box-shadow: 0 2px 8px rgb(139 92 246 / 10%);
  transition: all 0.3s ease;
}

.form-textarea {
  height: auto !important;                 /* 文本域保持自适应高度 */
  min-height: 50px !important;             /* 最小高度与菜单一致 */
  padding: 12px 16px !important;           /* 文本域保持原有内边距 */
  line-height: 1.5 !important;             /* 文本域使用正常行高 */
}

.form-input:focus,
.form-textarea:focus {
  background: white;
  box-shadow:
    0 0 0 3px rgb(139 92 246 / 20%),
    0 4px 12px rgb(139 92 246 / 20%);
  outline: none;
  border-color: var(--van-gogh-primary);
}

.form-input.error,
.form-textarea.error {
  border-color: #ef4444;
  box-shadow:
    0 0 0 3px rgb(239 68 68 / 20%),
    0 4px 12px rgb(239 68 68 / 20%);
}

.required {
  font-weight: bold;
  color: #ef4444;
}

.error-message {
  font-size: 0.85rem;
  font-weight: 500;
  color: #ef4444;
  margin-top: 4px;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
  font-family: inherit;
}

.form-actions {
  display: flex;
  padding: 20px 30px;
  border-radius: 0 0 18px 18px;
  background: linear-gradient(135deg, rgb(248 250 252 / 80%) 0%, rgb(241 245 249 / 80%) 100%);
  border-top: 1px solid rgb(139 92 246 / 10%);
  justify-content: flex-end;
  gap: 15px;
}

.action-btn {
  display: flex;
  min-width: 100px;
  padding: 12px 24px;
  border: 2px solid transparent;
  border-radius: 10px;
  font-size: 0.95rem;
  font-weight: 600;
  transition: all 0.3s ease;
  cursor: pointer;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.cancel-btn {
  color: white;
  background: linear-gradient(135deg, #6b7280 0%, #9ca3af 100%);
  border-color: #6b7280;
}

.cancel-btn:hover {
  background: linear-gradient(135deg, #4b5563 0%, #6b7280 100%);
  box-shadow: 0 4px 12px rgb(107 114 128 / 30%);
  transform: translateY(-2px);
}

.confirm-btn {
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-primary) 0%, var(--van-gogh-secondary) 100%);
  border-color: var(--van-gogh-primary);
}

.confirm-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--van-gogh-primary-dark) 0%, var(--van-gogh-primary) 100%);
  box-shadow: 0 6px 20px rgb(139 92 246 / 40%);
  transform: translateY(-2px);
}

.confirm-btn:disabled {
  background: linear-gradient(135deg, #9ca3af 0%, #d1d5db 100%);
  border-color: #9ca3af;
  cursor: not-allowed;
  transform: none;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }

  to {
    opacity: 1;
    transform: scale(1);
  }
}

/* 🎨 梵高风格分页组件 - 符合CI_CD_STANDARDS.md */
.pagination-container {
  display: flex;
  padding: 12px 18px;
  border: 2px solid rgb(192 132 252 / 40%);
  border-radius: 12px;
  background: linear-gradient(135deg, rgb(139 92 246 / 10%), rgb(168 85 247 / 10%));
  box-shadow:
    0 4px 8px rgb(139 92 246 / 20%),
    0 2px 4px rgb(192 132 252 / 10%);
  margin-top: 15px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  font-size: 14px;
  font-weight: 600;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.highlight-number {
  font-size: 16px;
  font-weight: 700;
  color: var(--van-gogh-accent);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 40%);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-label {
  font-size: 13px;
  font-weight: 600;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.page-size-select {
  padding: 6px 12px;
  border: 2px solid var(--van-gogh-accent);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-primary-dark), var(--van-gogh-secondary));
  box-shadow: 0 2px 4px rgb(139 92 246 / 30%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.page-size-select:hover {
  border-color: var(--van-gogh-primary);
  box-shadow: 0 4px 8px rgb(139 92 246 / 40%);
}

.page-size-select option {
  padding: 8px;
  color: white;
  background: var(--van-gogh-primary-dark);
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  padding: 8px 12px;
  border: 2px solid var(--van-gogh-primary);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-secondary), var(--van-gogh-secondary-dark));
  box-shadow: 0 2px 4px rgb(139 92 246 / 30%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.page-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--van-gogh-primary), var(--van-gogh-primary-dark));
  box-shadow: 0 4px 8px rgb(139 92 246 / 40%);
  transform: translateY(-1px);
}

.page-btn:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  border-color: #d1d5db;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.page-btn.active {
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-accent), var(--van-gogh-accent-dark));
  box-shadow:
    0 4px 8px rgb(244 114 182 / 40%),
    inset 1px 1px 2px rgb(255 255 255 / 30%);
  border-color: var(--van-gogh-accent-light);
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  min-width: 36px;
  text-align: center;
}

/* 响应式设计 */
@media (width <= 768px) {
  .picasso-therapists {
    left: 0;
    width: 100vw;
    padding: 15px;
  }

  .action-toolbar {
    flex-direction: column;
    gap: 15px;
  }

  .filter-cubism {
    flex-direction: column;
  }

  .action-cubism {
    justify-content: center;
  }

  .data-row {
    flex-direction: column;
    min-height: auto;
  }

  .data-cell {
    border-bottom: 1px solid rgb(142 68 173 / 20%);
    min-height: 50px;                      /* 🎯 与菜单高度一致：50px */
  }

  .title-layer {
    font-size: 2rem;
  }

  .subtitle-fragment {
    font-size: 1rem;
  }

  .action-fragments {
    justify-content: center;
  }

  /* 移动端梵高分页样式 */
  .pagination-container {
    flex-direction: column;
    gap: 10px;
    padding: 12px 15px;
  }

  .pagination-controls {
    gap: 15px;
  }

  .page-navigation {
    gap: 6px;
  }

  .page-btn {
    padding: 6px 10px;
    font-size: 12px;
  }

  .page-number {
    min-width: 32px;
  }

  /* 移动端模态框样式 */
  .form-modal {
    width: 95%;
    max-height: 90vh;
    margin: 20px;
  }

  .form-header {
    padding: 15px 20px;
  }

  .form-title {
    font-size: 1.2rem;
  }

  .form-content {
    max-height: 70vh;
    padding: 20px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .form-actions {
    padding: 15px 20px;
    flex-direction: column;
    gap: 10px;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }

  /* 移动端Toast样式 */
  .toast-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
  }
}
</style>
