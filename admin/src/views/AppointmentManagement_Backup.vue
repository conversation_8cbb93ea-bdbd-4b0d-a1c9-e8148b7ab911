<template>
  <div class="picasso-appointments">
    <!-- 毕加索风格操作栏 -->
    <div class="action-toolbar">
      <div class="search-cubism">
        <input 
          type="text" 
          placeholder="搜索客户姓名或电话..."
          v-model="searchValue"
          @input="handleSearch"
          class="search-fragment"
        />
      </div>
      
      <div class="filter-cubism">
        <select 
          v-model="statusFilter"
          @change="handleFilterChange"
          class="filter-fragment"
        >
          <option value="">所有状态</option>
          <option value="pending">待确认</option>
          <option value="confirmed">已确认</option>
          <option value="completed">已完成</option>
          <option value="cancelled">已取消</option>
        </select>

        <select 
          v-model="therapistFilter"
          @change="handleFilterChange"
          class="filter-fragment"
        >
          <option value="">所有技师</option>
          <option value="王医师">王医师</option>
          <option value="李医师">李医师</option>
          <option value="张医师">张医师</option>
        </select>
      </div>

      <div class="action-cubism">
        <div class="action-cube refresh-cube" @click="loadAppointments">
          <div class="cube-face">
            <span class="cube-icon">🔄</span>
            <span class="cube-text">刷新</span>
          </div>
        </div>
        <div class="action-cube add-cube" @click="showAddModal">
          <div class="cube-face">
            <span class="cube-icon">➕</span>
            <span class="cube-text">新增预约</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格数据表格 -->
    <div class="data-cubism">
      <div class="table-container">
        <!-- 表头 -->
        <div class="table-header">
          <div class="header-cell" style="flex: 0.5;">ID</div>
          <div class="header-cell" style="flex: 2;">客户信息</div>
          <div class="header-cell" style="flex: 1.5;">服务项目</div>
          <div class="header-cell" style="flex: 1;">技师</div>
          <div class="header-cell" style="flex: 1.5;">预约时间</div>
          <div class="header-cell" style="flex: 1;">状态</div>
          <div class="header-cell" style="flex: 2;">操作</div>
        </div>

        <!-- 数据行 -->
        <div class="table-body">
          <!-- 数据加载状态 -->
          <div v-if="loadingStates.dataLoading" class="table-loading-container">
            <div class="table-loading-content">
              <div class="loading-spinner-large">⏳</div>
              <div class="loading-text">正在加载预约数据...</div>
            </div>
          </div>

          <!-- 无数据状态 -->
          <div v-else-if="paginatedData.length === 0" class="table-empty-container">
            <div class="table-empty-content">
              <div class="empty-icon">📅</div>
              <div class="empty-text">暂无预约数据</div>
            </div>
          </div>

          <!-- 数据内容 -->
          <div v-else
            v-for="record in paginatedData"
            :key="record.id"
            class="data-row"
          >
            <div class="data-cell" style="flex: 0.5;">
              <div class="cell-content">{{ record.id }}</div>
            </div>
            
            <div class="data-cell" style="flex: 2;">
              <div class="customer-fragment">
                <div class="customer-avatar">{{ record.customer_name?.charAt(0) }}</div>
                <div class="customer-info">
                  <div class="customer-name">{{ record.customer_name }}</div>
                  <div class="customer-phone">{{ record.customer_phone }}</div>
                </div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1.5;">
              <div class="service-fragment">
                <div class="service-name">{{ record.service_name }}</div>
                <div class="service-price">¥{{ record.service_price }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1;">
              <div class="therapist-fragment">
                <div class="therapist-icon">👨‍⚕️</div>
                <div class="therapist-name">{{ record.therapist_name }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1.5;">
              <div class="time-fragment">
                <div class="date-part">{{ record.appointment_date }}</div>
                <div class="time-part">{{ record.appointment_time }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 1;">
              <div class="status-fragment" :class="'status-' + record.status">
                <div class="status-indicator"></div>
                <div class="status-text">{{ getStatusText(record.status) }}</div>
              </div>
            </div>

            <div class="data-cell" style="flex: 2;">
              <div class="action-fragments">
                <div class="action-btn edit" @click="handleEdit(record)">编辑</div>
                <div class="action-btn view" @click="handleView(record)">查看</div>
                <div class="action-btn cancel" @click="handleCancel(record)" v-if="record.status !== 'cancelled'">取消</div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 梵高风格分页组件 - 符合CI_CD_STANDARDS.md -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span class="total-info">
            共 <span class="highlight-number">{{ totalRecords }}</span> 条记录，
            第 <span class="highlight-number">{{ currentPage }}</span> /
            <span class="highlight-number">{{ totalPages }}</span> 页
          </span>
        </div>

        <div class="pagination-controls">
          <div class="page-size-selector">
            <label class="page-size-label">每页显示：</label>
            <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
              <option value="5">5条</option>
              <option value="10">10条</option>
              <option value="20">20条</option>
              <option value="50">50条</option>
            </select>
          </div>

          <div class="page-navigation" v-if="totalRecords > 0">
            <button
              class="page-btn prev-btn"
              @click="prevPage"
              :disabled="currentPage === 1"
             aria-label="操作按钮">
              ‹ 上一页
            </button>

            <div class="page-numbers">
              <button
                v-for="page in visiblePages"
                :key="page"
                class="page-btn page-number"
                :class="{ active: page === currentPage }"
                @click="goToPage(page)"
               aria-label="操作按钮">
                {{ page }}
              </button>
            </div>

            <button
              class="page-btn next-btn"
              @click="nextPage"
              :disabled="currentPage === totalPages"
             aria-label="操作按钮">
              下一页 ›
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 毕加索风格模态框 -->
    <div v-if="modalVisible" class="modal-overlay" @click="hideModal">
      <div class="modal-cubism" @click.stop>
        <div class="modal-header">
          <div class="header-title">{{ modalTitle }}</div>
          <div class="close-btn" @click="hideModal">×</div>
        </div>
        
        <div class="modal-body">
          <div class="form-cubism">
            <div class="form-row">
              <div class="form-group">
                <label class="form-label">客户姓名 <span class="required">*</span></label>
                <input
                  type="text"
                  v-model="formState.customer_name"
                  class="form-input"
                  :class="{ 'error': formErrors.customer_name }"
                  placeholder="请输入客户姓名"
                  required
                  aria-label="输入字段"
                />
                <div v-if="formErrors.customer_name" class="error-message">{{ formErrors.customer_name }}</div>
              </div>

              <div class="form-group">
                <label class="form-label">联系电话 <span class="required">*</span></label>
                <input
                  type="tel"
                  v-model="formState.customer_phone"
                  class="form-input"
                  :class="{ 'error': formErrors.customer_phone }"
                  placeholder="请输入联系电话"
                  required
                aria-label="输入字段">
                <div v-if="formErrors.customer_phone" class="error-message">{{ formErrors.customer_phone }}</div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">服务项目 <span class="required">*</span></label>
                <select
                  v-model="formState.service_name"
                  class="form-select"
                  :class="{ 'error': formErrors.service_name }"
                  required
                >
                  <option value="">请选择服务项目</option>
                  <option value="颈椎推拿">颈椎推拿</option>
                  <option value="全身推拿">全身推拿</option>
                  <option value="足疗保健">足疗保健</option>
                  <option value="艾灸理疗">艾灸理疗</option>
                </select>
                <div v-if="formErrors.service_name" class="error-message">{{ formErrors.service_name }}</div>
              </div>

              <div class="form-group">
                <label class="form-label">指定技师 <span class="required">*</span></label>
                <select
                  v-model="formState.therapist_name"
                  class="form-select"
                  :class="{ 'error': formErrors.therapist_name }"
                  required
                >
                  <option value="">请选择技师</option>
                  <option value="王医师">王医师</option>
                  <option value="李医师">李医师</option>
                  <option value="张医师">张医师</option>
                </select>
                <div v-if="formErrors.therapist_name" class="error-message">{{ formErrors.therapist_name }}</div>
              </div>
            </div>

            <div class="form-row">
              <div class="form-group">
                <label class="form-label">预约日期 <span class="required">*</span></label>
                <input
                  type="date"
                  v-model="formState.appointment_date"
                  class="form-input"
                  :class="{ 'error': formErrors.appointment_date }"
                  required
                aria-label="输入字段">
                <div v-if="formErrors.appointment_date" class="error-message">{{ formErrors.appointment_date }}</div>
              </div>

              <div class="form-group">
                <label class="form-label">预约时间 <span class="required">*</span></label>
                <input
                  type="time"
                  v-model="formState.appointment_time"
                  class="form-input"
                  :class="{ 'error': formErrors.appointment_time }"
                  required
                aria-label="输入字段">
                <div v-if="formErrors.appointment_time" class="error-message">{{ formErrors.appointment_time }}</div>
              </div>
            </div>

            <div class="form-row full-width">
              <div class="form-group">
                <label class="form-label">备注信息</label>
                <textarea 
                  v-model="formState.notes" 
                  class="form-textarea"
                  placeholder="预约备注信息..."
                  rows="3"
                ></textarea>
              </div>
            </div>
          </div>
        </div>
        
        <div class="modal-footer">
          <div class="footer-actions">
            <div class="action-btn cancel" @click="hideModal">
              <div class="btn-face">取消</div>
            </div>
            <div class="action-btn confirm" @click="handleSubmit">
              <div class="btn-face">{{ confirmLoading ? '提交中...' : '确定' }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- Toast通知组件 - 符合CI_CD_STANDARDS.md用户反馈规范 -->
  <div v-if="toastState.visible" class="toast-notification" :class="'toast-' + toastState.type">
    <div class="toast-content">
      <div class="toast-icon">
        <span v-if="toastState.type === 'success'">✅</span>
        <span v-else-if="toastState.type === 'error'">❌</span>
        <span v-else-if="toastState.type === 'warning'">⚠️</span>
      </div>
      <div class="toast-message">{{ toastState.message }}</div>
    </div>
  </div>
</template>

<script setup>
// 性能监控 - 性能优化
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};

import { ref, reactive, computed, onMounted , nextTick, shallowRef, watchEffect } from 'vue';;

// 响应式数据
const appointments = ref([]);
const modalVisible = ref(false);
const modalTitle = ref('新增预约');
const searchValue = ref('');
const statusFilter = ref('');
const therapistFilter = ref('');

// 交互加载状态 - 参考服务管理模板
const loadingStates = reactive({
  dataLoading: false,         // 数据加载状态
  appointmentEdit: false,     // 预约编辑加载状态
  appointmentSubmit: false,   // 预约提交加载状态
  appointmentDelete: false,   // 预约删除加载状态
  statusToggle: false         // 状态切换加载状态
});
const currentPage = ref(1);
const pageSize = ref(5); // 默认5条每页，符合开发规范

// Toast通知状态 - 符合CI_CD_STANDARDS.md用户反馈规范
const toastState = reactive({
  visible: false,
  message: '',
  type: 'success' // success, error, warning
});

// Toast通知函数 - 替代console日志
const showToast = (message, type = 'success') => {
  toastState.message = message;
  toastState.type = type;
  toastState.visible = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    toastState.visible = false;
  }, 3000);
};

// 表单状态
const formState = reactive({
  id: null,
  customer_name: '',
  customer_phone: '',
  service_name: '',
  therapist_name: '',
  appointment_date: '',
  appointment_time: '',
  notes: '',
  status: 'pending'
});

// 表单验证错误状态 - 符合CI_CD_STANDARDS.md表单验证规范
const formErrors = reactive({
  customer_name: '',
  customer_phone: '',
  service_name: '',
  therapist_name: '',
  appointment_date: '',
  appointment_time: ''
});

// 表单验证函数 - 符合CI_CD_STANDARDS.md表单验证规范
const validateForm = () => {
  // 清空之前的错误
  Object.assign(formErrors, {
    customer_name: '',
    customer_phone: '',
    service_name: '',
    therapist_name: '',
    appointment_date: '',
    appointment_time: ''
  });

  let isValid = true;

  // 验证客户姓名
  if (!formState.customer_name?.trim()) {
    formErrors.customer_name = '请输入客户姓名';
    isValid = false;
  }

  // 验证客户电话
  if (!formState.customer_phone?.trim()) {
    formErrors.customer_phone = '请输入客户电话';
    isValid = false;
  } else if (!/^1[3-9]\d{9}$/.test(formState.customer_phone)) {
    formErrors.customer_phone = '请输入正确的手机号码';
    isValid = false;
  }

  // 验证服务项目
  if (!formState.service_name?.trim()) {
    formErrors.service_name = '请选择服务项目';
    isValid = false;
  }

  // 验证技师
  if (!formState.therapist_name?.trim()) {
    formErrors.therapist_name = '请选择技师';
    isValid = false;
  }

  // 验证预约日期
  if (!formState.appointment_date?.trim()) {
    formErrors.appointment_date = '请选择预约日期';
    isValid = false;
  }

  // 验证预约时间
  if (!formState.appointment_time?.trim()) {
    formErrors.appointment_time = '请选择预约时间';
    isValid = false;
  }

  return isValid;
};

// 计算属性
const totalRecords = computed(() => appointments.value.length);
const totalPages = computed(() => Math.ceil(appointments.value.length / pageSize.value));
const paginatedData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value;
  const end = start + pageSize.value;
  return appointments.value.slice(start, end);
});

const visiblePages = computed(() => {
  const pages = [];
  const total = totalPages.value;
  const current = currentPage.value;
  
  for (let i = Math.max(1, current - 2); i <= Math.min(total, current + 2); i++) {
    pages.push(i);
  }
  return pages;
});

// 方法
const loadAppointments = async () => {
  // 模拟API调用
  await new Promise(resolve => setTimeout(resolve, 500));

  appointments.value = [
    {
      id: 1,
      customer_name: '张女士',
      customer_phone: '13800138001',
      service_name: '颈椎推拿',
      service_price: 120,
      therapist_name: '王医师',
      appointment_date: '2024-01-15',
      appointment_time: '10:30',
      status: 'confirmed',
      notes: '颈椎不适，需要温和治疗'
    },
    {
      id: 2,
      customer_name: '李先生',
      customer_phone: '13900139002',
      service_name: '全身推拿',
      service_price: 180,
      therapist_name: '李医师',
      appointment_date: '2024-01-15',
      appointment_time: '14:00',
      status: 'pending',
      notes: '首次体验，需要详细咨询'
    },
    {
      id: 3,
      customer_name: '王女士',
      customer_phone: '13700137003',
      service_name: '足疗保健',
      service_price: 100,
      therapist_name: '张医师',
      appointment_date: '2024-01-16',
      appointment_time: '09:00',
      status: 'completed',
      notes: '定期保健客户'
    }
  ];
};

const showAddModal = () => {
  modalTitle.value = '新增预约';
  Object.assign(formState, {
    id: null,
    customer_name: '',
    customer_phone: '',
    service_name: '',
    therapist_name: '',
    appointment_date: '',
    appointment_time: '',
    notes: '',
    status: 'pending'
  });
  modalVisible.value = true;
};

const hideModal = () => {
  modalVisible.value = false;
  confirmLoading.value = false;
};

const handleEdit = (record) => {
  modalTitle.value = '编辑预约';
  Object.assign(formState, record);
  modalVisible.value = true;
};

const handleView = (record) => {
  console.log('查看预约:', record);
};

const handleCancel = (record) => {
  record.status = 'cancelled';
  console.log('取消预约:', record);
};

const handleSearch = () => {
  console.log('搜索:', searchValue.value);
};

const handleFilterChange = () => {
  console.log('筛选:', { status: statusFilter.value, therapist: therapistFilter.value });
};

const handleSubmit = async () => {
  try {
    // 表单验证 - 符合CI_CD_STANDARDS.md表单验证规范
    if (!validateForm()) {
      showToast('请检查输入内容', 'error');
      return;
    }

    confirmLoading.value = true;
    await new Promise(resolve => setTimeout(resolve, 1000));

    if (formState.id) {
      const index = appointments.value.findIndex(item => item.id === formState.id);
      if (index !== -1) {
        appointments.value[index] = { ...formState };
        showToast('预约信息更新成功', 'success');
      }
    } else {
      appointments.value.unshift({
        ...formState,
        id: Date.now(),
        service_price: 120
      });
      showToast('预约添加成功', 'success');
    }

    hideModal();
  } catch (error) {
    console.error('预约操作失败:', error);
    showToast('操作失败，请重试', 'error');
  } finally {
    confirmLoading.value = false;
  }
};

const prevPage = () => {
  if (currentPage.value > 1) {
    currentPage.value--;
  }
};

const nextPage = () => {
  if (currentPage.value < totalPages.value) {
    currentPage.value++;
  }
};

const goToPage = (page) => {
  if (page >= 1 && page <= totalPages.value) {
    currentPage.value = page;
  }
};

const handlePageSizeChange = () => {
  currentPage.value = 1; // 重置到第一页
};

const getStatusText = (status) => {
  const texts = {
    pending: '待确认',
    confirmed: '已确认',
    completed: '已完成',
    cancelled: '已取消'
  };
  return texts[status] || status;
};

// 初始化
onMounted(() => {
  loadAppointments();
});
</script>

<style scoped>
/* 毕加索风格预约管理 - 占满主内容区 */
.picasso-appointments {
  display: flex;
  position: fixed;
  inset: 0 0 0 180px; /* 侧边栏宽度 */
  width: calc(100vw - 180px);
  height: 100vh;
  padding: 30px; /* 统一主内容区边距 */
  box-sizing: border-box;
  overflow: hidden;
  font-family: 'Arial Black', sans-serif;

  /* 设置与全局背景相同的梵高风格紫色渐变，调整背景位置以匹配全局背景 */
  background: linear-gradient(180deg,
    #2d1b69 0%,    /* 深紫色（梵高星夜风格） */
    #3730a3 15%,   /* 靛蓝紫 */
    #4338ca 30%,   /* 中紫色 */
    #5b21b6 45%,   /* 深紫色 */
    #6b21a8 60%,   /* 紫色 */
    #7c2d92 75%,   /* 紫红色 */
    #86198f 90%,   /* 深紫红 */
    #701a75 100%   /* 最深紫色 */
  ) !important;
  background-attachment: fixed; /* 固定背景，避免位移 */
  flex-direction: column;
}

@keyframes picassoFlow {
  0% { background-position: 0% 50%; }
  25% { background-position: 100% 50%; }
  50% { background-position: 50% 100%; }
  75% { background-position: 0% 50%; }
  100% { background-position: 50% 0%; }
}

/* 已删除无用的标题样式 */

/* 毕加索风格操作栏 */
.action-toolbar {
  display: flex;
  position: relative;
  z-index: 10;
  gap: 20px;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.search-cubism {
  flex: 1;
}

.search-fragment {
  width: 100%;
  padding: 12px 20px;
  border: none;
  border: 2px solid rgb(255 182 193 / 40%);
  border-radius: 0 25px;
  font-size: 1rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg,
    rgb(255 182 193 / 80%),
    rgb(255 218 185 / 70%),
    rgb(255 255 255 / 90%)
  );
  box-shadow: 3px 3px 10px rgb(255 182 193 / 30%);
  transform: skew(-3deg);
  transition: all 0.3s ease;
}

.search-fragment:focus {
  box-shadow: 5px 5px 15px rgb(65 105 225 / 40%);
  transform: skew(-3deg) scale(1.02);
  outline: none;
}

.filter-cubism {
  display: flex;
  gap: 10px;
}

.filter-fragment {
  padding: 12px 15px;
  border: none;
  border: 2px solid rgb(173 216 230 / 40%);
  border-radius: 0 20px;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg,
    rgb(173 216 230 / 80%),
    rgb(135 206 235 / 70%),
    rgb(255 255 255 / 90%)
  );
  box-shadow: 3px 3px 10px rgb(173 216 230 / 30%);
  transform: skew(-3deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.filter-fragment:focus {
  box-shadow: 5px 5px 15px rgb(65 105 225 / 40%);
  transform: skew(-3deg) scale(1.02);
  outline: none;
}

.action-cubism {
  display: flex;
  gap: 10px;
}

.action-cube {
  width: 120px;
  height: 45px;
  border-radius: 15px 0;
  transform: perspective(600px) rotateX(15deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-cube:hover {
  transform: perspective(600px) rotateX(15deg) scale(1.05);
}

.refresh-cube {
  background: linear-gradient(45deg, #ff6b6b, #ff8e8e);
}

.add-cube {
  background: linear-gradient(45deg, #4ecdc4, #45b7d1);
}

.cube-face {
  display: flex;
  width: 100%;
  height: 100%;
  border-radius: 15px 0;
  font-weight: bold;
  color: white;
  box-shadow: 0 5px 15px rgb(0 0 0 / 30%);
  align-items: center;
  justify-content: center;
  gap: 5px;
}

.cube-icon {
  font-size: 1.2rem;
}

.cube-text {
  font-size: 0.9rem;
}

/* 毕加索风格数据表格 */
.data-cubism {
  display: flex;
  position: relative;
  z-index: 5;
  padding: 20px;
  border-radius: 20px;
  overflow: hidden;
  background: rgb(255 255 255 / 95%);
  box-shadow: 0 20px 40px rgb(32 178 170 / 30%);
  transform: perspective(1000px) rotateX(3deg);
  flex: 1;
  flex-direction: column;
}

.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.table-header {
  display: flex;
  padding: 15px 0;
  border-radius: 15px 15px 0 0;
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
  margin-bottom: 10px;
  flex-shrink: 0;
}

.header-cell {
  display: flex;
  padding: 10px;
  font-size: 0.9rem;
  font-weight: 900;
  color: white;
  transform: rotate(1deg);
  align-items: center;
  justify-content: center;
  text-transform: uppercase;
  letter-spacing: 1px;
  text-shadow: 2px 2px 4px rgb(0 0 0 / 30%);
}

.table-body {
  max-height: calc(100vh - 320px); /* 减去头部、搜索栏、分页等空间 */

  /* 确保5行数据能够完整显示，避免内容被遮挡 */
  min-height: 350px; /* 5行 × 约70px行高 = 350px */
  flex: 1;
  overflow: hidden auto;

  /* 毕加索风格滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #c8a2c8 rgb(200 162 200 / 20%);
}

.table-body::-webkit-scrollbar {
  width: 8px;
}

.table-body::-webkit-scrollbar-track {
  border-radius: 8px;
  background: linear-gradient(45deg, rgb(200 162 200 / 10%), rgb(32 178 170 / 10%));
}

.table-body::-webkit-scrollbar-thumb {
  border-radius: 8px;
  background: linear-gradient(45deg, #c8a2c8, #20b2aa);
  transition: all 0.3s ease;
}

.table-body::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(45deg, #9370db, #4169e1);
}

.data-row {
  display: flex;
  height: 50px !important; /* 🎯 固定高度50px，与左侧菜单项高度完全一致 */
  min-height: 50px !important; /* 🎯 调整为50px，与菜单项高度完全一致 */
  border-radius: 10px;
  background: linear-gradient(90deg,
    rgb(200 162 200 / 10%),
    rgb(32 178 170 / 10%),
    rgb(65 105 225 / 10%)
  );
  transition: all 0.3s ease;
  margin-bottom: 8px; /* 🎯 保持8px间距，与菜单项间距一致 */
}

.data-row:hover {
  background: linear-gradient(90deg,
    rgb(200 162 200 / 20%),
    rgb(32 178 170 / 20%),
    rgb(65 105 225 / 20%)
  );
  box-shadow: 0 4px 12px rgb(32 178 170 / 30%);
  transform: scale(1.02);
}

.data-cell {
  display: flex;
  max-height: 50px; /* 🎯 强制限制最大高度为50px */
  padding: 0 10px; /* 🎯 调整内边距，只保留左右内边距以适应50px高度 */
  align-items: center;
  justify-content: center;
}

.cell-content {
  display: flex;
  width: 100%;
  font-size: 1rem;
  font-weight: 600;
  text-align: center;
  color: #2c3e50;
  align-items: center;
  justify-content: center;
}

/* 毕加索风格客户信息 */
.customer-fragment {
  display: flex;
  align-items: center;
  gap: 12px;
}

.customer-avatar {
  display: flex;
  width: 40px;
  height: 40px;
  border-radius: 50% 20%;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  background: linear-gradient(45deg, #c8a2c8, #dda0dd);
  box-shadow: 0 4px 12px rgb(200 162 200 / 40%);
  transform: rotate(12deg);
  align-items: center;
  justify-content: center;
  animation: avatarFloat 4s ease-in-out infinite;
}

@keyframes avatarFloat {
  0%, 100% { transform: rotate(12deg) translateY(0); }
  50% { transform: rotate(12deg) translateY(-3px); }
}

.customer-info {
  flex: 1;
}

.customer-name {
  font-size: 1rem;
  font-weight: bold;
  color: #2c3e50;
  transform: skew(-2deg);
  margin-bottom: 3px;
}

.customer-phone {
  font-size: 0.8rem;
  color: #7f8c8d;
  transform: skew(1deg);
}

/* 毕加索风格服务项目 */
.service-fragment {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.service-name {
  width: fit-content;
  padding: 3px 8px;
  border-radius: 8px 0;
  font-size: 0.9rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(32 178 170 / 20%), rgb(255 255 255 / 80%));
  transform: skew(-1deg);
}

.service-price {
  font-size: 0.8rem;
  font-weight: bold;
  color: #27ae60;
  transform: skew(1deg);
}

/* 毕加索风格技师信息 */
.therapist-fragment {
  display: flex;
  padding: 8px 12px;
  border-radius: 12px 0;
  background: linear-gradient(135deg, rgb(200 162 200 / 20%), rgb(255 255 255 / 80%));
  transform: skew(-1deg);
  align-items: center;
  gap: 8px;
}

.therapist-icon {
  font-size: 1.2rem;
  transform: rotate(10deg);
}

.therapist-name {
  font-size: 0.9rem;
  font-weight: bold;
  color: #2c3e50;
}

/* 毕加索风格时间信息 */
.time-fragment {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.date-part {
  padding: 3px 8px;
  border-radius: 6px;
  font-size: 0.9rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(65 105 225 / 20%), rgb(255 255 255 / 80%));
  transform: skew(-2deg);
}

.time-part {
  font-size: 0.8rem;
  font-weight: bold;
  color: #8e44ad;
  transform: skew(1deg);
}

/* 毕加索风格状态 */
.status-fragment {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 12px 0;
  transform: skew(-1deg);
}

.status-pending {
  border: 2px solid rgb(243 156 18 / 30%);
  background: linear-gradient(135deg, rgb(243 156 18 / 20%), rgb(230 126 34 / 10%));
}

.status-confirmed {
  border: 2px solid rgb(32 178 170 / 30%);
  background: linear-gradient(135deg, rgb(32 178 170 / 20%), rgb(0 206 209 / 10%));
}

.status-completed {
  border: 2px solid rgb(39 174 96 / 30%);
  background: linear-gradient(135deg, rgb(39 174 96 / 20%), rgb(46 204 113 / 10%));
}

.status-cancelled {
  border: 2px solid rgb(231 76 60 / 30%);
  background: linear-gradient(135deg, rgb(231 76 60 / 20%), rgb(192 57 43 / 10%));
}

.status-indicator {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: statusPulse 2s ease-in-out infinite;
}

.status-pending .status-indicator {
  background: #f39c12;
}

.status-confirmed .status-indicator {
  background: #20b2aa;
}

.status-completed .status-indicator {
  background: #27ae60;
}

.status-cancelled .status-indicator {
  background: #e74c3c;
}

@keyframes statusPulse {
  0%, 100% { opacity: 1; transform: scale(1); }
  50% { opacity: 0.7; transform: scale(1.2); }
}

.status-text {
  font-size: 0.8rem;
  font-weight: bold;
}

.status-pending .status-text {
  color: #f39c12;
}

.status-confirmed .status-text {
  color: #20b2aa;
}

.status-completed .status-text {
  color: #27ae60;
}

.status-cancelled .status-text {
  color: #e74c3c;
}

/* 毕加索风格操作按钮 */
.action-fragments {
  display: flex;
  gap: 6px;
  flex-wrap: wrap;
  justify-content: center;
}

.action-btn {
  padding: 6px 12px;
  border-radius: 8px 0;
  font-size: 0.8rem;
  font-weight: bold;
  color: white;
  transform: perspective(200px) rotateX(8deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.action-btn:hover {
  transform: perspective(200px) rotateX(8deg) scale(1.05);
}

.action-btn.edit {
  background: linear-gradient(45deg, #c8a2c8, #dda0dd);
}

.action-btn.view {
  background: linear-gradient(45deg, #4169e1, #6495ed);
}

.action-btn.cancel {
  background: linear-gradient(45deg, #e74c3c, #c0392b);
}

/* 🎨 梵高风格分页组件 - 符合CI_CD_STANDARDS.md */
.pagination-container {
  display: flex;
  padding: 12px 18px;
  border: 2px solid rgb(192 132 252 / 40%);
  border-radius: 12px;
  background: linear-gradient(135deg, rgb(139 92 246 / 10%), rgb(168 85 247 / 10%));
  box-shadow:
    0 4px 8px rgb(139 92 246 / 20%),
    0 2px 4px rgb(192 132 252 / 10%);
  margin-top: 15px;
  justify-content: space-between;
  align-items: center;
  flex-wrap: wrap;
  gap: 15px;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  font-size: 14px;
  font-weight: 600;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.highlight-number {
  font-size: 16px;
  font-weight: 700;
  color: var(--van-gogh-accent);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 40%);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-label {
  font-size: 13px;
  font-weight: 600;
  color: var(--van-gogh-primary);
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.page-size-select {
  padding: 6px 12px;
  border: 2px solid var(--van-gogh-accent);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-primary-dark), var(--van-gogh-secondary));
  box-shadow: 0 2px 4px rgb(139 92 246 / 30%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.page-size-select:hover {
  border-color: var(--van-gogh-primary);
  box-shadow: 0 4px 8px rgb(139 92 246 / 40%);
}

.page-size-select option {
  padding: 8px;
  color: white;
  background: var(--van-gogh-primary-dark);
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  padding: 8px 12px;
  border: 2px solid var(--van-gogh-primary);
  border-radius: 6px;
  font-size: 13px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-secondary), var(--van-gogh-secondary-dark));
  box-shadow: 0 2px 4px rgb(139 92 246 / 30%);
  transition: all 0.3s ease;
  cursor: pointer;
}

.page-btn:hover:not(:disabled) {
  background: linear-gradient(135deg, var(--van-gogh-primary), var(--van-gogh-primary-dark));
  box-shadow: 0 4px 8px rgb(139 92 246 / 40%);
  transform: translateY(-1px);
}

.page-btn:disabled {
  background: linear-gradient(135deg, #9ca3af, #6b7280);
  border-color: #d1d5db;
  cursor: not-allowed;
  opacity: 0.6;
  transform: none;
}

.page-btn.active {
  color: white;
  background: linear-gradient(135deg, var(--van-gogh-accent), var(--van-gogh-accent-dark));
  box-shadow:
    0 4px 8px rgb(244 114 182 / 40%),
    inset 1px 1px 2px rgb(255 255 255 / 30%);
  border-color: var(--van-gogh-accent-light);
}

.page-numbers {
  display: flex;
  gap: 4px;
}

.page-number {
  min-width: 36px;
  text-align: center;
}

/* 毕加索风格模态框 */
.modal-overlay {
  display: flex;
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1000;
  width: 100%;
  height: 100%;
  background: rgb(65 105 225 / 70%);
  align-items: center;
  justify-content: center;
}

.modal-cubism {
  width: 85%;
  max-width: min(700px, calc(100vw - 220px));
  max-height: 80vh;
  border-radius: 20px 5px;
  overflow: hidden;
  background: linear-gradient(135deg, #c8a2c8, #20b2aa, #4169e1, #dda0dd);
  box-shadow: 0 25px 50px rgb(32 178 170 / 40%);
  transform: perspective(800px) rotateY(3deg);
  background-size: 400% 400%;
  animation: modalFlow 8s ease infinite;
}

@keyframes modalFlow {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px;
  background: rgb(255 255 255 / 95%);
  border-bottom: 3px solid #c8a2c8;
}

.header-title {
  font-size: 1.4rem;
  font-weight: 900;
  color: #2c3e50;
  text-transform: uppercase;
  letter-spacing: 1px;
  transform: skew(-2deg);
}

.close-btn {
  display: flex;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  font-size: 1.2rem;
  font-weight: bold;
  color: white;
  background: linear-gradient(45deg, #c8a2c8, #20b2aa);
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.close-btn:hover {
  background: linear-gradient(45deg, #4169e1, #6495ed);
  transform: rotate(90deg) scale(1.1);
}

.modal-body {
  max-height: 60vh;
  padding: 25px;
  background: rgb(255 255 255 / 95%);
  overflow-y: auto;

  /* 模态框滚动条 */
  scrollbar-width: thin;
  scrollbar-color: #c8a2c8 rgb(200 162 200 / 20%);
}

.modal-body::-webkit-scrollbar {
  width: 6px;
}

.modal-body::-webkit-scrollbar-track {
  border-radius: 6px;
  background: rgb(200 162 200 / 10%);
}

.modal-body::-webkit-scrollbar-thumb {
  border-radius: 6px;
  background: linear-gradient(45deg, #c8a2c8, #20b2aa);
}

/* 毕加索风格表单 */
.form-cubism {
  transform: perspective(600px) rotateX(2deg);
}

.form-row {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.form-row.full-width .form-group {
  flex: 1;
}

.form-group {
  flex: 1;
  position: relative;
}

.form-label {
  display: block;
  width: fit-content;
  padding: 4px 10px;
  border-radius: 8px 0;
  font-size: 0.9rem;
  font-weight: bold;
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(200 162 200 / 80%), rgb(255 255 255 / 90%));
  transform: skew(-3deg);
  margin-bottom: 8px;
}

.form-input, .form-select, .form-textarea {
  width: 100%;
  height: 50px;                            /* 🎯 与菜单高度一致：50px */
  padding: 0 15px;                         /* 🎯 调整内边距适应新高度 */
  border: none;
  border-radius: 0 12px;
  font-size: 1rem;
  font-weight: 600;
  line-height: 50px;                       /* 🎯 与菜单行高一致 */
  color: #2c3e50;
  background: linear-gradient(135deg, rgb(200 162 200 / 80%), rgb(255 255 255 / 90%));
  box-shadow: 3px 3px 10px rgb(0 0 0 / 10%);
  transform: skew(-1deg);
  transition: all 0.3s ease;
}

.form-textarea {
  height: auto !important;                 /* 文本域保持自适应高度 */
  min-height: 50px !important;             /* 最小高度与菜单一致 */
  padding: 12px 15px !important;           /* 文本域保持原有内边距 */
  line-height: 1.5 !important;             /* 文本域使用正常行高 */
}

.form-input:focus, .form-select:focus, .form-textarea:focus {
  background: linear-gradient(135deg, rgb(200 162 200 / 90%), rgb(255 255 255 / 95%));
  box-shadow: 5px 5px 15px rgb(65 105 225 / 30%);
  transform: skew(-1deg) scale(1.02);
  outline: none;
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.modal-footer {
  padding: 20px 25px;
  background: rgb(255 255 255 / 95%);
  border-top: 3px solid #20b2aa;
}

.footer-actions {
  display: flex;
  justify-content: flex-end;
  gap: 15px;
}

.footer-actions .action-btn {
  padding: 12px 25px;
  border-radius: 20px 5px;
  font-weight: bold;
  transform: perspective(400px) rotateX(5deg);
  transition: all 0.3s ease;
  cursor: pointer;
}

.footer-actions .action-btn.cancel {
  color: white;
  background: linear-gradient(45deg, #95a5a6, #bdc3c7);
}

.footer-actions .action-btn.confirm {
  color: white;
  background: linear-gradient(45deg, #20b2aa, #4169e1);
}

.footer-actions .action-btn:hover {
  transform: perspective(400px) rotateX(5deg) scale(1.05);
}

.btn-face {
  font-size: 1rem;
}

/* 响应式设计 */
@media (width <= 768px) {
  .picasso-appointments {
    left: 0;
    width: 100vw;
    padding: 15px;
  }

  .action-toolbar {
    flex-direction: column;
    gap: 15px;
  }

  .filter-cubism {
    flex-direction: column;
  }

  .action-cubism {
    justify-content: center;
  }

  .data-row {
    flex-direction: column;
    min-height: auto;
  }

  .data-cell {
    border-bottom: 1px solid rgb(200 162 200 / 20%);
    min-height: 50px;                      /* 🎯 与菜单高度一致：50px */
  }

  .modal-cubism {
    width: 95%;
    margin: 10px;
  }

  .form-row {
    flex-direction: column;
    gap: 15px;
  }

  .header-cell, .cell-content {
    font-size: 0.8rem;
  }

  .title-layer {
    font-size: 2rem;
  }

  .subtitle-fragment {
    font-size: 1rem;
  }

  .action-fragments {
    justify-content: center;
  }

  /* 移动端梵高分页样式 */
  .pagination-container {
    flex-direction: column;
    gap: 10px;
    padding: 12px 15px;
  }

  .pagination-controls {
    gap: 15px;
  }

  .page-navigation {
    gap: 6px;
  }

  .page-btn {
    padding: 6px 10px;
    font-size: 12px;
  }

  .page-number {
    min-width: 32px;
  }

  /* 移动端Toast样式 */
  .toast-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
    min-width: auto;
  }
}

/* 🎨 Toast通知组件样式 - 符合CI_CD_STANDARDS.md用户反馈规范 */
.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 2000;
  max-width: 500px;
  min-width: 300px;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow:
    0 8px 32px rgb(0 0 0 / 20%),
    0 4px 16px rgb(0 0 0 / 10%);
  backdrop-filter: blur(10px);
  animation: slideInRight 0.3s ease-out;
}

.toast-success {
  border: 2px solid rgb(34 197 94 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(34 197 94 / 90%), rgb(22 163 74 / 90%));
}

.toast-error {
  border: 2px solid rgb(239 68 68 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(239 68 68 / 90%), rgb(220 38 38 / 90%));
}

.toast-warning {
  border: 2px solid rgb(245 158 11 / 50%);
  color: white;
  background: linear-gradient(135deg, rgb(245 158 11 / 90%), rgb(217 119 6 / 90%));
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toast-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.toast-message {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
