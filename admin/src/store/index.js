/**
 * Pinia状态管理 - 基于20年经验的专业状态管理
 * 模块化设计，严谨的数据流控制
 */

import {
    appointmentAPI,
    authAPI,
    customerAPI,
    dashboardAPI,
    serviceAPI,
    therapistAPI
} from '@/api'
import { message } from 'ant-design-vue'
import { createPinia, defineStore } from 'pinia'
import { computed, ref } from 'vue'

// 创建pinia实例
export const pinia = createPinia()

// 用户状态管理
export const useUserStore = defineStore('user', () => {
  const userInfo = ref(null)
  const token = ref(localStorage.getItem('admin_token'))
  const permissions = ref([])

  const isLoggedIn = computed(() => {
    const loggedIn = !!token.value
    console.log('🔍 检查登录状态:', { token: token.value, isLoggedIn: loggedIn })
    return loggedIn
  })
  const userName = computed(() => userInfo.value?.name || '管理员')

  // 登录
  const login = async (credentials) => {
    try {
      console.log('🔐 开始登录:', credentials)

      // 开发环境模拟登录
      if (process.env.NODE_ENV === 'development') {
        console.log('🧪 开发环境模拟登录')

        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 1000))

        // 简单的模拟验证
        if (credentials.username === 'admin' && credentials.password === 'admin123') {
          const mockToken = 'mock_token_' + Date.now()
          const mockUser = {
            id: 1,
            username: 'admin',
            name: '系统管理员',
            role: 'admin',
            avatar: null
          }

          token.value = mockToken
          userInfo.value = mockUser
          localStorage.setItem('admin_token', mockToken)

          message.success('登录成功！欢迎回来')
          console.log('✅ 模拟登录成功:', mockUser)
          return true
        } else {
          message.error('用户名或密码错误 (提示: admin/admin123)')
          console.log('❌ 模拟登录失败: 用户名或密码错误')
          return false
        }
      }

      // 生产环境调用后端登录API
      const response = await authAPI.login(credentials)

      if (response.success) {
        token.value = response.token
        userInfo.value = response.user
        localStorage.setItem('admin_token', response.token)

        message.success(response.message || '登录成功')
        console.log('✅ 登录成功:', response.user)
        return true
      } else {
        message.error(response.message || '登录失败')
        console.log('❌ 登录失败:', response.message)
        return false
      }
    } catch (error) {
      console.error('❌ 登录异常:', error)
      const errorMessage = error.response?.data?.message || error.message || '登录失败'
      message.error(errorMessage)
      return false
    }
  }

  // 登出
  const logout = () => {
    token.value = null
    userInfo.value = null
    permissions.value = []
    localStorage.removeItem('admin_token')
    message.success('已退出登录')
  }

  return {
    userInfo,
    token,
    permissions,
    isLoggedIn,
    userName,
    login,
    logout
  }
})

// 服务状态管理
export const useServiceStore = defineStore('service', () => {
  const services = ref([])
  const loading = ref(false)
  const total = ref(0)

  // 获取服务列表
  const fetchServices = async (params = {}) => {
    loading.value = true
    try {
      const response = await serviceAPI.getServices(params)
      services.value = response.results || response
      total.value = response.count || response.length
    } catch (error) {
      console.error('获取服务列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 创建服务
  const createService = async (data) => {
    try {
      await serviceAPI.createService(data)
      message.success('服务创建成功')
      await fetchServices()
      return true
    } catch (error) {
      message.error('服务创建失败')
      return false
    }
  }

  // 更新服务
  const updateService = async (id, data) => {
    try {
      await serviceAPI.updateService(id, data)
      message.success('服务更新成功')
      await fetchServices()
      return true
    } catch (error) {
      message.error('服务更新失败')
      return false
    }
  }

  // 删除服务
  const deleteService = async (id) => {
    try {
      await serviceAPI.deleteService(id)
      message.success('服务删除成功')
      await fetchServices()
      return true
    } catch (error) {
      message.error('服务删除失败')
      return false
    }
  }

  return {
    services,
    loading,
    total,
    fetchServices,
    createService,
    updateService,
    deleteService
  }
})

// 技师状态管理
export const useTherapistStore = defineStore('therapist', () => {
  const therapists = ref([])
  const loading = ref(false)
  const total = ref(0)

  // 获取技师列表
  const fetchTherapists = async (params = {}) => {
    loading.value = true
    try {
      const response = await therapistAPI.getTherapists(params)
      therapists.value = response.results || response
      total.value = response.count || response.length
    } catch (error) {
      console.error('获取技师列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 创建技师
  const createTherapist = async (data) => {
    try {
      await therapistAPI.createTherapist(data)
      message.success('技师创建成功')
      await fetchTherapists()
      return true
    } catch (error) {
      message.error('技师创建失败')
      return false
    }
  }

  // 更新技师
  const updateTherapist = async (id, data) => {
    try {
      await therapistAPI.updateTherapist(id, data)
      message.success('技师更新成功')
      await fetchTherapists()
      return true
    } catch (error) {
      message.error('技师更新失败')
      return false
    }
  }

  return {
    therapists,
    loading,
    total,
    fetchTherapists,
    createTherapist,
    updateTherapist
  }
})

// 客户状态管理
export const useCustomerStore = defineStore('customer', () => {
  const customers = ref([])
  const loading = ref(false)
  const total = ref(0)

  // 获取客户列表
  const fetchCustomers = async (params = {}) => {
    loading.value = true
    try {
      const response = await customerAPI.getCustomers(params)
      customers.value = response.results || response
      total.value = response.count || response.length
    } catch (error) {
      console.error('获取客户列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  return {
    customers,
    loading,
    total,
    fetchCustomers
  }
})

// 预约状态管理
export const useAppointmentStore = defineStore('appointment', () => {
  const appointments = ref([])
  const loading = ref(false)
  const total = ref(0)

  // 获取预约列表
  const fetchAppointments = async (params = {}) => {
    loading.value = true
    try {
      const response = await appointmentAPI.getAppointments(params)
      appointments.value = response.results || response
      total.value = response.count || response.length
    } catch (error) {
      console.error('获取预约列表失败:', error)
    } finally {
      loading.value = false
    }
  }

  // 创建预约
  const createAppointment = async (data) => {
    try {
      await appointmentAPI.createAppointment(data)
      message.success('预约创建成功')
      await fetchAppointments()
      return true
    } catch (error) {
      message.error('预约创建失败')
      return false
    }
  }

  return {
    appointments,
    loading,
    total,
    fetchAppointments,
    createAppointment
  }
})

// 仪表盘状态管理
export const useDashboardStore = defineStore('dashboard', () => {
  const stats = ref({})
  const loading = ref(false)

  // 获取统计数据
  const fetchStats = async () => {
    loading.value = true
    try {
      console.log('📊 尝试获取仪表盘统计数据...');
      const response = await dashboardAPI.getStats()
      stats.value = response
      console.log('✅ 仪表盘统计数据获取成功');
    } catch (error) {
      console.warn('⚠️ 后端服务不可用，使用模拟数据:', error.message);
      // 使用模拟数据
      stats.value = {
        today_appointments: 12,
        today_income: 2580.00,
        total_customers: 156,
        active_therapists: 8,
        pending_appointments: 5,
        completed_appointments_today: 7,
        weekly_income: [1200, 1800, 2200, 1900, 2580, 2100, 1650],
        service_distribution: [
          { name: '中式按摩', appointment_count: 45 },
          { name: '足疗保健', appointment_count: 32 },
          { name: '拔罐刮痧', appointment_count: 28 },
          { name: '艾灸理疗', appointment_count: 21 },
          { name: '推拿正骨', appointment_count: 18 }
        ],
        therapist_performance: [
          { name: '张师傅', appointment_count: 15, total_income: 1200 },
          { name: '李师傅', appointment_count: 12, total_income: 980 },
          { name: '王师傅', appointment_count: 10, total_income: 850 },
          { name: '赵师傅', appointment_count: 8, total_income: 720 },
          { name: '刘师傅', appointment_count: 6, total_income: 580 }
        ]
      }
    } finally {
      loading.value = false
    }
  }

  return {
    stats,
    loading,
    fetchStats
  }
})
