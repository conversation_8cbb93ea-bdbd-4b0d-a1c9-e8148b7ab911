/**
 * Ant Design 浅紫色主题配置
 * 统一所有组件的紫色主题样式
 */

export const purpleTheme = {
  token: {
    // 主色调
    colorPrimary: '#9333ea',
    colorPrimaryHover: '#a855f7',
    colorPrimaryActive: '#7c3aed',
    colorPrimaryBg: '#faf5ff',
    colorPrimaryBgHover: '#f3e8ff',
    colorPrimaryBorder: '#d8b4fe',
    colorPrimaryBorderHover: '#c084fc',
    
    // 信息色
    colorInfo: '#9333ea',
    colorInfoHover: '#a855f7',
    colorInfoActive: '#7c3aed',
    colorInfoBg: '#faf5ff',
    
    // 链接色
    colorLink: '#9333ea',
    colorLinkHover: '#a855f7',
    colorLinkActive: '#7c3aed',
    
    // 成功色（保持绿色）
    colorSuccess: '#52c41a',
    colorSuccessHover: '#73d13d',
    colorSuccessActive: '#389e0d',
    
    // 警告色（保持橙色）
    colorWarning: '#faad14',
    colorWarningHover: '#ffc53d',
    colorWarningActive: '#d48806',
    
    // 错误色（保持红色）
    colorError: '#f5222d',
    colorErrorHover: '#ff4d4f',
    colorErrorActive: '#cf1322',
    
    // 文本色
    colorText: '#333333',
    colorTextSecondary: '#666666',
    colorTextTertiary: '#999999',
    colorTextQuaternary: '#cccccc',
    
    // 背景色
    colorBgContainer: '#ffffff',
    colorBgElevated: '#ffffff',
    colorBgLayout: '#f0f2f5',
    colorBgSpotlight: '#faf5ff',
    
    // 边框色
    colorBorder: '#d9d9d9',
    colorBorderSecondary: '#e8e8e8',
    
    // 圆角
    borderRadius: 6,
    borderRadiusLG: 8,
    borderRadiusSM: 4,
    borderRadiusXS: 2,
    
    // 阴影
    boxShadow: '0 2px 8px rgba(147, 51, 234, 0.08)',
    boxShadowSecondary: '0 4px 12px rgba(147, 51, 234, 0.12)',
    boxShadowTertiary: '0 6px 16px rgba(147, 51, 234, 0.16)',
    
    // 字体
    fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif',
    fontSize: 14,
    fontSizeLG: 16,
    fontSizeSM: 12,
    fontSizeXL: 20,
    
    // 行高
    lineHeight: 1.5715,
    lineHeightLG: 1.5,
    lineHeightSM: 1.66,
    
    // 控件高度 - 按照菜单高度调整
    controlHeight: 50,        // 🎯 与菜单高度保持一致：50px
    controlHeightLG: 58,      // 大尺寸控件
    controlHeightSM: 42,      // 小尺寸控件
    controlHeightXS: 34,      // 超小尺寸控件

    // 间距 - 按照菜单间距调整
    padding: 8,               // 🎯 与菜单间距保持一致：8px
    paddingLG: 16,            // 大间距
    paddingSM: 6,             // 小间距
    paddingXS: 4,             // 超小间距
    paddingXXS: 2,            // 最小间距

    margin: 8,                // 🎯 与菜单间距保持一致：8px
    marginLG: 16,             // 大边距
    marginSM: 6,              // 小边距
    marginXS: 4,              // 超小边距
    marginXXS: 2,             // 最小边距
  },
  
  components: {
    // 按钮组件
    Button: {
      colorPrimary: '#9333ea',
      colorPrimaryHover: '#a855f7',
      colorPrimaryActive: '#7c3aed',
      primaryShadow: '0 2px 0 rgba(147, 51, 234, 0.1)',
      borderRadius: 6,
      fontWeight: 500,
    },
    
    // 表格组件
    Table: {
      headerBg: '#faf5ff',
      headerColor: '#333333',
      rowHoverBg: '#f3e8ff',
      borderColor: '#e8e8e8',
      headerSortActiveBg: '#f3e8ff',
      headerSortHoverBg: '#faf5ff',
    },
    
    // 菜单组件
    Menu: {
      itemSelectedBg: 'linear-gradient(135deg, rgba(147, 51, 234, 0.15) 0%, rgba(168, 85, 247, 0.12) 50%, rgba(147, 51, 234, 0.15) 100%)',
      itemSelectedColor: '#7c3aed',
      itemHoverBg: 'rgba(147, 51, 234, 0.1)',
      itemHoverColor: '#9333ea',
      itemActiveBg: 'rgba(147, 51, 234, 0.2)',
      iconSize: 16,
      itemHeight: 40,
      subMenuItemBg: 'transparent',
    },
    
    // 输入框组件
    Input: {
      colorBorder: '#d9d9d9',
      colorBorderHover: '#a855f7',
      colorPrimaryHover: '#a855f7',
      activeBorderColor: '#9333ea',
      activeShadow: '0 0 0 2px rgba(147, 51, 234, 0.2)',
      hoverBorderColor: '#a855f7',
    },
    
    // 选择器组件
    Select: {
      colorBorder: '#d9d9d9',
      colorBorderHover: '#a855f7',
      colorPrimary: '#9333ea',
      colorPrimaryHover: '#a855f7',
      optionSelectedBg: '#f3e8ff',
      optionActiveBg: '#faf5ff',
      optionSelectedColor: '#7c3aed',
    },
    
    // 标签组件
    Tag: {
      colorPrimary: '#9333ea',
      colorPrimaryBg: '#f3e8ff',
      colorPrimaryBorder: '#d8b4fe',
      colorPrimaryHover: '#a855f7',
    },
    
    // 进度条组件
    Progress: {
      colorInfo: '#9333ea',
      colorSuccess: '#52c41a',
      remainingColor: '#f0f0f0',
    },
    
    // 分页组件
    Pagination: {
      colorPrimary: '#9333ea',
      colorPrimaryHover: '#a855f7',
      itemActiveBg: '#9333ea',
      itemLinkBg: '#ffffff',
      itemInputBg: '#ffffff',
    },
    
    // 模态框组件
    Modal: {
      headerBg: '#faf5ff',
      contentBg: '#ffffff',
      footerBg: '#ffffff',
      titleColor: '#333333',
    },
    
    // 抽屉组件
    Drawer: {
      colorBgElevated: '#ffffff',
      colorBgMask: 'rgba(0, 0, 0, 0.45)',
    },
    
    // 卡片组件
    Card: {
      headerBg: '#faf5ff',
      actionsBg: '#fafafa',
      colorBorderSecondary: 'rgba(147, 51, 234, 0.1)',
      colorTextHeading: '#333333',
    },
    
    // 表单组件
    Form: {
      labelColor: '#333333',
      labelRequiredMarkColor: '#f5222d',
      itemMarginBottom: 24,
    },
    
    // 日期选择器
    DatePicker: {
      colorPrimary: '#9333ea',
      colorPrimaryHover: '#a855f7',
      cellActiveWithRangeBg: '#f3e8ff',
      cellHoverWithRangeBg: '#faf5ff',
      cellRangeBorderColor: '#d8b4fe',
    },
    
    // 时间选择器
    TimePicker: {
      colorPrimary: '#9333ea',
      colorPrimaryHover: '#a855f7',
      cellHoverBg: '#faf5ff',
    },
    
    // 开关组件
    Switch: {
      colorPrimary: '#9333ea',
      colorPrimaryHover: '#a855f7',
    },
    
    // 滑块组件
    Slider: {
      colorPrimary: '#9333ea',
      colorPrimaryBorder: '#9333ea',
      colorPrimaryBorderHover: '#a855f7',
      handleColor: '#9333ea',
      handleActiveColor: '#a855f7',
      trackBg: '#9333ea',
      trackHoverBg: '#a855f7',
      railBg: '#f0f0f0',
      railHoverBg: '#e8e8e8',
    },
    
    // 评分组件
    Rate: {
      colorFillContent: '#9333ea',
    },
    
    // 步骤条组件
    Steps: {
      colorPrimary: '#9333ea',
      colorText: '#333333',
      colorTextDescription: '#666666',
    },
    
    // 徽章组件
    Badge: {
      colorBgContainer: '#9333ea',
      colorText: '#ffffff',
    },
    
    // 头像组件
    Avatar: {
      colorTextPlaceholder: '#ffffff',
      colorBgContainer: '#9333ea',
    },
    
    // 通知组件
    Notification: {
      colorBgElevated: '#ffffff',
      colorIcon: '#9333ea',
      colorIconHover: '#a855f7',
    },
    
    // 消息组件
    Message: {
      colorBgElevated: '#ffffff',
      colorText: '#333333',
      colorIcon: '#9333ea',
    },
    
    // 警告框组件
    Alert: {
      colorInfoBg: '#f3e8ff',
      colorInfoBorder: '#d8b4fe',
      colorInfo: '#7c3aed',
    },
    
    // 气泡确认框
    Popconfirm: {
      colorBgElevated: '#ffffff',
      colorWarning: '#faad14',
    },
    
    // 工具提示
    Tooltip: {
      colorBgSpotlight: '#7c3aed',
      colorTextLightSolid: '#ffffff',
    },
    
    // 气泡卡片
    Popover: {
      colorBgElevated: '#ffffff',
      colorText: '#333333',
    },
    
    // 锚点组件
    Anchor: {
      colorPrimary: '#9333ea',
      colorPrimaryHover: '#a855f7',
      colorSplit: '#e8e8e8',
    },
    
    // 回到顶部
    BackTop: {
      colorBgElevated: '#9333ea',
      colorText: '#ffffff',
      colorTextHover: '#ffffff',
    }
  }
};

export default purpleTheme;
