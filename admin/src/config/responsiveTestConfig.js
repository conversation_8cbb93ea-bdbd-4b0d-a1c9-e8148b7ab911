/**
 * 响应式测试配置 - 符合壹心堂开发规范
 * 🚨 强制要求：必须测试8种分辨率，支持桌面/平板/移动端
 */

export const RESPONSIVE_TEST_CONFIG = {
  // 🚨 强制要求：8种分辨率测试 (符合开发规范)
  resolutions: [
    {
      name: '4K超高清',
      width: 3840,
      height: 2160,
      type: 'desktop',
      description: '4K显示器，超高分辨率',
      testPoints: ['表格列宽适配', '分页组件显示', '模态框居中']
    },
    {
      name: '2K高清',
      width: 2560,
      height: 1440,
      type: 'desktop', 
      description: '2K显示器，高分辨率',
      testPoints: ['内容区域利用', '侧边栏比例', '字体大小适配']
    },
    {
      name: '标准桌面',
      width: 1920,
      height: 1080,
      type: 'desktop',
      description: '标准桌面分辨率',
      testPoints: ['默认布局显示', '所有功能可见', '操作按钮大小']
    },
    {
      name: '小桌面',
      width: 1366,
      height: 768,
      type: 'desktop',
      description: '小尺寸桌面/笔记本',
      testPoints: ['内容压缩适配', '滚动条出现', '按钮文字显示']
    },
    {
      name: '平板横屏',
      width: 1024,
      height: 768,
      type: 'tablet',
      description: 'iPad等平板横屏模式',
      testPoints: ['触摸友好设计', '表格水平滚动', '搜索框适配']
    },
    {
      name: '平板竖屏',
      width: 768,
      height: 1024,
      type: 'tablet',
      description: 'iPad等平板竖屏模式',
      testPoints: ['垂直布局优化', '分页组件堆叠', '模态框适配']
    },
    {
      name: '手机横屏',
      width: 667,
      height: 375,
      type: 'mobile',
      description: 'iPhone等手机横屏',
      testPoints: ['卡片式布局', '按钮图标显示', '导航简化']
    },
    {
      name: '手机竖屏',
      width: 375,
      height: 667,
      type: 'mobile',
      description: 'iPhone等手机竖屏',
      testPoints: ['单列布局', '触摸按钮大小', '文字可读性']
    }
  ],

  // 断点定义 (符合开发规范)
  breakpoints: {
    mobile: 768,
    tablet: 1024,
    desktop: 1025
  },

  // 测试检查项目 (符合开发规范)
  testChecks: {
    // 🚨 强制检查项目
    mandatory: [
      {
        name: '搜索区域可见性',
        selector: '.search-section',
        check: 'visibility',
        minWidth: 200,
        description: '搜索区域必须可见且宽度足够'
      },
      {
        name: '表格布局完整性',
        selector: '.table-container',
        check: 'layout',
        description: '表格容器布局正常，无溢出'
      },
      {
        name: '分页组件功能性',
        selector: '.pagination-container',
        check: 'functionality',
        description: '分页组件显示完整，按钮可点击'
      },
      {
        name: '操作按钮可用性',
        selector: '.action-btn',
        check: 'accessibility',
        minTouchSize: 44,
        description: '操作按钮大小符合触摸标准'
      },
      {
        name: '模态框响应式',
        selector: '.modal-overlay',
        check: 'responsive',
        description: '模态框在不同分辨率下正确显示'
      }
    ],

    // 建议检查项目
    recommended: [
      {
        name: '滚动条优化',
        selector: '.table-body',
        check: 'scrollbar',
        description: '表格滚动条仅在必要时显示'
      },
      {
        name: '文字可读性',
        selector: '.service-name',
        check: 'readability',
        minFontSize: 14,
        description: '服务名称文字大小适合阅读'
      },
      {
        name: '图片适配',
        selector: '.service-image',
        check: 'imageAdaptation',
        description: '服务图片在不同分辨率下正确显示'
      }
    ]
  },

  // 性能标准 (符合开发规范)
  performanceStandards: {
    // 页面加载时间
    loadTime: {
      desktop: 3000,  // 3秒
      tablet: 4000,   // 4秒  
      mobile: 5000    // 5秒
    },
    
    // 响应时间
    responseTime: {
      search: 500,    // 搜索响应 500ms
      pagination: 300, // 分页切换 300ms
      modal: 200      // 模态框打开 200ms
    },

    // 内存使用
    memoryUsage: {
      maxHeapSize: 100, // 100MB
      maxDOMNodes: 5000 // 5000个节点
    }
  },

  // UI设计规范检查 (符合毕加索风格要求)
  uiStandards: {
    // 颜色规范
    colors: {
      primary: '#8e44ad',      // 紫色主色调
      secondary: '#9b59b6',    // 次要紫色
      accent: '#f472b6',       // 强调色
      background: 'linear-gradient' // 渐变背景
    },

    // 字体规范
    typography: {
      minFontSize: 14,         // 最小字体大小
      maxLineHeight: 1.6,      // 最大行高
      fontWeight: {
        normal: 400,
        medium: 600,
        bold: 700
      }
    },

    // 间距规范
    spacing: {
      minPadding: 8,           // 最小内边距
      minMargin: 4,            // 最小外边距
      buttonPadding: '8px 12px', // 按钮内边距
      modalPadding: '20px'     // 模态框内边距
    },

    // 动画规范
    animations: {
      duration: '0.3s',        // 动画时长
      easing: 'cubic-bezier(0.4, 0, 0.2, 1)', // 缓动函数
      hoverScale: 1.05         // 悬停缩放比例
    }
  },

  // 测试报告配置
  reporting: {
    // 报告格式
    format: 'detailed',
    
    // 包含的信息
    includeScreenshots: true,
    includePerformanceMetrics: true,
    includeAccessibilityChecks: true,
    
    // 报告存储
    saveToLocalStorage: true,
    exportToFile: true,
    
    // 报告模板
    template: {
      title: '壹心堂服务管理响应式测试报告',
      sections: [
        'executive_summary',
        'test_results',
        'performance_metrics', 
        'ui_compliance',
        'recommendations'
      ]
    }
  },

  // 自动化测试配置
  automation: {
    // 是否启用自动测试
    enabled: true,
    
    // 测试间隔 (毫秒)
    interval: 1000,
    
    // 失败重试次数
    retryCount: 3,
    
    // 超时时间 (毫秒)
    timeout: 30000,
    
    // 并发测试数量
    concurrency: 2
  }
};

// 导出测试工具类型定义
export const TEST_TYPES = {
  FULL: 'full',           // 完整测试
  QUICK: 'quick',         // 快速测试
  SPECIFIC: 'specific',   // 特定分辨率测试
  PERFORMANCE: 'performance', // 性能测试
  UI_COMPLIANCE: 'ui_compliance' // UI规范检查
};

// 导出测试结果状态
export const TEST_STATUS = {
  PASS: 'pass',
  FAIL: 'fail', 
  WARNING: 'warning',
  SKIP: 'skip',
  RUNNING: 'running'
};

// 导出设备类型
export const DEVICE_TYPES = {
  DESKTOP: 'desktop',
  TABLET: 'tablet',
  MOBILE: 'mobile'
};
