/**
 * API服务层 - 基于20年经验的专业API封装
 * 严谨的错误处理和请求拦截
 */

import { message } from 'ant-design-vue'
import axios from 'axios'

// 创建axios实例
const api = axios.create({
  baseURL: process.env.NODE_ENV === 'production'
    ? '/api/v1'
    : 'http://localhost:8000/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 创建认证API实例
const authClient = axios.create({
  baseURL: process.env.NODE_ENV === 'production'
    ? '/api'
    : 'http://127.0.0.1:8000/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 添加请求拦截器
authClient.interceptors.request.use(
  config => {
    console.log('🌐 发送API请求:', {
      method: config.method,
      url: config.url,
      baseURL: config.baseURL,
      data: config.data
    })
    return config
  },
  error => {
    console.error('❌ 请求拦截器错误:', error)
    return Promise.reject(error)
  }
)

// 添加响应拦截器
authClient.interceptors.response.use(
  response => {
    console.log('✅ API响应成功:', {
      status: response.status,
      url: response.config.url,
      data: response.data
    })
    return response
  },
  error => {
    console.error('❌ API响应错误:', {
      status: error.response?.status,
      url: error.config?.url,
      message: error.message,
      data: error.response?.data
    })
    return Promise.reject(error)
  }
)

// 请求拦截器
api.interceptors.request.use(
  config => {
    // 添加认证token（如果有）
    const token = localStorage.getItem('admin_token')
    if (token) {
      config.headers.Authorization = `Bearer ${token}`
    }
    
    // 添加请求时间戳
    config.metadata = { startTime: new Date() }
    
    return config
  },
  error => {
    console.error('请求错误:', error)
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    // 计算请求耗时
    const endTime = new Date()
    const duration = endTime - response.config.metadata.startTime
    console.log(`API请求耗时: ${duration}ms - ${response.config.url}`)
    
    return response.data
  },
  error => {
    // 统一错误处理
    const { response } = error
    
    if (response) {
      switch (response.status) {
        case 401:
          message.error('未授权，请重新登录')
          // 清除token并跳转到登录页
          localStorage.removeItem('admin_token')
          window.location.href = '/login'
          break
        case 403:
          message.error('权限不足')
          break
        case 404:
          message.error('请求的资源不存在')
          break
        case 500:
          message.error('服务器内部错误')
          break
        default:
          message.error(response.data?.message || '请求失败')
      }
    } else {
      message.error('网络错误，请检查网络连接')
    }
    
    return Promise.reject(error)
  }
)

// 服务API
export const serviceAPI = {
  // 获取服务列表
  getServices: (params = {}) => api.get('/services/', { params }),
  
  // 获取服务详情
  getService: (id) => api.get(`/services/${id}/`),
  
  // 创建服务
  createService: (data) => api.post('/services/', data),
  
  // 更新服务
  updateService: (id, data) => api.put(`/services/${id}/`, data),
  
  // 删除服务
  deleteService: (id) => api.delete(`/services/${id}/`),
  
  // 获取热门服务
  getPopularServices: () => api.get('/services/popular/')
}

// 技师API
export const therapistAPI = {
  // 获取技师列表
  getTherapists: (params = {}) => api.get('/therapists/', { params }),
  
  // 获取技师详情
  getTherapist: (id) => api.get(`/therapists/${id}/`),
  
  // 创建技师
  createTherapist: (data) => api.post('/therapists/', data),
  
  // 更新技师
  updateTherapist: (id, data) => api.put(`/therapists/${id}/`, data),
  
  // 删除技师
  deleteTherapist: (id) => api.delete(`/therapists/${id}/`),
  
  // 获取技师可用时间段
  getAvailableSlots: (id, date) => api.get(`/therapists/${id}/available_slots/`, {
    params: { date }
  })
}

// 客户API
export const customerAPI = {
  // 获取客户列表
  getCustomers: (params = {}) => api.get('/customers/', { params }),
  
  // 获取客户详情
  getCustomer: (id) => api.get(`/customers/${id}/`),
  
  // 创建客户
  createCustomer: (data) => api.post('/customers/', data),
  
  // 更新客户
  updateCustomer: (id, data) => api.put(`/customers/${id}/`, data),
  
  // 删除客户
  deleteCustomer: (id) => api.delete(`/customers/${id}/`),
  
  // 获取客户预约记录
  getCustomerAppointments: (id, params = {}) => api.get(`/customers/${id}/appointments/`, { params })
}

// 预约API
export const appointmentAPI = {
  // 获取预约列表
  getAppointments: (params = {}) => api.get('/appointments/', { params }),
  
  // 获取预约详情
  getAppointment: (id) => api.get(`/appointments/${id}/`),
  
  // 创建预约
  createAppointment: (data) => api.post('/appointments/', data),
  
  // 更新预约
  updateAppointment: (id, data) => api.put(`/appointments/${id}/`, data),
  
  // 取消预约
  cancelAppointment: (id, reason) => api.post(`/appointments/${id}/cancel/`, { reason }),
  
  // 完成预约
  completeAppointment: (id) => api.post(`/appointments/${id}/complete/`),
  
  // 开始服务
  startService: (id) => api.post(`/appointments/${id}/start/`),
}

// 仪表盘API
export const dashboardAPI = {
  // 获取统计数据
  getStats: () => api.get('/dashboard/stats/'),
  
  // 获取图表数据
  getChartData: (type, params = {}) => api.get(`/dashboard/charts/${type}/`, { params })
}

// 版本API
export const versionAPI = {
  // 获取当前版本
  getCurrentVersion: () => api.get('/version/'),

  // 获取版本历史
  getVersionHistory: () => api.get('/version/history/')
}

// 认证API
export const authAPI = {
  // 管理员登录
  login: async (credentials) => {
    console.log('🔐 API调用登录:', credentials)
    const response = await authClient.post('/auth/login/', credentials)
    return response.data
  },

  // 管理员登出
  logout: async () => {
    console.log('🔐 API调用登出')
    const response = await authClient.post('/auth/logout/')
    return response.data
  },

  // 获取用户信息
  getProfile: async () => {
    console.log('🔐 API调用获取用户信息')
    const response = await authClient.get('/auth/profile/')
    return response.data
  },

  // 微信扫码登录相关API
  wechat: {
    // 生成微信登录二维码
    generateQR: async () => {
      console.log('📱 API调用生成微信登录二维码')
      const response = await authClient.post('/auth/wechat/qr/')
      return response.data
    },

    // 检查二维码扫描状态
    checkStatus: async (sessionId) => {
      console.log('🔍 API调用检查微信登录状态:', sessionId)
      const response = await authClient.get(`/auth/wechat/status/?session_id=${sessionId}`)
      return response.data
    }
  }
}

// 导出默认API实例
export default api
