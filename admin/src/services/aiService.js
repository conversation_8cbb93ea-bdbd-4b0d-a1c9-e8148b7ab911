/**
 * AI服务 - 火山引擎后端集成
 * 通过后端Python SDK调用火山引擎AI服务
 */

class AIService {
  constructor() {
    // 后端API配置
    this.backendBaseURL = 'http://localhost:8000/api';
    this.volcengineImageAPI = '/volcengine/generate-image/';
    this.volcengineConfigAPI = '/volcengine/config/';
    
    // DeepSeek配置（用于描述生成）
    this.deepseekApiKey = '***********************************';
    this.deepseekBaseURL = 'https://api.deepseek.com/v1/chat/completions';
    
    console.log('🌋 AI服务初始化完成 - 使用火山引擎后端集成');
  }

  /**
   * 检查火山引擎服务配置状态
   */
  async checkConfiguration() {
    try {
      const response = await fetch(`${this.backendBaseURL}${this.volcengineConfigAPI}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json'
        }
      });
      
      if (response.ok) {
        const result = await response.json();
        return result.configured;
      }
      return false;
    } catch (error) {
      console.warn('⚠️ 无法检查火山引擎配置状态:', error);
      return false;
    }
  }

  /**
   * 检查服务是否已配置（兼容性方法）
   */
  get isConfigured() {
    return true; // 后端配置，前端无需检查
  }

  /**
   * 生成服务图片 - 通过后端火山引擎API
   * @param {string} serviceName - 服务名称
   * @param {string} description - 服务描述
   * @returns {Promise<string>} 图片URL
   */
  async generateServiceImage(serviceName, description = '') {
    try {
      console.log(`🌋 调用后端火山引擎生成图片: ${serviceName}`);
      
      const response = await fetch(`${this.backendBaseURL}${this.volcengineImageAPI}`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          serviceName: serviceName,
          serviceDescription: description
        })
      });

      if (response.ok) {
        const result = await response.json();
        if (result.success) {
          console.log(`✅ 火山引擎图片生成成功: ${result.imageUrl}`);
          return result.imageUrl;
        } else {
          throw new Error(result.error || '图片生成失败');
        }
      } else {
        throw new Error(`API请求失败: ${response.status}`);
      }
    } catch (error) {
      console.warn('⚠️ 火山引擎图片生成失败，使用备用图片:', error.message);
      return this.getFallbackImage(serviceName);
    }
  }

  /**
   * 生成服务描述 - 使用DeepSeek
   * @param {string} serviceName - 服务名称
   * @returns {Promise<string>} 生成的描述
   */
  async generateServiceDescription(serviceName) {
    try {
      console.log(`🤖 使用DeepSeek生成服务描述: ${serviceName}`);
      
      const prompt = `请为中医理疗服务"${serviceName}"生成一段专业的服务描述。要求：
1. 突出服务的专业性和效果
2. 包含适用人群和主要功效
3. 语言简洁明了，约50-80字
4. 体现中医理疗的特色和优势
5. 不要包含价格信息

请直接返回描述内容，不要其他解释。`;

      const response = await fetch(this.deepseekBaseURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.deepseekApiKey}`
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [
            {
              role: 'user',
              content: prompt
            }
          ],
          max_tokens: 200,
          temperature: 0.7
        })
      });

      if (response.ok) {
        const result = await response.json();
        const description = result.choices[0].message.content.trim();
        console.log(`✅ DeepSeek描述生成成功: ${description}`);
        return description;
      } else {
        throw new Error(`DeepSeek API请求失败: ${response.status}`);
      }
    } catch (error) {
      console.warn('⚠️ DeepSeek描述生成失败，使用备用描述:', error.message);
      return this.getFallbackDescription(serviceName);
    }
  }

  /**
   * 获取备用图片
   * @param {string} serviceName - 服务名称
   * @returns {string} 备用图片URL
   */
  getFallbackImage(serviceName) {
    const fallbackImages = {
      '按摩': 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400',
      '推拿': 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400',
      '足疗': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
      '艾灸': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400',
      '拔罐': 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400',
      '刮痧': 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=400',
      '针灸': 'https://images.unsplash.com/photo-1559757175-0eb30cd8c063?w=400',
      '理疗': 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400',
      '康复': 'https://images.unsplash.com/photo-1559757148-5c350d0d3c56?w=400',
      '养生': 'https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=400'
    };

    // 根据服务名称匹配备用图片
    for (const [key, imageUrl] of Object.entries(fallbackImages)) {
      if (serviceName.includes(key)) {
        console.log(`📷 使用备用图片: ${key}`);
        return imageUrl;
      }
    }

    // 默认备用图片
    console.log('📷 使用默认备用图片');
    return 'https://images.unsplash.com/photo-1544161515-4ab6ce6db874?w=400';
  }

  /**
   * 获取备用描述
   * @param {string} serviceName - 服务名称
   * @returns {string} 备用描述
   */
  getFallbackDescription(serviceName) {
    const fallbackDescriptions = {
      '按摩': '专业中医按摩服务，运用传统手法疏通经络，缓解肌肉疲劳，促进血液循环，适合久坐办公人群和运动后恢复。',
      '推拿': '传统中医推拿理疗，通过专业手法调理气血，疏通经络，改善身体机能，有效缓解颈肩腰腿痛等不适症状。',
      '足疗': '专业足部按摩护理，刺激足底反射区，促进全身血液循环，缓解疲劳，改善睡眠质量，适合各年龄段人群。',
      '艾灸': '传统中医艾灸疗法，温经散寒，调理气血，增强免疫力，适用于体质虚寒、慢性疲劳等亚健康状态调理。',
      '拔罐': '传统中医拔罐疗法，通过负压作用疏通经络，祛湿排毒，缓解肌肉酸痛，改善局部血液循环。',
      '刮痧': '传统中医刮痧疗法，疏通经络，活血化瘀，排毒养颜，有效缓解颈肩不适，改善面部气色。',
      '针灸': '传统中医针灸治疗，通过针刺特定穴位调节脏腑功能，疏通经络，适用于多种慢性疾病的调理治疗。',
      '理疗': '现代物理理疗结合传统中医理念，运用专业设备和手法，改善身体机能，促进康复，适合术后恢复。',
      '康复': '专业康复理疗服务，针对运动损伤、术后恢复等情况，制定个性化康复方案，帮助恢复身体功能。',
      '养生': '中医养生保健服务，根据个人体质特点，提供专业的养生指导和调理方案，预防疾病，延缓衰老。'
    };

    // 根据服务名称匹配备用描述
    for (const [key, description] of Object.entries(fallbackDescriptions)) {
      if (serviceName.includes(key)) {
        console.log(`📝 使用备用描述: ${key}`);
        return description;
      }
    }

    // 默认备用描述
    console.log('📝 使用默认备用描述');
    return `专业的${serviceName}服务，采用传统中医理疗方法，结合现代技术，为您提供安全有效的健康调理方案，改善身体状况，提升生活质量。`;
  }
}

// 创建全局实例
const aiService = new AIService();

// 导出实例
export default aiService;
