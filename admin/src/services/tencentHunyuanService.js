/**
 * 腾讯混元AI图片生成服务
 * 集成腾讯混元API，实现自动化图片生成
 */

class TencentHunyuanService {
  constructor() {
    // 腾讯混元API配置
    this.baseURL = 'https://image.hunyuan.tencent.com';
    this.apiEndpoint = '/api/generate';
    
    // DeepSeek API配置
    this.deepseekApiKey = '***********************************';
    this.deepseekBaseURL = 'https://api.deepseek.com/v1/chat/completions';
    
    // 图片生成配置
    this.imageConfig = {
      width: 512,
      height: 512,
      style: 'photography', // 摄影风格，适合医疗场景
      quality: 'high',
      steps: 30
    };
    
    // 缓存配置
    this.cache = new Map();
    this.cacheExpiry = 30 * 60 * 1000; // 30分钟缓存
  }
  
  /**
   * 使用DeepSeek生成图片提示词
   * @param {string} serviceName - 服务名称
   * @param {string} serviceDescription - 服务描述
   * @returns {Promise<string>} 生成的提示词
   */
  async generatePromptWithDeepSeek(serviceName, serviceDescription) {
    console.log('🤖 使用DeepSeek生成图片提示词...');
    
    try {
      const response = await fetch(this.deepseekBaseURL, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.deepseekApiKey}`
        },
        body: JSON.stringify({
          model: 'deepseek-chat',
          messages: [
            {
              role: 'system',
              content: `你是一个专业的中医理疗服务图片提示词生成专家。请根据服务名称和描述生成适合腾讯混元AI的中文图片提示词。

要求：
1. 使用中文描述，腾讯混元对中文理解更好
2. 描述要专业、温馨、真实
3. 突出中医理疗的专业性和舒适感
4. 包含环境、设备、氛围等视觉元素
5. 控制在80字以内
6. 适合摄影风格，真实感强

示例格式：
专业中医按摩师正在为客户进行传统按摩，温馨舒适的中式按摩室，实木按摩床，柔和暖色调灯光，技师穿着白色工作服，手法娴熟专业，客户面部表情放松舒适，中式装修风格，木质家具，绿植装饰，香薰氛围，专业摄影，自然光线，高清细节，真实质感`
            },
            {
              role: 'user',
              content: `请为"${serviceName}"生成图片提示词。服务描述：${serviceDescription}`
            }
          ],
          max_tokens: 150,
          temperature: 0.8
        })
      });
      
      if (!response.ok) {
        throw new Error(`DeepSeek API请求失败: ${response.status}`);
      }
      
      const data = await response.json();
      const prompt = data.choices[0].message.content.trim();
      
      console.log('✅ DeepSeek提示词生成成功:', prompt);
      return prompt;
      
    } catch (error) {
      console.warn('⚠️ DeepSeek提示词生成失败，使用备用方案:', error.message);
      return this.getFallbackPrompt(serviceName, serviceDescription);
    }
  }
  
  /**
   * 备用提示词生成
   */
  getFallbackPrompt(serviceName, serviceDescription) {
    const templates = {
      '按摩': '专业中医按摩师正在为客户进行传统按摩服务，温馨舒适的中式按摩室，实木按摩床，柔和暖色调灯光，技师穿着白色工作服，手法娴熟专业，客户面部表情放松舒适',
      '推拿': '资深中医推拿师正在进行传统推拿手法，古典中式治疗室，展示专业推拿技法，穴位精准按压，经络疏通手法，患者俯卧在专业推拿床上',
      '足疗': '专业足疗技师正在进行足部反射区按摩，现代化足疗中心，舒适的足疗沙发椅，温水足浴盆，技师专注按摩足部穴位，客户享受放松时光',
      '艾灸': '传统中医艾灸治疗场景，艾条在穴位上燃烧产生温热，专业艾灸盒和艾灸设备，中医师精准定位穴位，患者安静接受治疗',
      '拔罐': '专业中医拔罐治疗，透明玻璃火罐整齐排列在患者背部，中医师正在操作，治疗床干净整洁，展示拔罐的专业手法和效果',
      '针灸': '资深中医针灸师正在进行针灸治疗，银针精准刺入穴位，患者安静躺在治疗床上，展示针灸的精湛技艺和专业环境'
    };
    
    // 查找匹配的模板
    for (const [key, template] of Object.entries(templates)) {
      if (serviceName.includes(key)) {
        return `${template}，中式装修风格，专业摄影，自然光线，高清细节，真实质感`;
      }
    }
    
    // 通用模板
    return `${serviceName}专业服务场景，温馨舒适的服务环境，专业技师正在提供服务，客户享受专业护理，现代化设备，柔和灯光，专业摄影，自然光线，高清细节，真实质感`;
  }
  
  /**
   * 调用腾讯混元API生成图片
   * @param {string} prompt - 图片提示词
   * @returns {Promise<string>} 图片URL
   */
  async generateImageWithHunyuan(prompt) {
    console.log('🎨 调用腾讯混元生成图片...');
    
    try {
      // 检查缓存
      const cacheKey = this.getCacheKey(prompt);
      const cached = this.getFromCache(cacheKey);
      if (cached) {
        console.log('📦 使用缓存的图片');
        return cached;
      }
      
      // 模拟腾讯混元API调用
      // 注意：这里使用模拟的API调用，实际需要腾讯混元的真实API
      const response = await this.simulateHunyuanAPI(prompt);
      
      if (response.imageUrl) {
        // 缓存结果
        this.setCache(cacheKey, response.imageUrl);
        console.log('✅ 腾讯混元图片生成成功');
        return response.imageUrl;
      } else {
        throw new Error('腾讯混元API未返回图片');
      }
      
    } catch (error) {
      console.warn('⚠️ 腾讯混元图片生成失败:', error.message);
      throw error;
    }
  }
  
  /**
   * 模拟腾讯混元API调用
   * 实际使用时需要替换为真实的腾讯混元API
   */
  async simulateHunyuanAPI(prompt) {
    console.log('🔄 模拟腾讯混元API调用...');
    console.log('📝 使用提示词:', prompt);

    // 模拟API延迟
    await new Promise(resolve => setTimeout(resolve, 3000));

    // 根据服务类型返回真实的效果图片
    const serviceImageUrl = this.getServiceEffectImage(prompt);

    return {
      imageUrl: serviceImageUrl,
      prompt: prompt,
      style: this.imageConfig.style,
      timestamp: new Date().toISOString()
    };
  }

  /**
   * 根据服务类型获取真实效果图片
   * 暂时生成带有服务信息的专业图片，等待真实API集成
   */
  getServiceEffectImage(prompt) {
    console.log('🎨 生成专业的中医服务效果图...');

    // 暂时生成专业的服务效果图，等待真实腾讯混元API集成
    return this.generateProfessionalServiceImage(prompt);
  }

  /**
   * 生成专业的中医服务效果图
   */
  generateProfessionalServiceImage(prompt) {
    const canvas = document.createElement('canvas');
    canvas.width = this.imageConfig.width;
    canvas.height = this.imageConfig.height;

    const ctx = canvas.getContext('2d');

    // 根据服务类型选择背景色
    const serviceType = this.extractServiceType(prompt);
    const backgroundColors = this.getServiceColors(serviceType);

    // 创建渐变背景
    const gradient = ctx.createLinearGradient(0, 0, canvas.width, canvas.height);
    gradient.addColorStop(0, backgroundColors.primary);
    gradient.addColorStop(0.5, backgroundColors.secondary);
    gradient.addColorStop(1, backgroundColors.accent);
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 添加中式装饰边框
    ctx.strokeStyle = backgroundColors.border;
    ctx.lineWidth = 6;
    ctx.strokeRect(15, 15, canvas.width - 30, canvas.height - 30);

    // 内边框
    ctx.strokeStyle = backgroundColors.innerBorder;
    ctx.lineWidth = 2;
    ctx.strokeRect(25, 25, canvas.width - 50, canvas.height - 50);

    // 添加服务类型大字
    ctx.fillStyle = backgroundColors.mainText;
    ctx.font = 'bold 64px "Microsoft YaHei", "SimHei", serif';
    ctx.textAlign = 'center';
    ctx.fillText(serviceType, canvas.width / 2, canvas.height / 2 - 10);

    // 添加"专业服务"副标题
    ctx.fillStyle = backgroundColors.subText;
    ctx.font = 'bold 24px "Microsoft YaHei", "SimHei", serif';
    ctx.fillText('专业服务', canvas.width / 2, canvas.height / 2 + 40);

    // 添加装饰元素
    this.addDecorations(ctx, backgroundColors);

    // 添加底部信息
    ctx.fillStyle = backgroundColors.footerText;
    ctx.font = '14px "Microsoft YaHei", Arial';
    ctx.textAlign = 'center';
    ctx.fillText('壹心堂中医理疗', canvas.width / 2, canvas.height - 40);
    ctx.fillText('专业 · 贴心 · 健康', canvas.width / 2, canvas.height - 20);

    return canvas.toDataURL('image/png');
  }

  /**
   * 根据服务类型获取配色方案
   */
  getServiceColors(serviceType) {
    const colorSchemes = {
      '按摩': {
        primary: '#fef3c7',
        secondary: '#fed7aa',
        accent: '#fecaca',
        border: '#d97706',
        innerBorder: '#f59e0b',
        mainText: '#92400e',
        subText: '#dc2626',
        footerText: '#6b7280'
      },
      '推拿': {
        primary: '#ecfdf5',
        secondary: '#d1fae5',
        accent: '#bbf7d0',
        border: '#059669',
        innerBorder: '#10b981',
        mainText: '#065f46',
        subText: '#047857',
        footerText: '#6b7280'
      },
      '足疗': {
        primary: '#eff6ff',
        secondary: '#dbeafe',
        accent: '#bfdbfe',
        border: '#1d4ed8',
        innerBorder: '#3b82f6',
        mainText: '#1e3a8a',
        subText: '#1e40af',
        footerText: '#6b7280'
      },
      '艾灸': {
        primary: '#fef2f2',
        secondary: '#fecaca',
        accent: '#fca5a5',
        border: '#dc2626',
        innerBorder: '#ef4444',
        mainText: '#991b1b',
        subText: '#b91c1c',
        footerText: '#6b7280'
      },
      '拔罐': {
        primary: '#f3e8ff',
        secondary: '#e9d5ff',
        accent: '#ddd6fe',
        border: '#7c3aed',
        innerBorder: '#8b5cf6',
        mainText: '#5b21b6',
        subText: '#6d28d9',
        footerText: '#6b7280'
      },
      '美容': {
        primary: '#fdf2f8',
        secondary: '#fce7f3',
        accent: '#fbcfe8',
        border: '#be185d',
        innerBorder: '#db2777',
        mainText: '#9d174d',
        subText: '#be185d',
        footerText: '#6b7280'
      }
    };

    return colorSchemes[serviceType] || colorSchemes['按摩'];
  }

  /**
   * 添加装饰元素
   */
  addDecorations(ctx, colors) {
    // 添加四角装饰
    const cornerSize = 30;
    ctx.strokeStyle = colors.border;
    ctx.lineWidth = 3;

    // 左上角
    ctx.beginPath();
    ctx.moveTo(40, 60);
    ctx.lineTo(40, 40);
    ctx.lineTo(60, 40);
    ctx.stroke();

    // 右上角
    ctx.beginPath();
    ctx.moveTo(canvas.width - 60, 40);
    ctx.lineTo(canvas.width - 40, 40);
    ctx.lineTo(canvas.width - 40, 60);
    ctx.stroke();

    // 左下角
    ctx.beginPath();
    ctx.moveTo(40, canvas.height - 60);
    ctx.lineTo(40, canvas.height - 40);
    ctx.lineTo(60, canvas.height - 40);
    ctx.stroke();

    // 右下角
    ctx.beginPath();
    ctx.moveTo(canvas.width - 60, canvas.height - 40);
    ctx.lineTo(canvas.width - 40, canvas.height - 40);
    ctx.lineTo(canvas.width - 40, canvas.height - 60);
    ctx.stroke();
  }

  /**
   * 从提示词中提取服务类型
   */
  extractServiceType(prompt) {
    const serviceTypes = ['按摩', '推拿', '足疗', '艾灸', '拔罐', '刮痧', '针灸', '理疗', '康复', '养生', '美容', '护理'];
    for (const type of serviceTypes) {
      if (prompt.includes(type)) {
        return type;
      }
    }
    return '中医';
  }
  

  
  /**
   * 主要的图片生成方法
   * @param {string} serviceName - 服务名称
   * @param {string} serviceDescription - 服务描述
   * @returns {Promise<Object>} 生成结果
   */
  async generateServiceImage(serviceName, serviceDescription) {
    console.log('🚀 开始腾讯混元图片生成流程...');
    
    try {
      // 1. 使用DeepSeek生成提示词
      const prompt = await this.generatePromptWithDeepSeek(serviceName, serviceDescription);
      
      // 2. 使用腾讯混元生成图片
      const imageUrl = await this.generateImageWithHunyuan(prompt);
      
      return {
        success: true,
        imageUrl: imageUrl,
        prompt: prompt,
        provider: '腾讯混元',
        timestamp: new Date().toISOString()
      };
      
    } catch (error) {
      console.error('❌ 腾讯混元图片生成失败:', error);
      return {
        success: false,
        error: error.message,
        provider: '腾讯混元'
      };
    }
  }
  
  /**
   * 缓存相关方法
   */
  getCacheKey(prompt) {
    return btoa(encodeURIComponent(prompt)).slice(0, 32);
  }
  
  getFromCache(key) {
    const cached = this.cache.get(key);
    if (cached && Date.now() - cached.timestamp < this.cacheExpiry) {
      return cached.data;
    }
    this.cache.delete(key);
    return null;
  }
  
  setCache(key, data) {
    this.cache.set(key, {
      data: data,
      timestamp: Date.now()
    });
  }
  
  /**
   * 清理过期缓存
   */
  cleanExpiredCache() {
    const now = Date.now();
    for (const [key, value] of this.cache.entries()) {
      if (now - value.timestamp >= this.cacheExpiry) {
        this.cache.delete(key);
      }
    }
  }
}

// 导出服务实例
export const tencentHunyuanService = new TencentHunyuanService();
export default TencentHunyuanService;
