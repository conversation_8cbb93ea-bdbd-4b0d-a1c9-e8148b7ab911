import { createPinia } from 'pinia';
import { createApp } from 'vue';

import Antd from 'ant-design-vue';
import 'ant-design-vue/dist/antd.css';

import App from './App.vue';
import router from './router';
import purpleTheme from './config/theme.js';

import './assets/styles/main.scss';

// 🎯 导入壹心堂CSS标准库（基于历史成功案例）
import './styles/standards.css';

// 🎯 导入响应式rem系统 - 实现真正的比例缩放
import './utils/responsive.js';

// 轮廓调试工具 - 仅开发环境加载
if (process.env.NODE_ENV === 'development') {
  // 确保DOM加载完成后再初始化调试工具
  document.addEventListener('DOMContentLoaded', () => {
    import('./utils/outlineDebugger.js').then((module) => {
      console.log('🎯 轮廓调试工具已加载 (开发环境)');
      console.log('📋 使用快捷键 Ctrl+Shift+D 启用调试或输入 debug.help() 查看帮助');

      // 立即显示帮助信息
      if (window.debug && window.debug.help) {
        setTimeout(() => {
          console.log('🎯 轮廓调试工具已就绪！输入 debug.help() 查看完整帮助');
        }, 1000);
      }
    }).catch(error => {
      console.warn('⚠️ 轮廓调试工具加载失败:', error);
    });
  });
}

// 开发环境下的自动化测试系统 (暂时禁用以提升用户体验)
// eslint-disable-next-line no-constant-condition
if (false) {
  // 导入实时修复工具
  import('./utils/realTimeFixer.js').then(({ realTimeFixer }) => {
    console.log('🔧 实时修复系统已加载')
  }).catch(error => {
    console.warn('⚠️ 实时修复工具加载失败:', error)
  })

  // 导入基础自动测试工具
  import('./utils/autoTest.js').then(({ autoTester }) => {
    console.log('🔧 基础自动化测试工具已加载')
  }).catch(error => {
    console.warn('⚠️ 基础测试工具加载失败:', error)
  })

  // 导入完善测试工具
  import('./utils/comprehensiveTest.js').then(({ comprehensiveSystemTester }) => {
    console.log('🏆 IT全栈大奖级别完善测试工具已加载')
  }).catch(error => {
    console.warn('⚠️ 完善测试工具加载失败:', error)
  })

  // 导入自动化测试调度器
  import('./utils/autoTestScheduler.js').then(({ autoTestScheduler }) => {
    console.log('🤖 自动化测试调度器已加载')

    // 延迟10秒后启动完全自动化测试，给所有系统充分时间初始化
    setTimeout(() => {
      console.log('🚀 启动完全自动化测试调度器...')
      autoTestScheduler.startAutoTesting()
    }, 10000)
  }).catch(error => {
    console.warn('⚠️ 自动化测试调度器加载失败:', error)
  })

  // 导入完美主义测试器
  import('./utils/perfectionist.js').then(({ perfectionistTester }) => {
    console.log('💎 完美主义测试器已加载')

    // 延迟15秒后启动完美主义测试，确保所有系统稳定运行
    setTimeout(() => {
      console.log('🏆 启动完美主义级别测试...')
      perfectionistTester.startPerfectionistTesting()
    }, 15000)
  }).catch(error => {
    console.warn('⚠️ 完美主义测试器加载失败:', error)
  })
}

// 简化的开发环境日志
if (process.env.NODE_ENV === 'development') {
  console.log('🚀 壹心堂管理系统 - 开发模式')
  console.log('📍 前端地址: http://localhost:3000/')
  console.log('📍 后端API: http://localhost:8000/')
}


// 全局错误捕获
window.addEventListener('error', (event) => {
  // 过滤掉null错误和无意义的错误
  if (event.error === null && event.lineno === 0 && event.colno === 0) {
    console.warn('⚠️ 捕获到null错误，可能是浏览器兼容性问题，已忽略');
    return;
  }

  console.error('🚨 全局JavaScript错误:', event.error);
  console.error('错误文件:', event.filename);
  console.error('错误行号:', event.lineno);
  console.error('错误列号:', event.colno);
  console.error('错误堆栈:', event.error?.stack);
});

// 未处理的Promise错误
window.addEventListener('unhandledrejection', (event) => {
  console.error('🚨 未处理的Promise错误:', event.reason);
  event.preventDefault(); // 防止错误显示在控制台
});

// Vue错误处理
const app = createApp(App);

app.config.errorHandler = (err, vm, info) => {
  console.error('🚨 Vue错误:', err);
  console.error('组件信息:', info);
  console.error('错误堆栈:', err.stack);
};



app.use(createPinia())
app.use(router)
app.use(Antd)

// 应用紫色主题
app.provide('theme', purpleTheme)

app.mount('#app');
