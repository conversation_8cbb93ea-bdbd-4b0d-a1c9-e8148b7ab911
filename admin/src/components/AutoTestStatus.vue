<template>
  <div class="auto-test-status" :class="{ 'minimized': minimized }">
    <div class="status-header">
      <div class="status-title">
        <RobotOutlined :spin="isRunning" />
        <span>自动化测试</span>
        <a-badge :count="failedCount" :offset="[10, 0]">
          <span class="status-indicator" :class="statusClass"></span>
        </a-badge>
      </div>
      <div class="status-controls">
        <a-button v-if="!isRunning" @click.stop="startTesting" size="small" type="primary">
          <PlayCircleOutlined />
        </a-button>
        <a-button v-else @click.stop="stopTesting" size="small" danger>
          <PauseCircleOutlined />
        </a-button>
        <!-- 移除最小化按钮，保持默认显示全部 -->
      </div>
    </div>

    <div class="status-content">
      <div class="test-stats">
        <div class="stat-item">
          <div class="stat-number">{{ totalTests }}</div>
          <div class="stat-label">总测试</div>
        </div>
        <div class="stat-item success">
          <div class="stat-number">{{ successCount }}</div>
          <div class="stat-label">成功</div>
        </div>
        <div class="stat-item failed">
          <div class="stat-number">{{ failedCount }}</div>
          <div class="stat-label">失败</div>
        </div>
        <div class="stat-item">
          <div class="stat-number">{{ successRate }}%</div>
          <div class="stat-label">成功率</div>
        </div>
      </div>

      <div v-if="currentTest" class="current-test">
        <div class="current-test-title">
          <LoadingOutlined spin />
          正在执行: {{ currentTest }}
        </div>
        <a-progress :percent="testProgress" size="small" />
      </div>

      <div class="recent-tests">
        <div class="recent-title">最近测试结果</div>
        <div class="test-list">
          <div v-for="test in recentTests" :key="test.id" 
               class="test-item" :class="test.status">
            <div class="test-icon">
              <CheckCircleOutlined v-if="test.status === 'success'" />
              <CloseCircleOutlined v-else-if="test.status === 'failed'" />
              <LoadingOutlined v-else spin />
            </div>
            <div class="test-info">
              <div class="test-name">{{ test.name }}</div>
              <div class="test-time">{{ formatTime(test.timestamp) }}</div>
            </div>
            <div class="test-duration" v-if="test.duration">
              {{ test.duration }}ms
            </div>
          </div>
        </div>
      </div>

      <div class="test-actions">
        <a-button @click="runSingleTest" size="small" style="margin-right: 8px;">
          <ThunderboltOutlined />
          快速测试
        </a-button>
        <a-button @click="runFullTest" size="small" style="margin-right: 8px;">
          <RocketOutlined />
          完整测试
        </a-button>
        <a-button @click="viewReport" size="small">
          <FileTextOutlined />
          查看报告
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  RobotOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  UpOutlined,
  DownOutlined,
  LoadingOutlined,
  CheckCircleOutlined,
  CloseCircleOutlined,
  ThunderboltOutlined,
  RocketOutlined,
  FileTextOutlined
} from '@ant-design/icons-vue'

// 响应式数据 - 默认显示全部，不允许最小化
const minimized = ref(false)
const isRunning = ref(false)
const currentTest = ref('')
const testProgress = ref(0)
const testResults = ref([])
const updateInterval = ref(null)

// 计算属性
const totalTests = computed(() => testResults.value.length)
const successCount = computed(() => testResults.value.filter(t => t.status === 'success').length)
const failedCount = computed(() => testResults.value.filter(t => t.status === 'failed').length)
const successRate = computed(() => {
  if (totalTests.value === 0) return 0
  return Math.round((successCount.value / totalTests.value) * 100)
})

const statusClass = computed(() => {
  if (!isRunning.value) return 'stopped'
  if (failedCount.value > 0) return 'warning'
  return 'running'
})

const recentTests = computed(() => {
  return testResults.value
    .slice(-5)
    .reverse()
    .map((test, index) => ({
      ...test,
      id: index
    }))
})

// 禁用切换最小化功能
const toggleMinimize = () => {
  // 不允许点击切换，保持默认显示全部
  return false
}

// 启动测试
const startTesting = async () => {
  if (window.autoTestScheduler) {
    await window.autoTestScheduler.startAutoTesting()
    isRunning.value = true
    message.success('自动化测试已启动')
  } else {
    message.error('自动化测试调度器未加载')
  }
}

// 停止测试
const stopTesting = () => {
  if (window.autoTestScheduler) {
    window.autoTestScheduler.stopAutoTesting()
    isRunning.value = false
    currentTest.value = ''
    testProgress.value = 0
    message.info('自动化测试已停止')
  }
}

// 运行单个测试
const runSingleTest = async () => {
  if (window.comprehensiveSystemTester) {
    try {
      currentTest.value = '快速功能测试'
      testProgress.value = 0
      
      // 模拟测试进度
      const progressInterval = setInterval(() => {
        testProgress.value += 10
        if (testProgress.value >= 100) {
          clearInterval(progressInterval)
          currentTest.value = ''
          testProgress.value = 0
        }
      }, 200)
      
      await window.comprehensiveSystemTester.testMenuSystem()
      message.success('快速测试完成')
    } catch (error) {
      message.error('快速测试失败: ' + error.message)
    }
  }
}

// 运行完整测试
const runFullTest = async () => {
  if (window.comprehensiveSystemTester) {
    try {
      currentTest.value = '完整系统测试'
      testProgress.value = 0
      
      // 模拟测试进度
      const progressInterval = setInterval(() => {
        testProgress.value += 5
        if (testProgress.value >= 100) {
          clearInterval(progressInterval)
          currentTest.value = ''
          testProgress.value = 0
        }
      }, 500)
      
      await window.comprehensiveSystemTester.runComprehensiveTests()
      message.success('完整测试完成')
    } catch (error) {
      message.error('完整测试失败: ' + error.message)
    }
  }
}

// 查看报告
const viewReport = () => {
  if (window.autoTestScheduler) {
    const report = window.autoTestScheduler.getTestReport()
    console.log('📊 测试报告:', report)
    message.info('测试报告已输出到控制台')
  }
}

// 更新测试状态
const updateTestStatus = () => {
  if (window.autoTestScheduler) {
    const scheduler = window.autoTestScheduler
    isRunning.value = scheduler.isRunning
    
    if (scheduler.currentTest) {
      currentTest.value = scheduler.currentTest.name
    } else {
      currentTest.value = ''
    }
    
    // 更新测试结果
    if (scheduler.testResults && scheduler.testResults.length > testResults.value.length) {
      testResults.value = [...scheduler.testResults]
    }
  }
}

// 格式化时间
const formatTime = (timestamp) => {
  if (!timestamp) return ''
  return new Date(timestamp).toLocaleTimeString()
}

// 组件挂载
onMounted(() => {
  // 定期更新状态
  updateInterval.value = setInterval(updateTestStatus, 1000)
  
  // 初始状态检查
  setTimeout(() => {
    if (window.autoTestScheduler && window.autoTestScheduler.isRunning) {
      isRunning.value = true
    }
  }, 2000)
})

// 组件卸载
onUnmounted(() => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
  }
})
</script>

<style scoped>
.auto-test-status {
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 1000;
  width: 320px;
  border-radius: 8px;
  background: white;
  box-shadow: 0 4px 16px rgb(0 0 0 / 10%);
  transition: all 0.3s ease;
  pointer-events: auto;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

.auto-test-status.minimized {
  width: 200px;
}

.status-header {
  display: flex;
  padding: 12px 16px;
  border-radius: 8px 8px 0 0;
  color: white;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  justify-content: space-between;
  align-items: center;
  cursor: default;
  user-select: none;
  -webkit-user-drag: none;
  -khtml-user-drag: none;
  -moz-user-drag: none;
  -o-user-drag: none;
  user-drag: none;
}

.status-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.status-indicator {
  display: inline-block;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  margin-left: 8px;
}

.status-indicator.running {
  background: #52c41a;
  animation: pulse 2s infinite;
}

.status-indicator.warning {
  background: #faad14;
  animation: pulse 2s infinite;
}

.status-indicator.stopped {
  background: #d9d9d9;
}

@keyframes pulse {
  0% { opacity: 1; }
  50% { opacity: 0.5; }
  100% { opacity: 1; }
}

.status-controls {
  display: flex;
  gap: 4px;
}

.status-content {
  padding: 16px;
}

.test-stats {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 12px;
  margin-bottom: 16px;
}

.stat-item {
  padding: 8px;
  border-radius: 4px;
  text-align: center;
  background: #f5f5f5;
}

.stat-item.success {
  color: #52c41a;
  background: #f6ffed;
}

.stat-item.failed {
  color: #ff4d4f;
  background: #fff2f0;
}

.stat-number {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #666;
}

.current-test {
  padding: 12px;
  border: 1px solid #91d5ff;
  border-radius: 4px;
  background: #e6f7ff;
  margin-bottom: 16px;
}

.current-test-title {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-weight: 600;
  color: #1890ff;
}

.recent-tests {
  margin-bottom: 16px;
}

.recent-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.test-list {
  max-height: 150px;
  overflow-y: auto;
}

.test-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 4px;
  margin-bottom: 4px;
  background: #fafafa;
}

.test-item.success {
  background: #f6ffed;
}

.test-item.failed {
  background: #fff2f0;
}

.test-item.running {
  background: #e6f7ff;
}

.test-icon {
  margin-right: 8px;
  font-size: 14px;
}

.test-item.success .test-icon {
  color: #52c41a;
}

.test-item.failed .test-icon {
  color: #ff4d4f;
}

.test-item.running .test-icon {
  color: #1890ff;
}

.test-info {
  flex: 1;
}

.test-name {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 2px;
}

.test-time {
  font-size: 11px;
  color: #999;
}

.test-duration {
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 11px;
  color: #666;
  background: #f0f0f0;
}

.test-actions {
  display: flex;
  justify-content: space-between;
}
</style>
