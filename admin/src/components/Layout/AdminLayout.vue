<template>
  <a-layout class="admin-layout">
    <!-- 波浪背景容器 -->
    <div class="wave-container">
      <svg class="waves" xmlns="http://www.w3.org/2000/svg" viewBox="0 24 150 28" preserveAspectRatio="none" shape-rendering="auto">
        <defs>
          <path id="gentle-wave" d="m-160 44c30 0 58-18 88-18s 58 18 88 18 58-18 88-18 58 18 88 18 v44h-352z" />
        </defs>
        <g class="parallax">
          <use href="#gentle-wave" x="48" y="0" fill="rgba(255,255,255,0.7)" />
          <use href="#gentle-wave" x="48" y="3" fill="rgba(255,255,255,0.5)" />
          <use href="#gentle-wave" x="48" y="5" fill="rgba(255,255,255,0.3)" />
          <use href="#gentle-wave" x="48" y="7" fill="#fff" />
        </g>
      </svg>
    </div>

    <!-- 侧边栏 -->
    <aside>
      <a-layout-sider
        :collapsed="false"
        :trigger="null"
        theme="dark"
        class="admin-sider sidebar"
      >
      <!-- Logo区域 - 移动logo到此处，去掉汉字标题 -->
      <div class="logo">
        <img src="@/assets/images/logo.png" alt="壹心堂" class="header-logo" />
      </div>

      <!-- 导航菜单 -->
      <a-menu
        v-model:selectedKeys="selectedKeys"
        v-model:openKeys="openKeys"
        mode="inline"
        theme="dark"
        :inline-collapsed="collapsed"
        @click="handleMenuClick"
      >
        <a-menu-item key="dashboard">
          <template #icon>
            <DashboardOutlined />
          </template>
          <span>仪表盘</span>
        </a-menu-item>

        <a-menu-item key="appointment-list">
          <template #icon>
            <CalendarOutlined />
          </template>
          <span>预约管理</span>
        </a-menu-item>

        <a-menu-item key="customers">
          <template #icon>
            <UserOutlined />
          </template>
          <span>客户管理</span>
        </a-menu-item>

        <a-menu-item key="therapists">
          <template #icon>
            <TeamOutlined />
          </template>
          <span>技师管理</span>
        </a-menu-item>

        <a-menu-item key="services">
          <template #icon>
            <AppstoreOutlined />
          </template>
          <span>服务管理</span>
        </a-menu-item>

        <a-menu-item key="finance-overview">
          <template #icon>
            <DollarOutlined />
          </template>
          <span>财务概览</span>
        </a-menu-item>

        <a-menu-item key="system-settings">
          <template #icon>
            <SettingOutlined />
          </template>
          <span>系统设置</span>
        </a-menu-item>

        <a-menu-item key="health-tips">
          <template #icon>
            <HeartOutlined />
          </template>
          <span>健康贴士</span>
        </a-menu-item>

        <a-menu-item key="data-statistics">
          <template #icon>
            <BarChartOutlined />
          </template>
          <span>数据统计</span>
        </a-menu-item>

        <a-menu-item key="logout">
          <template #icon>
            <LogoutOutlined />
          </template>
          <span>退出登录</span>
        </a-menu-item>
      </a-menu>



      <!-- 侧边栏底部版权信息 - 移除logo，仅保留版权信息 -->
      <div class="sidebar-footer">
        <div class="footer-info">
          <div class="copyright">
            <div class="copyright-line1">© 2025 壹心堂 Yixintang</div>
            <div class="copyright-line2">版本: {{ backendVersion }}</div>
          </div>
        </div>
      </div>
      </a-layout-sider>
    </aside>

    <a-layout>
      <!-- 主内容区域 -->
      <main>
        <a-layout-content class="admin-content main-content">
        <div :class="['content-wrapper', { scrollable: needsScroll }]">
          <router-view />
        </div>
        </a-layout-content>
      </main>
    </a-layout>
  </a-layout>

  <!-- 🚫 移除测试控制台浮动按钮 - 影响界面美观 -->
  <!-- <TestConsole v-if="isDevelopment" /> -->
</template>

<script setup>
import TestConsole from '@/components/TestConsole.vue'
import { useUserStore } from '@/store'
import {
    AppstoreOutlined,
    BarChartOutlined,
    CalendarOutlined,
    DashboardOutlined,
    DollarOutlined,
    HeartOutlined,
    LogoutOutlined,
    SettingOutlined,
    TeamOutlined,
    UserOutlined
} from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { computed, onMounted, ref, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()
const userStore = useUserStore()

// 侧边栏折叠状态
const collapsed = ref(false)

// 后端版本号
const backendVersion = ref('v1.0.0') // 默认版本号

// 获取后端版本号
const fetchBackendVersion = async () => {
  try {
    console.log('🔍 尝试获取后端版本...');
    const response = await fetch('/api/version/', {
      timeout: 3000 // 3秒超时
    });

    if (response.ok) {
      const data = await response.json();
      backendVersion.value = data.version || 'v1.0.0';
      console.log('✅ 后端版本获取成功:', backendVersion.value);
    } else {
      console.warn('⚠️ 后端版本接口响应异常，使用默认版本');
      backendVersion.value = 'v1.0.0';
    }
  } catch (error) {
    console.warn('⚠️ 后端服务未启动或连接失败，使用默认版本:', error.message);
    backendVersion.value = 'v1.0.0 (离线模式)';
    // 不显示错误消息，因为这是正常的开发情况
  }
}



// 开发环境检测
const isDevelopment = process.env.NODE_ENV === 'development'

// 响应式数据
const selectedKeys = ref([])
const openKeys = ref([])
const version = ref('1.0.0')

// 🎯 移除固定宽度设置，让CSS样式生效，PostCSS会自动转换为rem
// 🎯 完全移除Vue的width控制，让CSS样式和PostCSS完全接管
// const responsiveWidth = computed(() => {
//   return undefined
// })

// 判断当前页面是否需要滚动
const needsScroll = computed(() => {
  // 仪表盘页面不需要滚动，其他页面需要滚动
  const noScrollPages = ['dashboard']
  return !noScrollPages.includes(route.name)
})

// 更新菜单选中状态
const updateMenuSelection = (path) => {
  const pathMap = {
    '/dashboard': ['dashboard'],
    '/appointments': ['appointment-list'],
    '/customers': ['customers'],
    '/therapists': ['therapists'],
    '/services': ['services'],
    '/finance': ['finance-overview'],
    '/finance/records': ['finance-records'],
    '/finance/reports': ['finance-reports'],
    '/system': ['system-settings'],  // 修复：/system 路径对应 system-settings 菜单项
    '/system/settings': ['system-settings'],
    '/system/logs': ['system-logs']
  }

  selectedKeys.value = pathMap[path] || ['dashboard']

  // 设置展开的子菜单
  if (path.startsWith('/appointment')) {
    openKeys.value = ['appointment']
  } else if (path.startsWith('/finance')) {
    openKeys.value = ['finance']
  } else if (path.startsWith('/system')) {
    openKeys.value = ['system']
  }
}

// 监听路由变化
watch(
  () => route.path,
  (newPath, oldPath) => {
    // 只有路径真正改变时才更新菜单选中状态
    if (newPath !== oldPath) {
      updateMenuSelection(newPath)
    }
  },
  { immediate: true }
)

// 菜单点击处理
const handleMenuClick = ({ key }) => {
  console.log('🔥 菜单点击:', key)

  const routeMap = {
    'dashboard': '/dashboard',
    'appointment-list': '/appointments',
    'customers': '/customers',
    'therapists': '/therapists',
    'services': '/services',
    'finance-overview': '/finance',
    'system-settings': '/system',
    'health-tips': '/health-tips',
    'data-statistics': '/statistics'
  }

  // 🚪 处理退出登录菜单项
  if (key === 'logout') {
    handleLogout()
    return
  }

  // 防止重复点击同一菜单项
  const targetPath = routeMap[key]
  if (targetPath && targetPath === route.path) {
    console.log('🔄 已在当前页面，跳过导航')
    return
  }

  console.log('🎯 目标路由:', targetPath)

  if (targetPath) {
    router.push(targetPath).then(() => {
      console.log('✅ 路由跳转成功:', targetPath)
    }).catch(error => {
      console.error('❌ 路由跳转失败:', error)
      message.error('页面跳转失败')
    })
  } else {
    console.warn('⚠️ 未找到对应路由:', key)
    message.warning('该功能正在开发中')
  }
}

// 退出登录处理
const handleLogout = () => {
  console.log('🚪 用户点击退出登录')

  // 清除用户状态
  userStore.logout()

  // 显示退出成功提示
  message.success('退出登录成功')

  // 跳转到登录页面
  router.push('/login').then(() => {
    console.log('✅ 已跳转到登录页面')
  }).catch(error => {
    console.error('❌ 跳转登录页面失败:', error)
  })
}

// 获取菜单标题
const getMenuTitle = (key) => {
  const titleMap = {
    'dashboard': '仪表盘',
    'appointment-list': '预约列表',
    'customers': '客户管理',
    'therapists': '技师管理',
    'services': '服务管理',
    'finance-overview': '财务概览',
    'finance-records': '收支记录',
    'finance-reports': '财务报表',
    'system-settings': '系统设置',
    'system-logs': '操作日志'
  }
  return titleMap[key] || key
}

// 用户菜单点击处理
const handleUserMenuClick = ({ key }) => {
  switch (key) {
    case 'logout':
      userStore.logout()
      router.push('/login')
      break
  }
}

onMounted(() => {
  // 初始化菜单状态
  updateMenuSelection(route.path)

  // 获取后端版本号
  fetchBackendVersion()

  // 初始化Logo RGB三原色变色效果 - 永久保持
  initLogoColorEffect()

  // 🎯 PostCSS会自动处理响应式，不需要手动监听窗口大小变化
  // 移除窗口大小监听器，因为PostCSS会自动转换px为rem

  // 开发环境加载精确对齐调试工具
  if (process.env.NODE_ENV === 'development') {
    import('@/utils/alignmentDebugger.js').then(({ alignmentDebugger }) => {
      console.log('🎯 精确对齐调试工具已加载')
      console.log('💡 快捷键说明:')
      console.log('   Alt + A: 切换对齐调试模式')
      console.log('   Alt + G: 切换网格覆盖')
      console.log('   Alt + M: 测量菜单表格对齐')
      console.log('💡 控制台命令:')
      console.log('   checkAlignment(): 检查对齐情况')
      console.log('   measureAlignment(): 测量对齐偏差')
      console.log('   fixAlignment(): 获取修复建议')
    }).catch(error => {
      console.warn('⚠️ 对齐调试工具加载失败:', error)
    })

    // 加载精确对齐修复工具
    import('@/utils/preciseAlignmentFixer.js').then(({ preciseAlignmentFixer }) => {
      console.log('🔧 精确对齐修复工具已加载')
      console.log('💡 控制台命令:')
      console.log('   fixAlignment() - 执行精确对齐修复')
      console.log('   measureCurrentAlignment() - 测量当前对齐状态')
      console.log('   showAlignmentReport() - 显示详细对齐报告')
      console.log('   resetAlignment() - 重置对齐修复')

      // 自动执行智能对齐测试
      setTimeout(() => {
        console.log('🤖 启动智能对齐测试...')
        preciseAlignmentFixer.autoTestAlignment().then((result) => {
          if (result) {
            console.log('✅ 自动对齐测试完成！')
            console.log('💡 使用以下命令查看结果:')
            console.log('   showAlignmentReport() - 查看详细报告')
            console.log('   resetAlignment() - 重置修复')
          } else {
            console.log('⚠️ 自动测试未完成，请手动导航到有表格数据的页面')
            console.log('💡 可用命令:')
            console.log('   navigateToTestPage() - 导航到测试页面')
            console.log('   autoTestAlignment() - 重新运行自动测试')
          }
        }).catch(error => {
          console.warn('⚠️ 自动测试失败:', error)
          console.log('💡 请手动执行: fixAlignment()')
        })
      }, 3000)
    }).catch(error => {
      console.warn('⚠️ 精确对齐修复工具加载失败:', error)
    })

    // 加载对齐测试报告生成器
    import('@/utils/alignmentReporter.js').then(({ alignmentReporter }) => {
      console.log('📊 对齐测试报告生成器已加载')
      console.log('💡 报告命令:')
      console.log('   generateAlignmentReport() - 生成详细对齐报告')
      console.log('   showAlignmentSummary() - 显示报告摘要')
      console.log('   exportAlignmentReport() - 导出报告数据')
    }).catch(error => {
      console.warn('⚠️ 对齐测试报告生成器加载失败:', error)
    })

    // 加载强制对齐修复工具
    import('@/utils/forceAlignmentFixer.js').then(({ forceAlignmentFixer }) => {
      console.log('💪 强制对齐修复工具已加载')
      console.log('💡 强力修复命令:')
      console.log('   forceFixAlignment() - 强制修复对齐')
      console.log('   smartAlignment() - 智能对齐修复')
      console.log('   testOffsets() - 测试多个偏移量')
      console.log('   adjustOffset(px) - 手动调整偏移')
      console.log('   resetForceAlignment() - 重置强制修复')
    }).catch(error => {
      console.warn('⚠️ 强制对齐修复工具加载失败:', error)
    })

    // 🎯 加载菜单栏缩放调试工具
    import('@/utils/menuScaleDebugger.js').then((module) => {
      console.log('🎯 菜单栏缩放调试工具已加载')
      console.log('📏 缩放测试命令:')
      console.log('   menuScaleDebugger.startTest() - 开始菜单栏缩放测试')
      console.log('   menuScaleDebugger.testDifferentScales() - 测试不同缩放比例')
      console.log('   menuScaleDebugger.disableDebug() - 关闭调试模式')
      console.log('   menuScaleDebugger.getResults() - 获取测试结果')
    }).catch(error => {
      console.warn('⚠️ 菜单栏缩放调试工具加载失败:', error)
    })
  }
})

// 🌈 Logo RGB三原色变色效果 - 永久保持
const initLogoColorEffect = () => {
  setTimeout(() => {
    const logoImg = document.querySelector('.header-logo')
    if (!logoImg) return

    // 清除可能存在的旧变色元素
    const oldColorOverlay = document.querySelector('#logo-color-overlay')
    if (oldColorOverlay) {
      oldColorOverlay.remove()
    }

    // 确保logo容器是相对定位
    const logoContainer = logoImg.parentElement
    logoContainer.style.position = 'relative'

    // 隐藏原logo
    logoImg.style.opacity = '0'

    // 创建RGB三原色变色覆盖层 - 使用vh/vw单位确保相对大小一致
    const colorOverlay = document.createElement('div')
    colorOverlay.id = 'logo-color-overlay'

    // 🎯 使用vh/vw单位，确保在所有分辨率下保持相同的相对大小
    const viewportHeight = window.innerHeight
    const viewportWidth = window.innerWidth
    const vhTop = 1.3 // 1.3vh，相当于原来的1rem在768px高度下的比例
    const vwLeft = 1.85 // 1.85vw，相当于原来的1.75rem在1512px宽度下的比例
    const vwWidth = 8.2 // 8.2vw，确保Logo宽度相对一致
    const vhHeight = 10.25 // 10.25vh，与CSS中的height保持一致

    colorOverlay.style.cssText = `
      position: absolute;
      top: ${vhTop}vh;
      left: ${vwLeft}vw;
      width: ${vwWidth}vw;
      height: ${vhHeight}vh;
      background: rgb(255, 0, 0);
      z-index: 16;
      pointer-events: none;
      mask: url('${logoImg.src}') no-repeat center/contain;
      -webkit-mask: url('${logoImg.src}') no-repeat center/contain;
      mask-size: contain;
      -webkit-mask-size: contain;
      animation: rgbPrimaryColors 60s linear infinite;
      opacity: 1;
    `

    // 添加RGB三原色动画样式
    if (!document.querySelector('#logo-color-shift-style')) {
      const style = document.createElement('style')
      style.id = 'logo-color-shift-style'
      style.textContent = `
        @keyframes rgbPrimaryColors {
          0% { background: rgb(255, 0, 0); }      /* 纯红色 */
          16.66% { background: rgb(255, 255, 0); }   /* 黄色 */
          33.33% { background: rgb(0, 255, 0); }     /* 纯绿色 */
          50% { background: rgb(0, 255, 255); }      /* 青色 */
          66.66% { background: rgb(0, 0, 255); }     /* 纯蓝色 */
          83.33% { background: rgb(255, 0, 255); }   /* 洋红色 */
          100% { background: rgb(255, 0, 0); }       /* 回到纯红色 */
        }
      `
      document.head.appendChild(style)
    }

    logoContainer.appendChild(colorOverlay)

    console.log('🌈 Logo RGB三原色变色效果已永久启动')
    console.log('  - 60秒连续循环')
    console.log('  - 红→黄→绿→青→蓝→洋红→红')
    console.log('  - 永不停留，持续流动')
  }, 100)
}

// 监听路由变化，确保logo变色效果始终保持
watch(route, (newRoute) => {
  updateMenuSelection(newRoute.path)

  // 确保logo变色效果始终存在
  setTimeout(() => {
    const colorOverlay = document.querySelector('#logo-color-overlay')
    if (!colorOverlay) {
      console.log('🔄 路由变化后重新初始化logo变色效果')
      initLogoColorEffect()
    }
  }, 100)
}, { immediate: true })
</script>

<style lang="scss" scoped>
// 修复Ant Design默认背景覆盖问题
.ant-layout {
  background: transparent !important; // 强制覆盖Ant Design的默认灰色背景
}

/* SVG波浪动画容器 */
.wave-container {
  position: absolute;
  bottom: 0;
  left: 0;
  z-index: 1;
  width: 100%;
  height: 15vh;
  max-height: 150px;
  min-height: 100px;
  margin-bottom: -7px;
}

/* SVG波浪样式 */
.waves {
  position: relative;
  width: 100%;
  height: 15vh;
  max-height: 150px;
  min-height: 100px;
  margin-bottom: -7px;
}

/* 波浪动画 */
.parallax > use {
  animation: move-forever 25s cubic-bezier(.55,.5,.45,.5) infinite;
}

.parallax > use:nth-child(1) {
  animation-delay: -2s;
  animation-duration: 7s;
}

.parallax > use:nth-child(2) {
  animation-delay: -3s;
  animation-duration: 10s;
}

.parallax > use:nth-child(3) {
  animation-delay: -4s;
  animation-duration: 13s;
}

.parallax > use:nth-child(4) {
  animation-delay: -5s;
  animation-duration: 20s;
}

@keyframes move-forever {
  0% {
    transform: translate3d(-90px,0,0);
  }

  100% {
    transform: translate3d(85px,0,0);
  }
}

.admin-sider {
  position: fixed;
  position: relative;
  top: 0;
  left: 0;
  z-index: 100; /* 确保侧边栏在波浪效果之上 */

  /* 🎯 使用px值，PostCSS会自动转换为rem实现响应式缩放 */

  /* 强制覆盖Ant Design的默认宽度 */

  /* 180px，PostCSS会转换为rem保持响应式 */
  width: 180px !important;

  /* 768px，PostCSS会转换为rem保持响应式 */
  height: 768px;
  max-width: 180px !important;
  min-width: 180px !important;
  overflow: hidden;
  // 继承父容器的统一背景，不单独设置
  background: transparent;

  /* ========================================
     🎨 统一整体设计 - 移除分割线和阴影
     ======================================== */
  // 移除右边框和阴影，让菜单栏与主内容区融为一体
  box-shadow: none;
  flex-basis: 180px !important;
  border-right: none;

  // 为底部版权信息预留空间 - 使用px值，PostCSS会转换为rem
  padding-bottom: 80px; /* PostCSS会转换为rem */

  // 梵高风格的纹理效果
  &::before {
    position: absolute;
    inset: 0;
    background:
      radial-gradient(ellipse at 30% 20%, rgb(168 85 247 / 15%) 0%, transparent 60%),
      radial-gradient(ellipse at 70% 40%, rgb(139 92 246 / 12%) 0%, transparent 50%),
      radial-gradient(ellipse at 20% 70%, rgb(147 51 234 / 10%) 0%, transparent 40%),
      radial-gradient(ellipse at 80% 90%, rgb(124 58 237 / 8%) 0%, transparent 45%);
    content: '';
    pointer-events: none;
    animation: vanGoghFlow 25s ease-in-out infinite;
  }

  .logo {
    display: flex;
    position: relative;
    z-index: 2;
    height: 100px;                    // 增加logo容器高度，给logo更多空间
    padding: 16px 28px;               // 增加上下内边距
    overflow: visible !important;     // 临时改为visible，确保流光不被裁剪
    align-items: center;
    justify-content: center;          // 改为居中对齐，更美观
    margin-bottom: 0;               // 去掉下边距，让菜单紧贴logo
    // 去掉横线分隔效果
    // border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(4px);

    // 移除装饰线，保持统一背景
    // &::after {
    //   content: '';
    //   position: absolute;
    //   bottom: 0;
    //   left: 15%;
    //   right: 15%;
    //   height: 1px;
    //   background: linear-gradient(90deg,
    //     transparent 0%,
    //     rgba(139, 92, 246, 0.3) 50%,
    //     transparent 100%);
    // }



    /* 🔧 顶部logo图片样式 - 使用vh单位确保相对大小一致 */
    // 🌈 七彩流光效果 - 使用:deep()确保伪元素样式生效
    .header-logo {
    /* 🌈 七彩流光效果 - 修复层级冲突 */
      position: relative;
    z-index: 15 !important;          // 提高logo元素层级，确保::before可见
      z-index: 1;
    width: auto !important;
    height: 10.25vh !important;       // 使用vh单位，确保在所有分辨率下相对高度一致（10.25%）
    max-width: 16.2vw !important;     // 使用vw单位，确保宽度也保持相对一致
    max-height: 10.25vh !important;   // 使用vh单位，与height保持一致
    overflow: hidden !important;

    /* 🌟 移除发光效果以优化透明背景 */
    box-shadow: none !important;
    transition: none !important;      // 移除过渡动画
    object-fit: contain !important;   // 保持比例

    /* 🎨 优化透明背景显示 */
    filter: none !important;          // 移除滤镜，保持原始透明度

      // 字体平滑和其他属性
      -webkit-font-smoothing: antialiased;

    /* 🎭 移除悬浮变大效果 - 按用户要求 */
    &:hover {
      transform: none !important;     // 移除悬浮缩放效果
      filter: none !important;       // 移除悬浮滤镜效果
      cursor: default !important;    // 改为默认鼠标样式
    }

    &::before {
        position: absolute;
        top: 0;
        left: -100%;
        z-index: 20 !important;         // 进一步提高层级
        width: 100%;
        height: 100%;
        border-radius: 8px;
        background: linear-gradient(
          90deg,
          red 0%,
          orange 14%,
          yellow 28%,
          green 42%,
          cyan 56%,
          blue 70%,
          purple 84%,
          red 100%
        );
        content: '';
        animation: logoRainbowShine 3s linear infinite; // 放慢速度，便于观察
        pointer-events: none;
        mix-blend-mode: normal !important; // 改为normal模式，最直接显示
        filter: none !important;        // 暂时移除滤镜，看原始效果
        // 移除红色背景，只保留七彩渐变
      }
    }
  }

  // 侧边栏底部版权信息样式 - 移除独立背景，与主背景统一
  .sidebar-footer {
    position: absolute;

    /* 修正为720px，确保在1512×768下不超出屏幕，PostCSS会自动转换 */
    top: 720px;
    right: 0;
    left: 0;

    /* 8px 16px，PostCSS会转换为rem保持响应式 */
    padding: 8px 16px 16px;
    // 移除独立背景，使用主侧边栏背景
    // background: transparent;
    // backdrop-filter: none;
    // border-top: none;
    // box-shadow: none;

    // 直接在sidebar-footer下的logo样式（当没有footer-info容器时）
    .footer-logo {
      display: block !important;
      width: 120px !important;          // 增大宽度
      height: auto !important;
      max-width: 120px !important;      // 增大最大宽度
      max-height: 90px !important;      // 增大最大高度
      margin: 16px auto !important;     // 居中显示

      // 确保没有其他背景影响
      border: none !important;
      border-radius: 0;                  // 移除圆角

      // 确保图片背景透明但图片本身可见
      background: none !important;
      box-shadow: none !important;       // 移除阴影框
      opacity: 1 !important;
      transition: all 0.3s ease;
      object-fit: contain;
      background-color: transparent !important;
      visibility: visible !important;
      outline: none !important;

      &:hover {
        box-shadow: none !important;     // 悬停时也不显示阴影框
        transform: scale(1.05);
      }
    }

    .footer-info {
      display: flex;
      position: relative;
      text-align: center;
      flex-direction: column;
      align-items: center;

      .footer-logo {
        display: block;

        /* ========================================
           🌟 超级炫酷光泽扫过效果
           ======================================== */
        position: relative;
        width: 70%;                      // 增大宽度百分比
        height: auto;
        max-width: 120px;                // 增大最大宽度

        // 方法3：强力去白背景（谨慎使用）
        // mix-blend-mode: screen;
        // filter: invert(1) brightness(0) contrast(100);

        // 确保没有其他背景影响
        border: none !important;
        border-radius: 8px;
        overflow: hidden;

        // 确保图片背景透明但图片本身可见
        background: none !important;
        box-shadow:
          0 4px 12px rgb(139 92 246 / 20%),
          0 2px 6px rgb(139 92 246 / 10%);

        // 确保图片正常显示
        opacity: 1 !important;
        margin-bottom: 8px;
        object-fit: contain;
        visibility: visible !important;
        background-color: transparent !important;
        cursor: pointer;
        outline: none !important;

        // 创建光泽容器
        &::before {
          position: absolute;
          top: -50%;
          left: -50%;
          z-index: 1;
          width: 200%;
          height: 200%;
          background: linear-gradient(
            45deg,
            transparent 30%,
            rgb(255 255 255 / 10%) 35%,
            rgb(255 255 255 / 30%) 40%,
            rgb(255 255 255 / 80%) 45%,
            rgb(255 255 255 / 100%) 50%,
            rgb(255 255 255 / 80%) 55%,
            rgb(255 255 255 / 30%) 60%,
            rgb(255 255 255 / 10%) 65%,
            transparent 70%
          );
          transform: translateX(-100%) translateY(-100%) rotate(45deg);
          content: '';
          animation: superShine 4s ease-in-out infinite;
          pointer-events: none;
        }

        // 第二层彩虹光泽
        &::after {
          position: absolute;
          top: -50%;
          left: -50%;
          z-index: 2;
          width: 200%;
          height: 200%;
          background: linear-gradient(
            45deg,
            transparent 25%,
            rgb(255 0 128 / 30%) 30%,
            rgb(255 128 0 / 40%) 35%,
            rgb(255 255 0 / 50%) 40%,
            rgb(128 255 0 / 60%) 45%,
            rgb(0 255 128 / 70%) 50%,
            rgb(0 128 255 / 60%) 55%,
            rgb(128 0 255 / 50%) 60%,
            rgb(255 0 255 / 40%) 65%,
            transparent 75%
          );
          opacity: 0.6;
          transform: translateX(-100%) translateY(-100%) rotate(45deg);
          content: '';
          animation: rainbowShine 4s ease-in-out infinite 0.5s;
          pointer-events: none;
        }

        // 悬停时触发更强烈的效果
        &:hover {
          box-shadow:
            0 8px 25px rgb(139 92 246 / 40%),
            0 4px 15px rgb(139 92 246 / 30%),
            0 0 30px rgb(255 255 255 / 50%);
          transform: scale(1.05);

          &::before {
            animation: superShineHover 1.5s ease-out infinite;
          }

          &::after {
            animation: rainbowShineHover 1.5s ease-out infinite 0.2s;
            opacity: 0.8;
          }
        }

        &:hover {
          box-shadow:
            0 6px 16px rgb(139 92 246 / 30%),
            0 3px 8px rgb(139 92 246 / 15%);
          transform: scale(1.05);
        }
      }

      .copyright {
        text-align: center;

        .copyright-line1 {
          font-size: 11px; /* PostCSS会转换为rem */
          font-weight: 500;
          letter-spacing: 0.3px; /* PostCSS会转换为rem */
          margin-bottom: 2px; /* PostCSS会转换为rem */

          // 彩虹字特效
          background: linear-gradient(
            45deg,
            #ff0080 0%,    // 粉红
            #ff8000 14%,   // 橙色
            #ff0 28%,   // 黄色
            #80ff00 42%,   // 绿色
            #00ff80 56%,   // 青绿
            #0080ff 70%,   // 蓝色
            #8000ff 84%,   // 紫色
            #ff0080 100%   // 回到粉红
          );
          background-size: 200% 200%;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          animation: rainbowText 3s ease-in-out infinite;
          text-shadow: 0 0 10px rgb(255 255 255 / 30%);
        }

        .copyright-line2 {
          font-size: 10px; /* PostCSS会转换为rem */
          font-weight: 400;
          letter-spacing: 0.2px; /* PostCSS会转换为rem */

          // 彩虹字特效（稍微不同的颜色）
          background: linear-gradient(
            -45deg,
            #ff4080 0%,    // 粉红
            #ff9040 14%,   // 橙色
            #ffff40 28%,   // 黄色
            #80ff40 42%,   // 绿色
            #40ff80 56%,   // 青绿
            #4080ff 70%,   // 蓝色
            #8040ff 84%,   // 紫色
            #ff4080 100%   // 回到粉红
          );
          background-size: 200% 200%;
          background-clip: text;
          -webkit-text-fill-color: transparent;
          animation: rainbowText 3s ease-in-out infinite reverse;
          text-shadow: 0 0 8px rgb(255 255 255 / 20%);
        }
      }
    }
  }

  // 🎨 梵高风格菜单 - 完全重新设计，继续上移避免挡住版本信息
  :deep(.ant-menu) {
    max-height: 650px !important;    // 🎯 修复退出登录显示问题：调整为650px确保10个菜单项完整显示
    overflow: hidden !important;     // 🎯 隐藏所有滚动条（上下和左右）
    font-family: Georgia, 'Times New Roman', '宋体', serif !important; // 梵高时代的经典字体
    background: transparent !important;
    border-right: none !important;
    margin-top: 5.14px !important;  // 🎯 精确对齐：将菜单往下移动5.14px与表格数据对齐

    .ant-menu-item, .ant-menu-submenu-title {
      position: relative !important;
      height: 50px !important; /* 🎯 调整高度为50px，与表单控件一致 */
      margin: 8px 20px !important; /* 🎯 调整间距为8px，9个菜单项平均分布 */

      // 梵高风格内边距
      padding: 0 20px !important;

      // 保留紫色边框 - 按用户要求保留
      border: 2px solid rgb(139 92 246 / 80%) !important;
      border-radius: 0 !important; // 梵高风格：不规则边缘
      overflow: hidden !important;
      font-size: 16px !important;
      font-weight: 700 !important;
      line-height: 50px !important; /* 🎯 调整行高匹配新高度 */

      // 保留白色文字 - 按用户要求保留
      color: rgb(255 255 255) !important; // 白色文字

      // 菜单背景透明 - 按用户要求显示全局背景
      background: transparent !important;
      transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
      letter-spacing: 1px !important;
      text-transform: uppercase !important; // 大写字母更有力量感

      // 去掉文字阴影 - 按用户要求透明
      text-shadow: none !important;
      border-image: none !important;

      // 🎨 梵高风格悬停效果已禁用 - 按用户要求去掉菜单悬停
      // &:hover {
      //   // 梵高《星夜》风格的旋转背景
      //   background: linear-gradient(135deg,
      //     #FFD700 0%,   // 金黄 - 梵高向日葵
      //     #FF8C00 15%,  // 橙色 - 梵高的热情
      //     #4169E1 30%,  // 皇家蓝 - 梵高星夜
      //     #8A2BE2 45%,  // 蓝紫 - 梵高的神秘
      //     #32CD32 60%,  // 绿色 - 梵高的自然
      //     #FF6347 75%,  // 番茄红 - 梵高的激情
      //     #FFD700 90%,  // 回到金黄
      //     #FF8C00 100%  // 橙色结尾
      //   ) !important;

      //   color: #FFFFFF !important;
      //   text-shadow:
      //     2px 2px 4px rgba(0, 0, 0, 0.8),
      //     0 0 10px rgba(255, 215, 0, 0.6) !important;

      //   // 梵高风格：厚重的笔触变形
      //   transform: translateX(8px) rotateZ(1deg) scale(1.05) !important;

      //   // 梵高风格：厚重的阴影和光晕
      //   box-shadow:
      //     8px 8px 20px rgba(0, 0, 0, 0.4),
      //     0 0 30px rgba(255, 215, 0, 0.5),
      //     inset 0 0 20px rgba(255, 255, 255, 0.2) !important;

      //   // 梵高风格：不规则边框
      //   border: 4px solid #FFD700 !important;
      //   border-radius: 8px 20px 8px 20px !important; // 不规则圆角
      // }

      // 移除focus状态的蓝色框和outline
      &:focus,
      &:focus-visible,
      &:focus-within {
        outline: none !important;
        border: none !important;
        box-shadow: 0 2px 8px rgb(139 92 246 / 15%) !important;
      }

      // 移除active状态的额外样式
      &:active {
        outline: none !important;
        border: none !important;
      }

      // 🌟 选中状态：保留紫色边框和白色文字，其他透明
      &.ant-menu-item-selected {
        position: relative !important;
        width: auto !important;

        // 强制限制宽度，确保不超出容器
        max-width: calc(100% - 40px) !important;

        // 强制覆盖所有可能的padding设置
        padding: 0 16px !important;

        // 保留紫色边框
        border: 2px solid rgb(139 92 246 / 80%) !important;
        border-radius: 12px !important;
        overflow: hidden !important; // 改为hidden以裁剪流光效果
        font-size: 17px !important;
        font-weight: 800 !important;

        // 保留白色文字
        color: rgb(255 255 255) !important;
        // 背景透明
        background: transparent !important;

        // 去掉阴影效果
        box-shadow: none !important;

        // 去掉变形效果
        transform: none !important;
        text-shadow: none !important; // 去掉文字阴影

        // 💫 选中菜单流光效果
        &::before {
          position: absolute;
          top: 0;
          left: -100%;
          z-index: 1;
          width: 100%;
          height: 100%;
          border-radius: 12px;
          background: linear-gradient(
            90deg,
            transparent 0%,
            rgb(255 255 255 / 30%) 20%,
            rgb(255 255 255 / 60%) 50%,
            rgb(255 255 255 / 30%) 80%,
            transparent 100%
          );
          content: '';
          animation: selectedMenuShine 2s ease-in-out infinite;
          pointer-events: none;
        }

        // 流光效果已移除
        // &::before {
        //   // 流光效果代码已注释
        // }

        // 移除选中状态的focus蓝色框
        &:focus,
        &:focus-visible,
        &:focus-within {
          outline: none !important;
          border: none !important;
          // 保持选中状态的box-shadow
          box-shadow:
            0 4px 12px rgb(139 92 246 / 25%),
            inset 0 1px 0 rgb(255 255 255 / 30%),
            inset 0 0 0 1px rgb(139 92 246 / 30%),
            inset 4px 0 0 0 #8b5cf6 !important;
        }

        // 移除选中状态的active额外样式
        &:active {
          outline: none !important;
          border: none !important;
        }



        // 确保内容在流光之上
        .anticon,
        span {
          position: relative !important;
          z-index: 2 !important;
        }

        // 🎨 选中状态图标：保留白色，确保在流光之上
        .anticon {
          position: relative !important;
          z-index: 2 !important; // 确保在流光效果之上
          font-size: 20px !important;
          color: rgb(255 255 255) !important; // 保留白色
          transform: none !important; // 去掉变形
          filter: none !important; // 去掉滤镜效果
        }

        span {
          text-transform: uppercase !important;
          letter-spacing: 1.5px !important;
          position: relative !important;
          z-index: 2 !important; // 确保文字在流光效果之上
        }
      }

      // 🎨 图标样式：保留白色，去掉滤镜
      .anticon {
        font-size: 18px !important;
        color: rgb(255 255 255) !important; // 保留白色图标
        transform: scale(1) rotate(0deg) !important;
        transition: all 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55) !important;
        margin-right: 12px !important;
        filter: none !important; // 去掉滤镜效果
      }
    }

    // 🎨 梵高风格子菜单（如果需要的话）
    .ant-menu-submenu {
      .ant-menu-submenu-arrow {
        font-size: 14px !important;
        color: #FFD700 !important;
      }

      &.ant-menu-submenu-open > .ant-menu-submenu-title {
        color: #FFF !important;
        background: linear-gradient(45deg,
          #FFD700 0%, #FF8C00 50%, #4169E1 100%) !important;
      }
    }

    .ant-menu-sub {
      margin: 4px 20px !important;
      padding: 8px 0 !important;
      border: 2px solid #FFD700 !important;
      border-radius: 0 !important;
      background: transparent !important;
      backdrop-filter: blur(10px) !important;

      .ant-menu-item {
        height: 35px !important; /* 🎯 调整小屏幕高度 */
        margin: 6px 12px !important; /* 🎯 调整小屏幕间距 */
        font-size: 14px !important;
        line-height: 35px !important; /* 🎯 调整小屏幕行高 */
        color: #F4E4BC !important;
        padding-left: 24px !important;
      }
    }
  }
}

.admin-header {
  display: flex;
  position: relative;
  z-index: 100;
  height: 36px;
  padding: 0 32px;
  background: linear-gradient(135deg,
    rgb(255 255 255 / 98%) 0%,
    rgb(248 244 255 / 95%) 30%,
    rgb(243 232 255 / 98%) 70%,
    rgb(237 233 254 / 95%) 100%);
  box-shadow:
    0 2px 8px rgb(139 92 246 / 8%),
    0 8px 32px rgb(139 92 246 / 4%),
    inset 0 1px 0 rgb(255 255 255 / 90%);
  backdrop-filter: blur(24px);
  border-bottom: 1px solid rgb(139 92 246 / 12%);
  align-items: center;
  justify-content: space-between;
  margin-left: 11.25rem; /* 180px ÷ 16px = 11.25rem，跟随rem缩放 */

  // 添加微妙的顶部高光
  &::before {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    height: 1px;
    background: linear-gradient(90deg,
      transparent 0%,
      rgb(139 92 246 / 25%) 50%,
      transparent 100%);
    content: '';
  }
  
  .header-left {
    display: flex;
    align-items: center;

    // header-logo 相关样式已移除 - 删除重复的顶部Logo

    .breadcrumb {
      margin: 0;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 16px;
    
    .notification {
      cursor: pointer;
    }
    
    .user-info {
      display: flex;
      align-items: center;
      gap: 8px;

      .username {
        margin-left: 8px;
      }
    }
  }
}

.admin-content {
  position: relative;
  z-index: 10; /* 确保主内容区在波浪效果之上 */

  /* 704px ÷ 16px = 44rem，使用rem单位确保高度正确缩放 */
  height: 704px;
  padding: 0;
  overflow: hidden; /* 完全隐藏外层滚动条 */
  // 继承父容器的统一背景
  background: inherit;
  margin-left: 11.25rem; /* 180px ÷ 16px = 11.25rem，跟随rem缩放 */

  .content-wrapper {
    height: 100%;
    padding: 32px; /* 统一设置为32px，确保所有页面顶部间距一致 */
    overflow: hidden; /* 仪表盘页面不需要滚动 */

    // 调整文字颜色以适应深色背景
    color: rgb(255 255 255 / 90%);

    // 确保所有内容元素在深色背景上可读
    :deep(.ant-card) {
      color: rgb(0 0 0 / 85%);
      background: rgb(255 255 255 / 95%);
    }

    :deep(.ant-table) {
      color: rgb(0 0 0 / 85%);
      background: rgb(255 255 255 / 95%);
    }

    :deep(.ant-form) {
      .ant-form-item-label > label {
        color: rgb(255 255 255 / 90%);
      }
    }

    /* 对于其他需要滚动的页面，可以通过类名控制 */
    &.scrollable {
      overflow-y: auto;

      /* 完全隐藏滚动条 */
      scrollbar-width: none; /* Firefox */
      -ms-overflow-style: none; /* IE and Edge */

      &::-webkit-scrollbar {
        display: none; /* Chrome, Safari, Opera */
      }
    }

    /* 统一页面内容顶部间距 - 确保所有页面元素到顶部距离一致 */
    :deep(.picasso-dashboard),
    :deep(.picasso-customers),
    :deep(.picasso-services),
    :deep(.picasso-appointments),
    :deep(.picasso-therapists),
    :deep(.picasso-settings),
    :deep(.picasso-finance),
    :deep(.picasso-logs),
    :deep(.picasso-management) {
      /* 移除所有页面第一个元素的额外顶部间距 */
      > *:first-child:not(.geometric-background) {
        margin-top: 0 !important;
        padding-top: 0 !important;
      }

      /* 确保搜索栏、操作栏、统计卡片等关键元素的顶部间距统一 */
      .action-toolbar,
      .stats-cubism,
      .content-cubism,
      .settings-fragment,
      .tabs-cubism {
        margin-top: 0 !important;
        padding-top: 0 !important;
      }


    }
  }
}

// 彩虹字动画关键帧
@keyframes rainbowText {
  0% {
    background-position: 0% 50%;
  }

  50% {
    background-position: 100% 50%;
  }

  100% {
    background-position: 0% 50%;
  }
}

/* ========================================
   🌟 Logo光泽效果动画关键帧
   ======================================== */
// 主光泽扫过动画
@keyframes superShine {
  0% {
    opacity: 0;
    transform: translateX(-200%) translateY(-200%) rotate(45deg);
  }

  10% {
    opacity: 1;
  }

  50% {
    opacity: 1;
    transform: translateX(0%) translateY(0%) rotate(45deg);
  }

  90% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    transform: translateX(200%) translateY(200%) rotate(45deg);
  }
}

// 彩虹光泽动画
@keyframes rainbowShine {
  0% {
    opacity: 0;
    transform: translateX(-200%) translateY(-200%) rotate(45deg);
  }

  15% {
    opacity: 0.6;
  }

  50% {
    opacity: 0.8;
    transform: translateX(0%) translateY(0%) rotate(45deg);
  }

  85% {
    opacity: 0.6;
  }

  100% {
    opacity: 0;
    transform: translateX(200%) translateY(200%) rotate(45deg);
  }
}

// 悬停时的快速光泽动画
@keyframes superShineHover {
  0% {
    opacity: 0;
    transform: translateX(-150%) translateY(-150%) rotate(45deg);
  }

  20% {
    opacity: 1;
  }

  50% {
    opacity: 1;
    transform: translateX(50%) translateY(50%) rotate(45deg);
  }

  80% {
    opacity: 1;
  }

  100% {
    opacity: 0;
    transform: translateX(150%) translateY(150%) rotate(45deg);
  }
}

// 悬停时的彩虹光泽动画
@keyframes rainbowShineHover {
  0% {
    opacity: 0;
    transform: translateX(-150%) translateY(-150%) rotate(45deg);
  }

  25% {
    opacity: 0.8;
  }

  50% {
    opacity: 1;
    transform: translateX(50%) translateY(50%) rotate(45deg);
  }

  75% {
    opacity: 0.8;
  }

  100% {
    opacity: 0;
    transform: translateX(150%) translateY(150%) rotate(45deg);
  }
}

/* ========================================
   💜 选中菜单流光动画关键帧
   ======================================== */
@keyframes selectedMenuShine {
  0% {
    left: -100%;
    opacity: 0;
  }

  10% {
    opacity: 0.8;
  }

  50% {
    left: 100%;
    opacity: 1;
  }

  90% {
    opacity: 0.8;
  }

  100% {
    left: 100%;
    opacity: 0;
  }
}

/* ========================================
   🌈 Logo七彩流光动画关键帧
   ======================================== */
@keyframes logoRainbowShine {
  0% {
    left: -100%;
    opacity: 0;
  }

  10% {
    opacity: 0.8;
  }

  50% {
    left: 100%;
    opacity: 1;
  }

  90% {
    opacity: 0.8;
  }

  100% {
    left: 100%;
    opacity: 0;
  }
}

// 备用：transform版本（已废弃，有边界问题）
// @keyframes shimmerTransform {
//   0% {
//     transform: translateX(-100%);
//     opacity: 0;
//   }
//   10% {
//     opacity: 1;
//   }
//   90% {
//     opacity: 1;
//   }
//   100% {
//     transform: translateX(100%);
//     opacity: 0;
//   }
// }

// 流光动画关键帧已移除
// @keyframes shimmerPrecise {
//   // 流光动画已注释
// }

// 流光动画关键帧已移除
// @keyframes shimmer {
//   // 流光动画已注释
// }

/* ========================================
   🎯 菜单项豁口换到右侧设计
   ========================================

   📋 设计目标:
   - 豁口从左侧换到右侧
   - 左边贴边，右边留空隙
   - 右边圆角，左边直角

   🔧 技术实现:
   - margin-left: 0px (左边贴边)
   - margin-right: 20px (右边豁口)
   - border-radius: 0px 16px 16px 0px (右边圆角)
   ======================================== */
// 菜单项基础样式 - 豁口在右侧
:deep(.admin-sider .ant-menu .ant-menu-item) {
  display: flex !important;
  padding: 14px 20px !important;    // 内边距
  border: 1px solid rgb(255 255 255 / 10%) !important;

  /* 🎨 右侧圆角设计 */
  border-radius: 0 16px 16px 0 !important; // 右边圆角，左边直角

  /* 🎭 布局和对齐 */
  text-align: left !important;

  /* 💎 磨砂半透明效果 */
  background: transparent !important;

  /* 🎬 过渡动画 */
  transition: all 0.3s ease !important;

  /* 🔧 豁口在右侧布局 */
  margin-left: 0 !important;      // 左边贴边，无豁口
  margin-right: 20px !important;    // 右边留豁口
  justify-content: flex-start !important;
  align-items: center !important;
  backdrop-filter: blur(20px) saturate(180%) !important;
}

// 菜单项悬浮状态 - 往左侧移动效果
:deep(.admin-sider .ant-menu .ant-menu-item:hover) {
  /* ✨ 悬浮阴影增强 */
  box-shadow:
    0 8px 24px rgb(0 0 0 / 15%),
    0 4px 12px rgb(0 0 0 / 10%) !important;

  /* 🎭 悬浮往左移动效果 */
  transform: translateX(-4px) !important; // 往左移动4px

  /* 🎬 平滑过渡 */
  transition: all 0.3s ease !important;

  /* 🔧 保持豁口在右侧布局 */
  margin-left: 0 !important;      // 保持左边贴边
  margin-right: 20px !important;    // 保持右边豁口

  /* 💎 悬浮时增强磨砂效果 */
  backdrop-filter: blur(25px) saturate(200%) !important;
  border-color: rgb(255 255 255 / 30%) !important;
}

// 菜单项选中状态 - 豁口在右侧，紫色效果 (高优先级)
:deep(.admin-sider[data-v-b77bf5ee] .ant-menu .ant-menu-item-selected.ant-menu-item-selected) {
  display: flex !important;
  padding: 14px 20px !important;    // 内边距
  border: 2px solid rgb(139 92 246 / 80%) !important; // 更明显的紫色边框

  /* 🎨 右侧圆角设计 */
  border-radius: 0 20px 20px 0 !important; // 选中时更大的右边圆角

  /* 🎭 布局和对齐 */
  text-align: left !important;

  /* 💜 紫色选中效果 - 紫色边框 */
  background: transparent !important;

  /* ✨ 紫色光晕阴影 */
  box-shadow:
    0 8px 24px rgb(139 92 246 / 20%),
    0 4px 12px rgb(139 92 246 / 10%),
    0 0 20px rgb(139 92 246 / 15%) !important;

  /* 🎬 选中状态动画 */
  transition: all 0.3s ease !important;

  /* 🔧 豁口在右侧布局 */
  margin-left: 0 !important;      // 左边贴边，无豁口
  margin-right: 20px !important;    // 右边留豁口
  justify-content: flex-start !important;
  align-items: center !important;
  backdrop-filter: blur(25px) saturate(200%) !important;
}

// 移除可能影响文字显示的样式

// 梵高风格动画效果
@keyframes vanGoghFlow {
  0% {
    background-position: 0% 0%, 100% 100%, 0% 100%, 100% 0%;
  }

  25% {
    background-position: 100% 0%, 0% 100%, 100% 100%, 0% 0%;
  }

  50% {
    background-position: 100% 100%, 0% 0%, 100% 0%, 0% 100%;
  }

  75% {
    background-position: 0% 100%, 100% 0%, 0% 0%, 100% 100%;
  }

  100% {
    background-position: 0% 0%, 100% 100%, 0% 100%, 100% 0%;
  }
}



@keyframes vanGoghSpiral {
  0% {
    transform: rotate(0deg) scale(1);
  }

  25% {
    transform: rotate(90deg) scale(1.1);
  }

  50% {
    transform: rotate(180deg) scale(1);
  }

  75% {
    transform: rotate(270deg) scale(0.9);
  }

  100% {
    transform: rotate(360deg) scale(1);
  }
}

// 全局移除所有菜单项的focus outline和蓝色框
:deep(.ant-menu-item) {
  &:focus,
  &:focus-visible,
  &:focus-within {
    outline: none !important;
    border: none !important;
  }

  &:active {
    outline: none !important;
    border: none !important;
  }
}

// 特别处理选中菜单项的focus状态
:deep(.ant-menu-item-selected) {
  &:focus,
  &:focus-visible,
  &:focus-within {
    outline: none !important;
    border: none !important;
  }

  &:active {
    outline: none !important;
    border: none !important;
  }
}





.admin-footer {
  margin-left: 180px; /* PostCSS会转换为rem保持响应式 */
  text-align: center;
  background: white;
  border-top: 1px solid #f0f0f0;
  
  .footer-content {
    display: flex;
    font-size: 12px;
    color: #666;
    justify-content: space-between;
    align-items: center;
  }
}

// 响应式设计
@media (width <= 768px) {
  .admin-sider {
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    
    &.ant-layout-sider-collapsed {
      transform: translateX(0);
    }
  }
  
  .admin-header,
  .admin-content,
  .admin-footer {
    margin-left: 0;
  }
}
</style>
