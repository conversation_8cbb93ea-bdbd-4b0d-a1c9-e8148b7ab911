<!--
  标准数据单元格组件 v1.0
  基于轮廓调试成功案例制定
  目的：确保所有单元格内容都适应50px行高，不超出边界
  历史问题：padding冲突、行高过大、内容撑开容器
  解决方案：标准padding + 紧凑行高 + 强制高度限制
-->

<template>
  <div class="standard-data-cell" :class="computedClasses">
    <slot></slot>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 对齐方式
  align: {
    type: String,
    default: 'center', // left, center, right
    validator: (value) => ['left', 'center', 'right'].includes(value)
  },
  // 额外的CSS类名
  additionalClasses: {
    type: [String, Array, Object],
    default: ''
  },
  // 是否启用调试模式
  debug: {
    type: Boolean,
    default: false
  },
  // 是否可点击
  clickable: {
    type: Boolean,
    default: false
  }
})

// 计算类名
const computedClasses = computed(() => {
  const classes = [`align-${props.align}`]
  
  if (props.debug) {
    classes.push('debug-outline-yellow')
  }
  
  if (props.clickable) {
    classes.push('clickable')
  }
  
  if (props.additionalClasses) {
    if (typeof props.additionalClasses === 'string') {
      classes.push(props.additionalClasses)
    } else if (Array.isArray(props.additionalClasses)) {
      classes.push(...props.additionalClasses)
    }
  }
  
  return classes
})
</script>

<style scoped>
/* 导入标准库 */
@import url('@/styles/standards.css');

.standard-data-cell {
  /* 🎯 标准布局 */
  display: flex;
  
  /* 🎯 强制高度限制（防止内容撑开） */
  max-height: var(--row-height); /* 50px */

  /* 🎯 基于历史成功案例的标准样式 */
  padding: var(--cell-padding); /* 0 8px - 只设置左右内边距 */
  
  /* 🎯 确保内容不会撑开容器 */
  box-sizing: border-box;
  overflow: hidden;
  font-size: var(--cell-font-size); /* 0.9rem - 适应50px高度 */
  line-height: var(--cell-line-height); /* 1.2 - 紧凑行高 */
  
  /* 🎯 标准过渡效果 */
  transition: var(--transition-normal);
  align-items: center;
}

/* 🎯 对齐方式 */
.align-left {
  justify-content: flex-start;
}

.align-center {
  justify-content: center;
}

.align-right {
  justify-content: flex-end;
}

/* 🎯 可点击样式 */
.clickable {
  cursor: pointer;
  position: relative;
}

.clickable:hover {
  box-shadow: var(--shadow-light);
  transform: translateX(-2px);
  background-color: var(--hover-color);
}

.clickable:hover::after {
  position: absolute;
  top: -30px;
  left: 50%;
  z-index: 1000;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: white;
  background: var(--primary-color);
  opacity: 0.9;
  transform: translateX(-50%);
  content: "🔍 点击搜索";
  white-space: nowrap;
}

/* 🎯 调试模式样式 */
.debug-outline-yellow {
  outline: 3px solid yellow !important;
  outline-offset: -2px;
}

.debug-outline-yellow::before {
  position: absolute;
  top: -25px;
  left: 0;
  z-index: 10000;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 12px;
  color: black;
  background: yellow;
  box-shadow: 0 1px 3px rgb(0 0 0 / 30%);
  content: "标准数据单元格 (黄色边界)";
  white-space: nowrap;
}
</style>
