<!--
  标准数据行组件 v1.0
  基于轮廓调试成功案例制定
  目的：确保所有数据行都符合50px高度标准，与菜单项完美对齐
  历史问题：紫色框内元素超出边界
  解决方案：强制高度限制 + overflow hidden
-->

<template>
  <div class="standard-data-row" :class="additionalClasses">
    <slot></slot>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  // 额外的CSS类名
  additionalClasses: {
    type: [String, Array, Object],
    default: ''
  },
  // 是否启用调试模式（显示紫色轮廓）
  debug: {
    type: Boolean,
    default: false
  }
})

// 计算类名
const computedClasses = computed(() => {
  const classes = []
  
  if (props.debug) {
    classes.push('debug-outline-purple')
  }
  
  if (props.additionalClasses) {
    if (typeof props.additionalClasses === 'string') {
      classes.push(props.additionalClasses)
    } else if (Array.isArray(props.additionalClasses)) {
      classes.push(...props.additionalClasses)
    } else {
      Object.keys(props.additionalClasses).forEach(key => {
        if (props.additionalClasses[key]) {
          classes.push(key)
        }
      })
    }
  }
  
  return classes
})
</script>

<style scoped>
/* 导入标准库 */
@import url('@/styles/standards.css');

.standard-data-row {
  /* 🎯 标准布局 */
  display: flex;

  /* 🎯 基于历史成功案例的标准尺寸 */
  height: var(--row-height); /* 50px - 与菜单项对齐 */
  max-height: var(--row-height);
  min-height: var(--row-height);
  
  /* 🎯 确保内容不会撑开容器 */
  box-sizing: border-box;
  
  /* 🎯 防止内容超出边界（历史教训） */
  overflow: hidden;
  
  /* 🎯 标准过渡效果 */
  transition: var(--transition-normal);
  align-items: center;
}

/* 🎯 悬停效果（基于壹心堂设计标准） */
.standard-data-row:hover {
  box-shadow: var(--shadow-light);
  transform: translateX(-2px);
  background-color: var(--hover-color);
}

/* 🎯 调试模式样式 */
.debug-outline-purple {
  outline: 3px solid purple !important;
  outline-offset: -2px;
}

.debug-outline-purple::before {
  position: absolute;
  top: -25px;
  left: 0;
  z-index: 10000;
  padding: 2px 6px;
  border-radius: 2px;
  font-size: 12px;
  color: white;
  background: purple;
  box-shadow: 0 1px 3px rgb(0 0 0 / 30%);
  content: "标准数据行 (紫色边界)";
  white-space: nowrap;
}
</style>
