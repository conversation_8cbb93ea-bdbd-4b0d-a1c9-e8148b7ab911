<template>
  <div v-if="hasError" class="error-boundary">
    <div class="error-container">
      <div class="error-icon">
        <ExclamationCircleOutlined />
      </div>
      <div class="error-content">
        <h2>页面出现错误</h2>
        <p class="error-message">{{ errorMessage }}</p>
        <div class="error-actions">
          <a-button type="primary" @click="reload">刷新页面</a-button>
          <a-button @click="goHome" style="margin-left: 8px">返回首页</a-button>
          <a-button @click="showDetails" style="margin-left: 8px">查看详情</a-button>
        </div>
      </div>
    </div>

    <!-- 错误详情弹窗 -->
    <a-modal
      v-model:open="detailsVisible"
      title="错误详情"
      :footer="null"
      width="800px"
    >
      <div class="error-details">
        <a-descriptions title="错误信息" bordered size="small">
          <a-descriptions-item label="错误类型">{{ errorInfo.type }}</a-descriptions-item>
          <a-descriptions-item label="发生时间">{{ errorInfo.timestamp }}</a-descriptions-item>
          <a-descriptions-item label="页面路径">{{ errorInfo.path }}</a-descriptions-item>
          <a-descriptions-item label="用户代理" :span="2">{{ errorInfo.userAgent }}</a-descriptions-item>
        </a-descriptions>
        
        <div style="margin-top: 16px;">
          <h4>错误堆栈</h4>
          <pre class="error-stack">{{ errorInfo.stack }}</pre>
        </div>
        
        <div style="margin-top: 16px;">
          <h4>组件信息</h4>
          <pre class="component-info">{{ errorInfo.componentStack }}</pre>
        </div>
      </div>
    </a-modal>
  </div>
  <slot v-else />
</template>

<script setup>
import { ExclamationCircleOutlined } from '@ant-design/icons-vue'
import { message } from 'ant-design-vue'
import { onErrorCaptured, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'

// 响应式数据
const hasError = ref(false)
const errorMessage = ref('')
const detailsVisible = ref(false)
const errorInfo = ref({})

const router = useRouter()

// 捕获错误
onErrorCaptured((error, instance, info) => {
  // 检测是否在测试环境
  const isTestMode = window.navigator.webdriver || window.location.href.includes('test') || window.__TESTING__

  // 只在开发环境且非测试模式下输出错误信息
  if (import.meta.env.DEV && !isTestMode) {
    console.error('🚨 组件错误捕获:', {
      message: error.message,
      name: error.name,
      stack: error.stack
    })
  }

  hasError.value = true
  errorMessage.value = error.message || '未知错误'
  
  // 收集错误信息
  errorInfo.value = {
    type: error.name || 'Error',
    message: error.message,
    stack: error.stack,
    componentStack: info,
    timestamp: new Date().toLocaleString(),
    path: router.currentRoute.value.path,
    userAgent: navigator.userAgent
  }
  
  // 发送错误报告（在实际项目中）
  reportError(errorInfo.value)
  
  // 阻止错误继续传播
  return false
})

// 全局错误处理
onMounted(() => {
  // 监听未捕获的Promise错误
  window.addEventListener('unhandledrejection', (event) => {
    // 检测是否在测试环境
    const isTestMode = window.navigator.webdriver || window.location.href.includes('test') || window.__TESTING__

    // 只在开发环境且非测试模式下输出错误信息
    if (import.meta.env.DEV && !isTestMode) {
      console.error('🚨 未处理的Promise错误:', {
        message: event.reason?.message,
        stack: event.reason?.stack
      })
    }
    
    hasError.value = true
    errorMessage.value = event.reason?.message || '异步操作失败'
    
    errorInfo.value = {
      type: 'UnhandledPromiseRejection',
      message: event.reason?.message || '异步操作失败',
      stack: event.reason?.stack || '',
      timestamp: new Date().toLocaleString(),
      path: router.currentRoute.value.path,
      userAgent: navigator.userAgent
    }
    
    reportError(errorInfo.value)
  })
  
  // 监听全局JavaScript错误
  window.addEventListener('error', (event) => {
    // 过滤掉null错误和无意义的错误
    if (event.error === null && event.lineno === 0 && event.colno === 0) {
      console.warn('⚠️ ErrorBoundary捕获到null错误，已忽略');
      return;
    }

    console.error('🚨 全局JavaScript错误:', event.error)

    hasError.value = true
    errorMessage.value = event.error?.message || '脚本执行错误'
    
    errorInfo.value = {
      type: 'GlobalError',
      message: event.error?.message || '脚本执行错误',
      stack: event.error?.stack || '',
      filename: event.filename,
      lineno: event.lineno,
      colno: event.colno,
      timestamp: new Date().toLocaleString(),
      path: router.currentRoute.value.path,
      userAgent: navigator.userAgent
    }
    
    reportError(errorInfo.value)
  })
})

// 错误报告
const reportError = (error) => {
  // 在实际项目中，这里应该发送错误到监控服务
  console.log('📊 错误报告:', error)
  
  // 可以集成Sentry、LogRocket等错误监控服务
  // if (window.Sentry) {
  //   window.Sentry.captureException(new Error(error.message), {
  //     extra: error
  //   })
  // }
}

// 刷新页面
const reload = () => {
  window.location.reload()
}

// 返回首页
const goHome = () => {
  hasError.value = false
  router.push('/dashboard')
  message.info('已返回首页')
}

// 显示错误详情
const showDetails = () => {
  detailsVisible.value = true
}

// 重置错误状态
const resetError = () => {
  hasError.value = false
  errorMessage.value = ''
  errorInfo.value = {}
}

// 暴露重置方法
defineExpose({
  resetError
})
</script>

<style scoped>
.error-boundary {
  display: flex;
  min-height: 100vh;
  background: #f5f5f5;
  align-items: center;
  justify-content: center;
}

.error-container {
  max-width: 500px;
  padding: 48px;
  border-radius: 8px;
  text-align: center;
  background: white;
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
}

.error-icon {
  font-size: 64px;
  color: #ff4d4f;
  margin-bottom: 24px;
}

.error-content h2 {
  margin: 0 0 16px;
  font-size: 24px;
  font-weight: 600;
  color: #262626;
}

.error-message {
  font-size: 16px;
  line-height: 1.5;
  color: #666;
  margin-bottom: 32px;
}

.error-actions {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 8px;
}

.error-details {
  max-height: 60vh;
  overflow-y: auto;
}

.error-stack,
.component-info {
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 12px;
  line-height: 1.4;
  background: #f5f5f5;
  overflow-x: auto;
  white-space: pre-wrap;
  word-break: break-all;
}

@media (width <= 768px) {
  .error-container {
    margin: 16px;
    padding: 32px 24px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .ant-btn {
    width: 120px;
    margin: 4px 0 !important;
  }
}
</style>
