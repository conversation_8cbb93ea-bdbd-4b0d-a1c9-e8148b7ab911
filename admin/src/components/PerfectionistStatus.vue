<template>
  <div class="perfectionist-status" :class="{ 'minimized': minimized }">
    <div class="status-header" @click="toggleMinimize">
      <div class="status-title">
        <DiamondOutlined :class="{ 'perfect': perfectionLevel === 100 }" />
        <span>完美主义测试</span>
        <div class="perfection-level" :class="getPerfectionClass()">
          {{ perfectionLevel }}%
        </div>
      </div>
      <div class="status-controls">
        <a-button v-if="!isRunning" @click.stop="startTesting" size="small" type="primary">
          <PlayCircleOutlined />
        </a-button>
        <a-button v-else @click.stop="stopTesting" size="small" danger>
          <PauseCircleOutlined />
        </a-button>
        <a-button @click.stop="minimized = !minimized" size="small">
          <UpOutlined v-if="!minimized" />
          <DownOutlined v-else />
        </a-button>
      </div>
    </div>

    <div v-if="!minimized" class="status-content">
      <!-- 完美度仪表盘 -->
      <div class="perfection-dashboard">
        <div class="perfection-circle">
          <svg width="120" height="120" viewBox="0 0 120 120">
            <circle cx="60" cy="60" r="50" fill="none" stroke="#f0f0f0" stroke-width="8"/>
            <circle 
              cx="60" cy="60" r="50" 
              fill="none" 
              :stroke="getPerfectionColor()" 
              stroke-width="8"
              stroke-linecap="round"
              :stroke-dasharray="circumference"
              :stroke-dashoffset="circumference - (perfectionLevel / 100) * circumference"
              transform="rotate(-90 60 60)"
            />
          </svg>
          <div class="perfection-text">
            <div class="perfection-number">{{ perfectionLevel }}%</div>
            <div class="perfection-label">{{ getPerfectionLabel() }}</div>
          </div>
        </div>
      </div>

      <!-- 问题统计 -->
      <div class="issue-stats">
        <div class="stat-row">
          <div class="stat-item critical">
            <ExclamationCircleOutlined />
            <span>关键: {{ criticalIssues }}</span>
          </div>
          <div class="stat-item high">
            <WarningOutlined />
            <span>高级: {{ highIssues }}</span>
          </div>
        </div>
        <div class="stat-row">
          <div class="stat-item medium">
            <InfoCircleOutlined />
            <span>中级: {{ mediumIssues }}</span>
          </div>
          <div class="stat-item low">
            <CheckCircleOutlined />
            <span>低级: {{ lowIssues }}</span>
          </div>
        </div>
      </div>

      <!-- 修复进度 -->
      <div class="fix-progress">
        <div class="progress-title">
          <ToolOutlined />
          修复进度
        </div>
        <a-progress 
          :percent="fixProgress" 
          :stroke-color="getProgressColor()"
          size="small"
        />
        <div class="fix-stats">
          成功修复: {{ successfulFixes }} / {{ totalFixes }}
        </div>
      </div>

      <!-- 最近问题 -->
      <div class="recent-issues">
        <div class="issues-title">最近发现的问题</div>
        <div class="issues-list">
          <div v-for="issue in recentIssues" :key="issue.id" 
               class="issue-item" :class="issue.severity">
            <div class="issue-icon">
              <ExclamationCircleOutlined v-if="issue.severity === 'critical'" />
              <WarningOutlined v-else-if="issue.severity === 'high'" />
              <InfoCircleOutlined v-else-if="issue.severity === 'medium'" />
              <CheckCircleOutlined v-else />
            </div>
            <div class="issue-content">
              <div class="issue-name">{{ issue.name }}</div>
              <div class="issue-category">{{ issue.category }}</div>
            </div>
            <div class="issue-status">
              <CheckOutlined v-if="issue.fixed" style="color: #52c41a;" />
              <ClockCircleOutlined v-else style="color: #faad14;" />
            </div>
          </div>
        </div>
      </div>

      <!-- 改进建议 -->
      <div v-if="improvements.length > 0" class="improvements">
        <div class="improvements-title">
          <BulbOutlined />
          改进建议
        </div>
        <div class="improvements-list">
          <div v-for="improvement in improvements" :key="improvement.id" class="improvement-item">
            <div class="improvement-category">{{ improvement.category }}</div>
            <div class="improvement-text">{{ improvement.suggestedImprovement }}</div>
          </div>
        </div>
      </div>

      <!-- 操作按钮 -->
      <div class="action-buttons">
        <a-button @click="runQuickFix" size="small" type="primary" style="margin-right: 8px;">
          <ThunderboltOutlined />
          快速修复
        </a-button>
        <a-button @click="generateReport" size="small" style="margin-right: 8px;">
          <FileTextOutlined />
          生成报告
        </a-button>
        <a-button @click="resetPerfection" size="small">
          <ReloadOutlined />
          重新检测
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { message } from 'ant-design-vue'
import {
  DiamondOutlined,
  PlayCircleOutlined,
  PauseCircleOutlined,
  UpOutlined,
  DownOutlined,
  ExclamationCircleOutlined,
  WarningOutlined,
  InfoCircleOutlined,
  CheckCircleOutlined,
  ToolOutlined,
  CheckOutlined,
  ClockCircleOutlined,
  BulbOutlined,
  ThunderboltOutlined,
  FileTextOutlined,
  ReloadOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const minimized = ref(false)
const isRunning = ref(false)
const perfectionLevel = ref(0)
const issues = ref([])
const fixes = ref([])
const improvements = ref([])
const updateInterval = ref(null)

// 计算属性
const circumference = 2 * Math.PI * 50

const criticalIssues = computed(() => issues.value.filter(i => i.severity === 'critical').length)
const highIssues = computed(() => issues.value.filter(i => i.severity === 'high').length)
const mediumIssues = computed(() => issues.value.filter(i => i.severity === 'medium').length)
const lowIssues = computed(() => issues.value.filter(i => i.severity === 'low').length)

const totalFixes = computed(() => fixes.value.length)
const successfulFixes = computed(() => fixes.value.filter(f => f.success).length)
const fixProgress = computed(() => {
  if (totalFixes.value === 0) return 0
  return Math.round((successfulFixes.value / totalFixes.value) * 100)
})

const recentIssues = computed(() => {
  return issues.value
    .slice(-5)
    .reverse()
    .map((issue, index) => ({
      ...issue,
      id: index,
      fixed: fixes.value.some(f => f.issue === issue.name && f.success)
    }))
})

// 获取完美度等级
const getPerfectionClass = () => {
  if (perfectionLevel.value === 100) return 'perfect'
  if (perfectionLevel.value >= 95) return 'excellent'
  if (perfectionLevel.value >= 85) return 'good'
  if (perfectionLevel.value >= 70) return 'fair'
  return 'poor'
}

// 获取完美度颜色
const getPerfectionColor = () => {
  if (perfectionLevel.value === 100) return '#722ed1'
  if (perfectionLevel.value >= 95) return '#52c41a'
  if (perfectionLevel.value >= 85) return '#1890ff'
  if (perfectionLevel.value >= 70) return '#faad14'
  return '#ff4d4f'
}

// 获取完美度标签
const getPerfectionLabel = () => {
  if (perfectionLevel.value === 100) return '完美'
  if (perfectionLevel.value >= 95) return '优秀'
  if (perfectionLevel.value >= 85) return '良好'
  if (perfectionLevel.value >= 70) return '及格'
  return '需改进'
}

// 获取进度条颜色
const getProgressColor = () => {
  if (fixProgress.value >= 90) return '#52c41a'
  if (fixProgress.value >= 70) return '#1890ff'
  if (fixProgress.value >= 50) return '#faad14'
  return '#ff4d4f'
}

// 切换最小化
const toggleMinimize = () => {
  minimized.value = !minimized.value
}

// 启动测试
const startTesting = async () => {
  if (window.perfectionistTester) {
    await window.perfectionistTester.startPerfectionistTesting()
    isRunning.value = true
    message.success('完美主义测试已启动')
  } else {
    message.error('完美主义测试器未加载')
  }
}

// 停止测试
const stopTesting = () => {
  if (window.perfectionistTester) {
    window.perfectionistTester.stopPerfectionistTesting()
    isRunning.value = false
    message.info('完美主义测试已停止')
  }
}

// 快速修复
const runQuickFix = async () => {
  if (window.perfectionistTester) {
    try {
      await window.perfectionistTester.fixIssuesImmediately()
      message.success('快速修复完成')
      updateStatus()
    } catch (error) {
      message.error('快速修复失败: ' + error.message)
    }
  }
}

// 生成报告
const generateReport = () => {
  if (window.perfectionistTester) {
    const report = window.perfectionistTester.generatePerfectionistReport()
    console.log('💎 完美主义测试报告:', report)
    message.info('完美主义测试报告已输出到控制台')
  }
}

// 重新检测
const resetPerfection = async () => {
  if (window.perfectionistTester) {
    perfectionLevel.value = 0
    issues.value = []
    fixes.value = []
    improvements.value = []
    
    await window.perfectionistTester.detectIssues()
    updateStatus()
    message.info('重新检测完成')
  }
}

// 更新状态
const updateStatus = () => {
  if (window.perfectionistTester) {
    const tester = window.perfectionistTester
    
    isRunning.value = tester.isRunning
    perfectionLevel.value = tester.perfectionLevel || 0
    issues.value = [...(tester.issues || [])]
    fixes.value = [...(tester.fixes || [])]
    improvements.value = [...(tester.improvements || [])]
  }
}

// 组件挂载
onMounted(() => {
  // 定期更新状态
  updateInterval.value = setInterval(updateStatus, 2000)
  
  // 初始状态检查
  setTimeout(() => {
    if (window.perfectionistTester && window.perfectionistTester.isRunning) {
      isRunning.value = true
    }
  }, 3000)
})

// 组件卸载
onUnmounted(() => {
  if (updateInterval.value) {
    clearInterval(updateInterval.value)
  }
})
</script>

<style scoped>
.perfectionist-status {
  position: fixed;
  top: 20px;
  left: 350px;
  z-index: 9998;
  width: 300px;
  border: 2px solid #722ed1;
  border-radius: 12px;
  background: white;
  box-shadow: 0 8px 32px rgb(0 0 0 / 15%);
  transition: all 0.3s ease;
}

.perfectionist-status.minimized {
  width: 180px;
}

.status-header {
  display: flex;
  padding: 12px 16px;
  border-radius: 10px 10px 0 0;
  color: white;
  background: linear-gradient(135deg, #722ed1 0%, #eb2f96 100%);
  justify-content: space-between;
  align-items: center;
  cursor: pointer;
}

.status-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
}

.status-title .anticon.perfect {
  color: #ffd700;
  animation: sparkle 2s infinite;
}

@keyframes sparkle {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

.perfection-level {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: bold;
  background: rgb(255 255 255 / 20%);
}

.perfection-level.perfect {
  color: #722ed1;
  background: #ffd700;
}

.perfection-level.excellent {
  background: #52c41a;
}

.perfection-level.good {
  background: #1890ff;
}

.perfection-level.fair {
  background: #faad14;
}

.perfection-level.poor {
  background: #ff4d4f;
}

.status-controls {
  display: flex;
  gap: 4px;
}

.status-content {
  padding: 16px;
}

.perfection-dashboard {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
}

.perfection-circle {
  position: relative;
  width: 120px;
  height: 120px;
}

.perfection-text {
  position: absolute;
  top: 50%;
  left: 50%;
  text-align: center;
  transform: translate(-50%, -50%);
}

.perfection-number {
  font-size: 24px;
  font-weight: bold;
  color: #722ed1;
}

.perfection-label {
  font-size: 12px;
  color: #666;
}

.issue-stats {
  margin-bottom: 16px;
}

.stat-row {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
}

.stat-item {
  display: flex;
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  background: #f5f5f5;
  align-items: center;
  gap: 4px;
}

.stat-item.critical {
  color: #ff4d4f;
  background: #fff2f0;
}

.stat-item.high {
  color: #fa8c16;
  background: #fff7e6;
}

.stat-item.medium {
  color: #1890ff;
  background: #e6f7ff;
}

.stat-item.low {
  color: #52c41a;
  background: #f6ffed;
}

.fix-progress {
  margin-bottom: 16px;
}

.progress-title {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.fix-stats {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

.recent-issues {
  margin-bottom: 16px;
}

.issues-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.issues-list {
  max-height: 120px;
  overflow-y: auto;
}

.issue-item {
  display: flex;
  align-items: center;
  padding: 6px;
  border-radius: 4px;
  margin-bottom: 4px;
  background: #fafafa;
}

.issue-item.critical {
  background: #fff2f0;
}

.issue-item.high {
  background: #fff7e6;
}

.issue-item.medium {
  background: #e6f7ff;
}

.issue-item.low {
  background: #f6ffed;
}

.issue-icon {
  margin-right: 8px;
  font-size: 12px;
}

.issue-content {
  flex: 1;
}

.issue-name {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 2px;
}

.issue-category {
  font-size: 10px;
  color: #999;
}

.issue-status {
  font-size: 12px;
}

.improvements {
  margin-bottom: 16px;
}

.improvements-title {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 8px;
  color: #333;
}

.improvements-list {
  max-height: 80px;
  overflow-y: auto;
}

.improvement-item {
  padding: 6px;
  border-radius: 4px;
  margin-bottom: 4px;
  background: #f0f9ff;
  border-left: 3px solid #1890ff;
}

.improvement-category {
  font-size: 11px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 2px;
}

.improvement-text {
  font-size: 11px;
  color: #666;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
}
</style>
