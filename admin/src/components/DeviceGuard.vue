<template>
  <div v-if="!isDesktop" class="device-restriction">
    <div class="restriction-container">
      <div class="restriction-icon">
        <svg viewBox="0 0 100 100" class="desktop-icon">
          <rect x="10" y="20" width="80" height="50" rx="4" fill="#7c3aed" />
          <rect x="15" y="25" width="70" height="35" fill="white" />
          <rect x="35" y="75" width="30" height="3" fill="#7c3aed" />
          <rect x="25" y="80" width="50" height="8" rx="4" fill="#7c3aed" />
        </svg>
      </div>
      
      <h1 class="restriction-title">仅支持电脑端访问</h1>
      <p class="restriction-message">
        壹心堂管理系统专为电脑端设计，为确保最佳使用体验，请使用电脑浏览器访问。
      </p>
      
      <div class="restriction-requirements">
        <h3>系统要求：</h3>
        <ul>
          <li>✅ 电脑或笔记本电脑</li>
          <li>✅ 屏幕分辨率：1366×768 或更高</li>
          <li>✅ 现代浏览器（Chrome、Firefox、Safari、Edge）</li>
          <li>❌ 不支持手机和平板设备</li>
        </ul>
      </div>
      
      <div class="restriction-footer">
        <p>© 2025 壹心堂 Yixintang 管理系统-专业版</p>
      </div>
    </div>
  </div>
  
  <div v-else class="desktop-content" :class="resolutionClass">
    <slot />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 响应式数据
const screenWidth = ref(window.innerWidth)
const screenHeight = ref(window.innerHeight)
const userAgent = ref(navigator.userAgent)

// 设备检测
const isDesktop = computed(() => {
  const ua = userAgent.value.toLowerCase()
  
  // 检测移动设备
  const isMobile = /android|webos|iphone|ipad|ipod|blackberry|iemobile|opera mini/i.test(ua)
  
  // 检测平板设备
  const isTablet = /ipad|android(?!.*mobile)|tablet/i.test(ua)
  
  // 检测最小分辨率要求
  const hasMinResolution = screenWidth.value >= 1366 && screenHeight.value >= 768
  
  // 只有非移动设备、非平板设备且满足最小分辨率要求才算桌面设备
  return !isMobile && !isTablet && hasMinResolution
})

// 分辨率分类
const resolutionClass = computed(() => {
  const width = screenWidth.value
  const height = screenHeight.value
  
  if (width >= 2560) {
    return 'resolution-4k'        // 4K及以上
  } else if (width >= 1920) {
    return 'resolution-fhd'       // 1920×1080 Full HD
  } else if (width >= 1600) {
    return 'resolution-hd-plus'   // 1600×900 HD+
  } else if (width >= 1366) {
    return 'resolution-hd'        // 1366×768 HD
  } else {
    return 'resolution-low'       // 低于HD
  }
})

// 窗口大小变化监听
const handleResize = () => {
  screenWidth.value = window.innerWidth
  screenHeight.value = window.innerHeight
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  
  // 输出设备信息到控制台
  console.log('🖥️ 设备检测信息:')
  console.log('屏幕分辨率:', `${screenWidth.value}×${screenHeight.value}`)
  console.log('用户代理:', userAgent.value)
  console.log('是否为桌面设备:', isDesktop.value)
  console.log('分辨率等级:', resolutionClass.value)
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.device-restriction {
  display: flex;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #f3e8ff 0%, #e9d5ff 25%, #ddd6fe 50%, #c4b5fd 75%, #a855f7 100%);
  align-items: center;
  justify-content: center;

  .restriction-container {
    max-width: 600px;
    padding: 48px;
    border: 1px solid rgb(255 255 255 / 30%);
    border-radius: 24px;
    text-align: center;
    background: rgb(255 255 255 / 95%);
    box-shadow: 
      0 20px 40px rgb(168 85 247 / 15%),
      0 8px 32px rgb(139 92 246 / 10%);
    backdrop-filter: blur(20px);

    .restriction-icon {
      margin-bottom: 32px;

      .desktop-icon {
        width: 120px;
        height: 120px;
        margin: 0 auto;
      }
    }

    .restriction-title {
      font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
      font-size: 32px;
      font-weight: 600;
      color: #7c3aed;
      margin-bottom: 16px;
    }

    .restriction-message {
      font-size: 18px;
      line-height: 1.6;
      color: #6b7280;
      margin-bottom: 32px;
    }

    .restriction-requirements {
      padding: 24px;
      border-radius: 12px;
      text-align: left;
      background: rgb(124 58 237 / 5%);
      margin-bottom: 32px;

      h3 {
        font-size: 18px;
        font-weight: 600;
        color: #7c3aed;
        margin-bottom: 16px;
      }

      ul {
        margin: 0;
        padding: 0;
        list-style: none;

        li {
          font-size: 16px;
          color: #4b5563;
          margin-bottom: 8px;
          padding-left: 8px;
        }
      }
    }

    .restriction-footer {
      border-top: 1px solid rgb(124 58 237 / 10%);
      padding-top: 24px;
      
      p {
        margin: 0;
        font-size: 14px;
        color: #9ca3af;
      }
    }
  }
}

// 桌面端分辨率适配
.desktop-content {
  min-height: 100vh;
  
  // HD 1366×768
  &.resolution-hd {
    font-size: 14px;
    
    :deep(.ant-layout-sider) {
      width: 200px !important;
    }
    
    :deep(.ant-layout-header) {
      height: 28px !important;
      line-height: 28px !important;
    }
  }
  
  // HD+ 1600×900
  &.resolution-hd-plus {
    font-size: 15px;
    
    :deep(.ant-layout-sider) {
      width: 220px !important;
    }
    
    :deep(.ant-layout-header) {
      height: 30px !important;
      line-height: 30px !important;
    }
  }
  
  // Full HD 1920×1080
  &.resolution-fhd {
    font-size: 16px;
    
    :deep(.ant-layout-sider) {
      width: 240px !important;
    }
    
    :deep(.ant-layout-header) {
      height: 32px !important;
      line-height: 32px !important;
    }
  }
  
  // 4K 2560×1440+
  &.resolution-4k {
    font-size: 18px;
    
    :deep(.ant-layout-sider) {
      width: 280px !important;
    }
    
    :deep(.ant-layout-header) {
      height: 72px !important;
      line-height: 72px !important;
    }
    
    :deep(.ant-card) {
      .ant-card-head {
        font-size: 20px;
      }
      
      .ant-card-body {
        font-size: 16px;
      }
    }
  }
  
  // 低分辨率警告
  &.resolution-low {
    position: relative;
    
    &::before {
      position: fixed;
      top: 0;
      right: 0;
      left: 0;
      z-index: 9999;
      padding: 8px;
      font-size: 14px;
      font-weight: 500;
      text-align: center;
      color: #92400e;
      background: #fbbf24;
      content: '⚠️ 当前分辨率过低，建议使用1366×768或更高分辨率以获得最佳体验';
    }
  }
}
</style>
