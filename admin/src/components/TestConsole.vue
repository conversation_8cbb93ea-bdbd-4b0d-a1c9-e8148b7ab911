<template>
  <div v-if="showConsole" class="test-console">
    <div class="console-header">
      <h3>🏆 IT全栈大奖级别测试控制台</h3>
      <div class="console-controls">
        <a-button @click="runAllTests" type="primary" :loading="testing">
          <PlayCircleOutlined />
          运行完整测试
        </a-button>
        <a-button @click="runQuickTest" :loading="quickTesting">
          <ThunderboltOutlined />
          快速测试
        </a-button>
        <a-button @click="toggleRealTimeFix" :type="realTimeFixing ? 'danger' : 'default'">
          <ToolOutlined />
          {{ realTimeFixing ? '停止' : '启动' }}实时修复
        </a-button>
        <a-button @click="showConsole = false" size="small">
          <CloseOutlined />
        </a-button>
      </div>
    </div>

    <div class="console-content">
      <a-tabs v-model:activeKey="activeTab">
        <a-tab-pane key="status" tab="系统状态">
          <div class="status-grid">
            <div class="status-card" :class="getStatusClass('frontend')">
              <div class="status-icon">🎨</div>
              <div class="status-info">
                <div class="status-title">前端状态</div>
                <div class="status-value">{{ systemStatus.frontend }}</div>
              </div>
            </div>
            
            <div class="status-card" :class="getStatusClass('backend')">
              <div class="status-icon">⚙️</div>
              <div class="status-info">
                <div class="status-title">后端状态</div>
                <div class="status-value">{{ systemStatus.backend }}</div>
              </div>
            </div>
            
            <div class="status-card" :class="getStatusClass('database')">
              <div class="status-icon">💾</div>
              <div class="status-info">
                <div class="status-title">数据库状态</div>
                <div class="status-value">{{ systemStatus.database }}</div>
              </div>
            </div>
            
            <div class="status-card" :class="getStatusClass('api')">
              <div class="status-icon">📡</div>
              <div class="status-info">
                <div class="status-title">API状态</div>
                <div class="status-value">{{ systemStatus.api }}</div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="tests" tab="测试结果">
          <div class="test-results">
            <div v-if="testResults.length === 0" class="no-results">
              <InboxOutlined style="font-size: 48px; color: #ccc;" />
              <p>暂无测试结果，点击上方按钮开始测试</p>
            </div>
            
            <div v-else class="results-list">
              <div v-for="result in testResults" :key="result.id" class="result-item" :class="result.status">
                <div class="result-icon">
                  <CheckCircleOutlined v-if="result.status === 'success'" />
                  <ExclamationCircleOutlined v-else-if="result.status === 'warning'" />
                  <CloseCircleOutlined v-else />
                </div>
                <div class="result-content">
                  <div class="result-title">{{ result.test }}</div>
                  <div class="result-details">{{ result.details }}</div>
                  <div class="result-time">{{ formatTime(result.timestamp) }}</div>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="fixes" tab="自动修复">
          <div class="fixes-list">
            <div v-if="fixes.length === 0" class="no-fixes">
              <ToolOutlined style="font-size: 48px; color: #ccc;" />
              <p>暂无修复记录</p>
            </div>
            
            <div v-else>
              <div v-for="fix in fixes" :key="fix.id" class="fix-item">
                <div class="fix-icon">
                  <CheckCircleOutlined v-if="fix.fixed" style="color: #52c41a;" />
                  <ExclamationCircleOutlined v-else style="color: #faad14;" />
                </div>
                <div class="fix-content">
                  <div class="fix-title">{{ fix.issue }}</div>
                  <div class="fix-status">{{ fix.fixed ? '已自动修复' : '需要手动处理' }}</div>
                  <div class="fix-time">{{ formatTime(fix.timestamp) }}</div>
                </div>
              </div>
            </div>
          </div>
        </a-tab-pane>

        <a-tab-pane key="manual" tab="手动测试">
          <div class="manual-tests">
            <h4>菜单测试</h4>
            <div class="test-buttons">
              <a-button v-for="menu in menuItems" :key="menu.key" 
                       @click="testMenuClick(menu)" size="small" style="margin: 4px;">
                测试 {{ menu.name }}
              </a-button>
            </div>
            
            <h4>页面测试</h4>
            <div class="test-buttons">
              <a-button @click="testPageLoad('/dashboard')" size="small" style="margin: 4px;">
                测试仪表盘加载
              </a-button>
              <a-button @click="testPageLoad('/appointments')" size="small" style="margin: 4px;">
                测试预约页面
              </a-button>
              <a-button @click="testPageLoad('/customers')" size="small" style="margin: 4px;">
                测试客户页面
              </a-button>
            </div>
            
            <h4>API测试</h4>
            <div class="test-buttons">
              <a-button @click="testAPI('/api/v1/services/')" size="small" style="margin: 4px;">
                测试服务API
              </a-button>
              <a-button @click="testAPI('/api/v1/customers/')" size="small" style="margin: 4px;">
                测试客户API
              </a-button>
              <a-button @click="testAPI('/api/v1/appointments/')" size="small" style="margin: 4px;">
                测试预约API
              </a-button>
            </div>
          </div>
        </a-tab-pane>
      </a-tabs>
    </div>
  </div>

  <!-- 浮动按钮 -->
  <div v-if="!showConsole" class="console-toggle" @click="showConsole = true">
    <BugOutlined />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { message } from 'ant-design-vue'
import {
  PlayCircleOutlined,
  ThunderboltOutlined,
  ToolOutlined,
  CloseOutlined,
  CheckCircleOutlined,
  ExclamationCircleOutlined,
  CloseCircleOutlined,
  InboxOutlined,
  BugOutlined
} from '@ant-design/icons-vue'

// 响应式数据
const showConsole = ref(false)
const activeTab = ref('status')
const testing = ref(false)
const quickTesting = ref(false)
const realTimeFixing = ref(false)

const router = useRouter()

// 系统状态
const systemStatus = reactive({
  frontend: '正常',
  backend: '检测中...',
  database: '检测中...',
  api: '检测中...'
})

// 测试结果
const testResults = ref([])

// 修复记录
const fixes = ref([])

// 菜单项
const menuItems = [
  { key: 'dashboard', name: '仪表盘', route: '/dashboard' },
  { key: 'appointments', name: '预约管理', route: '/appointments' },
  { key: 'customers', name: '客户管理', route: '/customers' },
  { key: 'therapists', name: '技师管理', route: '/therapists' },
  { key: 'services', name: '服务管理', route: '/services' },
  { key: 'finance', name: '财务管理', route: '/finance' }
]

// 运行完整测试
const runAllTests = async () => {
  testing.value = true
  
  try {
    if (window.comprehensiveSystemTester) {
      await window.comprehensiveSystemTester.runComprehensiveTests()
      message.success('完整测试已完成，请查看测试结果')
    } else {
      message.error('测试工具未加载，请刷新页面重试')
    }
  } catch (error) {
    message.error('测试执行失败: ' + error.message)
  } finally {
    testing.value = false
  }
}

// 运行快速测试
const runQuickTest = async () => {
  quickTesting.value = true
  
  try {
    // 快速检查关键功能
    await checkSystemStatus()
    await quickMenuTest()
    
    message.success('快速测试完成')
  } catch (error) {
    message.error('快速测试失败: ' + error.message)
  } finally {
    quickTesting.value = false
  }
}

// 切换实时修复
const toggleRealTimeFix = () => {
  if (window.realTimeFixer) {
    if (realTimeFixing.value) {
      window.realTimeFixer.stopRealTimeMonitoring()
      realTimeFixing.value = false
      message.info('实时修复已停止')
    } else {
      window.realTimeFixer.startRealTimeMonitoring()
      realTimeFixing.value = true
      message.info('实时修复已启动')
    }
  } else {
    message.error('实时修复工具未加载')
  }
}

// 检查系统状态
const checkSystemStatus = async () => {
  // 检查后端状态
  try {
    console.log('🔍 检查后端健康状态...');
    const response = await fetch('http://localhost:8000/health/', {
      timeout: 3000
    });
    systemStatus.backend = response.ok ? '正常' : '异常';
    console.log('✅ 后端状态:', systemStatus.backend);
  } catch (error) {
    console.warn('⚠️ 后端服务离线:', error.message);
    systemStatus.backend = '离线';
  }

  // 检查API状态
  try {
    console.log('🔍 检查API服务状态...');
    const response = await fetch('http://localhost:8000/api/v1/services/', {
      timeout: 3000
    });
    systemStatus.api = response.ok ? '正常' : '异常';
    console.log('✅ API状态:', systemStatus.api);
  } catch (error) {
    console.warn('⚠️ API服务离线:', error.message);
    systemStatus.api = '离线';
  }
  
  // 检查数据库状态（通过API）
  systemStatus.database = systemStatus.api === '正常' ? '正常' : '异常'
}

// 快速菜单测试
const quickMenuTest = async () => {
  for (const menu of menuItems) {
    try {
      await router.push(menu.route)
      await new Promise(resolve => setTimeout(resolve, 100))
      
      testResults.value.push({
        id: Date.now() + Math.random(),
        test: `${menu.name} 菜单`,
        status: 'success',
        details: '导航成功',
        timestamp: new Date()
      })
    } catch (error) {
      testResults.value.push({
        id: Date.now() + Math.random(),
        test: `${menu.name} 菜单`,
        status: 'error',
        details: error.message,
        timestamp: new Date()
      })
    }
  }
}

// 测试菜单点击
const testMenuClick = async (menu) => {
  try {
    await router.push(menu.route)
    message.success(`${menu.name} 菜单测试成功`)
    
    testResults.value.push({
      id: Date.now(),
      test: `${menu.name} 手动测试`,
      status: 'success',
      details: '手动测试通过',
      timestamp: new Date()
    })
  } catch (error) {
    message.error(`${menu.name} 菜单测试失败`)
    
    testResults.value.push({
      id: Date.now(),
      test: `${menu.name} 手动测试`,
      status: 'error',
      details: error.message,
      timestamp: new Date()
    })
  }
}

// 测试页面加载
const testPageLoad = async (route) => {
  try {
    await router.push(route)
    await new Promise(resolve => setTimeout(resolve, 500))
    
    message.success(`页面 ${route} 加载成功`)
    
    testResults.value.push({
      id: Date.now(),
      test: `页面加载 ${route}`,
      status: 'success',
      details: '页面加载正常',
      timestamp: new Date()
    })
  } catch (error) {
    message.error(`页面 ${route} 加载失败`)
    
    testResults.value.push({
      id: Date.now(),
      test: `页面加载 ${route}`,
      status: 'error',
      details: error.message,
      timestamp: new Date()
    })
  }
}

// 测试API
const testAPI = async (endpoint) => {
  try {
    const response = await fetch(`http://localhost:8000${endpoint}`)
    
    if (response.ok) {
      message.success(`API ${endpoint} 测试成功`)
      
      testResults.value.push({
        id: Date.now(),
        test: `API ${endpoint}`,
        status: 'success',
        details: `状态码: ${response.status}`,
        timestamp: new Date()
      })
    } else {
      throw new Error(`状态码: ${response.status}`)
    }
  } catch (error) {
    message.error(`API ${endpoint} 测试失败`)
    
    testResults.value.push({
      id: Date.now(),
      test: `API ${endpoint}`,
      status: 'error',
      details: error.message,
      timestamp: new Date()
    })
  }
}

// 获取状态样式类
const getStatusClass = (type) => {
  const status = systemStatus[type]
  if (status === '正常') return 'status-success'
  if (status === '异常') return 'status-error'
  return 'status-warning'
}

// 格式化时间
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleTimeString()
}

// 组件挂载时检查系统状态
onMounted(() => {
  checkSystemStatus()
  
  // 检查是否有实时修复工具
  if (window.realTimeFixer) {
    realTimeFixing.value = window.realTimeFixer.isMonitoring
  }
})
</script>

<style scoped>
.test-console {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 9999;
  width: 600px;
  max-height: 80vh;
  border-radius: 8px;
  overflow: hidden;
  background: white;
  box-shadow: 0 8px 32px rgb(0 0 0 / 20%);
}

.console-header {
  display: flex;
  padding: 16px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  justify-content: space-between;
  align-items: center;
}

.console-header h3 {
  margin: 0;
  font-size: 16px;
}

.console-controls {
  display: flex;
  gap: 8px;
}

.console-content {
  max-height: 60vh;
  overflow-y: auto;
  padding: 16px;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
  margin-bottom: 16px;
}

.status-card {
  display: flex;
  padding: 12px;
  border: 1px solid #f0f0f0;
  border-radius: 6px;
  align-items: center;
}

.status-card.status-success {
  background: #f6ffed;
  border-color: #b7eb8f;
}

.status-card.status-warning {
  background: #fffbe6;
  border-color: #ffe58f;
}

.status-card.status-error {
  background: #fff2f0;
  border-color: #ffccc7;
}

.status-icon {
  font-size: 24px;
  margin-right: 12px;
}

.status-info {
  flex: 1;
}

.status-title {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.status-value {
  font-size: 14px;
  font-weight: 600;
}

.test-results, .fixes-list {
  max-height: 300px;
  overflow-y: auto;
}

.no-results, .no-fixes {
  padding: 40px;
  text-align: center;
  color: #999;
}

.result-item, .fix-item {
  display: flex;
  align-items: flex-start;
  padding: 12px;
  border-bottom: 1px solid #f0f0f0;
}

.result-item.success .result-icon {
  color: #52c41a;
}

.result-item.warning .result-icon {
  color: #faad14;
}

.result-item.error .result-icon {
  color: #ff4d4f;
}

.result-icon, .fix-icon {
  margin-right: 12px;
  margin-top: 2px;
}

.result-content, .fix-content {
  flex: 1;
}

.result-title, .fix-title {
  font-weight: 600;
  margin-bottom: 4px;
}

.result-details, .fix-status {
  font-size: 12px;
  color: #666;
  margin-bottom: 4px;
}

.result-time, .fix-time {
  font-size: 11px;
  color: #999;
}

.manual-tests h4 {
  margin: 16px 0 8px;
  font-size: 14px;
}

.test-buttons {
  margin-bottom: 16px;
}

.console-toggle {
  display: flex;
  position: fixed;
  right: 20px;
  bottom: 20px;
  z-index: 9998;
  width: 56px;
  height: 56px;
  border-radius: 50%;
  font-size: 24px;
  color: white;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  box-shadow: 0 4px 16px rgb(0 0 0 / 20%);
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.console-toggle:hover {
  transform: scale(1.1);
}
</style>
