<template>
  <Teleport to="body">
    <div v-if="notifications.length > 0" class="sci-fi-notification-container">
      <TransitionGroup name="notification" tag="div">
        <div
          v-for="notification in notifications"
          :key="notification.id"
          :class="[
            'sci-fi-notification',
            `notification-${notification.type}`,
            { 'notification-entering': notification.entering }
          ]"
        >
          <!-- 科幻边框效果 -->
          <div class="notification-border">
            <div class="border-corner top-left"></div>
            <div class="border-corner top-right"></div>
            <div class="border-corner bottom-left"></div>
            <div class="border-corner bottom-right"></div>
            <div class="border-line top"></div>
            <div class="border-line right"></div>
            <div class="border-line bottom"></div>
            <div class="border-line left"></div>
          </div>
          
          <!-- 扫描线效果 -->
          <div class="scan-line"></div>
          
          <!-- 内容区域 -->
          <div class="notification-content">
            <div class="notification-icon">
              <div class="icon-glow">
                <span v-if="notification.type === 'error'">⚠️</span>
                <span v-else-if="notification.type === 'success'">✅</span>
                <span v-else-if="notification.type === 'warning'">⚡</span>
                <span v-else>ℹ️</span>
              </div>
            </div>
            
            <div class="notification-text">
              <div class="notification-title">{{ notification.title }}</div>
              <div v-if="notification.message" class="notification-message">{{ notification.message }}</div>
            </div>
            
            <!-- 进度条 -->
            <div class="notification-progress">
              <div 
                class="progress-bar" 
                :style="{ animationDuration: notification.duration + 'ms' }"
              ></div>
            </div>
          </div>
          
          <!-- 关闭按钮 -->
          <button class="notification-close" @click="removeNotification(notification.id)">
            <span class="close-icon">×</span>
          </button>
          
          <!-- 全息效果 -->
          <div class="hologram-effect"></div>
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup>
import { ref, reactive } from 'vue'

// 通知列表
const notifications = ref([])

// 通知ID计数器
let notificationId = 0

// 添加通知
const addNotification = (options) => {
  const id = ++notificationId
  const notification = {
    id,
    type: options.type || 'info',
    title: options.title || '系统提示',
    message: options.message || '',
    duration: options.duration || 4000,
    entering: true
  }
  
  notifications.value.push(notification)
  
  // 入场动画完成后移除entering状态
  setTimeout(() => {
    const notif = notifications.value.find(n => n.id === id)
    if (notif) {
      notif.entering = false
    }
  }, 100)
  
  // 自动移除
  setTimeout(() => {
    removeNotification(id)
  }, notification.duration)
  
  return id
}

// 移除通知
const removeNotification = (id) => {
  const index = notifications.value.findIndex(n => n.id === id)
  if (index > -1) {
    notifications.value.splice(index, 1)
  }
}

// 清空所有通知
const clearAll = () => {
  notifications.value = []
}

// 暴露方法给外部使用
defineExpose({
  addNotification,
  removeNotification,
  clearAll,
  // 便捷方法
  error: (title, message, duration) => addNotification({ type: 'error', title, message, duration }),
  success: (title, message, duration) => addNotification({ type: 'success', title, message, duration }),
  warning: (title, message, duration) => addNotification({ type: 'warning', title, message, duration }),
  info: (title, message, duration) => addNotification({ type: 'info', title, message, duration })
})
</script>

<style scoped>
/* 🚀 科幻通知容器 */
.sci-fi-notification-container {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 10000;
  pointer-events: none;
}

/* 🎯 科幻通知主体 */
.sci-fi-notification {
  position: relative;
  width: 380px;
  border-radius: 8px;
  overflow: hidden;
  background: linear-gradient(135deg, 
    rgb(0 20 40 / 95%) 0%,
    rgb(0 30 60 / 95%) 50%,
    rgb(0 40 80 / 95%) 100%
  );
  box-shadow: 
    0 8px 32px rgb(0 100 255 / 30%),
    inset 0 1px 0 rgb(255 255 255 / 10%),
    inset 0 -1px 0 rgb(0 0 0 / 20%);
  transform: translateX(100%);
  margin-bottom: 16px;
  backdrop-filter: blur(20px);
  pointer-events: auto;
  animation: slideIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

/* 🎨 不同类型的颜色主题 */
.notification-error {
  background: linear-gradient(135deg, 
    rgb(40 0 20 / 95%) 0%,
    rgb(60 0 30 / 95%) 50%,
    rgb(80 0 40 / 95%) 100%
  );
  box-shadow: 
    0 8px 32px rgb(255 50 100 / 30%),
    inset 0 1px 0 rgb(255 255 255 / 10%),
    inset 0 -1px 0 rgb(0 0 0 / 20%);
}

.notification-success {
  background: linear-gradient(135deg, 
    rgb(0 40 20 / 95%) 0%,
    rgb(0 60 30 / 95%) 50%,
    rgb(0 80 40 / 95%) 100%
  );
  box-shadow: 
    0 8px 32px rgb(50 255 100 / 30%),
    inset 0 1px 0 rgb(255 255 255 / 10%),
    inset 0 -1px 0 rgb(0 0 0 / 20%);
}

.notification-warning {
  background: linear-gradient(135deg, 
    rgb(40 30 0 / 95%) 0%,
    rgb(60 45 0 / 95%) 50%,
    rgb(80 60 0 / 95%) 100%
  );
  box-shadow: 
    0 8px 32px rgb(255 200 50 / 30%),
    inset 0 1px 0 rgb(255 255 255 / 10%),
    inset 0 -1px 0 rgb(0 0 0 / 20%);
}

/* ⚡ 科幻边框效果 */
.notification-border {
  position: absolute;
  inset: 0;
  pointer-events: none;
}

.border-corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 2px solid;
  border-color: rgb(0 200 255 / 80%);
}

.border-corner.top-left {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
  border-top-left-radius: 8px;
}

.border-corner.top-right {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
  border-top-right-radius: 8px;
}

.border-corner.bottom-left {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
  border-bottom-left-radius: 8px;
}

.border-corner.bottom-right {
  right: 0;
  bottom: 0;
  border-left: none;
  border-top: none;
  border-bottom-right-radius: 8px;
}

.border-line {
  position: absolute;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgb(0 200 255 / 60%) 50%, 
    transparent 100%
  );
  animation: borderPulse 2s ease-in-out infinite;
}

.border-line.top {
  top: 0;
  right: 20px;
  left: 20px;
  height: 2px;
}

.border-line.bottom {
  right: 20px;
  bottom: 0;
  left: 20px;
  height: 2px;
}

.border-line.left {
  top: 20px;
  bottom: 20px;
  left: 0;
  width: 2px;
  background: linear-gradient(180deg, 
    transparent 0%, 
    rgb(0 200 255 / 60%) 50%, 
    transparent 100%
  );
}

.border-line.right {
  top: 20px;
  right: 0;
  bottom: 20px;
  width: 2px;
  background: linear-gradient(180deg, 
    transparent 0%, 
    rgb(0 200 255 / 60%) 50%, 
    transparent 100%
  );
}

/* 📡 扫描线效果 */
.scan-line {
  position: absolute;
  top: 0;
  right: 0;
  left: 0;
  height: 2px;
  background: linear-gradient(90deg, 
    transparent 0%, 
    rgb(0 255 200 / 80%) 50%, 
    transparent 100%
  );
  animation: scanLine 3s linear infinite;
}

/* 📝 内容区域 */
.notification-content {
  display: flex;
  position: relative;
  z-index: 2;
  padding: 16px 50px 16px 16px;
  align-items: flex-start;
  gap: 12px;
}

/* 🎭 图标区域 */
.notification-icon {
  display: flex;
  position: relative;
  width: 32px;
  height: 32px;
  flex-shrink: 0;
  align-items: center;
  justify-content: center;
}

.icon-glow {
  position: relative;
  font-size: 18px;
  animation: iconGlow 2s ease-in-out infinite;
}

.icon-glow::before {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: radial-gradient(circle, rgb(0 200 255 / 30%) 0%, transparent 70%);
  transform: translate(-50%, -50%);
  content: '';
  animation: iconPulse 2s ease-in-out infinite;
}

/* 📄 文本区域 */
.notification-text {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: 14px;
  font-weight: 600;
  color: #fff;
  margin-bottom: 4px;
  text-shadow: 0 0 10px rgb(0 200 255 / 50%);
}

.notification-message {
  font-size: 12px;
  line-height: 1.4;
  color: rgb(255 255 255 / 80%);
}

/* 📊 进度条 */
.notification-progress {
  position: absolute;
  right: 0;
  bottom: 0;
  left: 0;
  height: 3px;
  overflow: hidden;
  background: rgb(0 0 0 / 30%);
}

.progress-bar {
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, 
    rgb(0 200 255 / 80%) 0%, 
    rgb(0 255 200 / 80%) 100%
  );
  transform: translateX(-100%);
  animation: progressBar linear forwards;
}

/* ❌ 关闭按钮 */
.notification-close {
  display: flex;
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 3;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: rgb(255 255 255 / 10%);
  transition: all 0.3s ease;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}

.notification-close:hover {
  background: rgb(255 100 100 / 30%);
  transform: scale(1.1);
}

.close-icon {
  font-size: 14px;
  font-weight: bold;
  color: rgb(255 255 255 / 80%);
}

/* 🌈 全息效果 */
.hologram-effect {
  position: absolute;
  inset: 0;
  background: linear-gradient(45deg, 
    transparent 30%, 
    rgb(0 255 200 / 10%) 50%, 
    transparent 70%
  );
  animation: hologramShift 4s ease-in-out infinite;
  pointer-events: none;
}

/* 🎬 动画定义 */
@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideOut {
  from {
    opacity: 1;
    transform: translateX(0);
  }

  to {
    opacity: 0;
    transform: translateX(100%);
  }
}

@keyframes borderPulse {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes scanLine {
  0% { transform: translateY(-100%); }
  100% { transform: translateY(calc(100vh + 100%)); }
}

@keyframes iconGlow {
  0%, 100% { text-shadow: 0 0 10px rgb(0 200 255 / 50%); }
  50% { text-shadow: 0 0 20px rgb(0 200 255 / 80%), 0 0 30px rgb(0 200 255 / 60%); }
}

@keyframes iconPulse {
  0%, 100% { opacity: 0.3; transform: translate(-50%, -50%) scale(1); }
  50% { opacity: 0.6; transform: translate(-50%, -50%) scale(1.2); }
}

@keyframes progressBar {
  from { transform: translateX(-100%); }
  to { transform: translateX(0); }
}

@keyframes hologramShift {
  0%, 100% { transform: translateX(-100%); }
  50% { transform: translateX(100%); }
}

/* 🔄 过渡动画 */
.notification-enter-active {
  animation: slideIn 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.notification-leave-active {
  animation: slideOut 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19);
}

/* 📱 响应式设计 */
@media (width <= 480px) {
  .sci-fi-notification-container {
    top: 10px;
    right: 10px;
    left: 10px;
  }
  
  .sci-fi-notification {
    width: 100%;
  }
}
</style>
