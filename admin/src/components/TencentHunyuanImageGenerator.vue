<template>
  <div class="tencent-hunyuan-generator">
    <div class="generator-header">
      <h3 class="generator-title">🎨 腾讯混元AI图片生成</h3>
      <div class="generator-badges">
        <span class="badge recommended">推荐</span>
        <span class="badge free">免费使用</span>
        <span class="badge chinese">中文优化</span>
      </div>
    </div>
    
    <div class="generator-description">
      <p>腾讯混元提供高质量的AI图片生成服务，特别适合中文提示词，支持多种艺术风格。</p>
    </div>
    
    <!-- 快速提示词生成 -->
    <div class="quick-prompt-section">
      <h4>🚀 快速生成提示词</h4>
      <div class="service-info" v-if="serviceName">
        <span class="service-label">当前服务:</span>
        <span class="service-name">{{ serviceName }}</span>
      </div>
      
      <div class="prompt-templates">
        <button 
          v-for="template in promptTemplates" 
          :key="template.key"
          class="template-btn"
          @click="selectTemplate(template)"
        >
          {{ template.name }}
        </button>
      </div>
      
      <div class="generated-prompt" v-if="generatedPrompt">
        <label class="prompt-label">生成的提示词:</label>
        <div class="prompt-text">{{ generatedPrompt }}</div>
        <div class="prompt-actions">
          <button class="copy-btn" @click="copyPrompt">📋 复制</button>
          <button class="edit-btn" @click="editPrompt">✏️ 编辑</button>
        </div>
      </div>
    </div>
    
    <!-- 自动生成功能 -->
    <div class="auto-generate-section">
      <h4>🤖 自动生成图片</h4>
      <div class="auto-generate-card">
        <div class="auto-generate-header">
          <span class="auto-icon">⚡</span>
          <span class="auto-title">DeepSeek + 腾讯混元</span>
          <span class="badge auto">智能生成</span>
        </div>
        <div class="auto-generate-description">
          使用DeepSeek生成专业提示词，自动调用腾讯混元生成高质量图片
        </div>
        <div class="auto-generate-features">
          <div class="feature">🧠 DeepSeek智能提示词</div>
          <div class="feature">🎨 腾讯混元高质量生成</div>
          <div class="feature">⚡ 一键自动化流程</div>
          <div class="feature">💾 自动显示结果</div>
        </div>

        <!-- 生成状态 -->
        <div v-if="isGenerating" class="generating-status">
          <div class="loading-spinner"></div>
          <div class="status-text">{{ generatingStatus }}</div>
          <div class="progress-bar">
            <div class="progress-fill" :style="{ width: generatingProgress + '%' }"></div>
          </div>
        </div>

        <!-- 生成结果 -->
        <div v-if="generatedImage" class="generated-result">
          <div class="result-header">
            <span class="result-title">✅ 生成成功</span>
            <span class="result-time">{{ formatTime(generatedImage.timestamp) }}</span>
          </div>
          <div class="result-image">
            <img :src="generatedImage.imageUrl" alt="AI生成的服务图片" @load="onImageLoad" />
          </div>
          <div class="result-prompt">
            <div class="prompt-label">使用的提示词:</div>
            <div class="prompt-text">{{ generatedImage.prompt }}</div>
          </div>
          <div class="result-actions">
            <button class="use-btn" @click="useGeneratedImage">
              ✅ 使用此图片
            </button>
            <button class="regenerate-btn" @click="regenerateImage">
              🔄 重新生成
            </button>
            <button class="download-btn" @click="downloadImage">
              💾 下载图片
            </button>
          </div>
        </div>

        <!-- 生成按钮 -->
        <div v-if="!isGenerating && !generatedImage" class="auto-generate-actions">
          <button
            class="generate-btn"
            @click="startAutoGenerate"
            :disabled="!props.serviceName || !props.serviceDescription"
          >
            🚀 自动生成图片
          </button>
          <div v-if="!props.serviceName || !props.serviceDescription" class="generate-tip">
            请先填写服务名称和描述
          </div>
        </div>
      </div>
    </div>

    <!-- 使用方式选择 -->
    <div class="usage-options">
      <h4>🎯 其他使用方式</h4>

      <div class="option-cards">
        <!-- 网页版 -->
        <div class="option-card">
          <div class="option-header">
            <span class="option-icon">🌐</span>
            <span class="option-title">手动网页版</span>
            <span class="badge">传统方式</span>
          </div>
          <div class="option-description">
            手动在腾讯混元官网生成图片，需要复制粘贴提示词
          </div>
          <div class="option-features">
            <div class="feature">✅ 无需注册登录</div>
            <div class="feature">✅ 界面友好直观</div>
            <div class="feature">✅ 支持多种风格</div>
            <div class="feature">⚠️ 需要手动操作</div>
          </div>
          <div class="option-actions">
            <button class="secondary-btn" @click="openHunyuanWeb">
              🚀 打开腾讯混元
            </button>
            <button class="secondary-btn" @click="showWebGuide = !showWebGuide">
              📖 使用指南
            </button>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 网页版使用指南 -->
    <div v-if="showWebGuide" class="guide-section">
      <h4>📖 网页版使用指南</h4>
      <div class="guide-steps">
        <div class="step">
          <div class="step-number">1</div>
          <div class="step-content">
            <div class="step-title">复制提示词</div>
            <div class="step-description">点击上方"复制"按钮，复制生成的中文提示词</div>
          </div>
        </div>
        
        <div class="step">
          <div class="step-number">2</div>
          <div class="step-content">
            <div class="step-title">打开腾讯混元</div>
            <div class="step-description">点击"打开腾讯混元"按钮，在新窗口中打开官网</div>
          </div>
        </div>
        
        <div class="step">
          <div class="step-number">3</div>
          <div class="step-content">
            <div class="step-title">粘贴提示词</div>
            <div class="step-description">在腾讯混元网站的输入框中粘贴提示词</div>
          </div>
        </div>
        
        <div class="step">
          <div class="step-number">4</div>
          <div class="step-content">
            <div class="step-title">选择风格</div>
            <div class="step-description">选择"摄影"或"写实"风格，适合医疗服务场景</div>
          </div>
        </div>
        
        <div class="step">
          <div class="step-number">5</div>
          <div class="step-content">
            <div class="step-title">生成图片</div>
            <div class="step-description">点击生成按钮，等待AI生成图片</div>
          </div>
        </div>
        
        <div class="step">
          <div class="step-number">6</div>
          <div class="step-content">
            <div class="step-title">下载保存</div>
            <div class="step-description">右键保存图片，然后上传到服务管理系统</div>
          </div>
        </div>
      </div>
      
      <div class="guide-tips">
        <h5>💡 使用技巧</h5>
        <ul>
          <li>使用中文提示词效果更好</li>
          <li>选择"摄影"风格获得真实感</li>
          <li>可以多次生成选择最佳效果</li>
          <li>建议图片尺寸选择1:1或4:3</li>
        </ul>
      </div>
    </div>
    
    <!-- API指南 -->
    <div v-if="showApiGuide" class="guide-section">
      <h4>📚 API集成指南</h4>
      <div class="api-info">
        <p>API集成功能正在开发中，将提供以下功能：</p>
        <ul>
          <li>🔄 自动根据服务名称生成图片</li>
          <li>💾 自动保存到系统中</li>
          <li>🎨 支持批量生成</li>
          <li>⚙️ 可配置生成参数</li>
        </ul>
        
        <div class="api-status">
          <span class="status-badge developing">🚧 开发中</span>
          <span class="eta">预计完成时间: 2周内</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { tencentHunyuanService } from '../services/tencentHunyuanService.js';

// Props
const props = defineProps({
  serviceName: {
    type: String,
    default: ''
  },
  serviceDescription: {
    type: String,
    default: ''
  }
});

// Emits
const emit = defineEmits(['imageGenerated', 'imageSelected']);

// 响应式数据
const showWebGuide = ref(false);
const showApiGuide = ref(false);
const generatedPrompt = ref('');
const selectedTemplate = ref(null);

// 自动生成相关
const isGenerating = ref(false);
const generatingStatus = ref('');
const generatingProgress = ref(0);
const generatedImage = ref(null);

// 提示词模板
const promptTemplates = ref([
  {
    key: 'massage',
    name: '按摩推拿',
    template: '专业中医按摩师正在为客户进行{serviceName}，温馨舒适的中式按摩室，实木按摩床，柔和暖色调灯光，技师穿着白色工作服，手法娴熟专业，客户面部表情放松舒适，中式装修风格，木质家具，绿植装饰，香薰氛围，专业摄影，自然光线，高清细节，真实质感'
  },
  {
    key: 'acupuncture',
    name: '针灸艾灸',
    template: '传统中医{serviceName}治疗场景，资深中医师正在进行专业治疗，古典中式诊疗室，展示传统中医技法，患者安静接受治疗，传统中医诊所装修，中药柜背景，古典屏风，书法字画，纪实摄影风格，自然采光，细节清晰，专业构图'
  },
  {
    key: 'footcare',
    name: '足疗保健',
    template: '专业足疗技师正在进行{serviceName}，现代化足疗中心，舒适的足疗沙发椅，温水足浴盆，技师专注按摩足部穴位，客户享受放松时光，现代简约装修，暖色调灯光，舒适环境，清洁卫生，商业摄影，柔和光线，温馨氛围，高品质画面'
  },
  {
    key: 'wellness',
    name: '养生保健',
    template: '{serviceName}养生保健场景，茶艺师泡制养生茶，香薰缭绕，冥想垫，养生食材，展示健康生活方式，宁静祥和的养生环境，禅意装修，自然元素，绿植装饰，宁静氛围，生活摄影，自然光线，温馨氛围，高品质画面'
  },
  {
    key: 'beauty',
    name: '美容护理',
    template: '专业美容师正在为客户进行{serviceName}，高端美容床，专业护肤产品，优雅的美容院环境，展示美容护理的专业性和舒适性，高端美容院装修，优雅设计，柔和灯光，奢华氛围，商业摄影，柔光处理，细腻质感，高端品质'
  }
]);

// 计算属性
const currentPrompt = computed(() => {
  if (!selectedTemplate.value || !props.serviceName) return '';
  
  return selectedTemplate.value.template.replace(/{serviceName}/g, props.serviceName);
});

// 方法
const selectTemplate = (template) => {
  selectedTemplate.value = template;
  generatedPrompt.value = currentPrompt.value;
};

const copyPrompt = async () => {
  try {
    await navigator.clipboard.writeText(generatedPrompt.value);
    alert('✅ 提示词已复制到剪贴板');
  } catch (error) {
    // 降级方案
    const textArea = document.createElement('textarea');
    textArea.value = generatedPrompt.value;
    document.body.appendChild(textArea);
    textArea.select();
    document.execCommand('copy');
    document.body.removeChild(textArea);
    alert('✅ 提示词已复制到剪贴板');
  }
};

const editPrompt = () => {
  const newPrompt = prompt('编辑提示词:', generatedPrompt.value);
  if (newPrompt !== null) {
    generatedPrompt.value = newPrompt;
  }
};

// 自动生成图片
const startAutoGenerate = async () => {
  if (!props.serviceName || !props.serviceDescription) {
    alert('请先填写服务名称和描述');
    return;
  }

  isGenerating.value = true;
  generatingProgress.value = 0;
  generatedImage.value = null;

  try {
    // 步骤1: 生成提示词
    generatingStatus.value = '🧠 DeepSeek正在生成专业提示词...';
    generatingProgress.value = 20;

    await new Promise(resolve => setTimeout(resolve, 1000));

    // 步骤2: 调用腾讯混元
    generatingStatus.value = '🎨 腾讯混元正在生成图片...';
    generatingProgress.value = 50;

    const result = await tencentHunyuanService.generateServiceImage(
      props.serviceName,
      props.serviceDescription
    );

    generatingProgress.value = 80;

    if (result.success) {
      generatingStatus.value = '✅ 图片生成成功！';
      generatingProgress.value = 100;

      await new Promise(resolve => setTimeout(resolve, 500));

      generatedImage.value = result;

      // 通知父组件
      emit('imageGenerated', result);

    } else {
      throw new Error(result.error || '图片生成失败');
    }

  } catch (error) {
    console.error('❌ 自动生成失败:', error);
    alert(`图片生成失败: ${error.message}`);
  } finally {
    isGenerating.value = false;
    generatingStatus.value = '';
    generatingProgress.value = 0;
  }
};

// 重新生成图片
const regenerateImage = () => {
  generatedImage.value = null;
  startAutoGenerate();
};

// 使用生成的图片
const useGeneratedImage = () => {
  if (generatedImage.value) {
    emit('imageSelected', generatedImage.value);
    alert('✅ 图片已应用到服务中');
  }
};

// 下载图片
const downloadImage = () => {
  if (generatedImage.value && generatedImage.value.imageUrl) {
    const link = document.createElement('a');
    link.href = generatedImage.value.imageUrl;
    link.download = `${props.serviceName}-AI生成图片.png`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
};

// 图片加载完成
const onImageLoad = () => {
  console.log('✅ 生成的图片加载完成');
};

// 格式化时间
const formatTime = (timestamp) => {
  return new Date(timestamp).toLocaleString();
};

const openHunyuanWeb = () => {
  // 打开腾讯混元官网
  window.open('https://image.hunyuan.tencent.com/', '_blank');

  // 如果有生成的提示词，提醒用户复制
  if (generatedPrompt.value) {
    setTimeout(() => {
      if (confirm('是否要复制提示词到剪贴板？\n在腾讯混元网站中可以直接粘贴使用。')) {
        copyPrompt();
      }
    }, 1000);
  }
};

// 生命周期
onMounted(() => {
  // 如果有服务名称，自动选择合适的模板
  if (props.serviceName) {
    const serviceName = props.serviceName.toLowerCase();
    let matchedTemplate = null;
    
    if (serviceName.includes('按摩') || serviceName.includes('推拿')) {
      matchedTemplate = promptTemplates.value.find(t => t.key === 'massage');
    } else if (serviceName.includes('针灸') || serviceName.includes('艾灸')) {
      matchedTemplate = promptTemplates.value.find(t => t.key === 'acupuncture');
    } else if (serviceName.includes('足疗') || serviceName.includes('足浴')) {
      matchedTemplate = promptTemplates.value.find(t => t.key === 'footcare');
    } else if (serviceName.includes('养生') || serviceName.includes('保健')) {
      matchedTemplate = promptTemplates.value.find(t => t.key === 'wellness');
    } else if (serviceName.includes('美容') || serviceName.includes('护理')) {
      matchedTemplate = promptTemplates.value.find(t => t.key === 'beauty');
    }
    
    if (matchedTemplate) {
      selectTemplate(matchedTemplate);
    }
  }
});

// 暴露方法给父组件
defineExpose({
  generatePrompt: () => generatedPrompt.value,
  openHunyuan: openHunyuanWeb,
  startAutoGenerate: startAutoGenerate,
  getGeneratedImage: () => generatedImage.value
});
</script>

<style scoped>
.tencent-hunyuan-generator {
  padding: 20px;
  border: 2px solid rgb(255 182 193 / 40%);
  border-radius: 12px;
  background: linear-gradient(135deg,
    rgb(255 255 255 / 95%),
    rgb(255 248 220 / 95%)
  );
  margin-bottom: 20px;
}

.generator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.generator-title {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #2d1b69;
}

.generator-badges {
  display: flex;
  gap: 8px;
}

.badge {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.badge.recommended {
  color: white;
  background: linear-gradient(135deg, #ff6b6b, #ee5a24);
}

.badge.free {
  color: white;
  background: linear-gradient(135deg, #00b894, #00a085);
}

.badge.chinese {
  color: white;
  background: linear-gradient(135deg, #fd79a8, #e84393);
}

.badge.auto {
  color: white;
  background: linear-gradient(135deg, #a855f7, #7c3aed);
}

.generator-description {
  padding: 12px;
  border-radius: 8px;
  color: #2d1b69;
  background: rgb(255 182 193 / 10%);
  margin-bottom: 20px;
}

/* 自动生成功能样式 */
.auto-generate-section {
  margin-bottom: 24px;
}

.auto-generate-section h4 {
  color: #2d1b69;
  margin-bottom: 16px;
}

.auto-generate-card {
  padding: 20px;
  border: 2px solid rgb(168 85 247 / 30%);
  border-radius: 12px;
  background: linear-gradient(135deg,
    rgb(168 85 247 / 5%),
    rgb(124 58 237 / 5%)
  );
  margin-bottom: 20px;
}

.auto-generate-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 12px;
}

.auto-icon {
  font-size: 24px;
}

.auto-title {
  font-size: 16px;
  font-weight: 600;
  color: #2d1b69;
}

.auto-generate-description {
  line-height: 1.5;
  color: #7f8c8d;
  margin-bottom: 16px;
}

.auto-generate-features {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
  margin-bottom: 20px;
}

.auto-generate-features .feature {
  font-size: 14px;
  color: #2d1b69;
}

/* 生成状态样式 */
.generating-status {
  padding: 20px;
  border-radius: 8px;
  text-align: center;
  background: rgb(255 255 255 / 80%);
  margin-bottom: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto 12px;
  border: 4px solid rgb(168 85 247 / 20%);
  border-radius: 50%;
  border-top: 4px solid #a855f7;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.status-text {
  font-weight: 500;
  color: #2d1b69;
  margin-bottom: 12px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  border-radius: 4px;
  overflow: hidden;
  background: rgb(168 85 247 / 20%);
}

.progress-fill {
  height: 100%;
  border-radius: 4px;
  background: linear-gradient(90deg, #a855f7, #7c3aed);
  transition: width 0.3s ease;
}

/* 生成结果样式 */
.generated-result {
  padding: 16px;
  border: 1px solid rgb(168 85 247 / 20%);
  border-radius: 8px;
  background: rgb(255 255 255 / 90%);
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.result-title {
  font-weight: 600;
  color: #10b981;
}

.result-time {
  font-size: 12px;
  color: #7f8c8d;
}

.result-image {
  margin-bottom: 12px;
  text-align: center;
}

.result-image img {
  max-width: 100%;
  max-height: 300px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgb(0 0 0 / 10%);
}

.result-prompt {
  margin-bottom: 16px;
}

.prompt-label {
  font-size: 14px;
  font-weight: 500;
  color: #2d1b69;
  margin-bottom: 4px;
}

.result-prompt .prompt-text {
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 13px;
  line-height: 1.4;
  color: #2d1b69;
  background: rgb(168 85 247 / 10%);
}

.result-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.use-btn, .regenerate-btn, .download-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.use-btn {
  color: white;
  background: linear-gradient(135deg, #10b981, #059669);
}

.regenerate-btn {
  color: white;
  background: linear-gradient(135deg, #f59e0b, #d97706);
}

.download-btn {
  color: white;
  background: linear-gradient(135deg, #3b82f6, #1d4ed8);
}

.use-btn:hover, .regenerate-btn:hover, .download-btn:hover {
  box-shadow: 0 2px 8px rgb(0 0 0 / 20%);
  transform: translateY(-1px);
}

/* 生成按钮样式 */
.auto-generate-actions {
  text-align: center;
}

.generate-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, #a855f7, #7c3aed);
  transition: all 0.3s ease;
  cursor: pointer;
  margin-bottom: 8px;
}

.generate-btn:hover:not(:disabled) {
  box-shadow: 0 4px 12px rgb(168 85 247 / 40%);
  transform: translateY(-2px);
}

.generate-btn:disabled {
  box-shadow: none;
  opacity: 0.5;
  transform: none;
  cursor: not-allowed;
}

.generate-tip {
  font-size: 12px;
  color: #7f8c8d;
  font-style: italic;
}

.quick-prompt-section {
  margin-bottom: 24px;
}

.quick-prompt-section h4 {
  color: #2d1b69;
  margin-bottom: 12px;
}

.service-info {
  padding: 8px 12px;
  border-radius: 6px;
  background: rgb(52 152 219 / 10%);
  margin-bottom: 12px;
}

.service-label {
  color: #7f8c8d;
  margin-right: 8px;
}

.service-name {
  font-weight: 600;
  color: #2d1b69;
}

.prompt-templates {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}

.template-btn {
  padding: 8px 16px;
  border: 2px solid rgb(255 182 193 / 30%);
  border-radius: 20px;
  font-size: 14px;
  color: #2d1b69;
  background: linear-gradient(135deg,
    rgb(255 255 255 / 90%),
    rgb(255 248 220 / 90%)
  );
  transition: all 0.3s ease;
  cursor: pointer;
}

.template-btn:hover {
  box-shadow: 0 4px 12px rgb(255 182 193 / 30%);
  transform: translateY(-2px);
  border-color: rgb(255 182 193 / 60%);
}

.generated-prompt {
  margin-top: 16px;
}

.prompt-label {
  display: block;
  font-weight: 500;
  color: #2d1b69;
  margin-bottom: 8px;
}

.prompt-text {
  padding: 12px;
  border: 1px solid rgb(255 182 193 / 30%);
  border-radius: 8px;
  line-height: 1.5;
  color: #2d1b69;
  background: rgb(255 255 255 / 80%);
  margin-bottom: 8px;
}

.prompt-actions {
  display: flex;
  gap: 8px;
}

.copy-btn, .edit-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.copy-btn {
  color: white;
  background: linear-gradient(135deg, #00b894, #00a085);
}

.edit-btn {
  color: white;
  background: linear-gradient(135deg, #fdcb6e, #e17055);
}

.copy-btn:hover, .edit-btn:hover {
  box-shadow: 0 2px 8px rgb(0 0 0 / 20%);
  transform: translateY(-1px);
}

.usage-options h4 {
  color: #2d1b69;
  margin-bottom: 16px;
}

.option-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 20px;
}

.option-card {
  padding: 16px;
  border: 2px solid rgb(255 182 193 / 30%);
  border-radius: 12px;
  background: rgb(255 255 255 / 80%);
  transition: all 0.3s ease;
}

.option-card.recommended {
  border-color: rgb(255 182 193 / 60%);
  background: linear-gradient(135deg,
    rgb(255 182 193 / 10%),
    rgb(255 218 185 / 10%)
  );
}

.option-card:hover {
  box-shadow: 0 4px 12px rgb(255 182 193 / 30%);
  transform: translateY(-2px);
}

.option-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.option-icon {
  font-size: 20px;
}

.option-title {
  font-weight: 600;
  color: #2d1b69;
}

.option-description {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 12px;
}

.option-features {
  margin-bottom: 16px;
}

.feature {
  font-size: 12px;
  color: #2d1b69;
  margin-bottom: 4px;
}

.option-actions {
  display: flex;
  gap: 8px;
}

.primary-btn, .secondary-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.primary-btn {
  color: white;
  background: linear-gradient(135deg, #00b894, #00a085);
}

.secondary-btn {
  color: white;
  background: linear-gradient(135deg, #74b9ff, #0984e3);
}

.primary-btn:hover, .secondary-btn:hover {
  box-shadow: 0 2px 8px rgb(0 0 0 / 20%);
  transform: translateY(-1px);
}

.secondary-btn:disabled {
  box-shadow: none;
  opacity: 0.5;
  transform: none;
  cursor: not-allowed;
}

.guide-section {
  padding: 16px;
  border: 1px solid rgb(255 182 193 / 20%);
  border-radius: 8px;
  background: rgb(255 255 255 / 60%);
  margin-top: 20px;
}

.guide-section h4 {
  color: #2d1b69;
  margin-bottom: 16px;
}

.guide-steps {
  margin-bottom: 16px;
}

.step {
  display: flex;
  margin-bottom: 16px;
  align-items: flex-start;
}

.step-number {
  display: flex;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
  color: white;
  background: linear-gradient(135deg, #00b894, #00a085);
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  flex-shrink: 0;
}

.step-content {
  flex: 1;
}

.step-title {
  font-weight: 600;
  color: #2d1b69;
  margin-bottom: 4px;
}

.step-description {
  font-size: 14px;
  color: #7f8c8d;
}

.guide-tips {
  padding: 12px;
  border-radius: 6px;
  background: rgb(255 182 193 / 10%);
  margin-top: 16px;
}

.guide-tips h5 {
  color: #2d1b69;
  margin-bottom: 8px;
}

.guide-tips ul {
  margin: 0;
  padding-left: 20px;
}

.guide-tips li {
  font-size: 14px;
  color: #7f8c8d;
  margin-bottom: 4px;
}

.api-info {
  color: #7f8c8d;
}

.api-status {
  margin-top: 12px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-badge.developing {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  color: white;
  background: linear-gradient(135deg, #fdcb6e, #e17055);
}

.eta {
  font-size: 12px;
  color: #7f8c8d;
}

@media (width <= 768px) {
  .option-cards {
    grid-template-columns: 1fr;
  }
  
  .option-actions {
    flex-direction: column;
  }
  
  .prompt-templates {
    flex-direction: column;
  }
  
  .template-btn {
    width: 100%;
  }
}
</style>
