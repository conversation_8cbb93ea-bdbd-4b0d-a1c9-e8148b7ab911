<template>
  <ErrorBoundary>
    <div class="admin-layout purple-theme">
      <router-view />
    </div>
  </ErrorBoundary>
</template>

<script setup>
// 使用<script setup>语法
import ErrorBoundary from '@/components/ErrorBoundary.vue';
import { onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter()

onMounted(() => {
  console.log('🚀 壹心堂管理系统初始化完成');
  console.log('📍 当前路由:', router.currentRoute.value.path);
})
</script>

<style lang="scss">
.admin-layout {
  display: flex;
  position: relative;
  width: 100%;
  height: 100vh;
  overflow: hidden;
  // 统一背景 - 整个管理界面使用相同的渐变背景
  background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
  flex-direction: column;
}
</style>
