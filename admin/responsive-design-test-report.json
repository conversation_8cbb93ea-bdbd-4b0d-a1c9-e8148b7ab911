{"summary": {"totalTests": 33, "passedTests": 28, "failedTests": 5, "successRate": 84.8, "grade": "⚠️ 良好响应式", "categories": {"手机": {"total": 11, "passed": 11}, "手机横屏": {"total": 4, "passed": 4}, "平板": {"total": 3, "passed": 3}, "平板横屏": {"total": 2, "passed": 2}, "桌面": {"total": 6, "passed": 6}, "其他": {"total": 7, "passed": 2}}, "timestamp": "2025-07-18T15:55:41.447Z"}, "breakpoints": [{"name": "极小手机", "width": 320, "height": 568, "category": "手机"}, {"name": "iPhone SE", "width": 375, "height": 667, "category": "手机"}, {"name": "iPhone 12", "width": 390, "height": 844, "category": "手机"}, {"name": "手机横屏", "width": 667, "height": 375, "category": "手机横屏"}, {"name": "iPad Mini", "width": 768, "height": 1024, "category": "平板"}, {"name": "iPad 横屏", "width": 1024, "height": 768, "category": "平板横屏"}, {"name": "小桌面", "width": 1366, "height": 768, "category": "桌面"}, {"name": "标准桌面", "width": 1920, "height": 1080, "category": "桌面"}, {"name": "大桌面", "width": 2560, "height": 1440, "category": "桌面"}], "results": [{"name": "CSS媒体查询", "passed": true, "details": "发现 11 个媒体查询，4 个断点", "timestamp": "2025-07-18T15:55:41.439Z"}, {"name": "响应式断点覆盖", "passed": true, "details": "断点: 480, 667, 768, 1024px", "timestamp": "2025-07-18T15:55:41.440Z"}, {"name": "登录页 - 页面结构", "passed": false, "details": "测试失败: Request failed with status code 404", "timestamp": "2025-07-18T15:55:41.442Z"}, {"name": "仪表板 - 页面结构", "passed": false, "details": "测试失败: Request failed with status code 404", "timestamp": "2025-07-18T15:55:41.443Z"}, {"name": "服务管理 - 页面结构", "passed": false, "details": "测试失败: Request failed with status code 404", "timestamp": "2025-07-18T15:55:41.444Z"}, {"name": "技师管理 - 页面结构", "passed": false, "details": "测试失败: Request failed with status code 404", "timestamp": "2025-07-18T15:55:41.445Z"}, {"name": "客户管理 - 页面结构", "passed": false, "details": "测试失败: Request failed with status code 404", "timestamp": "2025-07-18T15:55:41.446Z"}, {"name": "极小手机 - 屏幕边界约束", "passed": true, "details": "元素应在视窗范围内", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "极小手机 - 触摸标准约束", "passed": true, "details": "交互元素应≥44x44px", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "极小手机 - 文本可读性约束", "passed": true, "details": "文本字体应≥12px", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "极小手机 - 极小屏幕布局", "passed": true, "details": "极小屏幕应无水平滚动", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "iPhone SE - 屏幕边界约束", "passed": true, "details": "元素应在视窗范围内", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "iPhone SE - 触摸标准约束", "passed": true, "details": "交互元素应≥44x44px", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "iPhone SE - 文本可读性约束", "passed": true, "details": "文本字体应≥12px", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "iPhone SE - 极小屏幕布局", "passed": true, "details": "极小屏幕应无水平滚动", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "iPhone 12 - 屏幕边界约束", "passed": true, "details": "元素应在视窗范围内", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "iPhone 12 - 触摸标准约束", "passed": true, "details": "交互元素应≥44x44px", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "iPhone 12 - 文本可读性约束", "passed": true, "details": "文本字体应≥12px", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "手机横屏 - 屏幕边界约束", "passed": true, "details": "元素应在视窗范围内", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "手机横屏 - 触摸标准约束", "passed": true, "details": "交互元素应≥44x44px", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "手机横屏 - 文本可读性约束", "passed": true, "details": "文本字体应≥12px", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "手机横屏 - 横屏布局约束", "passed": true, "details": "横屏模式内容应完全可见", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "iPad Mini - 屏幕边界约束", "passed": true, "details": "元素应在视窗范围内", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "iPad Mini - 触摸标准约束", "passed": true, "details": "交互元素应≥44x44px", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "iPad Mini - 文本可读性约束", "passed": true, "details": "文本字体应≥12px", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "iPad 横屏 - 屏幕边界约束", "passed": true, "details": "元素应在视窗范围内", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "iPad 横屏 - 文本可读性约束", "passed": true, "details": "文本字体应≥12px", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "小桌面 - 屏幕边界约束", "passed": true, "details": "元素应在视窗范围内", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "小桌面 - 文本可读性约束", "passed": true, "details": "文本字体应≥12px", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "标准桌面 - 屏幕边界约束", "passed": true, "details": "元素应在视窗范围内", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "标准桌面 - 文本可读性约束", "passed": true, "details": "文本字体应≥12px", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "大桌面 - 屏幕边界约束", "passed": true, "details": "元素应在视窗范围内", "timestamp": "2025-07-18T15:55:41.447Z"}, {"name": "大桌面 - 文本可读性约束", "passed": true, "details": "文本字体应≥12px", "timestamp": "2025-07-18T15:55:41.447Z"}]}