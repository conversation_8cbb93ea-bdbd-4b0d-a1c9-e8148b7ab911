{"summary": {"totalTests": 19, "passedTests": 15, "failedTests": 4, "successRate": 78.9, "grade": "⚠️ 一般", "timestamp": "2025-07-18T15:46:26.817Z"}, "results": [{"name": "基础连接", "passed": false, "details": "连接失败: Request failed with status code 404", "timestamp": "2025-07-18T15:46:18.100Z"}, {"name": "前端API代理", "passed": true, "details": "代理工作正常，返回数据: {\"today_appointments\":0,\"today_income\":\"0.00\",\"total_customers\":3,\"active_therapists\":3,\"pending_app...", "timestamp": "2025-07-18T15:46:19.294Z"}, {"name": "直接API访问", "passed": true, "details": "API直接访问正常", "timestamp": "2025-07-18T15:46:20.092Z"}, {"name": "页面-首页", "passed": true, "details": "状态: 200, 内容长度: 2731", "timestamp": "2025-07-18T15:46:20.096Z"}, {"name": "页面-登录页", "passed": true, "details": "状态: 200, 内容长度: 2731", "timestamp": "2025-07-18T15:46:20.098Z"}, {"name": "页面-仪表板", "passed": true, "details": "状态: 200, 内容长度: 2731", "timestamp": "2025-07-18T15:46:20.099Z"}, {"name": "页面-服务管理", "passed": true, "details": "状态: 200, 内容长度: 2731", "timestamp": "2025-07-18T15:46:20.101Z"}, {"name": "页面-技师管理", "passed": true, "details": "状态: 200, 内容长度: 2731", "timestamp": "2025-07-18T15:46:20.103Z"}, {"name": "页面-客户管理", "passed": true, "details": "状态: 200, 内容长度: 2731", "timestamp": "2025-07-18T15:46:20.106Z"}, {"name": "页面-预约管理", "passed": true, "details": "状态: 200, 内容长度: 2731", "timestamp": "2025-07-18T15:46:20.109Z"}, {"name": "页面-财务概览", "passed": true, "details": "状态: 200, 内容长度: 2731", "timestamp": "2025-07-18T15:46:20.111Z"}, {"name": "API-健康检查", "passed": false, "details": "状态: 200, 数据类型: string", "timestamp": "2025-07-18T15:46:20.113Z"}, {"name": "API-仪表板统计", "passed": true, "details": "状态: 200, 数据类型: object", "timestamp": "2025-07-18T15:46:21.244Z"}, {"name": "API-服务列表", "passed": true, "details": "状态: 200, 数据类型: object", "timestamp": "2025-07-18T15:46:22.924Z"}, {"name": "API-技师列表", "passed": true, "details": "状态: 200, 数据类型: object", "timestamp": "2025-07-18T15:46:23.338Z"}, {"name": "API-客户列表", "passed": true, "details": "状态: 200, 数据类型: object", "timestamp": "2025-07-18T15:46:23.666Z"}, {"name": "API-预约列表", "passed": true, "details": "状态: 200, 数据类型: object", "timestamp": "2025-07-18T15:46:24.437Z"}, {"name": "仪表板数据流", "passed": false, "details": "数据字段: today_appointments, today_income, total_customers, active_therapists, pending_appointments, completed_appointments_today, weekly_income, service_distribution, therapist_performance", "timestamp": "2025-07-18T15:46:25.550Z"}, {"name": "服务数据流", "passed": false, "details": "服务数量: 0", "timestamp": "2025-07-18T15:46:26.816Z"}]}