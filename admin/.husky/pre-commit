#!/usr/bin/env sh
. "$(dirname -- "$0")/_/husky.sh"

echo "🚨 执行强制自动化测试..."
echo "📋 根据PROJECT_STANDARDS.md强制约定，所有代码提交前必须通过自动化测试"

# 检查是否有前端服务运行
if ! curl -s http://localhost:3001 > /dev/null; then
  echo "❌ 前端服务未运行，请先启动: npm run dev"
  exit 1
fi

# 检查是否有后端服务运行
if ! curl -s http://localhost:8000 > /dev/null; then
  echo "⚠️ 后端服务未运行，将使用模拟数据进行测试"
fi

# 运行自动化测试
echo "🤖 开始执行全方面自动化测试..."
npm run test:comprehensive

# 检查测试结果
if [ $? -eq 0 ]; then
  echo "✅ 自动化测试通过，允许提交代码"
else
  echo "❌ 自动化测试失败，禁止提交代码"
  echo "📋 请修复所有测试失败项目后重新提交"
  exit 1
fi
