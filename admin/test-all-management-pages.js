#!/usr/bin/env node

/**
 * 测试所有管理页面的完美规则合规性
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 测试所有管理页面完美规则合规性...\n');

// 数据管理页面列表（只检查有表格数据的页面）
const managementPages = [
  { name: '预约管理', file: 'AppointmentManagement.vue', hasTable: true },
  { name: '客户管理', file: 'CustomerManagement.vue', hasTable: true },
  { name: '技师管理', file: 'TherapistManagement.vue', hasTable: true },
  { name: '服务管理', file: 'ServiceManagement.vue', hasTable: true },
  { name: '财务概览', file: 'FinanceOverview.vue', hasTable: false },
  { name: '系统管理', file: 'SystemManagement.vue', hasTable: false }
];

// 检查单个页面
function checkPage(pageName, fileName) {
  console.log(`\n📋 检查 ${pageName} (${fileName})`);
  console.log('='.repeat(50));
  
  const filePath = path.join(__dirname, 'src/views', fileName);
  
  if (!fs.existsSync(filePath)) {
    console.log(`❌ 文件不存在: ${fileName}`);
    return { name: pageName, issues: ['文件不存在'] };
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const issues = [];
  
  // 检查1: 5行数据显示约定（只检查有表格的页面）
  const hasTable = managementPages.find(p => p.file === fileName)?.hasTable;
  if (hasTable) {
    console.log('🔍 检查5行数据显示约定...');
    const pageSizeMatch = content.match(/pageSize.*?ref\((\d+)\)/);
    if (pageSizeMatch) {
      if (pageSizeMatch[1] === '5') {
        console.log('  ✅ 默认每页5条记录设置正确');
      } else {
        console.log(`  ❌ 默认每页记录数不是5条，当前是${pageSizeMatch[1]}条`);
        issues.push(`默认每页记录数不是5条，当前是${pageSizeMatch[1]}条`);
      }
    } else {
      console.log('  ⚠️  未找到pageSize设置');
      issues.push('未找到pageSize设置');
    }
  } else {
    console.log('🔍 跳过5行数据检查（非表格页面）');
  }
  
  // 检查2: 表格高度设置（只检查有表格的页面）
  if (hasTable) {
    console.log('🔍 检查表格高度设置...');
    const minHeightMatch = content.match(/min-height:\s*350px/);
    const maxHeightMatch = content.match(/max-height:\s*calc\(100vh\s*-\s*320px\)/);

    if (minHeightMatch) {
      console.log('  ✅ 表格最小高度设置正确(350px)');
    } else {
      console.log('  ❌ 表格最小高度设置不正确');
      issues.push('表格最小高度设置不正确');
    }

    if (maxHeightMatch) {
      console.log('  ✅ 表格最大高度设置正确');
    } else {
      console.log('  ❌ 表格最大高度设置不正确');
      issues.push('表格最大高度设置不正确');
    }
  } else {
    console.log('🔍 跳过表格高度检查（非表格页面）');
  }
  
  // 检查3: 边界溢出问题
  console.log('🔍 检查边界溢出问题...');
  const overflowPatterns = [
    /right:\s*-\d+px/g,
    /left:\s*-\d+px/g,
    /margin-left:\s*-\d+px/g,
  ];
  
  let hasOverflowIssues = false;
  overflowPatterns.forEach((pattern) => {
    const matches = content.match(pattern);
    if (matches) {
      hasOverflowIssues = true;
      console.log(`  ⚠️  发现潜在边界问题: ${matches.join(', ')}`);
      issues.push(`边界溢出: ${matches.join(', ')}`);
    }
  });
  
  if (!hasOverflowIssues) {
    console.log('  ✅ 未发现边界溢出问题');
  }
  
  // 检查4: Z-index层级管理
  console.log('🔍 检查Z-index层级管理...');
  const zIndexValues = content.match(/z-index:\s*(\d+)/g) || [];
  const zIndexNumbers = zIndexValues.map(z => parseInt(z.match(/\d+/)[0]));
  
  if (zIndexNumbers.length > 0) {
    console.log(`  📊 发现Z-index值: ${zIndexNumbers.join(', ')}`);
    
    const largeZIndex = zIndexNumbers.filter(z => z > 10000);
    if (largeZIndex.length > 0) {
      console.log(`  ⚠️  发现过大的z-index值: ${largeZIndex.join(', ')}`);
      issues.push(`过大的z-index值: ${largeZIndex.join(', ')}`);
    } else {
      console.log('  ✅ Z-index值使用合理');
    }
  } else {
    console.log('  ✅ 未使用z-index');
  }
  
  // 检查5: 响应式设计
  console.log('🔍 检查响应式设计...');
  const mediaQueries = content.match(/@media[^{]+\{/g) || [];
  console.log(`  📱 发现媒体查询: ${mediaQueries.length}个`);
  
  const responsiveBreakpoints = ['768px', '480px'];
  const hasBreakpoints = responsiveBreakpoints.some(bp => 
    content.includes(`max-width: ${bp}`)
  );
  
  if (hasBreakpoints) {
    console.log('  ✅ 响应式断点设置存在');
  } else {
    console.log('  ⚠️  响应式断点可能缺失');
    issues.push('响应式断点可能缺失');
  }
  
  // 检查6: 基础UX规范
  console.log('🔍 检查基础UX规范...');
  const hasEnterKeyNav = content.includes('@keydown.enter');
  const hasAutoFocus = content.includes('onMounted') && content.includes('focus');
  const hasFormValidation = content.includes('@blur') || content.includes('rules');
  
  if (hasEnterKeyNav) {
    console.log('  ✅ 包含Enter键导航');
  } else {
    console.log('  ⚠️  缺少Enter键导航');
    issues.push('缺少Enter键导航');
  }
  
  if (hasAutoFocus) {
    console.log('  ✅ 包含自动聚焦');
  } else {
    console.log('  ⚠️  缺少自动聚焦');
    issues.push('缺少自动聚焦');
  }
  
  if (hasFormValidation) {
    console.log('  ✅ 包含表单验证');
  } else {
    console.log('  ⚠️  缺少表单验证');
    issues.push('缺少表单验证');
  }
  
  return { name: pageName, issues };
}

// 检查所有页面
const results = [];
managementPages.forEach(page => {
  const result = checkPage(page.name, page.file);
  results.push(result);
});

// 生成总结报告
console.log('\n\n📊 完美规则检查总结报告');
console.log('='.repeat(60));

results.forEach(result => {
  console.log(`\n📋 ${result.name}:`);
  if (result.issues.length === 0) {
    console.log('  ✅ 完全合规，无问题');
  } else {
    console.log(`  ❌ 发现 ${result.issues.length} 个问题:`);
    result.issues.forEach(issue => {
      console.log(`    - ${issue}`);
    });
  }
});

// 统计
const totalPages = results.length;
const compliantPages = results.filter(r => r.issues.length === 0).length;
const totalIssues = results.reduce((sum, r) => sum + r.issues.length, 0);

console.log(`\n📈 统计信息:`);
console.log(`  总页面数: ${totalPages}`);
console.log(`  完全合规页面: ${compliantPages}`);
console.log(`  合规率: ${Math.round(compliantPages / totalPages * 100)}%`);
console.log(`  总问题数: ${totalIssues}`);

if (totalIssues === 0) {
  console.log('\n🎉 所有管理页面都符合完美规则！');
} else {
  console.log('\n⚠️  需要修复问题以达到完美规则标准！');
}
