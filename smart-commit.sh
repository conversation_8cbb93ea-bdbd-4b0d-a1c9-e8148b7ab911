#!/bin/bash

# 🎯 智能Git提交脚本
# 用途: 修改完成 → 检查通过 → 再提交
# 作者: AI助手
# 日期: 2025-01-27

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 项目根目录
PROJECT_ROOT="/Users/<USER>/Documents/wechatcloud"
cd "$PROJECT_ROOT" || exit 1

echo -e "${BLUE}🚀 智能Git提交流程开始...${NC}"
echo -e "${CYAN}📍 当前目录: $(pwd)${NC}"
echo ""

# 步骤1: 检查Git状态
echo -e "${BLUE}📋 步骤1: 检查Git状态${NC}"
if ! git status --porcelain | grep -q .; then
    echo -e "${YELLOW}⚠️  没有检测到文件更改，无需提交${NC}"
    exit 0
fi

echo -e "${GREEN}✅ 检测到文件更改${NC}"
git status --short
echo ""

# 步骤2: 运行代码质量检查
echo -e "${BLUE}🔍 步骤2: 运行代码质量检查${NC}"

# 检查admin目录是否存在
if [ -d "admin" ]; then
    echo -e "${CYAN}📁 进入admin目录进行检查...${NC}"
    cd admin
    
    # 运行CSS规范检查
    echo -e "${CYAN}🎨 运行CSS规范检查...${NC}"
    if node ../css-standards-checker.js src/views/ServiceManagement.vue 2>/dev/null; then
        echo -e "${GREEN}✅ CSS规范检查通过${NC}"
    else
        echo -e "${RED}❌ CSS规范检查失败，请修复样式重叠问题${NC}"
        echo -e "${YELLOW}💡 提示: 查看 CSS_STANDARDS_GUIDE.md 了解修复方法${NC}"
        exit 1
    fi

    # 运行stylelint检查
    echo -e "${CYAN}🎨 运行CSS样式检查...${NC}"
    if npm run stylelint 2>/dev/null; then
        echo -e "${GREEN}✅ CSS样式检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️  CSS样式检查发现问题，尝试自动修复...${NC}"

        # 尝试自动修复
        if npm run stylelint -- --fix 2>/dev/null; then
            echo -e "${GREEN}✅ CSS样式问题已自动修复${NC}"
        else
            echo -e "${RED}❌ CSS样式问题无法自动修复，请手动处理${NC}"
            echo -e "${YELLOW}💡 提示: 运行 'cd admin && npm run stylelint' 查看详细错误${NC}"
            exit 1
        fi
    fi
    
    # 检查是否有TypeScript错误（如果存在）
    if [ -f "tsconfig.json" ]; then
        echo -e "${CYAN}🔧 运行TypeScript类型检查...${NC}"
        if npx tsc --noEmit 2>/dev/null; then
            echo -e "${GREEN}✅ TypeScript类型检查通过${NC}"
        else
            echo -e "${RED}❌ TypeScript类型检查失败${NC}"
            echo -e "${YELLOW}💡 提示: 运行 'cd admin && npx tsc --noEmit' 查看详细错误${NC}"
            exit 1
        fi
    fi
    
    cd ..
else
    echo -e "${YELLOW}⚠️  admin目录不存在，跳过前端代码检查${NC}"
fi

echo ""

# 步骤3: 添加所有更改到暂存区
echo -e "${BLUE}📦 步骤3: 添加更改到暂存区${NC}"
git add .
echo -e "${GREEN}✅ 所有更改已添加到暂存区${NC}"
echo ""

# 步骤4: 获取提交信息
echo -e "${BLUE}✏️  步骤4: 准备提交信息${NC}"

# 如果提供了提交信息参数，使用它
if [ $# -gt 0 ]; then
    COMMIT_MESSAGE="$*"
    echo -e "${CYAN}📝 使用提供的提交信息${NC}"
else
    # 否则使用默认的提交信息
    COMMIT_MESSAGE="feat: 完善服务管理系统统一编辑界面和毛玻璃效果

✨ 新增功能:
- 统一编辑界面：整合服务费、提成、时长编辑功能
- 历史记录标签页：显示服务修改历史时间线
- 新增服务界面：左侧基础信息，右侧项目简介
- 毛玻璃效果：全面应用半透明毛玻璃视觉效果

🎨 界面优化:
- 实现真正的半透明效果 (透明度 0.03-0.15)
- 增强模糊效果 (blur 25-40px + saturate 1.5-1.8)
- 统一色彩搭配 (紫色品牌色 + 半透明白色)
- 响应式布局适配 (桌面/平板/移动端)

🔧 技术改进:
- PostCSS 兼容的 CSS 结构
- CSS 自定义属性 (CSS Variables)
- 组件化架构设计
- 性能优化和代码规范

📋 开发规范:
- 建立完整的开发规范文档
- 制定 User Guidelines for Augment Chat
- 统一代码质量标准
- 规范化开发流程"
    echo -e "${CYAN}📝 使用默认提交信息${NC}"
fi

echo ""

# 步骤5: 执行提交
echo -e "${BLUE}💾 步骤5: 执行Git提交${NC}"
if git commit -m "$COMMIT_MESSAGE"; then
    echo -e "${GREEN}✅ 代码提交成功！${NC}"
else
    echo -e "${RED}❌ 代码提交失败${NC}"
    exit 1
fi

echo ""

# 步骤6: 推送到远程仓库（可选）
echo -e "${BLUE}🚀 步骤6: 推送到远程仓库${NC}"
read -p "是否要推送到远程仓库？(y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if git push; then
        echo -e "${GREEN}✅ 代码推送成功！${NC}"
    else
        echo -e "${RED}❌ 代码推送失败${NC}"
        echo -e "${YELLOW}💡 提示: 请检查网络连接和远程仓库配置${NC}"
        exit 1
    fi
else
    echo -e "${YELLOW}⏭️  跳过推送，代码已本地提交${NC}"
fi

echo ""
echo -e "${PURPLE}🎉 智能Git提交流程完成！${NC}"
echo -e "${CYAN}📊 提交摘要:${NC}"
echo -e "${CYAN}  - 代码质量检查: ✅ 通过${NC}"
echo -e "${CYAN}  - 本地提交: ✅ 完成${NC}"
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo -e "${CYAN}  - 远程推送: ✅ 完成${NC}"
else
    echo -e "${CYAN}  - 远程推送: ⏭️  跳过${NC}"
fi
echo ""
echo -e "${GREEN}🚀 开发流程已优化，代码质量有保障！${NC}"
