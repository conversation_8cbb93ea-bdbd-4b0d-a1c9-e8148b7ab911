{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"CONTEXT_DEPTH": "deep", "SEARCH_SCOPE": "project"}}, "memory-server": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_STORAGE": "./user-memories", "ENABLE_GRAPH": "true"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {"THINKING_DEPTH": "5", "ENABLE_REFLECTION": "true"}}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"TASK_STORAGE_PATH": "./tasks", "ENABLE_REFLECTION": "true", "CHAIN_OF_THOUGHT": "true", "ENABLE_PLANNING": "true"}}, "interactive-feedback": {"command": "npx", "args": ["-y", "mcp-interactive-feedback"], "env": {"FEEDBACK_STORAGE": "./feedback", "ENABLE_AI_REPORTS": "true", "REPORT_FORMAT": "markdown", "FORCE_FEEDBACK": "true"}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Documents/wechatcloud"], "env": {"ALLOWED_DIRECTORIES": "/Users/<USER>/Documents/wechatcloud", "ENABLE_WRITE": "true"}}, "playwright": {"command": "npx", "args": ["-y", "@playwright/mcp@latest"], "env": {"PLAYWRIGHT_HEADLESS": "true", "PLAYWRIGHT_TIMEOUT": "30000"}}, "chart-generator": {"command": "npx", "args": ["-y", "@antv/mcp-server-chart"], "env": {"CHART_OUTPUT_DIR": "./charts", "DEFAULT_THEME": "purple", "ENABLE_EXPORT": "true"}}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"], "env": {"DEBUG_MODE": "true"}}}}