#!/usr/bin/env python3
"""
壹心堂开发规范检查脚本 (统一版本)
整合开发规范检查和工作流程指导
确保每次开发都遵守CI_CD_STANDARDS.md中的强制约定
"""

import os
import re
import glob
import sys
import subprocess
from datetime import datetime

def log_message(message, status="INFO"):
    """日志输出"""
    colors = {
        "INFO": "\033[94m",
        "SUCCESS": "\033[92m",
        "ERROR": "\033[91m",
        "WARNING": "\033[93m",
        "HEADER": "\033[95m",
        "STEP": "\033[96m",
        "CRITICAL": "\033[41m\033[97m"  # 红底白字
    }
    reset = "\033[0m"
    timestamp = datetime.now().strftime("%H:%M:%S")
    print(f"{colors.get(status, '')}{timestamp} {status}: {message}{reset}")

def check_try_catch_blocks():
    """检查Try-Catch块的完整性 - 改进版本"""
    log_message("🚨 检查Try-Catch块完整性 (强制)", "HEADER")

    vue_files = glob.glob("admin/src/views/*.vue", recursive=True)
    issues = []

    for file_path in vue_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 提取JavaScript部分
            script_start = content.find('<script setup>')
            script_end = content.find('</script>')

            if script_start == -1 or script_end == -1:
                continue

            script_content = content[script_start:script_end]

            # 使用正则表达式查找try块
            try_pattern = r'try\s*\{'
            try_matches = list(re.finditer(try_pattern, script_content))

            for match in try_matches:
                try_start = match.start()

                # 查找对应的catch或finally
                remaining_content = script_content[try_start:]

                # 检查是否有catch或finally
                has_catch = re.search(r'}\s*catch\s*\(', remaining_content)
                has_finally = re.search(r'}\s*finally\s*\{', remaining_content)

                if not has_catch and not has_finally:
                    # 计算行号
                    line_num = script_content[:try_start].count('\n') + 1
                    issues.append(f"{file_path}:{line_num} - Try块缺少catch或finally")

        except Exception as e:
            log_message(f"检查文件失败 {file_path}: {e}", "ERROR")

    if issues:
        log_message("🚨 发现Try-Catch问题 (必须修复):", "CRITICAL")
        for issue in issues:
            log_message(f"  ❌ {issue}", "ERROR")
        return False
    else:
        log_message("✅ Try-Catch块检查通过", "SUCCESS")
        return True

def check_error_handling():
    """检查错误处理规范 - 改进版本"""
    log_message("🚨 检查错误处理规范 (强制)", "HEADER")

    vue_files = glob.glob("admin/src/views/*.vue", recursive=True)
    issues = []

    for file_path in vue_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 只检查有用户交互的页面
            has_user_interaction = any(keyword in content for keyword in [
                'handleSubmit', 'handleAdd', 'handleEdit', 'handleDelete',
                '@click=', 'button', 'form'
            ])

            if not has_user_interaction:
                continue

            # 检查是否有错误反馈机制
            has_error_feedback = any(keyword in content for keyword in [
                'showToast', 'console.error', 'alert', 'notification', 'message'
            ])

            if not has_error_feedback:
                issues.append(f"{file_path} - 缺少错误反馈机制")

            # 检查关键异步函数是否有错误处理
            critical_functions = ['handleSubmit', 'handleAdd', 'handleEdit', 'handleDelete']
            for func_name in critical_functions:
                if func_name in content:
                    # 查找函数定义
                    func_pattern = f'{func_name}\\s*=\\s*async\\s*\\([^{{]*{{[^}}]*}}'
                    func_match = re.search(func_pattern, content, re.DOTALL)
                    if func_match and 'try' not in func_match.group():
                        issues.append(f"{file_path} - 关键函数{func_name}缺少错误处理")

        except Exception as e:
            log_message(f"检查文件失败 {file_path}: {e}", "ERROR")

    if issues:
        log_message("🚨 发现错误处理问题 (必须修复):", "CRITICAL")
        for issue in issues:
            log_message(f"  ❌ {issue}", "ERROR")
        return False
    else:
        log_message("✅ 错误处理规范检查通过", "SUCCESS")
        return True

def check_form_validation():
    """检查表单验证规范 - 改进版本"""
    log_message("🚨 检查表单验证规范 (强制)", "HEADER")

    vue_files = glob.glob("admin/src/views/*.vue", recursive=True)
    issues = []

    for file_path in vue_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查是否有实际的表单输入
            has_form_inputs = any(keyword in content for keyword in [
                'v-model=', 'input', 'textarea', 'select'
            ])

            # 检查是否有模态框表单
            has_modal_form = 'modal' in content.lower() and has_form_inputs

            if has_modal_form or (has_form_inputs and any(keyword in content for keyword in [
                'handleSubmit', 'handleAdd', 'handleEdit'
            ])):
                # 检查是否有验证函数
                has_validation = any(keyword in content for keyword in [
                    'validateForm', 'validate', 'checkForm'
                ])

                if not has_validation:
                    issues.append(f"{file_path} - 表单缺少验证函数")

                # 检查是否有错误状态管理
                has_error_state = any(keyword in content for keyword in [
                    'formErrors', 'errors', 'errorMessages'
                ])

                if not has_error_state:
                    issues.append(f"{file_path} - 表单缺少错误状态管理")

        except Exception as e:
            log_message(f"检查文件失败 {file_path}: {e}", "ERROR")

    if issues:
        log_message("🚨 发现表单验证问题 (必须修复):", "CRITICAL")
        for issue in issues:
            log_message(f"  ❌ {issue}", "ERROR")
        return False
    else:
        log_message("✅ 表单验证规范检查通过", "SUCCESS")
        return True

def check_loading_states():
    """检查加载状态管理"""
    log_message("🚨 检查加载状态管理 (强制)", "HEADER")
    
    vue_files = glob.glob("admin/src/views/*.vue", recursive=True)
    issues = []
    
    for file_path in vue_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有异步操作
            if 'async' in content or 'await' in content:
                # 检查是否有加载状态管理
                if 'loadingStates' not in content and 'loading' not in content:
                    issues.append(f"{file_path} - 异步操作缺少加载状态管理")
                
                # 检查按钮是否有禁用状态
                if 'button' in content and ':disabled' not in content:
                    issues.append(f"{file_path} - 按钮缺少禁用状态控制")
                        
        except Exception as e:
            log_message(f"检查文件失败 {file_path}: {e}", "ERROR")
    
    if issues:
        log_message("🚨 发现加载状态问题 (必须修复):", "CRITICAL")
        for issue in issues:
            log_message(f"  ❌ {issue}", "ERROR")
        return False
    else:
        log_message("✅ 加载状态管理检查通过", "SUCCESS")
        return True

def check_user_feedback():
    """检查用户反馈机制"""
    log_message("🚨 检查用户反馈机制 (强制)", "HEADER")
    
    vue_files = glob.glob("admin/src/views/*.vue", recursive=True)
    issues = []
    
    for file_path in vue_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有用户操作
            if 'handleSubmit' in content or 'handleAdd' in content or 'handleEdit' in content:
                # 检查是否有Toast通知
                if 'showToast' not in content and 'toast' not in content.lower():
                    issues.append(f"{file_path} - 用户操作缺少Toast反馈")
                
                # 检查是否有成功和失败反馈
                success_feedback = 'success' in content or '成功' in content
                error_feedback = 'error' in content or '失败' in content
                
                if not success_feedback:
                    issues.append(f"{file_path} - 缺少成功操作反馈")
                if not error_feedback:
                    issues.append(f"{file_path} - 缺少失败操作反馈")
                        
        except Exception as e:
            log_message(f"检查文件失败 {file_path}: {e}", "ERROR")
    
    if issues:
        log_message("🚨 发现用户反馈问题 (必须修复):", "CRITICAL")
        for issue in issues:
            log_message(f"  ❌ {issue}", "ERROR")
        return False
    else:
        log_message("✅ 用户反馈机制检查通过", "SUCCESS")
        return True

def check_responsive_design():
    """检查PC端自适应缩放和零重叠"""
    log_message("🚨 检查PC端自适应缩放和零重叠 (强制)", "HEADER")

    vue_files = glob.glob("admin/src/views/*.vue", recursive=True)
    issues = []

    for file_path in vue_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()

            # 检查是否有CSS样式
            if '<style' in content:
                # 检查自适应缩放相关CSS
                has_scale_factor = '--scale-factor' in content or 'clamp(' in content or 'calc(100vw' in content
                has_transform_scale = 'transform: scale' in content

                if not has_scale_factor and not has_transform_scale:
                    issues.append(f"{file_path} - 缺少自适应缩放CSS (--scale-factor或transform: scale)")

                # 检查零重叠保护CSS
                has_overlap_protection = any(keyword in content for keyword in [
                    'z-index', 'position: relative', 'box-sizing: border-box',
                    'no-overlap', 'element-spacing'
                ])

                if not has_overlap_protection:
                    issues.append(f"{file_path} - 缺少零重叠保护CSS")

                # 检查PC端分辨率断点
                if '@media' not in content:
                    issues.append(f"{file_path} - 缺少PC端分辨率断点")

                # 检查最小间距保护
                has_spacing_protection = any(keyword in content for keyword in [
                    'margin:', 'padding:', 'gap:', 'min-height:'
                ])

                if not has_spacing_protection:
                    issues.append(f"{file_path} - 缺少最小间距保护")

        except Exception as e:
            log_message(f"检查文件失败 {file_path}: {e}", "ERROR")

    if issues:
        log_message("🚨 发现PC端自适应缩放和零重叠问题 (建议修复):", "WARNING")
        for issue in issues:
            log_message(f"  ⚠️ {issue}", "WARNING")
        return True  # 这些问题不阻止提交，但需要记录
    else:
        log_message("✅ PC端自适应缩放和零重叠检查通过", "SUCCESS")
        return True

def generate_compliance_report(results):
    """生成合规报告"""
    log_message("📊 生成开发规范合规报告", "HEADER")
    
    total_checks = len(results)
    passed_checks = sum(results.values())
    compliance_rate = (passed_checks / total_checks) * 100
    
    log_message(f"规范合规率: {compliance_rate:.1f}% ({passed_checks}/{total_checks})", "INFO")
    
    # 详细结果
    check_names = {
        "try_catch": "Try-Catch块完整性",
        "error_handling": "错误处理规范",
        "form_validation": "表单验证规范",
        "loading_states": "加载状态管理",
        "user_feedback": "用户反馈机制",
        "responsive_design": "PC端自适应缩放和零重叠"
    }
    
    critical_issues = []
    for key, name in check_names.items():
        if key in results:
            status = "✅ 通过" if results[key] else "❌ 失败"
            color = "SUCCESS" if results[key] else "ERROR"
            log_message(f"  {name}: {status}", color)
            
            if not results[key] and key != "responsive_design":
                critical_issues.append(name)
    
    return compliance_rate, critical_issues

def display_workflow_header():
    """显示工作流程头部"""
    log_message("🚨 壹心堂开发规范检查 - 统一版本", "HEADER")
    log_message("📋 基于CI_CD_STANDARDS.md强制约定", "INFO")
    log_message("🎯 确保代码质量和开发规范", "INFO")
    print()

def pre_development_checklist():
    """开发前置检查清单"""
    log_message("📋 开发前置检查清单 (强制执行)", "HEADER")

    checklist = [
        "阅读相关规范 - 熟悉CI_CD_STANDARDS.md和最新开发标准",
        "检查现有代码 - 了解当前实现和代码结构",
        "确认需求理解 - 明确功能需求和技术要求",
        "制定实施计划 - 详细的开发步骤和验证方案",
        "准备测试用例 - 功能测试和边界测试场景",
        "确认设计规范 - UI/UX设计和交互规范"
    ]

    log_message("请确认以下检查项目已完成:", "STEP")
    for i, item in enumerate(checklist, 1):
        print(f"  {i}. {item}")

    print()
    response = input("是否已完成所有前置检查? (y/N): ").strip().lower()

    if response != 'y':
        log_message("❌ 前置检查未完成，请完成后再开始开发", "ERROR")
        return False

    log_message("✅ 前置检查完成，可以开始开发", "SUCCESS")
    return True

def development_standards_reminder():
    """开发标准提醒"""
    log_message("🚨 开发过程强制要求提醒", "HEADER")

    standards = [
        "🚨 禁止使用批量修改脚本 - 必须手动逐个修改文件",
        "所有try块必须有对应的catch和/或finally",
        "所有异步函数必须有错误处理",
        "所有用户操作必须有加载状态和反馈",
        "所有表单必须有验证和错误提示",
        "所有API调用必须有超时和重试机制",
        "所有操作必须有即时反馈(Toast/Loading)",
        "所有表单必须支持键盘操作(Enter提交/Esc取消)",
        "所有按钮必须有防抖处理(300ms)",
        "所有模态框必须支持点击遮罩关闭",
        "所有列表必须支持分页和搜索"
    ]

    log_message("🚨 开发过程中必须遵守以下标准:", "CRITICAL")
    for i, standard in enumerate(standards, 1):
        log_message(f"  {i}. {standard}", "WARNING")

    print()
    input("按Enter键确认已了解开发标准...")

def run_standards_checks():
    """运行开发规范检查"""
    log_message("🚨 开始开发规范强制检查", "HEADER")

    # 执行各项检查
    results = {
        "try_catch": check_try_catch_blocks(),
        "error_handling": check_error_handling(),
        "form_validation": check_form_validation(),
        "loading_states": check_loading_states(),
        "user_feedback": check_user_feedback(),
        "responsive_design": check_responsive_design()
    }

    print()

    # 生成合规报告
    compliance_rate, critical_issues = generate_compliance_report(results)

    return compliance_rate >= 90 and len(critical_issues) == 0, critical_issues

def post_development_verification():
    """开发完成后验证"""
    log_message("🎯 开发完成强制验证", "HEADER")

    verification_items = [
        "核心功能正常 - 新增/编辑/删除/查询功能",
        "边界情况处理 - 空数据/网络错误/权限限制",
        "用户体验优化 - 加载状态/错误提示/成功反馈",
        "响应式适配 - 桌面/平板/手机三种设备测试",
        "性能表现 - 加载速度/内存使用/网络请求优化",
        "代码规范 - ESLint/Prettier检查通过",
        "类型安全 - TypeScript或JSDoc注释完整",
        "错误处理 - 所有异常情况都有处理",
        "测试覆盖 - 单元测试和集成测试通过",
        "文档更新 - 相关文档和注释更新"
    ]

    log_message("请确认以下验证项目:", "STEP")
    for i, item in enumerate(verification_items, 1):
        print(f"  {i}. {item}")

    print()
    response = input("是否已完成所有验证项目? (y/N): ").strip().lower()

    if response != 'y':
        log_message("❌ 验证未完成，请完成后再提交代码", "ERROR")
        return False

    log_message("✅ 开发完成验证通过", "SUCCESS")
    return True

def main():
    """主函数 - 支持不同模式"""
    import argparse

    parser = argparse.ArgumentParser(description='壹心堂开发规范检查脚本')
    parser.add_argument('--mode', choices=['check', 'workflow', 'quick'],
                       default='check', help='运行模式: check(仅检查), workflow(完整工作流), quick(快速检查)')

    args = parser.parse_args()

    if args.mode == 'workflow':
        # 完整工作流程模式
        display_workflow_header()

        # 1. 开发前置检查
        if not pre_development_checklist():
            sys.exit(1)

        print()

        # 2. 开发标准提醒
        development_standards_reminder()

        print()

        # 3. 开发过程 (用户自行完成)
        log_message("🔧 请按照规范进行开发...", "STEP")
        log_message("💡 开发过程中请时刻参考CI_CD_STANDARDS.md", "INFO")

        input("开发完成后按Enter键继续...")

        print()

        # 4. 规范检查
        checks_passed, critical_issues = run_standards_checks()

        if not checks_passed:
            log_message("🚨 规范检查失败！", "CRITICAL")
            log_message("❌ 以下问题必须修复:", "ERROR")
            for issue in critical_issues:
                log_message(f"  🚨 {issue}", "ERROR")
            log_message("🚫 请修复所有问题后重新运行检查", "CRITICAL")
            sys.exit(1)

        print()

        # 5. 开发完成验证
        if not post_development_verification():
            sys.exit(1)

        print()

        # 6. 最终确认
        log_message("🎉 开发工作流程完成！", "SUCCESS")
        log_message("✅ 所有规范检查通过，代码可以提交", "SUCCESS")
        log_message("🚀 请继续进行代码提交和部署流程", "INFO")

    elif args.mode == 'quick':
        # 快速检查模式
        log_message("⚡ 快速规范检查模式", "HEADER")
        checks_passed, critical_issues = run_standards_checks()

        if critical_issues:
            log_message("🚨 发现严重规范违规问题！", "CRITICAL")
            for issue in critical_issues:
                log_message(f"  🚨 {issue}", "CRITICAL")
            log_message("🚫 禁止提交代码，直到所有问题修复", "CRITICAL")
            return False
        else:
            log_message("🎉 快速检查通过！", "SUCCESS")
            return True

    else:
        # 默认检查模式
        log_message("🚨 开始开发规范强制检查", "HEADER")
        log_message("📋 基于CI_CD_STANDARDS.md强制约定", "INFO")

        checks_passed, critical_issues = run_standards_checks()

        # 最终判断
        log_message("🏁 规范检查结论", "HEADER")

        if critical_issues:
            log_message("🚨 发现严重规范违规问题！", "CRITICAL")
            log_message("❌ 以下问题必须立即修复:", "ERROR")
            for issue in critical_issues:
                log_message(f"  🚨 {issue}", "CRITICAL")
            log_message("🚫 禁止提交代码，直到所有问题修复", "CRITICAL")
            return False
        else:
            log_message("🎉 规范检查全部通过！", "SUCCESS")
            log_message("✅ 代码符合开发规范，可以提交", "SUCCESS")
            return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
