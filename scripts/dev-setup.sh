#!/bin/bash

# 壹心堂开发环境快速启动脚本
# 基于轮廓调试标准规则v4.0和现成工具集成方案
# 一键配置完整的开发环境

echo "🚀 壹心堂开发环境快速启动"
echo "📋 基于轮廓调试标准规则v4.0"
echo "🛠️ 集成现成的成熟工具"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

# 检查Node.js环境
echo -e "\n${BLUE}🔍 检查开发环境...${NC}"
if ! command -v node &> /dev/null; then
    echo -e "${RED}❌ Node.js 未安装${NC}"
    exit 1
fi

if ! command -v npm &> /dev/null; then
    echo -e "${RED}❌ npm 未安装${NC}"
    exit 1
fi

echo -e "${GREEN}✅ Node.js $(node --version)${NC}"
echo -e "${GREEN}✅ npm $(npm --version)${NC}"

# 安装依赖
echo -e "\n${BLUE}📦 安装项目依赖...${NC}"
cd admin

# 安装Stylelint相关依赖
echo -e "${YELLOW}🔧 安装CSS标准检查工具...${NC}"
npm install --save-dev \
  stylelint \
  stylelint-config-standard \
  stylelint-config-recommended-vue \
  stylelint-order \
  stylelint-scss \
  postcss-html \
  postcss-scss

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Stylelint工具安装成功${NC}"
else
    echo -e "${RED}❌ Stylelint工具安装失败${NC}"
    exit 1
fi

# 安装Playwright相关依赖
echo -e "${YELLOW}🎭 安装自动化测试工具...${NC}"
npm install --save-dev puppeteer

if [ $? -eq 0 ]; then
    echo -e "${GREEN}✅ Playwright工具安装成功${NC}"
else
    echo -e "${RED}❌ Playwright工具安装失败${NC}"
    exit 1
fi

# 验证配置文件
echo -e "\n${BLUE}📋 验证配置文件...${NC}"

# 检查CSS标准库
if [ -f "src/styles/standards.css" ]; then
    echo -e "${GREEN}✅ CSS标准库存在${NC}"
else
    echo -e "${RED}❌ CSS标准库缺失${NC}"
    echo -e "${YELLOW}💡 请确保 src/styles/standards.css 文件存在${NC}"
fi

# 检查Stylelint配置
if [ -f ".stylelintrc.js" ]; then
    echo -e "${GREEN}✅ Stylelint配置存在${NC}"
else
    echo -e "${RED}❌ Stylelint配置缺失${NC}"
    echo -e "${YELLOW}💡 请确保 .stylelintrc.js 文件存在${NC}"
fi

# 检查标准组件模板
if [ -d "src/components/standards" ]; then
    echo -e "${GREEN}✅ 标准组件模板存在${NC}"
else
    echo -e "${RED}❌ 标准组件模板缺失${NC}"
    echo -e "${YELLOW}💡 请确保 src/components/standards 目录存在${NC}"
fi

# 运行初始检查
echo -e "\n${BLUE}🔍 运行初始质量检查...${NC}"

# CSS标准检查
echo -e "${YELLOW}📏 CSS标准合规性检查...${NC}"
if npm run stylelint-check; then
    echo -e "${GREEN}✅ CSS标准检查通过${NC}"
else
    echo -e "${YELLOW}⚠️ CSS标准检查发现问题，可运行 'npm run stylelint' 自动修复${NC}"
fi

# 轮廓调试检查（如果服务器运行中）
echo -e "${YELLOW}🎯 检查开发服务器状态...${NC}"
if curl -s http://localhost:3002 > /dev/null; then
    echo -e "${GREEN}✅ 开发服务器运行中${NC}"
    echo -e "${YELLOW}🎭 运行轮廓调试检查...${NC}"
    if npm run outline-check; then
        echo -e "${GREEN}✅ 轮廓调试检查通过${NC}"
    else
        echo -e "${YELLOW}⚠️ 轮廓调试检查发现问题${NC}"
    fi
else
    echo -e "${YELLOW}⚠️ 开发服务器未运行，跳过轮廓调试检查${NC}"
    echo -e "${YELLOW}💡 启动服务器后可运行 'npm run outline-check'${NC}"
fi

# 配置Git Hooks
echo -e "\n${BLUE}🔗 配置Git Hooks...${NC}"
cd ..
if [ -f ".githooks/pre-commit" ]; then
    chmod +x .githooks/pre-commit
    git config core.hooksPath .githooks
    echo -e "${GREEN}✅ Git Hooks配置成功${NC}"
else
    echo -e "${YELLOW}⚠️ Git Hooks文件不存在${NC}"
fi

# 显示推荐工具安装指南
echo -e "\n${PURPLE}🛠️ 推荐工具安装指南${NC}"
echo -e "${PURPLE}===================${NC}"

echo -e "\n${BLUE}📝 IntelliJ IDEA插件（您当前使用的IDE）：${NC}"
echo -e "1. ${YELLOW}Stylelint${NC} - 实时CSS标准检查"
echo -e "   Settings → Plugins → 搜索 'Stylelint' → 安装"
echo -e "2. ${YELLOW}CSS Support${NC} - 增强CSS支持"
echo -e "   Settings → Plugins → 搜索 'CSS Support' → 安装"
echo -e "3. ${YELLOW}Vue.js${NC} - Vue组件支持"
echo -e "   Settings → Plugins → 搜索 'Vue.js' → 安装"

echo -e "\n${BLUE}🌐 Chrome浏览器扩展：${NC}"
echo -e "1. ${YELLOW}Debug CSS${NC} ⭐⭐⭐⭐⭐ - 一键轮廓调试"
echo -e "   https://chromewebstore.google.com/detail/debug-css/igiofjnckcagmjgdoaakafngegecjnkj"
echo -e "2. ${YELLOW}Web Developer${NC} ⭐⭐⭐⭐⭐ - 综合开发工具"
echo -e "   https://chromewebstore.google.com/detail/web-developer/bfbameneiokkgbdmiekhjnmfkcnldhhm"

# 显示可用命令
echo -e "\n${PURPLE}📋 可用的开发命令${NC}"
echo -e "${PURPLE}===============${NC}"
echo -e "${YELLOW}npm run stylelint${NC}        - 自动修复CSS问题"
echo -e "${YELLOW}npm run stylelint-check${NC}  - 检查CSS标准合规性"
echo -e "${YELLOW}npm run outline-check${NC}    - 轮廓调试检查"
echo -e "${YELLOW}npm run pre-commit${NC}       - 运行提交前检查"

# 显示开发工作流程
echo -e "\n${PURPLE}🔄 推荐的开发工作流程${NC}"
echo -e "${PURPLE}==================${NC}"
echo -e "1. ${BLUE}IDEA中编码${NC} - Stylelint实时检查"
echo -e "2. ${BLUE}浏览器调试${NC} - Debug CSS扩展轮廓调试"
echo -e "3. ${BLUE}提交前检查${NC} - Git Hooks自动验证"
echo -e "4. ${BLUE}团队协作${NC} - 统一的工具和标准"

echo -e "\n${GREEN}🎉 开发环境配置完成！${NC}"
echo -e "${GREEN}📋 基于轮廓调试标准规则v4.0${NC}"
echo -e "${GREEN}🛠️ 使用成熟的现成工具${NC}"
echo -e "${GREEN}✅ 预期节省2-4周开发时间${NC}"

echo -e "\n${YELLOW}💡 下一步：${NC}"
echo -e "1. 安装推荐的IDEA插件和Chrome扩展"
echo -e "2. 启动开发服务器测试轮廓调试功能"
echo -e "3. 向团队成员推广这套工具组合"

cd admin
