#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完成后自检脚本模板
用于验证代码修改的完整性和正确性
"""

import re
import os

def comprehensive_self_check(file_path, check_config=None):
    """
    全面自检函数
    
    Args:
        file_path: 要检查的文件路径
        check_config: 检查配置字典
    
    Returns:
        bool: 检查是否通过
    """
    print(f'🔍 {os.path.basename(file_path)} - 全面自检')
    print('=' * 60)
    
    if not os.path.exists(file_path):
        print(f'❌ 文件不存在: {file_path}')
        return False
    
    with open(file_path, 'r', encoding='utf-8') as f:
        content = f.read()
    
    issues = []
    
    # 1. HTML结构检查 (适用于Vue文件)
    if file_path.endswith('.vue'):
        issues.extend(check_html_structure(content))
    
    # 2. 函数定义检查
    if check_config and 'required_functions' in check_config:
        issues.extend(check_function_definitions(content, check_config['required_functions']))
    
    # 3. 模板引用检查
    if check_config and 'template_calls' in check_config:
        issues.extend(check_template_references(content, check_config['template_calls']))
    
    # 4. CSS样式检查
    if check_config and 'required_styles' in check_config:
        issues.extend(check_css_styles(content, check_config['required_styles']))
    
    # 5. 事件绑定检查
    if check_config and 'event_bindings' in check_config:
        issues.extend(check_event_bindings(content, check_config['event_bindings']))
    
    # 6. 语法错误检查
    issues.extend(check_syntax_errors(content))
    
    # 7. 功能完整性检查
    if check_config and 'completeness_checks' in check_config:
        issues.extend(check_completeness(content, check_config['completeness_checks']))
    
    # 总结
    print('\n' + '='*60)
    if not issues:
        print('🎉 自检通过！所有功能正常，代码质量良好。')
        return True
    else:
        print(f'⚠️  发现 {len(issues)} 个问题:')
        for i, issue in enumerate(issues, 1):
            print(f'   {i}. {issue}')
        return False

def check_html_structure(content):
    """检查HTML结构"""
    issues = []
    print('1. HTML结构检查:')
    
    template_start = content.find('<template>')
    template_end = content.find('</template>')
    
    if template_start == -1 or template_end == -1:
        print('   ⚠️  非Vue模板文件，跳过HTML检查')
        return issues
    
    template_content = content[template_start:template_end]
    
    # 检查div标签匹配
    open_divs = template_content.count('<div')
    close_divs = template_content.count('</div>')
    if open_divs == close_divs:
        print('   ✅ div标签匹配')
    else:
        print(f'   ❌ div标签不匹配: {open_divs}开 vs {close_divs}闭')
        issues.append('HTML结构不完整')
    
    return issues

def check_function_definitions(content, required_functions):
    """检查函数定义"""
    issues = []
    print('\n2. 函数定义检查:')
    
    for func in required_functions:
        if f'const {func}' in content or f'function {func}' in content:
            print(f'   ✅ {func} 已定义')
        else:
            print(f'   ❌ {func} 缺失')
            issues.append(f'函数{func}未定义')
    
    return issues

def check_template_references(content, template_calls):
    """检查模板引用"""
    issues = []
    print('\n3. 模板引用检查:')
    
    template_start = content.find('<template>')
    template_end = content.find('</template>')
    
    if template_start == -1:
        print('   ⚠️  非Vue模板文件，跳过模板检查')
        return issues
    
    template_content = content[template_start:template_end]
    
    for call in template_calls:
        if call in template_content:
            print(f'   ✅ {call} 在模板中被调用')
        else:
            print(f'   ❌ {call} 在模板中未找到')
            issues.append(f'模板中缺少{call}调用')
    
    return issues

def check_css_styles(content, required_styles):
    """检查CSS样式"""
    issues = []
    print('\n4. CSS样式检查:')
    
    for style in required_styles:
        if style in content:
            print(f'   ✅ {style} 样式已定义')
        else:
            print(f'   ❌ {style} 样式缺失')
            issues.append(f'CSS样式{style}未定义')
    
    return issues

def check_event_bindings(content, event_bindings):
    """检查事件绑定"""
    issues = []
    print('\n5. 事件绑定检查:')
    
    for binding in event_bindings:
        if binding in content:
            print(f'   ✅ {binding} 事件已绑定')
        else:
            print(f'   ❌ {binding} 事件缺失')
            issues.append(f'事件绑定{binding}缺失')
    
    return issues

def check_syntax_errors(content):
    """检查语法错误"""
    issues = []
    print('\n6. 语法错误检查:')
    
    # 检查常见语法错误
    syntax_checks = [
        ('未闭合的括号', lambda c: c.count('(') == c.count(')')),
        ('未闭合的大括号', lambda c: c.count('{') == c.count('}')),
        ('未闭合的方括号', lambda c: c.count('[') == c.count(']'))
    ]
    
    for check_name, check_func in syntax_checks:
        if check_func(content):
            print(f'   ✅ {check_name} 检查通过')
        else:
            print(f'   ❌ {check_name} 检查失败')
            issues.append(check_name)
    
    return issues

def check_completeness(content, completeness_checks):
    """检查功能完整性"""
    issues = []
    print('\n7. 功能完整性检查:')
    
    for check_name, check_pattern in completeness_checks.items():
        if re.search(check_pattern, content, re.DOTALL):
            print(f'   ✅ {check_name} 检查通过')
        else:
            print(f'   ❌ {check_name} 检查失败')
            issues.append(f'{check_name}不完整')
    
    return issues

# 使用示例
if __name__ == '__main__':
    # 示例配置
    config = {
        'required_functions': ['focusSearchInput', 'getSortTitle', 'getFieldName'],
        'template_calls': ['focusSearchInput', 'getSortTitle', 'handleSort'],
        'required_styles': ['.service-info-header', '.sort-btn', '.header-text'],
        'event_bindings': ['@click="focusSearchInput"', '@click="handleSort'],
        'completeness_checks': {
            '服务信息表头结构': r'service-info-header.*?</div>'
        }
    }
    
    # 执行检查
    result = comprehensive_self_check('admin/src/views/ServiceManagement.vue', config)
    print(f'\n最终结果: {"通过" if result else "失败"}')
