#!/usr/bin/env node

/**
 * 🎯 字段一致性修复脚本
 * 
 * 功能：
 * - 统一所有管理页面的搜索字段名称
 * - 确保与服务管理页面的字段名完全一致
 * - 修复搜索、排序、过渡动画等相关字段引用
 * 
 * 标准字段名：name, phone, rating, joinDate
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-28
 */

const fs = require('fs');
const path = require('path');

// 🎯 标准字段映射 - 基于服务管理页面
const STANDARD_FIELDS = ['name', 'phone', 'rating', 'joinDate'];

// 🎯 页面字段映射配置
const PAGE_FIELD_MAPPINGS = {
  'TherapistManagement.vue': {
    // 技师管理页面 - 已经是标准字段，无需修改
    currentFields: ['name', 'phone', 'rating', 'joinDate'],
    standardFields: ['name', 'phone', 'rating', 'joinDate'],
    fieldLabels: ['技师信息', '联系方式', '绩效评价', '入职时间']
  },
  
  'CustomerManagement.vue': {
    // 客户管理页面 - 需要修复
    currentFields: ['name', 'phone', 'level', 'points'],
    standardFields: ['name', 'phone', 'rating', 'joinDate'],
    fieldLabels: ['客户信息', '联系方式', '客户等级', '积分余额']
  },
  
  'AppointmentManagement.vue': {
    // 预约管理页面 - 需要修复
    currentFields: ['customer', 'service', 'therapist', 'time'],
    standardFields: ['name', 'phone', 'rating', 'joinDate'],
    fieldLabels: ['预约信息', '服务项目', '预约时间', '技师安排']
  },
  
  'FinanceOverview.vue': {
    // 财务概览页面 - 需要修复
    currentFields: ['item', 'income', 'expense', 'profit'],
    standardFields: ['name', 'phone', 'rating', 'joinDate'],
    fieldLabels: ['财务项目', '金额', '类型', '交易时间']
  }
};

// 🎯 需要修复的字段引用模式
const FIELD_REFERENCE_PATTERNS = [
  // 响应式数据定义
  {
    pattern: /const searchModes = reactive\(\{[\s\S]*?\}\);/g,
    type: 'searchModes'
  },
  {
    pattern: /const searchValues = reactive\(\{[\s\S]*?\}\);/g,
    type: 'searchValues'
  },
  {
    pattern: /const searchInputRefs = reactive\(\{[\s\S]*?\}\);/g,
    type: 'searchInputRefs'
  },
  {
    pattern: /const sortButtonStates = reactive\(\{[\s\S]*?\}\);/g,
    type: 'sortButtonStates'
  },
  {
    pattern: /const isTransitioning = reactive\(\{[\s\S]*?\}\);/g,
    type: 'isTransitioning'
  },
  
  // 模板引用
  {
    pattern: /searchModes\.\w+/g,
    type: 'templateSearchModes'
  },
  {
    pattern: /searchValues\.\w+/g,
    type: 'templateSearchValues'
  },
  {
    pattern: /searchInputRefs\.\w+/g,
    type: 'templateSearchInputRefs'
  },
  {
    pattern: /sortButtonStates\.\w+/g,
    type: 'templateSortButtonStates'
  },
  {
    pattern: /isTransitioning\.\w+/g,
    type: 'templateIsTransitioning'
  }
];

// 🎯 生成标准字段定义
function generateStandardFieldDefinition(type) {
  const fields = STANDARD_FIELDS;
  
  switch (type) {
    case 'searchModes':
      return `const searchModes = reactive({
      name: false,
      phone: false,
      rating: false,
      joinDate: false
    });`;
    
    case 'searchValues':
      return `const searchValues = reactive({
      name: '',
      phone: '',
      rating: '',
      joinDate: ''
    });`;
    
    case 'searchInputRefs':
      return `const searchInputRefs = reactive({
      name: null,
      phone: null,
      rating: null,
      joinDate: null
    });`;
    
    case 'sortButtonStates':
      return `const sortButtonStates = reactive({
      name: { loading: false, disabled: false },
      phone: { loading: false, disabled: false },
      rating: { loading: false, disabled: false },
      joinDate: { loading: false, disabled: false }
    });`;
    
    case 'isTransitioning':
      return `const isTransitioning = reactive({
      name: false,
      phone: false,
      rating: false,
      joinDate: false
    });`;
    
    default:
      return '';
  }
}

// 🎯 修复页面字段一致性
function fixPageFieldConsistency(pageName, content) {
  console.log(`🔄 修复页面: ${pageName}`);
  
  const mapping = PAGE_FIELD_MAPPINGS[pageName];
  if (!mapping) {
    console.log(`⚠️  页面 ${pageName} 无需修复或未配置映射`);
    return content;
  }
  
  let fixedContent = content;
  let changeCount = 0;
  
  // 修复响应式数据定义
  FIELD_REFERENCE_PATTERNS.forEach(pattern => {
    if (pattern.type.startsWith('template')) return; // 跳过模板引用，单独处理
    
    const matches = fixedContent.match(pattern.pattern);
    if (matches) {
      matches.forEach(match => {
        const standardDefinition = generateStandardFieldDefinition(pattern.type);
        if (standardDefinition) {
          fixedContent = fixedContent.replace(match, standardDefinition);
          changeCount++;
          console.log(`  ✅ 修复 ${pattern.type} 定义`);
        }
      });
    }
  });
  
  // 修复模板中的字段引用
  mapping.currentFields.forEach((currentField, index) => {
    const standardField = mapping.standardFields[index];
    if (currentField !== standardField) {
      // 修复各种字段引用
      const patterns = [
        new RegExp(`searchModes\\.${currentField}`, 'g'),
        new RegExp(`searchValues\\.${currentField}`, 'g'),
        new RegExp(`searchInputRefs\\.${currentField}`, 'g'),
        new RegExp(`sortButtonStates\\.${currentField}`, 'g'),
        new RegExp(`isTransitioning\\.${currentField}`, 'g'),
        new RegExp(`handleSearchInput\\('${currentField}'\\)`, 'g'),
        new RegExp(`handleSearchBlur\\('${currentField}'\\)`, 'g'),
        new RegExp(`handleClickToSearch\\('${currentField}'\\)`, 'g'),
        new RegExp(`exitSearchMode\\('${currentField}'\\)`, 'g'),
        new RegExp(`handleSort\\('${currentField}'\\)`, 'g'),
        new RegExp(`getSortClass\\('${currentField}'\\)`, 'g'),
        new RegExp(`getSortIcon\\('${currentField}'\\)`, 'g')
      ];
      
      patterns.forEach(pattern => {
        const beforeCount = (fixedContent.match(pattern) || []).length;
        fixedContent = fixedContent.replace(pattern, (match) => {
          return match.replace(currentField, standardField);
        });
        const afterCount = (fixedContent.match(pattern) || []).length;
        if (beforeCount > afterCount) {
          changeCount += (beforeCount - afterCount);
        }
      });
      
      console.log(`  ✅ 字段映射: ${currentField} → ${standardField}`);
    }
  });
  
  console.log(`  📊 总计修复: ${changeCount} 处`);
  return fixedContent;
}

// 🎯 主函数
async function fixAllPagesFieldConsistency() {
  console.log('🚀 开始修复所有页面的字段一致性...');
  console.log('📋 基于服务管理页面的标准字段');
  
  const viewsDir = path.join(__dirname, '../admin/src/views');
  let totalChanges = 0;
  
  for (const [pageName, mapping] of Object.entries(PAGE_FIELD_MAPPINGS)) {
    const pagePath = path.join(viewsDir, pageName);
    
    if (!fs.existsSync(pagePath)) {
      console.log(`❌ 页面文件不存在: ${pageName}`);
      continue;
    }
    
    try {
      // 读取原文件
      const originalContent = fs.readFileSync(pagePath, 'utf8');
      
      // 修复字段一致性
      const fixedContent = fixPageFieldConsistency(pageName, originalContent);
      
      // 检查是否有变更
      if (fixedContent !== originalContent) {
        // 备份原文件
        const backupPath = `${pagePath}_FieldFix_Backup_${Date.now()}.vue`;
        fs.writeFileSync(backupPath, originalContent, 'utf8');
        console.log(`  📋 原文件已备份: ${backupPath}`);
        
        // 写入修复后的文件
        fs.writeFileSync(pagePath, fixedContent, 'utf8');
        console.log(`  ✅ 页面修复完成: ${pageName}`);
        totalChanges++;
      } else {
        console.log(`  ℹ️  页面无需修复: ${pageName}`);
      }
      
    } catch (error) {
      console.error(`❌ 修复页面出错 ${pageName}:`, error.message);
    }
  }
  
  console.log('\n🎉 字段一致性修复完成！');
  console.log(`📊 修复统计:`);
  console.log(`   - 检查页面数: ${Object.keys(PAGE_FIELD_MAPPINGS).length}`);
  console.log(`   - 修复页面数: ${totalChanges}`);
  console.log(`   - 标准字段: ${STANDARD_FIELDS.join(', ')}`);
}

// 运行脚本
if (require.main === module) {
  fixAllPagesFieldConsistency();
}

module.exports = { fixAllPagesFieldConsistency, PAGE_FIELD_MAPPINGS };
