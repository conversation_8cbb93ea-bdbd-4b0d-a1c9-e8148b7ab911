/**
 * CSS修改强制流程工具
 * 每次修改CSS都必须使用这个流程，避免层级冲突问题
 */

class CSSModificationWorkflow {
  constructor() {
    this.currentStep = 0;
    this.totalSteps = 5;
    this.targetSelector = '';
    this.conflictingRules = [];
    this.strongestRule = null;
  }

  /**
   * 开始CSS修改流程
   * @param {string} targetSelector - 要修改的CSS选择器
   */
  start(targetSelector) {
    this.targetSelector = targetSelector;
    this.currentStep = 0;
    console.log('🚨 开始CSS修改强制流程');
    console.log(`🎯 目标选择器: ${targetSelector}`);
    console.log('=' .repeat(50));
    
    this.step1_checkConflicts();
  }

  /**
   * 步骤1: 检查现有CSS规则冲突
   */
  step1_checkConflicts() {
    this.currentStep = 1;
    console.log(`🔍 [${this.currentStep}/${this.totalSteps}] 正在检查CSS规则冲突...`);
    
    this.conflictingRules = [];
    
    // 遍历所有样式表
    for (let sheet of document.styleSheets) {
      try {
        for (let rule of sheet.cssRules) {
          if (rule.style && rule.selectorText) {
            // 检查是否与目标选择器相关
            if (this.isRelatedSelector(rule.selectorText, this.targetSelector)) {
              this.conflictingRules.push({
                selector: rule.selectorText,
                specificity: this.calculateSpecificity(rule.selectorText),
                hasImportant: rule.cssText.includes('!important'),
                cssText: rule.cssText.substring(0, 150),
                sheet: sheet.href || 'inline'
              });
            }
          }
        }
      } catch (e) {
        // 跨域样式表会报错，忽略
      }
    }
    
    console.log(`📋 找到 ${this.conflictingRules.length} 个相关CSS规则:`);
    this.conflictingRules.forEach((rule, index) => {
      console.log(`  ${index + 1}. ${rule.selector} (优先级: ${rule.specificity}${rule.hasImportant ? ' + !important' : ''})`);
    });
    
    this.step2_analyzeSpecificity();
  }

  /**
   * 步骤2: 分析选择器优先级
   */
  step2_analyzeSpecificity() {
    this.currentStep = 2;
    console.log(`📊 [${this.currentStep}/${this.totalSteps}] 正在分析选择器优先级...`);
    
    // 按优先级排序
    this.conflictingRules.sort((a, b) => {
      // 先比较!important
      if (a.hasImportant && !b.hasImportant) return -1;
      if (!a.hasImportant && b.hasImportant) return 1;
      // 再比较特异性
      return b.specificity - a.specificity;
    });
    
    if (this.conflictingRules.length > 0) {
      this.strongestRule = this.conflictingRules[0];
      console.log(`🏆 最强的规则: ${this.strongestRule.selector}`);
      console.log(`   优先级: ${this.strongestRule.specificity}${this.strongestRule.hasImportant ? ' + !important' : ''}`);
      console.log(`   CSS: ${this.strongestRule.cssText.substring(0, 100)}...`);
    } else {
      console.log('✅ 没有发现冲突规则');
    }
    
    this.step3_generateCSS();
  }

  /**
   * 步骤3: 生成CSS规则建议
   */
  step3_generateCSS() {
    this.currentStep = 3;
    console.log(`✍️ [${this.currentStep}/${this.totalSteps}] 生成CSS规则建议...`);
    
    if (this.strongestRule) {
      const recommendedSpecificity = this.strongestRule.specificity + 1;
      const needsImportant = this.strongestRule.hasImportant;
      
      console.log('💡 建议的CSS规则:');
      console.log(`   选择器优先级应该 > ${this.strongestRule.specificity}`);
      console.log(`   ${needsImportant ? '必须' : '可选'}使用 !important`);
      console.log('   建议的选择器格式:');
      console.log(`   .parent .child.${this.targetSelector.replace('.', '')} {`);
      console.log(`     /* 你的CSS属性 */ ${needsImportant ? '!important' : ''};`);
      console.log(`   }`);
    } else {
      console.log('✅ 可以使用普通的CSS规则');
    }
    
    console.log('⚠️  请手动写CSS规则，然后调用 workflow.step4_verify()');
  }

  /**
   * 步骤4: 验证CSS生效
   */
  step4_verify() {
    this.currentStep = 4;
    console.log(`🔄 [${this.currentStep}/${this.totalSteps}] 验证CSS是否生效...`);
    
    console.log('请刷新页面，然后调用 workflow.step5_confirm()');
  }

  /**
   * 步骤5: 确认问题解决
   */
  step5_confirm() {
    this.currentStep = 5;
    console.log(`✅ [${this.currentStep}/${this.totalSteps}] 确认问题是否解决...`);
    
    console.log('请检查以下项目:');
    console.log('□ CSS规则是否生效？');
    console.log('□ 页面刷新后效果是否保持？');
    console.log('□ 没有产生新的副作用？');
    console.log('□ 问题是否彻底解决？');
    
    console.log('🎉 如果所有项目都确认，CSS修改流程完成！');
  }

  /**
   * 检查选择器是否相关
   */
  isRelatedSelector(selector, target) {
    const targetClass = target.replace('.', '');
    return selector.includes(targetClass) || 
           selector.includes('ant-input') || 
           selector.includes('input');
  }

  /**
   * 计算CSS选择器特异性
   */
  calculateSpecificity(selector) {
    const ids = (selector.match(/#/g) || []).length * 100;
    const classes = (selector.match(/\./g) || []).length * 10;
    const elements = (selector.match(/[a-zA-Z]/g) || []).length;
    return ids + classes + elements;
  }
}

// 创建全局实例
if (typeof window !== 'undefined') {
  window.cssWorkflow = new CSSModificationWorkflow();
  console.log('🔧 CSS修改流程工具已加载');
  console.log('使用方法: cssWorkflow.start(".your-selector")');
}

// 导出
if (typeof module !== 'undefined' && module.exports) {
  module.exports = CSSModificationWorkflow;
}
