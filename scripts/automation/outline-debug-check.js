#!/usr/bin/env node

/**
 * 轮廓调试自动检查工具 v1.0
 * 基于历史成功案例和轮廓调试标准规则v4.0制定
 * 目的：自动化执行轮廓调试检查，预防布局问题
 * 
 * 使用方法：
 * node scripts/automation/outline-debug-check.js
 * npm run outline-check
 */

const puppeteer = require('../../admin/node_modules/puppeteer');
const fs = require('fs');
const path = require('path');

// 配置项
const CONFIG = {
  // 测试URL
  baseUrl: 'http://localhost:3002',
  
  // 测试页面
  pages: [
    '/services',
    '/customers', 
    '/technicians',
    '/appointments'
  ],
  
  // 测试分辨率（基于历史成功案例和壹心堂标准）
  resolutions: [
    { width: 1024, height: 768, name: '1024x768', category: 'tablet' },
    { width: 1366, height: 768, name: '1366x768', category: 'desktop' },
    { width: 1920, height: 1080, name: '1920x1080', category: 'large' },
    { width: 2560, height: 1440, name: '2K', category: 'ultra' },
    { width: 3840, height: 2160, name: '4K', category: 'ultra' }
  ],
  
  // 轮廓调试检查项目（基于轮廓调试标准规则v4.0）
  outlineChecks: [
    { color: 'blue', name: '表格容器', selector: '.table-container' },
    { color: 'green', name: '表格主体', selector: '.table-body' },
    { color: 'red', name: '最后一行', selector: '.data-row:last-child' },
    { color: 'orange', name: '翻页组件', selector: '.pagination-container' },
    { color: 'purple', name: '数据行', selector: '.data-row:first-child' },
    { color: 'yellow', name: '操作按钮', selector: '.action-fragments' }
  ],
  
  // 成功标准（基于历史验证结果）
  successCriteria: {
    borderCompliance: true,    // 边界约束合规
    spacingAlignment: true,    // 间距对齐正确
    maxOverflow: 5,           // 最大允许超出像素
    minSuccessRate: 90        // 最小成功率（%）
  }
};

/**
 * 轮廓调试检查函数
 * 基于历史成功的getBoundingClientRect()方法
 */
async function performOutlineCheck(page) {
  return await page.evaluate(() => {
    console.log('🎯 开始轮廓调试检查');
    
    const results = {
      timestamp: new Date().toISOString(),
      resolution: `${window.innerWidth}x${window.innerHeight}`,
      checks: [],
      summary: {
        totalChecks: 0,
        passedChecks: 0,
        failedChecks: 0,
        successRate: 0
      }
    };
    
    // 检查项目配置
    const checks = [
      { color: 'blue', name: '表格容器', selector: '.table-container' },
      { color: 'green', name: '表格主体', selector: '.table-body' },
      { color: 'red', name: '最后一行', selector: '.data-row:last-child' },
      { color: 'orange', name: '翻页组件', selector: '.pagination-container' }
    ];
    
    checks.forEach(check => {
      const element = document.querySelector(check.selector);
      if (element) {
        const rect = element.getBoundingClientRect();
        const checkResult = {
          color: check.color,
          name: check.name,
          selector: check.selector,
          found: true,
          coordinates: {
            top: Math.round(rect.top),
            bottom: Math.round(rect.bottom),
            left: Math.round(rect.left),
            right: Math.round(rect.right),
            width: Math.round(rect.width),
            height: Math.round(rect.height)
          },
          passed: true,
          issues: []
        };
        
        // 基于历史成功案例的边界检查
        if (check.color === 'red') {
          // 检查红色区域是否在绿色区域内
          const greenElement = document.querySelector('.table-body');
          if (greenElement) {
            const greenRect = greenElement.getBoundingClientRect();
            const redInGreen = rect.bottom <= greenRect.bottom;
            if (!redInGreen) {
              checkResult.passed = false;
              checkResult.issues.push(`红色区域超出绿色区域 ${rect.bottom - greenRect.bottom}px`);
            }
          }
        }
        
        results.checks.push(checkResult);
        results.summary.totalChecks++;
        if (checkResult.passed) {
          results.summary.passedChecks++;
        } else {
          results.summary.failedChecks++;
        }
      } else {
        results.checks.push({
          color: check.color,
          name: check.name,
          selector: check.selector,
          found: false,
          passed: false,
          issues: ['元素未找到']
        });
        results.summary.totalChecks++;
        results.summary.failedChecks++;
      }
    });
    
    // 计算成功率
    results.summary.successRate = results.summary.totalChecks > 0 
      ? Math.round((results.summary.passedChecks / results.summary.totalChecks) * 100)
      : 0;
    
    console.log('✅ 轮廓调试检查完成');
    console.log(`📊 成功率: ${results.summary.successRate}%`);
    
    return results;
  });
}

/**
 * 主检查函数
 */
async function runOutlineChecks() {
  console.log('🚀 启动轮廓调试自动检查工具');
  console.log('📋 基于轮廓调试标准规则v4.0');
  
  let browser;
  const allResults = [];
  
  try {
    // 启动浏览器
    browser = await puppeteer.launch({
      headless: true,
      args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // 遍历所有页面和分辨率
    for (const pagePath of CONFIG.pages) {
      console.log(`\n📄 检查页面: ${pagePath}`);
      
      for (const resolution of CONFIG.resolutions) {
        console.log(`📱 分辨率: ${resolution.name}`);
        
        // 设置分辨率
        await page.setViewport({
          width: resolution.width,
          height: resolution.height
        });
        
        // 导航到页面
        const url = `${CONFIG.baseUrl}${pagePath}`;
        await page.goto(url, { waitUntil: 'networkidle0' });
        
        // 等待页面加载完成
        await page.waitForTimeout(2000);
        
        // 执行轮廓调试检查
        const result = await performOutlineCheck(page);
        result.page = pagePath;
        result.resolution = resolution.name;
        
        allResults.push(result);
        
        // 输出结果
        console.log(`  ✅ 成功率: ${result.summary.successRate}%`);
        if (result.summary.failedChecks > 0) {
          console.log(`  ❌ 失败项目: ${result.summary.failedChecks}`);
        }
      }
    }
    
  } catch (error) {
    console.error('❌ 检查过程中出现错误:', error);
  } finally {
    if (browser) {
      await browser.close();
    }
  }
  
  // 生成报告
  generateReport(allResults);
  
  return allResults;
}

/**
 * 生成检查报告
 */
function generateReport(results) {
  const reportPath = path.join(__dirname, '../../reports');
  
  // 确保报告目录存在
  if (!fs.existsSync(reportPath)) {
    fs.mkdirSync(reportPath, { recursive: true });
  }
  
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  const reportFile = path.join(reportPath, `outline-check-${timestamp}.json`);
  
  // 计算总体统计
  const totalChecks = results.reduce((sum, r) => sum + r.summary.totalChecks, 0);
  const totalPassed = results.reduce((sum, r) => sum + r.summary.passedChecks, 0);
  const overallSuccessRate = totalChecks > 0 ? Math.round((totalPassed / totalChecks) * 100) : 0;
  
  const report = {
    timestamp: new Date().toISOString(),
    tool: 'outline-debug-check v1.0',
    basedOn: '轮廓调试标准规则v4.0',
    summary: {
      totalTests: results.length,
      totalChecks,
      totalPassed,
      totalFailed: totalChecks - totalPassed,
      overallSuccessRate,
      meetsCriteria: overallSuccessRate >= CONFIG.successCriteria.minSuccessRate
    },
    results
  };
  
  // 保存报告
  fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));
  
  console.log('\n📊 检查报告已生成');
  console.log(`📁 报告文件: ${reportFile}`);
  console.log(`🎯 总体成功率: ${overallSuccessRate}%`);
  console.log(`✅ 是否达标: ${report.summary.meetsCriteria ? '是' : '否'} (标准: ${CONFIG.successCriteria.minSuccessRate}%)`);
  
  return report;
}

// 如果直接运行此脚本
if (require.main === module) {
  runOutlineChecks()
    .then(() => {
      console.log('🎉 轮廓调试检查完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 检查失败:', error);
      process.exit(1);
    });
}

module.exports = { runOutlineChecks, performOutlineCheck };
