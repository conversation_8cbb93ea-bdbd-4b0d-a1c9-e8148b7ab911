/**
 * 自动检查脚本 - 修改后强制执行
 * 根据血的教训制定的强制检查规则
 */

function runCompleteAutoCheck() {
  console.log('🎯 启动完整自动检查 - 强制执行模式');
  
  const testResults = {
    testDate: new Date().toISOString(),
    pageName: document.title || '未知页面',
    resolution: `${window.innerWidth}x${window.innerHeight}`,
    checks: {},
    criticalIssues: []
  };
  
  // 1. 🚨 Logo比例检查 (强制要求)
  const logo = document.querySelector('.brand-logo');
  const card = document.querySelector('.login-card');
  if (logo && card) {
    const logoRect = logo.getBoundingClientRect();
    const cardRect = card.getBoundingClientRect();
    const logoRatio = (logoRect.width / cardRect.width * 100);
    
    const logoCheck = {
      status: (logoRatio >= 8 && logoRatio <= 12) ? 'pass' : 'fail',
      logoSize: `${Math.round(logoRect.width)}x${Math.round(logoRect.height)}px`,
      cardSize: `${Math.round(cardRect.width)}x${Math.round(cardRect.height)}px`,
      logoRatio: `${logoRatio.toFixed(1)}%`,
      industryStandard: '8-12%',
      message: (logoRatio >= 8 && logoRatio <= 12) ? 
        '✅ Logo比例符合国际标准' : 
        `🚨 CRITICAL: Logo比例${logoRatio.toFixed(1)}%严重超标`
    };
    
    testResults.checks.logoProportionCheck = logoCheck;
    
    if (logoCheck.status === 'fail') {
      testResults.criticalIssues.push({
        type: 'LOGO_PROPORTION_VIOLATION',
        severity: 'CRITICAL',
        current: logoRatio.toFixed(1) + '%',
        required: '8-12%',
        action: 'MUST_FIX_IMMEDIATELY'
      });
    }
  }
  
  // 2. 🚨 容器边界检查 (零容忍)
  const containers = document.querySelectorAll('.login-card, .brand-section, .login-form');
  const containerIssues = [];
  
  containers.forEach(container => {
    const hasOverflow = container.scrollWidth > container.clientWidth || 
                       container.scrollHeight > container.clientHeight;
    if (hasOverflow) {
      containerIssues.push({
        element: container.className,
        scrollSize: { width: container.scrollWidth, height: container.scrollHeight },
        clientSize: { width: container.clientWidth, height: container.clientHeight },
        overflowType: container.scrollWidth > container.clientWidth ? 'horizontal' : 'vertical'
      });
    }
  });
  
  testResults.checks.containerBoundary = {
    status: containerIssues.length === 0 ? 'pass' : 'fail',
    issues: containerIssues,
    message: containerIssues.length === 0 ? '✅ 容器边界正常' : `🚨 CRITICAL: ${containerIssues.length}个容器溢出`
  };
  
  if (containerIssues.length > 0) {
    testResults.criticalIssues.push({
      type: 'CONTAINER_OVERFLOW',
      severity: 'CRITICAL',
      count: containerIssues.length,
      action: 'MUST_FIX_IMMEDIATELY'
    });
  }
  
  // 3. 自适应缩放检查
  const container = document.querySelector('.login-container');
  const computedStyle = getComputedStyle(container);
  const scaleFactor = computedStyle.getPropertyValue('--scale-factor');
  
  testResults.checks.adaptiveScaling = {
    status: scaleFactor ? 'pass' : 'fail',
    scaleFactor: scaleFactor,
    message: scaleFactor ? '✅ 自适应缩放系统正常' : '❌ 自适应缩放未实现'
  };
  
  // 4. 零重叠检测
  const elements = document.querySelectorAll('.brand-logo, .brand-title, .brand-subtitle, .zen-input, .zen-button');
  const overlaps = [];
  
  function isParentChild(elem1, elem2) {
    return elem1.contains(elem2) || elem2.contains(elem1);
  }
  
  function calculateOverlapArea(rect1, rect2) {
    const left = Math.max(rect1.left, rect2.left);
    const right = Math.min(rect1.right, rect2.right);
    const top = Math.max(rect1.top, rect2.top);
    const bottom = Math.min(rect1.bottom, rect2.bottom);
    return (left < right && top < bottom) ? (right - left) * (bottom - top) : 0;
  }
  
  for (let i = 0; i < elements.length; i++) {
    for (let j = i + 1; j < elements.length; j++) {
      if (!isParentChild(elements[i], elements[j])) {
        const rect1 = elements[i].getBoundingClientRect();
        const rect2 = elements[j].getBoundingClientRect();
        const overlapArea = calculateOverlapArea(rect1, rect2);
        
        if (overlapArea > 1) {
          overlaps.push({
            element1: elements[i].className,
            element2: elements[j].className,
            overlapArea: overlapArea
          });
        }
      }
    }
  }
  
  testResults.checks.zeroOverlap = {
    status: overlaps.length === 0 ? 'pass' : 'fail',
    overlaps: overlaps,
    message: overlaps.length === 0 ? '✅ 无元素重叠' : `❌ 发现${overlaps.length}个重叠`
  };
  
  // 5. 居中对齐检查
  const loginCard = document.querySelector('.login-card');
  const cardRect = loginCard ? loginCard.getBoundingClientRect() : null;
  const viewportCenter = window.innerWidth / 2;
  const cardCenter = cardRect ? (cardRect.left + cardRect.width / 2) : 0;
  const centerOffset = Math.abs(viewportCenter - cardCenter);
  
  testResults.checks.centerAlignment = {
    status: centerOffset <= 2 ? 'pass' : 'fail',
    offset: centerOffset,
    message: centerOffset <= 2 ? '✅ 居中对齐完美' : `❌ 偏离中心${centerOffset.toFixed(1)}px`
  };
  
  // 6. 功能完整性检查
  const usernameInput = document.querySelector('input[placeholder*="用户名"]');
  const passwordInput = document.querySelector('input[type="password"]');
  const loginButton = document.querySelector('.zen-button');
  
  testResults.checks.functionalIntegrity = {
    status: (usernameInput && passwordInput && loginButton) ? 'pass' : 'fail',
    elements: {
      username: !!usernameInput,
      password: !!passwordInput,
      button: !!loginButton
    },
    message: (usernameInput && passwordInput && loginButton) ? '✅ 所有功能元素正常' : '❌ 缺少功能元素'
  };
  
  // 计算总体通过率
  const totalChecks = Object.keys(testResults.checks).length;
  const passedChecks = Object.values(testResults.checks).filter(check => check.status === 'pass').length;
  const passRate = ((passedChecks / totalChecks) * 100).toFixed(1);
  
  testResults.summary = {
    totalChecks: totalChecks,
    passedChecks: passedChecks,
    failedChecks: totalChecks - passedChecks,
    passRate: `${passRate}%`,
    overallStatus: passRate >= 95 ? 'EXCELLENT' : 
                   passRate >= 85 ? 'GOOD' : 
                   passRate >= 70 ? 'ACCEPTABLE' : 'CRITICAL_FAILURE',
    criticalIssuesCount: testResults.criticalIssues.length
  };
  
  // 🚨 强制检查结果
  if (passRate < 85) {
    console.error('🚨 CRITICAL FAILURE: 通过率不足85%，必须立即修复！');
    console.error('📋 关键问题:', testResults.criticalIssues);
    throw new Error(`通过率${passRate}%不符合要求，必须≥85%`);
  }
  
  if (testResults.criticalIssues.length > 0) {
    console.error('🚨 发现关键问题，必须立即修复:', testResults.criticalIssues);
    throw new Error(`发现${testResults.criticalIssues.length}个关键问题，必须修复`);
  }
  
  console.log('📊 自动检查完成:', testResults);
  return testResults;
}

// 导出检查函数
if (typeof window !== 'undefined') {
  window.runCompleteAutoCheck = runCompleteAutoCheck;
  console.log('✅ 自动检查工具已注册到 window.runCompleteAutoCheck');
}

// 自动执行检查 (如果在浏览器环境中)
if (typeof window !== 'undefined' && document.readyState === 'complete') {
  setTimeout(() => {
    try {
      runCompleteAutoCheck();
      console.log('✅ 自动检查通过');
    } catch (error) {
      console.error('🚨 自动检查失败:', error.message);
    }
  }, 1000);
}
