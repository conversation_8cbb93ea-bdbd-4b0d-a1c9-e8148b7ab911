#!/usr/bin/env python3
"""
写代码前强制约束检查工具
确保AI在写代码前必须执行知识查询和示例参考查找
"""

import sys
import os
from datetime import datetime

class PreCodingChecker:
    def __init__(self):
        self.checks_passed = []
        self.checks_failed = []
        
    def check_context7_query(self):
        """检查是否执行了Context 7查询"""
        print("🔍 检查Context 7查询执行情况...")
        
        # 这里应该检查是否有Context 7查询的记录
        # 实际实现中可以检查日志或状态文件
        response = input("是否已使用Context 7查询相关代码? (y/N): ")
        
        if response.lower() == 'y':
            details = input("请简述查询到的关键信息: ")
            if details.strip():
                self.checks_passed.append("Context 7查询已执行")
                print(f"✅ Context 7查询已完成: {details}")
                return True
            else:
                self.checks_failed.append("Context 7查询信息不完整")
                print("❌ Context 7查询信息不完整")
                return False
        else:
            self.checks_failed.append("未执行Context 7查询")
            print("❌ 未执行Context 7查询 - 这是强制要求!")
            return False
    
    def check_example_reference(self):
        """检查是否查找了示例参考文件"""
        print("\n📚 检查示例参考文件查找情况...")
        
        response = input("是否已查找相似功能的示例参考文件? (y/N): ")
        
        if response.lower() == 'y':
            examples = input("请列出找到的示例文件路径: ")
            if examples.strip():
                self.checks_passed.append("示例参考文件已查找")
                print(f"✅ 示例参考已找到: {examples}")
                print("💡 提示: 示例参考可提升代码质量50%+")
                return True
            else:
                self.checks_failed.append("示例参考文件信息不完整")
                print("❌ 示例参考文件信息不完整")
                return False
        else:
            self.checks_failed.append("未查找示例参考文件")
            print("❌ 未查找示例参考文件 - 这会导致代码质量下降50%+!")
            return False
    
    def check_memory_server_query(self):
        """检查是否查询了历史经验"""
        print("\n🧠 检查memory-server历史经验查询...")
        
        response = input("是否已查询相关的历史经验和解决方案? (y/N): ")
        
        if response.lower() == 'y':
            experience = input("请简述查询到的关键经验: ")
            if experience.strip():
                self.checks_passed.append("历史经验查询已执行")
                print(f"✅ 历史经验查询已完成: {experience}")
                return True
            else:
                self.checks_failed.append("历史经验查询信息不完整")
                print("❌ 历史经验查询信息不完整")
                return False
        else:
            self.checks_failed.append("未查询历史经验")
            print("❌ 未查询历史经验 - 可能重复已知错误!")
            return False
    
    def check_sequential_thinking(self):
        """检查是否进行了问题分析"""
        print("\n🤔 检查Sequential thinking问题分析...")
        
        response = input("是否已使用Sequential thinking分析问题? (y/N): ")
        
        if response.lower() == 'y':
            analysis = input("请简述分析的关键要点: ")
            if analysis.strip():
                self.checks_passed.append("问题分析已完成")
                print(f"✅ 问题分析已完成: {analysis}")
                return True
            else:
                self.checks_failed.append("问题分析信息不完整")
                print("❌ 问题分析信息不完整")
                return False
        else:
            self.checks_failed.append("未进行问题分析")
            print("❌ 未进行问题分析 - 可能导致方案不完整!")
            return False
    
    def generate_report(self):
        """生成检查报告"""
        print("\n" + "="*60)
        print("📊 写代码前强制约束检查报告")
        print("="*60)
        
        print(f"\n✅ 通过的检查 ({len(self.checks_passed)}):")
        for check in self.checks_passed:
            print(f"   • {check}")
        
        print(f"\n❌ 失败的检查 ({len(self.checks_failed)}):")
        for check in self.checks_failed:
            print(f"   • {check}")
        
        total_checks = len(self.checks_passed) + len(self.checks_failed)
        pass_rate = (len(self.checks_passed) / total_checks * 100) if total_checks > 0 else 0
        
        print(f"\n📈 通过率: {pass_rate:.1f}% ({len(self.checks_passed)}/{total_checks})")
        
        if len(self.checks_failed) == 0:
            print("\n🎉 所有强制约束检查通过！可以开始写代码")
            print("💡 预期代码质量提升: 50%+")
            return True
        else:
            print(f"\n⚠️  有 {len(self.checks_failed)} 项强制约束未满足")
            print("🚨 禁止开始写代码，必须先完成所有强制检查")
            print("\n📋 需要完成的步骤:")
            for check in self.checks_failed:
                print(f"   • {check}")
            return False
    
    def run_full_check(self):
        """运行完整的强制约束检查"""
        print("🚨 写代码前强制约束检查")
        print("="*40)
        print("⚠️  必须严格指令AI在写代码前执行知识查询")
        print("📈 示例参考文件可提升代码质量50%+")
        print()
        
        # 执行所有检查
        checks = [
            self.check_context7_query,
            self.check_example_reference,
            self.check_memory_server_query,
            self.check_sequential_thinking
        ]
        
        all_passed = True
        for check in checks:
            if not check():
                all_passed = False
        
        # 生成报告
        return self.generate_report()

def main():
    """主函数"""
    if len(sys.argv) > 1 and sys.argv[1] == '--help':
        print("写代码前强制约束检查工具")
        print("用法: python3 pre_coding_check.py")
        print("目的: 确保AI在写代码前执行必要的知识查询")
        return
    
    checker = PreCodingChecker()
    
    print("⚡ 开始执行写代码前强制约束检查...")
    print(f"🕒 检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    if checker.run_full_check():
        print("\n✅ 检查通过，可以开始编码工作")
        sys.exit(0)
    else:
        print("\n❌ 检查未通过，禁止开始编码")
        print("💡 请完成所有强制约束后重新运行检查")
        sys.exit(1)

if __name__ == "__main__":
    main()
