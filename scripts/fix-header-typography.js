#!/usr/bin/env node

/**
 * 🎯 表头字体排版统一修复脚本
 * 
 * 功能：
 * - 统一所有管理页面表头的字体大小和排版
 * - 确保操作列使用标准的header-text类
 * - 统一排序按钮和新增按钮的字体大小
 * - 修复表头元素的对齐和间距问题
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-28
 */

const fs = require('fs');
const path = require('path');

// 🎯 需要修复的页面列表
const PAGES_TO_FIX = [
  'TherapistManagement.vue',
  'CustomerManagement.vue', 
  'AppointmentManagement.vue',
  'FinanceOverview.vue'
];

// 🎯 修复操作列的表头结构
function fixOperationHeaderStructure(content) {
  // 查找操作列的不规范结构
  const operationHeaderPattern = /<div class="header-cell" style="flex: 1\.5;">\s*<span>操作<\/span>/g;
  
  if (operationHeaderPattern.test(content)) {
    console.log('  ✅ 修复操作列表头结构');
    return content.replace(
      operationHeaderPattern,
      `<div class="header-cell" style="flex: 1.5;">
              <div class="header-normal-container">
                <span class="header-text">操作</span>
              </div>`
    );
  }
  
  return content;
}

// 🎯 统一排序按钮字体大小
function fixSortButtonFontSize(content) {
  // 查找排序指示器的字体大小定义
  const sortIndicatorPattern = /(\.sort-indicator\s*\{[^}]*font-size:\s*)0\.8rem/g;
  
  if (sortIndicatorPattern.test(content)) {
    console.log('  ✅ 统一排序按钮字体大小');
    return content.replace(sortIndicatorPattern, '$10.9rem  /* 🎯 统一字体大小与表头文字一致 */');
  }
  
  return content;
}

// 🎯 统一新增按钮字体大小
function fixAddButtonFontSize(content) {
  // 查找新增按钮的字体大小定义
  const addButtonPattern = /(\.header-add-btn-small\s*\{[^}]*font-size:\s*)0\.8rem/g;
  
  if (addButtonPattern.test(content)) {
    console.log('  ✅ 统一新增按钮字体大小');
    return content.replace(addButtonPattern, '$10.9rem  /* 🎯 统一字体大小与表头文字一致 */');
  }
  
  return content;
}

// 🎯 确保表头文字样式统一
function ensureHeaderTextConsistency(content) {
  // 检查是否存在header-text样式定义
  if (!content.includes('.header-text')) {
    console.log('  ⚠️  页面缺少header-text样式定义');
    return content;
  }
  
  // 确保header-text字体大小为0.9rem
  const headerTextPattern = /(\.header-text\s*\{[^}]*font-size:\s*)[\d.]+rem/g;
  
  if (headerTextPattern.test(content)) {
    const updatedContent = content.replace(headerTextPattern, '$10.9rem');
    if (updatedContent !== content) {
      console.log('  ✅ 统一表头文字字体大小');
      return updatedContent;
    }
  }
  
  return content;
}

// 🎯 修复表头对齐问题
function fixHeaderAlignment(content) {
  let fixedContent = content;
  let changeCount = 0;
  
  // 确保所有header-cell都有正确的对齐属性
  const headerCellPattern = /(\.header-cell\s*\{[^}]*)/g;
  const matches = fixedContent.match(headerCellPattern);
  
  if (matches) {
    matches.forEach(match => {
      if (!match.includes('align-items: center')) {
        const fixedMatch = match.replace(/\}$/, '  align-items: center;\n  justify-content: space-between;\n}');
        fixedContent = fixedContent.replace(match, fixedMatch);
        changeCount++;
      }
    });
    
    if (changeCount > 0) {
      console.log(`  ✅ 修复表头对齐属性 (${changeCount}处)`);
    }
  }
  
  return fixedContent;
}

// 🎯 修复单个页面
function fixPageTypography(pageName, content) {
  console.log(`🔄 修复页面: ${pageName}`);
  
  let fixedContent = content;
  let changeCount = 0;
  
  // 1. 修复操作列表头结构
  const beforeOperation = fixedContent;
  fixedContent = fixOperationHeaderStructure(fixedContent);
  if (fixedContent !== beforeOperation) changeCount++;
  
  // 2. 统一排序按钮字体大小
  const beforeSort = fixedContent;
  fixedContent = fixSortButtonFontSize(fixedContent);
  if (fixedContent !== beforeSort) changeCount++;
  
  // 3. 统一新增按钮字体大小
  const beforeAdd = fixedContent;
  fixedContent = fixAddButtonFontSize(fixedContent);
  if (fixedContent !== beforeAdd) changeCount++;
  
  // 4. 确保表头文字样式统一
  const beforeText = fixedContent;
  fixedContent = ensureHeaderTextConsistency(fixedContent);
  if (fixedContent !== beforeText) changeCount++;
  
  // 5. 修复表头对齐问题
  const beforeAlign = fixedContent;
  fixedContent = fixHeaderAlignment(fixedContent);
  if (fixedContent !== beforeAlign) changeCount++;
  
  console.log(`  📊 总计修复: ${changeCount} 处`);
  return fixedContent;
}

// 🎯 主函数
async function fixAllPagesTypography() {
  console.log('🚀 开始修复所有页面的表头字体排版...');
  console.log('📋 统一字体大小、对齐方式和排版结构');
  
  const viewsDir = path.join(__dirname, '../admin/src/views');
  let totalChanges = 0;
  
  for (const pageName of PAGES_TO_FIX) {
    const pagePath = path.join(viewsDir, pageName);
    
    if (!fs.existsSync(pagePath)) {
      console.log(`❌ 页面文件不存在: ${pageName}`);
      continue;
    }
    
    try {
      // 读取原文件
      const originalContent = fs.readFileSync(pagePath, 'utf8');
      
      // 修复页面
      const fixedContent = fixPageTypography(pageName, originalContent);
      
      // 检查是否有变更
      if (fixedContent !== originalContent) {
        // 备份原文件
        const backupPath = `${pagePath}_Typography_Backup_${Date.now()}.vue`;
        fs.writeFileSync(backupPath, originalContent, 'utf8');
        console.log(`  📋 原文件已备份: ${backupPath}`);
        
        // 写入修复后的文件
        fs.writeFileSync(pagePath, fixedContent, 'utf8');
        console.log(`  ✅ 页面修复完成: ${pageName}`);
        totalChanges++;
      } else {
        console.log(`  ℹ️  页面无需修复: ${pageName}`);
      }
      
    } catch (error) {
      console.error(`❌ 修复页面出错 ${pageName}:`, error.message);
    }
  }
  
  console.log('\n🎉 表头字体排版修复完成！');
  console.log(`📊 修复统计:`);
  console.log(`   - 检查页面数: ${PAGES_TO_FIX.length}`);
  console.log(`   - 修复页面数: ${totalChanges}`);
  console.log(`   - 统一字体大小: 0.9rem`);
  console.log(`   - 修复对齐方式: center`);
}

// 运行脚本
if (require.main === module) {
  fixAllPagesTypography();
}

module.exports = { fixAllPagesTypography };
