#!/bin/bash

# 🛑 壹心堂开发环境停止脚本
# 用途：停止所有开发服务
# 作者：AI助手
# 日期：2025-01-21

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
NC='\033[0m' # No Color

print_header() {
    echo -e "${PURPLE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
LOG_DIR="$PROJECT_ROOT/logs"

print_header "🛑 停止壹心堂开发环境"

# 停止服务函数
stop_service() {
    local service_name=$1
    local pid_file="$LOG_DIR/${service_name}.pid"
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if ps -p $pid > /dev/null 2>&1; then
            print_info "停止 $service_name 服务 (PID: $pid)"
            kill $pid
            sleep 2
            
            # 如果进程仍在运行，强制杀死
            if ps -p $pid > /dev/null 2>&1; then
                print_warning "强制停止 $service_name 服务"
                kill -9 $pid
            fi
            
            print_success "$service_name 服务已停止"
        else
            print_warning "$service_name 服务未运行"
        fi
        rm -f "$pid_file"
    else
        print_warning "未找到 $service_name 的PID文件"
    fi
}

# 停止前端服务
stop_service "frontend"

# 停止后端服务
stop_service "backend"

# 停止其他可能的Node.js进程
print_info "检查其他Node.js开发服务器..."
VITE_PIDS=$(pgrep -f "vite.*dev" 2>/dev/null || true)
if [ ! -z "$VITE_PIDS" ]; then
    print_info "停止Vite开发服务器..."
    echo $VITE_PIDS | xargs kill 2>/dev/null || true
    print_success "Vite服务器已停止"
fi

# 停止Django开发服务器
DJANGO_PIDS=$(pgrep -f "manage.py runserver" 2>/dev/null || true)
if [ ! -z "$DJANGO_PIDS" ]; then
    print_info "停止Django开发服务器..."
    echo $DJANGO_PIDS | xargs kill 2>/dev/null || true
    print_success "Django服务器已停止"
fi

# 停止Taro开发服务器
TARO_PIDS=$(pgrep -f "taro.*dev" 2>/dev/null || true)
if [ ! -z "$TARO_PIDS" ]; then
    print_info "停止Taro开发服务器..."
    echo $TARO_PIDS | xargs kill 2>/dev/null || true
    print_success "Taro服务器已停止"
fi

# 清理日志文件（可选）
read -p "是否清理日志文件? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    if [ -d "$LOG_DIR" ]; then
        rm -f "$LOG_DIR"/*.log
        rm -f "$LOG_DIR"/*.pid
        print_success "日志文件已清理"
    fi
fi

print_header "🎉 开发环境已停止"
print_info "如需重新启动，请运行: ./scripts/start-dev.sh"
