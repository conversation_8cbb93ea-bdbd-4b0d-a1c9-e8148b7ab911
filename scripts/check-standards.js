#!/usr/bin/env node

/**
 * 项目规范检查脚本
 * 用于CI/CD流水线中验证项目是否符合开发规范
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 开始项目规范检查...');

// 检查必要的文件是否存在
const requiredFiles = [
  'PROJECT_STANDARDS.md',
  'admin/FRONTEND_RULES.md',
  'server/requirements.txt',
  'admin/package.json',
  'client/package.json'
];

let hasErrors = false;

console.log('\n📋 检查必要文件...');
requiredFiles.forEach(file => {
  if (fs.existsSync(file)) {
    console.log(`✅ ${file}`);
  } else {
    console.log(`❌ ${file} - 文件不存在`);
    hasErrors = true;
  }
});

// 检查package.json中的脚本
console.log('\n📋 检查package.json脚本...');
const adminPackageJson = path.join('admin', 'package.json');
if (fs.existsSync(adminPackageJson)) {
  const adminPkg = JSON.parse(fs.readFileSync(adminPackageJson, 'utf8'));
  const requiredScripts = ['dev', 'build', 'lint'];
  
  requiredScripts.forEach(script => {
    if (adminPkg.scripts && adminPkg.scripts[script]) {
      console.log(`✅ admin package.json 包含 ${script} 脚本`);
    } else {
      console.log(`❌ admin package.json 缺少 ${script} 脚本`);
      hasErrors = true;
    }
  });
}

// 检查Django设置
console.log('\n📋 检查Django配置...');
const requirementsTxt = path.join('server', 'requirements.txt');
if (fs.existsSync(requirementsTxt)) {
  const requirements = fs.readFileSync(requirementsTxt, 'utf8');
  if (requirements.includes('Django==3.2.8')) {
    console.log('✅ Django版本正确 (3.2.8)');
  } else {
    console.log('❌ Django版本不正确，应该使用3.2.8');
    hasErrors = true;
  }
}

console.log('\n🎯 项目规范检查完成');

if (hasErrors) {
  console.log('❌ 发现规范问题，请修复后重试');
  process.exit(1);
} else {
  console.log('✅ 所有规范检查通过');
  process.exit(0);
}
