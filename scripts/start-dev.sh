#!/bin/bash

# 🚀 壹心堂单人全栈开发环境一键启动脚本
# 用途：同时启动前端、后端、数据库等所有开发服务
# 适用：单人全栈开发，一个命令启动整个开发环境
# 作者：AI助手
# 日期：2025-01-21

set -e  # 遇到错误立即退出

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
PURPLE='\033[0;35m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

# 打印带颜色的消息
print_header() {
    echo -e "${PURPLE}$1${NC}"
}

print_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

print_error() {
    echo -e "${RED}❌ $1${NC}"
}

print_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

print_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

print_step() {
    echo -e "${CYAN}🔄 $1${NC}"
}

# 主标题
clear
echo -e "${PURPLE}"
echo "╔══════════════════════════════════════════════════════════════╗"
echo "║                    🚀 壹心堂一键启动                          ║"
echo "║                  单人全栈开发环境启动器                        ║"
echo "║                                                              ║"
echo "║  前端(Vue3) + 后端(Django) + 小程序(Taro) + 数据库(MySQL)     ║"
echo "║                     v1.0 - 2025-01-21                       ║"
echo "╚══════════════════════════════════════════════════════════════╝"
echo -e "${NC}"

# 获取项目根目录
PROJECT_ROOT="$(cd "$(dirname "${BASH_SOURCE[0]}")/.." && pwd)"
cd "$PROJECT_ROOT"

print_info "项目根目录: $PROJECT_ROOT"
print_info "启动时间: $(date '+%Y-%m-%d %H:%M:%S')"

# 检查基础环境
print_header "🔍 检查开发环境"

# 检查Node.js
if ! command -v node &> /dev/null; then
    print_error "Node.js未安装，请先安装Node.js (https://nodejs.org/)"
    exit 1
fi
NODE_VERSION=$(node --version)
print_success "Node.js: $NODE_VERSION"

# 检查npm
if ! command -v npm &> /dev/null; then
    print_error "npm未安装"
    exit 1
fi
NPM_VERSION=$(npm --version)
print_success "npm: $NPM_VERSION"

# 检查Python
if ! command -v python3 &> /dev/null; then
    print_warning "Python3未安装，后端服务将无法启动"
    PYTHON_AVAILABLE=false
else
    PYTHON_VERSION=$(python3 --version)
    print_success "Python3: $PYTHON_VERSION"
    PYTHON_AVAILABLE=true
fi

# 检查MySQL
if command -v mysql &> /dev/null; then
    MYSQL_VERSION=$(mysql --version | cut -d' ' -f3 | cut -d',' -f1)
    print_success "MySQL: $MYSQL_VERSION"
    MYSQL_AVAILABLE=true
else
    print_warning "MySQL未检测到，数据库服务可能无法启动"
    MYSQL_AVAILABLE=false
fi

# 检查端口占用
print_header "🔌 检查端口占用"

check_port() {
    local port=$1
    local service=$2
    if lsof -i :$port &> /dev/null; then
        print_warning "$service端口 $port 已被占用"
        print_info "如需释放端口，请运行: lsof -ti:$port | xargs kill -9"
        return 1
    else
        print_success "$service端口 $port 可用"
        return 0
    fi
}

# 检查常用端口
FRONTEND_PORT=3000
BACKEND_PORT=8000
MYSQL_PORT=3306

check_port $FRONTEND_PORT "前端开发服务器"
check_port $BACKEND_PORT "Django后端服务器"
if [ "$MYSQL_AVAILABLE" = true ]; then
    check_port $MYSQL_PORT "MySQL数据库"
fi

# 创建日志目录
LOG_DIR="$PROJECT_ROOT/logs"
mkdir -p "$LOG_DIR"
print_success "日志目录: $LOG_DIR"

# 启动服务函数
start_frontend() {
    print_step "启动前端开发服务器 (Vue3 + Vite)"
    cd "$PROJECT_ROOT/admin"
    
    # 检查依赖
    if [ ! -d "node_modules" ]; then
        print_step "安装前端依赖..."
        npm install
    fi
    
    # 启动前端服务器
    print_info "前端服务器将在 http://localhost:3000 启动"
    npm run dev > "$LOG_DIR/frontend.log" 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > "$LOG_DIR/frontend.pid"
    print_success "前端服务器已启动 (PID: $FRONTEND_PID)"
}

start_backend() {
    if [ "$PYTHON_AVAILABLE" = false ]; then
        print_warning "Python3不可用，跳过后端服务器启动"
        return
    fi

    print_step "启动Django后端服务器"
    cd "$PROJECT_ROOT/server"
    
    # 检查虚拟环境
    if [ ! -d "venv" ]; then
        print_step "创建Python虚拟环境..."
        python3 -m venv venv
    fi
    
    # 激活虚拟环境
    source venv/bin/activate
    
    # 检查依赖
    if [ -f "requirements.txt" ]; then
        print_step "安装后端依赖..."
        pip install -r requirements.txt > "$LOG_DIR/backend-install.log" 2>&1
    fi
    
    # 数据库迁移
    if [ -f "manage.py" ]; then
        print_step "执行数据库迁移..."
        python manage.py migrate > "$LOG_DIR/backend-migrate.log" 2>&1
        
        # 启动Django服务器
        print_info "后端服务器将在 http://localhost:$BACKEND_PORT 启动"
        python manage.py runserver 0.0.0.0:$BACKEND_PORT > "$LOG_DIR/backend.log" 2>&1 &
        BACKEND_PID=$!
        echo $BACKEND_PID > "$LOG_DIR/backend.pid"
        print_success "后端服务器已启动 (PID: $BACKEND_PID)"
    else
        print_warning "未找到Django项目文件，跳过后端启动"
    fi
}

start_miniprogram() {
    print_step "准备小程序开发环境 (Taro)"
    cd "$PROJECT_ROOT"

    if [ -d "client" ]; then
        cd client
        
        # 检查依赖
        if [ ! -d "node_modules" ]; then
            print_step "安装小程序依赖..."
            npm install
        fi
        
        print_info "小程序开发环境已准备就绪"
        print_info "启动小程序开发: cd client && npm run dev:weapp"
    else
        print_warning "未找到小程序目录，跳过小程序环境准备"
    fi
}

# 启动MySQL (如果需要)
start_mysql() {
    if [ "$MYSQL_AVAILABLE" = false ]; then
        print_warning "MySQL不可用，跳过数据库启动"
        return
    fi
    
    # 检查MySQL是否已经运行
    if pgrep mysqld > /dev/null; then
        print_success "MySQL数据库已在运行"
    else
        print_step "启动MySQL数据库..."
        # 尝试启动MySQL (不同系统的启动方式)
        if command -v brew &> /dev/null; then
            # macOS with Homebrew
            brew services start mysql > "$LOG_DIR/mysql.log" 2>&1 &
        elif command -v systemctl &> /dev/null; then
            # Linux with systemd
            sudo systemctl start mysql > "$LOG_DIR/mysql.log" 2>&1 &
        else
            print_warning "无法自动启动MySQL，请手动启动"
        fi
    fi
}

# 主启动流程
print_header "🚀 启动开发服务"

# 启动各个服务
start_mysql
sleep 2

start_backend
sleep 3

start_frontend
sleep 2

start_miniprogram

# 等待服务启动
print_header "⏳ 等待服务启动完成"
sleep 5

# 检查服务状态
print_header "📊 服务状态检查"

check_service() {
    local url=$1
    local name=$2
    local max_attempts=10
    local attempt=1
    
    while [ $attempt -le $max_attempts ]; do
        if curl -s "$url" > /dev/null 2>&1; then
            print_success "$name 服务正常运行"
            return 0
        fi
        print_info "$name 启动中... ($attempt/$max_attempts)"
        sleep 2
        ((attempt++))
    done
    
    print_warning "$name 服务启动超时或失败"
    return 1
}

# 检查前端服务
check_service "http://localhost:3000" "前端服务器"

# 检查后端服务
if [ "$PYTHON_AVAILABLE" = true ]; then
    check_service "http://localhost:$BACKEND_PORT" "后端服务器"
fi

# 显示服务信息
print_header "🌐 服务访问地址"
echo -e "${CYAN}前端管理系统: ${YELLOW}http://localhost:3000${NC}"
if [ "$PYTHON_AVAILABLE" = true ]; then
    echo -e "${CYAN}后端API接口: ${YELLOW}http://localhost:$BACKEND_PORT${NC}"
    echo -e "${CYAN}Django管理后台: ${YELLOW}http://localhost:$BACKEND_PORT/admin${NC}"
fi

# 显示日志文件位置
print_header "📋 日志文件位置"
echo -e "${CYAN}前端日志: ${YELLOW}$LOG_DIR/frontend.log${NC}"
if [ "$PYTHON_AVAILABLE" = true ]; then
    echo -e "${CYAN}后端日志: ${YELLOW}$LOG_DIR/backend.log${NC}"
fi

# 显示停止命令
print_header "🛑 停止开发环境"
echo -e "${CYAN}停止所有服务: ${YELLOW}./scripts/stop-dev.sh${NC}"
echo -e "${CYAN}或手动停止: ${YELLOW}kill \$(cat $LOG_DIR/*.pid)${NC}"

# 显示开发工具
print_header "🛠️ 开发工具"
echo -e "${CYAN}CSS检查: ${YELLOW}npm run stylelint-check${NC}"
echo -e "${CYAN}CSS修复: ${YELLOW}npm run stylelint${NC}"
echo -e "${CYAN}轮廓调试: ${YELLOW}npm run outline-check${NC}"
echo -e "${CYAN}Git提交: ${YELLOW}git commit -m \"message\"${NC} (自动质量检查)"

print_header "🎉 开发环境启动完成！"
print_success "单人全栈开发环境已就绪"
print_info "按 Ctrl+C 可以停止此脚本，但服务会继续运行"
print_info "查看实时日志: tail -f $LOG_DIR/frontend.log"

# 保持脚本运行，显示实时状态
echo -e "\n${YELLOW}按 Ctrl+C 退出监控模式...${NC}"
trap 'echo -e "\n${YELLOW}退出监控模式，服务继续运行${NC}"; exit 0' INT

# 实时监控服务状态
while true; do
    sleep 30
    echo -e "${BLUE}$(date '+%H:%M:%S')${NC} - 服务运行中..."
done
