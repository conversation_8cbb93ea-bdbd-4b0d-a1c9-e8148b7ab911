#!/usr/bin/env node

/**
 * 🎯 文字对比度修复脚本
 * 
 * 功能：
 * - 修复所有管理页面在紫色背景下的文字可见性问题
 * - 将深色文字改为高对比度的白色文字
 * - 添加文字阴影增强可读性
 * - 确保所有文字在紫色背景下清晰可见
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-28
 */

const fs = require('fs');
const path = require('path');

// 🎯 需要修复的页面列表
const PAGES_TO_FIX = [
  'TherapistManagement.vue',
  'CustomerManagement.vue', 
  'AppointmentManagement.vue',
  'FinanceOverview.vue'
];

// 🎯 文字颜色映射表 - 深色到高对比度白色
const COLOR_MAPPINGS = [
  // 表头文字
  {
    pattern: /color:\s*var\(--text-primary\)/g,
    replacement: 'color: rgba(255, 255, 255, 0.95); text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3)'
  },
  
  // 深色文字 - 主要文字
  {
    pattern: /color:\s*#111827/g,
    replacement: 'color: rgba(255, 255, 255, 0.95); text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3)'
  },
  
  // 深灰色文字 - 次要文字
  {
    pattern: /color:\s*#374151/g,
    replacement: 'color: rgba(255, 255, 255, 0.7)'
  },
  
  // 中灰色文字 - 描述文字
  {
    pattern: /color:\s*#4b5563/g,
    replacement: 'color: rgba(255, 255, 255, 0.8)'
  },
  
  // 浅灰色文字 - 辅助文字
  {
    pattern: /color:\s*#6b7280/g,
    replacement: 'color: rgba(255, 255, 255, 0.6)'
  },
  
  // 绿色价格文字
  {
    pattern: /color:\s*#047857/g,
    replacement: 'color: rgba(34, 197, 94, 0.9); text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3)'
  },
  
  // 紫色分类文字
  {
    pattern: /color:\s*#6366f1/g,
    replacement: 'color: rgba(168, 85, 247, 0.9)'
  },
  
  // 深蓝色文字
  {
    pattern: /color:\s*#1e40af/g,
    replacement: 'color: rgba(59, 130, 246, 0.9)'
  },
  
  // 深红色文字
  {
    pattern: /color:\s*#b91c1c/g,
    replacement: 'color: rgba(239, 68, 68, 0.9)'
  }
];

// 🎯 修复文字对比度
function fixTextContrast(content) {
  let fixedContent = content;
  let changeCount = 0;
  
  COLOR_MAPPINGS.forEach(mapping => {
    const beforeCount = (fixedContent.match(mapping.pattern) || []).length;
    fixedContent = fixedContent.replace(mapping.pattern, mapping.replacement);
    const afterCount = (fixedContent.match(mapping.pattern) || []).length;
    
    if (beforeCount > afterCount) {
      changeCount += (beforeCount - afterCount);
    }
  });
  
  return { content: fixedContent, changeCount };
}

// 🎯 添加搜索输入框占位符样式
function addPlaceholderStyles(content) {
  // 检查是否已经有占位符样式
  if (content.includes('::placeholder')) {
    return { content, changeCount: 0 };
  }
  
  // 查找搜索输入框样式的位置
  const searchInputPattern = /(\.header-search-input\s*\{[^}]+\})/;
  const match = content.match(searchInputPattern);
  
  if (match) {
    const placeholderStyle = `
/* 🎯 搜索输入框占位符样式 */
.header-search-input::placeholder {
  color: rgba(255, 255, 255, 0.6);  /* 🎯 占位符使用半透明白色 */
}`;
    
    const updatedContent = content.replace(searchInputPattern, match[1] + placeholderStyle);
    return { content: updatedContent, changeCount: 1 };
  }
  
  return { content, changeCount: 0 };
}

// 🎯 修复单个页面
function fixPageTextContrast(pageName, content) {
  console.log(`🔄 修复页面: ${pageName}`);
  
  let fixedContent = content;
  let totalChanges = 0;
  
  // 1. 修复文字对比度
  const contrastResult = fixTextContrast(fixedContent);
  fixedContent = contrastResult.content;
  totalChanges += contrastResult.changeCount;
  
  if (contrastResult.changeCount > 0) {
    console.log(`  ✅ 修复文字对比度 (${contrastResult.changeCount}处)`);
  }
  
  // 2. 添加占位符样式
  const placeholderResult = addPlaceholderStyles(fixedContent);
  fixedContent = placeholderResult.content;
  totalChanges += placeholderResult.changeCount;
  
  if (placeholderResult.changeCount > 0) {
    console.log(`  ✅ 添加占位符样式`);
  }
  
  console.log(`  📊 总计修复: ${totalChanges} 处`);
  return fixedContent;
}

// 🎯 主函数
async function fixAllPagesTextContrast() {
  console.log('🚀 开始修复所有页面的文字对比度...');
  console.log('📋 将深色文字改为高对比度白色，确保在紫色背景下清晰可见');
  
  const viewsDir = path.join(__dirname, '../admin/src/views');
  let totalChanges = 0;
  
  for (const pageName of PAGES_TO_FIX) {
    const pagePath = path.join(viewsDir, pageName);
    
    if (!fs.existsSync(pagePath)) {
      console.log(`❌ 页面文件不存在: ${pageName}`);
      continue;
    }
    
    try {
      // 读取原文件
      const originalContent = fs.readFileSync(pagePath, 'utf8');
      
      // 修复页面
      const fixedContent = fixPageTextContrast(pageName, originalContent);
      
      // 检查是否有变更
      if (fixedContent !== originalContent) {
        // 备份原文件
        const backupPath = `${pagePath}_TextContrast_Backup_${Date.now()}.vue`;
        fs.writeFileSync(backupPath, originalContent, 'utf8');
        console.log(`  📋 原文件已备份: ${backupPath}`);
        
        // 写入修复后的文件
        fs.writeFileSync(pagePath, fixedContent, 'utf8');
        console.log(`  ✅ 页面修复完成: ${pageName}`);
        totalChanges++;
      } else {
        console.log(`  ℹ️  页面无需修复: ${pageName}`);
      }
      
    } catch (error) {
      console.error(`❌ 修复页面出错 ${pageName}:`, error.message);
    }
  }
  
  console.log('\n🎉 文字对比度修复完成！');
  console.log(`📊 修复统计:`);
  console.log(`   - 检查页面数: ${PAGES_TO_FIX.length}`);
  console.log(`   - 修复页面数: ${totalChanges}`);
  console.log(`   - 文字颜色: 深色 → 高对比度白色`);
  console.log(`   - 可读性: 显著提升`);
}

// 运行脚本
if (require.main === module) {
  fixAllPagesTextContrast();
}

module.exports = { fixAllPagesTextContrast };
