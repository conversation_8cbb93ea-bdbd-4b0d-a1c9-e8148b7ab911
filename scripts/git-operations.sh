#!/bin/bash
# 🚀 壹心堂管理系统 - Git操作脚本
# 符合 docs/standards/DEVELOPMENT_STANDARDS.md 中的Git工作流规范

# 颜色定义
GREEN='\033[38;2;82;196;26m'
YELLOW='\033[38;2;250;173;20m'
RED='\033[38;2;245;34;45m'
BLUE='\033[38;2;57;179;219m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 壹心堂管理系统 Git操作脚本${NC}"
echo -e "${BLUE}==============================${NC}"
echo ""

# 检查当前目录是否为git仓库
if [ ! -d ".git" ]; then
    echo -e "${RED}❌ 当前目录不是git仓库${NC}"
    exit 1
fi

echo -e "${YELLOW}🔍 正在获取远程仓库最新代码...${NC}"
echo ""

# 执行git pull
if git pull origin main; then
    echo -e "${GREEN}✅ 成功获取远程仓库最新代码${NC}"
else
    echo -e "${RED}❌ 获取远程代码失败${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}🔍 检查本地更改...${NC}"
echo ""

# 检查是否有更改
if git diff --quiet && git diff --cached --quiet; then
    echo -e "${GREEN}✅ 没有需要提交的更改${NC}"
    exit 0
else
    echo -e "${YELLOW}📁 发现本地更改，准备提交...${NC}"
fi

echo ""
echo -e "${YELLOW}➕ 添加所有更改到暂存区...${NC}"
echo ""

# 添加所有更改
if git add .; then
    echo -e "${GREEN}✅ 成功添加所有更改到暂存区${NC}"
else
    echo -e "${RED}❌ 添加更改失败${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}📝 创建提交...${NC}"
echo ""

# 创建提交
COMMIT_MESSAGE="✨ 更新代码

技术改进:
- 根据项目规范整理代码
- 确保符合目录结构规范要求
- 保持代码风格一致性"

if echo "$COMMIT_MESSAGE" | git commit -F -; then
    echo -e "${GREEN}✅ 成功创建提交${NC}"
else
    echo -e "${RED}❌ 创建提交失败${NC}"
    exit 1
fi

echo ""
echo -e "${YELLOW}📤 推送到远程仓库...${NC}"
echo ""

# 推送到远程仓库
if git push origin main; then
    echo -e "${GREEN}✅ 成功推送到远程仓库${NC}"
    echo ""
    echo -e "${GREEN}🎉 Git操作完成！${NC}"
else
    echo -e "${RED}❌ 推送失败${NC}"
    exit 1
fi