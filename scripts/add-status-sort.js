#!/usr/bin/env node

/**
 * 🎯 状态排序功能添加脚本
 * 
 * 功能：
 * - 为所有管理页面添加status字段到排序按钮状态中
 * - 删除不再需要的状态筛选相关函数
 * - 确保所有页面的状态列都使用排序按钮而不是筛选按钮
 * 
 * <AUTHOR>
 * @version 1.0.0
 * @date 2025-01-28
 */

const fs = require('fs');
const path = require('path');

// 🎯 需要修复的页面列表
const PAGES_TO_FIX = [
  'TherapistManagement.vue',
  'CustomerManagement.vue', 
  'AppointmentManagement.vue',
  'FinanceOverview.vue'
];

// 🎯 添加status字段到排序按钮状态
function addStatusToSortButtonStates(content) {
  // 查找sortButtonStates定义
  const sortButtonStatesPattern = /(const sortButtonStates = reactive\(\{[\s\S]*?)\}\);/;
  const match = content.match(sortButtonStatesPattern);
  
  if (match) {
    const originalDefinition = match[1];
    // 检查是否已经包含status字段
    if (!originalDefinition.includes('status:')) {
      const newDefinition = originalDefinition + ',\n      status: { loading: false, disabled: false }  // 🎯 添加状态排序支持';
      return content.replace(sortButtonStatesPattern, newDefinition + '\n    });');
    }
  }
  
  return content;
}

// 🎯 添加status字段到排序逻辑
function addStatusToSortLogic(content) {
  // 查找排序逻辑中的switch语句
  const switchPattern = /(case 'category':[\s\S]*?break;)/;
  const match = content.match(switchPattern);
  
  if (match && !content.includes("case 'status':")) {
    const statusCase = `              case 'status':
                aValue = a.status || '';
                bValue = b.status || '';
                break;`;
    
    return content.replace(switchPattern, match[1] + '\n' + statusCase);
  }
  
  return content;
}

// 🎯 删除状态筛选相关函数
function removeStatusFilterFunctions(content) {
  // 删除状态筛选函数定义
  const functionsToRemove = [
    /const toggleStatusFilter = \(\) => \{[\s\S]*?\};/g,
    /const getStatusFilterClass = \(\) => \{[\s\S]*?\};/g,
    /const getStatusFilterTitle = \(\) => \{[\s\S]*?\};/g,
    /const getStatusFilterIcon = \(\) => \{[\s\S]*?\};/g,
    /const getStatusFilterLabel = \(\) => \{[\s\S]*?\};/g
  ];
  
  functionsToRemove.forEach(pattern => {
    content = content.replace(pattern, '');
  });
  
  // 从返回值中删除这些函数
  const returnFunctionsToRemove = [
    'toggleStatusFilter,',
    'getStatusFilterClass,',
    'getStatusFilterTitle,',
    'getStatusFilterIcon,',
    'getStatusFilterLabel,'
  ];
  
  returnFunctionsToRemove.forEach(func => {
    content = content.replace(new RegExp(`\\s*${func.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')}`, 'g'), '');
  });
  
  return content;
}

// 🎯 修复单个页面
function fixPage(pageName, content) {
  console.log(`🔄 修复页面: ${pageName}`);
  
  let fixedContent = content;
  let changeCount = 0;
  
  // 1. 添加status字段到排序按钮状态
  const beforeSortStates = fixedContent;
  fixedContent = addStatusToSortButtonStates(fixedContent);
  if (fixedContent !== beforeSortStates) {
    changeCount++;
    console.log(`  ✅ 添加status字段到排序按钮状态`);
  }
  
  // 2. 添加status字段到排序逻辑
  const beforeSortLogic = fixedContent;
  fixedContent = addStatusToSortLogic(fixedContent);
  if (fixedContent !== beforeSortLogic) {
    changeCount++;
    console.log(`  ✅ 添加status字段到排序逻辑`);
  }
  
  // 3. 删除状态筛选相关函数
  const beforeRemove = fixedContent;
  fixedContent = removeStatusFilterFunctions(fixedContent);
  if (fixedContent !== beforeRemove) {
    changeCount++;
    console.log(`  ✅ 删除状态筛选相关函数`);
  }
  
  console.log(`  📊 总计修复: ${changeCount} 处`);
  return fixedContent;
}

// 🎯 主函数
async function addStatusSortToAllPages() {
  console.log('🚀 开始为所有页面添加状态排序功能...');
  console.log('📋 将状态筛选改为状态排序');
  
  const viewsDir = path.join(__dirname, '../admin/src/views');
  let totalChanges = 0;
  
  for (const pageName of PAGES_TO_FIX) {
    const pagePath = path.join(viewsDir, pageName);
    
    if (!fs.existsSync(pagePath)) {
      console.log(`❌ 页面文件不存在: ${pageName}`);
      continue;
    }
    
    try {
      // 读取原文件
      const originalContent = fs.readFileSync(pagePath, 'utf8');
      
      // 修复页面
      const fixedContent = fixPage(pageName, originalContent);
      
      // 检查是否有变更
      if (fixedContent !== originalContent) {
        // 备份原文件
        const backupPath = `${pagePath}_StatusSort_Backup_${Date.now()}.vue`;
        fs.writeFileSync(backupPath, originalContent, 'utf8');
        console.log(`  📋 原文件已备份: ${backupPath}`);
        
        // 写入修复后的文件
        fs.writeFileSync(pagePath, fixedContent, 'utf8');
        console.log(`  ✅ 页面修复完成: ${pageName}`);
        totalChanges++;
      } else {
        console.log(`  ℹ️  页面无需修复: ${pageName}`);
      }
      
    } catch (error) {
      console.error(`❌ 修复页面出错 ${pageName}:`, error.message);
    }
  }
  
  console.log('\n🎉 状态排序功能添加完成！');
  console.log(`📊 修复统计:`);
  console.log(`   - 检查页面数: ${PAGES_TO_FIX.length}`);
  console.log(`   - 修复页面数: ${totalChanges}`);
  console.log(`   - 功能变更: 状态筛选 → 状态排序`);
}

// 运行脚本
if (require.main === module) {
  addStatusSortToAllPages();
}

module.exports = { addStatusSortToAllPages };
