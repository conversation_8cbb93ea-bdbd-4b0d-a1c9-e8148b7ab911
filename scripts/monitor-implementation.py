#!/usr/bin/env python3
"""
壹心堂项目实施状态监控脚本
监控MCP配置应用、工作流程执行、质量标准达成情况
"""

import json
import os
import sys
import datetime
from pathlib import Path

class ImplementationMonitor:
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.config_dir = self.project_root / "config"
        self.report_dir = self.project_root / "reports"
        self.report_dir.mkdir(exist_ok=True)
        
    def check_mcp_configuration(self):
        """检查MCP配置应用状态"""
        print("🔍 检查MCP配置应用状态...")
        
        results = {
            "status": "success",
            "details": {},
            "issues": []
        }
        
        # 检查主配置文件
        main_config = self.config_dir / "mcp-config.json"
        stable_config = self.config_dir / "mcp-config-stable.json"
        
        if not main_config.exists():
            results["issues"].append("主配置文件 mcp-config.json 不存在")
            results["status"] = "error"
        
        if not stable_config.exists():
            results["issues"].append("稳定配置文件 mcp-config-stable.json 不存在")
            results["status"] = "error"
        
        # 比较配置文件
        if main_config.exists() and stable_config.exists():
            try:
                with open(main_config) as f:
                    main_data = json.load(f)
                with open(stable_config) as f:
                    stable_data = json.load(f)
                
                # 检查是否应用了稳定配置
                main_servers = set(main_data.get("mcpServers", {}).keys())
                stable_servers = set(stable_data.get("mcpServers", {}).keys())
                
                if main_servers == stable_servers:
                    results["details"]["config_applied"] = True
                    results["details"]["server_count"] = len(main_servers)
                    print(f"✅ 稳定配置已应用，包含 {len(main_servers)} 个MCP服务器")
                else:
                    results["issues"].append("主配置与稳定配置不一致")
                    results["status"] = "warning"
                    print(f"⚠️ 配置不一致: 主配置{len(main_servers)}个服务器，稳定配置{len(stable_servers)}个服务器")
                    
            except Exception as e:
                results["issues"].append(f"配置文件解析错误: {e}")
                results["status"] = "error"
        
        # 检查必要目录
        required_dirs = ["tasks", "feedback", "charts", "user-memories"]
        missing_dirs = []
        
        for dir_name in required_dirs:
            dir_path = self.project_root / dir_name
            if not dir_path.exists():
                missing_dirs.append(dir_name)
        
        if missing_dirs:
            results["issues"].append(f"缺少必要目录: {', '.join(missing_dirs)}")
            results["status"] = "warning"
        else:
            results["details"]["directories_created"] = True
            print("✅ 所有必要目录已创建")
        
        return results
    
    def check_workflow_configuration(self):
        """检查工作流程配置状态"""
        print("🔍 检查工作流程配置状态...")
        
        results = {
            "status": "success",
            "details": {},
            "issues": []
        }
        
        # 检查工作流程配置文件
        workflow_files = [
            "workflow-checklist.json",
            "stable-workflow-config.json"
        ]
        
        for file_name in workflow_files:
            file_path = self.config_dir / file_name
            if not file_path.exists():
                results["issues"].append(f"工作流程配置文件 {file_name} 不存在")
                results["status"] = "warning"
            else:
                try:
                    with open(file_path) as f:
                        data = json.load(f)
                    results["details"][file_name] = "已配置"
                    print(f"✅ {file_name} 配置正常")
                except Exception as e:
                    results["issues"].append(f"{file_name} 解析错误: {e}")
                    results["status"] = "error"
        
        return results
    
    def check_quality_monitoring(self):
        """检查质量监控配置状态"""
        print("🔍 检查质量监控配置状态...")
        
        results = {
            "status": "success",
            "details": {},
            "issues": []
        }
        
        # 检查质量监控配置文件
        quality_files = [
            "quality-monitoring.json",
            "continuous-improvement.json"
        ]
        
        for file_name in quality_files:
            file_path = self.config_dir / file_name
            if not file_path.exists():
                results["issues"].append(f"质量监控配置文件 {file_name} 不存在")
                results["status"] = "warning"
            else:
                try:
                    with open(file_path) as f:
                        data = json.load(f)
                    results["details"][file_name] = "已配置"
                    print(f"✅ {file_name} 配置正常")
                except Exception as e:
                    results["issues"].append(f"{file_name} 解析错误: {e}")
                    results["status"] = "error"
        
        return results
    
    def check_documentation_status(self):
        """检查文档状态"""
        print("🔍 检查文档状态...")
        
        results = {
            "status": "success",
            "details": {},
            "issues": []
        }
        
        # 检查关键文档
        key_documents = [
            "docs/FINAL_USER_GUIDELINES.md",
            "docs/STABLE_DEVELOPMENT_STANDARDS.md"
        ]
        
        for doc_path in key_documents:
            full_path = self.project_root / doc_path
            if not full_path.exists():
                results["issues"].append(f"关键文档 {doc_path} 不存在")
                results["status"] = "warning"
            else:
                # 检查文档大小（简单的完整性检查）
                size = full_path.stat().st_size
                if size < 1000:  # 小于1KB可能不完整
                    results["issues"].append(f"文档 {doc_path} 可能不完整（大小: {size} bytes）")
                    results["status"] = "warning"
                else:
                    results["details"][doc_path] = f"正常 ({size} bytes)"
                    print(f"✅ {doc_path} 文档正常")
        
        return results
    
    def generate_implementation_report(self):
        """生成实施状态报告"""
        print("📊 生成实施状态报告...")
        
        # 执行所有检查
        mcp_results = self.check_mcp_configuration()
        workflow_results = self.check_workflow_configuration()
        quality_results = self.check_quality_monitoring()
        docs_results = self.check_documentation_status()
        
        # 汇总结果
        overall_status = "success"
        total_issues = 0
        
        all_results = [mcp_results, workflow_results, quality_results, docs_results]
        for result in all_results:
            if result["status"] == "error":
                overall_status = "error"
            elif result["status"] == "warning" and overall_status != "error":
                overall_status = "warning"
            total_issues += len(result["issues"])
        
        # 生成报告
        report = {
            "report_metadata": {
                "generated_at": datetime.datetime.now().isoformat(),
                "overall_status": overall_status,
                "total_issues": total_issues
            },
            "implementation_status": {
                "mcp_configuration": mcp_results,
                "workflow_configuration": workflow_results,
                "quality_monitoring": quality_results,
                "documentation": docs_results
            },
            "summary": {
                "mcp_servers_configured": mcp_results["details"].get("server_count", 0),
                "directories_created": mcp_results["details"].get("directories_created", False),
                "workflow_files_configured": len([k for k in workflow_results["details"] if k.endswith(".json")]),
                "quality_files_configured": len([k for k in quality_results["details"] if k.endswith(".json")]),
                "key_documents_available": len([k for k in docs_results["details"] if k.endswith(".md")])
            },
            "recommendations": self.generate_recommendations(all_results)
        }
        
        # 保存报告
        report_file = self.report_dir / f"implementation-status-{datetime.datetime.now().strftime('%Y%m%d-%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 报告已保存到: {report_file}")
        
        # 打印摘要
        self.print_summary(report)
        
        return report
    
    def generate_recommendations(self, results):
        """生成改进建议"""
        recommendations = []
        
        for result in results:
            for issue in result["issues"]:
                if "不存在" in issue:
                    recommendations.append(f"创建缺失的文件或目录: {issue}")
                elif "不一致" in issue:
                    recommendations.append(f"同步配置文件: {issue}")
                elif "解析错误" in issue:
                    recommendations.append(f"修复配置文件格式: {issue}")
                else:
                    recommendations.append(f"解决问题: {issue}")
        
        if not recommendations:
            recommendations.append("所有配置正常，建议定期监控和维护")
        
        return recommendations
    
    def print_summary(self, report):
        """打印报告摘要"""
        print("\n" + "="*60)
        print("📊 实施状态报告摘要")
        print("="*60)
        
        status = report["report_metadata"]["overall_status"]
        status_icon = "✅" if status == "success" else "⚠️" if status == "warning" else "❌"
        print(f"总体状态: {status_icon} {status.upper()}")
        print(f"发现问题: {report['report_metadata']['total_issues']} 个")
        
        print("\n📈 配置状态:")
        summary = report["summary"]
        print(f"  MCP服务器: {summary['mcp_servers_configured']} 个已配置")
        print(f"  目录结构: {'✅ 已创建' if summary['directories_created'] else '❌ 未创建'}")
        print(f"  工作流程配置: {summary['workflow_files_configured']} 个文件")
        print(f"  质量监控配置: {summary['quality_files_configured']} 个文件")
        print(f"  关键文档: {summary['key_documents_available']} 个可用")
        
        if report["recommendations"]:
            print("\n💡 改进建议:")
            for i, rec in enumerate(report["recommendations"], 1):
                print(f"  {i}. {rec}")
        
        print("="*60)

def main():
    """主函数"""
    print("🚀 壹心堂项目实施状态监控")
    print("基于9个稳定MCP服务器的开发规范体系")
    print("-" * 60)
    
    monitor = ImplementationMonitor()
    report = monitor.generate_implementation_report()
    
    # 返回适当的退出码
    status = report["report_metadata"]["overall_status"]
    if status == "error":
        sys.exit(1)
    elif status == "warning":
        sys.exit(2)
    else:
        sys.exit(0)

if __name__ == "__main__":
    main()
