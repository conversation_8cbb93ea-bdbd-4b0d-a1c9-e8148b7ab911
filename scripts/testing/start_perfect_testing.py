#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完美测试计划启动脚本
基于PERFECT_TESTING_PLAN.md的自动化测试启动器
"""

import os
import sys
import time
import webbrowser
from datetime import datetime

def print_banner():
    """打印测试计划横幅"""
    banner = """
🎯 =============================================== 🎯
    完美测试计划 - 追求极致的界面测试方案
🎯 =============================================== 🎯

📋 测试原则:
   • 零重叠: 任何元素重叠都不允许，哪怕1px
   • 零覆盖: 元素不能覆盖其他功能区域
   • 零错位: 元素位置必须精确对齐，误差≤1px
   • 自适应缩放: 缩放比例0.8x-1.2x，基于1366px基准

🖥️ 测试分辨率:
   • 1024x768 (缩放0.8x) - 边界测试
   • 1366x768 (基准1.0x) - 标准测试
   • 1920x1080 (标准1.0x) - 桌面测试
   • 2560x1440 (缩放1.2x) - 2K显示器测试
   • 3840x2160 (缩放1.2x) - 4K显示器测试

📊 测试覆盖:
   • 14个页面 × 5个分辨率 × 21个检查项 = 1,470个测试用例
   • 预计测试时间: 11.7小时 (建议分3天完成)
   • 完美度要求: ≥99.5%

🎯 =============================================== 🎯
"""
    print(banner)

def show_test_menu():
    """显示测试菜单"""
    menu = """
📋 请选择测试模式:

1. 🏆 完整测试计划 - 按照PERFECT_TESTING_PLAN.md执行完整测试
2. 🎯 单页面测试 - 选择特定页面进行详细测试 (集成轮廓调试)
3. 📊 查看测试报告 - 查看历史测试结果
4. 📖 查看测试文档 - 打开完美测试计划文档
0. 退出

请输入选项 (0-4): """
    
    return input(menu).strip()

def start_full_testing():
    """启动完整测试计划"""
    print("\n🎯 启动完整测试计划...")
    print("📋 测试流程:")
    print("   1. 服务管理页面 (/services) - 🏆 模板页面")
    print("   2. 技师管理页面 (/technicians)")
    print("   3. 客户管理页面 (/customers)")
    print("   4. 预约管理页面 (/appointments)")
    print("   5. 财务管理页面 (/finance)")
    print("   6. 健康贴士页面 (/health-tips)")
    print("   7. 登录页面 (/login)")
    print("   8. 仪表板页面 (/dashboard)")
    print("\n⏱️ 预计完成时间: 11.7小时")
    print("💡 建议: 分3天完成，每天4小时")
    
    confirm = input("\n是否开始完整测试? (y/N): ").strip().lower()
    if confirm == 'y':
        print("🚀 正在启动开发服务器...")
        os.system("python one_click_start.py")
    else:
        print("❌ 已取消完整测试")



def start_single_page_testing():
    """启动单页面测试 (集成轮廓调试)"""
    pages = {
        '1': ('服务管理', '/services'),
        '2': ('技师管理', '/technicians'),
        '3': ('客户管理', '/customers'),
        '4': ('预约管理', '/appointments'),
        '5': ('财务管理', '/finance'),
        '6': ('健康贴士', '/health-tips'),
        '7': ('登录页面', '/login'),
        '8': ('仪表板', '/dashboard')
    }

    print("\n🎯 单页面测试模式 (集成轮廓调试)")
    print("📋 可选页面:")
    for key, (name, path) in pages.items():
        print(f"   {key}. {name} ({path})")

    choice = input("\n请选择页面 (1-8): ").strip()
    if choice in pages:
        name, path = pages[choice]
        print(f"\n🎯 开始测试: {name} ({path})")
        print("📋 测试步骤:")
        print("   1. 🛠️ 自动启用轮廓调试工具")
        print("   2. 📐 逐个分辨率测试 (5个分辨率)")
        print("   3. 🔍 执行强制检查项 (零重叠、缩放等)")
        print("   4. 📝 记录测试结果")

        print("\n🛠️ 集成调试功能:")
        print("   • 自动轮廓调试 - 可视化元素边界")
        print("   • 重叠检测 - 自动发现重叠问题")
        print("   • 缩放验证 - 检查自适应缩放效果")
        print("   • 边界检查 - 确保元素不超出容器")

        print("\n⌨️ 调试快捷键:")
        print("   • Ctrl+Shift+D - 启用/禁用全局调试")
        print("   • Ctrl+Shift+C - 清除调试样式")
        print("   • Ctrl+Shift+L - 调试布局")
        print("   • Ctrl+Shift+S - 调试滚动条")

        confirm = input(f"\n是否开始测试 {name}? (y/N): ").strip().lower()
        if confirm == 'y':
            print("🚀 正在启动开发服务器...")
            print("🛠️ 轮廓调试工具将自动加载...")
            os.system("python one_click_start.py")
        else:
            print("❌ 已取消单页面测试")
    else:
        print("❌ 无效选择")



def show_test_reports():
    """显示测试报告"""
    print("\n📊 测试报告")
    print("📁 报告位置: docs/reports/testing/")
    
    # 检查是否有测试报告
    reports_dir = "docs/reports/testing"
    if os.path.exists(reports_dir):
        reports = [f for f in os.listdir(reports_dir) if f.endswith('.md')]
        if reports:
            print("📋 可用报告:")
            for i, report in enumerate(reports, 1):
                print(f"   {i}. {report}")
        else:
            print("📋 暂无测试报告")
    else:
        print("📋 暂无测试报告目录")
    
    input("\n按回车键返回主菜单...")

def open_test_documentation():
    """打开测试文档"""
    print("\n📖 打开测试文档...")
    
    # 检查文档是否存在
    doc_path = "docs/PERFECT_TESTING_PLAN.md"
    if os.path.exists(doc_path):
        try:
            # 尝试用默认编辑器打开
            if sys.platform.startswith('darwin'):  # macOS
                os.system(f"open '{doc_path}'")
            elif sys.platform.startswith('linux'):  # Linux
                os.system(f"xdg-open '{doc_path}'")
            elif sys.platform.startswith('win'):  # Windows
                os.system(f"start '{doc_path}'")
            else:
                print(f"📄 请手动打开文档: {doc_path}")
            
            print("✅ 已打开完美测试计划文档")
        except Exception as e:
            print(f"❌ 打开文档失败: {e}")
            print(f"📄 请手动打开文档: {doc_path}")
    else:
        print("❌ 测试文档不存在")
    
    input("\n按回车键返回主菜单...")

def main():
    """主函数"""
    print_banner()
    
    while True:
        choice = show_test_menu()
        
        if choice == '1':
            start_full_testing()
        elif choice == '2':
            start_single_page_testing()
        elif choice == '3':
            show_test_reports()
        elif choice == '4':
            open_test_documentation()
        elif choice == '0':
            print("\n👋 感谢使用完美测试计划！")
            print("🎯 追求完美，永不妥协！")
            break
        else:
            print("\n❌ 无效选择，请重新输入")
        
        print()  # 空行分隔

if __name__ == "__main__":
    main()
