#!/usr/bin/env python3
"""
文件整理工具 - 安全的逐步整理方案
遵循"不进行批量操作"的约束，每次只处理一个文件
"""

import os
import shutil
import json
from pathlib import Path

class SafeFileOrganizer:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root).resolve()
        self.violations = []
        
    def analyze_violations(self):
        """分析违规文件，但不执行任何操作"""
        print("🔍 分析文件结构违规情况...")
        
        # 根目录违规文件分析
        root_files = list(self.project_root.glob("*"))
        
        # 允许的根目录文件（根据PROJECT_STRUCTURE_STANDARDS.md）
        allowed_root_files = {
            "README.md", "LICENSE", "PROJECT_STRUCTURE_STANDARDS.md",
            "project_check.py", "start-all-dev.sh", "stop-all-dev.sh",
            "start.py", "start.bat", "start-simple.sh", "package-lock.json",
            "qodana.yaml", ".gitignore", ".dockerignore", ".nvmrc",
            ".python-version", ".prettierrc", ".eslintrc.js", ".cursorrules",
            ".env.server.example"
        }

        # 临时文件（应该清理）
        temp_files = {".backend.pid", ".frontend.pid", ".miniprogram.pid", ".DS_Store"}
        
        # 允许的根目录目录
        allowed_root_dirs = {
            "admin", "client", "server", "docs", "scripts", 
            "logs", "test-reports", "config", "venv"
        }
        
        for item in root_files:
            if item.is_file():
                if item.name in temp_files:
                    self.violations.append({
                        'type': 'temp_file',
                        'current_path': str(item),
                        'suggested_path': 'DELETE',
                        'reason': f'临时文件应删除: {item.name}'
                    })
                elif item.name not in allowed_root_files:
                    self.violations.append({
                        'type': 'file',
                        'current_path': str(item),
                        'suggested_path': self._suggest_new_path(item),
                        'reason': f'根目录不允许文件: {item.name}'
                    })
            elif item.is_dir():
                if item.name not in allowed_root_dirs and not item.name.startswith('.'):
                    self.violations.append({
                        'type': 'directory',
                        'current_path': str(item),
                        'suggested_path': self._suggest_new_path(item),
                        'reason': f'根目录不允许目录: {item.name}'
                    })
    
    def _suggest_new_path(self, file_path):
        """根据文件类型建议新路径"""
        name = file_path.name.lower()
        
        # 配置文件
        if name.endswith('.json') and ('config' in name or 'mcp' in name):
            return f"config/{file_path.name}"
        
        # 文档文件
        if name.endswith('.md'):
            if 'summary' in name or 'report' in name:
                return f"docs/reports/{file_path.name}"
            elif 'guide' in name or 'checklist' in name:
                return f"docs/guides/{file_path.name}"
            elif 'standard' in name or 'constraint' in name:
                return f"docs/standards/{file_path.name}"
            else:
                return f"docs/archive/{file_path.name}"
        
        # 脚本文件
        if name.endswith(('.py', '.js', '.sh')) and not name.startswith('start'):
            if 'test' in name:
                return f"scripts/testing/{file_path.name}"
            elif 'check' in name or 'verify' in name:
                return f"scripts/automation/{file_path.name}"
            else:
                return f"scripts/{file_path.name}"
        
        return f"archive/{file_path.name}"
    
    def show_analysis_report(self):
        """显示分析报告"""
        if not self.violations:
            print("✅ 文件结构符合规范，无需整理")
            return
        
        print(f"\n📊 发现 {len(self.violations)} 个违规项目:")
        print("=" * 60)
        
        for i, violation in enumerate(self.violations, 1):
            print(f"\n{i}. {violation['reason']}")
            print(f"   当前位置: {violation['current_path']}")
            print(f"   建议位置: {violation['suggested_path']}")
        
        print("\n" + "=" * 60)
        print("⚠️  注意: 这只是分析报告，未执行任何文件操作")
        print("💡 建议: 逐个检查每个文件，确认后手动移动")
    
    def move_single_file(self, current_path, new_path, confirm=True):
        """安全地移动单个文件"""
        current = Path(current_path)
        new = Path(new_path)
        
        if not current.exists():
            print(f"❌ 文件不存在: {current_path}")
            return False
        
        if confirm:
            response = input(f"确认移动 {current.name} 到 {new_path}? (y/N): ")
            if response.lower() != 'y':
                print("❌ 操作已取消")
                return False
        
        # 创建目标目录
        new.parent.mkdir(parents=True, exist_ok=True)
        
        try:
            shutil.move(str(current), str(new))
            print(f"✅ 已移动: {current.name} -> {new_path}")
            return True
        except Exception as e:
            print(f"❌ 移动失败: {e}")
            return False
    
    def create_standard_directories(self):
        """创建标准目录结构"""
        standard_dirs = [
            "config",
            "scripts/automation",
            "scripts/testing", 
            "scripts/deployment",
            "docs/standards",
            "docs/guides",
            "docs/reports",
            "docs/archive"
        ]
        
        for dir_path in standard_dirs:
            full_path = self.project_root / dir_path
            if not full_path.exists():
                full_path.mkdir(parents=True, exist_ok=True)
                print(f"✅ 创建目录: {dir_path}")

def main():
    organizer = SafeFileOrganizer()
    
    print("🗂️  文件结构安全整理工具")
    print("=" * 40)
    
    # 创建标准目录
    organizer.create_standard_directories()
    
    # 分析违规情况
    organizer.analyze_violations()
    
    # 显示分析报告
    organizer.show_analysis_report()
    
    if organizer.violations:
        print("\n💡 使用建议:")
        print("1. 逐个检查每个违规文件")
        print("2. 确认文件用途和依赖关系")
        print("3. 手动移动到建议位置")
        print("4. 测试项目功能是否正常")
        
        print("\n🔧 手动移动示例:")
        for violation in organizer.violations[:3]:  # 只显示前3个
            print(f"mv '{violation['current_path']}' '{violation['suggested_path']}'")

if __name__ == "__main__":
    main()
