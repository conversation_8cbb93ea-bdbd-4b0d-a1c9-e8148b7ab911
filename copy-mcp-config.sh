#!/bin/bash

# 🔧 MCP配置快速复制脚本
# 用途: 将MCP配置复制到剪贴板，方便在AI助手中粘贴
# 作者: AI助手
# 日期: 2025-01-25

echo "🚀 MCP配置快速复制工具"
echo "================================"

# 设置项目根目录
PROJECT_ROOT="/Users/<USER>/Documents/wechatcloud"
cd "$PROJECT_ROOT" || exit 1

# 检查配置文件是否存在
if [ ! -f "mcp-settings.json" ]; then
    echo "❌ 错误: mcp-settings.json 文件不存在"
    echo "请先运行 ./setup-mcp.sh 创建配置文件"
    exit 1
fi

echo "📋 可用的复制选项:"
echo "1. 完整MCP配置 (推荐)"
echo "2. 核心服务器配置 (最小化)"
echo "3. 显示配置内容 (不复制)"
echo "4. 验证配置语法"
echo ""
read -p "请选择选项 (1-4): " choice

case $choice in
    1)
        echo "📋 复制完整MCP配置到剪贴板..."
        if command -v pbcopy &> /dev/null; then
            cat mcp-settings.json | pbcopy
            echo "✅ 完整配置已复制到剪贴板！"
            echo ""
            echo "🎯 下一步操作:"
            echo "1. 打开Claude Desktop设置"
            echo "2. 找到MCP服务器配置"
            echo "3. 粘贴配置 (Cmd+V)"
            echo "4. 保存并重启Claude Desktop"
        else
            echo "❌ 错误: pbcopy命令不可用"
            echo "请手动复制 mcp-settings.json 文件内容"
        fi
        ;;
    2)
        echo "📋 复制核心服务器配置到剪贴板..."
        if command -v pbcopy &> /dev/null; then
            # 创建最小化配置
            cat << 'EOF' | pbcopy
{
  "mcpServers": {
    "context7": {
      "command": "npx",
      "args": ["-y", "@upstash/context7-mcp@latest"],
      "env": {
        "CONTEXT_DEPTH": "deep",
        "SEARCH_SCOPE": "project"
      }
    },
    "memory-server": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-memory"],
      "env": {
        "MEMORY_STORAGE": "./user-memories",
        "ENABLE_GRAPH": "true"
      }
    },
    "sequential-thinking": {
      "command": "npx",
      "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"],
      "env": {
        "THINKING_DEPTH": "5",
        "ENABLE_REFLECTION": "true"
      }
    }
  }
}
EOF
            echo "✅ 核心配置已复制到剪贴板！"
            echo "📝 包含: context7, memory-server, sequential-thinking"
        else
            echo "❌ 错误: pbcopy命令不可用"
        fi
        ;;
    3)
        echo "📄 显示配置内容:"
        echo "================================"
        cat mcp-settings.json
        echo "================================"
        echo "💡 提示: 手动复制上述内容到AI助手配置中"
        ;;
    4)
        echo "🔍 验证配置语法..."
        if command -v jq &> /dev/null; then
            if jq . mcp-settings.json > /dev/null 2>&1; then
                echo "✅ 配置语法正确"
                echo "📊 配置统计:"
                echo "- 服务器数量: $(jq '.mcpServers | length' mcp-settings.json)"
                echo "- 服务器列表: $(jq -r '.mcpServers | keys | join(", ")' mcp-settings.json)"
            else
                echo "❌ 配置语法错误"
                echo "请检查 mcp-settings.json 文件"
            fi
        else
            echo "⚠️  警告: jq命令不可用，无法验证JSON语法"
            echo "请手动检查配置文件语法"
        fi
        ;;
    *)
        echo "❌ 无效选项，请选择 1-4"
        exit 1
        ;;
esac

echo ""
echo "📚 相关文档:"
echo "- 完整配置指南: MCP_完整配置指南.md"
echo "- 使用说明: MCP_配置使用指南.md"
echo "- 开发规范: docs/COMPREHENSIVE_DEVELOPMENT_STANDARDS.md"

echo ""
echo "🔧 故障排除:"
echo "如果遇到问题，请检查:"
echo "1. Node.js和npm是否正常安装"
echo "2. 网络连接是否正常"
echo "3. AI助手是否支持MCP"
echo "4. 配置文件JSON语法是否正确"

echo ""
echo "✅ 操作完成！"