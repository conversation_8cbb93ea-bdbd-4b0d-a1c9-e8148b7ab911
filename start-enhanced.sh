#!/bin/bash
# 🚀 壹心堂管理系统 - 增强版一键启动脚本 v2.0
# 支持端口冲突检测、进程管理、热更新的开发环境启动
#
# 使用方法：
#   ./start-enhanced.sh              # 启动所有服务（前端+后端+小程序）
#   ./start-enhanced.sh backend      # 仅启动后端
#   ./start-enhanced.sh frontend     # 仅启动前端
#   ./start-enhanced.sh miniprogram  # 仅启动小程序
#   ./start-enhanced.sh check        # 仅检查端口冲突
#
# 新增特性：
#   - 智能端口冲突检测和处理
#   - 进程归属识别（本程序 vs 其他程序）
#   - 自动清理冲突进程
#   - 热更新支持（前后端无需重启）
#   - 完整的进程监控和管理

# 🎨 壹心堂菜单按钮风格颜色定义
PURPLE_PRIMARY='\033[38;2;139;92;246m'      # 主紫色 #8b5cf6
PURPLE_LIGHT='\033[38;2;168;85;247m'        # 浅紫色 #a855f7
PURPLE_DARK='\033[38;2;124;58;237m'         # 深紫色 #7c3aed
WHITE_BOLD='\033[1;97m'                     # 粗体白色
WHITE='\033[97m'                            # 白色
GREEN='\033[38;2;82;196;26m'                # 成功绿色
YELLOW='\033[38;2;250;173;20m'              # 警告黄色
RED='\033[38;2;245;34;45m'                  # 错误红色
CYAN='\033[38;2;19;194;194m'                # 信息青色
BORDER_PURPLE='\033[38;2;139;92;246m'       # 紫色边框色
BOLD='\033[1m'                              # 粗体
NC='\033[0m'                                # 重置颜色

# 🎯 项目配置
BACKEND_PORT=8000
FRONTEND_PORT=3000
MINIPROGRAM_PORT=3002
PROJECT_NAME="yixintang"
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 🎯 进程标识文件
PID_DIR="$SCRIPT_DIR/.pids"
BACKEND_PID_FILE="$PID_DIR/backend.pid"
FRONTEND_PID_FILE="$PID_DIR/frontend.pid"
MINIPROGRAM_PID_FILE="$PID_DIR/miniprogram.pid"

# 创建PID目录
mkdir -p "$PID_DIR"

# 🎨 启动标题
show_header() {
    echo ""
    echo -e "${BORDER_PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🚀 壹心堂管理系统 - 增强版一键启动 v2.0${NC}                                   ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}  ${PURPLE_PRIMARY}智能端口检测 + 进程管理 + 热更新支持${NC}                                  ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}  ${WHITE}基于9个稳定MCP服务器的开发规范体系${NC}                                      ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
}

# 🔍 检查端口是否被占用
check_port() {
    local port=$1
    lsof -ti:$port 2>/dev/null
}

# 🔍 获取进程信息
get_process_info() {
    local pid=$1
    if [ -n "$pid" ]; then
        ps -p $pid -o pid,ppid,command --no-headers 2>/dev/null
    fi
}

# 🔍 检查进程是否属于本项目
is_our_process() {
    local pid=$1
    local process_info=$(get_process_info $pid)
    
    if [ -z "$process_info" ]; then
        return 1
    fi
    
    # 检查进程命令是否包含项目相关关键词
    if echo "$process_info" | grep -q -E "(django|runserver|npm.*dev|vue|vite|$PROJECT_NAME)"; then
        return 0
    fi
    
    # 检查是否是我们启动的进程（通过PID文件）
    if [ -f "$BACKEND_PID_FILE" ] && [ "$(cat $BACKEND_PID_FILE 2>/dev/null)" = "$pid" ]; then
        return 0
    fi
    if [ -f "$FRONTEND_PID_FILE" ] && [ "$(cat $FRONTEND_PID_FILE 2>/dev/null)" = "$pid" ]; then
        return 0
    fi
    if [ -f "$MINIPROGRAM_PID_FILE" ] && [ "$(cat $MINIPROGRAM_PID_FILE 2>/dev/null)" = "$pid" ]; then
        return 0
    fi
    
    return 1
}

# 🛡️ 处理端口冲突
handle_port_conflict() {
    local port=$1
    local service_name=$2
    local pids=$(check_port $port)
    
    if [ -z "$pids" ]; then
        echo -e "${GREEN}✅ 端口 $port ($service_name) 可用${NC}"
        return 0
    fi
    
    echo -e "${YELLOW}⚠️  端口 $port ($service_name) 被占用${NC}"
    
    for pid in $pids; do
        local process_info=$(get_process_info $pid)
        echo -e "${CYAN}   进程 $pid: $process_info${NC}"
        
        if is_our_process $pid; then
            echo -e "${GREEN}   ✅ 这是本项目进程，保持运行${NC}"
        else
            echo -e "${RED}   ❌ 这是其他程序进程，准备清理${NC}"
            kill -9 $pid 2>/dev/null || true
            sleep 1
            if ! kill -0 $pid 2>/dev/null; then
                echo -e "${GREEN}   ✅ 进程 $pid 已清理${NC}"
            else
                echo -e "${RED}   ❌ 进程 $pid 清理失败${NC}"
                return 1
            fi
        fi
    done
    
    # 再次检查端口
    local remaining_pids=$(check_port $port)
    if [ -z "$remaining_pids" ]; then
        echo -e "${GREEN}✅ 端口 $port ($service_name) 现在可用${NC}"
        return 0
    else
        echo -e "${RED}❌ 端口 $port ($service_name) 仍被占用${NC}"
        return 1
    fi
}

# 🔍 全面端口检查
check_all_ports() {
    echo -e "${BORDER_PURPLE}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BORDER_PURPLE}│${NC} ${WHITE_BOLD}🔍 检查端口冲突${NC}                                                           ${BORDER_PURPLE}│${NC}"
    echo -e "${BORDER_PURPLE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
    
    local all_clear=true
    
    if ! handle_port_conflict $BACKEND_PORT "Django后端"; then
        all_clear=false
    fi
    
    if ! handle_port_conflict $FRONTEND_PORT "Vue前端"; then
        all_clear=false
    fi
    
    if ! handle_port_conflict $MINIPROGRAM_PORT "小程序开发"; then
        all_clear=false
    fi
    
    if [ "$all_clear" = true ]; then
        echo -e "${GREEN}🎉 所有端口检查通过${NC}"
        return 0
    else
        echo -e "${RED}❌ 部分端口仍有冲突${NC}"
        return 1
    fi
}

# 🚀 启动后端服务
start_backend() {
    echo -e "${BORDER_PURPLE}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BORDER_PURPLE}│${NC} ${WHITE_BOLD}🔄 启动Django后端服务 (热更新模式)${NC}                                        ${BORDER_PURPLE}│${NC}"
    echo -e "${BORDER_PURPLE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
    
    # 检查是否已经运行
    if [ -f "$BACKEND_PID_FILE" ]; then
        local existing_pid=$(cat "$BACKEND_PID_FILE")
        if kill -0 $existing_pid 2>/dev/null; then
            echo -e "${GREEN}✅ Django后端已在运行 (PID: $existing_pid) - 支持热更新，无需重启${NC}"
            return 0
        else
            rm -f "$BACKEND_PID_FILE"
        fi
    fi
    
    cd server
    
    # 检查虚拟环境
    if [ ! -d "venv" ]; then
        echo -e "${CYAN}📦 创建Python虚拟环境...${NC}"
        python3 -m venv venv
    fi
    
    source venv/bin/activate
    
    # 安装依赖
    if [ ! -f ".deps_installed" ] || [ "requirements.txt" -nt ".deps_installed" ]; then
        echo -e "${CYAN}📦 安装/更新Python依赖...${NC}"
        pip install -r requirements.txt
        touch .deps_installed
    fi
    
    # 数据库迁移
    echo -e "${CYAN}📊 执行数据库迁移...${NC}"
    python manage.py migrate
    
    # 启动服务器
    echo -e "${PURPLE_PRIMARY}🌐 启动Django服务器 (热更新模式)...${NC}"
    python manage.py runserver 0.0.0.0:$BACKEND_PORT &
    local django_pid=$!
    echo $django_pid > "$BACKEND_PID_FILE"
    
    cd ..
    
    # 等待启动
    echo -e "${CYAN}⏳ 等待后端服务启动...${NC}"
    sleep 5
    
    # 健康检查
    if curl -f http://localhost:$BACKEND_PORT/health/ > /dev/null 2>&1; then
        echo -e "${GREEN}✅ Django后端启动成功 (PID: $django_pid) - 支持代码热更新${NC}"
        return 0
    else
        echo -e "${RED}❌ Django后端启动失败${NC}"
        kill $django_pid 2>/dev/null || true
        rm -f "$BACKEND_PID_FILE"
        return 1
    fi
}

# 🚀 启动前端服务
start_frontend() {
    echo -e "${BORDER_PURPLE}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BORDER_PURPLE}│${NC} ${WHITE_BOLD}🔄 启动Vue前端服务 (热更新模式)${NC}                                           ${BORDER_PURPLE}│${NC}"
    echo -e "${BORDER_PURPLE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
    
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装${NC}"
        return 1
    fi
    
    # 检查是否已经运行
    if [ -f "$FRONTEND_PID_FILE" ]; then
        local existing_pid=$(cat "$FRONTEND_PID_FILE")
        if kill -0 $existing_pid 2>/dev/null; then
            echo -e "${GREEN}✅ Vue前端已在运行 (PID: $existing_pid) - 支持热更新，无需重启${NC}"
            return 0
        else
            rm -f "$FRONTEND_PID_FILE"
        fi
    fi
    
    cd admin
    
    # 安装依赖
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        echo -e "${CYAN}📦 安装/更新前端依赖...${NC}"
        npm install
    fi
    
    # 启动开发服务器
    echo -e "${PURPLE_PRIMARY}🎨 启动Vue开发服务器 (热更新模式)...${NC}"
    npm run dev &
    local frontend_pid=$!
    echo $frontend_pid > "$FRONTEND_PID_FILE"
    
    cd ..
    
    echo -e "${GREEN}✅ Vue前端启动成功 (PID: $frontend_pid) - 支持代码热更新${NC}"
    return 0
}

# 🚀 启动小程序服务
start_miniprogram() {
    echo -e "${BORDER_PURPLE}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BORDER_PURPLE}│${NC} ${WHITE_BOLD}🔄 启动小程序开发服务 (热更新模式)${NC}                                        ${BORDER_PURPLE}│${NC}"
    echo -e "${BORDER_PURPLE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
    
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装${NC}"
        return 1
    fi
    
    if [ ! -d "client" ]; then
        echo -e "${YELLOW}⚠️  小程序目录不存在，跳过启动${NC}"
        return 0
    fi
    
    # 检查是否已经运行
    if [ -f "$MINIPROGRAM_PID_FILE" ]; then
        local existing_pid=$(cat "$MINIPROGRAM_PID_FILE")
        if kill -0 $existing_pid 2>/dev/null; then
            echo -e "${GREEN}✅ 小程序开发服务已在运行 (PID: $existing_pid) - 支持热更新，无需重启${NC}"
            return 0
        else
            rm -f "$MINIPROGRAM_PID_FILE"
        fi
    fi
    
    cd client
    
    # 安装依赖
    if [ ! -d "node_modules" ] || [ "package.json" -nt "node_modules" ]; then
        echo -e "${CYAN}📦 安装/更新小程序依赖...${NC}"
        npm install
    fi
    
    # 启动开发服务器
    echo -e "${PURPLE_PRIMARY}📱 启动小程序开发服务器 (热更新模式)...${NC}"
    npm run dev -- --port $MINIPROGRAM_PORT &
    local miniprogram_pid=$!
    echo $miniprogram_pid > "$MINIPROGRAM_PID_FILE"
    
    cd ..
    
    echo -e "${GREEN}✅ 小程序开发服务启动成功 (PID: $miniprogram_pid) - 支持代码热更新${NC}"
    return 0
}

# 🛑 停止服务
stop_service() {
    local service_name=$1
    local pid_file=$2
    
    if [ -f "$pid_file" ]; then
        local pid=$(cat "$pid_file")
        if kill -0 $pid 2>/dev/null; then
            kill $pid 2>/dev/null || true
            sleep 2
            if kill -0 $pid 2>/dev/null; then
                kill -9 $pid 2>/dev/null || true
            fi
            echo -e "${CYAN}🛑 $service_name 已停止 (PID: $pid)${NC}"
        fi
        rm -f "$pid_file"
    fi
}

# 🧹 清理函数
cleanup() {
    echo ""
    echo -e "${BORDER_PURPLE}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BORDER_PURPLE}│${NC} ${WHITE_BOLD}🔄 正在停止所有服务...${NC}                                                   ${BORDER_PURPLE}│${NC}"
    echo -e "${BORDER_PURPLE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
    
    stop_service "Django后端" "$BACKEND_PID_FILE"
    stop_service "Vue前端" "$FRONTEND_PID_FILE"
    stop_service "小程序开发服务" "$MINIPROGRAM_PID_FILE"
    
    # 强制清理端口
    lsof -ti:$BACKEND_PORT | xargs kill -9 2>/dev/null || true
    lsof -ti:$FRONTEND_PORT | xargs kill -9 2>/dev/null || true
    lsof -ti:$MINIPROGRAM_PORT | xargs kill -9 2>/dev/null || true
    
    echo -e "${GREEN}✅ 所有服务已停止 - 壹心堂管理系统已关闭${NC}"
    exit 0
}

# 📊 显示服务状态
show_status() {
    echo ""
    echo -e "${BORDER_PURPLE}╔═══════════════════════════════════════════════════════════════════════════════╗${NC}"
    echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🎉 壹心堂管理系统启动完成！${NC}                                                ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}  ${PURPLE_PRIMARY}智能端口检测 + 进程管理 + 热更新支持${NC}                                  ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}╠═══════════════════════════════════════════════════════════════════════════════╣${NC}"
    echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}📊 服务地址${NC}                                                               ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}    ${GREEN}🌐 后端API: http://localhost:$BACKEND_PORT/${NC}                                    ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}    ${GREEN}🎨 前端管理: http://localhost:$FRONTEND_PORT/${NC}                                  ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}    ${GREEN}📱 小程序开发: http://localhost:$MINIPROGRAM_PORT/${NC}                             ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🔥 热更新支持 (无需重启)${NC}                                                  ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ Django后端: 代码修改自动重载${NC}                                       ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ Vue前端: 实时热更新 (HMR)${NC}                                         ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ 小程序: 开发工具同步更新${NC}                                          ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🔐 开发者账号${NC}                                                             ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}    ${CYAN}👤 用户名: ROOT${NC}                                                              ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}║${NC}    ${CYAN}🔑 密码: 13210583333${NC}                                                        ${BORDER_PURPLE}║${NC}"
    echo -e "${BORDER_PURPLE}╚═══════════════════════════════════════════════════════════════════════════════╝${NC}"
    echo ""
    echo -e "${YELLOW}⚠️  按 Ctrl+C 停止服务${NC}"
    echo -e "${CYAN}💡 提示: 代码修改会自动热更新，无需重启服务${NC}"
}

# 设置退出清理
trap cleanup EXIT INT TERM

# 主程序
main() {
    show_header
    
    # 解析参数
    local mode=${1:-"all"}
    
    case $mode in
        "check")
            check_all_ports
            exit $?
            ;;
        "backend")
            check_all_ports || exit 1
            start_backend || exit 1
            ;;
        "frontend")
            check_all_ports || exit 1
            start_frontend || exit 1
            ;;
        "miniprogram")
            check_all_ports || exit 1
            start_miniprogram || exit 1
            ;;
        "all"|*)
            check_all_ports || exit 1
            start_backend || exit 1
            start_frontend || exit 1
            start_miniprogram || exit 1
            ;;
    esac
    
    show_status
    
    # 保持运行
    while true; do
        sleep 1
    done
}

# 运行主程序
main "$@"
