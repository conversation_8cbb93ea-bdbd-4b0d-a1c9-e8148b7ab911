# 🚀 壹心堂管理系统 - 启动脚本使用指南

> **📋 文档目的**: 说明项目启动脚本的使用方法和选择建议  
> **🔄 更新日期**: 2025-01-21  
> **🎯 适用范围**: 基于9个稳定MCP服务器的开发规范体系

## 📦 **可用启动脚本**

### **🎯 推荐使用：start.sh - 一键启动（简化版）**

**适用场景**：
- ✅ 日常开发和调试
- ✅ 快速启动和测试
- ✅ 新手开发者使用
- ✅ CI/CD自动化部署

**功能特性**：
- 🚀 快速启动Django后端 (端口8000)
- 🎨 自动启动Vue前端 (端口3000，如果Node.js可用)
- 🔧 集成MCP开发规范体系检查
- 📊 自动创建虚拟环境和安装依赖
- 🔍 数据库迁移和健康检查
- 💡 清晰的状态显示和错误提示

**使用方法**：
```bash
# 一键启动
./start.sh

# 或者
bash start.sh
```

**启动后访问**：
- 🌐 后端API: http://localhost:8000/
- 🔧 健康检查: http://localhost:8000/health/
- 🎨 前端管理: http://localhost:3000/ (如果Node.js可用)

---

### **🔧 高级使用：start-all-dev.sh - 完整开发环境**

**适用场景**：
- ✅ 完整功能开发
- ✅ 小程序开发调试
- ✅ 热重载开发模式
- ✅ 生产环境模拟

**功能特性**：
- 🚀 Django后端 + Vue前端 + Taro小程序
- 🔥 智能热重载（代码修改自动生效）
- 🔍 智能端口检测和清理
- 📊 服务状态监控和自动重启
- ⚡ 完整的错误处理和重试机制
- 🛠️ 开发环境状态检查

**使用方法**：
```bash
# 启动完整开发环境
./start-all-dev.sh
```

**启动后访问**：
- 🌐 Django后端: http://localhost:8000/
- 🎨 Vue前端: http://localhost:3000/
- 📱 Taro小程序: client/dist/ (在微信开发者工具中打开)

---

### **🛑 停止服务：stop-all-dev.sh**

**功能**：
- 停止所有开发服务
- 清理端口占用
- 清理临时文件
- 验证清理结果

**使用方法**：
```bash
# 停止所有服务
./stop-all-dev.sh
```

## 🎯 **选择建议**

### **🥇 推荐选择：start.sh**
```bash
# 适合90%的开发场景
./start.sh
```

**优势**：
- ⚡ 启动速度快（约30秒）
- 🎯 功能精简，稳定可靠
- 🔧 集成MCP开发规范体系
- 💡 用户界面友好
- 🛠️ 维护成本低

### **🔧 特殊需求：start-all-dev.sh**
```bash
# 需要完整功能时使用
./start-all-dev.sh
```

**适用情况**：
- 需要开发小程序功能
- 需要热重载开发模式
- 需要完整的服务监控
- 需要生产环境模拟

## 🛠️ **MCP开发规范体系集成**

### **自动检查项目**
两个启动脚本都会自动检查：
- ✅ 9个稳定MCP服务器配置状态
- ✅ 开发规范体系实施状态
- ✅ 质量监控机制运行状态
- ✅ 工作流程配置完整性

### **显示规范信息**
启动后会显示：
- 🛠️ MCP服务器状态
- 📋 开发规范要求
- 🎨 壹心堂项目特色
- 🔐 开发者账号信息

## 🎨 **壹心堂项目特色**

### **UI设计标准**
- 🎭 **设计风格**: 毕加索艺术风格
- 🟣 **主色调**: 紫色系 (#8B5CF6, #A78BFA)
- 📐 **布局原则**: 黄金比例 (1.618)
- 🔄 **缩放范围**: 0.8x-1.2x自适应
- ✨ **特色效果**: 七色阴影、右侧圆角

### **技术栈约束**
- **前端**: Vue 3 + Vite + Ant Design Vue
- **后端**: Django + MySQL + Python 3.9+
- **小程序**: Taro + WeUI (强制要求)
- **测试**: Playwright自动化测试
- **部署**: 微信云托管

## 🔧 **环境要求**

### **必需环境**
- ✅ **Python 3.9+** (必须)
- ✅ **pip** (Python包管理器)
- ✅ **虚拟环境支持** (venv)

### **可选环境**
- 🔧 **Node.js 16+** (前端开发)
- 🔧 **npm** (前端包管理器)
- 🔧 **微信开发者工具** (小程序开发)

## 🚨 **常见问题**

### **Q: 端口被占用怎么办？**
A: 启动脚本会自动检测和清理端口，如果仍有问题：
```bash
# 手动清理端口
lsof -ti:8000 | xargs kill -9
lsof -ti:3000 | xargs kill -9
```

### **Q: Django启动失败？**
A: 检查以下项目：
1. Python版本是否为3.9+
2. 虚拟环境是否正确激活
3. 依赖是否完整安装
4. 数据库配置是否正确

### **Q: 前端启动失败？**
A: 检查Node.js环境：
```bash
# 检查Node.js版本
node --version
npm --version

# 重新安装依赖
cd admin
rm -rf node_modules
npm install
```

### **Q: MCP服务器状态异常？**
A: 运行监控脚本检查：
```bash
python3 scripts/monitor-implementation.py
```

## 📊 **性能指标**

### **启动时间对比**
- **start.sh**: ~30秒 (Django + Vue)
- **start-all-dev.sh**: ~60秒 (Django + Vue + Taro)

### **资源占用**
- **内存使用**: 约500MB-1GB
- **CPU使用**: 启动时较高，稳定后较低
- **磁盘空间**: 约2GB (包含依赖)

## 🔄 **更新和维护**

### **脚本更新**
- 启动脚本会自动检查和更新依赖
- MCP配置会自动验证和应用
- 开发规范会自动检查合规性

### **问题反馈**
如遇到问题，请：
1. 查看启动日志
2. 运行监控脚本诊断
3. 检查环境配置
4. 参考故障排除指南

---

> **💡 提示**: 
> 1. 推荐使用 `./start.sh` 进行日常开发
> 2. 需要完整功能时使用 `./start-all-dev.sh`
> 3. 使用 `./stop-all-dev.sh` 停止所有服务
> 4. 遇到问题时查看本指南的常见问题部分

**基于9个稳定MCP服务器的开发规范体系，为您提供最佳的开发体验！** ✨
