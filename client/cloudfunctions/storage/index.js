const cloud=require("wx-server-sdk");cloud.init({env:cloud.DYNAMIC_CURRENT_ENV});const db=cloud.database(),_=db.command;async function initStorage(e,t){try{const t=cloud.storage(),r=await t.listFiles({fileList:[]}).catch(e=>({errCode:-1,errMsg:e.message||"\u5b58\u50a8\u6876\u4e0d\u53ef\u7528"}));if(0!==r.errCode)return{code:-1,message:"\u5b58\u50a8\u6876\u4e0d\u53ef\u7528",error:r.errMsg};const a=["avatars","services","therapists","health-tips","banners","temp"],s=a.map(t=>createDirectory(e,t));return await Promise.all(s),{code:0,message:"\u5b58\u50a8\u521d\u59cb\u5316\u6210\u529f",baseDirectories:a}}catch(e){return{code:-1,message:"\u5b58\u50a8\u521d\u59cb\u5316\u5931\u8d25",error:e}}}async function createDirectory(e,t){if(!t)return{code:-1,message:"\u76ee\u5f55\u8def\u5f84\u4e0d\u80fd\u4e3a\u7a7a"};try{const e=t.endsWith("/")?t:`${t}/`,r=await cloud.storage().uploadFile({cloudPath:`${e}.placeholder`,fileContent:Buffer.from("")});return{code:0,message:"\u76ee\u5f55\u521b\u5efa\u6210\u529f",path:e,fileID:r.fileID}}catch(e){return{code:-1,message:"\u521b\u5efa\u76ee\u5f55\u5931\u8d25",error:e}}}async function uploadTestFile(e,t,r){if(!t)return{code:-1,message:"\u6587\u4ef6\u8def\u5f84\u4e0d\u80fd\u4e3a\u7a7a"};try{const e=JSON.stringify(r||{test:"test data",time:Date.now()}),a=await cloud.storage().uploadFile({cloudPath:t,fileContent:Buffer.from(e)});return{code:0,message:"\u6d4b\u8bd5\u6587\u4ef6\u4e0a\u4f20\u6210\u529f",path:t,fileID:a.fileID}}catch(e){return{code:-1,message:"\u6d4b\u8bd5\u6587\u4ef6\u4e0a\u4f20\u5931\u8d25",error:e}}}exports.main=async(e,t)=>{const{action:r,bucket:a,region:s,path:o,data:c}=e;switch(r){case"initStorage":return await initStorage(a,s);case"createDirectory":return await createDirectory(a,o);case"uploadTestFile":return await uploadTestFile(a,o,c);default:return{code:-1,message:"\u672a\u77e5\u64cd\u4f5c"}}};