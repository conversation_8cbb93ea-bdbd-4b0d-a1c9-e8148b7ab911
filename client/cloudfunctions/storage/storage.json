{"rules": [{"action": "read", "condition": "true"}, {"action": "write", "condition": "auth.openid != null"}, {"action": "read", "condition": "true", "path": "public/**"}, {"action": "write", "condition": "auth.openid != null", "path": "public/**"}, {"action": "read", "condition": "doc.openid == auth.openid", "path": "private/**"}, {"action": "write", "condition": "doc.openid == auth.openid", "path": "private/**"}, {"action": "read", "condition": "true", "path": "services/**"}, {"action": "write", "condition": "auth.openid != null && 'admin' in auth.roles", "path": "services/**"}, {"action": "read", "condition": "true", "path": "therapists/**"}, {"action": "write", "condition": "auth.openid != null && 'admin' in auth.roles", "path": "therapists/**"}, {"action": "read", "condition": "true", "path": "banners/**"}, {"action": "write", "condition": "auth.openid != null && 'admin' in auth.roles", "path": "banners/**"}, {"action": "read", "condition": "true", "path": "health_tips/**"}, {"action": "write", "condition": "auth.openid != null && 'admin' in auth.roles", "path": "health_tips/**"}, {"action": "read", "condition": "doc.openid == auth.openid || 'admin' in auth.roles", "path": "user_avatars/**"}, {"action": "write", "condition": "doc.openid == auth.openid || 'admin' in auth.roles", "path": "user_avatars/**"}]}