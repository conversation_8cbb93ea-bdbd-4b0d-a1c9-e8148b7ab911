const cloud=require("wx-server-sdk");cloud.init({env:cloud.DYNAMIC_CURRENT_ENV});const db=cloud.database();async function initCollection(e,t){if(!e)return{code:-1,message:"\u96c6\u5408\u540d\u79f0\u4e0d\u80fd\u4e3a\u7a7a"};try{const r=await db.createCollection(e).catch(e=>{if(-501001!==e.errCode)throw e;return{created:!1}});return t&&Array.isArray(t)&&t.length>0&&await db.collection(e).add({data:t}),{code:0,message:r.created?"\u96c6\u5408\u521b\u5efa\u6210\u529f":"\u96c6\u5408\u5df2\u5b58\u5728",collection:e}}catch(e){return{code:-1,message:"\u521d\u59cb\u5316\u96c6\u5408\u5931\u8d25",error:e}}}async function createIndexes(e,t){if(!e)return{code:-1,message:"\u96c6\u5408\u540d\u79f0\u4e0d\u80fd\u4e3a\u7a7a"};if(!t||!Array.isArray(t)||0===t.length)return{code:-1,message:"\u7d22\u5f15\u914d\u7f6e\u4e0d\u80fd\u4e3a\u7a7a"};try{const r=[];for(const a of t){const{name:t,key:c,unique:n,partialFilterExpression:o}=a,s=await db.collection(e).createIndex({name:t,key:c,unique:n,partialFilterExpression:o});r.push({name:t,result:s})}return{code:0,message:"\u7d22\u5f15\u521b\u5efa\u6210\u529f",collection:e,results:r}}catch(e){return{code:-1,message:"\u521b\u5efa\u7d22\u5f15\u5931\u8d25",error:e}}}exports.main=async(e,t)=>{const{action:r,collection:a,data:c}=e;switch(r){case"initCollection":return await initCollection(a,c);case"createIndexes":return await createIndexes(a,c);default:return{code:-1,message:"\u672a\u77e5\u64cd\u4f5c"}}};