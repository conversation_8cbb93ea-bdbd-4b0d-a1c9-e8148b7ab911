const cloud=require("wx-server-sdk");cloud.init({env:cloud.DYNAMIC_CURRENT_ENV});const db=cloud.database(),_=db.command,MAX_LIMIT=100;async function getHealthTipsList(t,e){try{const a=(t-1)*e,i=await db.collection("health_tips").where({status:1}).count(),s=i.total,c=await db.collection("health_tips").where({status:1}).orderBy("is_top","desc").orderBy("publish_time","desc").skip(a).limit(e).get(),o=c.data.map(t=>(t.publish_time&&(t.publish_time=t.publish_time.toJSON()),t));return{code:0,message:"\u83b7\u53d6\u6210\u529f",data:o,total:s,page:t,pageSize:e}}catch(t){return{code:-1,message:"\u83b7\u53d6\u5065\u5eb7\u5c0f\u8d34\u58eb\u5217\u8868\u5931\u8d25",error:t}}}async function getHealthTipDetail(t){try{if(!t)return{code:-1,message:"\u53c2\u6570\u9519\u8bef"};const e=await db.collection("health_tips").doc(t).get();if(!e.data)return{code:-1,message:"\u5065\u5eb7\u5c0f\u8d34\u58eb\u4e0d\u5b58\u5728"};const a=e.data;a.publish_time&&(a.publish_time=a.publish_time.toJSON());const i=await db.collection("health_tip_tags").where({health_tip_id:t}).get();if(i.data&&i.data.length>0){const t=i.data.map(t=>t.tag_id),e=await db.collection("tags").where({_id:_.in(t),status:1}).get();a.tags=e.data||[]}else a.tags=[];return{code:0,message:"\u83b7\u53d6\u6210\u529f",data:a}}catch(t){return{code:-1,message:"\u83b7\u53d6\u5065\u5eb7\u5c0f\u8d34\u58eb\u8be6\u60c5\u5931\u8d25",error:t}}}async function updateViewCount(t){try{return t?(await db.collection("health_tips").doc(t).update({data:{view_count:_.inc(1)}}),{code:0,message:"\u66f4\u65b0\u6210\u529f"}):{code:-1,message:"\u53c2\u6570\u9519\u8bef"}}catch(t){return{code:-1,message:"\u66f4\u65b0\u6d4f\u89c8\u6b21\u6570\u5931\u8d25",error:t}}}async function updateLikeCount(t,e){try{return t?(await db.collection("health_tips").doc(t).update({data:{like_count:e?_.inc(1):_.inc(-1)}}),{code:0,message:"\u66f4\u65b0\u6210\u529f"}):{code:-1,message:"\u53c2\u6570\u9519\u8bef"}}catch(t){return{code:-1,message:"\u66f4\u65b0\u70b9\u8d5e\u6b21\u6570\u5931\u8d25",error:t}}}exports.main=async(t,e)=>{const{action:a="list",id:i,page:s=1,pageSize:c=10}=t;switch(a){case"list":return await getHealthTipsList(s,c);case"detail":return await getHealthTipDetail(i);case"updateViewCount":return await updateViewCount(i);case"updateLikeCount":return await updateLikeCount(i,t.isLike);default:return{code:-1,message:"\u672a\u77e5\u64cd\u4f5c"}}};