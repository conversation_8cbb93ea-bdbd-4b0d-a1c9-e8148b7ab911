const cloud=require("wx-server-sdk");cloud.init({env:cloud.DYNAMIC_CURRENT_ENV});const db=cloud.database(),bookingsCollection=db.collection("bookings");exports.main=async(e,o)=>{const s=cloud.getWXContext(),{id:r}=e;try{if(!r)return{success:!1,errCode:"INVALID_PARAMS",errMsg:"\u53c2\u6570\u4e0d\u5b8c\u6574"};let e;e=r.startsWith("B")?bookingsCollection.where({booking_id:r,openid:s.OPENID}):bookingsCollection.where({_id:r,openid:s.OPENID});const o=await e.get();return o.data&&0!==o.data.length?{success:!0,data:o.data[0]}:{success:!1,errCode:"BOOKING_NOT_FOUND",errMsg:"\u9884\u7ea6\u4e0d\u5b58\u5728\u6216\u65e0\u6743\u67e5\u770b"}}catch(e){return{success:!1,errCode:e.errCode||"UNKNOWN_ERROR",errMsg:e.errMsg||e.message||"\u83b7\u53d6\u9884\u7ea6\u8be6\u60c5\u5931\u8d25"}}};