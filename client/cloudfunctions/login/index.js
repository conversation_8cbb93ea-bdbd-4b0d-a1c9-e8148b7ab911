const cloud=require("wx-server-sdk");cloud.init({env:cloud.DYNAMIC_CURRENT_ENV});const db=cloud.database(),usersCollection=db.collection("users");exports.main=async(e,t)=>{const s=cloud.getWXContext();if(!s.OPENID)return{code:401,message:"\u672a\u6388\u6743\u7684\u8bbf\u95ee",data:null};try{const e=await usersCollection.where({openid:s.OPENID}).get();if(e.data&&e.data.length>0){const t=e.data[0],o=`${s.OPENID}_${Date.now()}`;return{code:200,message:"\u767b\u5f55\u6210\u529f",data:{openid:s.OPENID,userInfo:t,token:o,isRegistered:!0}}}return{code:200,message:"\u7528\u6237\u672a\u6ce8\u518c",data:{openid:s.OPENID,isRegistered:!1}}}catch(e){return{code:500,message:"\u767b\u5f55\u5931\u8d25",data:null}}};