const cloud=require("wx-server-sdk");cloud.init({env:cloud.DYNAMIC_CURRENT_ENV});const db=cloud.database(),usersCollection=db.collection("users");exports.main=async(e,t)=>{const a=cloud.getWXContext();if(!a.OPENID)return{code:401,message:"\u672a\u6388\u6743\u7684\u8bbf\u95ee",data:null};try{const t=await usersCollection.where({openid:a.OPENID}).get();let o=e;o.openid=a.OPENID;const s=db.serverDate();if(t.data&&t.data.length>0){const e=t.data[0];await usersCollection.doc(e._id).update({data:{...o,update_time:s}});const a=await usersCollection.doc(e._id).get();return{code:200,message:"\u66f4\u65b0\u7528\u6237\u4fe1\u606f\u6210\u529f",data:{userInfo:a.data}}}{o.create_time=s,o.update_time=s,o.roles||(o.roles=["user"]);const e=await usersCollection.add({data:o}),t=await usersCollection.doc(e._id).get();return{code:200,message:"\u6ce8\u518c\u6210\u529f",data:{userInfo:t.data}}}}catch(e){return{code:500,message:"\u6ce8\u518c\u5931\u8d25",data:null}}};