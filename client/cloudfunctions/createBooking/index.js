const cloud=require("wx-server-sdk");cloud.init({env:cloud.DYNAMIC_CURRENT_ENV});const db=cloud.database(),bookingsCollection=db.collection("bookings"),servicesCollection=db.collection("services"),therapistsCollection=db.collection("therapists");exports.main=async(e,t)=>{const r=cloud.getWXContext(),{service_id:s,therapist_id:a,date:o,time:i,remarks:c}=e;try{if(!s||!a||!o||!i)return{success:!1,errCode:"INVALID_PARAMS",errMsg:"\u53c2\u6570\u4e0d\u5b8c\u6574"};const e=await servicesCollection.doc(s).get();if(!e.data)return{success:!1,errCode:"SERVICE_NOT_FOUND",errMsg:"\u670d\u52a1\u4e0d\u5b58\u5728"};const t=e.data,d=await therapistsCollection.doc(a).get();if(!d.data)return{success:!1,errCode:"THERAPIST_NOT_FOUND",errMsg:"\u6280\u5e08\u4e0d\u5b58\u5728"};const n=d.data,l=await bookingsCollection.where({therapist_id:a,date:o,time:i,status:db.command.neq("cancelled")}).get();if(l.data&&l.data.length>0)return{success:!1,errCode:"TIME_SLOT_UNAVAILABLE",errMsg:"\u8be5\u65f6\u95f4\u6bb5\u5df2\u88ab\u9884\u7ea6"};const _=`B${Date.now()}${Math.floor(1e3*Math.random()).toString().padStart(3,"0")}`,u={booking_id:_,openid:r.OPENID,service_id:s,service_name:t.name,price:t.price,therapist_id:a,therapist_name:n.name,date:o,time:i,remarks:c||"",status:"pending",created_at:db.serverDate(),updated_at:db.serverDate()},g=await bookingsCollection.add({data:u});return{success:!0,booking_id:_,_id:g._id}}catch(e){return{success:!1,errCode:e.errCode||"UNKNOWN_ERROR",errMsg:e.errMsg||e.message||"\u521b\u5efa\u9884\u7ea6\u5931\u8d25"}}};