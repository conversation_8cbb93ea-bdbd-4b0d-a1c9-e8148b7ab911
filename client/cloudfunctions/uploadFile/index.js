const cloud=require("wx-server-sdk");cloud.init({env:cloud.DYNAMIC_CURRENT_ENV}),exports.main=async(e,r)=>{const s=cloud.getWXContext(),{cloudPath:t}=e;try{if(!t)return{success:!1,errCode:"INVALID_PARAMS",errMsg:"\u53c2\u6570\u9519\u8bef\uff0ccloudPath\u4e0d\u80fd\u4e3a\u7a7a"};if(!/^[a-zA-Z0-9_\-\/]+\.[a-zA-Z0-9]+$/.test(t))return{success:!1,errCode:"INVALID_PATH",errMsg:"\u6587\u4ef6\u8def\u5f84\u683c\u5f0f\u4e0d\u6b63\u786e"};let e=t;t.startsWith("public/")||(e=`users/${s.OPENID}/${t}`);const r=await cloud.getTempFileURL({fileList:[{fileID:e,maxAge:600}]});return{success:!0,fileID:e,tempFileURL:r.fileList[0].tempFileURL,requestId:s.REQUESTID,openid:s.OPENID}}catch(e){return{success:!1,errCode:e.errCode||"UNKNOWN_ERROR",errMsg:e.errMsg||e.message||"\u672a\u77e5\u9519\u8bef",requestId:s.REQUESTID}}};