#!/usr/bin/env node

/**
 * 微信小程序测试脚本
 * 测试Taro小程序的代码结构、配置和功能逻辑
 * CTO级别完美主义标准
 */

const fs = require('fs').promises;
const path = require('path');

class MiniprogramTester {
  constructor() {
    this.results = [];
    this.startTime = Date.now();
    this.projectRoot = path.join(__dirname, '..');
  }

  async logResult(category, test, success, details) {
    this.results.push({
      category,
      test,
      success,
      details,
      timestamp: new Date().toISOString()
    });
    
    const status = success ? '✅' : '❌';
    console.log(`  ${status} ${test}: ${details}`);
  }

  async testProjectStructure() {
    console.log('📁 测试小程序项目结构...');
    
    try {
      // 检查核心文件
      const coreFiles = [
        'package.json',
        'project.config.json',
        'src/app.js',
        'src/app.config.js',
        'src/app.scss'
      ];

      for (const file of coreFiles) {
        try {
          await fs.access(path.join(this.projectRoot, file));
          await this.logResult('项目结构', `核心文件-${path.basename(file)}`, true, '文件存在');
        } catch (error) {
          await this.logResult('项目结构', `核心文件-${path.basename(file)}`, false, '文件缺失');
        }
      }

      // 检查目录结构
      const directories = ['src/pages', 'src/components', 'src/api', 'src/utils'];
      for (const dir of directories) {
        try {
          const stats = await fs.stat(path.join(this.projectRoot, dir));
          await this.logResult('项目结构', `目录-${path.basename(dir)}`, stats.isDirectory(), '目录存在');
        } catch (error) {
          await this.logResult('项目结构', `目录-${path.basename(dir)}`, false, '目录缺失');
        }
      }

      return true;
    } catch (error) {
      await this.logResult('项目结构', '结构检查', false, error.message);
      return false;
    }
  }

  async testTaroConfiguration() {
    console.log('⚙️ 测试Taro配置...');
    
    try {
      // 检查package.json
      const packageJson = JSON.parse(await fs.readFile(path.join(this.projectRoot, 'package.json'), 'utf8'));
      
      const hasTaro = packageJson.dependencies && packageJson.dependencies['@tarojs/taro'];
      const hasTaroComponents = packageJson.dependencies && packageJson.dependencies['@tarojs/components'];
      const hasTaroWeapp = packageJson.devDependencies &&
        (packageJson.devDependencies['@tarojs/webpack-runner'] || packageJson.devDependencies['@tarojs/webpack5-runner']);
      
      await this.logResult('Taro配置', 'Taro核心', !!hasTaro, hasTaro || '未安装');
      await this.logResult('Taro配置', 'Taro组件', !!hasTaroComponents, hasTaroComponents || '未安装');
      await this.logResult('Taro配置', 'Webpack运行器', !!hasTaroWeapp, hasTaroWeapp || '未安装');

      // 检查构建脚本
      const scripts = packageJson.scripts || {};
      const hasBuildWeapp = scripts['build:weapp'];
      const hasDevWeapp = scripts['dev:weapp'];
      
      await this.logResult('Taro配置', '构建脚本', !!hasBuildWeapp, hasBuildWeapp || '缺失');
      await this.logResult('Taro配置', '开发脚本', !!hasDevWeapp, hasDevWeapp || '缺失');

      // 检查project.config.json
      const projectConfig = JSON.parse(await fs.readFile(path.join(this.projectRoot, 'project.config.json'), 'utf8'));
      const hasAppId = projectConfig.appid;
      const hasProjectName = projectConfig.projectname;
      
      await this.logResult('Taro配置', '小程序AppID', !!hasAppId, hasAppId || '未配置');
      await this.logResult('Taro配置', '项目名称', !!hasProjectName, hasProjectName || '未配置');

      return true;
    } catch (error) {
      await this.logResult('Taro配置', '配置检查', false, error.message);
      return false;
    }
  }

  async testAppConfiguration() {
    console.log('📱 测试小程序应用配置...');
    
    try {
      // 检查app.config.js
      const appConfigContent = await fs.readFile(path.join(this.projectRoot, 'src/app.config.js'), 'utf8');
      
      const hasPages = appConfigContent.includes('pages');
      const hasWindow = appConfigContent.includes('window');
      const hasTabBar = appConfigContent.includes('tabBar');
      
      await this.logResult('应用配置', '页面配置', hasPages, hasPages ? '包含页面配置' : '缺少页面配置');
      await this.logResult('应用配置', '窗口配置', hasWindow, hasWindow ? '包含窗口配置' : '缺少窗口配置');
      await this.logResult('应用配置', '底部导航', hasTabBar, hasTabBar ? '包含底部导航' : '缺少底部导航');

      // 检查app.js
      const appJsContent = await fs.readFile(path.join(this.projectRoot, 'src/app.js'), 'utf8');
      
      const hasTaroImport = appJsContent.includes('@tarojs/taro') || appJsContent.includes('Taro');
      const hasComponentImport = appJsContent.includes('@tarojs/components');
      const hasAppClass = appJsContent.includes('class App') || appJsContent.includes('function App') || appJsContent.includes('createApp');
      
      await this.logResult('应用配置', 'Taro导入', hasTaroImport, hasTaroImport ? '正确导入Taro' : '缺少Taro导入');
      await this.logResult('应用配置', '组件导入', hasComponentImport, hasComponentImport ? '正确导入组件' : '缺少组件导入');
      await this.logResult('应用配置', '应用组件', hasAppClass, hasAppClass ? '应用组件存在' : '应用组件缺失');

      return true;
    } catch (error) {
      await this.logResult('应用配置', '配置检查', false, error.message);
      return false;
    }
  }

  async testPagesStructure() {
    console.log('📄 测试页面结构...');
    
    try {
      const pagesDir = path.join(this.projectRoot, 'src/pages');
      const pageDirectories = await fs.readdir(pagesDir);
      
      await this.logResult('页面结构', '页面数量', pageDirectories.length > 0, `页面数量: ${pageDirectories.length}`);

      // 检查每个页面的基本文件
      let validPages = 0;
      for (const pageDir of pageDirectories) {
        const pagePath = path.join(pagesDir, pageDir);
        const stats = await fs.stat(pagePath);
        
        if (stats.isDirectory()) {
          try {
            const pageFiles = await fs.readdir(pagePath);
            const hasIndex = pageFiles.some(file => file.startsWith('index.'));
            const hasConfig = pageFiles.some(file => file.includes('config.'));
            
            if (hasIndex) {
              validPages++;
              await this.logResult('页面结构', `页面-${pageDir}`, hasIndex, 
                `入口文件: ${hasIndex ? '✓' : '✗'}, 配置文件: ${hasConfig ? '✓' : '✗'}`);
            }
          } catch (error) {
            await this.logResult('页面结构', `页面-${pageDir}`, false, '读取失败');
          }
        }
      }

      await this.logResult('页面结构', '有效页面', validPages > 0, `有效页面: ${validPages}/${pageDirectories.length}`);

      return true;
    } catch (error) {
      await this.logResult('页面结构', '页面检查', false, error.message);
      return false;
    }
  }

  async testComponentsStructure() {
    console.log('🧩 测试组件结构...');
    
    try {
      const componentsDir = path.join(this.projectRoot, 'src/components');
      
      try {
        const componentDirectories = await fs.readdir(componentsDir);
        await this.logResult('组件结构', '组件数量', componentDirectories.length >= 0, `组件数量: ${componentDirectories.length}`);

        // 检查组件文件结构
        let validComponents = 0;
        for (const componentDir of componentDirectories) {
          const componentPath = path.join(componentsDir, componentDir);
          const stats = await fs.stat(componentPath);
          
          if (stats.isDirectory()) {
            try {
              const componentFiles = await fs.readdir(componentPath);
              const hasIndex = componentFiles.some(file => file.startsWith('index.'));
              
              if (hasIndex) {
                validComponents++;
              }
              
              await this.logResult('组件结构', `组件-${componentDir}`, hasIndex, hasIndex ? '结构正确' : '缺少入口文件');
            } catch (error) {
              await this.logResult('组件结构', `组件-${componentDir}`, false, '读取失败');
            }
          }
        }

        await this.logResult('组件结构', '有效组件', validComponents >= 0, `有效组件: ${validComponents}/${componentDirectories.length}`);
      } catch (error) {
        await this.logResult('组件结构', '组件目录', false, '组件目录不存在或为空');
      }

      return true;
    } catch (error) {
      await this.logResult('组件结构', '组件检查', false, error.message);
      return false;
    }
  }

  async testAPIStructure() {
    console.log('📡 测试API结构...');
    
    try {
      const apiDir = path.join(this.projectRoot, 'src/api');
      const apiFiles = await fs.readdir(apiDir);
      
      await this.logResult('API结构', 'API文件数量', apiFiles.length > 0, `API文件数量: ${apiFiles.length}`);

      // 检查主要API文件
      const expectedApiFiles = ['index.js', 'user.js', 'cloud-api.js'];
      for (const apiFile of expectedApiFiles) {
        const hasFile = apiFiles.includes(apiFile);
        await this.logResult('API结构', `API文件-${apiFile}`, hasFile, hasFile ? '文件存在' : '文件缺失');
      }

      // 检查API文件内容
      if (apiFiles.includes('index.js')) {
        const indexContent = await fs.readFile(path.join(apiDir, 'index.js'), 'utf8');
        const hasRequest = indexContent.includes('request') || indexContent.includes('Taro.request');
        await this.logResult('API结构', '请求方法', hasRequest, hasRequest ? '包含请求方法' : '缺少请求方法');
      }

      return true;
    } catch (error) {
      await this.logResult('API结构', 'API检查', false, error.message);
      return false;
    }
  }

  async testUtilsStructure() {
    console.log('🛠️ 测试工具函数结构...');
    
    try {
      const utilsDir = path.join(this.projectRoot, 'src/utils');
      const utilFiles = await fs.readdir(utilsDir);
      
      await this.logResult('工具函数', '工具文件数量', utilFiles.length > 0, `工具文件数量: ${utilFiles.length}`);

      // 检查主要工具文件
      const expectedUtilFiles = ['request.js', 'storage.js', 'cloud.js'];
      for (const utilFile of expectedUtilFiles) {
        const hasFile = utilFiles.includes(utilFile);
        await this.logResult('工具函数', `工具文件-${utilFile}`, hasFile, hasFile ? '文件存在' : '文件缺失');
      }

      // 检查工具文件功能
      if (utilFiles.includes('request.js')) {
        const requestContent = await fs.readFile(path.join(utilsDir, 'request.js'), 'utf8');
        const hasRequestFunction = requestContent.includes('function') || requestContent.includes('export');
        await this.logResult('工具函数', '请求工具', hasRequestFunction, hasRequestFunction ? '包含请求工具' : '缺少请求工具');
      }

      if (utilFiles.includes('storage.js')) {
        const storageContent = await fs.readFile(path.join(utilsDir, 'storage.js'), 'utf8');
        const hasStorageFunction = storageContent.includes('Storage') || storageContent.includes('storage');
        await this.logResult('工具函数', '存储工具', hasStorageFunction, hasStorageFunction ? '包含存储工具' : '缺少存储工具');
      }

      return true;
    } catch (error) {
      await this.logResult('工具函数', '工具检查', false, error.message);
      return false;
    }
  }

  async testCloudFunctions() {
    console.log('☁️ 测试云函数结构...');
    
    try {
      const cloudDir = path.join(this.projectRoot, 'cloudfunctions');
      
      try {
        const cloudFunctions = await fs.readdir(cloudDir);
        await this.logResult('云函数', '云函数数量', cloudFunctions.length > 0, `云函数数量: ${cloudFunctions.length}`);

        // 检查主要云函数
        const expectedFunctions = ['login', 'database', 'createBooking'];
        let validFunctions = 0;
        
        for (const funcName of expectedFunctions) {
          if (cloudFunctions.includes(funcName)) {
            try {
              const funcPath = path.join(cloudDir, funcName);
              const funcFiles = await fs.readdir(funcPath);
              const hasIndex = funcFiles.includes('index.js');
              const hasPackage = funcFiles.includes('package.json');
              
              if (hasIndex) validFunctions++;
              
              await this.logResult('云函数', `云函数-${funcName}`, hasIndex, 
                `入口文件: ${hasIndex ? '✓' : '✗'}, 配置文件: ${hasPackage ? '✓' : '✗'}`);
            } catch (error) {
              await this.logResult('云函数', `云函数-${funcName}`, false, '读取失败');
            }
          } else {
            await this.logResult('云函数', `云函数-${funcName}`, false, '函数不存在');
          }
        }

        await this.logResult('云函数', '有效云函数', validFunctions > 0, `有效云函数: ${validFunctions}/${expectedFunctions.length}`);
      } catch (error) {
        await this.logResult('云函数', '云函数目录', false, '云函数目录不存在');
      }

      return true;
    } catch (error) {
      await this.logResult('云函数', '云函数检查', false, error.message);
      return false;
    }
  }

  async generateReport() {
    const endTime = Date.now();
    const duration = endTime - this.startTime;
    
    const successCount = this.results.filter(r => r.success).length;
    const totalCount = this.results.length;
    const successRate = Math.round((successCount / totalCount) * 100);

    // 按类别统计
    const categories = {};
    this.results.forEach(result => {
      if (!categories[result.category]) {
        categories[result.category] = { total: 0, success: 0 };
      }
      categories[result.category].total++;
      if (result.success) {
        categories[result.category].success++;
      }
    });

    const report = {
      summary: {
        type: 'miniprogram-test',
        totalTests: totalCount,
        successCount: successCount,
        failureCount: totalCount - successCount,
        successRate: successRate,
        duration: duration,
        timestamp: new Date().toISOString()
      },
      categories: categories,
      results: this.results
    };

    // 保存报告
    const reportDir = path.join(__dirname, '../test-reports');
    await fs.mkdir(reportDir, { recursive: true });
    
    const reportFile = path.join(reportDir, `miniprogram-${new Date().toISOString().replace(/[:.]/g, '-')}.json`);
    await fs.writeFile(reportFile, JSON.stringify(report, null, 2));

    return { report, reportFile };
  }

  async run() {
    try {
      console.log('📱 启动微信小程序测试...');
      
      // 执行所有测试
      await this.testProjectStructure();
      await this.testTaroConfiguration();
      await this.testAppConfiguration();
      await this.testPagesStructure();
      await this.testComponentsStructure();
      await this.testAPIStructure();
      await this.testUtilsStructure();
      await this.testCloudFunctions();

      const { report, reportFile } = await this.generateReport();

      // 输出结果
      console.log('\n📱 微信小程序测试完成');
      console.log(`✅ 测试结果: ${report.summary.successCount}/${report.summary.totalTests} (${report.summary.successRate}%)`);
      console.log(`⏱️ 耗时: ${Math.round(report.summary.duration / 1000)}秒`);
      console.log(`📄 详细报告: ${reportFile}`);

      // 分类统计
      console.log('\n📊 分类统计:');
      Object.entries(report.categories).forEach(([category, stats]) => {
        const rate = Math.round((stats.success / stats.total) * 100);
        console.log(`  ${category}: ${stats.success}/${stats.total} (${rate}%)`);
      });

      // 综合评估
      if (report.summary.successRate >= 95) {
        console.log('\n🏆 完美！微信小程序结构完整，配置正确');
      } else if (report.summary.successRate >= 85) {
        console.log('\n✅ 优秀！微信小程序基本正常');
      } else if (report.summary.successRate >= 70) {
        console.log('\n⚠️ 良好！部分功能需要完善');
      } else {
        console.log('\n❌ 需要改进！小程序结构存在问题');
      }

    } catch (error) {
      console.error('❌ 微信小程序测试失败:', error.message);
    }
  }
}

// 运行微信小程序测试
const tester = new MiniprogramTester();
tester.run();
