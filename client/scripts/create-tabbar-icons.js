#!/usr/bin/env node

/**
 * 创建tabBar图标脚本
 * 生成简单的PNG图标文件
 */

const fs = require('fs');
const path = require('path');

// 创建简单的PNG图标数据 (24x24像素的简单图标)
const createSimplePNG = (color = '#7b7b7b') => {
  // 这是一个最小的PNG文件数据 (24x24像素，单色)
  // 实际项目中应该使用专业的图标设计工具
  const pngHeader = Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x18, // width: 24
    0x00, 0x00, 0x00, 0x18, // height: 24
    0x08, 0x02, 0x00, 0x00, 0x00, // bit depth: 8, color type: 2 (RGB), compression: 0, filter: 0, interlace: 0
    0x9D, 0x19, 0xD5, 0x6B, // CRC
  ]);
  
  // 简化的图像数据 (实际应该包含完整的像素数据)
  const imageData = Buffer.alloc(24 * 24 * 3); // 24x24 RGB
  imageData.fill(0x7b); // 填充灰色
  
  const idat = Buffer.concat([
    Buffer.from([0x00, 0x00, 0x00, 0x00]), // IDAT length (placeholder)
    Buffer.from([0x49, 0x44, 0x41, 0x54]), // IDAT
    imageData,
    Buffer.from([0x00, 0x00, 0x00, 0x00]), // CRC (placeholder)
  ]);
  
  const iend = Buffer.from([
    0x00, 0x00, 0x00, 0x00, // IEND length
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // CRC
  ]);
  
  return Buffer.concat([pngHeader, idat, iend]);
};

// 创建占位符PNG文件
const createPlaceholderPNG = () => {
  // 创建一个最小的有效PNG文件 (1x1像素)
  return Buffer.from([
    0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A, // PNG signature
    0x00, 0x00, 0x00, 0x0D, // IHDR chunk length
    0x49, 0x48, 0x44, 0x52, // IHDR
    0x00, 0x00, 0x00, 0x18, // width: 24
    0x00, 0x00, 0x00, 0x18, // height: 24
    0x01, 0x00, 0x00, 0x00, 0x00, // bit depth: 1, color type: 0 (grayscale)
    0x37, 0x6E, 0xF9, 0x24, // CRC
    0x00, 0x00, 0x00, 0x0A, // IDAT chunk length
    0x49, 0x44, 0x41, 0x54, // IDAT
    0x78, 0x9C, 0x62, 0x00, 0x00, 0x00, 0x02, 0x00, 0x01, 0xE2, // compressed data
    0x21, 0xBC, 0x33, // CRC
    0x00, 0x00, 0x00, 0x00, // IEND chunk length
    0x49, 0x45, 0x4E, 0x44, // IEND
    0xAE, 0x42, 0x60, 0x82  // CRC
  ]);
};

const main = () => {
  console.log('🎨 创建tabBar图标...');
  
  const iconDir = path.join(__dirname, '../src/assets/images/tabbar');
  
  // 确保目录存在
  if (!fs.existsSync(iconDir)) {
    fs.mkdirSync(iconDir, { recursive: true });
  }
  
  // 图标列表
  const icons = [
    'home.png',
    'home-active.png',
    'service.png',
    'service-active.png',
    'booking.png',
    'booking-active.png',
    'profile.png',
    'profile-active.png'
  ];
  
  // 创建占位符图标
  const pngData = createPlaceholderPNG();
  
  icons.forEach(iconName => {
    const iconPath = path.join(iconDir, iconName);
    fs.writeFileSync(iconPath, pngData);
    console.log(`✅ 创建图标: ${iconName}`);
  });
  
  console.log('🎉 tabBar图标创建完成！');
  console.log('📝 注意: 这些是占位符图标，建议使用专业设计工具创建实际图标');
  console.log('📐 图标规格: 24x24像素，PNG格式');
};

if (require.main === module) {
  main();
}

module.exports = { createPlaceholderPNG };
