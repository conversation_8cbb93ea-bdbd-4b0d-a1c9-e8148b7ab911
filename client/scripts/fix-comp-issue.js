#!/usr/bin/env node

/**
 * 修复小程序comp组件循环引用问题
 * 这是一个反复出现的问题，需要在每次编译后自动修复
 */

const fs = require('fs');
const path = require('path');

const log = (message, type = 'info') => {
  const colors = {
    success: '\x1b[32m✅',
    error: '\x1b[31m❌',
    warning: '\x1b[33m⚠️',
    info: '\x1b[34mℹ️'
  };
  console.log(`${colors[type]} ${message}\x1b[0m`);
};

const fixCompIssue = () => {
  log('开始修复comp组件循环引用问题...');
  
  const distDir = path.join(__dirname, '../dist');
  
  if (!fs.existsSync(distDir)) {
    log('dist目录不存在，请先编译小程序', 'error');
    return false;
  }
  
  // 修复comp.json循环引用
  const compJsonPath = path.join(distDir, 'comp.json');
  if (fs.existsSync(compJsonPath)) {
    try {
      const originalContent = fs.readFileSync(compJsonPath, 'utf8');
      log(`原始comp.json内容: ${originalContent}`);
      
      // 修复循环引用
      const fixedConfig = {
        "component": true,
        "usingComponents": {}
      };
      
      fs.writeFileSync(compJsonPath, JSON.stringify(fixedConfig));
      log('修复comp.json循环引用成功', 'success');
    } catch (error) {
      log(`修复comp.json失败: ${error.message}`, 'error');
      return false;
    }
  } else {
    log('comp.json文件不存在', 'warning');
  }
  
  // 检查页面中的comp组件引用
  const pagesDir = path.join(distDir, 'pages');
  if (fs.existsSync(pagesDir)) {
    const pages = fs.readdirSync(pagesDir);
    let hasCompReference = false;
    
    pages.forEach(pageName => {
      const pageJsonPath = path.join(pagesDir, pageName, 'index.json');
      if (fs.existsSync(pageJsonPath)) {
        try {
          const content = fs.readFileSync(pageJsonPath, 'utf8');
          if (content.includes('"comp"')) {
            hasCompReference = true;
            log(`页面 ${pageName} 引用了comp组件`, 'warning');
          }
        } catch (error) {
          log(`检查页面 ${pageName} 失败: ${error.message}`, 'error');
        }
      }
    });
    
    if (hasCompReference) {
      log('发现页面引用comp组件，这可能导致组件未找到警告', 'warning');
      log('如果不需要comp组件，建议从页面配置中移除', 'info');
    } else {
      log('未发现页面引用comp组件', 'success');
    }
  }
  
  return true;
};

const main = () => {
  log('🔧 壹心堂小程序comp组件修复工具');
  log('解决反复出现的"Component is not found in path comp"问题');
  log('');
  
  const success = fixCompIssue();
  
  if (success) {
    log('');
    log('修复完成！现在可以在微信开发者工具中测试', 'success');
    log('如果仍有警告，请检查页面配置中的comp组件引用', 'info');
  } else {
    log('');
    log('修复失败，请检查错误信息', 'error');
    process.exit(1);
  }
};

if (require.main === module) {
  main();
}

module.exports = { fixCompIssue };
