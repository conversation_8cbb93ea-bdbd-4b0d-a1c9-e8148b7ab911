{"name": "yixintang-miniapp", "version": "1.0.0", "private": true, "description": "壹心堂中医推拿小程序", "templateInfo": {"name": "default", "typescript": true, "css": "sass"}, "scripts": {"build:weapp": "taro build --type weapp && node scripts/fix-comp-issue.js", "build:swan": "taro build --type swan", "build:alipay": "taro build --type alipay", "build:tt": "taro build --type tt", "build:h5": "taro build --type h5", "build:rn": "taro build --type rn", "build:qq": "taro build --type qq", "build:jd": "taro build --type jd", "build:quickapp": "taro build --type quickapp", "dev:weapp": "npm run build:weapp -- --watch", "dev:swan": "npm run build:swan -- --watch", "dev:alipay": "npm run build:alipay -- --watch", "dev:tt": "npm run build:tt -- --watch", "dev:h5": "npm run build:h5 -- --watch", "dev:rn": "npm run build:rn -- --watch", "dev:qq": "npm run build:qq -- --watch", "dev:jd": "npm run build:jd -- --watch", "dev:quickapp": "npm run build:quickapp -- --watch", "dev": "node run-dev.js", "open:devtools": "/Applications/wechatwebdevtools.app/Contents/MacOS/cli open --project ./dist", "cloud:deploy": "node cloud-deploy.js", "cloud:deploy-database": "node cloud-deploy.js database", "cloud:deploy-storage": "node cloud-deploy.js storage", "setup": "bash setup.sh"}, "browserslist": ["last 3 versions", "Android >= 4.1", "ios >= 8"], "author": "", "dependencies": {"@babel/runtime": "^7.7.7", "@cloudbase/js-sdk": "^1.7.2", "@tarojs/cli": "^4.0.6", "@tarojs/components": "^4.0.6", "@tarojs/helper": "^4.0.6", "@tarojs/plugin-framework-vue3": "^4.0.6", "@tarojs/plugin-platform-weapp": "^4.0.6", "@tarojs/runtime": "^4.0.6", "@tarojs/shared": "^4.0.6", "@tarojs/taro": "^4.0.6", "@vue/compiler-sfc": "^3.5.17", "vue": "^3.3.0", "vue-router": "^4.0.0", "vuex": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@tarojs/cli": "^4.0.6", "@tarojs/webpack5-runner": "^4.0.6", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "babel-preset-taro": "^4.0.6", "chalk": "^4.1.2", "eslint": "^8.12.0", "eslint-config-taro": "^4.0.6", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "stylelint": "^14.4.0", "typescript": "^4.1.0", "vue-loader": "^17.4.2", "webpack": "^5.99.9", "webpack-cli": "^5.1.4", "wx-server-sdk": "^2.6.3"}}