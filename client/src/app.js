import { createApp } from 'vue'
import './app.scss'
// 导入Taro组件 - 修复组件导入问题
import '@tarojs/components/dist/taro-components/taro-components.css'
import { initCloud, login } from './utils/cloud'
import { enforceWechatMiniProgram } from './utils/env-check'
import { initPrivacyPolicy } from './utils/privacy'

const App = createApp({
  onLaunch(options) {
    console.log('App onLaunch')
    
    // 强制在微信小程序中打开
    enforceWechatMiniProgram({
      redirectOnWeb: true,
      defaultPath: 'pages/index/index'
    })
    
    // 初始化云开发
    if (process.env.TARO_ENV === 'weapp') {
      // 使用工具类初始化云开发
      initCloud()
      
      // 自动登录获取用户OpenID
      this.autoLogin()
    }
    
    // 初始化隐私协议
    initPrivacyPolicy()
  },
  
  // 自动登录方法
  async autoLogin() {
    try {
      const loginResult = await login()
      if (loginResult && loginResult.code === 200) {
        console.log('用户登录成功', loginResult.data)
        // 可以在这里存储用户信息到全局状态
      }
    } catch (error) {
      console.error('自动登录失败', error)
    }
  },
  
  onShow (options) {
    console.log('App onShow')
  },
  
  onHide () {
    console.log('App onHide')
  }
})

export default App 