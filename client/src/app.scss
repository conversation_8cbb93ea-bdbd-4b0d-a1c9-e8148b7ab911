/* 全局样式 */

/* 基础变量 */
:root {
  /* 主色调 */
  --primary-color: #07c160;
  --success-color: #07c160;
  --warning-color: #ff976a;
  --danger-color: #ee0a24;
  --info-color: #1989fa;

  /* 文字颜色 */
  --text-color-primary: #323233;
  --text-color-secondary: #646566;
  --text-color-tertiary: #999999;

  /* 背景颜色 */
  --background-color: #f7f8fa;
  --background-color-light: #f5f5f5;
  --border-color: #ebedf0;
  --border-color-light: #f2f3f5;

  /* 字体大小 */
  --font-size-xs: 20rpx;
  --font-size-sm: 24rpx;
  --font-size-md: 28rpx;
  --font-size-lg: 32rpx;
  --font-size-xl: 36rpx;
  --font-size-xxl: 40rpx;

  /* 间距 */
  --spacing-xs: 8rpx;
  --spacing-sm: 12rpx;
  --spacing-md: 16rpx;
  --spacing-lg: 24rpx;
  --spacing-xl: 32rpx;
  --spacing-xxl: 40rpx;

  /* 圆角 */
  --border-radius-sm: 4rpx;
  --border-radius-md: 8rpx;
  --border-radius-lg: 16rpx;
  --border-radius-round: 999rpx;

  /* 阴影 */
  --shadow-sm: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  --shadow-md: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
  --shadow-lg: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
}

/* 通用样式 */
page {
  font-size: var(--font-size-md);
  color: var(--text-color-primary);
  background-color: var(--background-color);
  line-height: 1.5;
  box-sizing: border-box;
}

view, text, navigator, button, input, textarea {
  box-sizing: border-box;
}

/* 清除默认样式 */
button::after {
  border: none;
}

button {
  background-color: transparent;
  padding: 0;
  margin: 0;
  line-height: 1;
}

/* 文本溢出省略号 */
.ellipsis {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.ellipsis-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}

/* 弹性布局 */
.flex {
  display: flex;
}

.flex-column {
  display: flex;
  flex-direction: column;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-around {
  display: flex;
  align-items: center;
  justify-content: space-around;
}

.flex-1 {
  flex: 1;
}

/* 边距 */
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.ml-xs { margin-left: var(--spacing-xs); }
.ml-sm { margin-left: var(--spacing-sm); }
.ml-md { margin-left: var(--spacing-md); }
.ml-lg { margin-left: var(--spacing-lg); }
.ml-xl { margin-left: var(--spacing-xl); }

.mr-xs { margin-right: var(--spacing-xs); }
.mr-sm { margin-right: var(--spacing-sm); }
.mr-md { margin-right: var(--spacing-md); }
.mr-lg { margin-right: var(--spacing-lg); }
.mr-xl { margin-right: var(--spacing-xl); }

.pt-xs { padding-top: var(--spacing-xs); }
.pt-sm { padding-top: var(--spacing-sm); }
.pt-md { padding-top: var(--spacing-md); }
.pt-lg { padding-top: var(--spacing-lg); }
.pt-xl { padding-top: var(--spacing-xl); }

.pb-xs { padding-bottom: var(--spacing-xs); }
.pb-sm { padding-bottom: var(--spacing-sm); }
.pb-md { padding-bottom: var(--spacing-md); }
.pb-lg { padding-bottom: var(--spacing-lg); }
.pb-xl { padding-bottom: var(--spacing-xl); }

.pl-xs { padding-left: var(--spacing-xs); }
.pl-sm { padding-left: var(--spacing-sm); }
.pl-md { padding-left: var(--spacing-md); }
.pl-lg { padding-left: var(--spacing-lg); }
.pl-xl { padding-left: var(--spacing-xl); }

.pr-xs { padding-right: var(--spacing-xs); }
.pr-sm { padding-right: var(--spacing-sm); }
.pr-md { padding-right: var(--spacing-md); }
.pr-lg { padding-right: var(--spacing-lg); }
.pr-xl { padding-right: var(--spacing-xl); }

/* 字体 */
.text-xs { font-size: var(--font-size-xs); }
.text-sm { font-size: var(--font-size-sm); }
.text-md { font-size: var(--font-size-md); }
.text-lg { font-size: var(--font-size-lg); }
.text-xl { font-size: var(--font-size-xl); }

.text-primary { color: var(--text-color-primary); }
.text-secondary { color: var(--text-color-secondary); }
.text-tertiary { color: var(--text-color-tertiary); }
.text-danger { color: var(--danger-color); }
.text-success { color: var(--success-color); }
.text-warning { color: var(--warning-color); }
.text-info { color: var(--info-color); }

.text-bold { font-weight: 500; }

.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

/* 按钮样式 */
.btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-lg);
  font-size: var(--font-size-md);
  border-radius: var(--border-radius-md);
  height: 80rpx;
  line-height: 80rpx;
}

.btn-primary {
  background-color: var(--primary-color);
  color: #ffffff;
}

.btn-outline {
  background-color: #ffffff;
  color: var(--primary-color);
  border: 2rpx solid var(--primary-color);
}

.btn-plain {
  background-color: transparent;
  color: var(--text-color-primary);
}

.btn-small {
  height: 60rpx;
  line-height: 60rpx;
  font-size: var(--font-size-sm);
  padding: 0 var(--spacing-md);
}

.btn-large {
  height: 88rpx;
  line-height: 88rpx;
  font-size: var(--font-size-lg);
}

.btn-block {
  width: 100%;
  display: block;
}

.btn-round {
  border-radius: var(--border-radius-round);
}

.btn-disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 卡片组件 */
.card {
  background-color: #ffffff;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
}

/* 标签组件 */
.tag {
  display: inline-flex;
  align-items: center;
  padding: 0 var(--spacing-sm);
  height: 40rpx;
  line-height: 40rpx;
  font-size: var(--font-size-xs);
  border-radius: var(--border-radius-sm);
  margin-right: var(--spacing-xs);
}

.tag-primary {
  background-color: rgba(7, 193, 96, 0.1);
  color: var(--primary-color);
}

.tag-success {
  background-color: rgba(7, 193, 96, 0.1);
  color: var(--success-color);
}

.tag-danger {
  background-color: rgba(238, 10, 36, 0.1);
  color: var(--danger-color);
}

.tag-warning {
  background-color: rgba(255, 151, 106, 0.1);
  color: var(--warning-color);
}

.tag-info {
  background-color: rgba(25, 137, 250, 0.1);
  color: var(--info-color);
}

/* 徽标 */
.badge {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  height: 32rpx;
  min-width: 32rpx;
  padding: 0 var(--spacing-xs);
  border-radius: var(--border-radius-round);
  background-color: var(--danger-color);
  color: #ffffff;
  font-size: var(--font-size-xs);
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 分割线 */
.divider {
  height: 1rpx;
  background-color: var(--border-color);
  width: 100%;
  margin: var(--spacing-md) 0;
}

.divider-text {
  display: flex;
  align-items: center;
  color: var(--text-color-tertiary);
  font-size: var(--font-size-sm);
}

.divider-text::before,
.divider-text::after {
  content: '';
  flex: 1;
  height: 1rpx;
  background-color: var(--border-color);
}

.divider-text::before {
  margin-right: var(--spacing-md);
}

.divider-text::after {
  margin-left: var(--spacing-md);
}

/* 常用工具类 */
.clearfix::after {
  content: '';
  display: block;
  clear: both;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.fixed {
  position: fixed;
}

.w-full {
  width: 100%;
}

.h-full {
  height: 100%;
}

.bg-white {
  background-color: #ffffff;
}

.bg-primary {
  background-color: var(--primary-color);
}

.rounded {
  border-radius: var(--border-radius-md);
}

.rounded-lg {
  border-radius: var(--border-radius-lg);
}

.rounded-full {
  border-radius: 50%;
}

.shadow {
  box-shadow: var(--shadow-sm);
}

.shadow-md {
  box-shadow: var(--shadow-md);
}

.shadow-lg {
  box-shadow: var(--shadow-lg);
}

.border {
  border: 2rpx solid var(--border-color);
}

.border-t {
  border-top: 2rpx solid var(--border-color);
}

.border-b {
  border-bottom: 2rpx solid var(--border-color);
}

.border-l {
  border-left: 2rpx solid var(--border-color);
}

.border-r {
  border-right: 2rpx solid var(--border-color);
} 