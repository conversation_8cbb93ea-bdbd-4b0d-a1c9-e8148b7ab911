<template>
  <view class="success-page">
    <view class="success-icon">
      <text class="icon">✓</text>
    </view>
    
    <view class="success-content">
      <text class="title">预约成功</text>
      <text class="desc">您的预约已成功提交，请按时到店享受服务</text>
    </view>
    
    <view class="booking-details">
      <view class="detail-item">
        <text class="label">预约编号</text>
        <text class="value">YXT{{ orderNo }}</text>
      </view>
      <view class="detail-item">
        <text class="label">服务项目</text>
        <text class="value">中医推拿</text>
      </view>
      <view class="detail-item">
        <text class="label">预约时间</text>
        <text class="value">2025-07-06 14:00</text>
      </view>
      <view class="detail-item">
        <text class="label">技师</text>
        <text class="value">张医师</text>
      </view>
    </view>
    
    <view class="actions">
      <button class="btn-home" @tap="goHome">返回首页</button>
      <button class="btn-orders" @tap="goOrders">查看订单</button>
    </view>
  </view>
</template>

<script>
import { defineComponent, ref } from 'vue'
import Taro from '@tarojs/taro'

export default defineComponent({
  name: 'BookingSuccess',
  
  setup() {
    const orderNo = ref(Date.now().toString().slice(-6))
    
    const goHome = () => {
      Taro.switchTab({
        url: '/pages/index/index'
      })
    }
    
    const goOrders = () => {
      Taro.switchTab({
        url: '/pages/orders/index'
      })
    }
    
    return {
      orderNo,
      goHome,
      goOrders
    }
  }
})
</script>

<style lang="scss">
.success-page {
  padding: 40px 20px;
  text-align: center;
  
  .success-icon {
    margin-bottom: 30px;
    
    .icon {
      display: inline-block;
      width: 80px;
      height: 80px;
      line-height: 80px;
      background: #07c160;
      color: #fff;
      border-radius: 50%;
      font-size: 40px;
      font-weight: bold;
    }
  }
  
  .success-content {
    margin-bottom: 40px;
    
    .title {
      display: block;
      font-size: 20px;
      font-weight: bold;
      color: #333;
      margin-bottom: 10px;
    }
    
    .desc {
      font-size: 14px;
      color: #666;
      line-height: 1.5;
    }
  }
  
  .booking-details {
    background: #f8f8f8;
    border-radius: 8px;
    padding: 20px;
    margin-bottom: 40px;
    text-align: left;
    
    .detail-item {
      display: flex;
      justify-content: space-between;
      margin-bottom: 15px;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .label {
        color: #666;
        font-size: 14px;
      }
      
      .value {
        color: #333;
        font-size: 14px;
        font-weight: 500;
      }
    }
  }
  
  .actions {
    display: flex;
    gap: 15px;
    
    .btn-home,
    .btn-orders {
      flex: 1;
      height: 44px;
      border-radius: 22px;
      font-size: 16px;
      border: none;
    }
    
    .btn-home {
      background: #f5f5f5;
      color: #666;
    }
    
    .btn-orders {
      background: #07c160;
      color: #fff;
    }
  }
}
</style>
