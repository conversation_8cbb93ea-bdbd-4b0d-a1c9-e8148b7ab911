<template>
  <view class="confirm-page">
    <view class="header">
      <text class="title">确认预约</text>
    </view>
    
    <view class="content">
      <view class="service-info">
        <text class="service-name">服务确认</text>
        <text class="service-desc">请确认您的预约信息</text>
      </view>
      
      <view class="booking-info">
        <view class="info-item">
          <text class="label">服务项目：</text>
          <text class="value">中医推拿</text>
        </view>
        <view class="info-item">
          <text class="label">预约时间：</text>
          <text class="value">2025-07-06 14:00</text>
        </view>
        <view class="info-item">
          <text class="label">技师：</text>
          <text class="value">张医师</text>
        </view>
      </view>
      
      <view class="actions">
        <button class="btn-cancel" @tap="onCancel">取消</button>
        <button class="btn-confirm" @tap="onConfirm">确认预约</button>
      </view>
    </view>
  </view>
</template>

<script>
import { defineComponent } from 'vue'
import Taro from '@tarojs/taro'

export default defineComponent({
  name: 'BookingConfirm',
  
  methods: {
    onCancel() {
      Taro.navigateBack()
    },
    
    onConfirm() {
      // 确认预约逻辑
      Taro.showToast({
        title: '预约成功',
        icon: 'success'
      })
      
      setTimeout(() => {
        Taro.redirectTo({
          url: '/packageBooking/pages/success/index'
        })
      }, 1500)
    }
  }
})
</script>

<style lang="scss">
.confirm-page {
  padding: 20px;
  
  .header {
    text-align: center;
    margin-bottom: 30px;
    
    .title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
  }
  
  .content {
    .service-info {
      background: #f8f8f8;
      padding: 20px;
      border-radius: 8px;
      margin-bottom: 20px;
      
      .service-name {
        display: block;
        font-size: 16px;
        font-weight: bold;
        color: #333;
        margin-bottom: 8px;
      }
      
      .service-desc {
        font-size: 14px;
        color: #666;
      }
    }
    
    .booking-info {
      background: #fff;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 30px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      
      .info-item {
        display: flex;
        justify-content: space-between;
        margin-bottom: 15px;
        
        &:last-child {
          margin-bottom: 0;
        }
        
        .label {
          color: #666;
          font-size: 14px;
        }
        
        .value {
          color: #333;
          font-size: 14px;
          font-weight: 500;
        }
      }
    }
    
    .actions {
      display: flex;
      gap: 15px;
      
      .btn-cancel,
      .btn-confirm {
        flex: 1;
        height: 44px;
        border-radius: 22px;
        font-size: 16px;
        border: none;
      }
      
      .btn-cancel {
        background: #f5f5f5;
        color: #666;
      }
      
      .btn-confirm {
        background: #07c160;
        color: #fff;
      }
    }
  }
}
</style>
