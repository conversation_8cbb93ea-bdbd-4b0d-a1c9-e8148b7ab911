<template>
  <view class="history-page">
    <view class="header">
      <text class="title">预约历史</text>
    </view>
    
    <view class="filter-tabs">
      <view 
        v-for="(tab, index) in tabs" 
        :key="index"
        class="tab-item"
        :class="{ active: activeTab === index }"
        @tap="switchTab(index)"
      >
        <text>{{ tab.name }}</text>
      </view>
    </view>
    
    <scroll-view class="booking-list" scroll-y>
      <view 
        v-for="booking in filteredBookings" 
        :key="booking.id"
        class="booking-item"
        @tap="viewDetail(booking)"
      >
        <view class="booking-header">
          <text class="service-name">{{ booking.serviceName }}</text>
          <text class="status" :class="booking.status">{{ getStatusText(booking.status) }}</text>
        </view>
        
        <view class="booking-info">
          <view class="info-row">
            <text class="label">预约时间：</text>
            <text class="value">{{ booking.appointmentTime }}</text>
          </view>
          <view class="info-row">
            <text class="label">技师：</text>
            <text class="value">{{ booking.therapistName }}</text>
          </view>
          <view class="info-row">
            <text class="label">订单号：</text>
            <text class="value">{{ booking.orderNo }}</text>
          </view>
        </view>
        
        <view class="booking-actions" v-if="booking.status === 'pending'">
          <button class="btn-cancel" @tap.stop="cancelBooking(booking)">取消预约</button>
          <button class="btn-modify" @tap.stop="modifyBooking(booking)">修改预约</button>
        </view>
      </view>
      
      <view v-if="filteredBookings.length === 0" class="empty-state">
        <text class="empty-text">暂无预约记录</text>
      </view>
    </scroll-view>
  </view>
</template>

<script>
import { defineComponent, ref, computed } from 'vue'
import Taro from '@tarojs/taro'

export default defineComponent({
  name: 'BookingHistory',
  
  setup() {
    const activeTab = ref(0)
    
    const tabs = ref([
      { name: '全部', status: 'all' },
      { name: '待服务', status: 'pending' },
      { name: '已完成', status: 'completed' },
      { name: '已取消', status: 'cancelled' }
    ])
    
    const bookings = ref([
      {
        id: 1,
        serviceName: '中医推拿',
        status: 'pending',
        appointmentTime: '2025-07-06 14:00',
        therapistName: '张医师',
        orderNo: 'YXT123456'
      },
      {
        id: 2,
        serviceName: '针灸治疗',
        status: 'completed',
        appointmentTime: '2025-07-05 10:00',
        therapistName: '李医师',
        orderNo: 'YXT123455'
      }
    ])
    
    const filteredBookings = computed(() => {
      const currentTab = tabs.value[activeTab.value]
      if (currentTab.status === 'all') {
        return bookings.value
      }
      return bookings.value.filter(booking => booking.status === currentTab.status)
    })
    
    const switchTab = (index) => {
      activeTab.value = index
    }
    
    const getStatusText = (status) => {
      const statusMap = {
        pending: '待服务',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusMap[status] || status
    }
    
    const viewDetail = (booking) => {
      Taro.showToast({
        title: '查看详情',
        icon: 'none'
      })
    }
    
    const cancelBooking = (booking) => {
      Taro.showModal({
        title: '确认取消',
        content: '确定要取消这个预约吗？',
        success: (res) => {
          if (res.confirm) {
            booking.status = 'cancelled'
            Taro.showToast({
              title: '取消成功',
              icon: 'success'
            })
          }
        }
      })
    }
    
    const modifyBooking = (booking) => {
      Taro.showToast({
        title: '修改预约',
        icon: 'none'
      })
    }
    
    return {
      activeTab,
      tabs,
      bookings,
      filteredBookings,
      switchTab,
      getStatusText,
      viewDetail,
      cancelBooking,
      modifyBooking
    }
  }
})
</script>

<style lang="scss">
.history-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  
  .header {
    padding: 20px;
    text-align: center;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    
    .title {
      font-size: 18px;
      font-weight: bold;
      color: #333;
    }
  }
  
  .filter-tabs {
    display: flex;
    background: #fff;
    border-bottom: 1px solid #f0f0f0;
    
    .tab-item {
      flex: 1;
      padding: 15px 0;
      text-align: center;
      font-size: 14px;
      color: #666;
      position: relative;
      
      &.active {
        color: #07c160;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 30px;
          height: 2px;
          background: #07c160;
        }
      }
    }
  }
  
  .booking-list {
    flex: 1;
    padding: 10px;
    
    .booking-item {
      background: #fff;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 10px;
      box-shadow: 0 2px 8px rgba(0,0,0,0.1);
      
      .booking-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 10px;
        
        .service-name {
          font-size: 16px;
          font-weight: bold;
          color: #333;
        }
        
        .status {
          font-size: 12px;
          padding: 4px 8px;
          border-radius: 4px;
          
          &.pending {
            background: #fff7e6;
            color: #fa8c16;
          }
          
          &.completed {
            background: #f6ffed;
            color: #52c41a;
          }
          
          &.cancelled {
            background: #fff2f0;
            color: #ff4d4f;
          }
        }
      }
      
      .booking-info {
        margin-bottom: 15px;
        
        .info-row {
          display: flex;
          margin-bottom: 5px;
          
          .label {
            color: #666;
            font-size: 14px;
            width: 80px;
          }
          
          .value {
            color: #333;
            font-size: 14px;
            flex: 1;
          }
        }
      }
      
      .booking-actions {
        display: flex;
        gap: 10px;
        
        .btn-cancel,
        .btn-modify {
          flex: 1;
          height: 32px;
          border-radius: 16px;
          font-size: 14px;
          border: none;
        }
        
        .btn-cancel {
          background: #f5f5f5;
          color: #666;
        }
        
        .btn-modify {
          background: #07c160;
          color: #fff;
        }
      }
    }
    
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      
      .empty-text {
        color: #999;
        font-size: 14px;
      }
    }
  }
}
</style>
