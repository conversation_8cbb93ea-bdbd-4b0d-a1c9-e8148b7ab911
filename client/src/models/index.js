/**
 * 数据模型定义
 * 用于定义各个数据集合的结构和默认值
 */

/**
 * 客户模型
 */
export const CustomerModel = {
  // 模型名称
  name: 'customers',
  // 模型结构
  struct: {
    // 微信OpenID
    openid: '',
    // 昵称
    nickname: '',
    // 头像
    avatar: '',
    // 性别 0-未知 1-男 2-女
    gender: 0,
    // 手机号
    phone: '',
    // 出生日期
    birth: '',
    // 创建时间
    created_at: new Date(),
    // 更新时间
    updated_at: new Date(),
    // 最后登录时间
    last_login: new Date(),
    // 状态 1-正常 0-禁用
    status: 1,
    // 健康档案
    health_record: {
      // 过敏史
      allergies: [],
      // 既往病史
      medical_history: [],
      // 特殊情况
      special_conditions: [],
      // 备注
      notes: ''
    }
  }
}

/**
 * 员工/技师模型
 */
export const EmployeeModel = {
  // 模型名称
  name: 'employees',
  // 模型结构
  struct: {
    // 姓名
    name: '',
    // 性别 1-男 2-女
    gender: 1,
    // 手机号
    phone: '',
    // 头像
    avatar: '',
    // 职位
    position: '',
    // 专长
    specialties: [],
    // 介绍
    introduction: '',
    // 工作年限
    work_years: 0,
    // 评分
    rating: 5.0,
    // 评价数量
    rating_count: 0,
    // 等级 1-初级 2-中级 3-高级 4-专家
    level: 1,
    // 是否推荐
    is_recommend: false,
    // 状态 1-在职 0-离职
    status: 1,
    // 排序
    sort_order: 0,
    // 创建时间
    created_at: new Date(),
    // 更新时间
    updated_at: new Date()
  }
}

/**
 * 服务项目模型
 */
export const ServiceModel = {
  // 模型名称
  name: 'services',
  // 模型结构
  struct: {
    // 服务名称
    name: '',
    // 服务分类
    category: '',
    // 服务价格
    price: 0,
    // 服务时长(分钟)
    duration: 60,
    // 服务图片
    image: '',
    // 服务简介
    description: '',
    // 服务详情
    detail: '',
    // 是否推荐
    is_recommend: false,
    // 服务状态 1-上线 0-下线
    status: 1,
    // 排序
    sort_order: 0,
    // 创建时间
    created_at: new Date(),
    // 更新时间
    updated_at: new Date()
  }
}

/**
 * 预约模型
 */
export const AppointmentModel = {
  // 模型名称
  name: 'appointments',
  // 模型结构
  struct: {
    // 客户ID
    customer_id: '',
    // 客户OpenID
    customer_openid: '',
    // 客户姓名
    customer_name: '',
    // 客户手机
    customer_phone: '',
    // 技师ID
    employee_id: '',
    // 技师OpenID
    employee_openid: '',
    // 技师姓名
    employee_name: '',
    // 服务ID
    service_id: '',
    // 服务名称
    service_name: '',
    // 服务价格
    service_price: 0,
    // 预约日期
    date: '',
    // 开始时间
    start_time: '',
    // 结束时间
    end_time: '',
    // 备注
    notes: '',
    // 预约状态 待确认|已确认|已完成|已取消|已爽约
    status: '待确认',
    // 支付状态 未支付|已支付|已退款
    payment_status: '未支付',
    // 支付方式 微信支付|现场支付
    payment_method: '',
    // 支付时间
    payment_time: null,
    // 创建时间
    created_at: new Date(),
    // 更新时间
    updated_at: new Date()
  }
}

/**
 * Banner模型
 */
export const BannerModel = {
  // 模型名称
  name: 'banners',
  // 模型结构
  struct: {
    // 标题
    title: '',
    // 图片
    image: '',
    // 链接类型 page|url
    link_type: 'page',
    // 链接地址
    link_url: '',
    // 排序
    sort_order: 0,
    // 状态 1-启用 0-禁用
    status: 1,
    // 创建时间
    created_at: new Date(),
    // 更新时间
    updated_at: new Date()
  }
}

/**
 * 公告模型
 */
export const NoticeModel = {
  // 模型名称
  name: 'notices',
  // 模型结构
  struct: {
    // 标题
    title: '',
    // 内容
    content: '',
    // 是否重要
    is_important: false,
    // 排序
    sort_order: 0,
    // 状态 1-启用 0-禁用
    status: 1,
    // 创建时间
    created_at: new Date(),
    // 更新时间
    updated_at: new Date()
  }
}

// 导出所有模型
export default {
  CustomerModel,
  EmployeeModel,
  ServiceModel,
  AppointmentModel,
  BannerModel,
  NoticeModel
} 