<template>
  <view class="profile">
    <!-- 用户信息头部 -->
    <view class="user-header">
      <view class="user-info">
        <image :src="userInfo.avatar || defaultAvatar" class="user-avatar" mode="aspectFill" />
        <view class="user-details">
          <text class="user-name">{{ userInfo.username || '未登录' }}</text>
          <text class="user-phone">{{ userInfo.phone || '请绑定手机号' }}</text>
          <view class="user-vip" v-if="userInfo.is_vip">
            <text class="vip-badge">VIP会员</text>
            <text class="vip-expire">有效期至：{{ formatDate(userInfo.vip_expire_date) }}</text>
          </view>
        </view>
      </view>
      <view class="user-actions">
        <button class="edit-btn" @tap="navigateToEdit">编辑资料</button>
      </view>
    </view>

    <!-- 统计信息 -->
    <view class="stats-section">
      <view class="stats-item" @tap="navigateToMyServices">
        <text class="stats-number">{{ stats.total_appointments }}</text>
        <text class="stats-label">总预约</text>
      </view>
      <view class="stats-item" @tap="navigateToMyServices('completed')">
        <text class="stats-number">{{ stats.completed_appointments }}</text>
        <text class="stats-label">已完成</text>
      </view>
      <view class="stats-item" @tap="navigateToMyServices('pending')">
        <text class="stats-number">{{ stats.pending_appointments }}</text>
        <text class="stats-label">待确认</text>
      </view>
      <view class="stats-item">
        <text class="stats-number">{{ stats.total_spent }}</text>
        <text class="stats-label">总消费</text>
      </view>
    </view>

    <!-- 功能菜单 -->
    <view class="menu-section">
      <view class="menu-group">
        <view class="menu-item" @tap="navigateToMyServices">
          <view class="menu-icon">
            <text class="iconfont icon-appointment"></text>
          </view>
          <text class="menu-title">我的预约</text>
          <text class="menu-arrow">></text>
        </view>
        <view class="menu-item" @tap="navigateToHealthRecord">
          <view class="menu-icon">
            <text class="iconfont icon-health"></text>
          </view>
          <text class="menu-title">健康档案</text>
          <text class="menu-arrow">></text>
        </view>
        <view class="menu-item" @tap="navigateToMemberCenter">
          <view class="menu-icon">
            <text class="iconfont icon-vip"></text>
          </view>
          <text class="menu-title">会员中心</text>
          <view class="menu-badge" v-if="userInfo.is_vip">VIP</view>
          <text class="menu-arrow">></text>
        </view>
      </view>

      <view class="menu-group">
        <view class="menu-item" @tap="navigateToAddressBook">
          <view class="menu-icon">
            <text class="iconfont icon-address"></text>
          </view>
          <text class="menu-title">地址管理</text>
          <text class="menu-arrow">></text>
        </view>
        <view class="menu-item" @tap="navigateToFavorites">
          <view class="menu-icon">
            <text class="iconfont icon-favorite"></text>
          </view>
          <text class="menu-title">我的收藏</text>
          <text class="menu-arrow">></text>
        </view>
        <view class="menu-item" @tap="navigateToInvite">
          <view class="menu-icon">
            <text class="iconfont icon-invite"></text>
          </view>
          <text class="menu-title">邀请好友</text>
          <text class="menu-arrow">></text>
        </view>
      </view>

      <view class="menu-group">
        <view class="menu-item" @tap="navigateToSettings">
          <view class="menu-icon">
            <text class="iconfont icon-settings"></text>
          </view>
          <text class="menu-title">设置</text>
          <text class="menu-arrow">></text>
        </view>
        <view class="menu-item" @tap="navigateToHelp">
          <view class="menu-icon">
            <text class="iconfont icon-help"></text>
          </view>
          <text class="menu-title">帮助与反馈</text>
          <text class="menu-arrow">></text>
        </view>
        <view class="menu-item" @tap="navigateToAbout">
          <view class="menu-icon">
            <text class="iconfont icon-about"></text>
          </view>
          <text class="menu-title">关于我们</text>
          <text class="menu-arrow">></text>
        </view>
      </view>
    </view>

    <!-- 退出登录 -->
    <view class="logout-section" v-if="userInfo.username">
      <button class="logout-btn" @tap="handleLogout">退出登录</button>
    </view>
  </view>
</template>

<script>
import Taro, { onShow } from '@tarojs/taro';
import { onMounted, ref } from 'vue';

export default {
  name: 'Profile',
  setup() {
    // 数据
    const userInfo = ref({});
    const stats = ref({
      total_appointments: 0,
      completed_appointments: 0,
      pending_appointments: 0,
      total_spent: 0
    });
    
    const defaultAvatar = '/assets/images/default-avatar.png';

    // 生命周期
    onMounted(() => {
      Taro.setNavigationBarTitle({
        title: '个人中心'
      });
    });

    onShow(() => {
      loadUserInfo();
      loadUserStats();
    });

    // 方法
    const loadUserInfo = async () => {
      try {
        // 模拟数据，实际应该从API获取
        const mockUserInfo = {
          id: '1',
          username: '张三',
          phone: '138****8888',
          avatar: 'https://example.com/avatar.jpg',
          is_vip: true,
          vip_expire_date: '2025-12-31',
          email: '<EMAIL>'
        };
        
        userInfo.value = mockUserInfo;
      } catch (error) {
        console.error('加载用户信息失败', error);
      }
    };

    const loadUserStats = async () => {
      try {
        // 模拟数据
        const mockStats = {
          total_appointments: 15,
          completed_appointments: 12,
          pending_appointments: 2,
          total_spent: 2980
        };
        
        stats.value = mockStats;
      } catch (error) {
        console.error('加载用户统计失败', error);
      }
    };

    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}.${(date.getMonth() + 1).toString().padStart(2, '0')}.${date.getDate().toString().padStart(2, '0')}`;
    };

    // 导航方法
    const navigateToEdit = () => {
      Taro.navigateTo({
        url: '/packageProfile/pages/edit/index'
      });
    };

    const navigateToMyServices = (status = '') => {
      const url = status ? `/pages/my-services/index?status=${status}` : '/pages/my-services/index';
      Taro.switchTab({
        url: '/pages/my-services/index'
      });
    };

    const navigateToHealthRecord = () => {
      Taro.navigateTo({
        url: '/packageProfile/pages/health-record/index'
      });
    };

    const navigateToMemberCenter = () => {
      Taro.navigateTo({
        url: '/packageProfile/pages/member/index'
      });
    };

    const navigateToAddressBook = () => {
      Taro.navigateTo({
        url: '/packageProfile/pages/address/index'
      });
    };

    const navigateToFavorites = () => {
      Taro.navigateTo({
        url: '/packageProfile/pages/favorites/index'
      });
    };

    const navigateToInvite = () => {
      Taro.navigateTo({
        url: '/packageProfile/pages/invite/index'
      });
    };

    const navigateToSettings = () => {
      Taro.navigateTo({
        url: '/packageProfile/pages/settings/index'
      });
    };

    const navigateToHelp = () => {
      Taro.navigateTo({
        url: '/packageProfile/pages/help/index'
      });
    };

    const navigateToAbout = () => {
      Taro.navigateTo({
        url: '/packageProfile/pages/about/index'
      });
    };

    const handleLogout = async () => {
      try {
        const result = await Taro.showModal({
          title: '确认退出',
          content: '确定要退出登录吗？',
          confirmText: '退出',
          cancelText: '取消'
        });

        if (!result.confirm) return;

        Taro.showLoading({ title: '退出中...' });
        
        // 这里应该调用实际的退出登录API
        await new Promise(resolve => setTimeout(resolve, 1000));
        
        // 清除本地存储的用户信息
        Taro.removeStorageSync('userInfo');
        Taro.removeStorageSync('token');
        
        // 重置用户信息
        userInfo.value = {};
        
        Taro.hideLoading();
        Taro.showToast({
          title: '已退出登录',
          icon: 'success'
        });
        
        // 跳转到登录页面
        setTimeout(() => {
          Taro.navigateTo({
            url: '/pages/auth/login/index'
          });
        }, 1500);
        
      } catch (error) {
        console.error('退出登录失败', error);
        Taro.hideLoading();
        Taro.showToast({
          title: '退出失败',
          icon: 'none'
        });
      }
    };

    return {
      userInfo,
      stats,
      defaultAvatar,
      formatDate,
      navigateToEdit,
      navigateToMyServices,
      navigateToHealthRecord,
      navigateToMemberCenter,
      navigateToAddressBook,
      navigateToFavorites,
      navigateToInvite,
      navigateToSettings,
      navigateToHelp,
      navigateToAbout,
      handleLogout
    };
  }
};
</script>

<style lang="scss">
.profile {
  background-color: #f5f5f5;
  min-height: 100vh;

  .user-header {
    background: linear-gradient(135deg, #07c160 0%, #05a050 100%);
    padding: 40px 20px 30px;
    color: #fff;

    .user-info {
      display: flex;
      align-items: center;
      margin-bottom: 20px;

      .user-avatar {
        width: 120px;
        height: 120px;
        border-radius: 60px;
        margin-right: 20px;
        border: 3px solid rgba(255, 255, 255, 0.3);
      }

      .user-details {
        flex: 1;

        .user-name {
          font-size: 36px;
          font-weight: bold;
          margin-bottom: 10px;
          display: block;
        }

        .user-phone {
          font-size: 28px;
          opacity: 0.8;
          margin-bottom: 15px;
          display: block;
        }

        .user-vip {
          .vip-badge {
            background-color: #ffd700;
            color: #333;
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 22px;
            margin-right: 10px;
          }

          .vip-expire {
            font-size: 24px;
            opacity: 0.8;
          }
        }
      }
    }

    .user-actions {
      text-align: right;

      .edit-btn {
        background-color: rgba(255, 255, 255, 0.2);
        color: #fff;
        border: 1px solid rgba(255, 255, 255, 0.3);
        padding: 15px 30px;
        border-radius: 25px;
        font-size: 26px;
      }
    }
  }

  .stats-section {
    display: flex;
    background-color: #fff;
    margin: 20px;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

    .stats-item {
      flex: 1;
      text-align: center;
      padding: 30px 10px;
      border-right: 1px solid #f0f0f0;

      &:last-child {
        border-right: none;
      }

      .stats-number {
        font-size: 36px;
        font-weight: bold;
        color: #07c160;
        display: block;
        margin-bottom: 8px;
      }

      .stats-label {
        font-size: 26px;
        color: #666;
      }
    }
  }

  .menu-section {
    padding: 0 20px;

    .menu-group {
      background-color: #fff;
      border-radius: 12px;
      margin-bottom: 20px;
      overflow: hidden;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);

      .menu-item {
        display: flex;
        align-items: center;
        padding: 30px 20px;
        border-bottom: 1px solid #f0f0f0;
        position: relative;

        &:last-child {
          border-bottom: none;
        }

        .menu-icon {
          width: 60px;
          height: 60px;
          background-color: #f0f9f0;
          border-radius: 30px;
          display: flex;
          align-items: center;
          justify-content: center;
          margin-right: 20px;

          .iconfont {
            font-size: 32px;
            color: #07c160;
          }
        }

        .menu-title {
          flex: 1;
          font-size: 30px;
          color: #333;
        }

        .menu-badge {
          background-color: #ff4757;
          color: #fff;
          padding: 4px 8px;
          border-radius: 10px;
          font-size: 20px;
          margin-right: 10px;
        }

        .menu-arrow {
          font-size: 28px;
          color: #ccc;
        }
      }
    }
  }

  .logout-section {
    padding: 20px;

    .logout-btn {
      width: 100%;
      background-color: #fff;
      color: #f56c6c;
      border: 1px solid #f56c6c;
      font-size: 32px;
      padding: 25px;
      border-radius: 12px;
      text-align: center;
    }
  }
}

// 图标字体样式（如果使用iconfont）
.iconfont {
  font-family: "iconfont" !important;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
