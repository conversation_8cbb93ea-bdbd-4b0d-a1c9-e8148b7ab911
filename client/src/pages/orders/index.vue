<template>
  <view class="orders-page">
    <!-- 状态标签 -->
    <view class="status-tabs">
      <view 
        v-for="(tab, index) in statusTabs" 
        :key="index" 
        class="status-tab" 
        :class="{ active: activeStatus === tab.value }" 
        @tap="setActiveStatus(tab.value)"
      >
        {{ tab.label }}
        <view class="tab-badge" v-if="tab.count > 0">{{ tab.count }}</view>
      </view>
    </view>

    <!-- 订单列表 -->
    <view class="order-list" v-if="filteredOrders.length > 0">
      <view 
        class="order-item" 
        v-for="(order, index) in filteredOrders" 
        :key="index"
        @tap="goToDetail(order.id)"
      >
        <view class="order-header">
          <view class="order-info">
            <text class="order-number">订单号：{{ order.order_number }}</text>
            <text class="order-date">{{ formatDate(order.created_at) }}</text>
          </view>
          <view class="order-status" :class="getStatusClass(order.status)">
            {{ getStatusText(order.status) }}
          </view>
        </view>

        <view class="order-content">
          <image class="service-image" :src="order.service.image" mode="aspectFill" />
          <view class="service-info">
            <view class="service-name">{{ order.service.name }}</view>
            <view class="service-details">
              <text class="therapist-name">技师：{{ order.therapist.name }}</text>
              <text class="service-time">时间：{{ formatDateTime(order.appointment_time) }}</text>
              <text class="service-duration">时长：{{ order.service.duration }}分钟</text>
            </view>
            <view class="service-price">¥{{ order.total_amount }}</view>
          </view>
        </view>

        <view class="order-actions">
          <button 
            v-if="order.status === 'pending'" 
            class="action-btn cancel-btn" 
            @tap.stop="cancelOrder(order.id)"
          >
            取消订单
          </button>
          <button 
            v-if="order.status === 'pending'" 
            class="action-btn modify-btn" 
            @tap.stop="modifyOrder(order.id)"
          >
            修改预约
          </button>
          <button 
            v-if="order.status === 'completed'" 
            class="action-btn review-btn" 
            @tap.stop="goToReview(order.id)"
          >
            评价
          </button>
          <button 
            v-if="order.status === 'completed'" 
            class="action-btn rebooking-btn" 
            @tap.stop="rebooking(order.id)"
          >
            再次预约
          </button>
        </view>
      </view>
    </view>

    <!-- 无数据展示 -->
    <view class="empty-container" v-if="!loading && filteredOrders.length === 0">
      <image class="empty-image" src="../../assets/images/empty-orders.png" />
      <view class="empty-text">{{ getEmptyText() }}</view>
      <button class="empty-btn" @tap="goToServices">去预约服务</button>
    </view>

    <!-- 加载中 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import Taro from '@tarojs/taro'

export default {
  name: 'OrdersPage',
  setup() {
    const orders = ref([])
    const activeStatus = ref('all')
    const loading = ref(true)

    const statusTabs = ref([
      { label: '全部', value: 'all', count: 0 },
      { label: '待确认', value: 'pending', count: 0 },
      { label: '已确认', value: 'confirmed', count: 0 },
      { label: '已完成', value: 'completed', count: 0 },
      { label: '已取消', value: 'cancelled', count: 0 }
    ])

    // 筛选后的订单列表
    const filteredOrders = computed(() => {
      if (activeStatus.value === 'all') {
        return orders.value
      }
      return orders.value.filter(order => order.status === activeStatus.value)
    })

    // 初始化数据
    const initData = () => {
      // 模拟订单数据
      orders.value = [
        {
          id: '1',
          order_number: 'ORD202507050001',
          status: 'pending',
          created_at: '2025-07-05 10:30:00',
          appointment_time: '2025-07-06 14:00:00',
          total_amount: 198,
          service: {
            id: '1',
            name: '全身推拿按摩',
            duration: 60,
            image: 'https://example.com/service1.jpg'
          },
          therapist: {
            id: '1',
            name: '张医师'
          }
        },
        {
          id: '2',
          order_number: 'ORD202507040002',
          status: 'completed',
          created_at: '2025-07-04 09:15:00',
          appointment_time: '2025-07-04 16:00:00',
          total_amount: 168,
          service: {
            id: '2',
            name: '颈肩调理',
            duration: 45,
            image: 'https://example.com/service2.jpg'
          },
          therapist: {
            id: '2',
            name: '李医师'
          }
        },
        {
          id: '3',
          order_number: 'ORD202507030003',
          status: 'confirmed',
          created_at: '2025-07-03 14:20:00',
          appointment_time: '2025-07-07 10:00:00',
          total_amount: 218,
          service: {
            id: '3',
            name: '中医推拿',
            duration: 90,
            image: 'https://example.com/service3.jpg'
          },
          therapist: {
            id: '3',
            name: '王医师'
          }
        }
      ]

      // 更新状态标签计数
      updateStatusCounts()
      loading.value = false
    }

    // 更新状态标签计数
    const updateStatusCounts = () => {
      const counts = {
        all: orders.value.length,
        pending: 0,
        confirmed: 0,
        completed: 0,
        cancelled: 0
      }

      orders.value.forEach(order => {
        counts[order.status]++
      })

      statusTabs.value.forEach(tab => {
        tab.count = counts[tab.value]
      })
    }

    // 设置活动状态
    const setActiveStatus = (status) => {
      activeStatus.value = status
    }

    // 获取状态文本
    const getStatusText = (status) => {
      const statusTexts = {
        pending: '待确认',
        confirmed: '已确认',
        completed: '已完成',
        cancelled: '已取消'
      }
      return statusTexts[status] || '未知状态'
    }

    // 获取状态样式类
    const getStatusClass = (status) => {
      return `status-${status}`
    }

    // 格式化日期
    const formatDate = (dateStr) => {
      const date = new Date(dateStr)
      return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    }

    // 格式化日期时间
    const formatDateTime = (dateStr) => {
      const date = new Date(dateStr)
      return `${date.getMonth() + 1}月${date.getDate()}日 ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
    }

    // 获取空状态文本
    const getEmptyText = () => {
      const emptyTexts = {
        all: '暂无订单记录',
        pending: '暂无待确认订单',
        confirmed: '暂无已确认订单',
        completed: '暂无已完成订单',
        cancelled: '暂无已取消订单'
      }
      return emptyTexts[activeStatus.value] || '暂无数据'
    }

    // 跳转到详情页
    const goToDetail = (orderId) => {
      Taro.navigateTo({
        url: `/pages/order-detail/index?id=${orderId}`
      })
    }

    // 取消订单
    const cancelOrder = (orderId) => {
      Taro.showModal({
        title: '确认取消',
        content: '确定要取消这个订单吗？',
        success: (res) => {
          if (res.confirm) {
            // 模拟取消订单
            const order = orders.value.find(o => o.id === orderId)
            if (order) {
              order.status = 'cancelled'
              updateStatusCounts()
              Taro.showToast({
                title: '订单已取消',
                icon: 'success'
              })
            }
          }
        }
      })
    }

    // 修改预约
    const modifyOrder = (orderId) => {
      Taro.navigateTo({
        url: `/pages/booking/index?orderId=${orderId}&mode=modify`
      })
    }

    // 跳转到评价页面
    const goToReview = (orderId) => {
      Taro.navigateTo({
        url: `/pages/review/index?orderId=${orderId}`
      })
    }

    // 再次预约
    const rebooking = (orderId) => {
      const order = orders.value.find(o => o.id === orderId)
      if (order) {
        Taro.navigateTo({
          url: `/pages/booking/index?serviceId=${order.service.id}&therapistId=${order.therapist.id}`
        })
      }
    }

    // 跳转到服务页面
    const goToServices = () => {
      Taro.switchTab({
        url: '/pages/services/index'
      })
    }

    onMounted(() => {
      Taro.setNavigationBarTitle({
        title: '我的订单'
      })
      
      // 获取页面参数
      const instance = Taro.getCurrentInstance()
      const status = instance.router?.params?.status
      if (status) {
        activeStatus.value = status
      }
      
      initData()
    })

    return {
      orders,
      filteredOrders,
      statusTabs,
      activeStatus,
      loading,
      setActiveStatus,
      getStatusText,
      getStatusClass,
      formatDate,
      formatDateTime,
      getEmptyText,
      goToDetail,
      cancelOrder,
      modifyOrder,
      goToReview,
      rebooking,
      goToServices
    }
  }
}
</script>

<style lang="scss">
.orders-page {
  background-color: #f5f5f5;
  min-height: 100vh;
  
  .status-tabs {
    display: flex;
    background-color: #fff;
    padding: 0 20px;
    border-bottom: 2px solid #f0f0f0;
    
    .status-tab {
      position: relative;
      flex: 1;
      text-align: center;
      padding: 20px 0;
      font-size: 28px;
      color: #666;
      
      &.active {
        color: #07c160;
        font-weight: bold;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 60px;
          height: 4px;
          background-color: #07c160;
          border-radius: 2px;
        }
      }
      
      .tab-badge {
        position: absolute;
        top: 10px;
        right: 20px;
        background-color: #f56c6c;
        color: #fff;
        font-size: 20px;
        padding: 2px 8px;
        border-radius: 10px;
        min-width: 20px;
        text-align: center;
      }
    }
  }
  
  .order-list {
    padding: 20px;
    
    .order-item {
      background-color: #fff;
      margin-bottom: 20px;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      
      .order-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 15px;
        padding-bottom: 15px;
        border-bottom: 2px solid #f0f0f0;
        
        .order-info {
          .order-number {
            display: block;
            font-size: 26px;
            color: #333;
            margin-bottom: 5px;
          }
          
          .order-date {
            font-size: 22px;
            color: #999;
          }
        }
        
        .order-status {
          font-size: 24px;
          font-weight: bold;
          padding: 5px 12px;
          border-radius: 15px;
          
          &.status-pending {
            color: #ff9800;
            background-color: #fff3e0;
          }
          
          &.status-confirmed {
            color: #2196f3;
            background-color: #e3f2fd;
          }
          
          &.status-completed {
            color: #4caf50;
            background-color: #e8f5e8;
          }
          
          &.status-cancelled {
            color: #f44336;
            background-color: #ffebee;
          }
        }
      }
      
      .order-content {
        display: flex;
        margin-bottom: 15px;
        
        .service-image {
          width: 100px;
          height: 100px;
          border-radius: 8px;
          margin-right: 15px;
        }
        
        .service-info {
          flex: 1;
          
          .service-name {
            font-size: 30px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
          }
          
          .service-details {
            margin-bottom: 10px;
            
            .therapist-name,
            .service-time,
            .service-duration {
              display: block;
              font-size: 24px;
              color: #666;
              margin-bottom: 5px;
            }
          }
          
          .service-price {
            font-size: 28px;
            color: #f56c6c;
            font-weight: bold;
          }
        }
      }
      
      .order-actions {
        display: flex;
        justify-content: flex-end;
        gap: 15px;
        
        .action-btn {
          padding: 8px 20px;
          border-radius: 20px;
          font-size: 24px;
          border: 2px solid;
          
          &.cancel-btn {
            background-color: #fff;
            color: #f56c6c;
            border-color: #f56c6c;
          }
          
          &.modify-btn {
            background-color: #fff;
            color: #07c160;
            border-color: #07c160;
          }
          
          &.review-btn {
            background-color: #07c160;
            color: #fff;
            border-color: #07c160;
          }
          
          &.rebooking-btn {
            background-color: #fff;
            color: #2196f3;
            border-color: #2196f3;
          }
        }
      }
    }
  }
  
  .empty-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100px 20px;
    
    .empty-image {
      width: 200px;
      height: 200px;
      margin-bottom: 20px;
    }
    
    .empty-text {
      font-size: 28px;
      color: #999;
      margin-bottom: 30px;
    }
    
    .empty-btn {
      background-color: #07c160;
      color: #fff;
      font-size: 28px;
      padding: 12px 30px;
      border-radius: 25px;
      border: none;
    }
  }
  
  .loading-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 100px 20px;
    
    .loading-spinner {
      width: 60px;
      height: 60px;
      border: 4px solid #f3f3f3;
      border-top: 4px solid #07c160;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
    
    .loading-text {
      margin-top: 20px;
      font-size: 28px;
      color: #999;
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
