<template>
  <view class="therapists">
    <view class="search-bar">
      <input 
        class="search-input" 
        placeholder="搜索技师" 
        v-model="searchKeyword"
        @input="onSearch"
      />
    </view>

    <view class="filter-bar">
      <view class="filter-item">
        <picker 
          mode="selector" 
          :range="specialtyOptions" 
          range-key="name"
          @change="onSpecialtyChange"
        >
          <view class="filter-text">
            {{ selectedSpecialty ? selectedSpecialty.name : '专长筛选' }}
          </view>
        </picker>
      </view>
      <view class="filter-item">
        <picker 
          mode="selector" 
          :range="sortOptions" 
          range-key="name"
          @change="onSortChange"
        >
          <view class="filter-text">
            {{ selectedSort ? selectedSort.name : '排序方式' }}
          </view>
        </picker>
      </view>
    </view>

    <view class="therapist-list">
      <view 
        class="therapist-card" 
        v-for="therapist in filteredTherapists" 
        :key="therapist.id"
        @tap="navigateToDetail(therapist.id)"
      >
        <image :src="therapist.avatar" class="therapist-avatar" mode="aspectFill" />
        <view class="therapist-info">
          <view class="therapist-header">
            <text class="therapist-name">{{ therapist.name }}</text>
            <view class="therapist-rating">
              <text class="rating-score">{{ therapist.rating }}</text>
              <text class="rating-star">★</text>
            </view>
          </view>
          <text class="therapist-title">{{ therapist.position }}</text>
          <text class="therapist-experience">从业{{ therapist.experience_years }}年</text>
          <view class="therapist-specialties">
            <text 
              class="specialty-tag" 
              v-for="specialty in therapist.specialties.slice(0, 3)" 
              :key="specialty.id"
            >
              {{ specialty.name }}
            </text>
          </view>
          <view class="therapist-intro">{{ therapist.introduction }}</view>
          <view class="therapist-actions">
            <button 
              class="action-btn book-btn" 
              @tap.stop="navigateToBooking(therapist.id)"
            >
              立即预约
            </button>
            <button 
              class="action-btn detail-btn" 
              @tap.stop="navigateToDetail(therapist.id)"
            >
              查看详情
            </button>
          </view>
        </view>
      </view>
    </view>

    <view class="loading" v-if="loading">
      <text>加载中...</text>
    </view>

    <view class="empty" v-if="!loading && filteredTherapists.length === 0">
      <text>暂无技师信息</text>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import Taro from '@tarojs/taro';

export default {
  name: 'Therapists',
  setup() {
    // 数据
    const loading = ref(false);
    const searchKeyword = ref('');
    const therapists = ref([]);
    const selectedSpecialty = ref(null);
    const selectedSort = ref(null);

    const specialtyOptions = ref([
      { id: 'all', name: '全部专长' },
      { id: '1', name: '推拿按摩' },
      { id: '2', name: '针灸理疗' },
      { id: '3', name: '艾灸养生' },
      { id: '4', name: '拔罐刮痧' }
    ]);

    const sortOptions = ref([
      { id: 'rating', name: '评分排序' },
      { id: 'experience', name: '经验排序' },
      { id: 'price', name: '价格排序' }
    ]);

    // 计算属性
    const filteredTherapists = computed(() => {
      let result = therapists.value;

      // 搜索过滤
      if (searchKeyword.value) {
        result = result.filter(therapist => 
          therapist.name.includes(searchKeyword.value) ||
          therapist.position.includes(searchKeyword.value)
        );
      }

      // 专长过滤
      if (selectedSpecialty.value && selectedSpecialty.value.id !== 'all') {
        result = result.filter(therapist =>
          therapist.specialties.some(specialty => 
            specialty.id === selectedSpecialty.value.id
          )
        );
      }

      // 排序
      if (selectedSort.value) {
        switch (selectedSort.value.id) {
          case 'rating':
            result.sort((a, b) => b.rating - a.rating);
            break;
          case 'experience':
            result.sort((a, b) => b.experience_years - a.experience_years);
            break;
          case 'price':
            result.sort((a, b) => a.base_price - b.base_price);
            break;
        }
      }

      return result;
    });

    // 生命周期
    onMounted(() => {
      Taro.setNavigationBarTitle({
        title: '技师团队'
      });
      loadTherapists();
    });

    // 方法
    const loadTherapists = async () => {
      try {
        loading.value = true;
        
        // 模拟数据，实际应该调用API
        const mockData = [
          {
            id: '1',
            name: '张医师',
            position: '高级技师',
            avatar: 'https://example.com/therapist1.jpg',
            rating: 4.8,
            experience_years: 8,
            introduction: '擅长颈椎调理和全身推拿，手法娴熟，深受客户好评。',
            specialties: [
              { id: '1', name: '推拿按摩' },
              { id: '2', name: '针灸理疗' }
            ],
            base_price: 198
          },
          {
            id: '2',
            name: '李医师',
            position: '资深技师',
            avatar: 'https://example.com/therapist2.jpg',
            rating: 4.9,
            experience_years: 12,
            introduction: '中医世家出身，精通各种传统理疗手法。',
            specialties: [
              { id: '3', name: '艾灸养生' },
              { id: '4', name: '拔罐刮痧' }
            ],
            base_price: 268
          }
        ];
        
        therapists.value = mockData;
        loading.value = false;
      } catch (error) {
        console.error('加载技师列表失败', error);
        loading.value = false;
        Taro.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    };

    const onSearch = () => {
      // 搜索逻辑已在计算属性中处理
    };

    const onSpecialtyChange = (e) => {
      const index = e.detail.value;
      selectedSpecialty.value = specialtyOptions.value[index];
    };

    const onSortChange = (e) => {
      const index = e.detail.value;
      selectedSort.value = sortOptions.value[index];
    };

    const navigateToDetail = (id) => {
      Taro.navigateTo({
        url: `/packageTherapists/pages/detail/index?id=${id}`
      });
    };

    const navigateToBooking = (therapistId) => {
      Taro.navigateTo({
        url: `/pages/booking/index?therapistId=${therapistId}`
      });
    };

    return {
      loading,
      searchKeyword,
      therapists,
      filteredTherapists,
      selectedSpecialty,
      selectedSort,
      specialtyOptions,
      sortOptions,
      onSearch,
      onSpecialtyChange,
      onSortChange,
      navigateToDetail,
      navigateToBooking
    };
  }
};
</script>

<style lang="scss">
.therapists {
  padding-bottom: 30px;
  
  .search-bar {
    padding: 20px;
    background-color: #f5f5f5;
    
    .search-input {
      background-color: #fff;
      padding: 20px;
      border-radius: 8px;
      font-size: 28px;
    }
  }
  
  .filter-bar {
    display: flex;
    padding: 20px;
    background-color: #fff;
    border-bottom: 1px solid #eee;
    
    .filter-item {
      flex: 1;
      margin-right: 20px;
      
      &:last-child {
        margin-right: 0;
      }
      
      .filter-text {
        padding: 15px;
        background-color: #f5f5f5;
        border-radius: 6px;
        text-align: center;
        font-size: 26px;
        color: #666;
      }
    }
  }
  
  .therapist-list {
    padding: 0 20px;
    
    .therapist-card {
      display: flex;
      background-color: #fff;
      margin-bottom: 20px;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      
      .therapist-avatar {
        width: 200px;
        height: 200px;
        flex-shrink: 0;
      }
      
      .therapist-info {
        flex: 1;
        padding: 20px;
        
        .therapist-header {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: 10px;
          
          .therapist-name {
            font-size: 32px;
            font-weight: bold;
            color: #333;
          }
          
          .therapist-rating {
            display: flex;
            align-items: center;
            
            .rating-score {
              font-size: 28px;
              color: #333;
              margin-right: 5px;
            }
            
            .rating-star {
              color: #ff9800;
              font-size: 24px;
            }
          }
        }
        
        .therapist-title {
          font-size: 26px;
          color: #666;
          margin-bottom: 8px;
          display: block;
        }
        
        .therapist-experience {
          font-size: 24px;
          color: #999;
          margin-bottom: 15px;
          display: block;
        }
        
        .therapist-specialties {
          margin-bottom: 15px;
          
          .specialty-tag {
            display: inline-block;
            padding: 6px 12px;
            background-color: #e8f5e8;
            color: #07c160;
            font-size: 22px;
            border-radius: 4px;
            margin-right: 10px;
            margin-bottom: 8px;
          }
        }
        
        .therapist-intro {
          font-size: 24px;
          color: #666;
          line-height: 1.5;
          margin-bottom: 20px;
          display: -webkit-box;
          -webkit-line-clamp: 2;
          -webkit-box-orient: vertical;
          overflow: hidden;
        }
        
        .therapist-actions {
          display: flex;
          gap: 15px;
          
          .action-btn {
            flex: 1;
            padding: 15px;
            border-radius: 6px;
            font-size: 26px;
            text-align: center;
            
            &.book-btn {
              background-color: #07c160;
              color: #fff;
            }
            
            &.detail-btn {
              background-color: #f5f5f5;
              color: #333;
            }
          }
        }
      }
    }
  }
  
  .loading, .empty {
    text-align: center;
    padding: 60px 20px;
    color: #999;
    font-size: 28px;
  }
}
</style>
