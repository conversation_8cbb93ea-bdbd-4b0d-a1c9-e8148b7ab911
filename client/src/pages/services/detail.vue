<template>
  <view class="service-detail-page">
    <!-- 服务图片 -->
    <swiper class="service-swiper" indicator-dots indicator-color="rgba(255,255,255,0.6)" indicator-active-color="#ffffff" autoplay circular>
      <swiper-item v-for="(image, index) in service.images" :key="index">
        <image class="service-image" :src="image" mode="aspectFill" />
      </swiper-item>
    </swiper>
    
    <!-- 服务基本信息 -->
    <view class="service-info-card">
      <view class="service-name">{{ service.name }}</view>
      <view class="service-brief">{{ service.brief }}</view>
      <view class="service-meta">
        <view class="service-price">¥{{ service.price }}</view>
        <view class="service-sold">已售 {{ service.sold_count }}</view>
        <view class="service-duration">{{ service.duration }}分钟</view>
      </view>
    </view>
    
    <!-- 服务技师 -->
    <view class="section-card">
      <view class="section-title">服务技师</view>
      <scroll-view class="therapist-scroll" scroll-x>
        <view class="therapist-list">
          <view 
            class="therapist-item" 
            v-for="(therapist, index) in therapists" 
            :key="index"
            @tap="goToTherapistDetail(therapist.id)"
          >
            <image class="therapist-avatar" :src="therapist.avatar" mode="aspectFill" />
            <view class="therapist-info">
              <view class="therapist-name">{{ therapist.name }}</view>
              <view class="therapist-title">{{ therapist.title }}</view>
            </view>
          </view>
        </view>
      </scroll-view>
    </view>
    
    <!-- 服务详情 -->
    <view class="section-card">
      <view class="section-title">服务详情</view>
      <view class="detail-content">
        <rich-text :nodes="service.description"></rich-text>
      </view>
    </view>
    
    <!-- 服务须知 -->
    <view class="section-card">
      <view class="section-title">服务须知</view>
      <view class="notice-list">
        <view class="notice-item">
          <view class="notice-label">预约须知</view>
          <view class="notice-text">{{ service.booking_notice }}</view>
        </view>
        <view class="notice-item">
          <view class="notice-label">取消规则</view>
          <view class="notice-text">{{ service.cancel_rule }}</view>
        </view>
        <view class="notice-item">
          <view class="notice-label">温馨提示</view>
          <view class="notice-text">{{ service.tips }}</view>
        </view>
      </view>
    </view>
    
    <!-- 用户评价 -->
    <view class="section-card">
      <view class="section-header">
        <view class="section-title">用户评价({{ service.review_count }})</view>
        <navigator url="/pages/services/reviews?id=${service.id}" class="section-more">
          <text>查看全部</text>
          <image class="section-arrow" src="../../assets/images/icons/arrow-right.png" />
        </navigator>
      </view>
      <view class="review-list">
        <view class="review-item" v-for="(review, index) in service.reviews" :key="index">
          <view class="review-header">
            <image class="review-avatar" :src="review.user.avatar" mode="aspectFill" />
            <view class="review-user">
              <view class="review-username">{{ review.user.nickname }}</view>
              <view class="review-rating">
                <image 
                  class="star-icon" 
                  v-for="i in 5" 
                  :key="i" 
                  :src="i <= review.rating ? '../../assets/images/icons/star-filled.png' : '../../assets/images/icons/star.png'" 
                />
                <text class="review-time">{{ review.created_at }}</text>
              </view>
            </view>
          </view>
          <view class="review-content">{{ review.content }}</view>
          <view class="review-images" v-if="review.images && review.images.length > 0">
            <image 
              class="review-image" 
              v-for="(image, imgIndex) in review.images" 
              :key="imgIndex" 
              :src="image" 
              mode="aspectFill"
              @tap="previewImage(review.images, imgIndex)"
            />
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作栏 -->
    <view class="bottom-bar">
      <view class="action-group">
        <button class="action-btn" @tap="toggleFavorite">
          <image class="action-icon" :src="isFavorite ? '../../assets/images/icons/heart-filled.png' : '../../assets/images/icons/heart.png'" />
          <text>收藏</text>
        </button>
        <button class="action-btn" @tap="makeCall">
          <image class="action-icon" src="../../assets/images/icons/phone.png" />
          <text>咨询</text>
        </button>
        <button class="action-btn" open-type="share">
          <image class="action-icon" src="../../assets/images/icons/share.png" />
          <text>分享</text>
        </button>
      </view>
      <view class="booking-btn" @tap="goToBooking">立即预约</view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import Taro from '@tarojs/taro'
import { serviceApi, therapistApi } from '../../api/index'

export default {
  name: 'ServiceDetailPage',
  setup() {
    const serviceId = Taro.getCurrentInstance().router.params.id
    const service = ref({})
    const therapists = ref([])
    const isFavorite = ref(false)
    const loading = ref(true)

    // 加载服务详情
    const loadServiceDetail = async () => {
      loading.value = true
      try {
        const res = await serviceApi.getServiceDetail(serviceId)
        if (res && res.success) {
          service.value = res.data
        }
      } catch (error) {
        console.error('获取服务详情失败', error)
        Taro.showToast({
          title: '获取服务详情失败',
          icon: 'none'
        })
      } finally {
        loading.value = false
      }
    }

    // 加载技师列表
    const loadTherapists = async () => {
      try {
        // 实际应该从API获取与此服务相关的技师
        const res = await therapistApi.getTherapistList({ service_id: serviceId })
        if (res && res.success) {
          therapists.value = res.data
        }
      } catch (error) {
        console.error('获取技师列表失败', error)
      }
    }

    // 切换收藏状态
    const toggleFavorite = () => {
      isFavorite.value = !isFavorite.value
      Taro.showToast({
        title: isFavorite.value ? '收藏成功' : '已取消收藏',
        icon: 'success'
      })
      // TODO: 调用API更新收藏状态
    }

    // 拨打电话
    const makeCall = () => {
      Taro.makePhoneCall({
        phoneNumber: '4001234567' // 使用实际的咨询电话
      })
    }

    // 预览图片
    const previewImage = (images, current) => {
      Taro.previewImage({
        urls: images,
        current: images[current]
      })
    }

    // 跳转到技师详情
    const goToTherapistDetail = (id) => {
      Taro.navigateTo({
        url: `/pages/therapists/detail?id=${id}`
      })
    }

    // 跳转到预约页面
    const goToBooking = () => {
      Taro.navigateTo({
        url: `/pages/booking/index?serviceId=${serviceId}`
      })
    }

    // 设置页面标题
    const setPageTitle = () => {
      if (service.value && service.value.name) {
        Taro.setNavigationBarTitle({
          title: service.value.name
        })
      }
    }

    onMounted(() => {
      loadServiceDetail()
      loadTherapists()
    })

    // 监听服务详情变化，更新页面标题
    const unwatch = computed(() => service.value).value
    
    onUnmounted(() => {
      // 清理工作
    })

    return {
      service,
      therapists,
      isFavorite,
      loading,
      toggleFavorite,
      makeCall,
      previewImage,
      goToTherapistDetail,
      goToBooking
    }
  }
}
</script>

<style lang="scss">
.service-detail-page {
  min-height: 100vh;
  background-color: var(--background-color);
  padding-bottom: 120rpx; // 为底部栏留空间
}

.service-swiper {
  width: 100%;
  height: 500rpx;
}

.service-image {
  width: 100%;
  height: 100%;
}

.service-info-card {
  background-color: #ffffff;
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.service-name {
  font-size: var(--font-size-xl);
  font-weight: 500;
  color: var(--text-color-primary);
  margin-bottom: var(--spacing-xs);
}

.service-brief {
  font-size: var(--font-size-md);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacing-md);
}

.service-meta {
  display: flex;
  align-items: center;
}

.service-price {
  font-size: var(--font-size-xl);
  font-weight: 500;
  color: #ee0a24;
  margin-right: var(--spacing-lg);
}

.service-sold {
  font-size: var(--font-size-sm);
  color: var(--text-color-tertiary);
  margin-right: var(--spacing-lg);
}

.service-duration {
  font-size: var(--font-size-sm);
  color: var(--text-color-tertiary);
}

.section-card {
  background-color: #ffffff;
  padding: var(--spacing-lg);
  margin-bottom: var(--spacing-md);
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
}

.section-title {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--text-color-primary);
  margin-bottom: var(--spacing-md);
}

.section-more {
  display: flex;
  align-items: center;
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
}

.section-arrow {
  width: 24rpx;
  height: 24rpx;
  margin-left: var(--spacing-xs);
}

.therapist-scroll {
  white-space: nowrap;
}

.therapist-list {
  display: inline-flex;
}

.therapist-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-right: var(--spacing-xl);
  width: 160rpx;
}

.therapist-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  margin-bottom: var(--spacing-xs);
}

.therapist-info {
  text-align: center;
}

.therapist-name {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-color-primary);
  margin-bottom: 2rpx;
}

.therapist-title {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.detail-content {
  font-size: var(--font-size-md);
  color: var(--text-color-secondary);
  line-height: 1.6;
}

.notice-list {
  
}

.notice-item {
  margin-bottom: var(--spacing-md);
  
  &:last-child {
    margin-bottom: 0;
  }
}

.notice-label {
  font-size: var(--font-size-md);
  font-weight: 500;
  color: var(--text-color-primary);
  margin-bottom: var(--spacing-xs);
}

.notice-text {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
  line-height: 1.6;
}

.review-list {
  
}

.review-item {
  padding: var(--spacing-md) 0;
  border-bottom: 1rpx solid var(--border-color-light);
  
  &:last-child {
    border-bottom: none;
    padding-bottom: 0;
  }
}

.review-header {
  display: flex;
  align-items: center;
  margin-bottom: var(--spacing-sm);
}

.review-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: var(--spacing-md);
}

.review-user {
  flex: 1;
}

.review-username {
  font-size: var(--font-size-md);
  color: var(--text-color-primary);
  margin-bottom: 4rpx;
}

.review-rating {
  display: flex;
  align-items: center;
}

.star-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 2rpx;
}

.review-time {
  margin-left: var(--spacing-sm);
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.review-content {
  font-size: var(--font-size-md);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacing-sm);
  line-height: 1.5;
}

.review-images {
  display: flex;
  flex-wrap: wrap;
}

.review-image {
  width: 150rpx;
  height: 150rpx;
  margin-right: var(--spacing-sm);
  margin-bottom: var(--spacing-sm);
  border-radius: var(--border-radius-sm);
}

.bottom-bar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 100rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
  padding: 0 var(--spacing-md);
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.action-group {
  display: flex;
  align-items: center;
  flex: 1;
}

.action-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-md);
  background-color: transparent;
  line-height: normal;
  font-size: var(--font-size-xs);
  color: var(--text-color-secondary);
  
  &::after {
    border: none;
  }
}

.action-icon {
  width: 48rpx;
  height: 48rpx;
  margin-bottom: 4rpx;
}

.booking-btn {
  height: 80rpx;
  line-height: 80rpx;
  padding: 0 var(--spacing-xl);
  background-color: var(--primary-color);
  color: #ffffff;
  font-size: var(--font-size-lg);
  border-radius: var(--border-radius-round);
  text-align: center;
}
</style> 