<template>
  <view class="services-page">
    <!-- 搜索框 -->
    <view class="search-bar">
      <view class="search-input">
        <image class="search-icon" src="../../assets/images/icons/search.png" />
        <input class="input" type="text" placeholder="搜索服务项目" v-model="searchText" confirm-type="search" @confirm="handleSearch" />
        <image v-if="searchText" class="clear-icon" src="../../assets/images/icons/close.png" @tap="clearSearch" />
      </view>
    </view>

    <!-- 分类标签 -->
    <scroll-view class="category-scroll" scroll-x>
      <view class="category-list">
        <view 
          v-for="(category, index) in categories" 
          :key="index" 
          class="category-item" 
          :class="{ active: activeCategory === category.id }" 
          @tap="setCategory(category.id)"
        >
          {{ category.name }}
        </view>
      </view>
    </scroll-view>

    <!-- 服务列表 -->
    <view class="service-list" v-if="filteredServices.length > 0">
      <view 
        class="service-item" 
        v-for="(service, index) in filteredServices" 
        :key="index"
        @tap="goToDetail(service.id)"
      >
        <image class="service-image" :src="service.image" mode="aspectFill" />
        <view class="service-content">
          <view class="service-name">{{ service.name }}</view>
          <view class="service-desc ellipsis-2">{{ service.description }}</view>
          <view class="service-meta">
            <view class="service-price">¥{{ service.price }}</view>
            <view class="service-sold">已售 {{ service.sold_count }}</view>
          </view>
          <button class="book-btn" @tap.stop="goToBooking(service.id)">立即预约</button>
        </view>
      </view>
    </view>

    <!-- 无数据展示 -->
    <view class="empty-container" v-if="!loading && filteredServices.length === 0">
      <image class="empty-image" src="../../assets/images/empty.png" />
      <view class="empty-text">暂无相关服务</view>
    </view>

    <!-- 加载中 -->
    <view class="loading-container" v-if="loading">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import Taro from '@tarojs/taro'
import { serviceApi } from '../../api/index'

export default {
  name: 'ServicesPage',
  setup() {
    const services = ref([])
    const categories = ref([])
    const activeCategory = ref('all')
    const searchText = ref('')
    const loading = ref(true)

    // 筛选后的服务列表
    const filteredServices = computed(() => {
      let result = services.value
      
      // 根据分类筛选
      if (activeCategory.value !== 'all') {
        result = result.filter(item => item.category_id === activeCategory.value)
      }
      
      // 根据搜索词筛选
      if (searchText.value.trim()) {
        const keyword = searchText.value.trim().toLowerCase()
        result = result.filter(item => 
          item.name.toLowerCase().includes(keyword) || 
          item.description.toLowerCase().includes(keyword)
        )
      }
      
      return result
    })

    // 加载服务列表
    const loadServices = async () => {
      loading.value = true
      try {
        const res = await serviceApi.getServiceList()
        if (res && res.success) {
          services.value = res.data
        }
      } catch (error) {
        console.error('获取服务列表失败', error)
        Taro.showToast({
          title: '获取服务列表失败',
          icon: 'none'
        })
      } finally {
        loading.value = false
      }
    }

    // 加载分类
    const loadCategories = async () => {
      try {
        // 模拟从API获取分类
        // 实际项目中应该通过API获取分类
        categories.value = [
          { id: 'all', name: '全部' },
          { id: '1', name: '推拿按摩' },
          { id: '2', name: '颈肩调理' },
          { id: '3', name: '腰背调理' },
          { id: '4', name: '足疗保健' },
          { id: '5', name: '中医理疗' }
        ]
      } catch (error) {
        console.error('获取分类失败', error)
      }
    }

    // 设置当前分类
    const setCategory = (categoryId) => {
      activeCategory.value = categoryId
    }

    // 处理搜索
    const handleSearch = () => {
      // 可以在这里添加额外的搜索逻辑
      console.log('搜索:', searchText.value)
    }

    // 清除搜索
    const clearSearch = () => {
      searchText.value = ''
    }

    // 跳转到详情页
    const goToDetail = (id) => {
      Taro.navigateTo({
        url: `/pages/services/detail?id=${id}`
      })
    }

    // 跳转到预约页面
    const goToBooking = (serviceId) => {
      Taro.navigateTo({
        url: `/pages/booking/index?serviceId=${serviceId}`
      })
    }

    onMounted(() => {
      loadCategories()
      loadServices()
    })

    return {
      services,
      categories,
      activeCategory,
      searchText,
      loading,
      filteredServices,
      setCategory,
      handleSearch,
      clearSearch,
      goToDetail,
      goToBooking
    }
  }
}
</script>

<style lang="scss">
.services-page {
  min-height: 100vh;
  background-color: var(--background-color);
}

.search-bar {
  background-color: #ffffff;
  padding: var(--spacing-md) var(--spacing-lg);
  position: sticky;
  top: 0;
  z-index: 10;
}

.search-input {
  position: relative;
  display: flex;
  align-items: center;
  background-color: var(--background-color-light);
  border-radius: var(--border-radius-round);
  padding: 0 var(--spacing-md);
  height: 72rpx;
}

.search-icon {
  width: 36rpx;
  height: 36rpx;
  margin-right: var(--spacing-sm);
}

.clear-icon {
  width: 32rpx;
  height: 32rpx;
}

.input {
  flex: 1;
  height: 72rpx;
  font-size: var(--font-size-md);
}

.category-scroll {
  background-color: #ffffff;
  padding: var(--spacing-sm) 0;
  white-space: nowrap;
  position: sticky;
  top: 72rpx;
  z-index: 10;
  border-bottom: 1rpx solid var(--border-color);
}

.category-list {
  display: inline-flex;
  padding: 0 var(--spacing-lg);
}

.category-item {
  padding: var(--spacing-xs) var(--spacing-lg);
  margin-right: var(--spacing-sm);
  border-radius: var(--border-radius-round);
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
  background-color: var(--background-color-light);
  
  &.active {
    color: #ffffff;
    background-color: var(--primary-color);
  }
}

.service-list {
  padding: var(--spacing-md) var(--spacing-lg);
}

.service-item {
  display: flex;
  background-color: #ffffff;
  border-radius: var(--border-radius-md);
  overflow: hidden;
  margin-bottom: var(--spacing-md);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.service-image {
  width: 220rpx;
  height: 220rpx;
  flex-shrink: 0;
}

.service-content {
  flex: 1;
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  position: relative;
}

.service-name {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: var(--text-color-primary);
  margin-bottom: var(--spacing-xs);
}

.service-desc {
  font-size: var(--font-size-sm);
  color: var(--text-color-secondary);
  margin-bottom: var(--spacing-sm);
  height: 72rpx;
}

.service-meta {
  display: flex;
  justify-content: space-between;
  margin-top: auto;
  align-items: center;
}

.service-price {
  font-size: var(--font-size-lg);
  font-weight: 500;
  color: #ee0a24;
}

.service-sold {
  font-size: var(--font-size-xs);
  color: var(--text-color-tertiary);
}

.book-btn {
  position: absolute;
  bottom: var(--spacing-md);
  right: var(--spacing-md);
  background-color: var(--primary-color);
  color: #ffffff;
  font-size: var(--font-size-sm);
  padding: 6rpx 24rpx;
  border-radius: var(--border-radius-md);
  line-height: 1.5;
}

.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: var(--spacing-md);
}

.empty-text {
  color: var(--text-color-secondary);
  font-size: var(--font-size-md);
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 4rpx solid var(--border-color);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

.loading-text {
  margin-top: var(--spacing-md);
  color: var(--text-color-secondary);
  font-size: var(--font-size-sm);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 