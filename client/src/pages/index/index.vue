<template>
  <view class="index">
    <view class="banner">
      <swiper
        class="swiper"
        indicator-dots
        autoplay
        circular
        indicator-active-color="#ffffff"
      >
        <swiper-item v-for="(banner, index) in banners" :key="index">
          <view class="banner-item">
            <image :src="banner.image" class="banner-image" mode="aspectFill" />
            <view class="banner-overlay">
              <view class="banner-content">
                <text class="banner-title">{{ banner.title }}</text>
                <text class="banner-subtitle">{{ banner.subtitle }}</text>
              </view>
            </view>
          </view>
        </swiper-item>
      </swiper>
    </view>

    <view class="notice-bar" v-if="notice">
      <view class="notice-icon">
        <text class="iconfont icon-notice"></text>
      </view>
      <view class="notice-content">
        <text>{{ notice.title }}</text>
      </view>
    </view>

    <view class="service-section">
      <view class="section-title">
        <text>推荐服务</text>
        <navigator url="/pages/services/index" class="more">更多</navigator>
      </view>
      <view class="service-list">
        <view
          class="service-item"
          v-for="service in services"
          :key="service.id"
          @tap="navigateToDetail(service.id)"
        >
          <image :src="service.image" class="service-image" mode="aspectFill" />
          <view class="service-info">
            <text class="service-name">{{ service.name }}</text>
            <text class="service-description">{{ service.description }}</text>
            <text class="service-price">{{ service.price }}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="therapist-section">
      <view class="section-title">
        <text>推荐技师</text>
        <navigator url="/pages/therapists/index" class="more">更多</navigator>
      </view>
      <view class="therapist-list">
        <view
          class="therapist-item"
          v-for="therapist in therapists"
          :key="therapist.id"
          @tap="navigateToTherapist(therapist.id)"
        >
          <image :src="therapist.avatar" class="therapist-avatar" mode="aspectFill" />
          <view class="therapist-info">
            <text class="therapist-name">{{ therapist.name }}</text>
            <text class="therapist-specialty">{{ therapist.specialty }}</text>
            <text class="therapist-experience">{{ therapist.experience }}</text>
            <view class="therapist-level">{{ therapist.level }}星技师</view>
          </view>
        </view>
      </view>
    </view>

    <view class="health-tips-section">
      <view class="section-title">
        <text>健康小贴士</text>
        <navigator url="/pages/health-tips/index" class="more">更多</navigator>
      </view>
      <view class="health-tips-list">
        <view
          class="health-tip-item"
          v-for="tip in healthTips"
          :key="tip.id"
          @tap="navigateToHealthTip(tip.id)"
        >
          <image :src="tip.image" class="tip-image" mode="aspectFill" />
          <view class="tip-info">
            <text class="tip-category">{{ tip.category }}</text>
            <text class="tip-title">{{ tip.title }}</text>
            <text class="tip-summary">{{ tip.summary }}</text>
          </view>
        </view>
      </view>
    </view>

    <view class="quick-action">
      <button class="action-button" @tap="navigateToBooking">立即预约</button>
    </view>
  </view>
</template>

<script>
import Taro from '@tarojs/taro';
import { onMounted, ref } from 'vue';
// import { serviceApi, employeeApi, healthTipApi } from '../../api';

export default {
  name: 'Index',
  setup() {
    // 响应式数据
    const banners = ref([
      {
        image: '/assets/images/placeholder.svg',
        title: '专业中医推拿',
        subtitle: '传承千年中医精髓'
      },
      {
        image: '/assets/images/placeholder.svg',
        title: '足疗保健养生',
        subtitle: '舒缓疲劳，焕发活力'
      },
      {
        image: '/assets/images/placeholder.svg',
        title: '艾灸理疗调理',
        subtitle: '温经通络，祛湿驱寒'
      }
    ]);
    const notice = ref({ title: '欢迎来到壹心堂中医推拿，新用户首次体验享受8折优惠！' });
    const services = ref([]);
    const therapists = ref([]);
    const healthTips = ref([]);
    const loading = ref(false);

    // 生命周期
    onMounted(() => {
      Taro.setNavigationBarTitle({
        title: '壹心堂中医推拿'
      });

      // 加载数据
      loadData();
    });

    // 加载首页数据 - 使用模拟数据
    const loadData = async () => {
      if (loading.value) return;

      loading.value = true;

      try {
        // 模拟API延迟
        await new Promise(resolve => setTimeout(resolve, 500));

        // 使用本地图片资源
        services.value = [
          {
            id: 1,
            name: '中式按摩',
            price: 198,
            image: '/assets/images/placeholder.svg',
            description: '传统中式按摩手法，舒缓肌肉疲劳'
          },
          {
            id: 2,
            name: '足疗保健',
            price: 168,
            image: '/assets/images/placeholder.svg',
            description: '专业足底按摩，促进血液循环'
          },
          {
            id: 3,
            name: '拔罐刮痧',
            price: 138,
            image: '/assets/images/placeholder.svg',
            description: '传统拔罐刮痧，排毒养颜'
          },
          {
            id: 4,
            name: '艾灸理疗',
            price: 128,
            image: '/assets/images/placeholder.svg',
            description: '温经通络，调理体质'
          }
        ];

        therapists.value = [
          {
            id: 1,
            name: '张师傅',
            level: 5,
            avatar: '/assets/images/placeholder.svg',
            specialty: '中式按摩专家',
            experience: '15年经验'
          },
          {
            id: 2,
            name: '李师傅',
            level: 4,
            avatar: '/assets/images/placeholder.svg',
            specialty: '足疗保健师',
            experience: '12年经验'
          },
          {
            id: 3,
            name: '王师傅',
            level: 5,
            avatar: '/assets/images/placeholder.svg',
            specialty: '艾灸理疗师',
            experience: '18年经验'
          }
        ];

        healthTips.value = [
          {
            id: 1,
            title: '如何保护颈椎健康',
            summary: '现代人长时间使用电子设备，颈椎问题日益突出。正确的坐姿和定期按摩可以有效缓解颈椎压力...',
            image: '/assets/images/placeholder.svg',
            category: '颈椎保健'
          },
          {
            id: 2,
            title: '中医艾灸的好处',
            summary: '艾灸是中医传统疗法，具有温经通络、驱寒除湿的功效。适合体质虚寒、免疫力低下的人群...',
            image: '/assets/images/placeholder.svg',
            category: '中医养生'
          },
          {
            id: 3,
            title: '足底按摩的养生原理',
            summary: '足底有众多穴位，通过专业按摩可以刺激相应器官，促进血液循环，提高身体免疫力...',
            image: '/assets/images/placeholder.svg',
            category: '足疗养生'
          }
        ];

      } catch (error) {
        console.error('加载首页数据失败:', error);
        Taro.showToast({
          title: '加载失败，请稍后重试',
          icon: 'none'
        });
      } finally {
        loading.value = false;
      }
    };

    const navigateToDetail = (id) => {
      Taro.navigateTo({
        url: `/pages/services/detail?id=${id}`
      });
    };

    const navigateToTherapist = (id) => {
      Taro.navigateTo({
        url: `/pages/therapists/detail?id=${id}`
      });
    };

    const navigateToHealthTip = (id) => {
      Taro.navigateTo({
        url: `/pages/health-tips/detail?id=${id}`
      });
    };

    const navigateToBooking = () => {
      Taro.navigateTo({
        url: '/pages/booking/index'
      });
    };

    return {
      banners,
      notice,
      services,
      therapists,
      healthTips,
      navigateToDetail,
      navigateToTherapist,
      navigateToHealthTip,
      navigateToBooking
    };
  }
};
</script>

<style lang="scss">
.index {
  padding-bottom: 30px;
  
  .banner {
    width: 100%;
    height: 350px;

    .swiper {
      width: 100%;
      height: 100%;

      .banner-item {
        position: relative;
        width: 100%;
        height: 100%;

        .banner-image {
          width: 100%;
          height: 100%;
          filter: brightness(0.7);
        }

        .banner-overlay {
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          display: flex;
          align-items: center;
          justify-content: center;
          background: linear-gradient(45deg, rgba(139, 69, 19, 0.3), rgba(160, 82, 45, 0.3));

          .banner-content {
            text-align: center;
            color: white;

            .banner-title {
              display: block;
              font-size: 40px;
              font-weight: bold;
              margin-bottom: 10px;
              text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.5);
            }

            .banner-subtitle {
              display: block;
              font-size: 28px;
              opacity: 0.9;
              text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.5);
            }
          }
        }
      }
    }
  }
  
  .notice-bar {
    display: flex;
    align-items: center;
    padding: 20px;
    background-color: #fff8e1;
    margin: 20px;
    border-radius: 8px;
    
    .notice-icon {
      margin-right: 10px;
      color: #ff9800;
    }
    
    .notice-content {
      font-size: 28px;
      color: #333;
    }
  }
  
  .section-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 30px 20px 20px;
    font-size: 32px;
    font-weight: bold;
    
    .more {
      font-size: 28px;
      color: #666;
      font-weight: normal;
    }
  }
  
  .service-section {
    margin-bottom: 30px;
    
    .service-list {
      display: flex;
      flex-wrap: wrap;
      padding: 0 10px;
      
      .service-item {
        width: 48%;
        margin: 0 1% 20px;
        background: linear-gradient(135deg, #fff 0%, #faf8f3 100%);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
        border: 1px solid rgba(160, 82, 45, 0.1);
        transition: all 0.3s ease;
        
        .service-image {
          width: 100%;
          height: 200px;
        }
        
        .service-info {
          padding: 20px;

          .service-name {
            font-size: 30px;
            color: #8b4513;
            margin-bottom: 8px;
            display: block;
            font-weight: bold;
          }

          .service-description {
            font-size: 24px;
            color: #666;
            margin-bottom: 12px;
            line-height: 1.4;
          }

          .service-price {
            font-size: 32px;
            color: #d2691e;
            font-weight: bold;

            &::before {
              content: '¥';
              font-size: 24px;
            }
          }
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(139, 69, 19, 0.15);
        }
      }
    }
  }
  
  .therapist-section {
    margin-bottom: 30px;
    
    .therapist-list {
      display: flex;
      overflow-x: auto;
      padding: 0 20px;
      
      .therapist-item {
        flex-shrink: 0;
        width: 220px;
        margin-right: 20px;
        background: linear-gradient(135deg, #fff 0%, #faf8f3 100%);
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
        border: 1px solid rgba(160, 82, 45, 0.1);
        transition: all 0.3s ease;
        
        .therapist-avatar {
          width: 220px;
          height: 200px;
          border-radius: 12px 12px 0 0;
        }

        .therapist-info {
          padding: 18px;

          .therapist-name {
            font-size: 28px;
            color: #8b4513;
            margin-bottom: 8px;
            display: block;
            font-weight: bold;
          }

          .therapist-specialty {
            font-size: 22px;
            color: #666;
            margin-bottom: 6px;
            display: block;
          }

          .therapist-experience {
            font-size: 20px;
            color: #999;
            margin-bottom: 8px;
            display: block;
          }

          .therapist-level {
            color: #d2691e;
            font-size: 24px;
            font-weight: bold;

            &::before {
              content: '⭐ ';
            }
          }
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(139, 69, 19, 0.15);
        }
      }
    }
  }
  
  .health-tips-section {
    margin-bottom: 30px;
    
    .health-tips-list {
      padding: 0 20px;
      
      .health-tip-item {
        display: flex;
        background: linear-gradient(135deg, #fff 0%, #faf8f3 100%);
        margin-bottom: 20px;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 15px rgba(139, 69, 19, 0.1);
        border: 1px solid rgba(160, 82, 45, 0.1);
        transition: all 0.3s ease;
        
        .tip-image {
          width: 200px;
          height: 150px;
        }
        
        .tip-info {
          flex: 1;
          padding: 20px;

          .tip-category {
            display: inline-block;
            background: linear-gradient(45deg, #d2691e, #cd853f);
            color: white;
            font-size: 20px;
            padding: 4px 12px;
            border-radius: 12px;
            margin-bottom: 10px;
          }

          .tip-title {
            font-size: 28px;
            color: #8b4513;
            margin-bottom: 10px;
            display: block;
            font-weight: bold;
            line-height: 1.3;
          }

          .tip-summary {
            font-size: 24px;
            color: #666;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
            line-height: 1.4;
          }
        }

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 6px 20px rgba(139, 69, 19, 0.15);
        }
      }
    }
  }
  
  .quick-action {
    padding: 20px;

    .action-button {
      background: linear-gradient(45deg, #d2691e, #cd853f);
      color: #fff;
      font-size: 32px;
      font-weight: bold;
      padding: 25px;
      border-radius: 25px;
      box-shadow: 0 4px 15px rgba(210, 105, 30, 0.3);
      transition: all 0.3s ease;
      border: none;

      &:hover {
        transform: translateY(-2px);
        box-shadow: 0 6px 20px rgba(210, 105, 30, 0.4);
      }

      &:active {
        transform: translateY(0);
      }
      text-align: center;
    }
  }
}
</style> 