<template>
  <view class="redirect-page">
    <view class="redirect-container">
      <image class="logo" src="../../assets/images/logo.png" mode="aspectFit" />
      <view class="title">壹心堂中医推拿</view>
      
      <view class="tip-box">
        <view class="tip-title">请在微信小程序中打开</view>
        <view class="tip-desc">为了获得最佳体验，请使用微信扫描下方二维码</view>
      </view>
      
      <image class="qrcode" src="../../assets/images/miniapp-qrcode.png" mode="aspectFit" />
      
      <view class="guide">
        <view class="guide-step">
          <view class="step-num">1</view>
          <view class="step-text">点击右上角</view>
          <view class="step-icon">···</view>
        </view>
        <view class="guide-step">
          <view class="step-num">2</view>
          <view class="step-text">选择"在浏览器打开"</view>
        </view>
        <view class="guide-step">
          <view class="step-num">3</view>
          <view class="step-text">点击右上角"..."，选择"在微信中打开"</view>
        </view>
      </view>
      
      <view v-if="isWechatBrowser" class="wx-open-box">
        <wx-open-launch-weapp
          id="launch-btn"
          :username="wxUsername"
          :path="wxPath"
          @error="handleLaunchError"
        >
          <button class="launch-btn">打开小程序</button>
        </wx-open-launch-weapp>
      </view>
    </view>
  </view>
</template>

<script>
import { ref, onMounted } from 'vue'
import Taro from '@tarojs/taro'
import { isWechatBrowser } from '../../utils/env-check'

export default {
  name: 'RedirectPage',
  setup() {
    const wxUsername = ref('gh_xxxxxxxxxx') // 微信小程序原始ID
    const wxPath = ref('pages/index/index')
    const isWechatBrowser = ref(false)
    
    // 处理跳转错误
    const handleLaunchError = (e) => {
      console.error('启动小程序失败', e.detail)
    }
    
    onMounted(() => {
      // 检查是否在微信浏览器中
      isWechatBrowser.value = isWechatBrowser()
      
      // 获取URL参数
      const params = Taro.getCurrentInstance().router.params
      if (params.path) {
        wxPath.value = params.path
      }
    })
    
    return {
      wxUsername,
      wxPath,
      isWechatBrowser,
      handleLaunchError
    }
  }
}
</script>

<style lang="scss">
.redirect-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40rpx;
  background-color: #f8f8f8;
}

.redirect-container {
  width: 100%;
  max-width: 600rpx;
  background-color: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  padding: 40rpx;
  text-align: center;
}

.logo {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 20rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 40rpx;
}

.tip-box {
  background-color: #f2f9f2;
  padding: 30rpx;
  border-radius: 16rpx;
  margin-bottom: 40rpx;
}

.tip-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #07c160;
  margin-bottom: 10rpx;
}

.tip-desc {
  font-size: 28rpx;
  color: #666;
}

.qrcode {
  width: 300rpx;
  height: 300rpx;
  margin: 40rpx auto;
}

.guide {
  margin-top: 40rpx;
  margin-bottom: 40rpx;
  text-align: left;
}

.guide-step {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.step-num {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  background-color: #07c160;
  color: #fff;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 20rpx;
}

.step-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.step-icon {
  font-size: 32rpx;
  font-weight: bold;
}

.wx-open-box {
  margin-top: 40rpx;
}

.launch-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  background-color: #07c160;
  color: #fff;
  font-size: 30rpx;
  border-radius: 40rpx;
  border: none;
}
</style> 