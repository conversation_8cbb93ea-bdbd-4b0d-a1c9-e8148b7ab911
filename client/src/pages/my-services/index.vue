<template>
  <view class="my-services">
    <view class="tabs">
      <view
        class="tab-item"
        :class="{ active: activeTab === tab.key }"
        v-for="tab in tabs"
        :key="tab.key"
        @tap="switchTab(tab.key)"
      >
        <text>{{ tab.label }}</text>
        <text class="tab-count" v-if="tab.count > 0">({{ tab.count }})</text>
      </view>
    </view>

    <view class="appointment-list">
      <view
        class="appointment-item"
        v-for="appointment in filteredAppointments"
        :key="appointment.id"
        @tap="navigateToDetail(appointment.id)"
      >
        <view class="appointment-header">
          <view class="appointment-info">
            <text class="appointment-no">预约编号：{{ appointment.appointment_no }}</text>
            <view class="appointment-status" :class="appointment.status">
              {{ getStatusText(appointment.status) }}
            </view>
          </view>
          <text class="appointment-date">{{ formatDate(appointment.appointment_date) }}</text>
        </view>

        <view class="appointment-content">
          <image :src="appointment.service.image" class="service-image" mode="aspectFill" />
          <view class="appointment-details">
            <text class="service-name">{{ appointment.service.name }}</text>
            <text class="therapist-name">技师：{{ appointment.therapist.name }}</text>
            <text class="appointment-time">
              时间：{{ formatDate(appointment.appointment_date) }} {{ appointment.appointment_time }}
            </text>
            <text class="appointment-duration">时长：{{ appointment.duration }}分钟</text>
            <view class="appointment-price">
              <text class="price-label">价格：</text>
              <text class="price-value">¥{{ appointment.actual_price }}</text>
            </view>
          </view>
        </view>

        <view class="appointment-actions" v-if="getAvailableActions(appointment).length > 0">
          <button
            class="action-btn"
            :class="action.type"
            v-for="action in getAvailableActions(appointment)"
            :key="action.key"
            @tap.stop="handleAction(action.key, appointment)"
          >
            {{ action.label }}
          </button>
        </view>

        <view class="appointment-notes" v-if="appointment.customer_notes">
          <text class="notes-label">备注：</text>
          <text class="notes-content">{{ appointment.customer_notes }}</text>
        </view>
      </view>
    </view>

    <view class="empty" v-if="!loading && filteredAppointments.length === 0">
      <image src="/assets/images/empty.png" class="empty-image" />
      <text class="empty-text">暂无{{ getTabLabel(activeTab) }}</text>
      <button class="empty-btn" @tap="navigateToBooking">立即预约</button>
    </view>

    <view class="loading" v-if="loading">
      <text>加载中...</text>
    </view>
  </view>
</template>

<script>
import Taro, { onShow } from '@tarojs/taro';
import { computed, onMounted, ref } from 'vue';

export default {
  name: 'MyServices',
  setup() {
    // 数据
    const loading = ref(false);
    const activeTab = ref('all');
    const appointments = ref([]);

    const tabs = ref([
      { key: 'all', label: '全部', count: 0 },
      { key: 'pending', label: '待确认', count: 0 },
      { key: 'confirmed', label: '已确认', count: 0 },
      { key: 'completed', label: '已完成', count: 0 },
      { key: 'cancelled', label: '已取消', count: 0 }
    ]);

    // 计算属性
    const filteredAppointments = computed(() => {
      if (activeTab.value === 'all') {
        return appointments.value;
      }
      return appointments.value.filter(appointment =>
        appointment.status === activeTab.value
      );
    });

    // 生命周期
    onMounted(() => {
      Taro.setNavigationBarTitle({
        title: '我的服务'
      });
    });

    onShow(() => {
      loadAppointments();
    });

    // 方法
    const loadAppointments = async () => {
      try {
        loading.value = true;

        // 模拟数据
        const mockData = [
          {
            id: '1',
            appointment_no: 'AP20241201001',
            status: 'confirmed',
            appointment_date: '2024-12-05',
            appointment_time: '14:00',
            duration: 60,
            actual_price: 198,
            customer_notes: '希望技师手法轻一些',
            service: {
              id: '1',
              name: '全身推拿',
              image: 'https://example.com/service1.jpg'
            },
            therapist: {
              id: '1',
              name: '张医师'
            }
          },
          {
            id: '2',
            appointment_no: 'AP20241201002',
            status: 'completed',
            appointment_date: '2024-11-28',
            appointment_time: '10:00',
            duration: 45,
            actual_price: 168,
            customer_notes: '',
            service: {
              id: '2',
              name: '颈椎调理',
              image: 'https://example.com/service2.jpg'
            },
            therapist: {
              id: '2',
              name: '李医师'
            }
          }
        ];

        appointments.value = mockData;
        updateTabCounts();
        loading.value = false;
      } catch (error) {
        console.error('加载预约列表失败', error);
        loading.value = false;
        Taro.showToast({
          title: '加载失败',
          icon: 'none'
        });
      }
    };

    const updateTabCounts = () => {
      tabs.value.forEach(tab => {
        if (tab.key === 'all') {
          tab.count = appointments.value.length;
        } else {
          tab.count = appointments.value.filter(appointment =>
            appointment.status === tab.key
          ).length;
        }
      });
    };

    const switchTab = (tabKey) => {
      activeTab.value = tabKey;
    };

    const getTabLabel = (tabKey) => {
      const tab = tabs.value.find(t => t.key === tabKey);
      return tab ? tab.label : '';
    };

    const getStatusText = (status) => {
      const statusMap = {
        pending: '待确认',
        confirmed: '已确认',
        in_progress: '进行中',
        completed: '已完成',
        cancelled: '已取消',
        no_show: '未到店'
      };
      return statusMap[status] || status;
    };

    const formatDate = (dateString) => {
      const date = new Date(dateString);
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${month}月${day}日`;
    };

    const getAvailableActions = (appointment) => {
      const actions = [];

      switch (appointment.status) {
        case 'pending':
        case 'confirmed':
          actions.push({ key: 'cancel', label: '取消预约', type: 'cancel' });
          break;
        case 'completed':
          actions.push({ key: 'review', label: '评价', type: 'primary' });
          actions.push({ key: 'rebook', label: '再次预约', type: 'secondary' });
          break;
        case 'cancelled':
          actions.push({ key: 'rebook', label: '重新预约', type: 'primary' });
          break;
      }

      return actions;
    };

    const handleAction = async (actionKey, appointment) => {
      switch (actionKey) {
        case 'cancel':
          await cancelAppointment(appointment);
          break;
        case 'review':
          navigateToReview(appointment.id);
          break;
        case 'rebook':
          navigateToRebook(appointment);
          break;
      }
    };

    const cancelAppointment = async (appointment) => {
      try {
        const result = await Taro.showModal({
          title: '确认取消',
          content: '确定要取消这个预约吗？',
          confirmText: '确认取消',
          cancelText: '我再想想'
        });

        if (!result.confirm) return;

        Taro.showLoading({ title: '取消中...' });

        // 这里应该调用实际的API
        await new Promise(resolve => setTimeout(resolve, 1000));

        // 更新本地数据
        const index = appointments.value.findIndex(a => a.id === appointment.id);
        if (index !== -1) {
          appointments.value[index].status = 'cancelled';
          updateTabCounts();
        }

        Taro.hideLoading();
        Taro.showToast({
          title: '取消成功',
          icon: 'success'
        });
      } catch (error) {
        console.error('取消预约失败', error);
        Taro.hideLoading();
        Taro.showToast({
          title: '取消失败',
          icon: 'none'
        });
      }
    };

    const navigateToDetail = (appointmentId) => {
      Taro.navigateTo({
        url: `/packageBooking/pages/detail/index?id=${appointmentId}`
      });
    };

    const navigateToReview = (appointmentId) => {
      Taro.navigateTo({
        url: `/packageBooking/pages/review/index?appointmentId=${appointmentId}`
      });
    };

    const navigateToRebook = (appointment) => {
      Taro.navigateTo({
        url: `/pages/booking/index?serviceId=${appointment.service.id}&therapistId=${appointment.therapist.id}`
      });
    };

    const navigateToBooking = () => {
      Taro.navigateTo({
        url: '/pages/booking/index'
      });
    };

    return {
      loading,
      activeTab,
      appointments,
      tabs,
      filteredAppointments,
      switchTab,
      getTabLabel,
      getStatusText,
      formatDate,
      getAvailableActions,
      handleAction,
      navigateToDetail,
      navigateToBooking
    };
  }
};
</script>

<style lang="scss">
.my-services {
  padding-bottom: 30px;
  
  .tabs {
    display: flex;
    background-color: #fff;
    border-bottom: 1px solid #f0f0f0;
    
    .tab-item {
      flex: 1;
      text-align: center;
      padding: 30px 10px;
      font-size: 28px;
      color: #666;
      position: relative;
      
      &.active {
        color: #07c160;
        
        &::after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 50%;
          transform: translateX(-50%);
          width: 60px;
          height: 4px;
          background-color: #07c160;
        }
      }
      
      .tab-count {
        font-size: 24px;
        margin-left: 5px;
      }
    }
  }
  
  .appointment-list {
    padding: 20px;
    
    .appointment-item {
      background-color: #fff;
      border-radius: 12px;
      margin-bottom: 20px;
      overflow: hidden;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      
      .appointment-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 20px 20px 15px;
        border-bottom: 1px solid #f0f0f0;
        
        .appointment-info {
          display: flex;
          align-items: center;
          
          .appointment-no {
            font-size: 26px;
            color: #666;
            margin-right: 15px;
          }
          
          .appointment-status {
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 22px;
            
            &.pending {
              background-color: #fff3cd;
              color: #856404;
            }
            
            &.confirmed {
              background-color: #d4edda;
              color: #155724;
            }
            
            &.completed {
              background-color: #cce5ff;
              color: #004085;
            }
            
            &.cancelled {
              background-color: #f8d7da;
              color: #721c24;
            }
          }
        }
        
        .appointment-date {
          font-size: 26px;
          color: #666;
        }
      }
      
      .appointment-content {
        display: flex;
        padding: 20px;
        
        .service-image {
          width: 120px;
          height: 120px;
          border-radius: 8px;
          margin-right: 20px;
          flex-shrink: 0;
        }
        
        .appointment-details {
          flex: 1;
          
          .service-name {
            font-size: 30px;
            color: #333;
            font-weight: bold;
            margin-bottom: 10px;
            display: block;
          }
          
          .therapist-name,
          .appointment-time,
          .appointment-duration {
            font-size: 26px;
            color: #666;
            margin-bottom: 8px;
            display: block;
          }
          
          .appointment-price {
            display: flex;
            align-items: center;
            margin-top: 10px;
            
            .price-label {
              font-size: 26px;
              color: #666;
            }
            
            .price-value {
              font-size: 32px;
              color: #f56c6c;
              font-weight: bold;
              margin-left: 5px;
            }
          }
        }
      }
      
      .appointment-actions {
        display: flex;
        gap: 15px;
        padding: 15px 20px 20px;
        
        .action-btn {
          flex: 1;
          padding: 15px;
          border-radius: 6px;
          font-size: 26px;
          text-align: center;
          
          &.primary {
            background-color: #07c160;
            color: #fff;
          }
          
          &.secondary {
            background-color: #f5f5f5;
            color: #333;
          }
          
          &.cancel {
            background-color: #f56c6c;
            color: #fff;
          }
        }
      }
      
      .appointment-notes {
        padding: 15px 20px 20px;
        background-color: #f9f9f9;
        
        .notes-label {
          font-size: 26px;
          color: #666;
        }
        
        .notes-content {
          font-size: 26px;
          color: #333;
          margin-left: 10px;
        }
      }
    }
  }
  
  .empty {
    text-align: center;
    padding: 100px 20px;
    
    .empty-image {
      width: 200px;
      height: 200px;
      margin-bottom: 30px;
    }
    
    .empty-text {
      font-size: 28px;
      color: #999;
      margin-bottom: 40px;
      display: block;
    }
    
    .empty-btn {
      background-color: #07c160;
      color: #fff;
      font-size: 28px;
      padding: 20px 40px;
      border-radius: 8px;
    }
  }
  
  .loading {
    text-align: center;
    padding: 60px 20px;
    color: #999;
    font-size: 28px;
  }
}
</style>
