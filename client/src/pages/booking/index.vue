<template>
  <view class="booking">
    <!-- 服务选择 -->
    <view class="section">
      <view class="section-title">选择服务</view>
      <view class="service-selector">
        <view 
          class="service-item" 
          :class="{ active: selectedService && selectedService.id === service.id }"
          v-for="service in services" 
          :key="service.id"
          @tap="selectService(service)"
        >
          <image :src="service.image" class="service-image" mode="aspectFill" />
          <view class="service-info">
            <text class="service-name">{{ service.name }}</text>
            <text class="service-price">¥{{ service.price }}</text>
            <text class="service-duration">{{ service.duration }}分钟</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 技师选择 -->
    <view class="section" v-if="selectedService">
      <view class="section-title">选择技师</view>
      <view class="therapist-selector">
        <view 
          class="therapist-item" 
          :class="{ active: selectedTherapist && selectedTherapist.id === therapist.id }"
          v-for="therapist in availableTherapists" 
          :key="therapist.id"
          @tap="selectTherapist(therapist)"
        >
          <image :src="therapist.avatar" class="therapist-avatar" mode="aspectFill" />
          <view class="therapist-info">
            <text class="therapist-name">{{ therapist.name }}</text>
            <text class="therapist-title">{{ therapist.position }}</text>
            <view class="therapist-rating">
              <text class="rating-score">{{ therapist.rating }}</text>
              <text class="rating-star">★</text>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 时间选择 -->
    <view class="section" v-if="selectedTherapist">
      <view class="section-title">选择时间</view>
      
      <!-- 日期选择 -->
      <view class="date-selector">
        <scroll-view class="date-scroll" scroll-x>
          <view 
            class="date-item" 
            :class="{ active: selectedDate === date.value }"
            v-for="date in availableDates" 
            :key="date.value"
            @tap="selectDate(date.value)"
          >
            <text class="date-text">{{ date.label }}</text>
            <text class="weekday-text">{{ date.weekday }}</text>
          </view>
        </scroll-view>
      </view>

      <!-- 时间段选择 -->
      <view class="time-selector" v-if="selectedDate">
        <view 
          class="time-item" 
          :class="{ 
            active: selectedTime === time.value,
            disabled: !time.available 
          }"
          v-for="time in availableTimes" 
          :key="time.value"
          @tap="selectTime(time)"
        >
          <text class="time-text">{{ time.label }}</text>
          <text class="time-status" v-if="!time.available">已约满</text>
        </view>
      </view>
    </view>

    <!-- 客户信息 -->
    <view class="section" v-if="selectedTime">
      <view class="section-title">客户信息</view>
      <view class="customer-form">
        <view class="form-item">
          <text class="form-label">姓名</text>
          <input 
            class="form-input" 
            placeholder="请输入姓名" 
            v-model="customerInfo.name"
          />
        </view>
        <view class="form-item">
          <text class="form-label">手机号</text>
          <input 
            class="form-input" 
            placeholder="请输入手机号" 
            type="number"
            v-model="customerInfo.phone"
          />
        </view>
        <view class="form-item">
          <text class="form-label">备注</text>
          <textarea 
            class="form-textarea" 
            placeholder="请输入特殊需求或备注信息" 
            v-model="customerInfo.notes"
          />
        </view>
      </view>
    </view>

    <!-- 预约信息确认 -->
    <view class="section" v-if="isFormComplete">
      <view class="section-title">预约信息确认</view>
      <view class="booking-summary">
        <view class="summary-item">
          <text class="summary-label">服务项目</text>
          <text class="summary-value">{{ selectedService.name }}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">服务技师</text>
          <text class="summary-value">{{ selectedTherapist.name }}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">预约时间</text>
          <text class="summary-value">{{ formatDateTime }}</text>
        </view>
        <view class="summary-item">
          <text class="summary-label">服务时长</text>
          <text class="summary-value">{{ selectedService.duration }}分钟</text>
        </view>
        <view class="summary-item total">
          <text class="summary-label">总价</text>
          <text class="summary-value price">¥{{ selectedService.price }}</text>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-section" v-if="isFormComplete">
      <button 
        class="submit-btn" 
        :disabled="submitting"
        @tap="submitBooking"
      >
        {{ submitting ? '提交中...' : '确认预约' }}
      </button>
    </view>
  </view>
</template>

<script>
import { ref, computed, onMounted } from 'vue';
import Taro from '@tarojs/taro';

export default {
  name: 'Booking',
  setup() {
    // 数据
    const services = ref([]);
    const availableTherapists = ref([]);
    const availableDates = ref([]);
    const availableTimes = ref([]);
    
    const selectedService = ref(null);
    const selectedTherapist = ref(null);
    const selectedDate = ref('');
    const selectedTime = ref('');
    
    const customerInfo = ref({
      name: '',
      phone: '',
      notes: ''
    });
    
    const submitting = ref(false);

    // 计算属性
    const isFormComplete = computed(() => {
      return selectedService.value && 
             selectedTherapist.value && 
             selectedDate.value && 
             selectedTime.value &&
             customerInfo.value.name &&
             customerInfo.value.phone;
    });

    const formatDateTime = computed(() => {
      if (!selectedDate.value || !selectedTime.value) return '';
      const date = availableDates.value.find(d => d.value === selectedDate.value);
      const time = availableTimes.value.find(t => t.value === selectedTime.value);
      return `${date?.label} ${time?.label}`;
    });

    // 生命周期
    onMounted(() => {
      Taro.setNavigationBarTitle({
        title: '预约服务'
      });
      loadServices();
      generateAvailableDates();
    });

    // 方法
    const loadServices = async () => {
      try {
        // 模拟数据
        const mockServices = [
          {
            id: '1',
            name: '全身推拿',
            price: 198,
            duration: 60,
            image: '/assets/images/service1.jpg'
          },
          {
            id: '2',
            name: '颈椎调理',
            price: 168,
            duration: 45,
            image: '/assets/images/service2.jpg'
          }
        ];
        services.value = mockServices;
      } catch (error) {
        console.error('加载服务列表失败', error);
      }
    };

    const selectService = async (service) => {
      selectedService.value = service;
      selectedTherapist.value = null;
      selectedDate.value = '';
      selectedTime.value = '';
      
      // 加载可提供此服务的技师
      await loadAvailableTherapists(service.id);
    };

    const loadAvailableTherapists = async (serviceId) => {
      try {
        // 模拟数据
        const mockTherapists = [
          {
            id: '1',
            name: '张医师',
            position: '高级技师',
            avatar: 'https://example.com/therapist1.jpg',
            rating: 4.8
          },
          {
            id: '2',
            name: '李医师',
            position: '资深技师',
            avatar: 'https://example.com/therapist2.jpg',
            rating: 4.9
          }
        ];
        availableTherapists.value = mockTherapists;
      } catch (error) {
        console.error('加载技师列表失败', error);
      }
    };

    const selectTherapist = (therapist) => {
      selectedTherapist.value = therapist;
      selectedDate.value = '';
      selectedTime.value = '';
    };

    const generateAvailableDates = () => {
      const dates = [];
      const today = new Date();
      
      for (let i = 0; i < 7; i++) {
        const date = new Date(today);
        date.setDate(today.getDate() + i);
        
        const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
        
        dates.push({
          value: date.toISOString().split('T')[0],
          label: i === 0 ? '今天' : i === 1 ? '明天' : `${date.getMonth() + 1}/${date.getDate()}`,
          weekday: weekdays[date.getDay()]
        });
      }
      
      availableDates.value = dates;
    };

    const selectDate = async (date) => {
      selectedDate.value = date;
      selectedTime.value = '';
      
      // 加载该日期的可用时间段
      await loadAvailableTimes(date);
    };

    const loadAvailableTimes = async (date) => {
      try {
        // 模拟数据
        const mockTimes = [
          { value: '09:00', label: '09:00', available: true },
          { value: '10:00', label: '10:00', available: true },
          { value: '11:00', label: '11:00', available: false },
          { value: '14:00', label: '14:00', available: true },
          { value: '15:00', label: '15:00', available: true },
          { value: '16:00', label: '16:00', available: true }
        ];
        availableTimes.value = mockTimes;
      } catch (error) {
        console.error('加载时间段失败', error);
      }
    };

    const selectTime = (time) => {
      if (!time.available) return;
      selectedTime.value = time.value;
    };

    const submitBooking = async () => {
      if (!isFormComplete.value) return;
      
      try {
        submitting.value = true;
        
        const bookingData = {
          service_id: selectedService.value.id,
          therapist_id: selectedTherapist.value.id,
          appointment_date: selectedDate.value,
          appointment_time: selectedTime.value,
          customer_name: customerInfo.value.name,
          customer_phone: customerInfo.value.phone,
          customer_notes: customerInfo.value.notes,
          price: selectedService.value.price
        };
        
        // 这里应该调用实际的API
        console.log('提交预约数据:', bookingData);
        
        // 模拟API调用
        await new Promise(resolve => setTimeout(resolve, 2000));
        
        Taro.showToast({
          title: '预约成功',
          icon: 'success'
        });
        
        // 跳转到预约成功页面
        setTimeout(() => {
          Taro.redirectTo({
            url: '/packageBooking/pages/success/index'
          });
        }, 1500);
        
      } catch (error) {
        console.error('提交预约失败', error);
        Taro.showToast({
          title: '预约失败，请重试',
          icon: 'none'
        });
      } finally {
        submitting.value = false;
      }
    };

    return {
      services,
      availableTherapists,
      availableDates,
      availableTimes,
      selectedService,
      selectedTherapist,
      selectedDate,
      selectedTime,
      customerInfo,
      submitting,
      isFormComplete,
      formatDateTime,
      selectService,
      selectTherapist,
      selectDate,
      selectTime,
      submitBooking
    };
  }
};
</script>

<style lang="scss">
.booking {
  padding-bottom: 120px;
  
  .section {
    margin-bottom: 30px;
    background-color: #fff;
    
    .section-title {
      padding: 30px 20px 20px;
      font-size: 32px;
      font-weight: bold;
      color: #333;
      border-bottom: 1px solid #f0f0f0;
    }
  }
  
  .service-selector {
    padding: 20px;
    
    .service-item {
      display: flex;
      align-items: center;
      padding: 20px;
      margin-bottom: 15px;
      border: 2px solid #f0f0f0;
      border-radius: 12px;
      
      &.active {
        border-color: #07c160;
        background-color: #f0f9f0;
      }
      
      .service-image {
        width: 120px;
        height: 120px;
        border-radius: 8px;
        margin-right: 20px;
      }
      
      .service-info {
        flex: 1;
        
        .service-name {
          font-size: 30px;
          color: #333;
          margin-bottom: 10px;
          display: block;
        }
        
        .service-price {
          font-size: 32px;
          color: #f56c6c;
          font-weight: bold;
          margin-right: 15px;
        }
        
        .service-duration {
          font-size: 26px;
          color: #666;
        }
      }
    }
  }
  
  .therapist-selector {
    padding: 20px;
    
    .therapist-item {
      display: flex;
      align-items: center;
      padding: 20px;
      margin-bottom: 15px;
      border: 2px solid #f0f0f0;
      border-radius: 12px;
      
      &.active {
        border-color: #07c160;
        background-color: #f0f9f0;
      }
      
      .therapist-avatar {
        width: 100px;
        height: 100px;
        border-radius: 50px;
        margin-right: 20px;
      }
      
      .therapist-info {
        flex: 1;
        
        .therapist-name {
          font-size: 30px;
          color: #333;
          margin-bottom: 8px;
          display: block;
        }
        
        .therapist-title {
          font-size: 26px;
          color: #666;
          margin-bottom: 8px;
          display: block;
        }
        
        .therapist-rating {
          display: flex;
          align-items: center;
          
          .rating-score {
            font-size: 26px;
            color: #333;
            margin-right: 5px;
          }
          
          .rating-star {
            color: #ff9800;
            font-size: 24px;
          }
        }
      }
    }
  }
  
  .date-selector {
    padding: 20px;
    
    .date-scroll {
      white-space: nowrap;
      
      .date-item {
        display: inline-block;
        text-align: center;
        padding: 20px;
        margin-right: 15px;
        border: 2px solid #f0f0f0;
        border-radius: 8px;
        min-width: 120px;
        
        &.active {
          border-color: #07c160;
          background-color: #f0f9f0;
        }
        
        .date-text {
          font-size: 28px;
          color: #333;
          display: block;
          margin-bottom: 5px;
        }
        
        .weekday-text {
          font-size: 24px;
          color: #666;
        }
      }
    }
  }
  
  .time-selector {
    display: flex;
    flex-wrap: wrap;
    padding: 20px;
    gap: 15px;
    
    .time-item {
      flex: 0 0 calc(33.333% - 10px);
      text-align: center;
      padding: 20px 10px;
      border: 2px solid #f0f0f0;
      border-radius: 8px;
      position: relative;
      
      &.active {
        border-color: #07c160;
        background-color: #f0f9f0;
      }
      
      &.disabled {
        background-color: #f5f5f5;
        color: #ccc;
      }
      
      .time-text {
        font-size: 28px;
        color: #333;
      }
      
      .time-status {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        font-size: 22px;
        color: #999;
      }
    }
  }
  
  .customer-form {
    padding: 20px;
    
    .form-item {
      margin-bottom: 30px;
      
      .form-label {
        font-size: 28px;
        color: #333;
        margin-bottom: 15px;
        display: block;
      }
      
      .form-input, .form-textarea {
        width: 100%;
        padding: 20px;
        border: 1px solid #ddd;
        border-radius: 8px;
        font-size: 28px;
        background-color: #fff;
      }
      
      .form-textarea {
        height: 120px;
        resize: none;
      }
    }
  }
  
  .booking-summary {
    padding: 20px;
    
    .summary-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 20px 0;
      border-bottom: 1px solid #f0f0f0;
      
      &.total {
        border-bottom: none;
        padding-top: 30px;
        font-weight: bold;
        
        .summary-value.price {
          font-size: 36px;
          color: #f56c6c;
        }
      }
      
      .summary-label {
        font-size: 28px;
        color: #666;
      }
      
      .summary-value {
        font-size: 28px;
        color: #333;
      }
    }
  }
  
  .submit-section {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 20px;
    background-color: #fff;
    border-top: 1px solid #f0f0f0;
    
    .submit-btn {
      width: 100%;
      background-color: #07c160;
      color: #fff;
      font-size: 32px;
      padding: 25px;
      border-radius: 8px;
      text-align: center;
      
      &:disabled {
        background-color: #ccc;
      }
    }
  }
}
</style>
