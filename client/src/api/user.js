/**
 * 用户相关API服务
 */
import Taro from '@tarojs/taro'
import { callFunction } from '../utils/cloud'
import { chooseAndUploadImage } from '../utils/storage'
import { cloud } from '../utils/cloud'

/**
 * 用户登录
 * @returns {Promise<Object>} 登录结果
 */
export async function login() {
  try {
    const result = await cloud.callFunction({
      name: 'login',
    })
    return result
  } catch (error) {
    console.error('登录失败', error)
    throw error
  }
}

/**
 * 用户注册
 * @param {Object} userInfo 用户信息
 * @returns {Promise<Object>} 注册结果
 */
export async function register(userInfo) {
  try {
    const result = await cloud.callFunction({
      name: 'register',
      data: userInfo
    })
    return result
  } catch (error) {
    console.error('注册失败', error)
    throw error
  }
}

/**
 * 获取用户信息
 * @returns {Promise<Object>} 用户信息
 */
export async function getUserInfo() {
  try {
    const userInfo = wx.getStorageSync('userInfo')
    if (userInfo) {
      return {
        code: 200,
        message: '获取用户信息成功',
        data: userInfo
      }
    } else {
      return {
        code: 401,
        message: '用户未登录',
        data: null
      }
    }
  } catch (error) {
    console.error('获取用户信息失败', error)
    throw error
  }
}

/**
 * 更新用户信息
 * @param {Object} userInfo 用户信息
 * @returns {Promise<Object>} 更新结果
 */
export async function updateUserInfo(userInfo) {
  try {
    const result = await cloud.callFunction({
      name: 'register', // 复用注册云函数
      data: userInfo
    })
    
    if (result.code === 200 && result.data.userInfo) {
      // 更新本地存储
      wx.setStorageSync('userInfo', result.data.userInfo)
    }
    
    return result
  } catch (error) {
    console.error('更新用户信息失败', error)
    throw error
  }
}

/**
 * 上传用户头像
 * @param {string} filePath 文件临时路径
 * @returns {Promise<Object>} 上传结果
 */
export async function uploadAvatar(filePath) {
  try {
    // 获取用户openid
    const { result } = await cloud.callFunction({
      name: 'login'
    })
    
    if (!result || result.code !== 200) {
      return {
        code: 401,
        message: '用户未登录',
        data: null
      }
    }
    
    // 上传文件
    const cloudPath = `user_avatars/${result.data.openid}/${Date.now()}.${filePath.match(/\.(\w+)$/)[1]}`
    const uploadResult = await cloud.uploadFile({
      cloudPath,
      filePath
    })
    
    if (!uploadResult.fileID) {
      return {
        code: 500,
        message: '上传头像失败',
        data: null
      }
    }
    
    // 更新用户头像
    const userInfo = wx.getStorageSync('userInfo') || {}
    const updateResult = await updateUserInfo({
      ...userInfo,
      avatarUrl: uploadResult.fileID
    })
    
    return {
      code: 200,
      message: '上传头像成功',
      data: {
        fileID: uploadResult.fileID,
        userInfo: updateResult.data.userInfo
      }
    }
  } catch (error) {
    console.error('上传头像失败', error)
    throw error
  }
}

/**
 * 退出登录
 * @returns {Promise<Object>} 退出结果
 */
export function logout() {
  try {
    // 清除本地存储
    wx.removeStorageSync('userInfo')
    wx.removeStorageSync('token')
    
    return {
      code: 200,
      message: '退出登录成功',
      data: null
    }
  } catch (error) {
    console.error('退出登录失败', error)
    throw error
  }
}

/**
 * 检查登录状态
 * @returns {boolean} 是否已登录
 */
export function checkLoginStatus() {
  try {
    const userInfo = wx.getStorageSync('userInfo')
    const token = wx.getStorageSync('token')
    
    return !!(userInfo && token)
  } catch (error) {
    console.error('检查登录状态失败', error)
    return false
  }
} 