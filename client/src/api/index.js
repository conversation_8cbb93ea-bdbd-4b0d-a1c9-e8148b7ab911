import { get, post, put, del } from '../utils/request'
import { callFunction } from '../utils/cloud'
import {
  usersApi as customersApi,
  therapistsApi as employeesApi,
  servicesApi,
  appointmentsApi,
  healthTipsApi,
  bannersApi,
  noticesApi,
  appointmentExtApi
} from './cloud-api'
import * as userApi from './user'

// 判断是否使用云开发API
const useCloudApi = process.env.TARO_ENV === 'weapp'

/**
 * 服务相关API
 */
export const serviceApi = {
  /**
   * 获取服务列表
   */
  getServiceList: (params) => {
    if (useCloudApi) {
      return servicesApi.list({
        where: params,
        orderField: 'sort_order',
        orderDirection: 'asc'
      })
    }
    return get('/services/', params)
  },
  
  /**
   * 获取服务详情
   */
  getServiceDetail: (id) => {
    if (useCloudApi) {
      return servicesApi.get(id)
    }
    return get(`/services/${id}/`)
  },
  
  /**
   * 获取推荐服务
   */
  getRecommendServices: (params) => {
    if (useCloudApi) {
      return servicesApi.list({
        where: { is_recommend: true },
        limit: params.limit || 4,
        orderField: 'sort_order',
        orderDirection: 'asc'
      })
    }
    return get('/services/popular/', params)
  }
}

/**
 * 技师相关API
 */
export const therapistApi = {
  /**
   * 获取技师列表
   */
  getTherapistList: (params) => {
    if (useCloudApi) {
      return employeesApi.list({
        where: { is_active: true },
        orderField: 'level',
        orderDirection: 'desc'
      })
    }
    return get('/therapists/', params)
  },
  
  /**
   * 获取技师详情
   */
  getTherapistDetail: (id) => {
    if (useCloudApi) {
      return employeesApi.get(id)
    }
    return get(`/therapists/${id}/`)
  },
  
  /**
   * 获取推荐技师
   */
  getRecommendTherapists: (params) => {
    if (useCloudApi) {
      return employeesApi.list({
        where: { is_active: true },
        limit: params.limit || 4,
        orderField: 'level',
        orderDirection: 'desc'
      })
    }
    return get('/api/employees', { ...params, recommend: true })
  }
}

/**
 * 预约相关API
 */
export const appointmentApi = {
  /**
   * 获取预约列表
   */
  getAppointmentList: (params) => {
    if (useCloudApi) {
      return appointmentsApi.list({
        where: params
      })
    }
    return get('/api/appointments', params)
  },
  
  /**
   * 获取预约详情
   */
  getAppointmentDetail: (id) => {
    if (useCloudApi) {
      return appointmentsApi.get(id)
    }
    return get(`/api/appointments/${id}`)
  },
  
  /**
   * 创建预约
   */
  createAppointment: (data) => {
    if (useCloudApi) {
      return appointmentsApi.add(data)
    }
    return post('/api/appointments', data)
  },
  
  /**
   * 取消预约
   */
  cancelAppointment: (id) => {
    if (useCloudApi) {
      return appointmentsApi.update(id, { status: '已取消' })
    }
    return put(`/api/appointments/${id}`, { status: 'cancelled' })
  },
  
  /**
   * 查询用户的预约列表
   */
  getUserAppointments: (customerId, status) => {
    if (useCloudApi) {
      return appointmentExtApi.getUserAppointments(customerId, status)
    }
    return get('/api/appointments/user', { customer_id: customerId, status })
  },
  
  /**
   * 检查时间段是否可预约
   */
  checkTimeAvailable: (employeeId, date, startTime, endTime) => {
    if (useCloudApi) {
      return appointmentExtApi.checkTimeAvailable(employeeId, date, startTime, endTime)
    }
    return get('/api/appointments/check-time', { employee_id: employeeId, date, start_time: startTime, end_time: endTime })
  }
}

/**
 * 客户相关API
 */
export const customerApi = {
  /**
   * 获取客户信息
   */
  getCustomerInfo: () => {
    if (useCloudApi) {
      return callFunction('login')
        .then(res => {
          if (res && res.code === 200 && res.data) {
            return res.data.userInfo || null
          }
          return null
        })
    }
    return get('/api/customers/profile')
  },
  
  /**
   * 更新客户信息
   */
  updateCustomerInfo: (data) => {
    if (useCloudApi) {
      return customersApi.update(data._id, data)
    }
    return put('/api/customers/profile', data)
  }
}

/**
 * 健康小贴士相关API
 */
export const healthTipApi = {
  /**
   * 获取健康小贴士列表
   */
  getHealthTips: (params) => {
    if (useCloudApi) {
      return healthTipsApi.list({
        where: params,
        orderField: 'created_at',
        orderDirection: 'desc'
      })
    }
    return get('/health-tips/', params)
  },

  /**
   * 获取健康小贴士详情
   */
  getHealthTipDetail: (id) => {
    if (useCloudApi) {
      return healthTipsApi.get(id)
    }
    return get(`/health-tips/${id}/`)
  }
}

/**
 * 首页相关API
 */
export const homeApi = {
  /**
   * 获取首页Banner
   */
  getBanners: () => {
    if (useCloudApi) {
      return bannersApi.list({
        where: { status: 1 },
        orderField: 'sort_order',
        orderDirection: 'asc'
      }).then(data => {
        return {
          success: true,
          data
        }
      })
    }
    return get('/api/banners')
  },
  
  /**
   * 获取公告
   */
  getNotices: () => {
    if (useCloudApi) {
      return noticesApi.list({
        where: { status: 1 },
        orderField: 'sort_order',
        orderDirection: 'asc'
      }).then(data => {
        return {
          success: true,
          data
        }
      })
    }
    return get('/api/notices')
  },
  
  /**
   * 获取健康知识
   */
  getHealthTips: (params) => {
    if (useCloudApi) {
      // 假设我们从云函数获取健康知识
      return callFunction('getHealthTips', params)
        .then(res => {
          if (res && res.code === 200) {
            return res.data
          }
          return []
        })
    }
    return get('/api/health-tips', params)
  }
}

/**
 * 用户相关API
 */
export const userApis = {
  /**
   * 用户登录
   */
  login: userApi.login,
  
  /**
   * 用户注册
   */
  register: userApi.register,
  
  /**
   * 上传用户头像
   */
  uploadAvatar: userApi.uploadAvatar,
  
  /**
   * 更新用户信息
   */
  updateUserInfo: userApi.updateUserInfo
}

// employeeApi别名导出
export const employeeApi = {
  getRecommendEmployees: (params) => {
    if (useCloudApi) {
      return employeesApi.list({
        where: params,
        orderField: 'level',
        orderDirection: 'desc'
      })
    }
    return get('/therapists/', params)
  }
}

// API入口文件
export default {
  service: serviceApi,
  employee: employeeApi,
  healthTip: healthTipApi,
  appointment: appointmentApi,
  user: userApi,
};