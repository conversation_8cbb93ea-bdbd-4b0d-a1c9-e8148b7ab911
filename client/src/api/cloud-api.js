/**
 * 云数据库API封装
 * 提供常用的云数据库操作方法
 */
import { getDB } from '../utils/cloud'
import { 
  CustomerModel, 
  EmployeeModel, 
  ServiceModel, 
  AppointmentModel,
  BannerModel,
  NoticeModel
} from '../models'
import { callFunction } from '../utils/cloud'
import { db } from '../utils/cloud'

// 获取数据库实例
const dbInstance = getDB()

/**
 * 通用集合操作类
 */
class CollectionAPI {
  /**
   * 构造函数
   * @param {string} collectionName 集合名称
   */
  constructor(collectionName) {
    this.collectionName = collectionName;
  }
  
  /**
   * 获取集合引用
   * @returns {object} 集合引用
   */
  collection() {
    return db.collection(this.collectionName);
  }
  
  /**
   * 获取单条记录
   * @param {string} id 记录ID
   * @returns {Promise<object>} 记录数据
   */
  async get(id) {
    try {
      const res = await this.collection().doc(id).get();
      return {
        code: 200,
        message: '获取成功',
        data: res.data
      };
    } catch (error) {
      console.error(`获取${this.collectionName}数据失败`, error);
      return {
        code: 500,
        message: '获取数据失败',
        data: null
      };
    }
  }
  
  /**
   * 获取记录列表
   * @param {object} options 查询选项
   * @param {object} options.where 查询条件
   * @param {number} options.limit 查询数量
   * @param {number} options.skip 跳过数量
   * @param {string} options.orderField 排序字段
   * @param {string} options.orderDirection 排序方向，'asc'或'desc'
   * @returns {Promise<object>} 记录列表
   */
  async list(options = {}) {
    const { where = {}, limit = 10, skip = 0, orderField, orderDirection = 'desc' } = options;
    
    try {
      let query = this.collection().where(where);
      
      // 设置排序
      if (orderField) {
        query = query.orderBy(orderField, orderDirection);
      }
      
      // 分页
      query = query.skip(skip).limit(limit);
      
      // 执行查询
      const res = await query.get();
      
      // 获取总数
      const countRes = await this.collection().where(where).count();
      
      return {
        code: 200,
        message: '获取成功',
        data: {
          list: res.data,
          pagination: {
            total: countRes.total,
            page: Math.floor(skip / limit) + 1,
            limit
          }
        }
      };
    } catch (error) {
      console.error(`获取${this.collectionName}列表失败`, error);
      return {
        code: 500,
        message: '获取数据失败',
        data: null
      };
    }
  }
  
  /**
   * 添加记录
   * @param {object} data 记录数据
   * @returns {Promise<object>} 添加结果
   */
  async add(data) {
    try {
      // 添加创建时间和更新时间
      const now = db.serverDate();
      const newData = {
        ...data,
        create_time: now,
        update_time: now
      };
      
      const res = await this.collection().add({
        data: newData
      });
      
      return {
        code: 200,
        message: '添加成功',
        data: {
          _id: res._id
        }
      };
    } catch (error) {
      console.error(`添加${this.collectionName}数据失败`, error);
      return {
        code: 500,
        message: '添加数据失败',
        data: null
      };
    }
  }
  
  /**
   * 更新记录
   * @param {string} id 记录ID
   * @param {object} data 更新数据
   * @returns {Promise<object>} 更新结果
   */
  async update(id, data) {
    try {
      // 添加更新时间
      const updateData = {
        ...data,
        update_time: db.serverDate()
      };
      
      await this.collection().doc(id).update({
        data: updateData
      });
      
      return {
        code: 200,
        message: '更新成功',
        data: null
      };
    } catch (error) {
      console.error(`更新${this.collectionName}数据失败`, error);
      return {
        code: 500,
        message: '更新数据失败',
        data: null
      };
    }
  }
  
  /**
   * 删除记录
   * @param {string} id 记录ID
   * @returns {Promise<object>} 删除结果
   */
  async remove(id) {
    try {
      await this.collection().doc(id).remove();
      
      return {
        code: 200,
        message: '删除成功',
        data: null
      };
    } catch (error) {
      console.error(`删除${this.collectionName}数据失败`, error);
      return {
        code: 500,
        message: '删除数据失败',
        data: null
      };
    }
  }
}

// 导出各个集合的API
export const usersApi = new CollectionAPI('users');
export const servicesApi = new CollectionAPI('services');
export const therapistsApi = new CollectionAPI('therapists');
export const appointmentsApi = new CollectionAPI('appointments');
export const healthTipsApi = new CollectionAPI('health_tips');
export const bannersApi = new CollectionAPI('banners');
export const noticesApi = new CollectionAPI('notices');
export const reviewsApi = new CollectionAPI('reviews');
export const userFavoritesApi = new CollectionAPI('user_favorites');

// 为servicesApi添加收藏功能
servicesApi.toggleFavorite = async function(params) {
  try {
    const { service_id, is_favorite } = params;

    // 获取当前用户ID（从本地存储或全局状态）
    const userInfo = wx.getStorageSync('userInfo') || {};
    const user_id = userInfo.id || userInfo._id;

    if (!user_id) {
      return {
        success: false,
        message: '用户未登录',
        data: null
      };
    }

    if (is_favorite) {
      // 添加收藏
      const result = await userFavoritesApi.create({
        user_id: user_id,
        service_id: service_id,
        type: 'service',
        created_at: new Date()
      });

      return {
        success: true,
        message: '收藏成功',
        data: result.data
      };
    } else {
      // 取消收藏
      const favorites = await userFavoritesApi.getList({
        user_id: user_id,
        service_id: service_id,
        type: 'service'
      });

      if (favorites.success && favorites.data.length > 0) {
        const favoriteId = favorites.data[0]._id;
        await userFavoritesApi.delete(favoriteId);
      }

      return {
        success: true,
        message: '已取消收藏',
        data: null
      };
    }
  } catch (error) {
    console.error('切换收藏状态失败:', error);
    return {
      success: false,
      message: '操作失败，请重试',
      data: null
    };
  }
};

/**
 * 预约扩展API
 */
export const appointmentExtApi = {
  /**
   * 获取用户的预约列表
   * @param {string} customerId 客户ID
   * @param {string} status 预约状态
   * @returns {Promise<Array>} 预约列表
   */
  getUserAppointments: async (customerId, status) => {
    try {
      // 调用云函数
      const result = await callFunction('getAppointments', {
        customerId,
        status
      })
      
      if (result && result.code === 200) {
        return result.data
      }
      
      console.error('获取用户预约列表失败', result)
      return []
    } catch (error) {
      console.error('获取用户预约列表失败', error)
      return []
    }
  },
  
  /**
   * 检查时间段是否可预约
   * @param {string} employeeId 技师ID
   * @param {string} date 日期
   * @param {string} startTime 开始时间
   * @param {string} endTime 结束时间
   * @returns {Promise<boolean>} 是否可预约
   */
  checkTimeAvailable: async (employeeId, date, startTime, endTime) => {
    try {
      // 调用云函数
      const result = await callFunction('checkTimeAvailable', {
        employeeId,
        date,
        startTime,
        endTime
      })
      
      if (result && result.code === 200) {
        return result.data.available
      }
      
      console.error('检查时间段失败', result)
      return false
    } catch (error) {
      console.error('检查时间段失败', error)
      return false
    }
  }
} 