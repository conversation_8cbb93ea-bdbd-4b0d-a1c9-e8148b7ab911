export default {
  pages: [
    'pages/index/index',
    'pages/services/index',
    'pages/therapists/index',
    'pages/booking/index',
    'pages/my-services/index',
    'pages/profile/index',
    'pages/redirect/index'
  ],
  window: {
    backgroundTextStyle: 'light',
    navigationBarBackgroundColor: '#07c160',
    navigationBarTitleText: '壹心堂中医推拿',
    navigationBarTextStyle: 'white'
  },
  tabBar: {
    color: '#7b7b7b',
    selectedColor: '#07c160',
    backgroundColor: '#fff',
    borderStyle: 'white',
    list: [
      {
        pagePath: 'pages/index/index',
        text: '首页',
        iconPath: 'assets/images/tabbar/home.png',
        selectedIconPath: 'assets/images/tabbar/home-active.png'
      },
      {
        pagePath: 'pages/services/index',
        text: '服务',
        iconPath: 'assets/images/tabbar/service.png',
        selectedIconPath: 'assets/images/tabbar/service-active.png'
      },
      {
        pagePath: 'pages/booking/index',
        text: '预约',
        iconPath: 'assets/images/tabbar/booking.png',
        selectedIconPath: 'assets/images/tabbar/booking-active.png'
      },
      {
        pagePath: 'pages/profile/index',
        text: '我的',
        iconPath: 'assets/images/tabbar/profile.png',
        selectedIconPath: 'assets/images/tabbar/profile-active.png'
      }
    ]
  },
  subpackages: [
    {
      root: 'packageBooking',
      name: 'booking',
      pages: [
        'pages/confirm/index',
        'pages/success/index',
        'pages/history/index'
      ]
    }
  ],
  preloadRule: {
    'pages/index/index': {
      network: 'all',
      packages: ['booking']
    },
    'pages/booking/index': {
      network: 'wifi',
      packages: ['booking']
    }
  },
  usingComponents: {}
} 