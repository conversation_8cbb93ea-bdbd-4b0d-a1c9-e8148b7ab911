/**
 * 云开发工具类
 * 提供统一的云函数和云数据库调用接口
 */
import Taro from '@tarojs/taro'

// 云环境ID
const CLOUD_ENV = 'cloud1-9gtxemp4512e0880'

/**
 * 调用云函数
 * @param {string} name - 云函数名称
 * @param {object} data - 参数
 * @param {boolean} showLoading - 是否显示加载中提示
 * @returns {Promise<any>} 云函数返回结果
 */
export async function callFunction(name, data = {}, showLoading = true) {
  try {
    if (showLoading) {
      Taro.showLoading({ title: '加载中' })
    }
    
    const result = await Taro.cloud.callFunction({
      name,
      data
    })
    
    return result.result
  } catch (error) {
    console.error(`调用云函数${name}失败`, error)
    Taro.showToast({
      title: '服务异常，请稍后再试',
      icon: 'none'
    })
    return null
  } finally {
    if (showLoading) {
      Taro.hideLoading()
    }
  }
}

/**
 * 获取云数据库实例
 * @returns {object} 云数据库实例
 */
export function getDB() {
  return Taro.cloud.database()
}

/**
 * 上传文件到云存储
 * @param {string} cloudPath - 云端路径
 * @param {string} filePath - 本地文件路径
 * @returns {Promise<object>} 上传结果
 */
export async function uploadFile(cloudPath, filePath) {
  try {
    Taro.showLoading({ title: '上传中' })
    
    const result = await Taro.cloud.uploadFile({
      cloudPath,
      filePath
    })
    
    return {
      fileID: result.fileID,
      url: result.fileID
    }
  } catch (error) {
    console.error('上传文件失败', error)
    Taro.showToast({
      title: '文件上传失败',
      icon: 'none'
    })
    return null
  } finally {
    Taro.hideLoading()
  }
}

/**
 * 获取临时文件链接
 * @param {string|string[]} fileList - 文件ID或ID数组
 * @returns {Promise<object|object[]>} 文件临时链接
 */
export async function getTempFileURL(fileList) {
  if (!fileList) return null
  
  const files = Array.isArray(fileList) ? fileList : [fileList]
  
  try {
    const result = await Taro.cloud.getTempFileURL({
      fileList: files
    })
    
    if (Array.isArray(fileList)) {
      return result.fileList
    } else {
      return result.fileList[0]
    }
  } catch (error) {
    console.error('获取文件链接失败', error)
    return null
  }
}

/**
 * 用户登录，获取OpenID
 * @returns {Promise<object>} 登录结果
 */
export async function login() {
  return callFunction('login')
}

/**
 * 初始化云开发
 */
export function initCloud() {
  if (!Taro.cloud) {
    console.error('请使用 2.2.3 或以上的基础库以使用云能力')
    return false
  }
  
  Taro.cloud.init({
    env: CLOUD_ENV,
    traceUser: true
  })
  
  return true
}

/**
 * 云数据库操作
 */
export const db = {
  /**
   * 获取集合
   * @param {string} collection 集合名称
   * @returns {object} 集合对象
   */
  collection(collection) {
    return Taro.cloud.database().collection(collection)
  },
  
  /**
   * 获取数据库操作符
   * @returns {object} 数据库操作符
   */
  get command() {
    return Taro.cloud.database().command
  },
  
  /**
   * 获取服务器时间
   * @returns {object} 服务器时间
   */
  get serverDate() {
    return Taro.cloud.database().serverDate()
  },
  
  /**
   * 获取数据库实例
   * @returns {object} 数据库实例
   */
  get instance() {
    return Taro.cloud.database()
  }
}

/**
 * 云存储操作
 */
export const storage = {
  /**
   * 上传文件
   * @param {string} cloudPath 云存储路径
   * @param {string} filePath 文件临时路径
   * @returns {Promise<object>} 上传结果
   */
  uploadFile(cloudPath, filePath) {
    return new Promise((resolve, reject) => {
      Taro.cloud.uploadFile({
        cloudPath,
        filePath,
        success: res => {
          resolve(res)
        },
        fail: err => {
          console.error('[云存储] 上传文件失败', err)
          reject(err)
        }
      })
    })
  },
  
  /**
   * 获取文件临时链接
   * @param {string} fileID 文件ID
   * @param {number} maxAge 链接有效期，单位：秒，默认1800秒（30分钟）
   * @returns {Promise<string>} 文件临时链接
   */
  getTempFileURL(fileID, maxAge = 1800) {
    return new Promise((resolve, reject) => {
      Taro.cloud.getTempFileURL({
        fileList: [fileID],
        success: res => {
          if (res.fileList && res.fileList[0] && res.fileList[0].tempFileURL) {
            resolve(res.fileList[0].tempFileURL)
          } else {
            reject(new Error('获取临时链接失败'))
          }
        },
        fail: err => {
          console.error('[云存储] 获取临时链接失败', err)
          reject(err)
        }
      })
    })
  },
  
  /**
   * 删除文件
   * @param {string|Array<string>} fileIDs 文件ID或文件ID数组
   * @returns {Promise<object>} 删除结果
   */
  deleteFile(fileIDs) {
    const fileList = Array.isArray(fileIDs) ? fileIDs : [fileIDs]
    
    return new Promise((resolve, reject) => {
      Taro.cloud.deleteFile({
        fileList,
        success: res => {
          resolve(res)
        },
        fail: err => {
          console.error('[云存储] 删除文件失败', err)
          reject(err)
        }
      })
    })
  },
  
  /**
   * 下载文件
   * @param {string} fileID 文件ID
   * @returns {Promise<string>} 文件临时路径
   */
  downloadFile(fileID) {
    return new Promise((resolve, reject) => {
      Taro.cloud.downloadFile({
        fileID,
        success: res => {
          if (res.tempFilePath) {
            resolve(res.tempFilePath)
          } else {
            reject(new Error('下载文件失败'))
          }
        },
        fail: err => {
          console.error('[云存储] 下载文件失败', err)
          reject(err)
        }
      })
    })
  }
}

/**
 * 云开发实例
 */
export const cloud = Taro.cloud 