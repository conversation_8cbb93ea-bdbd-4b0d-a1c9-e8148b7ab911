/**
 * 环境检测工具
 * 用于检测当前应用运行环境，并提供强制跳转到微信小程序的功能
 */
import Taro from '@tarojs/taro'

/**
 * 检测当前是否在微信小程序环境中
 * @returns {boolean} 是否在微信小程序中
 */
export function isWechatMiniProgram() {
  // 检测当前环境
  // process.env.TARO_ENV 在编译时由Taro注入，表示当前编译的目标平台
  const taroEnv = process.env.TARO_ENV
  if (taroEnv === 'weapp') {
    return true
  }
  
  // 运行时检测
  if (Taro.getEnv() === Taro.ENV_TYPE.WEAPP) {
    return true
  }
  
  return false
}

/**
 * 检测是否是微信浏览器
 * @returns {boolean} 是否在微信浏览器中
 */
export function isWechatBrowser() {
  if (typeof window === 'undefined' || !window.navigator) {
    return false
  }
  
  const ua = window.navigator.userAgent.toLowerCase()
  return ua.indexOf('micromessenger') !== -1
}

/**
 * 获取当前完整URL
 * @returns {string} 当前URL
 */
export function getCurrentUrl() {
  if (typeof window === 'undefined' || !window.location) {
    return ''
  }
  
  return window.location.href
}

/**
 * 跳转到微信小程序
 * @param {string} path 小程序内页面路径
 * @param {object} query 查询参数
 * @returns {Promise<void>}
 */
export async function redirectToMiniProgram(path = 'pages/index/index', query = {}) {
  if (isWechatBrowser()) {
    // 在微信浏览器中，使用微信开放标签跳转
    // 需要在页面中使用<wx-open-launch-weapp>标签
    return
  }
  
  // 在其他环境中，跳转到中间页面
  if (typeof window !== 'undefined' && window.location) {
    // 构建跳转URL
    const redirectUrl = '/pages/redirect/index'
    const queryParams = new URLSearchParams()
    queryParams.append('path', path)
    if (Object.keys(query).length > 0) {
      queryParams.append('query', JSON.stringify(query))
    }
    
    window.location.href = `${redirectUrl}?${queryParams.toString()}`
  }
}

/**
 * 强制检查环境并处理跳转
 * 在应用入口调用此函数，确保只在微信小程序环境中运行
 * @param {object} options 配置选项
 * @param {boolean} options.redirectOnWeb 是否在Web环境中自动跳转
 * @param {string} options.defaultPath 默认跳转路径
 * @returns {boolean} 是否在微信小程序环境中
 */
export function enforceWechatMiniProgram(options = {}) {
  const { redirectOnWeb = true, defaultPath = 'pages/index/index' } = options
  
  // 检查当前环境
  const isInMiniProgram = isWechatMiniProgram()
  
  // 如果不在小程序中且允许跳转
  if (!isInMiniProgram && redirectOnWeb) {
    // 提取当前路径信息作为跳转参数
    let targetPath = defaultPath
    let targetQuery = {}
    
    // 尝试从当前URL中提取路径信息
    if (typeof window !== 'undefined' && window.location) {
      const urlParams = new URLSearchParams(window.location.search)
      if (urlParams.has('path')) {
        targetPath = urlParams.get('path')
      }
      if (urlParams.has('query')) {
        try {
          targetQuery = JSON.parse(urlParams.get('query'))
        } catch (e) {
          console.error('解析query参数失败', e)
        }
      }
    }
    
    // 执行跳转
    redirectToMiniProgram(targetPath, targetQuery)
  }
  
  return isInMiniProgram
} 