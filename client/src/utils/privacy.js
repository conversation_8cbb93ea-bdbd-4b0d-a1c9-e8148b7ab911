/**
 * 微信小程序隐私协议处理工具
 */
import Taro from '@tarojs/taro'

// 隐私弹窗版本号，变更协议内容时更新版本号
const PRIVACY_VERSION = '1.0.0'
// 隐私协议本地存储键名
const PRIVACY_STORAGE_KEY = 'YIXINTANG_PRIVACY_ACCEPTED'

/**
 * 检查用户是否已同意隐私协议
 * @returns {Promise<boolean>} 用户是否已同意
 */
export function checkPrivacyAuthorization() {
  return new Promise((resolve) => {
    // 从本地存储获取用户是否已同意
    const acceptedVersion = Taro.getStorageSync(PRIVACY_STORAGE_KEY)
    if (acceptedVersion === PRIVACY_VERSION) {
      resolve(true)
      return
    }
    
    resolve(false)
  })
}

/**
 * 显示隐私协议弹窗
 * @returns {Promise<boolean>} 用户是否同意
 */
export function showPrivacyDialog() {
  return new Promise((resolve) => {
    // 使用微信原生隐私协议弹窗
    wx.requirePrivacyAuthorize({
      success: () => {
        // 用户同意隐私协议
        Taro.setStorageSync(PRIVACY_STORAGE_KEY, PRIVACY_VERSION)
        resolve(true)
      },
      fail: () => {
        // 用户拒绝隐私协议
        resolve(false)
      }
    })
  })
}

/**
 * 完整的隐私协议流程
 * @param {Function} onAccepted - 用户接受隐私协议后的回调
 * @param {Function} onRejected - 用户拒绝隐私协议后的回调
 * @returns {Promise<boolean>} 用户是否同意
 */
export async function handlePrivacy(onAccepted, onRejected) {
  // 检查用户是否已同意
  const hasAuthorized = await checkPrivacyAuthorization()
  
  if (hasAuthorized) {
    onAccepted && onAccepted()
    return true
  }
  
  // 显示隐私协议弹窗
  const accepted = await showPrivacyDialog()
  
  if (accepted) {
    onAccepted && onAccepted()
    return true
  } else {
    onRejected && onRejected()
    return false
  }
}

/**
 * 在App.js中初始化隐私协议
 */
export function initPrivacyPolicy() {
  if (wx.onNeedPrivacyAuthorization) {
    wx.onNeedPrivacyAuthorization(() => {
      wx.requirePrivacyAuthorize({
        success: () => {
          Taro.setStorageSync(PRIVACY_STORAGE_KEY, PRIVACY_VERSION)
        }
      })
    })
  }
} 