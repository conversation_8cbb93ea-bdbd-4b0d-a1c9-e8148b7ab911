/**
 * 存储工具函数
 * 提供小程序端云存储和本地存储操作接口
 */
import Taro from '@tarojs/taro'
import { getTempFileURL, uploadFile } from './cloud'

// ==================== 本地存储工具函数 ====================

/**
 * 设置本地存储
 * @param {string} key - 存储键
 * @param {any} data - 存储数据
 * @returns {Promise<boolean>} 是否成功
 */
export async function setStorage(key, data) {
  try {
    await Taro.setStorage({ key, data })
    return true
  } catch (error) {
    console.error('设置本地存储失败', error)
    return false
  }
}

/**
 * 获取本地存储
 * @param {string} key - 存储键
 * @param {any} defaultValue - 默认值
 * @returns {Promise<any>} 存储数据
 */
export async function getStorage(key, defaultValue = null) {
  try {
    const result = await Taro.getStorage({ key })
    return result.data
  } catch (error) {
    console.error('获取本地存储失败', error)
    return defaultValue
  }
}

/**
 * 移除本地存储
 * @param {string} key - 存储键
 * @returns {Promise<boolean>} 是否成功
 */
export async function removeStorage(key) {
  try {
    await Taro.removeStorage({ key })
    return true
  } catch (error) {
    console.error('移除本地存储失败', error)
    return false
  }
}

/**
 * 清空本地存储
 * @returns {Promise<boolean>} 是否成功
 */
export async function clearStorage() {
  try {
    await Taro.clearStorage()
    return true
  } catch (error) {
    console.error('清空本地存储失败', error)
    return false
  }
}

// ==================== 云存储工具函数 ====================

// 云存储根目录配置
const STORAGE_PATHS = {
  AVATARS: 'avatars/', // 用户头像目录
  SERVICES: 'services/', // 服务图片目录
  THERAPISTS: 'therapists/', // 技师图片目录
  ARTICLES: 'articles/', // 文章图片目录
  TEMP: 'temp/' // 临时文件目录
}

/**
 * 生成唯一文件名
 * @param {string} originalName - 原始文件名
 * @returns {string} 新文件名
 */
const generateUniqueFileName = (originalName = '') => {
  const timestamp = Date.now()
  const randomStr = Math.random().toString(36).substring(2, 8)
  const ext = originalName.includes('.') 
    ? originalName.substring(originalName.lastIndexOf('.'))
    : ''
  
  return `${timestamp}_${randomStr}${ext}`
}

/**
 * 从本地相册选择图片并上传到云存储
 * @param {Object} options - 选项
 * @param {string} options.category - 存储类别，对应STORAGE_PATHS中的键
 * @param {number} options.count - 最多可以选择的图片张数
 * @param {Array<string>} options.sizeType - 所选的图片的尺寸
 * @param {Array<string>} options.sourceType - 选择图片的来源
 * @returns {Promise<Array<{fileID: string, tempFileURL: string}>>} 上传结果
 */
export async function chooseAndUploadImage(options = {}) {
  const { 
    category = 'TEMP',
    count = 1,
    sizeType = ['original', 'compressed'],
    sourceType = ['album', 'camera']
  } = options
  
  try {
    // 选择图片
    const chooseResult = await Taro.chooseImage({
      count,
      sizeType,
      sourceType
    })
    
    // 获取临时文件路径
    const tempFilePaths = chooseResult.tempFilePaths
    
    // 上传文件到云存储
    const uploadPromises = tempFilePaths.map(async (filePath) => {
      // 获取文件名
      const fileName = filePath.substring(filePath.lastIndexOf('/') + 1)
      // 生成唯一文件名
      const uniqueFileName = generateUniqueFileName(fileName)
      // 云端存储路径
      const cloudPath = `${STORAGE_PATHS[category] || STORAGE_PATHS.TEMP}${uniqueFileName}`
      
      // 上传文件
      const uploadResult = await uploadFile(cloudPath, filePath)
      if (!uploadResult) return null
      
      // 获取临时访问链接
      const urlResult = await getTempFileURL(uploadResult.fileID)
      if (!urlResult) return { fileID: uploadResult.fileID, tempFileURL: '' }
      
      return {
        fileID: uploadResult.fileID,
        tempFileURL: urlResult.tempFileURL
      }
    })
    
    const results = await Promise.all(uploadPromises)
    return results.filter(item => item !== null)
  } catch (error) {
    console.error('选择上传图片失败', error)
    Taro.showToast({
      title: '上传图片失败',
      icon: 'none'
    })
    return []
  }
}

/**
 * 获取云存储文件的临时访问链接
 * @param {string|Array<string>} fileList - 文件ID或ID数组
 * @returns {Promise<{fileID: string, tempFileURL: string}|Array<{fileID: string, tempFileURL: string}>>}
 */
export async function getCloudFileURL(fileList) {
  return getTempFileURL(fileList)
}

/**
 * 预览图片
 * @param {Object} options - 选项
 * @param {string} options.url - 要预览的图片链接
 * @param {Array<string>} options.urls - 图片链接列表
 * @returns {Promise<void>}
 */
export async function previewImage(options) {
  const { url, urls = [] } = options
  
  try {
    await Taro.previewImage({
      current: url,
      urls: urls.length > 0 ? urls : [url]
    })
  } catch (error) {
    console.error('预览图片失败', error)
  }
}

/**
 * 下载并保存图片到相册
 * @param {string} url - 图片链接
 * @returns {Promise<boolean>} 是否成功
 */
export async function saveImageToAlbum(url) {
  try {
    // 检查授权状态
    const authSetting = await Taro.getSetting()
    if (!authSetting.authSetting['scope.writePhotosAlbum']) {
      // 没有授权，请求授权
      await Taro.authorize({ scope: 'scope.writePhotosAlbum' })
    }
    
    // 下载图片
    const downloadResult = await Taro.downloadFile({ url })
    
    // 保存图片到相册
    await Taro.saveImageToPhotosAlbum({
      filePath: downloadResult.tempFilePath
    })
    
    Taro.showToast({
      title: '保存成功',
      icon: 'success'
    })
    
    return true
  } catch (error) {
    console.error('保存图片失败', error)
    
    if (error.errMsg.includes('auth')) {
      // 授权失败，提示用户
      Taro.showModal({
        title: '提示',
        content: '需要您授权保存图片到相册',
        confirmText: '去设置',
        success: (res) => {
          if (res.confirm) {
            Taro.openSetting()
          }
        }
      })
    } else {
      Taro.showToast({
        title: '保存失败',
        icon: 'none'
      })
    }
    
    return false
  }
} 