import Taro from '@tarojs/taro'

// 基础配置 - 连接Django后端
const BASE_URL = process.env.NODE_ENV === 'production'
  ? 'https://your-production-domain.com/api/v1'
  : 'http://localhost:8000/api/v1'

/**
 * HTTP请求封装
 * @param {Object} options - 请求配置
 * @param {string} options.url - 请求URL
 * @param {string} options.method - 请求方法
 * @param {Object} options.data - 请求数据
 * @param {boolean} options.loading - 是否显示加载提示
 * @param {string} options.loadingText - 加载提示文字
 * @returns {Promise} - 请求Promise
 */
export function request(options) {
  const { url, method = 'GET', data = {}, loading = true, loadingText = '加载中...' } = options

  // 显示加载提示
  if (loading) {
    Taro.showLoading({
      title: loadingText,
      mask: true
    })
  }

  return new Promise((resolve, reject) => {
    Taro.request({
      url: `${BASE_URL}${url}`,
      method,
      data,
      header: {
        'content-type': 'application/json'
      },
      success: (res) => {
        // 服务端返回的数据结构：{ success: boolean, data: any, message: string }
        const { statusCode } = res
        // 请求成功
        if (statusCode >= 200 && statusCode < 300) {
          resolve(res.data)
        } else {
          // 显示错误提示
          Taro.showToast({
            title: res.data?.message || '请求失败',
            icon: 'none',
            duration: 2000
          })
          reject(new Error(res.data?.message || '请求失败'))
        }
      },
      fail: (err) => {
        // 显示错误提示
        Taro.showToast({
          title: '网络错误，请稍后重试',
          icon: 'none',
          duration: 2000
        })
        reject(new Error(err.errMsg || '网络错误'))
      },
      complete: () => {
        // 隐藏加载提示
        if (loading) {
          Taro.hideLoading()
        }
      }
    })
  })
}

/**
 * GET请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 * @returns {Promise} - 请求Promise
 */
export function get(url, data = {}, options = {}) {
  return request({
    url,
    method: 'GET',
    data,
    ...options
  })
}

/**
 * POST请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 * @returns {Promise} - 请求Promise
 */
export function post(url, data = {}, options = {}) {
  return request({
    url,
    method: 'POST',
    data,
    ...options
  })
}

/**
 * PUT请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 * @returns {Promise} - 请求Promise
 */
export function put(url, data = {}, options = {}) {
  return request({
    url,
    method: 'PUT',
    data,
    ...options
  })
}

/**
 * DELETE请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 * @returns {Promise} - 请求Promise
 */
export function del(url, data = {}, options = {}) {
  return request({
    url,
    method: 'DELETE',
    data,
    ...options
  })
} 