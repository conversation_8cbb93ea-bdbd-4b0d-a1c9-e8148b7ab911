{"summary": {"type": "miniprogram-test", "totalTests": 50, "successCount": 50, "failureCount": 0, "successRate": 100, "duration": 16, "timestamp": "2025-07-05T16:18:24.212Z"}, "categories": {"项目结构": {"total": 9, "success": 9}, "Taro配置": {"total": 7, "success": 7}, "应用配置": {"total": 6, "success": 6}, "页面结构": {"total": 10, "success": 10}, "组件结构": {"total": 2, "success": 2}, "API结构": {"total": 5, "success": 5}, "工具函数": {"total": 6, "success": 6}, "云函数": {"total": 5, "success": 5}}, "results": [{"category": "项目结构", "test": "核心文件-package.json", "success": true, "details": "文件存在", "timestamp": "2025-07-05T16:18:24.204Z"}, {"category": "项目结构", "test": "核心文件-project.config.json", "success": true, "details": "文件存在", "timestamp": "2025-07-05T16:18:24.205Z"}, {"category": "项目结构", "test": "核心文件-app.js", "success": true, "details": "文件存在", "timestamp": "2025-07-05T16:18:24.205Z"}, {"category": "项目结构", "test": "核心文件-app.config.js", "success": true, "details": "文件存在", "timestamp": "2025-07-05T16:18:24.205Z"}, {"category": "项目结构", "test": "核心文件-app.scss", "success": true, "details": "文件存在", "timestamp": "2025-07-05T16:18:24.205Z"}, {"category": "项目结构", "test": "目录-pages", "success": true, "details": "目录存在", "timestamp": "2025-07-05T16:18:24.205Z"}, {"category": "项目结构", "test": "目录-components", "success": true, "details": "目录存在", "timestamp": "2025-07-05T16:18:24.206Z"}, {"category": "项目结构", "test": "目录-api", "success": true, "details": "目录存在", "timestamp": "2025-07-05T16:18:24.206Z"}, {"category": "项目结构", "test": "目录-utils", "success": true, "details": "目录存在", "timestamp": "2025-07-05T16:18:24.206Z"}, {"category": "Taro配置", "test": "<PERSON><PERSON>核心", "success": true, "details": "^4.0.6", "timestamp": "2025-07-05T16:18:24.207Z"}, {"category": "Taro配置", "test": "Taro组件", "success": true, "details": "^4.0.6", "timestamp": "2025-07-05T16:18:24.207Z"}, {"category": "Taro配置", "test": "Webpack运行器", "success": true, "details": "^4.0.6", "timestamp": "2025-07-05T16:18:24.207Z"}, {"category": "Taro配置", "test": "构建脚本", "success": true, "details": "taro build --type weapp", "timestamp": "2025-07-05T16:18:24.207Z"}, {"category": "Taro配置", "test": "开发脚本", "success": true, "details": "npm run build:weapp -- --watch", "timestamp": "2025-07-05T16:18:24.207Z"}, {"category": "Taro配置", "test": "小程序AppID", "success": true, "details": "wx1832d35c93f83a8b", "timestamp": "2025-07-05T16:18:24.207Z"}, {"category": "Taro配置", "test": "项目名称", "success": true, "details": "yixintang-miniprogram", "timestamp": "2025-07-05T16:18:24.207Z"}, {"category": "应用配置", "test": "页面配置", "success": true, "details": "包含页面配置", "timestamp": "2025-07-05T16:18:24.208Z"}, {"category": "应用配置", "test": "窗口配置", "success": true, "details": "包含窗口配置", "timestamp": "2025-07-05T16:18:24.208Z"}, {"category": "应用配置", "test": "底部导航", "success": true, "details": "包含底部导航", "timestamp": "2025-07-05T16:18:24.208Z"}, {"category": "应用配置", "test": "Taro导入", "success": true, "details": "正确导入Taro", "timestamp": "2025-07-05T16:18:24.209Z"}, {"category": "应用配置", "test": "组件导入", "success": true, "details": "正确导入组件", "timestamp": "2025-07-05T16:18:24.209Z"}, {"category": "应用配置", "test": "应用组件", "success": true, "details": "应用组件存在", "timestamp": "2025-07-05T16:18:24.209Z"}, {"category": "页面结构", "test": "页面数量", "success": true, "details": "页面数量: 8", "timestamp": "2025-07-05T16:18:24.209Z"}, {"category": "页面结构", "test": "页面-booking", "success": true, "details": "入口文件: ✓, 配置文件: ✗", "timestamp": "2025-07-05T16:18:24.209Z"}, {"category": "页面结构", "test": "页面-index", "success": true, "details": "入口文件: ✓, 配置文件: ✗", "timestamp": "2025-07-05T16:18:24.209Z"}, {"category": "页面结构", "test": "页面-my-services", "success": true, "details": "入口文件: ✓, 配置文件: ✗", "timestamp": "2025-07-05T16:18:24.209Z"}, {"category": "页面结构", "test": "页面-orders", "success": true, "details": "入口文件: ✓, 配置文件: ✗", "timestamp": "2025-07-05T16:18:24.209Z"}, {"category": "页面结构", "test": "页面-profile", "success": true, "details": "入口文件: ✓, 配置文件: ✗", "timestamp": "2025-07-05T16:18:24.209Z"}, {"category": "页面结构", "test": "页面-redirect", "success": true, "details": "入口文件: ✓, 配置文件: ✗", "timestamp": "2025-07-05T16:18:24.209Z"}, {"category": "页面结构", "test": "页面-services", "success": true, "details": "入口文件: ✓, 配置文件: ✗", "timestamp": "2025-07-05T16:18:24.209Z"}, {"category": "页面结构", "test": "页面-therapists", "success": true, "details": "入口文件: ✓, 配置文件: ✗", "timestamp": "2025-07-05T16:18:24.210Z"}, {"category": "页面结构", "test": "有效页面", "success": true, "details": "有效页面: 8/8", "timestamp": "2025-07-05T16:18:24.210Z"}, {"category": "组件结构", "test": "组件数量", "success": true, "details": "组件数量: 0", "timestamp": "2025-07-05T16:18:24.210Z"}, {"category": "组件结构", "test": "有效组件", "success": true, "details": "有效组件: 0/0", "timestamp": "2025-07-05T16:18:24.210Z"}, {"category": "API结构", "test": "API文件数量", "success": true, "details": "API文件数量: 3", "timestamp": "2025-07-05T16:18:24.210Z"}, {"category": "API结构", "test": "API文件-index.js", "success": true, "details": "文件存在", "timestamp": "2025-07-05T16:18:24.210Z"}, {"category": "API结构", "test": "API文件-user.js", "success": true, "details": "文件存在", "timestamp": "2025-07-05T16:18:24.210Z"}, {"category": "API结构", "test": "API文件-cloud-api.js", "success": true, "details": "文件存在", "timestamp": "2025-07-05T16:18:24.210Z"}, {"category": "API结构", "test": "请求方法", "success": true, "details": "包含请求方法", "timestamp": "2025-07-05T16:18:24.210Z"}, {"category": "工具函数", "test": "工具文件数量", "success": true, "details": "工具文件数量: 5", "timestamp": "2025-07-05T16:18:24.210Z"}, {"category": "工具函数", "test": "工具文件-request.js", "success": true, "details": "文件存在", "timestamp": "2025-07-05T16:18:24.210Z"}, {"category": "工具函数", "test": "工具文件-storage.js", "success": true, "details": "文件存在", "timestamp": "2025-07-05T16:18:24.210Z"}, {"category": "工具函数", "test": "工具文件-cloud.js", "success": true, "details": "文件存在", "timestamp": "2025-07-05T16:18:24.210Z"}, {"category": "工具函数", "test": "请求工具", "success": true, "details": "包含请求工具", "timestamp": "2025-07-05T16:18:24.211Z"}, {"category": "工具函数", "test": "存储工具", "success": true, "details": "包含存储工具", "timestamp": "2025-07-05T16:18:24.211Z"}, {"category": "云函数", "test": "云函数数量", "success": true, "details": "云函数数量: 8", "timestamp": "2025-07-05T16:18:24.212Z"}, {"category": "云函数", "test": "云函数-login", "success": true, "details": "入口文件: ✓, 配置文件: ✓", "timestamp": "2025-07-05T16:18:24.212Z"}, {"category": "云函数", "test": "云函数-database", "success": true, "details": "入口文件: ✓, 配置文件: ✓", "timestamp": "2025-07-05T16:18:24.212Z"}, {"category": "云函数", "test": "云函数-createBooking", "success": true, "details": "入口文件: ✓, 配置文件: ✓", "timestamp": "2025-07-05T16:18:24.212Z"}, {"category": "云函数", "test": "有效云函数", "success": true, "details": "有效云函数: 3/3", "timestamp": "2025-07-05T16:18:24.212Z"}]}