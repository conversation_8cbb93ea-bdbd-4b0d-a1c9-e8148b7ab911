{"miniprogramRoot": "dist/", "cloudfunctionRoot": "cloudfunctions/", "projectname": "yixintang-miniprogram", "description": "怡心堂中医理疗管理系统小程序", "appid": "wx1832d35c93f83a8b", "setting": {"urlCheck": true, "es6": false, "enhance": false, "postcss": false, "preloadBackgroundData": false, "minified": false, "newFeature": false, "coverView": true, "nodeModules": false, "autoAudits": false, "showShadowRootInWxmlPanel": true, "scopeDataCheck": false, "uglifyFileName": false, "checkInvalidKey": true, "checkSiteMap": true, "uploadWithSourceMap": true, "compileHotReLoad": false, "useMultiFrameRuntime": false, "useApiHook": true, "useApiHostProcess": false, "babelSetting": {"ignore": [], "disablePlugins": [], "outputPath": ""}, "useIsolateContext": true, "userConfirmedBundleSwitch": false, "packNpmManually": false, "packNpmRelationList": [], "minifyWXSS": false, "disableUseStrict": false, "minifyWXML": false, "showES6CompileOption": false, "useCompilerPlugins": false}, "compileType": "miniprogram", "libVersion": "2.25.3", "condition": {}}