const cloudConfig = require('./plugins/cloud')
const path = require('path')

const config = {
  projectName: '壹心堂中医推拿',
  date: '2023-6-1',
  designWidth: 750,
  deviceRatio: {
    640: 2.34 / 2,
    750: 1,
    828: 1.81 / 2
  },
  sourceRoot: 'src',
  outputRoot: 'dist',
  plugins: [],
  defineConstants: {
    CLOUD_ENV_ID: JSON.stringify('cloud1-9gtxemp4512e0880')
  },
  copy: {
    patterns: [
      // 复制云函数到输出目录
      { from: 'cloudfunctions', to: 'cloudfunctions' }
    ],
    options: {
    }
  },
  framework: 'vue3',
  compiler: 'webpack5',
  cache: {
    enable: true // Webpack 持久化缓存配置，建议开启。默认配置请参考：https://docs.taro.zone/docs/config-detail#cache
  },
  mini: {
    // 暂时移除所有自定义webpack配置，使用默认配置
    // webpackChain(chain, webpack) {
    //   // 添加云开发扩展
    //   chain.plugin('define')
    //     .tap(args => {
    //       args[0]['process.env'].CLOUD_ENV_ID = JSON.stringify('cloud1-9gtxemp4512e0880')
    //       return args
    //     })
    // },
    postcss: {
      pxtransform: {
        enable: true,
        config: {

        }
      },
      url: {
        enable: true,
        config: {
          limit: 1024 // 设定转换尺寸上限
        }
      },
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    },
    // 云开发模式
    cloud: true,
    // 启用主包优化
    optimizeMainPackage: {
      enable: true
    },
    // 启用热更新
    hot: true
  },
  h5: {
    publicPath: '/',
    staticDirectory: 'static',
    // 开发服务器配置
    devServer: {
      hot: true, // 启用热更新
      port: 10086, // 服务端口
      open: true, // 自动打开浏览器
      historyApiFallback: true // 支持单页应用路由
    },
    postcss: {
      autoprefixer: {
        enable: true,
        config: {
        }
      },
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
        config: {
          namingPattern: 'module', // 转换模式，取值为 global/module
          generateScopedName: '[name]__[local]___[hash:base64:5]'
        }
      }
    }
  },
  rn: {
    appName: 'taroDemo',
    postcss: {
      cssModules: {
        enable: false, // 默认为 false，如需使用 css modules 功能，则设为 true
      }
    }
  }
}

// 获取环境变量
const env = process.env.NODE_ENV || 'development';

// 根据环境变量合并配置
if (env === 'development') {
  // 开发环境配置
  const devConfig = require('./dev');
  module.exports = Object.assign({}, config, devConfig);
} else {
  // 生产环境配置
  const prodConfig = require('./prod');
  module.exports = Object.assign({}, config, prodConfig);
} 