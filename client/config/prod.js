module.exports = {
  env: {
    NODE_ENV: '"production"'
  },
  defineConstants: {
    // API配置
    API_BASE_URL: '"https://yixintang-backend.run.tcloudbase.com"',
    API_TIMEOUT: '"30000"',

    // 微信配置
    WECHAT_APP_ID: '"wx1832d35c93f83a8b"',

    // 功能开关
    ENABLE_DEBUG: 'false',
    ENABLE_MOCK: 'false',
    ENABLE_VCONSOLE: 'false',

    // 版本信息
    APP_VERSION: '"1.0.0"',
    BUILD_TIME: `"${new Date().toISOString()}"`,

    // 业务配置
    DEFAULT_CITY: '"北京"',

    // 上传配置
    UPLOAD_MAX_SIZE: '"5242880"', // 5MB

    // 分页配置
    PAGE_SIZE: '"20"',
    MAX_PAGE_SIZE: '"100"',

    // 缓存配置
    CACHE_EXPIRE_TIME: '"1800000"', // 30分钟
  },
  mini: {
    // 压缩配置
    terser: {
      enable: true,
      config: {
        // 生产环境移除console
        compress: {
          drop_console: true,
          drop_debugger: true
        }
      }
    },
    // 主包优化配置
    optimizeMainPackage: {
      enable: true
    },
    // 小程序配置
    miniCssExtractPluginOption: {
      ignoreOrder: true
    },
    webpackChain: (chain) => {
      chain.merge({
        optimization: {
          splitChunks: {
            chunks: 'all',
            minSize: 0,
            maxSize: 0,
            minChunks: 1,
            maxAsyncRequests: 100,
            maxInitialRequests: 100,
            automaticNameDelimiter: '~',
            cacheGroups: {
              vendors: {
                name: 'vendors',
                test: /[\\/]node_modules[\\/]/,
                priority: -10
              },
              common: {
                name: 'common',
                minChunks: 2,
                priority: -20,
                reuseExistingChunk: true
              }
            }
          }
        }
      })
    }
  },
  h5: {
    /**
     * WebpackChain 插件配置
     * @docs https://github.com/neutrinojs/webpack-chain
     */
    webpackChain (chain) {
      /**
       * 如果 H5 端编译后体积过大，可以使用 webpack-bundle-analyzer 插件对打包体积进行分析。
       * @docs https://github.com/webpack-contrib/webpack-bundle-analyzer
       */
      chain.plugin('analyzer')
        .use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin, [
          {
            analyzerMode: 'static',
            openAnalyzer: false,
            reportFilename: 'bundle-analyzer-report.html'
          }
        ])
    },
    /**
     * 如果 H5 端首屏加载时间过长，可以使用 prerender-spa-plugin 插件预渲染页面。
     * @docs https://github.com/chrisvfritz/prerender-spa-plugin
     */
    webpackChain (chain) {
      chain.plugin('prerender')
        .use(require('prerender-spa-plugin'), [{
          staticDir: path.join(__dirname, '..', 'dist'),
          routes: ['/', '/pages/index/index'],
          postProcess: (renderedRoute) => {
            renderedRoute.route = renderedRoute.originalRoute
            renderedRoute.html = renderedRoute.html
              .replace(/<script (.*?)>/g, '<script $1 defer>')
            return renderedRoute
          }
        }])
    }
  }
} 