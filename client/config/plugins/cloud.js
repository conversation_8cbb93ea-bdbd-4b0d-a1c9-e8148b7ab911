/**
 * 云开发相关配置
 */
module.exports = {
  env: {
    default: {
      // 云环境ID
      envId: 'cloud1-9gtxemp4512e0880',
      // 云函数目录
      functionRoot: 'cloudfunctions',
      // 云存储下载目录
      downloadDir: './.download',
      // 云存储上传根目录
      uploadDir: './.upload',
      // 云函数构建目录
      buildDir: './.build'
    },
    dev: {
      envId: 'cloud1-9gtxemp4512e0880',
      functionRoot: 'cloudfunctions',
      downloadDir: './.download',
      uploadDir: './.upload',
      buildDir: './.build'
    },
    prod: {
      envId: 'cloud1-9gtxemp4512e0880',
      functionRoot: 'cloudfunctions',
      downloadDir: './.download',
      uploadDir: './.upload',
      buildDir: './.build'
    }
  },
  // 云函数本地代理配置
  proxy: {
    port: 5000,
    secretId: process.env.TENCENT_SECRET_ID,
    secretKey: process.env.TENCENT_SECRET_KEY
  },
  // 云存储默认权限配置
  storage: {
    defaultPermission: 'READ'
  },
  // 云函数部署配置
  functions: {
    login: {
      // 超时时间
      timeout: 10,
      // 内存大小
      memory: 256,
      // 环境变量
      envVariables: {
        NODE_ENV: process.env.NODE_ENV
      },
      // 标签
      tags: {
        userService: 'login'
      }
    }
  }
} 