module.exports = {
  env: {
    NODE_ENV: '"development"'
  },
  defineConstants: {
  },
  mini: {
    hot: true, // 开启热更新
    hotOptions: {
      port: 10086, // 热更新端口
      domain: 'localhost', // 热更新域名
    },
    optimizeMainPackage: {
      enable: true
    },
    // 微信开发者工具配置
    compiler: {
      type: 'webpack5',
      // 指定微信开发者工具路径
      devToolsPath: '/Applications/wechatwebdevtools.app/Contents/MacOS/cli'
    },
    webpackChain(chain) {
      // 配置热更新
      chain.plugin('hotModuleReplacement')
        .use(require('webpack/lib/HotModuleReplacementPlugin'));
      
      // 控制台显示更详细的构建信息
      chain.plugin('progressPlugin')
        .use(require('webpack/lib/ProgressPlugin'));
    }
  },
  h5: {
    devServer: {
      port: 10086,
      open: true,
      hot: true
    }
  }
} 