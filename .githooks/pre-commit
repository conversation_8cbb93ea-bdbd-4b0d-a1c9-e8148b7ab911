#!/bin/bash

# 壹心堂Git Pre-commit Hook
# 基于轮廓调试标准规则v4.0和历史成功案例
# 确保所有提交都通过质量检查

echo "🎯 壹心堂代码质量检查开始..."
echo "📋 基于轮廓调试标准规则v4.0"

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 检查是否在admin目录
if [ ! -d "admin" ]; then
    echo -e "${RED}❌ 错误：请在项目根目录执行git commit${NC}"
    exit 1
fi

cd admin

# 1. Stylelint CSS标准检查
echo -e "\n${BLUE}🔍 1. CSS标准合规性检查...${NC}"
if npm run stylelint-check; then
    echo -e "${GREEN}✅ CSS标准检查通过${NC}"
else
    echo -e "${RED}❌ CSS标准检查失败${NC}"
    echo -e "${YELLOW}💡 运行 'npm run stylelint' 自动修复部分问题${NC}"
    exit 1
fi

# 2. 轮廓调试检查
echo -e "\n${BLUE}🎯 2. 轮廓调试检查...${NC}"
if npm run outline-check; then
    echo -e "${GREEN}✅ 轮廓调试检查通过${NC}"
else
    echo -e "${RED}❌ 轮廓调试检查失败${NC}"
    echo -e "${YELLOW}💡 请检查布局问题并修复后再提交${NC}"
    exit 1
fi

# 3. Vue组件标准检查
echo -e "\n${BLUE}🧩 3. Vue组件标准检查...${NC}"
vue_files=$(git diff --cached --name-only --diff-filter=ACM | grep '\.vue$' | wc -l)
if [ $vue_files -gt 0 ]; then
    echo "发现 $vue_files 个Vue文件变更"
    
    # 检查是否使用标准组件
    non_standard_components=0
    for file in $(git diff --cached --name-only --diff-filter=ACM | grep '\.vue$'); do
        if [ -f "$file" ]; then
            # 检查是否使用了非标准的高度设置
            if grep -q "height.*[0-9].*px" "$file" && ! grep -q "var(--row-height)" "$file"; then
                echo -e "${YELLOW}⚠️ $file 使用了非标准高度设置${NC}"
                non_standard_components=$((non_standard_components + 1))
            fi
            
            # 检查是否使用了非标准的padding设置
            if grep -q "padding.*[0-9].*px" "$file" && ! grep -q "var(--cell-padding)" "$file"; then
                echo -e "${YELLOW}⚠️ $file 使用了非标准padding设置${NC}"
                non_standard_components=$((non_standard_components + 1))
            fi
        fi
    done
    
    if [ $non_standard_components -gt 0 ]; then
        echo -e "${YELLOW}⚠️ 发现 $non_standard_components 个组件使用非标准设置${NC}"
        echo -e "${YELLOW}💡 建议使用CSS标准变量：var(--row-height), var(--cell-padding)${NC}"
        echo -e "${YELLOW}📋 参考：admin/src/styles/standards.css${NC}"
    else
        echo -e "${GREEN}✅ Vue组件标准检查通过${NC}"
    fi
else
    echo -e "${GREEN}✅ 无Vue文件变更${NC}"
fi

# 4. 检查是否有调试代码残留
echo -e "\n${BLUE}🔍 4. 调试代码检查...${NC}"
debug_issues=0
for file in $(git diff --cached --name-only --diff-filter=ACM | grep -E '\.(js|vue|ts)$'); do
    if [ -f "$file" ]; then
        # 检查console.log
        if grep -n "console\.log" "$file" > /dev/null; then
            echo -e "${YELLOW}⚠️ $file 包含 console.log${NC}"
            debug_issues=$((debug_issues + 1))
        fi
        
        # 检查debugger
        if grep -n "debugger" "$file" > /dev/null; then
            echo -e "${RED}❌ $file 包含 debugger 语句${NC}"
            debug_issues=$((debug_issues + 1))
        fi
        
        # 检查TODO注释
        if grep -n "TODO\|FIXME\|XXX" "$file" > /dev/null; then
            echo -e "${YELLOW}⚠️ $file 包含待办事项注释${NC}"
        fi
    fi
done

if [ $debug_issues -gt 0 ]; then
    echo -e "${RED}❌ 发现 $debug_issues 个调试代码问题${NC}"
    echo -e "${YELLOW}💡 请移除调试代码后再提交${NC}"
    exit 1
else
    echo -e "${GREEN}✅ 调试代码检查通过${NC}"
fi

# 5. 文件大小检查
echo -e "\n${BLUE}📦 5. 文件大小检查...${NC}"
large_files=0
for file in $(git diff --cached --name-only --diff-filter=ACM); do
    if [ -f "$file" ]; then
        size=$(wc -c < "$file")
        if [ $size -gt 1048576 ]; then  # 1MB
            echo -e "${RED}❌ $file 文件过大 ($(($size / 1024))KB)${NC}"
            large_files=$((large_files + 1))
        elif [ $size -gt 524288 ]; then  # 512KB
            echo -e "${YELLOW}⚠️ $file 文件较大 ($(($size / 1024))KB)${NC}"
        fi
    fi
done

if [ $large_files -gt 0 ]; then
    echo -e "${RED}❌ 发现 $large_files 个过大文件${NC}"
    echo -e "${YELLOW}💡 请优化文件大小或使用Git LFS${NC}"
    exit 1
else
    echo -e "${GREEN}✅ 文件大小检查通过${NC}"
fi

# 6. 提交信息预检查
echo -e "\n${BLUE}📝 6. 提交信息建议...${NC}"
echo -e "${YELLOW}💡 建议的提交信息格式：${NC}"
echo -e "${YELLOW}   feat: 添加新功能${NC}"
echo -e "${YELLOW}   fix: 修复问题${NC}"
echo -e "${YELLOW}   style: 样式调整${NC}"
echo -e "${YELLOW}   refactor: 代码重构${NC}"
echo -e "${YELLOW}   test: 测试相关${NC}"
echo -e "${YELLOW}   docs: 文档更新${NC}"

# 所有检查通过
echo -e "\n${GREEN}🎉 所有检查通过！准备提交...${NC}"
echo -e "${GREEN}📋 基于轮廓调试标准规则v4.0的质量保证${NC}"
echo -e "${GREEN}✅ 代码质量符合壹心堂标准${NC}"

cd ..
exit 0
