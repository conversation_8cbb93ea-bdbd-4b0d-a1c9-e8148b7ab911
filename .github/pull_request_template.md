# Pull Request

## 📋 变更概述

<!-- 请简要描述此 PR 的主要变更内容 -->

## 🎯 变更类型

请勾选适用的变更类型：

- [ ] 🐛 Bug 修复 (非破坏性变更，修复了一个问题)
- [ ] ✨ 新功能 (非破坏性变更，添加了新功能)
- [ ] 💥 破坏性变更 (修复或功能会导致现有功能无法正常工作)
- [ ] 📚 文档更新 (仅文档变更)
- [ ] 🎨 代码风格 (格式化、缺少分号等，不影响代码运行的变更)
- [ ] ♻️ 代码重构 (既不修复 bug 也不添加功能的代码变更)
- [ ] ⚡ 性能优化 (提高性能的代码变更)
- [ ] ✅ 测试 (添加缺失的测试或修正现有测试)
- [ ] 🔧 构建 (影响构建系统或外部依赖的变更)
- [ ] 👷 CI/CD (对 CI 配置文件和脚本的变更)

## 📝 详细描述

<!-- 详细描述你的变更，包括变更的原因和实现方式 -->

### 变更内容
- 
- 
- 

### 实现方式
<!-- 描述你是如何实现这些变更的 -->

## 🔗 相关 Issue

<!-- 如果此 PR 解决了某个 Issue，请在此处链接 -->
Closes #(issue number)

## 🧪 测试

### 测试用例
- [ ] 单元测试已通过
- [ ] 集成测试已通过
- [ ] 端到端测试已通过
- [ ] 手动测试已完成

### 测试步骤
<!-- 描述如何测试你的变更 -->
1. 
2. 
3. 

### 测试环境
- [ ] 本地开发环境
- [ ] 测试环境
- [ ] 预生产环境

## 📱 影响范围

请勾选此 PR 影响的组件：

- [ ] 🖥️ 管理后台 (admin/)
- [ ] 📱 小程序 (client/)
- [ ] 🔧 后端 API (server/)
- [ ] 🗄️ 数据库 (migrations, models)
- [ ] 🐳 部署配置 (Docker, CI/CD)
- [ ] 📚 文档 (README, docs)

## 🔄 数据库变更

- [ ] 此 PR 包含数据库迁移
- [ ] 数据库迁移已在本地测试
- [ ] 数据库迁移向后兼容
- [ ] 已考虑数据迁移的回滚方案

如果包含数据库变更，请描述：
<!-- 描述数据库变更的内容和影响 -->

## 🔒 安全考虑

- [ ] 此变更不涉及安全敏感内容
- [ ] 已进行安全评估
- [ ] 已更新相关的安全文档
- [ ] 已考虑潜在的安全风险

如果涉及安全变更，请描述：
<!-- 描述安全相关的变更和考虑 -->

## 📊 性能影响

- [ ] 此变更不影响性能
- [ ] 已进行性能测试
- [ ] 性能有所提升
- [ ] 性能有所下降（已评估影响）

如果影响性能，请描述：
<!-- 描述性能影响和测试结果 -->

## 🔧 配置变更

- [ ] 此 PR 不需要配置变更
- [ ] 需要更新环境变量
- [ ] 需要更新配置文件
- [ ] 需要更新部署脚本

如果需要配置变更，请列出：
<!-- 列出需要的配置变更 -->

## 📋 检查清单

在提交此 PR 之前，请确认：

### 代码质量
- [ ] 代码遵循项目的编码规范
- [ ] 已运行代码检查工具 (ESLint, Prettier, Black 等)
- [ ] 代码已经过自我审查
- [ ] 复杂的代码已添加注释

### 测试
- [ ] 已为新功能添加测试
- [ ] 所有测试都通过
- [ ] 测试覆盖率满足要求
- [ ] 已测试边界情况

### 文档
- [ ] 已更新相关文档
- [ ] API 变更已更新 API 文档
- [ ] README 已更新（如需要）
- [ ] 变更日志已更新

### 兼容性
- [ ] 变更向后兼容
- [ ] 已考虑对现有用户的影响
- [ ] 已提供迁移指南（如需要）

## 📸 截图/录屏

<!-- 如果是 UI 变更，请提供截图或录屏 -->

### 变更前
<!-- 变更前的截图 -->

### 变更后
<!-- 变更后的截图 -->

## 🚀 部署说明

<!-- 如果此 PR 需要特殊的部署步骤，请在此说明 -->

### 部署前准备
- [ ] 无特殊要求
- [ ] 需要数据库迁移
- [ ] 需要更新环境变量
- [ ] 需要重启服务

### 部署步骤
1. 
2. 
3. 

### 回滚方案
<!-- 如果部署出现问题，如何回滚 -->

## 👥 审查者

<!-- @mention 需要审查此 PR 的人员 -->

### 必需审查者
- @backend-team (后端变更)
- @frontend-team (前端变更)
- @devops-team (部署变更)

### 可选审查者
- @qa-team (测试相关)
- @security-team (安全相关)

## 📝 额外说明

<!-- 任何其他需要审查者知道的信息 -->

---

**感谢您的贡献！** 🎉

请确保您已经阅读并遵循了我们的 [贡献指南](../CONTRIBUTING.md) 和 [代码规范](../CODE_STYLE.md)。
