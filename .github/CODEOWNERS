# CODEOWNERS文件 - 定义代码所有者
# 更多信息: https://docs.github.com/en/repositories/managing-your-repositorys-settings-and-features/customizing-your-repository/about-code-owners

# 只对关键配置文件要求代码审查，其他文件允许自由修改

# 关键配置文件 - 需要代码所有者审查
package.json @wheresleak
package-lock.json @wheresleak

# GitHub工作流配置 - 需要审查
.github/workflows/ @wheresleak

# 部署相关配置 - 需要审查
docker-compose.yml @wheresleak
Dockerfile @wheresleak

# 其他所有文件都不需要特殊审查
# 注释掉全局规则，允许自由推送
# * @wheresleak
