# 怡心堂中医理疗管理系统 - CI/CD 工作流程
name: CI/CD Pipeline

on:
  push:
    branches:
      - main        # 生产环境部署
    tags:
      - 'v*'        # 版本标签发布
  pull_request:
    branches:
      - main

env:
  NODE_VERSION: '18'
  PYTHON_VERSION: '3.11'

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: 'admin/package-lock.json'

    - name: 安装前端依赖
      run: |
        cd admin
        npm ci

    - name: 前端代码检查
      run: |
        cd admin
        npm run lint
        npm run type-check

    - name: 设置 Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: 安装后端依赖
      run: |
        cd server
        pip install -r requirements.txt

    - name: 后端代码检查
      run: |
        cd server
        python check_code_quality.py

  # 前端测试
  frontend-test:
    name: 前端测试
    runs-on: ubuntu-latest
    needs: code-quality
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: ${{ env.NODE_VERSION }}
        cache: 'npm'
        cache-dependency-path: |
          admin/package-lock.json
          client/package-lock.json

    - name: 安装管理后台依赖
      run: |
        cd admin
        npm ci

    - name: 运行管理后台测试
      run: |
        cd admin
        npm run test:unit

    - name: 安装小程序依赖
      run: |
        cd client
        npm ci

    - name: 构建小程序
      run: |
        cd client
        npm run build:weapp

  # 后端测试
  backend-test:
    name: 后端测试
    runs-on: ubuntu-latest
    needs: code-quality
    services:
      mysql:
        image: mysql:8.0
        env:
          MYSQL_ROOT_PASSWORD: test_password
          MYSQL_DATABASE: test_db
        ports:
          - 3306:3306
        options: --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s --health-retries=3

    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 设置 Python
      uses: actions/setup-python@v5
      with:
        python-version: ${{ env.PYTHON_VERSION }}

    - name: 安装系统依赖
      run: |
        sudo apt-get update
        sudo apt-get install -y default-libmysqlclient-dev pkg-config

    - name: 安装 Python 依赖
      run: |
        cd server
        pip install -r requirements.txt

    - name: 运行数据库迁移
      run: |
        cd server
        python manage.py migrate
      env:
        DEBUG: True
        DB_NAME: test_db
        DB_USER: root
        DB_PASSWORD: test_password
        DB_HOST: 127.0.0.1
        DB_PORT: 3306

    - name: 运行后端测试
      run: |
        cd server
        python manage.py test
      env:
        DEBUG: True
        DB_NAME: test_db
        DB_USER: root
        DB_PASSWORD: test_password
        DB_HOST: 127.0.0.1
        DB_PORT: 3306

    - name: 生成测试覆盖率报告
      run: |
        cd server
        pip install coverage
        coverage run --source='.' manage.py test
        coverage xml
      env:
        DEBUG: True
        DB_NAME: test_db
        DB_USER: root
        DB_PASSWORD: test_password
        DB_HOST: 127.0.0.1
        DB_PORT: 3306

    - name: 上传覆盖率报告
      uses: codecov/codecov-action@v4
      with:
        file: ./server/coverage.xml
        flags: backend
        name: backend-coverage

  # 微信云托管自动部署 (基于django-t3qr-009成功配置)
  deploy-wechat-cloud:
    name: 微信云托管自动部署
    runs-on: ubuntu-latest
    needs: [frontend-test, backend-test]
    if: github.ref == 'refs/heads/main'
    environment: production
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 验证Django配置
      run: |
        cd server
        echo "🔍 验证Django 3.2.8配置..."
        grep "Django==3.2.8" requirements.txt || (echo "❌ 错误: 必须使用Django 3.2.8" && exit 1)
        echo "✅ Django版本验证通过"

    - name: 验证项目规范
      run: |
        echo "🔍 验证项目规范..."
        node scripts/check-standards.js --no-fix
        echo "✅ 项目规范验证通过"

    - name: 微信云托管部署
      run: |
        echo "🚀 开始部署到微信云托管..."
        echo "📋 部署信息:"
        echo "  - Django版本: 3.2.8 (django-t3qr-009成功配置)"
        echo "  - 端口: 80"
        echo "  - 数据库: MySQL (云托管环境变量)"
        echo "  - 构建方式: Git推送自动触发"
        echo ""
        echo "✅ Git推送已触发微信云托管自动部署"
        echo "📊 请在微信云托管控制台查看部署状态"

    - name: 创建 GitHub Release
      uses: softprops/action-gh-release@v1
      env:
        GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
      with:
        tag_name: v${{ github.run_number }}
        name: Release v${{ github.run_number }}
        body: |
          ## 🚀 新版本发布

          ### 📋 更新内容
          - 自动生成的发布版本
          - 包含最新的功能和修复

          ### 🔗 相关链接
          - [提交历史](https://github.com/wheresleak/wechatcloud/commits/main)
          - [部署状态](https://github.com/wheresleak/wechatcloud/actions)

          ### 📊 部署信息
          - 后端镜像: `${{ secrets.DOCKER_REGISTRY }}/yixintang/backend:${{ github.sha }}`
          - 云托管端口: 80
          - 管理后台: 仅本地开发 (不部署到云托管)
        draft: false
        prerelease: false

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    steps:
    - name: 检出代码
      uses: actions/checkout@v4

    - name: 运行 Trivy 漏洞扫描
      uses: aquasecurity/trivy-action@master
      with:
        scan-type: 'fs'
        scan-ref: '.'
        format: 'sarif'
        output: 'trivy-results.sarif'

    - name: 上传 Trivy 扫描结果
      uses: github/codeql-action/upload-sarif@v3
      with:
        sarif_file: 'trivy-results.sarif'

    - name: 依赖漏洞检查
      run: |
        # 前端依赖检查
        cd admin && npm audit --audit-level=high
        cd ../client && npm audit --audit-level=high
        
        # 后端依赖检查
        cd ../server
        pip install safety
        safety check
