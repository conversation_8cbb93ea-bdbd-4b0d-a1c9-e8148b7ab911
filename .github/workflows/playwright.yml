name: Playwright Tests

on:
  push:
    branches: [ main, develop ]
  pull_request:
    branches: [ main, develop ]

jobs:
  test:
    name: 自动化测试
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v4

    - name: 设置Node.js
      uses: actions/setup-node@v4
      with:
        node-version: 18
        cache: 'npm'
        cache-dependency-path: 'admin/package-lock.json'

    - name: 安装依赖
      run: |
        cd admin
        npm ci
      
    - name: 安装Playwright浏览器
      run: |
        cd admin
        npx playwright install --with-deps

    - name: 运行Mock服务器 (后台)
      run: |
        cd admin
        npm run mock-server &
      env:
        MOCK_PORT: 3001

    - name: 等待Mock服务器启动
      run: sleep 5

    - name: 运行HTTP服务器 (后台)
      run: |
        cd admin
        npx http-server -p 8080 &

    - name: 等待HTTP服务器启动
      run: sleep 2

    - name: 运行Playwright测试
      run: |
        cd admin
        npm run test:auto
    
    - name: 上传测试结果
      if: always()
      uses: actions/upload-artifact@v4
      with:
        name: playwright-report
        path: admin/test-reports/
        retention-days: 30