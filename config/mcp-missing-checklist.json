{"missing_mcp_checklist": {"description": "壹心堂项目缺失MCP服务器检查清单", "last_updated": "2025-01-25", "total_required": 9, "check_status": "pending"}, "required_mcp_servers": {"core_mandatory": {"priority": "🔴 绝对必须", "servers": [{"name": "context7", "package": "@upstash/context7-mcp@latest", "tools_count": 8, "status": "❓ 需检查", "description": "代码库上下文查询"}, {"name": "memory-server", "package": "@modelcontextprotocol/server-memory", "tools_count": 18, "status": "❓ 需检查", "description": "长期记忆和知识图谱"}, {"name": "sequential-thinking", "package": "@modelcontextprotocol/server-sequential-thinking", "tools_count": 4, "status": "❓ 需检查", "description": "思维链分析"}]}, "task_management": {"priority": "🟡 推荐安装", "servers": [{"name": "shrimp-task-manager", "package": "mcp-shrimp-task-manager", "tools_count": 15, "status": "❓ 需检查", "description": "AI任务管理"}, {"name": "interactive-feedback", "package": "mcp-interactive-feedback", "tools_count": 1, "status": "❓ 需检查", "description": "强制反馈收集"}]}, "implementation_verification": {"priority": "🟢 必要时安装", "servers": [{"name": "filesystem", "package": "@modelcontextprotocol/server-filesystem", "tools_count": 12, "status": "❓ 需检查", "description": "文件系统操作"}, {"name": "playwright", "package": "@playwright/mcp@latest", "tools_count": 96, "status": "❓ 需检查", "description": "自动化测试"}, {"name": "chart-generator", "package": "@antv/mcp-server-chart", "tools_count": 25, "status": "❓ 需检查", "description": "数据可视化"}, {"name": "everything", "package": "@modelcontextprotocol/server-everything", "tools_count": 8, "status": "❓ 需检查", "description": "调试和测试"}]}}, "installation_commands": {"description": "安装缺失MCP服务器的命令", "note": "这些是NPX命令，会在Claude Desktop配置中自动安装", "commands": ["npx -y @upstash/context7-mcp@latest", "npx -y @modelcontextprotocol/server-memory", "npx -y @modelcontextprotocol/server-sequential-thinking", "npx -y mcp-shrimp-task-manager", "npx -y mcp-interactive-feedback", "npx -y @modelcontextprotocol/server-filesystem", "npx -y @playwright/mcp@latest", "npx -y @antv/mcp-server-chart", "npx -y @modelcontextprotocol/server-everything"]}, "verification_steps": {"description": "验证MCP服务器安装状态的步骤", "steps": ["1. 检查Claude Desktop配置文件中的mcpServers部分", "2. 重启<PERSON>应用", "3. 在对话中测试每个MCP服务器的工具", "4. 更新此文件中的status字段", "5. 记录任何安装问题到项目日志"]}, "troubleshooting": {"common_issues": ["NPX包下载失败 - 检查网络连接", "权限问题 - 确保有写入权限", "版本冲突 - 使用@latest标签", "配置错误 - 检查JSON语法"], "support_resources": ["MCP官方文档: https://modelcontextprotocol.io/", "项目规范文档: docs/MASTER_DEVELOPMENT_GUIDE.md", "配置示例: config/mcp-config-stable.json"]}}