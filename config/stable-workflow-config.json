{"workflow_name": "Stable 9-Server Development Workflow", "version": "2.0.0", "description": "基于9个稳定MCP服务器的强制开发工作流程", "last_updated": "2025-01-21", "mandatory_execution_order": {"phase_1_information_gathering": {"description": "信息收集阶段 - 绝对强制执行", "steps": [{"step": 1, "tool": "context7", "action": "codebase-retrieval", "description": "查找相关代码和示例参考", "mandatory": true, "timeout": 60, "success_criteria": "获取到相关代码信息和示例"}, {"step": 2, "tool": "memory-server", "action": "search_nodes", "description": "查询历史经验和解决方案", "mandatory": true, "timeout": 30, "success_criteria": "找到相关历史经验或确认无先例"}, {"step": 3, "tool": "sequential-thinking", "action": "sequentialthinking", "description": "分析问题和制定方案", "mandatory": true, "timeout": 120, "success_criteria": "完成问题分析和方案制定"}]}, "phase_2_task_planning": {"description": "任务规划阶段 - 复杂任务推荐执行", "condition": "estimated_time > 2_hours OR complexity_level > 3", "steps": [{"step": 4, "tool": "shrimp-task-manager", "action": "plan_task", "description": "制定任务计划", "mandatory": false, "timeout": 90, "success_criteria": "任务计划制定完成"}, {"step": 5, "tool": "shrimp-task-manager", "action": "split_tasks", "description": "分解复杂任务", "mandatory": false, "timeout": 120, "success_criteria": "任务分解合理且可执行"}]}, "phase_3_implementation": {"description": "实施执行阶段 - 必要时执行", "steps": [{"step": 6, "tool": "filesystem", "action": "read_file", "description": "读取目标文件", "mandatory": false, "timeout": 30, "success_criteria": "成功读取文件内容"}, {"step": 7, "tool": "filesystem", "action": "str-replace-editor", "description": "修改代码文件", "mandatory": false, "timeout": 180, "success_criteria": "代码修改完成且语法正确", "constraints": ["single_file_only", "minimal_changes"]}]}, "phase_4_verification": {"description": "验证测试阶段 - 强制执行", "steps": [{"step": 8, "tool": "playwright", "action": "browser_snapshot", "description": "自动化测试验证", "mandatory": true, "timeout": 300, "success_criteria": "所有测试通过，兼容性验证完成"}]}, "phase_5_feedback_recording": {"description": "反馈记录阶段 - 强制执行", "steps": [{"step": 9, "tool": "interactive-feedback", "action": "interactive-feedback", "description": "收集用户反馈", "mandatory": true, "timeout": 180, "success_criteria": "反馈收集完成"}, {"step": 10, "tool": "memory-server", "action": "add_observations", "description": "记录重要经验", "mandatory": true, "timeout": 60, "success_criteria": "经验记录到知识库"}]}}, "quality_gates": {"pre_execution_checks": ["确认所需MCP服务器正常运行", "验证任务描述清晰完整", "检查项目环境状态"], "mid_execution_checks": ["验证每个阶段的成功标准", "检查代码质量和规范合规性", "确认测试覆盖率达标"], "post_execution_validation": ["确认所有强制步骤已执行", "验证最终交付质量", "检查知识库更新完成"]}, "error_handling": {"step_failure_recovery": {"context7_failure": "尝试使用filesystem直接查看文件", "memory_server_failure": "继续执行但记录查询失败", "sequential_thinking_failure": "使用简化分析继续", "playwright_failure": "手动验证功能后继续", "feedback_failure": "记录反馈收集失败原因"}, "critical_failures": ["context7查询完全失败 - 暂停执行", "文件系统访问失败 - 检查权限", "测试环境不可用 - 修复环境后继续"]}, "performance_metrics": {"execution_time_targets": {"phase_1": "≤ 3分钟", "phase_2": "≤ 5分钟", "phase_3": "≤ 10分钟", "phase_4": "≤ 8分钟", "phase_5": "≤ 4分钟"}, "success_rate_targets": {"mandatory_steps": "100%", "recommended_steps": "≥ 80%", "overall_workflow": "≥ 95%"}}, "project_specific_constraints": {"yixintang_requirements": {"ui_standards": {"color_scheme": "紫色主色调 (#8B5CF6)", "design_style": "毕加索艺术风格", "layout_principle": "黄金比例", "responsive_range": "1024px-4K, 0.8x-1.2x缩放"}, "technical_constraints": {"single_file_modification": true, "immediate_testing": true, "compliance_rate_minimum": 0.9, "test_coverage_minimum": 1.0}, "quality_standards": {"page_load_time": "≤ 3秒", "browser_compatibility": ["Chrome", "Firefox", "Safari"], "resolution_support": ["1024x768", "1366x768", "1920x1080", "2560x1440", "3840x2160"]}}}, "tool_coordination": {"primary_tools": {"context7": "代码库查询和示例参考", "memory-server": "历史经验和知识管理", "sequential-thinking": "问题分析和方案制定"}, "secondary_tools": {"shrimp-task-manager": "复杂任务管理", "interactive-feedback": "反馈收集和用户体验"}, "utility_tools": {"filesystem": "文件操作", "playwright": "自动化测试", "chart-generator": "数据可视化", "everything": "调试和测试"}}, "knowledge_management": {"auto_recording_triggers": ["问题解决方案", "最佳实践发现", "错误和修复记录", "用户反馈和改进建议", "性能优化经验", "兼容性解决方案"], "knowledge_categories": ["技术解决方案", "UI设计模式", "性能优化", "测试策略", "用户体验改进"]}, "continuous_improvement": {"workflow_optimization": {"success_rate_monitoring": "每周统计成功率", "execution_time_analysis": "识别瓶颈步骤", "tool_effectiveness_review": "评估工具使用效果"}, "feedback_integration": {"user_feedback_analysis": "分析用户反馈趋势", "quality_improvement": "基于反馈优化流程", "tool_configuration_tuning": "调整工具参数"}}}