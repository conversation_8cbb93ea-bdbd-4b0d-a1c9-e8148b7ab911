{"workflow_name": "7步强制工作流程检查清单", "version": "1.0.0", "last_updated": "2025-01-21", "description": "基于9个稳定MCP服务器的强制工作流程执行检查清单", "mandatory_workflow_steps": {"step_1": {"name": "Context 7查询", "priority": "绝对强制", "tool": "context7", "action": "codebase-retrieval", "timeout": 60, "success_criteria": "获取到相关代码信息和示例", "failure_action": "禁止继续执行，必须重新查询", "checklist": ["□ 已执行Context 7查询", "□ 获取到相关代码结构", "□ 找到示例参考代码", "□ 理解代码依赖关系", "□ 确认修改影响范围"]}, "step_2": {"name": "memory-server查询", "priority": "绝对强制", "tool": "memory-server", "action": "search_nodes", "timeout": 30, "success_criteria": "找到相关历史经验或确认无先例", "failure_action": "记录查询失败但可继续执行", "checklist": ["□ 已执行memory-server查询", "□ 搜索相关历史经验", "□ 查找类似问题解决方案", "□ 确认最佳实践记录", "□ 避免重复已知错误"]}, "step_3": {"name": "Sequential thinking分析", "priority": "强制", "tool": "sequential-thinking", "action": "sequentialthinking", "timeout": 120, "success_criteria": "完成问题分析和方案制定", "failure_action": "使用简化分析继续", "checklist": ["□ 已执行Sequential thinking分析", "□ 完成问题深度分析", "□ 制定技术实施方案", "□ 评估风险和影响", "□ 确认方案可行性"]}, "step_4": {"name": "shrimp-task-manager规划", "priority": "推荐", "tool": "shrimp-task-manager", "action": "plan_task", "timeout": 90, "condition": "estimated_time > 2_hours OR complexity_level > 3", "success_criteria": "任务计划制定完成", "failure_action": "跳过任务分解，直接执行", "checklist": ["□ 评估任务复杂度", "□ 制定任务计划（如需要）", "□ 分解复杂任务（如需要）", "□ 确定依赖关系", "□ 设定验证标准"]}, "step_5": {"name": "filesystem执行", "priority": "必要时", "tool": "filesystem", "action": "str-replace-editor", "timeout": 180, "success_criteria": "代码修改完成且语法正确", "failure_action": "检查错误并重新修改", "constraints": ["single_file_only", "minimal_changes"], "checklist": ["□ 确认只修改一个文件", "□ 执行最小化变更", "□ 验证语法正确性", "□ 检查功能完整性", "□ 确认无破坏性变更"]}, "step_6": {"name": "Playwright测试", "priority": "强制", "tool": "playwright", "action": "browser_snapshot", "timeout": 300, "success_criteria": "所有测试通过，兼容性验证完成", "failure_action": "手动验证功能后继续", "test_requirements": ["5_resolutions", "cross_browser", "functionality"], "checklist": ["□ 执行功能测试", "□ 验证5种分辨率兼容性", "□ 检查跨浏览器兼容性", "□ 确认用户交互正常", "□ 验证性能标准达标"]}, "step_7": {"name": "interactive-feedback收集", "priority": "强制", "tool": "interactive-feedback", "action": "interactive-feedback", "timeout": 180, "success_criteria": "反馈收集完成", "failure_action": "记录反馈收集失败原因", "checklist": ["□ 收集用户反馈", "□ 生成工作总结报告", "□ 记录重要经验到memory-server", "□ 更新知识库", "□ 完成质量评估"]}}, "quality_gates": {"pre_execution": {"description": "执行前质量检查", "checks": ["确认所需MCP服务器正常运行", "验证任务描述清晰完整", "检查项目环境状态", "确认权限和访问正常"]}, "mid_execution": {"description": "执行中质量检查", "checks": ["验证每个阶段的成功标准", "检查代码质量和规范合规性", "确认测试覆盖率达标", "监控执行时间和效率"]}, "post_execution": {"description": "执行后质量验证", "checks": ["确认所有强制步骤已执行", "验证最终交付质量", "检查知识库更新完成", "确认用户满意度达标"]}}, "success_metrics": {"workflow_completion_rate": "目标: 100%的强制步骤完成", "quality_compliance_rate": "目标: ≥90%的规范合规率", "test_pass_rate": "目标: 100%的测试通过率", "user_satisfaction": "目标: ≥4.0/5.0的满意度", "execution_efficiency": "目标: 在预期时间内完成"}, "escalation_procedures": {"step_failure": {"context7_failure": "尝试使用filesystem直接查看，记录失败原因", "memory_server_failure": "继续执行但记录查询失败，影响经验积累", "sequential_thinking_failure": "使用简化分析继续，可能影响方案质量", "playwright_failure": "手动验证功能，修复测试环境后重试", "feedback_failure": "记录失败原因，影响持续改进"}, "critical_failures": {"multiple_step_failure": "暂停执行，分析根本原因", "quality_standard_not_met": "重新执行相关步骤直到达标", "user_dissatisfaction": "重新分析需求，调整实施方案"}}, "continuous_improvement": {"feedback_integration": "每次执行后收集反馈，优化流程", "metrics_monitoring": "定期监控成功指标，识别改进点", "best_practices_update": "基于经验更新最佳实践", "tool_optimization": "根据使用效果优化工具配置"}}