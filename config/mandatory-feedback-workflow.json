{"workflow_name": "Mandatory Interactive Feedback Workflow", "version": "1.0.0", "description": "强制集成Interactive Feedback环节到所有AI工作流程中", "mandatory_triggers": {"task_completion": {"description": "任务完成后必须触发反馈收集", "required": true, "timeout": 300, "action": "interactive-feedback"}, "code_modification": {"description": "代码修改后必须收集用户反馈", "required": true, "timeout": 180, "action": "interactive-feedback"}, "feature_implementation": {"description": "功能实现后必须评估用户满意度", "required": true, "timeout": 600, "action": "interactive-feedback"}, "problem_resolution": {"description": "问题解决后必须确认解决效果", "required": true, "timeout": 240, "action": "interactive-feedback"}}, "feedback_integration_points": {"pre_task": {"description": "任务开始前收集需求确认", "tools": ["interactive-feedback"], "required_fields": ["task_understanding", "expected_outcome", "priority_level"]}, "mid_task": {"description": "任务执行中收集进度反馈", "tools": ["interactive-feedback", "shrimp-task-manager"], "required_fields": ["progress_status", "encountered_issues", "direction_confirmation"]}, "post_task": {"description": "任务完成后收集质量评估", "tools": ["interactive-feedback", "memory-server"], "required_fields": ["satisfaction_score", "quality_assessment", "improvement_suggestions"]}}, "shrimp_task_manager_integration": {"task_creation": {"description": "使用Shrimp Task Manager创建和管理任务", "mandatory": true, "workflow": ["plan_task - 制定任务计划", "analyze_task - 分析任务需求", "split_tasks - 分解复杂任务", "execute_task - 执行具体任务", "verify_task - 验证任务完成质量"]}, "task_tracking": {"description": "强制使用任务跟踪和状态管理", "mandatory": true, "features": ["list_tasks - 查看任务列表", "get_task_detail - 获取任务详情", "update_task - 更新任务状态", "delete_task - 删除无效任务"]}}, "software_planning_requirements": {"complex_tasks": {"description": "复杂任务必须使用软件规划工具", "threshold": "estimated_time > 2_hours OR complexity_level > 3", "mandatory_tools": ["shrimp-task-manager", "sequential-thinking"], "planning_steps": ["需求分析和理解确认", "技术方案设计和评估", "任务分解和依赖关系分析", "实施计划和时间估算", "风险评估和应对策略", "验收标准和测试计划"]}}, "rag_integration_rules": {"knowledge_queries": {"description": "知识查询优先使用RAG系统", "priority_order": ["memory-server - 查询历史经验", "sqlite-rag - 查询本地知识库", "context7 - 查询代码库上下文", "web-fetch - 获取外部信息"], "fallback_strategy": "如果RAG系统无法提供答案，使用传统搜索方法"}, "knowledge_storage": {"description": "重要信息必须存储到知识库", "auto_storage_triggers": ["问题解决方案", "最佳实践发现", "错误和修复记录", "用户反馈和改进建议"]}}, "workflow_enforcement": {"pre_execution_checks": ["确认任务已在Shrimp Task Manager中创建", "验证Interactive Feedback环节已配置", "检查RAG系统可用性", "确认所需MCP服务器正常运行"], "execution_monitoring": ["实时跟踪任务进度", "监控用户反馈收集状态", "检查知识库更新情况", "验证质量标准符合性"], "post_execution_validation": ["确认Interactive Feedback已收集", "验证任务完成质量", "检查知识库已更新", "生成工作总结报告"]}, "quality_assurance": {"feedback_quality_standards": {"completeness": "反馈必须包含所有必需字段", "specificity": "反馈必须具体明确，避免模糊表述", "actionability": "反馈必须包含可执行的改进建议", "timeliness": "反馈必须在规定时间内收集完成"}, "task_management_standards": {"documentation": "所有任务必须有完整的文档记录", "traceability": "任务执行过程必须可追溯", "verification": "任务完成必须通过验证检查", "knowledge_capture": "重要经验必须记录到知识库"}}, "integration_with_existing_tools": {"mcp_servers_coordination": {"primary_tools": ["shrimp-task-manager - 任务管理核心", "interactive-feedback - 反馈收集核心", "memory-server - 知识管理核心"], "supporting_tools": ["context7 - 代码上下文", "sequential-thinking - 问题分析", "deep-reasoning - 复杂推理", "playwright - 自动化测试"]}, "workflow_integration": {"development_workflow": "开发 → 任务管理 → 反馈收集 → 知识存储", "testing_workflow": "测试 → 结果分析 → 反馈收集 → 改进建议", "maintenance_workflow": "维护 → 问题记录 → 解决方案 → 知识更新"}}, "configuration_requirements": {"environment_setup": {"required_directories": ["./tasks - Shrimp Task Manager存储", "./feedback - Interactive Feedback存储", "./knowledge - RAG知识库存储", "./charts - 数据可视化输出"], "required_permissions": ["文件读写权限", "网络访问权限", "进程管理权限"]}, "mcp_server_dependencies": {"critical": ["mcp-shrimp-task-manager", "mcp-interactive-feedback", "@modelcontextprotocol/server-memory"], "important": ["@upstash/context7-mcp", "@modelcontextprotocol/server-sequential-thinking", "deep-reasoning-mcp"], "optional": ["@modelcontextprotocol/server-sqlite", "@modelcontextprotocol/server-fetch", "@antv/mcp-server-chart"]}}, "success_metrics": {"feedback_collection_rate": "目标: 100%的任务都收集反馈", "task_completion_quality": "目标: 90%以上的任务通过质量验证", "knowledge_capture_rate": "目标: 80%以上的重要经验被记录", "user_satisfaction_score": "目标: 平均满意度≥4.0/5.0"}}