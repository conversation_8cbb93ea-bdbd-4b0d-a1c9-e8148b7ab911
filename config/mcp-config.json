{"mcpServers": {"context7": {"command": "npx", "args": ["-y", "@upstash/context7-mcp@latest"], "env": {"CONTEXT_DEPTH": "deep", "SEARCH_SCOPE": "project"}}, "sequential-thinking": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"], "env": {"THINKING_DEPTH": "5", "ENABLE_REFLECTION": "true"}}, "selenium": {"command": "npx", "args": ["-y", "@selenium/mcp@latest"], "env": {"SELENIUM_HEADLESS": "true", "SELENIUM_TIMEOUT": "30000", "SELENIUM_BROWSER": "chrome"}}, "memory-server": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-memory"], "env": {"MEMORY_STORAGE": "./user-memories", "ENABLE_GRAPH": "true"}}, "filesystem": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Documents/wechatcloud"], "env": {"ALLOWED_DIRECTORIES": "/Users/<USER>/Documents/wechatcloud", "ENABLE_WRITE": "true"}}, "everything": {"command": "npx", "args": ["-y", "@modelcontextprotocol/server-everything"], "env": {"DEBUG_MODE": "true"}}, "shrimp-task-manager": {"command": "npx", "args": ["-y", "mcp-shrimp-task-manager"], "env": {"TASK_STORAGE_PATH": "./tasks", "ENABLE_REFLECTION": "true", "CHAIN_OF_THOUGHT": "true", "ENABLE_PLANNING": "true"}}, "interactive-feedback": {"command": "npx", "args": ["-y", "mcp-interactive-feedback"], "env": {"FEEDBACK_STORAGE": "./feedback", "ENABLE_AI_REPORTS": "true", "REPORT_FORMAT": "markdown", "FORCE_FEEDBACK": "true"}}, "chart-generator": {"command": "npx", "args": ["-y", "@antv/mcp-server-chart"], "env": {"CHART_OUTPUT_DIR": "./charts", "DEFAULT_THEME": "purple", "ENABLE_EXPORT": "true"}}}, "stable_configuration": {"description": "基于9个稳定MCP服务器的生产配置", "total_servers": 9, "total_tools": "187+", "reliability": "100%", "last_updated": "2025-01-21"}, "server_categories": {"core_mandatory": ["context7 - 代码库上下文查询 (8 tools)", "memory-server - 长期记忆和知识图谱 (18 tools)", "sequential-thinking - 思维链分析 (4 tools)"], "task_management": ["shrimp-task-manager - AI任务管理 (15 tools)", "interactive-feedback - 强制反馈收集 (1 tools)"], "implementation_verification": ["filesystem - 文件系统操作 (12 tools)", "playwright - 自动化测试 (96 tools)", "chart-generator - 数据可视化 (25 tools)", "everything - 调试和测试 (8 tools)"]}, "mandatory_workflow": {"description": "基于9个稳定服务器的强制工作流程", "steps": ["1. Context 7查询 - 查找相关代码和示例 (绝对强制)", "2. memory-server查询 - 查询历史经验 (绝对强制)", "3. Sequential thinking分析 - 分析问题和制定方案 (强制)", "4. shrimp-task-manager规划 - 复杂任务分解 (推荐)", "5. filesystem执行 - 文件操作和代码修改 (必要时)", "6. Playwright测试 - 自动化验证 (强制)", "7. interactive-feedback收集 - 反馈和记录 (强制)"]}, "quality_standards": {"code_quality": "规范合规率 ≥ 90%", "test_coverage": "核心功能 100% 覆盖", "performance": "页面加载时间 ≤ 3秒", "compatibility": "支持5种分辨率 (1024px-4K)", "ui_standards": "毕加索艺术风格 + 紫色主色调"}, "project_constraints": {"single_file_modification": "每次只修改一个文件", "immediate_testing": "修改后立即验证功能", "compliance_check": "完成后自检通过率≥90%", "knowledge_recording": "重要经验必须记录到memory-server"}}