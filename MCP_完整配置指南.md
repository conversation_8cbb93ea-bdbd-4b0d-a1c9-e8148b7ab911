# 🔧 MCP完整配置指南

> **📋 文档目的**: 帮助用户在AI助手中完整配置MCP服务器
> **🔄 更新日期**: 2025-01-25
> **🎯 配置文件**: `mcp-settings.json`
> **✅ 环境状态**: 已就绪

## 🚀 配置状态检查

✅ **环境准备完成**
- ✅ Node.js v22.14.0 已安装
- ✅ npm 10.9.2 已安装
- ✅ npx 可用
- ✅ 所有必要目录已创建
- ✅ MCP配置文件已生成

## 📱 支持的AI助手配置

### 1. 🤖 Claude Desktop (推荐)

#### 安装Claude Desktop
```bash
# 下载Claude Desktop应用
# 访问: https://claude.ai/download
# 安装后首次启动
```

#### 配置MCP服务器
1. **打开Claude Desktop设置**
   - 点击左下角齿轮图标
   - 选择 "Settings" 或"设置"

2. **找到MCP配置选项**
   - 查找 "MCP Servers" 或"MCP服务器"
   - 点击 "Edit Config" 或"编辑配置"

3. **导入配置文件**
   ```json
   {
     "mcpServers": {
       "context7": {
         "command": "npx",
         "args": ["-y", "@upstash/context7-mcp@latest"],
         "env": {
           "CONTEXT_DEPTH": "deep",
           "SEARCH_SCOPE": "project"
         }
       },
       "memory-server": {
         "command": "npx",
         "args": ["-y", "@modelcontextprotocol/server-memory"],
         "env": {
           "MEMORY_STORAGE": "./user-memories",
           "ENABLE_GRAPH": "true"
         }
       },
       "sequential-thinking": {
         "command": "npx",
         "args": ["-y", "@modelcontextprotocol/server-sequential-thinking"],
         "env": {
           "THINKING_DEPTH": "5",
           "ENABLE_REFLECTION": "true"
         }
       },
       "shrimp-task-manager": {
         "command": "npx",
         "args": ["-y", "mcp-shrimp-task-manager"],
         "env": {
           "TASK_STORAGE_PATH": "./tasks",
           "ENABLE_REFLECTION": "true",
           "CHAIN_OF_THOUGHT": "true",
           "ENABLE_PLANNING": "true"
         }
       },
       "interactive-feedback": {
         "command": "npx",
         "args": ["-y", "mcp-interactive-feedback"],
         "env": {
           "FEEDBACK_STORAGE": "./feedback",
           "ENABLE_AI_REPORTS": "true",
           "REPORT_FORMAT": "markdown",
           "FORCE_FEEDBACK": "true"
         }
       },
       "filesystem": {
         "command": "npx",
         "args": ["-y", "@modelcontextprotocol/server-filesystem", "/Users/<USER>/Documents/wechatcloud"],
         "env": {
           "ALLOWED_DIRECTORIES": "/Users/<USER>/Documents/wechatcloud",
           "ENABLE_WRITE": "true"
         }
       },
       "playwright": {
         "command": "npx",
         "args": ["-y", "@playwright/mcp@latest"],
         "env": {
           "PLAYWRIGHT_HEADLESS": "true",
           "PLAYWRIGHT_TIMEOUT": "30000"
         }
       },
       "chart-generator": {
         "command": "npx",
         "args": ["-y", "@antv/mcp-server-chart"],
         "env": {
           "CHART_OUTPUT_DIR": "./charts",
           "DEFAULT_THEME": "purple",
           "ENABLE_EXPORT": "true"
         }
       },
       "everything": {
         "command": "npx",
         "args": ["-y", "@modelcontextprotocol/server-everything"],
         "env": {
           "DEBUG_MODE": "true"
         }
       }
     }
   }
   ```

4. **保存并重启**
   - 保存配置文件
   - 完全退出Claude Desktop
   - 重新启动应用

### 2. 🌐 其他AI助手配置

#### Cursor IDE
```json
// 在Cursor设置中添加MCP配置
// 文件位置: ~/.cursor/mcp-config.json
// 复制上述配置内容
```

#### VS Code + Continue
```json
// 在Continue插件设置中配置
// 添加MCP服务器支持
```

## 🔍 配置验证步骤

### 1. 启动验证
```bash
# 在项目目录下测试
cd /Users/<USER>/Documents/wechatcloud

# 测试npx命令
npx -y @upstash/context7-mcp@latest --version
npx -y @modelcontextprotocol/server-memory --version
```

### 2. 功能测试
在AI助手中测试以下功能：

#### Context 7测试
```
用户: "查找Vue组件相关代码"
AI应该能够: 搜索并返回项目中的Vue组件文件
```

#### Memory Server测试
```
用户: "记住这个解决方案：CSS样式冲突用!important解决"
AI应该能够: 保存到长期记忆中
```

#### Task Manager测试
```
用户: "创建一个开发任务：优化管理页面性能"
AI应该能够: 创建结构化任务并分解步骤
```

## 🚨 故障排除

### 常见问题及解决方案

#### 问题1: MCP服务器启动失败
**症状**: AI助手显示MCP连接错误
**解决方案**:
```bash
# 检查网络连接
ping google.com

# 清理npm缓存
npm cache clean --force

# 重新安装
npx -y @upstash/context7-mcp@latest
```

#### 问题2: 权限错误
**症状**: 无法写入文件或创建目录
**解决方案**:
```bash
# 设置目录权限
chmod 755 user-memories tasks feedback charts reports

# 检查目录所有者
ls -la user-memories/
```

#### 问题3: 环境变量不生效
**症状**: MCP服务器功能异常
**解决方案**:
- 检查配置文件JSON语法
- 确认环境变量名称正确
- 重启AI助手应用

#### 问题4: 网络超时
**症状**: npx下载包失败
**解决方案**:
```bash
# 设置npm镜像
npm config set registry https://registry.npmmirror.com/

# 或使用代理
npm config set proxy http://proxy.company.com:8080
```

## 📊 配置完成检查清单

### ✅ 必须完成的步骤
- [ ] Claude Desktop已安装
- [ ] MCP配置已导入
- [ ] AI助手已重启
- [ ] 9个MCP服务器全部可用
- [ ] Context 7功能测试通过
- [ ] Memory Server功能测试通过
- [ ] 文件系统权限正确

### 🎯 可选优化步骤
- [ ] 设置npm镜像加速
- [ ] 配置代理（如需要）
- [ ] 备份配置文件
- [ ] 创建快速启动脚本

## 🔄 维护和更新

### 定期维护
```bash
# 每月执行一次
# 更新MCP服务器
npx -y @upstash/context7-mcp@latest
npx -y @modelcontextprotocol/server-memory

# 清理缓存
npm cache clean --force

# 检查存储空间
du -sh user-memories/ tasks/ feedback/ charts/
```

### 配置备份
```bash
# 备份重要配置
cp mcp-settings.json mcp-settings-backup-$(date +%Y%m%d).json

# 备份记忆数据
tar -czf user-memories-backup-$(date +%Y%m%d).tar.gz user-memories/
```

## 📞 获取帮助

### 项目文档
- 📖 使用指南: `MCP_配置使用指南.md`
- 📋 开发规范: `docs/COMPREHENSIVE_DEVELOPMENT_STANDARDS.md`
- 🎯 使用示例: `docs/MCP_USAGE_EXAMPLES.md`

### 在线资源
- 🌐 MCP官方文档: https://modelcontextprotocol.io/
- 📚 Claude Desktop文档: https://claude.ai/docs
- 🔧 GitHub Issues: 项目仓库问题追踪

---

> **🎉 恭喜！** 您已完成MCP配置，现在可以享受9个强大MCP服务器带来的AI增强体验！
> 
> **💡 下一步**: 开始使用AI助手进行开发，体验Context 7的代码理解、Memory Server的经验记录等强大功能。