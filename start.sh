#!/bin/bash
# 🚀 壹心堂管理系统 - 唯一一键启动脚本
# 支持热更新的开发环境启动 + MCP开发规范体系
#
# 使用方法：
#   ./start.sh          # 启动所有服务（前端+后端）
#   ./start.sh backend  # 仅启动后端
#   ./start.sh frontend # 仅启动前端
#
# 特性：
#   - 自动检测进程是否存在，避免重复启动
#   - 前端支持热更新（npm run dev）
#   - 后端支持热更新（Django runserver）
#   - 端口冲突自动处理

# 🎨 壹心堂菜单按钮风格颜色定义 (与AdminLayout.vue菜单项完全一致)
# 主色调：壹心堂紫色 #8b5cf6
PURPLE_PRIMARY='\033[38;2;139;92;246m'      # 主紫色 #8b5cf6
PURPLE_LIGHT='\033[38;2;168;85;247m'        # 浅紫色 #a855f7
PURPLE_DARK='\033[38;2;124;58;237m'         # 深紫色 #7c3aed
WHITE_BOLD='\033[1;97m'                     # 粗体白色 (菜单文字色)
WHITE='\033[97m'                            # 白色
GREEN='\033[38;2;82;196;26m'                # 成功绿色
YELLOW='\033[38;2;250;173;20m'              # 警告黄色
RED='\033[38;2;245;34;45m'                  # 错误红色
CYAN='\033[38;2;19;194;194m'                # 信息青色

# 🎨 背景和装饰效果
BG_TRANSPARENT='\033[0m'                    # 透明背景 (菜单背景)
BORDER_PURPLE='\033[38;2;139;92;246m'       # 紫色边框色
BOLD='\033[1m'                              # 粗体
UPPERCASE='\033[1m'                         # 大写效果模拟
NC='\033[0m'                                # 重置颜色

# 🎯 项目配置 (与菜单项高度一致)
BACKEND_PORT=8000
FRONTEND_PORT=3000
MINIPROGRAM_PORT=3002
PROJECT_NAME="yixintang"

# 🎯 进程标识文件
PID_DIR=".pids"
BACKEND_PID_FILE="$PID_DIR/backend.pid"
FRONTEND_PID_FILE="$PID_DIR/frontend.pid"
MINIPROGRAM_PID_FILE="$PID_DIR/miniprogram.pid"

# 创建PID目录
mkdir -p "$PID_DIR"

# 🎨 菜单按钮风格的启动标题 (模拟50px高度菜单项)
echo ""
echo -e "${BORDER_PURPLE}╔══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🚀 壹心堂管理系统 - 一键启动${NC}                                                ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}  ${PURPLE_PRIMARY}基于9个稳定MCP服务器的开发规范体系${NC}                                    ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}  ${WHITE}毕加索艺术风格 + 紫色主色调 + 黄金比例布局${NC}                                  ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}╚══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo ""

# 🔍 检查端口是否被占用
check_port() {
    local port=$1
    lsof -ti:$port 2>/dev/null
}

# 🔍 获取进程信息
get_process_info() {
    local pid=$1
    if [ -n "$pid" ]; then
        ps -p $pid -o pid,ppid,command --no-headers 2>/dev/null
    fi
}

# 🔍 检查进程是否属于本项目
is_our_process() {
    local pid=$1
    local process_info=$(get_process_info $pid)

    if [ -z "$process_info" ]; then
        return 1
    fi

    # 检查进程命令是否包含项目相关关键词
    if echo "$process_info" | grep -q -E "(django|runserver|npm.*dev|vue|vite|$PROJECT_NAME)"; then
        return 0
    fi

    # 检查是否是我们启动的进程（通过PID文件）
    if [ -f "$BACKEND_PID_FILE" ] && [ "$(cat $BACKEND_PID_FILE 2>/dev/null)" = "$pid" ]; then
        return 0
    fi
    if [ -f "$FRONTEND_PID_FILE" ] && [ "$(cat $FRONTEND_PID_FILE 2>/dev/null)" = "$pid" ]; then
        return 0
    fi

    return 1
}

# 🛡️ 处理端口冲突
handle_port_conflict() {
    local port=$1
    local service_name=$2
    local pids=$(check_port $port)

    if [ -z "$pids" ]; then
        echo -e "${GREEN}✅ 端口 $port ($service_name) 可用${NC}"
        return 0
    fi

    echo -e "${YELLOW}⚠️  端口 $port ($service_name) 被占用${NC}"

    for pid in $pids; do
        local process_info=$(get_process_info $pid)
        echo -e "${CYAN}   进程 $pid: $process_info${NC}"

        if is_our_process $pid; then
            echo -e "${GREEN}   ✅ 这是本项目进程，保持运行 (支持热更新)${NC}"
        else
            echo -e "${RED}   ❌ 这是其他程序进程，准备清理${NC}"
            kill -9 $pid 2>/dev/null || true
            sleep 1
            if ! kill -0 $pid 2>/dev/null; then
                echo -e "${GREEN}   ✅ 进程 $pid 已清理${NC}"
            else
                echo -e "${RED}   ❌ 进程 $pid 清理失败${NC}"
                return 1
            fi
        fi
    done

    # 再次检查端口
    local remaining_pids=$(check_port $port)
    if [ -z "$remaining_pids" ]; then
        echo -e "${GREEN}✅ 端口 $port ($service_name) 现在可用${NC}"
        return 0
    else
        echo -e "${RED}❌ 端口 $port ($service_name) 仍被占用${NC}"
        return 1
    fi
}

# 🔍 全面端口检查
check_all_ports() {
    echo -e "${BORDER_PURPLE}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BORDER_PURPLE}│${NC} ${WHITE_BOLD}🔍 智能端口冲突检测 (热更新优先)${NC}                                          ${BORDER_PURPLE}│${NC}"
    echo -e "${BORDER_PURPLE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"

    local all_clear=true

    if ! handle_port_conflict $BACKEND_PORT "Django后端"; then
        all_clear=false
    fi

    if ! handle_port_conflict $FRONTEND_PORT "Vue前端"; then
        all_clear=false
    fi

    if ! handle_port_conflict $MINIPROGRAM_PORT "小程序开发"; then
        all_clear=false
    fi

    if [ "$all_clear" = true ]; then
        echo -e "${GREEN}🎉 所有端口检查通过，支持热更新开发${NC}"
        return 0
    else
        echo -e "${RED}❌ 部分端口仍有冲突${NC}"
        return 1
    fi
}

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo -e "${RED}❌ Python3 未安装${NC}"
    exit 1
fi

# 🚨 执行端口冲突检测
check_all_ports || exit 1

# 🎨 菜单按钮风格的状态消息 (模拟菜单项样式)
echo -e "${BORDER_PURPLE}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
echo -e "${BORDER_PURPLE}│${NC} ${WHITE_BOLD}🔄 启动DJANGO后端服务 (热更新模式)${NC}                                        ${BORDER_PURPLE}│${NC}"
echo -e "${BORDER_PURPLE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"

# 检查是否已经运行
if [ -f "$BACKEND_PID_FILE" ]; then
    existing_pid=$(cat "$BACKEND_PID_FILE")
    if kill -0 $existing_pid 2>/dev/null; then
        echo -e "${GREEN}✅ Django后端已在运行 (PID: $existing_pid) - 支持热更新，无需重启${NC}"
        DJANGO_PID=$existing_pid
        cd ..
        # 跳转到前端启动
        if command -v node &> /dev/null; then
            echo -e "${BORDER_PURPLE}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
            echo -e "${BORDER_PURPLE}│${NC} ${WHITE_BOLD}🔄 启动VUE前端服务 (热更新模式)${NC}                                           ${BORDER_PURPLE}│${NC}"
            echo -e "${BORDER_PURPLE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
            cd admin

            # 检查前端是否已经运行
            if [ -f "../$FRONTEND_PID_FILE" ]; then
                frontend_existing_pid=$(cat "../$FRONTEND_PID_FILE")
                if kill -0 $frontend_existing_pid 2>/dev/null; then
                    echo -e "${GREEN}✅ Vue前端已在运行 (PID: $frontend_existing_pid) - 支持热更新，无需重启${NC}"
                    FRONTEND_PID=$frontend_existing_pid
                    cd ..
                    # 跳转到完成信息
                    echo ""
                    echo -e "${BORDER_PURPLE}╔═══════════════════════════════════════════════════════════════════════════════╗${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🎉 壹心堂管理系统启动完成！(热更新模式)${NC}                                    ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}  ${PURPLE_PRIMARY}前后端支持代码热更新，修改即时生效，无需重启${NC}                          ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}╠═══════════════════════════════════════════════════════════════════════════════╣${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🔥 热更新服务地址${NC}                                                       ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}    ${GREEN}🌐 后端API: http://localhost:$BACKEND_PORT/ (Django热重载)${NC}                   ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}    ${GREEN}🎨 前端管理: http://localhost:$FRONTEND_PORT/ (Vue HMR)${NC}                      ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}    ${GREEN}🔍 健康检查: http://localhost:$BACKEND_PORT/health/${NC}                           ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🔥 热更新特性${NC}                                                             ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ Django: Python代码修改自动重载${NC}                                   ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ Vue: 组件/样式实时热更新 (HMR)${NC}                                  ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ 智能端口管理: 自动处理冲突${NC}                                       ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
                    echo -e "${BORDER_PURPLE}╚═══════════════════════════════════════════════════════════════════════════════╝${NC}"
                    echo ""
                    echo -e "${CYAN}💡 提示: 代码修改会自动热更新，无需重启服务${NC}"
                    echo -e "${YELLOW}⚠️  按 Ctrl+C 停止服务${NC}"

                    # 保持运行
                    while true; do
                        sleep 1
                    done
                else
                    rm -f "../$FRONTEND_PID_FILE"
                fi
            fi

            if [ ! -d "node_modules" ]; then
                echo -e "${BORDER_PURPLE}│${NC} ${CYAN}📦 安装前端依赖...${NC}                                                        ${BORDER_PURPLE}│${NC}"
                npm install
            fi

            npm run dev &
            FRONTEND_PID=$!
            echo $FRONTEND_PID > "../$FRONTEND_PID_FILE"
            cd ..

            echo -e "${BORDER_PURPLE}│${NC} ${GREEN}✅ VUE前端启动成功 (PID: $FRONTEND_PID) - 支持代码热更新${NC}                   ${BORDER_PURPLE}│${NC}"
        else
            echo -e "${BORDER_PURPLE}│${NC} ${YELLOW}⚠️  NODE.JS 未安装，跳过前端启动${NC}                                          ${BORDER_PURPLE}│${NC}"
        fi

        # 跳转到完成信息
        echo ""
        echo -e "${BORDER_PURPLE}╔═══════════════════════════════════════════════════════════════════════════════╗${NC}"
        echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
        echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🎉 壹心堂管理系统启动完成！(热更新模式)${NC}                                    ${BORDER_PURPLE}║${NC}"
        echo -e "${BORDER_PURPLE}║${NC}  ${PURPLE_PRIMARY}前后端支持代码热更新，修改即时生效，无需重启${NC}                          ${BORDER_PURPLE}║${NC}"
        echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
        echo -e "${BORDER_PURPLE}╠═══════════════════════════════════════════════════════════════════════════════╣${NC}"
        echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🔥 热更新服务地址${NC}                                                       ${BORDER_PURPLE}║${NC}"
        echo -e "${BORDER_PURPLE}║${NC}    ${GREEN}🌐 后端API: http://localhost:$BACKEND_PORT/ (Django热重载)${NC}                   ${BORDER_PURPLE}║${NC}"
        if command -v node &> /dev/null; then
        echo -e "${BORDER_PURPLE}║${NC}    ${GREEN}🎨 前端管理: http://localhost:$FRONTEND_PORT/ (Vue HMR)${NC}                      ${BORDER_PURPLE}║${NC}"
        fi
        echo -e "${BORDER_PURPLE}║${NC}    ${GREEN}🔍 健康检查: http://localhost:$BACKEND_PORT/health/${NC}                           ${BORDER_PURPLE}║${NC}"
        echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
        echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🔥 热更新特性${NC}                                                             ${BORDER_PURPLE}║${NC}"
        echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ Django: Python代码修改自动重载${NC}                                   ${BORDER_PURPLE}║${NC}"
        if command -v node &> /dev/null; then
        echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ Vue: 组件/样式实时热更新 (HMR)${NC}                                  ${BORDER_PURPLE}║${NC}"
        fi
        echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ 智能端口管理: 自动处理冲突${NC}                                       ${BORDER_PURPLE}║${NC}"
        echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
        echo -e "${BORDER_PURPLE}╚═══════════════════════════════════════════════════════════════════════════════╝${NC}"
        echo ""
        echo -e "${CYAN}💡 提示: 代码修改会自动热更新，无需重启服务${NC}"
        echo -e "${YELLOW}⚠️  按 Ctrl+C 停止服务${NC}"

        # 保持运行
        while true; do
            sleep 1
        done
    else
        rm -f "$BACKEND_PID_FILE"
    fi
fi
cd server

if [ ! -d "venv" ]; then
    echo -e "${BORDER_PURPLE}│${NC} ${YELLOW}⚠️  创建虚拟环境...${NC}                                                        ${BORDER_PURPLE}│${NC}"
    python3 -m venv venv
fi

source venv/bin/activate

# 🎨 菜单按钮风格的依赖安装消息
if [ ! -f ".deps_installed" ]; then
    echo -e "${BORDER_PURPLE}│${NC} ${CYAN}📦 安装PYTHON依赖...${NC}                                                      ${BORDER_PURPLE}│${NC}"
    pip install -r requirements.txt
    touch .deps_installed
fi

# 🎨 菜单按钮风格的数据库迁移消息
echo -e "${BORDER_PURPLE}│${NC} ${CYAN}📊 执行数据库迁移...${NC}                                                      ${BORDER_PURPLE}│${NC}"
python manage.py migrate

# 🎨 菜单按钮风格的服务器启动消息
echo -e "${BORDER_PURPLE}│${NC} ${PURPLE_PRIMARY}🌐 启动DJANGO服务器 (热更新模式)...${NC}                                    ${BORDER_PURPLE}│${NC}"
python manage.py runserver 0.0.0.0:$BACKEND_PORT &
DJANGO_PID=$!
echo $DJANGO_PID > "../$BACKEND_PID_FILE"

cd ..

# 🎨 菜单按钮风格的等待消息
echo -e "${BORDER_PURPLE}│${NC} ${CYAN}⏳ 等待服务器启动...${NC}                                                      ${BORDER_PURPLE}│${NC}"
sleep 5

# 🎨 菜单按钮风格的成功/失败消息
if curl -f http://localhost:$BACKEND_PORT/health/ > /dev/null 2>&1; then
    echo -e "${BORDER_PURPLE}│${NC} ${GREEN}✅ DJANGO后端启动成功${NC}                                                    ${BORDER_PURPLE}│${NC}"
else
    echo -e "${BORDER_PURPLE}│${NC} ${RED}❌ DJANGO后端启动失败${NC}                                                    ${BORDER_PURPLE}│${NC}"
    kill $DJANGO_PID 2>/dev/null
    exit 1
fi

# 🎨 菜单按钮风格的前端启动 (如果Node.js可用)
if command -v node &> /dev/null; then
    echo -e "${BORDER_PURPLE}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BORDER_PURPLE}│${NC} ${WHITE_BOLD}🔄 启动VUE前端服务${NC}                                                        ${BORDER_PURPLE}│${NC}"
    echo -e "${BORDER_PURPLE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
    cd admin

    if [ ! -d "node_modules" ]; then
        echo -e "${BORDER_PURPLE}│${NC} ${CYAN}📦 安装前端依赖...${NC}                                                        ${BORDER_PURPLE}│${NC}"
        npm install
    fi

    # 检查前端是否已经运行
    if [ -f "../$FRONTEND_PID_FILE" ]; then
        frontend_existing_pid=$(cat "../$FRONTEND_PID_FILE")
        if kill -0 $frontend_existing_pid 2>/dev/null; then
            echo -e "${GREEN}✅ Vue前端已在运行 (PID: $frontend_existing_pid) - 支持热更新，无需重启${NC}"
            FRONTEND_PID=$frontend_existing_pid
            cd ..
        else
            rm -f "../$FRONTEND_PID_FILE"
            npm run dev &
            FRONTEND_PID=$!
            echo $FRONTEND_PID > "../$FRONTEND_PID_FILE"
            cd ..
            echo -e "${BORDER_PURPLE}│${NC} ${GREEN}✅ VUE前端启动成功 (PID: $FRONTEND_PID) - 支持代码热更新${NC}               ${BORDER_PURPLE}│${NC}"
        fi
    else
        npm run dev &
        FRONTEND_PID=$!
        echo $FRONTEND_PID > "../$FRONTEND_PID_FILE"
        cd ..
        echo -e "${BORDER_PURPLE}│${NC} ${GREEN}✅ VUE前端启动成功 (PID: $FRONTEND_PID) - 支持代码热更新${NC}               ${BORDER_PURPLE}│${NC}"
    fi
else
    echo -e "${BORDER_PURPLE}│${NC} ${YELLOW}⚠️  NODE.JS 未安装，跳过前端启动${NC}                                          ${BORDER_PURPLE}│${NC}"
fi

# 🎨 菜单按钮风格的MCP检查
echo -e "${BORDER_PURPLE}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
echo -e "${BORDER_PURPLE}│${NC} ${WHITE_BOLD}🔧 检查MCP开发规范体系${NC}                                                    ${BORDER_PURPLE}│${NC}"
echo -e "${BORDER_PURPLE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
if [ -f "scripts/monitor-implementation.py" ]; then
    python3 scripts/monitor-implementation.py --quiet 2>/dev/null || true
fi

# 🎨 菜单按钮风格的启动完成信息 (模拟菜单项布局)
echo ""
echo -e "${BORDER_PURPLE}╔═══════════════════════════════════════════════════════════════════════════════╗${NC}"
echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🎉 壹心堂管理系统启动完成！(热更新模式)${NC}                                    ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}  ${PURPLE_PRIMARY}智能端口检测 + 进程管理 + 前后端热更新支持${NC}                            ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}╠═══════════════════════════════════════════════════════════════════════════════╣${NC}"
echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🔥 热更新服务地址${NC}                                                       ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}    ${GREEN}🌐 后端API: http://localhost:$BACKEND_PORT/ (Django热重载)${NC}                   ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}    ${GREEN}🔧 健康检查: http://localhost:$BACKEND_PORT/health/${NC}                           ${BORDER_PURPLE}║${NC}"
if command -v node &> /dev/null; then
echo -e "${BORDER_PURPLE}║${NC}    ${GREEN}🎨 前端管理: http://localhost:$FRONTEND_PORT/ (Vue HMR)${NC}                      ${BORDER_PURPLE}║${NC}"
fi
echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🔥 热更新特性${NC}                                                             ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ Django: Python代码修改自动重载${NC}                                   ${BORDER_PURPLE}║${NC}"
if command -v node &> /dev/null; then
echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ Vue: 组件/样式实时热更新 (HMR)${NC}                                  ${BORDER_PURPLE}║${NC}"
fi
echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ 智能端口管理: 自动处理冲突${NC}                                       ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🛠️ MCP开发规范体系 (9个稳定服务器)${NC}                                        ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ 9个稳定MCP服务器已配置${NC}                                            ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ 强制7步工作流程已启用${NC}                                             ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_PRIMARY}✅ 质量监控机制已运行${NC}                                               ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🎨 壹心堂项目特色 (与菜单设计一致)${NC}                                         ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_LIGHT}🎭 毕加索艺术风格设计${NC}                                                 ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_LIGHT}🟣 紫色主色调 (#8b5cf6)${NC}                                              ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_LIGHT}📐 黄金比例布局 (1.618)${NC}                                              ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}    ${PURPLE_LIGHT}📱 5种分辨率完美适配 (1024px-4K)${NC}                                    ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}  ${WHITE_BOLD}🔐 开发者账号 (与菜单项字体一致)${NC}                                          ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}    ${CYAN}👤 用户名: ROOT${NC}                                                              ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}    ${CYAN}🔑 密码: 13210583333${NC}                                                        ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}║${NC}                                                                               ${BORDER_PURPLE}║${NC}"
echo -e "${BORDER_PURPLE}╚═══════════════════════════════════════════════════════════════════════════════╝${NC}"
echo ""
echo -e "${CYAN}💡 提示: 代码修改会自动热更新，无需重启服务${NC}"
echo -e "${YELLOW}⚠️  按 Ctrl+C 停止服务${NC}"

# 🎨 菜单按钮风格的清理函数
cleanup() {
    echo ""
    echo -e "${BORDER_PURPLE}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BORDER_PURPLE}│${NC} ${WHITE_BOLD}🔄 正在停止服务...${NC}                                                       ${BORDER_PURPLE}│${NC}"
    echo -e "${BORDER_PURPLE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"

    # 停止Django服务
    if [ ! -z "$DJANGO_PID" ]; then
        kill $DJANGO_PID 2>/dev/null || true
        echo -e "${BORDER_PURPLE}│${NC} ${CYAN}🛑 Django服务已停止 (PID: $DJANGO_PID)${NC}                                  ${BORDER_PURPLE}│${NC}"
    fi
    if [ -f "$BACKEND_PID_FILE" ]; then
        backend_pid=$(cat "$BACKEND_PID_FILE" 2>/dev/null)
        if [ ! -z "$backend_pid" ]; then
            kill $backend_pid 2>/dev/null || true
        fi
        rm -f "$BACKEND_PID_FILE"
    fi

    # 停止Vue前端服务
    if [ ! -z "$FRONTEND_PID" ]; then
        kill $FRONTEND_PID 2>/dev/null || true
        echo -e "${BORDER_PURPLE}│${NC} ${CYAN}🛑 Vue前端已停止 (PID: $FRONTEND_PID)${NC}                                   ${BORDER_PURPLE}│${NC}"
    fi
    if [ -f "$FRONTEND_PID_FILE" ]; then
        frontend_pid=$(cat "$FRONTEND_PID_FILE" 2>/dev/null)
        if [ ! -z "$frontend_pid" ]; then
            kill $frontend_pid 2>/dev/null || true
        fi
        rm -f "$FRONTEND_PID_FILE"
    fi

    # 🎨 菜单按钮风格的端口清理
    lsof -ti:$BACKEND_PORT | xargs kill -9 2>/dev/null || true
    lsof -ti:$FRONTEND_PORT | xargs kill -9 2>/dev/null || true
    lsof -ti:$MINIPROGRAM_PORT | xargs kill -9 2>/dev/null || true

    # 清理PID目录
    rm -rf "$PID_DIR"

    echo -e "${BORDER_PURPLE}┌─────────────────────────────────────────────────────────────────────────────┐${NC}"
    echo -e "${BORDER_PURPLE}│${NC} ${GREEN}✅ 所有服务已停止 - 壹心堂管理系统已关闭${NC}                                   ${BORDER_PURPLE}│${NC}"
    echo -e "${BORDER_PURPLE}└─────────────────────────────────────────────────────────────────────────────┘${NC}"
    exit 0
}

# 设置退出清理
trap cleanup EXIT INT TERM

# 保持运行
while true; do
    sleep 1
done
