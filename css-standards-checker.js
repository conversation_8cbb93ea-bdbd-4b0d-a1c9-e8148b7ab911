#!/usr/bin/env node

/**
 * 🎯 CSS规范化检查脚本
 * 用途: 检查CSS样式重叠、冲突和不规范问题
 * 作者: AI助手
 * 日期: 2025-01-27
 */

const fs = require('fs');
const path = require('path');

// 颜色定义
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

// CSS规范检查规则
const CSS_RULES = {
  // Z-index层级系统
  zIndexLevels: {
    'var(--service-z-base)': 1,
    'var(--service-z-content)': 10,
    'var(--service-z-dropdown)': 100,
    'var(--service-z-toolbar)': 200,
    'var(--service-z-table-header)': 300,
    'var(--service-z-tooltip)': 500,
    'var(--service-z-modal-backdrop)': 1000,
    'var(--service-z-modal)': 1001,
    'var(--service-z-toast)': 2000,
    'var(--service-z-debug)': 9999
  },
  
  // 禁止的硬编码z-index值
  forbiddenZIndex: [1000, 10000, 9999, 999],
  
  // 毛玻璃效果标准
  glassEffectStandards: {
    'backdrop-filter': /blur\(\d+px\)\s+saturate\([\d.]+\)/,
    'background': /rgba\(\d+,\s*\d+,\s*\d+,\s*0\.\d+\)/
  },
  
  // 禁止的!important使用场景
  forbiddenImportant: [
    'z-index',
    'position',
    'display'
  ]
};

/**
 * 检查CSS文件中的样式问题
 */
function checkCSSStandards(filePath) {
  console.log(`${colors.blue}🔍 检查文件: ${filePath}${colors.reset}`);
  
  if (!fs.existsSync(filePath)) {
    console.log(`${colors.red}❌ 文件不存在: ${filePath}${colors.reset}`);
    return false;
  }
  
  const content = fs.readFileSync(filePath, 'utf8');
  const lines = content.split('\n');
  
  let hasErrors = false;
  let hasWarnings = false;
  
  // 检查结果统计
  const results = {
    zIndexIssues: [],
    importantOveruse: [],
    glassEffectIssues: [],
    duplicateProperties: [],
    positioningIssues: []
  };
  
  // 逐行检查
  lines.forEach((line, index) => {
    const lineNum = index + 1;
    const trimmedLine = line.trim();
    
    // 检查硬编码z-index
    const zIndexMatch = trimmedLine.match(/z-index:\s*(\d+)/);
    if (zIndexMatch) {
      const zValue = parseInt(zIndexMatch[1]);
      if (CSS_RULES.forbiddenZIndex.includes(zValue)) {
        results.zIndexIssues.push({
          line: lineNum,
          content: trimmedLine,
          issue: `硬编码z-index值: ${zValue}，应使用CSS变量`
        });
        hasErrors = true;
      }
    }
    
    // 检查!important过度使用
    if (trimmedLine.includes('!important')) {
      const propertyMatch = trimmedLine.match(/([^:]+):\s*[^;]*!important/);
      if (propertyMatch) {
        const property = propertyMatch[1].trim();
        if (CSS_RULES.forbiddenImportant.some(forbidden => property.includes(forbidden))) {
          results.importantOveruse.push({
            line: lineNum,
            content: trimmedLine,
            issue: `不应在${property}属性上使用!important`
          });
          hasWarnings = true;
        }
      }
    }
    
    // 检查毛玻璃效果规范
    if (trimmedLine.includes('backdrop-filter')) {
      if (!CSS_RULES.glassEffectStandards['backdrop-filter'].test(trimmedLine)) {
        results.glassEffectIssues.push({
          line: lineNum,
          content: trimmedLine,
          issue: '毛玻璃效果不符合标准格式: blur(Npx) saturate(N.N)'
        });
        hasWarnings = true;
      }
    }
  });
  
  // 输出检查结果
  console.log(`\n${colors.cyan}📊 检查结果统计:${colors.reset}`);
  
  if (results.zIndexIssues.length > 0) {
    console.log(`${colors.red}❌ Z-index问题: ${results.zIndexIssues.length}个${colors.reset}`);
    results.zIndexIssues.forEach(issue => {
      console.log(`   第${issue.line}行: ${issue.issue}`);
      console.log(`   ${colors.yellow}${issue.content}${colors.reset}`);
    });
  }
  
  if (results.importantOveruse.length > 0) {
    console.log(`${colors.yellow}⚠️  !important过度使用: ${results.importantOveruse.length}个${colors.reset}`);
    results.importantOveruse.forEach(issue => {
      console.log(`   第${issue.line}行: ${issue.issue}`);
    });
  }
  
  if (results.glassEffectIssues.length > 0) {
    console.log(`${colors.yellow}⚠️  毛玻璃效果问题: ${results.glassEffectIssues.length}个${colors.reset}`);
    results.glassEffectIssues.forEach(issue => {
      console.log(`   第${issue.line}行: ${issue.issue}`);
    });
  }
  
  if (!hasErrors && !hasWarnings) {
    console.log(`${colors.green}✅ CSS规范检查通过${colors.reset}`);
    return true;
  }
  
  return !hasErrors; // 只有错误才返回false，警告不影响通过
}

/**
 * 生成CSS规范修复建议
 */
function generateFixSuggestions() {
  console.log(`\n${colors.magenta}💡 CSS规范修复建议:${colors.reset}`);
  
  console.log(`${colors.cyan}1. Z-index层级系统:${colors.reset}`);
  Object.entries(CSS_RULES.zIndexLevels).forEach(([variable, value]) => {
    console.log(`   ${variable} = ${value}`);
  });
  
  console.log(`\n${colors.cyan}2. 毛玻璃效果标准:${colors.reset}`);
  console.log(`   backdrop-filter: blur(25px) saturate(1.5);`);
  console.log(`   background: rgba(255, 255, 255, 0.08);`);
  
  console.log(`\n${colors.cyan}3. 避免!important使用:${colors.reset}`);
  console.log(`   - 提高选择器特异性而不是使用!important`);
  console.log(`   - 使用CSS变量统一管理样式`);
  console.log(`   - 合理的CSS层叠顺序`);
}

// 主函数
function main() {
  console.log(`${colors.blue}🚀 CSS规范化检查工具启动${colors.reset}\n`);
  
  const targetFile = process.argv[2] || 'admin/src/views/ServiceManagement.vue';
  const fullPath = path.resolve(targetFile);
  
  const isValid = checkCSSStandards(fullPath);
  
  if (!isValid) {
    generateFixSuggestions();
    console.log(`\n${colors.red}❌ CSS规范检查失败，请修复上述问题${colors.reset}`);
    process.exit(1);
  } else {
    console.log(`\n${colors.green}🎉 CSS规范检查通过！${colors.reset}`);
    process.exit(0);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { checkCSSStandards, CSS_RULES };
