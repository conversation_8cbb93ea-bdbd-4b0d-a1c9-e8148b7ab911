{"name": "yixintang-wechat-cloud", "version": "3.0.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "yixintang-wechat-cloud", "version": "3.0.0", "license": "MIT", "workspaces": ["admin", "client"], "devDependencies": {"concurrently": "^8.2.2"}, "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}}, "admin": {"name": "yixintang-admin", "version": "1.0.0", "dependencies": {"@ant-design/icons-vue": "^7.0.1", "ant-design-vue": "^3.2.20", "axios": "^1.5.0", "dayjs": "^1.11.13", "echarts": "^5.4.3", "pinia": "^2.1.6", "qrcode": "^1.5.4", "vue": "^3.3.4", "vue-router": "^4.2.4"}, "devDependencies": {"@playwright/test": "^1.53.2", "@rushstack/eslint-patch": "^1.3.3", "@vitejs/plugin-vue": "^4.3.4", "@vue/eslint-config-prettier": "^8.0.0", "eslint": "^8.49.0", "eslint-plugin-vue": "^9.17.0", "less": "^4.3.0", "playwright": "^1.53.2", "prettier": "^3.0.3", "puppeteer": "^24.11.2", "sass": "^1.67.0", "vite": "^4.4.9"}}, "admin/node_modules/@ant-design/colors": {"version": "6.0.0", "license": "MIT", "dependencies": {"@ctrl/tinycolor": "^3.4.0"}}, "admin/node_modules/@ant-design/icons-svg": {"version": "4.4.2", "license": "MIT"}, "admin/node_modules/@ant-design/icons-vue": {"version": "7.0.1", "license": "MIT", "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons-svg": "^4.2.1"}, "peerDependencies": {"vue": ">=3.0.3"}}, "admin/node_modules/@babel/code-frame": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "admin/node_modules/@babel/helper-string-parser": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "admin/node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "admin/node_modules/@babel/parser": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/types": "^7.28.0"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "admin/node_modules/@babel/types": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "admin/node_modules/@ctrl/tinycolor": {"version": "3.6.1", "license": "MIT", "engines": {"node": ">=10"}}, "admin/node_modules/@esbuild/darwin-arm64": {"version": "0.18.20", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "admin/node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "admin/node_modules/@eslint-community/regexpp": {"version": "4.12.1", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "admin/node_modules/@eslint/eslintrc": {"version": "2.1.4", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "admin/node_modules/@eslint/js": {"version": "8.57.1", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "admin/node_modules/@humanwhocodes/config-array": {"version": "0.13.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^2.0.3", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "admin/node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "admin/node_modules/@humanwhocodes/object-schema": {"version": "2.0.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "admin/node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.4", "license": "MIT"}, "admin/node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "admin/node_modules/@nodelib/fs.stat": {"version": "2.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "admin/node_modules/@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "admin/node_modules/@parcel/watcher": {"version": "2.5.1", "dev": true, "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0"}, "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"@parcel/watcher-android-arm64": "2.5.1", "@parcel/watcher-darwin-arm64": "2.5.1", "@parcel/watcher-darwin-x64": "2.5.1", "@parcel/watcher-freebsd-x64": "2.5.1", "@parcel/watcher-linux-arm-glibc": "2.5.1", "@parcel/watcher-linux-arm-musl": "2.5.1", "@parcel/watcher-linux-arm64-glibc": "2.5.1", "@parcel/watcher-linux-arm64-musl": "2.5.1", "@parcel/watcher-linux-x64-glibc": "2.5.1", "@parcel/watcher-linux-x64-musl": "2.5.1", "@parcel/watcher-win32-arm64": "2.5.1", "@parcel/watcher-win32-ia32": "2.5.1", "@parcel/watcher-win32-x64": "2.5.1"}}, "admin/node_modules/@parcel/watcher-darwin-arm64": {"version": "2.5.1", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "admin/node_modules/@pkgr/core": {"version": "0.2.7", "dev": true, "license": "MIT", "engines": {"node": "^12.20.0 || ^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/pkgr"}}, "admin/node_modules/@playwright/test": {"version": "1.53.2", "dev": true, "license": "Apache-2.0", "dependencies": {"playwright": "1.53.2"}, "bin": {"playwright": "cli.js"}, "engines": {"node": ">=18"}}, "admin/node_modules/@puppeteer/browsers": {"version": "2.10.5", "dev": true, "license": "Apache-2.0", "dependencies": {"debug": "^4.4.1", "extract-zip": "^2.0.1", "progress": "^2.0.3", "proxy-agent": "^6.5.0", "semver": "^7.7.2", "tar-fs": "^3.0.8", "yargs": "^17.7.2"}, "bin": {"browsers": "lib/cjs/main-cli.js"}, "engines": {"node": ">=18"}}, "admin/node_modules/@rushstack/eslint-patch": {"version": "1.12.0", "dev": true, "license": "MIT"}, "admin/node_modules/@simonwep/pickr": {"version": "1.8.2", "license": "MIT", "dependencies": {"core-js": "^3.15.1", "nanopop": "^2.1.0"}}, "admin/node_modules/@tootallnate/quickjs-emscripten": {"version": "0.23.0", "dev": true, "license": "MIT"}, "admin/node_modules/@types/node": {"version": "24.0.10", "dev": true, "license": "MIT", "optional": true, "dependencies": {"undici-types": "~7.8.0"}}, "admin/node_modules/@types/yauzl": {"version": "2.10.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"@types/node": "*"}}, "admin/node_modules/@ungap/structured-clone": {"version": "1.3.0", "dev": true, "license": "ISC"}, "admin/node_modules/@vitejs/plugin-vue": {"version": "4.6.2", "dev": true, "license": "MIT", "engines": {"node": "^14.18.0 || >=16.0.0"}, "peerDependencies": {"vite": "^4.0.0 || ^5.0.0", "vue": "^3.2.25"}}, "admin/node_modules/@vue/compiler-core": {"version": "3.5.17", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.5", "@vue/shared": "3.5.17", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.1"}}, "admin/node_modules/@vue/compiler-dom": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/compiler-core": "3.5.17", "@vue/shared": "3.5.17"}}, "admin/node_modules/@vue/compiler-sfc": {"version": "3.5.17", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.5", "@vue/compiler-core": "3.5.17", "@vue/compiler-dom": "3.5.17", "@vue/compiler-ssr": "3.5.17", "@vue/shared": "3.5.17", "estree-walker": "^2.0.2", "magic-string": "^0.30.17", "postcss": "^8.5.6", "source-map-js": "^1.2.1"}}, "admin/node_modules/@vue/compiler-ssr": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.17", "@vue/shared": "3.5.17"}}, "admin/node_modules/@vue/devtools-api": {"version": "6.6.4", "license": "MIT"}, "admin/node_modules/@vue/eslint-config-prettier": {"version": "8.0.0", "dev": true, "license": "MIT", "dependencies": {"eslint-config-prettier": "^8.8.0", "eslint-plugin-prettier": "^5.0.0"}, "peerDependencies": {"eslint": ">= 8.0.0", "prettier": ">= 3.0.0"}}, "admin/node_modules/@vue/reactivity": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/shared": "3.5.17"}}, "admin/node_modules/@vue/runtime-core": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.17", "@vue/shared": "3.5.17"}}, "admin/node_modules/@vue/runtime-dom": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.17", "@vue/runtime-core": "3.5.17", "@vue/shared": "3.5.17", "csstype": "^3.1.3"}}, "admin/node_modules/@vue/server-renderer": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/compiler-ssr": "3.5.17", "@vue/shared": "3.5.17"}, "peerDependencies": {"vue": "3.5.17"}}, "admin/node_modules/@vue/shared": {"version": "3.5.17", "license": "MIT"}, "admin/node_modules/acorn": {"version": "8.15.0", "dev": true, "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "admin/node_modules/acorn-jsx": {"version": "5.3.2", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "admin/node_modules/agent-base": {"version": "7.1.3", "dev": true, "license": "MIT", "engines": {"node": ">= 14"}}, "admin/node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "admin/node_modules/ant-design-vue": {"version": "3.2.20", "license": "MIT", "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons-vue": "^6.1.0", "@babel/runtime": "^7.10.5", "@ctrl/tinycolor": "^3.4.0", "@simonwep/pickr": "~1.8.0", "array-tree-filter": "^2.1.0", "async-validator": "^4.0.0", "dayjs": "^1.10.5", "dom-align": "^1.12.1", "dom-scroll-into-view": "^2.0.0", "lodash": "^4.17.21", "lodash-es": "^4.17.15", "resize-observer-polyfill": "^1.5.1", "scroll-into-view-if-needed": "^2.2.25", "shallow-equal": "^1.0.0", "vue-types": "^3.0.0", "warning": "^4.0.0"}, "engines": {"node": ">=12.22.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/ant-design-vue"}, "peerDependencies": {"vue": ">=3.2.0"}}, "admin/node_modules/ant-design-vue/node_modules/@ant-design/icons-vue": {"version": "6.1.0", "license": "MIT", "dependencies": {"@ant-design/colors": "^6.0.0", "@ant-design/icons-svg": "^4.2.1"}, "peerDependencies": {"vue": ">=3.0.3"}}, "admin/node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "admin/node_modules/array-tree-filter": {"version": "2.1.0", "license": "MIT"}, "admin/node_modules/ast-types": {"version": "0.13.4", "dev": true, "license": "MIT", "dependencies": {"tslib": "^2.0.1"}, "engines": {"node": ">=4"}}, "admin/node_modules/async-validator": {"version": "4.2.5", "license": "MIT"}, "admin/node_modules/asynckit": {"version": "0.4.0", "license": "MIT"}, "admin/node_modules/axios": {"version": "1.10.0", "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "admin/node_modules/b4a": {"version": "1.6.7", "dev": true, "license": "Apache-2.0"}, "admin/node_modules/balanced-match": {"version": "1.0.2", "dev": true, "license": "MIT"}, "admin/node_modules/bare-events": {"version": "2.5.4", "dev": true, "license": "Apache-2.0", "optional": true}, "admin/node_modules/bare-fs": {"version": "4.1.6", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"bare-events": "^2.5.4", "bare-path": "^3.0.0", "bare-stream": "^2.6.4"}, "engines": {"bare": ">=1.16.0"}, "peerDependencies": {"bare-buffer": "*"}, "peerDependenciesMeta": {"bare-buffer": {"optional": true}}}, "admin/node_modules/bare-os": {"version": "3.6.1", "dev": true, "license": "Apache-2.0", "optional": true, "engines": {"bare": ">=1.14.0"}}, "admin/node_modules/bare-path": {"version": "3.0.0", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"bare-os": "^3.0.1"}}, "admin/node_modules/bare-stream": {"version": "2.6.5", "dev": true, "license": "Apache-2.0", "optional": true, "dependencies": {"streamx": "^2.21.0"}, "peerDependencies": {"bare-buffer": "*", "bare-events": "*"}, "peerDependenciesMeta": {"bare-buffer": {"optional": true}, "bare-events": {"optional": true}}}, "admin/node_modules/basic-ftp": {"version": "5.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "admin/node_modules/boolbase": {"version": "1.0.0", "dev": true, "license": "ISC"}, "admin/node_modules/brace-expansion": {"version": "1.1.12", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "admin/node_modules/braces": {"version": "3.0.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "admin/node_modules/buffer-crc32": {"version": "0.2.13", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "admin/node_modules/call-bind-apply-helpers": {"version": "1.0.2", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "admin/node_modules/callsites": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "admin/node_modules/camelcase": {"version": "5.3.1", "license": "MIT", "engines": {"node": ">=6"}}, "admin/node_modules/chokidar": {"version": "4.0.3", "dev": true, "license": "MIT", "dependencies": {"readdirp": "^4.0.1"}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "admin/node_modules/chromium-bidi": {"version": "5.1.0", "dev": true, "license": "Apache-2.0", "dependencies": {"mitt": "^3.0.1", "zod": "^3.24.1"}, "peerDependencies": {"devtools-protocol": "*"}}, "admin/node_modules/combined-stream": {"version": "1.0.8", "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "admin/node_modules/compute-scroll-into-view": {"version": "1.0.20", "license": "MIT"}, "admin/node_modules/concat-map": {"version": "0.0.1", "dev": true, "license": "MIT"}, "admin/node_modules/copy-anything": {"version": "2.0.6", "dev": true, "license": "MIT", "dependencies": {"is-what": "^3.14.1"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "admin/node_modules/core-js": {"version": "3.43.0", "hasInstallScript": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "admin/node_modules/cosmiconfig": {"version": "9.0.0", "dev": true, "license": "MIT", "dependencies": {"env-paths": "^2.2.1", "import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/d-fischer"}, "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "admin/node_modules/cross-spawn": {"version": "7.0.6", "dev": true, "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "admin/node_modules/cssesc": {"version": "3.0.0", "dev": true, "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "admin/node_modules/csstype": {"version": "3.1.3", "license": "MIT"}, "admin/node_modules/data-uri-to-buffer": {"version": "6.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 14"}}, "admin/node_modules/dayjs": {"version": "1.11.13", "license": "MIT"}, "admin/node_modules/debug": {"version": "4.4.1", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "admin/node_modules/decamelize": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "admin/node_modules/deep-is": {"version": "0.1.4", "dev": true, "license": "MIT"}, "admin/node_modules/degenerator": {"version": "5.0.1", "dev": true, "license": "MIT", "dependencies": {"ast-types": "^0.13.4", "escodegen": "^2.1.0", "esprima": "^4.0.1"}, "engines": {"node": ">= 14"}}, "admin/node_modules/delayed-stream": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=0.4.0"}}, "admin/node_modules/detect-libc": {"version": "1.0.3", "dev": true, "license": "Apache-2.0", "optional": true, "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "admin/node_modules/devtools-protocol": {"version": "0.0.1464554", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "admin/node_modules/dijkstrajs": {"version": "1.0.3", "license": "MIT"}, "admin/node_modules/doctrine": {"version": "3.0.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "admin/node_modules/dom-align": {"version": "1.12.4", "license": "MIT"}, "admin/node_modules/dom-scroll-into-view": {"version": "2.0.1", "license": "MIT"}, "admin/node_modules/dunder-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "admin/node_modules/echarts": {"version": "5.6.0", "license": "Apache-2.0", "dependencies": {"tslib": "2.3.0", "zrender": "5.6.1"}}, "admin/node_modules/end-of-stream": {"version": "1.4.5", "dev": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "admin/node_modules/entities": {"version": "4.5.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "admin/node_modules/env-paths": {"version": "2.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "admin/node_modules/errno": {"version": "0.1.8", "dev": true, "license": "MIT", "optional": true, "dependencies": {"prr": "~1.0.1"}, "bin": {"errno": "cli.js"}}, "admin/node_modules/error-ex": {"version": "1.3.2", "dev": true, "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "admin/node_modules/es-define-property": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">= 0.4"}}, "admin/node_modules/es-errors": {"version": "1.3.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "admin/node_modules/es-object-atoms": {"version": "1.1.1", "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "admin/node_modules/es-set-tostringtag": {"version": "2.1.0", "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "admin/node_modules/esbuild": {"version": "0.18.20", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/android-arm": "0.18.20", "@esbuild/android-arm64": "0.18.20", "@esbuild/android-x64": "0.18.20", "@esbuild/darwin-arm64": "0.18.20", "@esbuild/darwin-x64": "0.18.20", "@esbuild/freebsd-arm64": "0.18.20", "@esbuild/freebsd-x64": "0.18.20", "@esbuild/linux-arm": "0.18.20", "@esbuild/linux-arm64": "0.18.20", "@esbuild/linux-ia32": "0.18.20", "@esbuild/linux-loong64": "0.18.20", "@esbuild/linux-mips64el": "0.18.20", "@esbuild/linux-ppc64": "0.18.20", "@esbuild/linux-riscv64": "0.18.20", "@esbuild/linux-s390x": "0.18.20", "@esbuild/linux-x64": "0.18.20", "@esbuild/netbsd-x64": "0.18.20", "@esbuild/openbsd-x64": "0.18.20", "@esbuild/sunos-x64": "0.18.20", "@esbuild/win32-arm64": "0.18.20", "@esbuild/win32-ia32": "0.18.20", "@esbuild/win32-x64": "0.18.20"}}, "admin/node_modules/escape-string-regexp": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "admin/node_modules/escodegen": {"version": "2.1.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esprima": "^4.0.1", "estraverse": "^5.2.0", "esutils": "^2.0.2"}, "bin": {"escodegen": "bin/escodegen.js", "esgenerate": "bin/esgenerate.js"}, "engines": {"node": ">=6.0"}, "optionalDependencies": {"source-map": "~0.6.1"}}, "admin/node_modules/eslint": {"version": "8.57.1", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.4", "@eslint/js": "8.57.1", "@humanwhocodes/config-array": "^0.13.0", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "@ungap/structured-clone": "^1.2.0", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "admin/node_modules/eslint-config-prettier": {"version": "8.10.0", "dev": true, "license": "MIT", "bin": {"eslint-config-prettier": "bin/cli.js"}, "peerDependencies": {"eslint": ">=7.0.0"}}, "admin/node_modules/eslint-plugin-prettier": {"version": "5.5.1", "dev": true, "license": "MIT", "dependencies": {"prettier-linter-helpers": "^1.0.0", "synckit": "^0.11.7"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint-plugin-prettier"}, "peerDependencies": {"@types/eslint": ">=8.0.0", "eslint": ">=8.0.0", "eslint-config-prettier": ">= 7.0.0 <10.0.0 || >=10.1.0", "prettier": ">=3.0.0"}, "peerDependenciesMeta": {"@types/eslint": {"optional": true}, "eslint-config-prettier": {"optional": true}}}, "admin/node_modules/eslint-plugin-vue": {"version": "9.33.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.4.0", "globals": "^13.24.0", "natural-compare": "^1.4.0", "nth-check": "^2.1.1", "postcss-selector-parser": "^6.0.15", "semver": "^7.6.3", "vue-eslint-parser": "^9.4.3", "xml-name-validator": "^4.0.0"}, "engines": {"node": "^14.17.0 || >=16.0.0"}, "peerDependencies": {"eslint": "^6.2.0 || ^7.0.0 || ^8.0.0 || ^9.0.0"}}, "admin/node_modules/eslint-scope": {"version": "7.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "admin/node_modules/eslint-visitor-keys": {"version": "3.4.3", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "admin/node_modules/espree": {"version": "9.6.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "admin/node_modules/esprima": {"version": "4.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "admin/node_modules/esquery": {"version": "1.6.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "admin/node_modules/esrecurse": {"version": "4.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "admin/node_modules/estraverse": {"version": "5.3.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "admin/node_modules/estree-walker": {"version": "2.0.2", "license": "MIT"}, "admin/node_modules/esutils": {"version": "2.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "admin/node_modules/extract-zip": {"version": "2.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"debug": "^4.1.1", "get-stream": "^5.1.0", "yauzl": "^2.10.0"}, "bin": {"extract-zip": "cli.js"}, "engines": {"node": ">= 10.17.0"}, "optionalDependencies": {"@types/yauzl": "^2.9.1"}}, "admin/node_modules/fast-deep-equal": {"version": "3.1.3", "dev": true, "license": "MIT"}, "admin/node_modules/fast-diff": {"version": "1.3.0", "dev": true, "license": "Apache-2.0"}, "admin/node_modules/fast-fifo": {"version": "1.3.2", "dev": true, "license": "MIT"}, "admin/node_modules/fast-json-stable-stringify": {"version": "2.1.0", "dev": true, "license": "MIT"}, "admin/node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "admin/node_modules/fastq": {"version": "1.19.1", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "admin/node_modules/fd-slicer": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"pend": "~1.2.0"}}, "admin/node_modules/file-entry-cache": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "admin/node_modules/fill-range": {"version": "7.1.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "admin/node_modules/find-up": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "admin/node_modules/flat-cache": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "admin/node_modules/flatted": {"version": "3.3.3", "dev": true, "license": "ISC"}, "admin/node_modules/follow-redirects": {"version": "1.15.9", "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "admin/node_modules/form-data": {"version": "4.0.3", "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "admin/node_modules/fs.realpath": {"version": "1.0.0", "dev": true, "license": "ISC"}, "admin/node_modules/fsevents": {"version": "2.3.3", "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "admin/node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "admin/node_modules/get-intrinsic": {"version": "1.3.0", "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "admin/node_modules/get-proto": {"version": "1.0.1", "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "admin/node_modules/get-stream": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "admin/node_modules/get-uri": {"version": "6.0.4", "dev": true, "license": "MIT", "dependencies": {"basic-ftp": "^5.0.2", "data-uri-to-buffer": "^6.0.2", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "admin/node_modules/glob": {"version": "7.2.3", "dev": true, "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "admin/node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "admin/node_modules/globals": {"version": "13.24.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "admin/node_modules/gopd": {"version": "1.2.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "admin/node_modules/graceful-fs": {"version": "4.2.11", "dev": true, "license": "ISC", "optional": true}, "admin/node_modules/graphemer": {"version": "1.4.0", "dev": true, "license": "MIT"}, "admin/node_modules/has-symbols": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "admin/node_modules/has-tostringtag": {"version": "1.0.2", "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "admin/node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "admin/node_modules/http-proxy-agent": {"version": "7.0.2", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "admin/node_modules/https-proxy-agent": {"version": "7.0.6", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "admin/node_modules/iconv-lite": {"version": "0.6.3", "dev": true, "license": "MIT", "optional": true, "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "admin/node_modules/ignore": {"version": "5.3.2", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "admin/node_modules/image-size": {"version": "0.5.5", "dev": true, "license": "MIT", "optional": true, "bin": {"image-size": "bin/image-size.js"}, "engines": {"node": ">=0.10.0"}}, "admin/node_modules/immutable": {"version": "5.1.3", "dev": true, "license": "MIT"}, "admin/node_modules/import-fresh": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "admin/node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "admin/node_modules/inflight": {"version": "1.0.6", "dev": true, "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "admin/node_modules/inherits": {"version": "2.0.4", "dev": true, "license": "ISC"}, "admin/node_modules/ip-address": {"version": "9.0.5", "dev": true, "license": "MIT", "dependencies": {"jsbn": "1.1.0", "sprintf-js": "^1.1.3"}, "engines": {"node": ">= 12"}}, "admin/node_modules/is-arrayish": {"version": "0.2.1", "dev": true, "license": "MIT"}, "admin/node_modules/is-extglob": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "admin/node_modules/is-glob": {"version": "4.0.3", "dev": true, "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "admin/node_modules/is-number": {"version": "7.0.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=0.12.0"}}, "admin/node_modules/is-path-inside": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "admin/node_modules/is-plain-object": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "admin/node_modules/is-what": {"version": "3.14.1", "dev": true, "license": "MIT"}, "admin/node_modules/isexe": {"version": "2.0.0", "dev": true, "license": "ISC"}, "admin/node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "admin/node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "admin/node_modules/jsbn": {"version": "1.1.0", "dev": true, "license": "MIT"}, "admin/node_modules/json-buffer": {"version": "3.0.1", "dev": true, "license": "MIT"}, "admin/node_modules/json-parse-even-better-errors": {"version": "2.3.1", "dev": true, "license": "MIT"}, "admin/node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "admin/node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "admin/node_modules/keyv": {"version": "4.5.4", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "admin/node_modules/less": {"version": "4.3.0", "dev": true, "license": "Apache-2.0", "dependencies": {"copy-anything": "^2.0.1", "parse-node-version": "^1.0.1", "tslib": "^2.3.0"}, "bin": {"lessc": "bin/lessc"}, "engines": {"node": ">=14"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "needle": "^3.1.0", "source-map": "~0.6.0"}}, "admin/node_modules/levn": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "admin/node_modules/lines-and-columns": {"version": "1.2.4", "dev": true, "license": "MIT"}, "admin/node_modules/locate-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "admin/node_modules/lodash-es": {"version": "4.17.21", "license": "MIT"}, "admin/node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "admin/node_modules/loose-envify": {"version": "1.4.0", "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "admin/node_modules/lru-cache": {"version": "7.18.3", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "admin/node_modules/magic-string": {"version": "0.30.17", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "admin/node_modules/make-dir": {"version": "2.1.0", "dev": true, "license": "MIT", "optional": true, "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "engines": {"node": ">=6"}}, "admin/node_modules/make-dir/node_modules/semver": {"version": "5.7.2", "dev": true, "license": "ISC", "optional": true, "bin": {"semver": "bin/semver"}}, "admin/node_modules/math-intrinsics": {"version": "1.1.0", "license": "MIT", "engines": {"node": ">= 0.4"}}, "admin/node_modules/micromatch": {"version": "4.0.8", "dev": true, "license": "MIT", "optional": true, "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "admin/node_modules/mime": {"version": "1.6.0", "dev": true, "license": "MIT", "optional": true, "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "admin/node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "admin/node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "admin/node_modules/minimatch": {"version": "3.1.2", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "admin/node_modules/mitt": {"version": "3.0.1", "dev": true, "license": "MIT"}, "admin/node_modules/ms": {"version": "2.1.3", "dev": true, "license": "MIT"}, "admin/node_modules/nanoid": {"version": "3.3.11", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "admin/node_modules/nanopop": {"version": "2.4.2", "license": "MIT"}, "admin/node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "admin/node_modules/needle": {"version": "3.3.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"iconv-lite": "^0.6.3", "sax": "^1.2.4"}, "bin": {"needle": "bin/needle"}, "engines": {"node": ">= 4.4.x"}}, "admin/node_modules/netmask": {"version": "2.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "admin/node_modules/node-addon-api": {"version": "7.1.1", "dev": true, "license": "MIT", "optional": true}, "admin/node_modules/nth-check": {"version": "2.1.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "admin/node_modules/once": {"version": "1.4.0", "dev": true, "license": "ISC", "dependencies": {"wrappy": "1"}}, "admin/node_modules/optionator": {"version": "0.9.4", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "admin/node_modules/p-limit": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "admin/node_modules/p-locate": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "admin/node_modules/p-try": {"version": "2.2.0", "license": "MIT", "engines": {"node": ">=6"}}, "admin/node_modules/pac-proxy-agent": {"version": "7.2.0", "dev": true, "license": "MIT", "dependencies": {"@tootallnate/quickjs-emscripten": "^0.23.0", "agent-base": "^7.1.2", "debug": "^4.3.4", "get-uri": "^6.0.1", "http-proxy-agent": "^7.0.0", "https-proxy-agent": "^7.0.6", "pac-resolver": "^7.0.1", "socks-proxy-agent": "^8.0.5"}, "engines": {"node": ">= 14"}}, "admin/node_modules/pac-resolver": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"degenerator": "^5.0.0", "netmask": "^2.0.2"}, "engines": {"node": ">= 14"}}, "admin/node_modules/parent-module": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "admin/node_modules/parse-json": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "admin/node_modules/parse-node-version": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "admin/node_modules/path-exists": {"version": "4.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "admin/node_modules/path-is-absolute": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "admin/node_modules/path-key": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "admin/node_modules/pend": {"version": "1.2.0", "dev": true, "license": "MIT"}, "admin/node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "admin/node_modules/picomatch": {"version": "2.3.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "admin/node_modules/pify": {"version": "4.0.1", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=6"}}, "admin/node_modules/pinia": {"version": "2.3.1", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.6.3", "vue-demi": "^0.14.10"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"typescript": ">=4.4.4", "vue": "^2.7.0 || ^3.5.11"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "admin/node_modules/playwright": {"version": "1.53.2", "dev": true, "license": "Apache-2.0", "dependencies": {"playwright-core": "1.53.2"}, "bin": {"playwright": "cli.js"}, "engines": {"node": ">=18"}, "optionalDependencies": {"fsevents": "2.3.2"}}, "admin/node_modules/playwright-core": {"version": "1.53.2", "dev": true, "license": "Apache-2.0", "bin": {"playwright-core": "cli.js"}, "engines": {"node": ">=18"}}, "admin/node_modules/playwright/node_modules/fsevents": {"version": "2.3.2", "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "admin/node_modules/pngjs": {"version": "5.0.0", "license": "MIT", "engines": {"node": ">=10.13.0"}}, "admin/node_modules/postcss": {"version": "8.5.6", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "admin/node_modules/postcss-selector-parser": {"version": "6.1.2", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "admin/node_modules/prelude-ls": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "admin/node_modules/prettier": {"version": "3.6.2", "dev": true, "license": "MIT", "bin": {"prettier": "bin/prettier.cjs"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/prettier/prettier?sponsor=1"}}, "admin/node_modules/prettier-linter-helpers": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"fast-diff": "^1.1.2"}, "engines": {"node": ">=6.0.0"}}, "admin/node_modules/progress": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "admin/node_modules/proxy-agent": {"version": "6.5.0", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "^4.3.4", "http-proxy-agent": "^7.0.1", "https-proxy-agent": "^7.0.6", "lru-cache": "^7.14.1", "pac-proxy-agent": "^7.1.0", "proxy-from-env": "^1.1.0", "socks-proxy-agent": "^8.0.5"}, "engines": {"node": ">= 14"}}, "admin/node_modules/proxy-from-env": {"version": "1.1.0", "license": "MIT"}, "admin/node_modules/prr": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true}, "admin/node_modules/pump": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "admin/node_modules/punycode": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "admin/node_modules/puppeteer": {"version": "24.11.2", "dev": true, "hasInstallScript": true, "license": "Apache-2.0", "dependencies": {"@puppeteer/browsers": "2.10.5", "chromium-bidi": "5.1.0", "cosmiconfig": "^9.0.0", "devtools-protocol": "0.0.1464554", "puppeteer-core": "24.11.2", "typed-query-selector": "^2.12.0"}, "bin": {"puppeteer": "lib/cjs/puppeteer/node/cli.js"}, "engines": {"node": ">=18"}}, "admin/node_modules/puppeteer-core": {"version": "24.11.2", "dev": true, "license": "Apache-2.0", "dependencies": {"@puppeteer/browsers": "2.10.5", "chromium-bidi": "5.1.0", "debug": "^4.4.1", "devtools-protocol": "0.0.1464554", "typed-query-selector": "^2.12.0", "ws": "^8.18.3"}, "engines": {"node": ">=18"}}, "admin/node_modules/qrcode": {"version": "1.5.4", "license": "MIT", "dependencies": {"dijkstrajs": "^1.0.1", "pngjs": "^5.0.0", "yargs": "^15.3.1"}, "bin": {"qrcode": "bin/qrcode"}, "engines": {"node": ">=10.13.0"}}, "admin/node_modules/qrcode/node_modules/cliui": {"version": "6.0.0", "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^6.2.0"}}, "admin/node_modules/qrcode/node_modules/find-up": {"version": "4.1.0", "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "admin/node_modules/qrcode/node_modules/locate-path": {"version": "5.0.0", "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "admin/node_modules/qrcode/node_modules/p-limit": {"version": "2.3.0", "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "admin/node_modules/qrcode/node_modules/p-locate": {"version": "4.1.0", "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "admin/node_modules/qrcode/node_modules/wrap-ansi": {"version": "6.2.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "admin/node_modules/qrcode/node_modules/y18n": {"version": "4.0.3", "license": "ISC"}, "admin/node_modules/qrcode/node_modules/yargs": {"version": "15.4.1", "license": "MIT", "dependencies": {"cliui": "^6.0.0", "decamelize": "^1.2.0", "find-up": "^4.1.0", "get-caller-file": "^2.0.1", "require-directory": "^2.1.1", "require-main-filename": "^2.0.0", "set-blocking": "^2.0.0", "string-width": "^4.2.0", "which-module": "^2.0.0", "y18n": "^4.0.0", "yargs-parser": "^18.1.2"}, "engines": {"node": ">=8"}}, "admin/node_modules/qrcode/node_modules/yargs-parser": {"version": "18.1.3", "license": "ISC", "dependencies": {"camelcase": "^5.0.0", "decamelize": "^1.2.0"}, "engines": {"node": ">=6"}}, "admin/node_modules/queue-microtask": {"version": "1.2.3", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "admin/node_modules/readdirp": {"version": "4.1.2", "dev": true, "license": "MIT", "engines": {"node": ">= 14.18.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}, "admin/node_modules/require-main-filename": {"version": "2.0.0", "license": "ISC"}, "admin/node_modules/resize-observer-polyfill": {"version": "1.5.1", "license": "MIT"}, "admin/node_modules/resolve-from": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "admin/node_modules/reusify": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "admin/node_modules/rimraf": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "admin/node_modules/rollup": {"version": "3.29.5", "dev": true, "license": "MIT", "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "admin/node_modules/run-parallel": {"version": "1.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "admin/node_modules/safer-buffer": {"version": "2.1.2", "dev": true, "license": "MIT", "optional": true}, "admin/node_modules/sass": {"version": "1.89.2", "dev": true, "license": "MIT", "dependencies": {"chokidar": "^4.0.0", "immutable": "^5.0.2", "source-map-js": ">=0.6.2 <2.0.0"}, "bin": {"sass": "sass.js"}, "engines": {"node": ">=14.0.0"}, "optionalDependencies": {"@parcel/watcher": "^2.4.1"}}, "admin/node_modules/sax": {"version": "1.4.1", "dev": true, "license": "ISC", "optional": true}, "admin/node_modules/scroll-into-view-if-needed": {"version": "2.2.31", "license": "MIT", "dependencies": {"compute-scroll-into-view": "^1.0.20"}}, "admin/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "admin/node_modules/set-blocking": {"version": "2.0.0", "license": "ISC"}, "admin/node_modules/shallow-equal": {"version": "1.2.1", "license": "MIT"}, "admin/node_modules/shebang-command": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "admin/node_modules/shebang-regex": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "admin/node_modules/smart-buffer": {"version": "4.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 6.0.0", "npm": ">= 3.0.0"}}, "admin/node_modules/socks": {"version": "2.8.5", "dev": true, "license": "MIT", "dependencies": {"ip-address": "^9.0.5", "smart-buffer": "^4.2.0"}, "engines": {"node": ">= 10.0.0", "npm": ">= 3.0.0"}}, "admin/node_modules/socks-proxy-agent": {"version": "8.0.5", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "^4.3.4", "socks": "^2.8.3"}, "engines": {"node": ">= 14"}}, "admin/node_modules/source-map": {"version": "0.6.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "optional": true, "engines": {"node": ">=0.10.0"}}, "admin/node_modules/source-map-js": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "admin/node_modules/sprintf-js": {"version": "1.1.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "admin/node_modules/streamx": {"version": "2.22.1", "dev": true, "license": "MIT", "dependencies": {"fast-fifo": "^1.3.2", "text-decoder": "^1.1.0"}, "optionalDependencies": {"bare-events": "^2.2.0"}}, "admin/node_modules/strip-json-comments": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "admin/node_modules/synckit": {"version": "0.11.8", "dev": true, "license": "MIT", "dependencies": {"@pkgr/core": "^0.2.4"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/synckit"}}, "admin/node_modules/tar-fs": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"pump": "^3.0.0", "tar-stream": "^3.1.5"}, "optionalDependencies": {"bare-fs": "^4.0.1", "bare-path": "^3.0.0"}}, "admin/node_modules/tar-stream": {"version": "3.1.7", "dev": true, "license": "MIT", "dependencies": {"b4a": "^1.6.4", "fast-fifo": "^1.2.0", "streamx": "^2.15.0"}}, "admin/node_modules/text-decoder": {"version": "1.2.3", "dev": true, "license": "Apache-2.0", "dependencies": {"b4a": "^1.6.4"}}, "admin/node_modules/text-table": {"version": "0.2.0", "dev": true, "license": "MIT"}, "admin/node_modules/to-regex-range": {"version": "5.0.1", "dev": true, "license": "MIT", "optional": true, "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "admin/node_modules/tslib": {"version": "2.3.0", "license": "0BSD"}, "admin/node_modules/type-check": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "admin/node_modules/type-fest": {"version": "0.20.2", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "admin/node_modules/typed-query-selector": {"version": "2.12.0", "dev": true, "license": "MIT"}, "admin/node_modules/undici-types": {"version": "7.8.0", "dev": true, "license": "MIT", "optional": true}, "admin/node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "admin/node_modules/util-deprecate": {"version": "1.0.2", "dev": true, "license": "MIT"}, "admin/node_modules/vite": {"version": "4.5.14", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.18.10", "postcss": "^8.4.27", "rollup": "^3.27.1"}, "bin": {"vite": "bin/vite.js"}, "engines": {"node": "^14.18.0 || >=16.0.0"}, "funding": {"url": "https://github.com/vitejs/vite?sponsor=1"}, "optionalDependencies": {"fsevents": "~2.3.2"}, "peerDependencies": {"@types/node": ">= 14", "less": "*", "lightningcss": "^1.21.0", "sass": "*", "stylus": "*", "sugarss": "*", "terser": "^5.4.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "less": {"optional": true}, "lightningcss": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}, "sugarss": {"optional": true}, "terser": {"optional": true}}}, "admin/node_modules/vue": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.17", "@vue/compiler-sfc": "3.5.17", "@vue/runtime-dom": "3.5.17", "@vue/server-renderer": "3.5.17", "@vue/shared": "3.5.17"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "admin/node_modules/vue-demi": {"version": "0.14.10", "hasInstallScript": true, "license": "MIT", "bin": {"vue-demi-fix": "bin/vue-demi-fix.js", "vue-demi-switch": "bin/vue-demi-switch.js"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/antfu"}, "peerDependencies": {"@vue/composition-api": "^1.0.0-rc.1", "vue": "^3.0.0-0 || ^2.6.0"}, "peerDependenciesMeta": {"@vue/composition-api": {"optional": true}}}, "admin/node_modules/vue-eslint-parser": {"version": "9.4.3", "dev": true, "license": "MIT", "dependencies": {"debug": "^4.3.4", "eslint-scope": "^7.1.1", "eslint-visitor-keys": "^3.3.0", "espree": "^9.3.1", "esquery": "^1.4.0", "lodash": "^4.17.21", "semver": "^7.3.6"}, "engines": {"node": "^14.17.0 || >=16.0.0"}, "funding": {"url": "https://github.com/sponsors/mysticatea"}, "peerDependencies": {"eslint": ">=6.0.0"}}, "admin/node_modules/vue-router": {"version": "4.5.1", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.6.4"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"vue": "^3.2.0"}}, "admin/node_modules/vue-types": {"version": "3.0.2", "license": "MIT", "dependencies": {"is-plain-object": "3.0.1"}, "engines": {"node": ">=10.15.0"}, "peerDependencies": {"vue": "^3.0.0"}}, "admin/node_modules/warning": {"version": "4.0.3", "license": "MIT", "dependencies": {"loose-envify": "^1.0.0"}}, "admin/node_modules/which": {"version": "2.0.2", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "admin/node_modules/which-module": {"version": "2.0.1", "license": "ISC"}, "admin/node_modules/word-wrap": {"version": "1.2.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "admin/node_modules/wrappy": {"version": "1.0.2", "dev": true, "license": "ISC"}, "admin/node_modules/ws": {"version": "8.18.3", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "admin/node_modules/xml-name-validator": {"version": "4.0.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12"}}, "admin/node_modules/yauzl": {"version": "2.10.0", "dev": true, "license": "MIT", "dependencies": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "admin/node_modules/yocto-queue": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "admin/node_modules/zod": {"version": "3.25.74", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/colinhacks"}}, "admin/node_modules/zrender": {"version": "5.6.1", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tslib": "2.3.0"}}, "client": {"name": "yixintang-miniapp", "version": "1.0.0", "dependencies": {"@babel/runtime": "^7.7.7", "@cloudbase/js-sdk": "^1.7.2", "@tarojs/cli": "^4.0.6", "@tarojs/components": "^4.0.6", "@tarojs/helper": "^4.0.6", "@tarojs/plugin-framework-vue3": "^4.0.6", "@tarojs/plugin-platform-weapp": "^4.0.6", "@tarojs/runtime": "^4.0.6", "@tarojs/shared": "^4.0.6", "@tarojs/taro": "^4.0.6", "@vue/compiler-sfc": "^3.5.17", "vue": "^3.3.0", "vue-router": "^4.0.0", "vuex": "^4.0.0"}, "devDependencies": {"@babel/core": "^7.8.0", "@tarojs/cli": "^4.0.6", "@tarojs/webpack5-runner": "^4.0.6", "@types/webpack-env": "^1.13.6", "@typescript-eslint/eslint-plugin": "^5.20.0", "@typescript-eslint/parser": "^5.20.0", "babel-preset-taro": "^4.0.6", "chalk": "^4.1.2", "eslint": "^8.12.0", "eslint-config-taro": "^4.0.6", "eslint-plugin-import": "^2.12.0", "eslint-plugin-react": "^7.8.2", "eslint-plugin-react-hooks": "^4.2.0", "stylelint": "^14.4.0", "typescript": "^4.1.0", "vue-loader": "^17.4.2", "webpack": "^5.99.9", "webpack-cli": "^5.1.4", "wx-server-sdk": "^2.6.3"}}, "client/node_modules/@ampproject/remapping": {"version": "2.3.0", "license": "Apache-2.0", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "client/node_modules/@asamuzakjp/css-color": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"@csstools/css-calc": "^2.1.3", "@csstools/css-color-parser": "^3.0.9", "@csstools/css-parser-algorithms": "^3.0.4", "@csstools/css-tokenizer": "^3.0.3", "lru-cache": "^10.4.3"}}, "client/node_modules/@asamuzakjp/css-color/node_modules/lru-cache": {"version": "10.4.3", "dev": true, "license": "ISC"}, "client/node_modules/@babel/code-frame": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/compat-data": {"version": "7.28.0", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/core": {"version": "7.28.0", "license": "MIT", "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.6", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/traverse": "^7.28.0", "@babel/types": "^7.28.0", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "client/node_modules/@babel/eslint-parser": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@nicolo-ribaudo/eslint-scope-5-internals": "5.1.1-v1", "eslint-visitor-keys": "^2.1.0", "semver": "^6.3.1"}, "engines": {"node": "^10.13.0 || ^12.13.0 || >=14.0.0"}, "peerDependencies": {"@babel/core": "^7.11.0", "eslint": "^7.5.0 || ^8.0.0 || ^9.0.0"}}, "client/node_modules/@babel/eslint-parser/node_modules/eslint-visitor-keys": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=10"}}, "client/node_modules/@babel/generator": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/parser": "^7.28.0", "@babel/types": "^7.28.0", "@jridgewell/gen-mapping": "^0.3.12", "@jridgewell/trace-mapping": "^0.3.28", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/helper-annotate-as-pure": {"version": "7.27.3", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.3"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/helper-create-class-features-plugin": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/traverse": "^7.27.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/helper-create-regexp-features-plugin": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "regexpu-core": "^6.2.0", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/helper-define-polyfill-provider": {"version": "0.6.5", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "debug": "^4.4.1", "lodash.debounce": "^4.0.8", "resolve": "^1.22.10"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "client/node_modules/@babel/helper-globals": {"version": "7.28.0", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/helper-member-expression-to-functions": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/helper-module-imports": {"version": "7.27.1", "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/helper-optimise-call-expression": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/helper-remap-async-to-generator": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-wrap-function": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/helper-replace-supers": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-member-expression-to-functions": "^7.27.1", "@babel/helper-optimise-call-expression": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/helper-skip-transparent-expression-wrappers": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/helper-string-parser": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/helper-validator-option": {"version": "7.27.1", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/helper-wrap-function": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/template": "^7.27.1", "@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/helpers": {"version": "7.27.6", "license": "MIT", "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.6"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/parser": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/types": "^7.28.0"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "client/node_modules/@babel/plugin-bugfix-firefox-class-in-computed-class-key": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/plugin-bugfix-safari-class-field-initializer-scope": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.13.0"}}, "client/node_modules/@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/plugin-proposal-decorators": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-syntax-decorators": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-proposal-private-property-in-object": {"version": "7.21.0-placeholder-for-preset-env.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-syntax-decorators": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-syntax-import-assertions": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-syntax-jsx": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-syntax-typescript": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-syntax-unicode-sets-regex": {"version": "7.18.6", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.18.6", "@babel/helper-plugin-utils": "^7.18.6"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/plugin-transform-arrow-functions": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-async-generator-functions": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1", "@babel/traverse": "^7.28.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-async-to-generator": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-remap-async-to-generator": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-block-scoped-functions": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-block-scoping": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-class-properties": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-class-static-block": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.12.0"}}, "client/node_modules/@babel/plugin-transform-classes": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-globals": "^7.28.0", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1", "@babel/traverse": "^7.28.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-computed-properties": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/template": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-destructuring": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.28.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-dotall-regex": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-duplicate-keys": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-duplicate-named-capturing-groups-regex": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/plugin-transform-dynamic-import": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-explicit-resource-management": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.28.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-exponentiation-operator": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-export-namespace-from": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-for-of": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-function-name": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-json-strings": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-literals": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-logical-assignment-operators": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-member-expression-literals": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-modules-amd": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-modules-commonjs": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-modules-systemjs": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-modules-umd": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-transforms": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-named-capturing-groups-regex": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/plugin-transform-new-target": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-nullish-coalescing-operator": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-numeric-separator": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-object-rest-spread": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.28.0", "@babel/plugin-transform-parameters": "^7.27.7", "@babel/traverse": "^7.28.0"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-object-super": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-replace-supers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-optional-catch-binding": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-optional-chaining": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-parameters": {"version": "7.27.7", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-private-methods": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-private-property-in-object": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.1", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-property-literals": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-regenerator": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-regexp-modifiers": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/plugin-transform-reserved-words": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-runtime": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "babel-plugin-polyfill-corejs2": "^0.4.14", "babel-plugin-polyfill-corejs3": "^0.13.0", "babel-plugin-polyfill-regenerator": "^0.6.5", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-shorthand-properties": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-spread": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-sticky-regex": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-template-literals": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-typeof-symbol": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-typescript": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-annotate-as-pure": "^7.27.3", "@babel/helper-create-class-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-skip-transparent-expression-wrappers": "^7.27.1", "@babel/plugin-syntax-typescript": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-unicode-escapes": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-unicode-property-regex": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-unicode-regex": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/plugin-transform-unicode-sets-regex": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-create-regexp-features-plugin": "^7.27.1", "@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "client/node_modules/@babel/preset-env": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.28.0", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-bugfix-firefox-class-in-computed-class-key": "^7.27.1", "@babel/plugin-bugfix-safari-class-field-initializer-scope": "^7.27.1", "@babel/plugin-bugfix-safari-id-destructuring-collision-in-function-expression": "^7.27.1", "@babel/plugin-bugfix-v8-spread-parameters-in-optional-chaining": "^7.27.1", "@babel/plugin-bugfix-v8-static-class-fields-redefine-readonly": "^7.27.1", "@babel/plugin-proposal-private-property-in-object": "7.21.0-placeholder-for-preset-env.2", "@babel/plugin-syntax-import-assertions": "^7.27.1", "@babel/plugin-syntax-import-attributes": "^7.27.1", "@babel/plugin-syntax-unicode-sets-regex": "^7.18.6", "@babel/plugin-transform-arrow-functions": "^7.27.1", "@babel/plugin-transform-async-generator-functions": "^7.28.0", "@babel/plugin-transform-async-to-generator": "^7.27.1", "@babel/plugin-transform-block-scoped-functions": "^7.27.1", "@babel/plugin-transform-block-scoping": "^7.28.0", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-class-static-block": "^7.27.1", "@babel/plugin-transform-classes": "^7.28.0", "@babel/plugin-transform-computed-properties": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.28.0", "@babel/plugin-transform-dotall-regex": "^7.27.1", "@babel/plugin-transform-duplicate-keys": "^7.27.1", "@babel/plugin-transform-duplicate-named-capturing-groups-regex": "^7.27.1", "@babel/plugin-transform-dynamic-import": "^7.27.1", "@babel/plugin-transform-explicit-resource-management": "^7.28.0", "@babel/plugin-transform-exponentiation-operator": "^7.27.1", "@babel/plugin-transform-export-namespace-from": "^7.27.1", "@babel/plugin-transform-for-of": "^7.27.1", "@babel/plugin-transform-function-name": "^7.27.1", "@babel/plugin-transform-json-strings": "^7.27.1", "@babel/plugin-transform-literals": "^7.27.1", "@babel/plugin-transform-logical-assignment-operators": "^7.27.1", "@babel/plugin-transform-member-expression-literals": "^7.27.1", "@babel/plugin-transform-modules-amd": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-modules-systemjs": "^7.27.1", "@babel/plugin-transform-modules-umd": "^7.27.1", "@babel/plugin-transform-named-capturing-groups-regex": "^7.27.1", "@babel/plugin-transform-new-target": "^7.27.1", "@babel/plugin-transform-nullish-coalescing-operator": "^7.27.1", "@babel/plugin-transform-numeric-separator": "^7.27.1", "@babel/plugin-transform-object-rest-spread": "^7.28.0", "@babel/plugin-transform-object-super": "^7.27.1", "@babel/plugin-transform-optional-catch-binding": "^7.27.1", "@babel/plugin-transform-optional-chaining": "^7.27.1", "@babel/plugin-transform-parameters": "^7.27.7", "@babel/plugin-transform-private-methods": "^7.27.1", "@babel/plugin-transform-private-property-in-object": "^7.27.1", "@babel/plugin-transform-property-literals": "^7.27.1", "@babel/plugin-transform-regenerator": "^7.28.0", "@babel/plugin-transform-regexp-modifiers": "^7.27.1", "@babel/plugin-transform-reserved-words": "^7.27.1", "@babel/plugin-transform-shorthand-properties": "^7.27.1", "@babel/plugin-transform-spread": "^7.27.1", "@babel/plugin-transform-sticky-regex": "^7.27.1", "@babel/plugin-transform-template-literals": "^7.27.1", "@babel/plugin-transform-typeof-symbol": "^7.27.1", "@babel/plugin-transform-unicode-escapes": "^7.27.1", "@babel/plugin-transform-unicode-property-regex": "^7.27.1", "@babel/plugin-transform-unicode-regex": "^7.27.1", "@babel/plugin-transform-unicode-sets-regex": "^7.27.1", "@babel/preset-modules": "0.1.6-no-external-plugins", "babel-plugin-polyfill-corejs2": "^0.4.14", "babel-plugin-polyfill-corejs3": "^0.13.0", "babel-plugin-polyfill-regenerator": "^0.6.5", "core-js-compat": "^3.43.0", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/preset-modules": {"version": "0.1.6-no-external-plugins", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/types": "^7.4.4", "esutils": "^2.0.2"}, "peerDependencies": {"@babel/core": "^7.0.0-0 || ^8.0.0-0 <8.0.0"}}, "client/node_modules/@babel/preset-typescript": {"version": "7.27.1", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.27.1", "@babel/helper-validator-option": "^7.27.1", "@babel/plugin-syntax-jsx": "^7.27.1", "@babel/plugin-transform-modules-commonjs": "^7.27.1", "@babel/plugin-transform-typescript": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/@babel/runtime-corejs3": {"version": "7.28.0", "dev": true, "license": "MIT", "dependencies": {"core-js-pure": "^3.43.0"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/template": {"version": "7.27.2", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/traverse": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.28.0", "@babel/helper-globals": "^7.28.0", "@babel/parser": "^7.28.0", "@babel/template": "^7.27.2", "@babel/types": "^7.28.0", "debug": "^4.3.1"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@babel/types": {"version": "7.28.0", "license": "MIT", "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/@cloudbase/adapter-interface": {"version": "0.4.1", "license": "ISC"}, "client/node_modules/@cloudbase/analytics": {"version": "1.8.4", "license": "ISC", "dependencies": {"@cloudbase/functions": "^1.8.4", "@cloudbase/types": "^1.8.4"}}, "client/node_modules/@cloudbase/app": {"version": "1.8.4", "license": "ISC", "dependencies": {"@cloudbase/adapter-interface": "^0.4.0", "@cloudbase/types": "^1.8.4", "@cloudbase/utilities": "^1.8.4", "cloudbase-adapter-wx_mp": "0.2.0"}}, "client/node_modules/@cloudbase/auth": {"version": "1.8.4", "license": "ISC", "dependencies": {"@cloudbase/adapter-interface": "^0.4.0", "@cloudbase/app": "^1.8.4", "@cloudbase/types": "^1.8.4", "@cloudbase/utilities": "^1.8.4", "sourcemapped-stacktrace": "^1.1.11"}}, "client/node_modules/@cloudbase/database": {"version": "0.9.22", "license": "ISC", "dependencies": {"bson": "^4.0.2"}}, "client/node_modules/@cloudbase/functions": {"version": "1.8.4", "license": "Apache-2.0", "dependencies": {"@cloudbase/adapter-interface": "^0.4.0", "@cloudbase/types": "^1.8.4", "@cloudbase/utilities": "^1.8.4"}}, "client/node_modules/@cloudbase/js-sdk": {"version": "1.8.4", "license": "ISC", "dependencies": {"@cloudbase/analytics": "^1.8.4", "@cloudbase/app": "^1.8.4", "@cloudbase/auth": "^1.8.4", "@cloudbase/database": "0.9.22", "@cloudbase/functions": "^1.8.4", "@cloudbase/realtime": "^1.8.4", "@cloudbase/storage": "^1.8.4", "@cloudbase/types": "^1.8.4", "@cloudbase/utilities": "^1.8.4"}}, "client/node_modules/@cloudbase/node-sdk": {"version": "2.10.0", "dev": true, "license": "MIT", "dependencies": {"@cloudbase/database": "1.4.1", "@cloudbase/signature-nodejs": "1.0.0-beta.0", "agentkeepalive": "^4.3.0", "axios": "^0.21.1", "jsonwebtoken": "^8.5.1", "request": "^2.87.0", "retry": "^0.13.1", "xml2js": "^0.5.0"}, "engines": {"node": ">=8.6.0"}}, "client/node_modules/@cloudbase/node-sdk/node_modules/@cloudbase/database": {"version": "1.4.1", "dev": true, "license": "ISC", "dependencies": {"bson": "^4.0.3", "lodash.clonedeep": "4.5.0", "lodash.set": "4.3.2", "lodash.unset": "4.5.2"}}, "client/node_modules/@cloudbase/node-sdk/node_modules/@cloudbase/signature-nodejs": {"version": "1.0.0-beta.0", "dev": true, "dependencies": {"@types/clone": "^0.1.30", "clone": "^2.1.2", "is-stream": "^2.0.0", "url": "^0.11.0"}}, "client/node_modules/@cloudbase/node-sdk/node_modules/@types/clone": {"version": "0.1.30", "dev": true, "license": "MIT"}, "client/node_modules/@cloudbase/realtime": {"version": "1.8.4", "license": "ISC", "dependencies": {"@cloudbase/types": "^1.8.4", "lodash.clonedeep": "^4.5.0", "lodash.set": "^4.3.2", "lodash.unset": "^4.5.2"}}, "client/node_modules/@cloudbase/storage": {"version": "1.8.4", "license": "ISC", "dependencies": {"@cloudbase/types": "^1.8.4", "@cloudbase/utilities": "^1.8.4"}}, "client/node_modules/@cloudbase/types": {"version": "1.8.4", "license": "ISC", "dependencies": {"@cloudbase/adapter-interface": "^0.4.0"}}, "client/node_modules/@cloudbase/utilities": {"version": "1.8.4", "license": "ISC", "dependencies": {"@cloudbase/adapter-interface": "^0.4.0", "@cloudbase/types": "^1.8.4", "crypto-js": "4.2.0"}}, "client/node_modules/@csstools/color-helpers": {"version": "5.0.2", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT-0", "engines": {"node": ">=18"}}, "client/node_modules/@csstools/css-calc": {"version": "2.1.4", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": ">=18"}, "peerDependencies": {"@csstools/css-parser-algorithms": "^3.0.5", "@csstools/css-tokenizer": "^3.0.4"}}, "client/node_modules/@csstools/css-color-parser": {"version": "3.0.10", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "dependencies": {"@csstools/color-helpers": "^5.0.2", "@csstools/css-calc": "^2.1.4"}, "engines": {"node": ">=18"}, "peerDependencies": {"@csstools/css-parser-algorithms": "^3.0.5", "@csstools/css-tokenizer": "^3.0.4"}}, "client/node_modules/@csstools/css-parser-algorithms": {"version": "3.0.5", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": ">=18"}, "peerDependencies": {"@csstools/css-tokenizer": "^3.0.4"}}, "client/node_modules/@csstools/css-tokenizer": {"version": "3.0.4", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/csstools"}, {"type": "opencollective", "url": "https://opencollective.com/csstools"}], "license": "MIT", "engines": {"node": ">=18"}}, "client/node_modules/@discoveryjs/json-ext": {"version": "0.5.7", "dev": true, "license": "MIT", "engines": {"node": ">=10.0.0"}}, "client/node_modules/@esbuild/darwin-arm64": {"version": "0.21.5", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=12"}}, "client/node_modules/@eslint-community/eslint-utils": {"version": "4.7.0", "dev": true, "license": "MIT", "dependencies": {"eslint-visitor-keys": "^3.4.3"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || >=8.0.0"}}, "client/node_modules/@eslint-community/regexpp": {"version": "4.12.1", "dev": true, "license": "MIT", "engines": {"node": "^12.0.0 || ^14.0.0 || >=16.0.0"}}, "client/node_modules/@eslint/eslintrc": {"version": "2.1.4", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.4", "debug": "^4.3.2", "espree": "^9.6.0", "globals": "^13.19.0", "ignore": "^5.2.0", "import-fresh": "^3.2.1", "js-yaml": "^4.1.0", "minimatch": "^3.1.2", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "client/node_modules/@eslint/js": {"version": "8.57.1", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "client/node_modules/@hapi/hoek": {"version": "9.3.0", "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@hapi/topo": {"version": "5.1.0", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "client/node_modules/@humanwhocodes/config-array": {"version": "0.13.0", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^2.0.3", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "client/node_modules/@humanwhocodes/module-importer": {"version": "1.0.1", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=12.22"}, "funding": {"type": "github", "url": "https://github.com/sponsors/nzakas"}}, "client/node_modules/@humanwhocodes/object-schema": {"version": "2.0.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@isaacs/cliui": {"version": "8.0.2", "dev": true, "license": "ISC", "dependencies": {"string-width": "^5.1.2", "string-width-cjs": "npm:string-width@^4.2.0", "strip-ansi": "^7.0.1", "strip-ansi-cjs": "npm:strip-ansi@^6.0.1", "wrap-ansi": "^8.1.0", "wrap-ansi-cjs": "npm:wrap-ansi@^7.0.0"}, "engines": {"node": ">=12"}}, "client/node_modules/@isaacs/cliui/node_modules/ansi-regex": {"version": "6.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-regex?sponsor=1"}}, "client/node_modules/@isaacs/cliui/node_modules/ansi-styles": {"version": "6.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "client/node_modules/@isaacs/cliui/node_modules/emoji-regex": {"version": "9.2.2", "dev": true, "license": "MIT"}, "client/node_modules/@isaacs/cliui/node_modules/string-width": {"version": "5.1.2", "dev": true, "license": "MIT", "dependencies": {"eastasianwidth": "^0.2.0", "emoji-regex": "^9.2.2", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/@isaacs/cliui/node_modules/strip-ansi": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^6.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/strip-ansi?sponsor=1"}}, "client/node_modules/@isaacs/cliui/node_modules/wrap-ansi": {"version": "8.1.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^6.1.0", "string-width": "^5.0.1", "strip-ansi": "^7.0.1"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "client/node_modules/@jest/schemas": {"version": "29.6.3", "dev": true, "license": "MIT", "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "client/node_modules/@jest/types": {"version": "29.6.3", "dev": true, "license": "MIT", "dependencies": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "client/node_modules/@jridgewell/gen-mapping": {"version": "0.3.12", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}}, "client/node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "license": "MIT", "engines": {"node": ">=6.0.0"}}, "client/node_modules/@jridgewell/source-map": {"version": "0.3.10", "license": "MIT", "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25"}}, "client/node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.4", "license": "MIT"}, "client/node_modules/@jridgewell/trace-mapping": {"version": "0.3.29", "license": "MIT", "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "client/node_modules/@leichtgewicht/ip-codec": {"version": "2.0.5", "devOptional": true, "license": "MIT"}, "client/node_modules/@napi-rs/triples": {"version": "1.2.0", "dev": true, "license": "MIT"}, "client/node_modules/@nicolo-ribaudo/eslint-scope-5-internals": {"version": "5.1.1-v1", "dev": true, "license": "MIT", "dependencies": {"eslint-scope": "5.1.1"}}, "client/node_modules/@nodelib/fs.scandir": {"version": "2.1.5", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "2.0.5", "run-parallel": "^1.1.9"}, "engines": {"node": ">= 8"}}, "client/node_modules/@nodelib/fs.stat": {"version": "2.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "client/node_modules/@nodelib/fs.walk": {"version": "1.2.8", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.scandir": "2.1.5", "fastq": "^1.6.0"}, "engines": {"node": ">= 8"}}, "client/node_modules/@parcel/watcher": {"version": "2.5.1", "hasInstallScript": true, "license": "MIT", "optional": true, "dependencies": {"detect-libc": "^1.0.3", "is-glob": "^4.0.3", "micromatch": "^4.0.5", "node-addon-api": "^7.0.0"}, "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"@parcel/watcher-android-arm64": "2.5.1", "@parcel/watcher-darwin-arm64": "2.5.1", "@parcel/watcher-darwin-x64": "2.5.1", "@parcel/watcher-freebsd-x64": "2.5.1", "@parcel/watcher-linux-arm-glibc": "2.5.1", "@parcel/watcher-linux-arm-musl": "2.5.1", "@parcel/watcher-linux-arm64-glibc": "2.5.1", "@parcel/watcher-linux-arm64-musl": "2.5.1", "@parcel/watcher-linux-x64-glibc": "2.5.1", "@parcel/watcher-linux-x64-musl": "2.5.1", "@parcel/watcher-win32-arm64": "2.5.1", "@parcel/watcher-win32-ia32": "2.5.1", "@parcel/watcher-win32-x64": "2.5.1"}}, "client/node_modules/@parcel/watcher-darwin-arm64": {"version": "2.5.1", "cpu": ["arm64"], "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "client/node_modules/@parcel/watcher/node_modules/detect-libc": {"version": "1.0.3", "license": "Apache-2.0", "optional": true, "bin": {"detect-libc": "bin/detect-libc.js"}, "engines": {"node": ">=0.10"}}, "client/node_modules/@pkgjs/parseargs": {"version": "0.11.0", "dev": true, "license": "MIT", "optional": true, "engines": {"node": ">=14"}}, "client/node_modules/@protobufjs/aspromise": {"version": "1.1.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@protobufjs/base64": {"version": "1.1.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@protobufjs/codegen": {"version": "2.0.4", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@protobufjs/eventemitter": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@protobufjs/fetch": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.1", "@protobufjs/inquire": "^1.1.0"}}, "client/node_modules/@protobufjs/float": {"version": "1.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@protobufjs/inquire": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@protobufjs/path": {"version": "1.1.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@protobufjs/pool": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@protobufjs/utf8": {"version": "1.1.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@rnx-kit/babel-preset-metro-react-native": {"version": "1.1.8", "dev": true, "license": "MIT", "dependencies": {"@rnx-kit/console": "^1.1.0", "babel-plugin-const-enum": "^1.0.0"}, "peerDependencies": {"@babel/core": "^7.20.0", "@babel/plugin-transform-typescript": "^7.20.0", "@babel/runtime": "^7.20.0", "@react-native/babel-preset": "*", "metro-react-native-babel-preset": "*"}, "peerDependenciesMeta": {"@babel/plugin-transform-typescript": {"optional": true}, "@react-native/babel-preset": {"optional": true}, "metro-react-native-babel-preset": {"optional": true}}}, "client/node_modules/@rnx-kit/console": {"version": "1.1.0", "dev": true, "license": "MIT"}, "client/node_modules/@rtsao/scc": {"version": "1.1.0", "dev": true, "license": "MIT"}, "client/node_modules/@sideway/address": {"version": "4.1.5", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.0.0"}}, "client/node_modules/@sideway/formula": {"version": "3.0.1", "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@sideway/pinpoint": {"version": "2.0.0", "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@sinclair/typebox": {"version": "0.27.8", "dev": true, "license": "MIT"}, "client/node_modules/@sindresorhus/is": {"version": "0.7.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/@sindresorhus/merge-streams": {"version": "2.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/@stencil/core": {"version": "2.22.3", "license": "MIT", "bin": {"stencil": "bin/stencil"}, "engines": {"node": ">=12.10.0", "npm": ">=6.0.0"}}, "client/node_modules/@swc/core": {"version": "1.3.96", "hasInstallScript": true, "license": "Apache-2.0", "dependencies": {"@swc/counter": "^0.1.1", "@swc/types": "^0.1.5"}, "engines": {"node": ">=10"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/swc"}, "optionalDependencies": {"@swc/core-darwin-arm64": "1.3.96", "@swc/core-darwin-x64": "1.3.96", "@swc/core-linux-arm-gnueabihf": "1.3.96", "@swc/core-linux-arm64-gnu": "1.3.96", "@swc/core-linux-arm64-musl": "1.3.96", "@swc/core-linux-x64-gnu": "1.3.96", "@swc/core-linux-x64-musl": "1.3.96", "@swc/core-win32-arm64-msvc": "1.3.96", "@swc/core-win32-ia32-msvc": "1.3.96", "@swc/core-win32-x64-msvc": "1.3.96"}, "peerDependencies": {"@swc/helpers": "^0.5.0"}, "peerDependenciesMeta": {"@swc/helpers": {"optional": true}}}, "client/node_modules/@swc/core-darwin-arm64": {"version": "1.3.96", "cpu": ["arm64"], "license": "Apache-2.0 AND MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=10"}}, "client/node_modules/@swc/counter": {"version": "0.1.3", "license": "Apache-2.0"}, "client/node_modules/@swc/register": {"version": "0.1.10", "license": "(Apache-2.0 OR MIT)", "dependencies": {"lodash.clonedeep": "^4.5.0", "pirates": "^4.0.1", "source-map-support": "^0.5.13"}, "bin": {"swc-node": "bin/swc-node"}, "peerDependencies": {"@swc/core": "^1.0.46"}}, "client/node_modules/@swc/types": {"version": "0.1.23", "license": "Apache-2.0", "dependencies": {"@swc/counter": "^0.1.3"}}, "client/node_modules/@szmarczak/http-timer": {"version": "1.1.2", "dev": true, "license": "MIT", "dependencies": {"defer-to-connect": "^1.0.1"}, "engines": {"node": ">=6"}}, "client/node_modules/@tarojs/api": {"version": "4.0.6", "license": "MIT", "engines": {"node": ">= 18"}, "peerDependencies": {"@tarojs/runtime": "4.0.6", "@tarojs/shared": "4.0.6"}}, "client/node_modules/@tarojs/binding": {"version": "4.0.6", "dev": true, "hasInstallScript": true, "license": "MIT", "dependencies": {"@napi-rs/triples": "1.2.0"}, "optionalDependencies": {"@tarojs/binding-darwin-arm64": "4.0.6", "@tarojs/binding-darwin-x64": "4.0.6", "@tarojs/binding-linux-x64-gnu": "4.0.6", "@tarojs/binding-win32-x64-msvc": "4.0.6"}}, "client/node_modules/@tarojs/binding-darwin-arm64": {"version": "4.0.6", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 18"}}, "client/node_modules/@tarojs/cli": {"version": "4.0.6", "dev": true, "license": "MIT", "dependencies": {"@tarojs/binding": "4.0.6", "@tarojs/helper": "4.0.6", "@tarojs/plugin-doctor": "^0.0.13", "@tarojs/service": "4.0.6", "@tarojs/shared": "4.0.6", "adm-zip": "^0.5.12", "axios": "^1.6.8", "cli-highlight": "^2.1.11", "download-git-repo": "^3.0.2", "envinfo": "^7.12.0", "inquirer": "^8.2.6", "latest-version": "^5.1.0", "minimist": "^1.2.8", "ora": "^5.4.1", "semver": "^7.6.0", "validate-npm-package-name": "^5.0.0"}, "bin": {"taro": "bin/taro"}, "engines": {"node": ">= 18"}}, "client/node_modules/@tarojs/cli/node_modules/axios": {"version": "1.10.0", "dev": true, "license": "MIT", "dependencies": {"follow-redirects": "^1.15.6", "form-data": "^4.0.0", "proxy-from-env": "^1.1.0"}}, "client/node_modules/@tarojs/cli/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "client/node_modules/@tarojs/components": {"version": "4.0.6", "license": "MIT", "dependencies": {"@stencil/core": "2.22.3", "@tarojs/components-advanced": "4.0.6", "@tarojs/runtime": "4.0.6", "@tarojs/taro": "4.0.6", "classnames": "^2.2.5", "hammerjs": "^2.0.8", "hls.js": "^1.1.5", "resolve-pathname": "^3.0.0", "swiper": "11.1.0", "tslib": "^2.6.2"}, "engines": {"node": ">= 18"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "vue": {"optional": true}}}, "client/node_modules/@tarojs/components-advanced": {"version": "4.0.6", "license": "MIT", "dependencies": {"@tarojs/components": "4.0.6", "classnames": "^2.2.5", "csstype": "^3.1.1", "memoize-one": "^6.0.0", "tslib": "^2.6.2"}, "peerDependencies": {"@tarojs/runtime": "~4.0.6", "@tarojs/shared": "~4.0.6", "@tarojs/taro": "~4.0.6", "react": ">=17"}, "peerDependenciesMeta": {"react": {"optional": true}, "vue": {"optional": true}}}, "client/node_modules/@tarojs/components-advanced/node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "client/node_modules/@tarojs/components/node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "client/node_modules/@tarojs/helper": {"version": "4.0.6", "license": "MIT", "dependencies": {"@babel/core": "^7.24.4", "@babel/generator": "^7.24.4", "@babel/parser": "^7.24.4", "@babel/traverse": "^7.24.1", "@babel/types": "^7.24.0", "@swc/core": "1.3.96", "@swc/register": "0.1.10", "ansi-escapes": "^4.3.2", "chalk": "^4.1.2", "chokidar": "^3.6.0", "cross-spawn": "^7.0.3", "debug": "^4.3.4", "dotenv": "^16.4.5", "dotenv-expand": "^11.0.6", "esbuild": "~0.21.0", "find-yarn-workspace-root": "^2.0.0", "fs-extra": "^11.2.0", "lodash": "^4.17.21", "require-from-string": "^2.0.2", "resolve": "^1.22.8", "supports-hyperlinks": "^3.0.0"}, "engines": {"node": ">= 18"}}, "client/node_modules/@tarojs/helper/node_modules/supports-hyperlinks": {"version": "3.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "engines": {"node": ">=14.18"}, "funding": {"url": "https://github.com/chalk/supports-hyperlinks?sponsor=1"}}, "client/node_modules/@tarojs/plugin-doctor": {"version": "0.0.13", "dev": true, "license": "MIT", "dependencies": {"eslint": "8.41.0", "glob": "10.2.6"}, "engines": {"node": ">= 10"}, "optionalDependencies": {"@tarojs/plugin-doctor-darwin-arm64": "0.0.13", "@tarojs/plugin-doctor-darwin-universal": "0.0.13", "@tarojs/plugin-doctor-darwin-x64": "0.0.13", "@tarojs/plugin-doctor-linux-arm-gnueabihf": "0.0.13", "@tarojs/plugin-doctor-linux-arm64-gnu": "0.0.13", "@tarojs/plugin-doctor-linux-arm64-musl": "0.0.13", "@tarojs/plugin-doctor-linux-x64-gnu": "0.0.13", "@tarojs/plugin-doctor-linux-x64-musl": "0.0.13", "@tarojs/plugin-doctor-win32-ia32-msvc": "0.0.13", "@tarojs/plugin-doctor-win32-x64-msvc": "0.0.13"}}, "client/node_modules/@tarojs/plugin-doctor-darwin-arm64": {"version": "0.0.13", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "client/node_modules/@tarojs/plugin-doctor-darwin-universal": {"version": "0.0.13", "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">= 10"}}, "client/node_modules/@tarojs/plugin-doctor/node_modules/@eslint/js": {"version": "8.41.0", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}}, "client/node_modules/@tarojs/plugin-doctor/node_modules/@humanwhocodes/config-array": {"version": "0.11.14", "dev": true, "license": "Apache-2.0", "dependencies": {"@humanwhocodes/object-schema": "^2.0.2", "debug": "^4.3.1", "minimatch": "^3.0.5"}, "engines": {"node": ">=10.10.0"}}, "client/node_modules/@tarojs/plugin-doctor/node_modules/brace-expansion": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "client/node_modules/@tarojs/plugin-doctor/node_modules/eslint": {"version": "8.41.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.4.0", "@eslint/eslintrc": "^2.0.3", "@eslint/js": "8.41.0", "@humanwhocodes/config-array": "^0.11.8", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "ajv": "^6.10.0", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.0", "eslint-visitor-keys": "^3.4.1", "espree": "^9.5.2", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "import-fresh": "^3.0.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.1", "strip-ansi": "^6.0.1", "strip-json-comments": "^3.1.0", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "client/node_modules/@tarojs/plugin-doctor/node_modules/eslint-scope": {"version": "7.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "client/node_modules/@tarojs/plugin-doctor/node_modules/glob": {"version": "10.2.6", "dev": true, "license": "ISC", "dependencies": {"foreground-child": "^3.1.0", "jackspeak": "^2.0.3", "minimatch": "^9.0.1", "minipass": "^5.0.0 || ^6.0.2", "path-scurry": "^1.7.0"}, "bin": {"glob": "dist/cjs/src/bin.js"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "client/node_modules/@tarojs/plugin-doctor/node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "client/node_modules/@tarojs/plugin-doctor/node_modules/glob/node_modules/minimatch": {"version": "9.0.5", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "client/node_modules/@tarojs/plugin-framework-vue3": {"version": "4.0.6", "license": "MIT", "dependencies": {"lodash": "4.17.21", "tslib": "2.6.2"}, "engines": {"node": ">= 18"}, "peerDependencies": {"@tarojs/helper": "4.0.6", "@tarojs/runner-utils": "4.0.6", "@tarojs/runtime": "4.0.6", "@tarojs/shared": "4.0.6", "@vitejs/plugin-vue": "^5", "@vitejs/plugin-vue-jsx": "^3", "vite": "^4", "vue": "^3", "vue-loader": "^17.0.0", "webpack": "^5"}, "peerDependenciesMeta": {"@vitejs/plugin-vue": {"optional": true}, "@vitejs/plugin-vue-jsx": {"optional": true}, "vite": {"optional": true}}}, "client/node_modules/@tarojs/plugin-framework-vue3/node_modules/tslib": {"version": "2.6.2", "license": "0BSD"}, "client/node_modules/@tarojs/plugin-platform-weapp": {"version": "4.0.6", "license": "MIT", "engines": {"node": ">= 18"}, "peerDependencies": {"@tarojs/service": "4.0.6", "@tarojs/shared": "4.0.6"}}, "client/node_modules/@tarojs/runner-utils": {"version": "4.0.6", "license": "MIT", "dependencies": {"@tarojs/helper": "4.0.6", "rollup": "^3.29.4", "scss-bundle": "^3.1.2"}, "engines": {"node": ">= 18"}}, "client/node_modules/@tarojs/runtime": {"version": "4.0.6", "license": "MIT", "dependencies": {"@tarojs/shared": "4.0.6", "tslib": "^2.6.2"}, "engines": {"node": ">= 18"}}, "client/node_modules/@tarojs/runtime/node_modules/tslib": {"version": "2.8.1", "license": "0BSD"}, "client/node_modules/@tarojs/service": {"version": "4.0.6", "license": "MIT", "dependencies": {"@tarojs/helper": "4.0.6", "@tarojs/runner-utils": "4.0.6", "@tarojs/shared": "4.0.6", "joi": "^17.12.3", "lodash": "^4.17.21", "ora": "^5.4.1", "resolve": "^1.22.8", "tapable": "^2.2.1", "webpack-merge": "^5.10.0"}, "engines": {"node": ">= 18"}}, "client/node_modules/@tarojs/shared": {"version": "4.0.6", "license": "MIT", "engines": {"node": ">= 18"}}, "client/node_modules/@tarojs/taro": {"version": "4.0.6", "license": "MIT", "dependencies": {"@tarojs/api": "4.0.6", "@tarojs/runtime": "4.0.6", "@types/postcss-url": "^10.0.4"}, "engines": {"node": ">= 18"}, "peerDependencies": {"@tarojs/components": "4.0.6", "@tarojs/helper": "4.0.6", "@tarojs/shared": "4.0.6", "@types/react": "^18", "html-webpack-plugin": "^5", "postcss": "^8", "rollup": "^3", "vue": "^3", "webpack": "^5", "webpack-chain": "^6", "webpack-dev-server": "^4"}, "peerDependenciesMeta": {"@types/react": {"optional": true}, "html-webpack-plugin": {"optional": true}, "rollup": {"optional": true}, "vue": {"optional": true}, "webpack": {"optional": true}, "webpack-chain": {"optional": true}, "webpack-dev-server": {"optional": true}}}, "client/node_modules/@tarojs/taro-loader": {"version": "4.0.6", "dev": true, "license": "MIT", "dependencies": {"@tarojs/helper": "4.0.6"}, "engines": {"node": ">= 18"}, "peerDependencies": {"webpack": "5.91.0"}}, "client/node_modules/@tarojs/webpack5-prebundle": {"version": "4.0.6", "dev": true, "license": "MIT", "dependencies": {"@tarojs/helper": "4.0.6", "@tarojs/shared": "4.0.6", "enhanced-resolve": "^5.16.0", "es-module-lexer": "^0.10.5", "lodash": "^4.17.21", "webpack-virtual-modules": "^0.6.1"}, "engines": {"node": ">= 18"}, "peerDependencies": {"webpack": "5.91.0"}}, "client/node_modules/@tarojs/webpack5-runner": {"version": "4.0.6", "dev": true, "license": "MIT", "dependencies": {"@tarojs/helper": "4.0.6", "@tarojs/runner-utils": "4.0.6", "@tarojs/shared": "4.0.6", "@tarojs/taro-loader": "4.0.6", "@tarojs/webpack5-prebundle": "4.0.6", "acorn": "^8.11.3", "acorn-walk": "^8.3.2", "autoprefixer": "^10.4.19", "babel-loader": "8.2.1", "copy-webpack-plugin": "^12.0.2", "css-loader": "^7.1.1", "css-minimizer-webpack-plugin": "^6.0.0", "detect-port": "^1.5.1", "esbuild": "~0.21.0", "esbuild-loader": "^4.1.0", "html-minifier": "^4.0.0", "html-webpack-plugin": "^5.6.0", "jsdom": "^24.0.0", "less-loader": "^12.2.0", "lightningcss": "^1.24.1", "loader-utils": "^3.2.1", "lodash": "^4.17.21", "md5": "^2.3.0", "mini-css-extract-plugin": "^2.9.0", "miniprogram-simulate": "^1.6.1", "ora": "^5.4.1", "picomatch": "^4.0.2", "postcss-html-transform": "4.0.6", "postcss-import": "^16.1.0", "postcss-loader": "^8.1.1", "postcss-plugin-constparse": "4.0.6", "postcss-pxtransform": "4.0.6", "postcss-url": "^10.1.3", "regenerator-runtime": "0.11", "resolve-url-loader": "^5.0.0", "sass-loader": "^14.2.1", "sax": "1.2.4", "style-loader": "^3.3.4", "stylus-loader": "^8.1.0", "terser-webpack-plugin": "^5.3.10", "vm2": "^3.9.19", "webpack-chain": "^6.5.1", "webpack-dev-server": "^4.15.2", "webpack-format-messages": "^3.0.1", "webpack-virtual-modules": "^0.6.1", "webpackbar": "^5.0.2"}, "engines": {"node": ">= 18"}, "peerDependencies": {"@babel/core": "^7.12.0", "@tarojs/runtime": "4.0.6", "less": "^4", "postcss": "^8", "sass": "^1.3.0", "stylus": ">=0.52.4", "webpack": "5.91.0"}, "peerDependenciesMeta": {"less": {"optional": true}, "sass": {"optional": true}, "stylus": {"optional": true}}}, "client/node_modules/@tarojs/webpack5-runner/node_modules/loader-utils": {"version": "3.3.1", "dev": true, "license": "MIT", "engines": {"node": ">= 12.13.0"}}, "client/node_modules/@tarojs/webpack5-runner/node_modules/picomatch": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "client/node_modules/@trysound/sax": {"version": "0.2.0", "dev": true, "license": "ISC", "engines": {"node": ">=10.13.0"}}, "client/node_modules/@types/archy": {"version": "0.0.31", "license": "MIT"}, "client/node_modules/@types/body-parser": {"version": "1.19.6", "devOptional": true, "license": "MIT", "dependencies": {"@types/connect": "*", "@types/node": "*"}}, "client/node_modules/@types/bonjour": {"version": "3.5.13", "devOptional": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "client/node_modules/@types/connect": {"version": "3.4.38", "devOptional": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "client/node_modules/@types/connect-history-api-fallback": {"version": "1.5.4", "devOptional": true, "license": "MIT", "dependencies": {"@types/express-serve-static-core": "*", "@types/node": "*"}}, "client/node_modules/@types/debug": {"version": "4.1.12", "license": "MIT", "dependencies": {"@types/ms": "*"}}, "client/node_modules/@types/eslint": {"version": "9.6.1", "license": "MIT", "dependencies": {"@types/estree": "*", "@types/json-schema": "*"}}, "client/node_modules/@types/eslint-scope": {"version": "3.7.7", "license": "MIT", "dependencies": {"@types/eslint": "*", "@types/estree": "*"}}, "client/node_modules/@types/estree": {"version": "1.0.8", "license": "MIT"}, "client/node_modules/@types/express": {"version": "4.17.23", "devOptional": true, "license": "MIT", "dependencies": {"@types/body-parser": "*", "@types/express-serve-static-core": "^4.17.33", "@types/qs": "*", "@types/serve-static": "*"}}, "client/node_modules/@types/express-serve-static-core": {"version": "5.0.6", "devOptional": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "client/node_modules/@types/express/node_modules/@types/express-serve-static-core": {"version": "4.19.6", "devOptional": true, "license": "MIT", "dependencies": {"@types/node": "*", "@types/qs": "*", "@types/range-parser": "*", "@types/send": "*"}}, "client/node_modules/@types/fs-extra": {"version": "8.1.5", "license": "MIT", "dependencies": {"@types/node": "*"}}, "client/node_modules/@types/glob": {"version": "7.2.0", "license": "MIT", "dependencies": {"@types/minimatch": "*", "@types/node": "*"}}, "client/node_modules/@types/html-minifier-terser": {"version": "6.1.0", "devOptional": true, "license": "MIT"}, "client/node_modules/@types/http-errors": {"version": "2.0.5", "devOptional": true, "license": "MIT"}, "client/node_modules/@types/http-proxy": {"version": "1.17.16", "devOptional": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "client/node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "dev": true, "license": "MIT"}, "client/node_modules/@types/istanbul-lib-report": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "client/node_modules/@types/istanbul-reports": {"version": "3.0.4", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-report": "*"}}, "client/node_modules/@types/json-schema": {"version": "7.0.15", "license": "MIT"}, "client/node_modules/@types/json5": {"version": "0.0.29", "dev": true, "license": "MIT"}, "client/node_modules/@types/lodash": {"version": "4.17.20", "license": "MIT"}, "client/node_modules/@types/lodash.debounce": {"version": "4.0.9", "license": "MIT", "dependencies": {"@types/lodash": "*"}}, "client/node_modules/@types/long": {"version": "4.0.2", "dev": true, "license": "MIT"}, "client/node_modules/@types/mime": {"version": "1.3.5", "devOptional": true, "license": "MIT"}, "client/node_modules/@types/minimatch": {"version": "5.1.2", "license": "MIT"}, "client/node_modules/@types/minimist": {"version": "1.2.5", "dev": true, "license": "MIT"}, "client/node_modules/@types/ms": {"version": "2.1.0", "license": "MIT"}, "client/node_modules/@types/node": {"version": "13.13.52", "license": "MIT"}, "client/node_modules/@types/node-forge": {"version": "1.3.12", "devOptional": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "client/node_modules/@types/normalize-package-data": {"version": "2.4.4", "dev": true, "license": "MIT"}, "client/node_modules/@types/parse-json": {"version": "4.0.2", "dev": true, "license": "MIT"}, "client/node_modules/@types/postcss-url": {"version": "10.0.4", "license": "MIT", "dependencies": {"@types/node": "*", "postcss": "^8.0.0"}}, "client/node_modules/@types/qs": {"version": "6.14.0", "devOptional": true, "license": "MIT"}, "client/node_modules/@types/range-parser": {"version": "1.2.7", "devOptional": true, "license": "MIT"}, "client/node_modules/@types/retry": {"version": "0.12.0", "devOptional": true, "license": "MIT"}, "client/node_modules/@types/sass": {"version": "1.43.1", "license": "MIT", "dependencies": {"@types/node": "*"}}, "client/node_modules/@types/semver": {"version": "7.7.0", "dev": true, "license": "MIT"}, "client/node_modules/@types/send": {"version": "0.17.5", "devOptional": true, "license": "MIT", "dependencies": {"@types/mime": "^1", "@types/node": "*"}}, "client/node_modules/@types/serve-index": {"version": "1.9.4", "devOptional": true, "license": "MIT", "dependencies": {"@types/express": "*"}}, "client/node_modules/@types/serve-static": {"version": "1.15.8", "devOptional": true, "license": "MIT", "dependencies": {"@types/http-errors": "*", "@types/node": "*", "@types/send": "*"}}, "client/node_modules/@types/sockjs": {"version": "0.3.36", "devOptional": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "client/node_modules/@types/webpack-env": {"version": "1.18.8", "dev": true, "license": "MIT"}, "client/node_modules/@types/ws": {"version": "8.18.1", "devOptional": true, "license": "MIT", "dependencies": {"@types/node": "*"}}, "client/node_modules/@types/yargs": {"version": "17.0.33", "dev": true, "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "client/node_modules/@types/yargs-parser": {"version": "21.0.3", "dev": true, "license": "MIT"}, "client/node_modules/@typescript-eslint/eslint-plugin": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.4.0", "@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/type-utils": "5.62.0", "@typescript-eslint/utils": "5.62.0", "debug": "^4.3.4", "graphemer": "^1.4.0", "ignore": "^5.2.0", "natural-compare-lite": "^1.4.0", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^5.0.0", "eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "client/node_modules/@typescript-eslint/eslint-plugin/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "client/node_modules/@typescript-eslint/parser": {"version": "5.62.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/types": "5.62.0", "@typescript-eslint/typescript-estree": "5.62.0", "debug": "^4.3.4"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "client/node_modules/@typescript-eslint/scope-manager": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "client/node_modules/@typescript-eslint/type-utils": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "5.62.0", "@typescript-eslint/utils": "5.62.0", "debug": "^4.3.4", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "client/node_modules/@typescript-eslint/types": {"version": "5.62.0", "dev": true, "license": "MIT", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "client/node_modules/@typescript-eslint/typescript-estree": {"version": "5.62.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "5.62.0", "@typescript-eslint/visitor-keys": "5.62.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "semver": "^7.3.7", "tsutils": "^3.21.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "client/node_modules/@typescript-eslint/typescript-estree/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "client/node_modules/@typescript-eslint/utils": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@types/json-schema": "^7.0.9", "@types/semver": "^7.3.12", "@typescript-eslint/scope-manager": "5.62.0", "@typescript-eslint/types": "5.62.0", "@typescript-eslint/typescript-estree": "5.62.0", "eslint-scope": "^5.1.1", "semver": "^7.3.7"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "client/node_modules/@typescript-eslint/utils/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "client/node_modules/@typescript-eslint/visitor-keys": {"version": "5.62.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "5.62.0", "eslint-visitor-keys": "^3.3.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "client/node_modules/@ungap/structured-clone": {"version": "1.3.0", "dev": true, "license": "ISC"}, "client/node_modules/@vue/compiler-core": {"version": "3.5.17", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.5", "@vue/shared": "3.5.17", "entities": "^4.5.0", "estree-walker": "^2.0.2", "source-map-js": "^1.2.1"}}, "client/node_modules/@vue/compiler-dom": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/compiler-core": "3.5.17", "@vue/shared": "3.5.17"}}, "client/node_modules/@vue/compiler-sfc": {"version": "3.5.17", "license": "MIT", "dependencies": {"@babel/parser": "^7.27.5", "@vue/compiler-core": "3.5.17", "@vue/compiler-dom": "3.5.17", "@vue/compiler-ssr": "3.5.17", "@vue/shared": "3.5.17", "estree-walker": "^2.0.2", "magic-string": "^0.30.17", "postcss": "^8.5.6", "source-map-js": "^1.2.1"}}, "client/node_modules/@vue/compiler-ssr": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.17", "@vue/shared": "3.5.17"}}, "client/node_modules/@vue/devtools-api": {"version": "6.6.4", "license": "MIT"}, "client/node_modules/@vue/reactivity": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/shared": "3.5.17"}}, "client/node_modules/@vue/runtime-core": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.17", "@vue/shared": "3.5.17"}}, "client/node_modules/@vue/runtime-dom": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/reactivity": "3.5.17", "@vue/runtime-core": "3.5.17", "@vue/shared": "3.5.17", "csstype": "^3.1.3"}}, "client/node_modules/@vue/server-renderer": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/compiler-ssr": "3.5.17", "@vue/shared": "3.5.17"}, "peerDependencies": {"vue": "3.5.17"}}, "client/node_modules/@vue/shared": {"version": "3.5.17", "license": "MIT"}, "client/node_modules/@webassemblyjs/ast": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/helper-numbers": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2"}}, "client/node_modules/@webassemblyjs/floating-point-hex-parser": {"version": "1.13.2", "license": "MIT"}, "client/node_modules/@webassemblyjs/helper-api-error": {"version": "1.13.2", "license": "MIT"}, "client/node_modules/@webassemblyjs/helper-buffer": {"version": "1.14.1", "license": "MIT"}, "client/node_modules/@webassemblyjs/helper-numbers": {"version": "1.13.2", "license": "MIT", "dependencies": {"@webassemblyjs/floating-point-hex-parser": "1.13.2", "@webassemblyjs/helper-api-error": "1.13.2", "@xtuc/long": "4.2.2"}}, "client/node_modules/@webassemblyjs/helper-wasm-bytecode": {"version": "1.13.2", "license": "MIT"}, "client/node_modules/@webassemblyjs/helper-wasm-section": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/wasm-gen": "1.14.1"}}, "client/node_modules/@webassemblyjs/ieee754": {"version": "1.13.2", "license": "MIT", "dependencies": {"@xtuc/ieee754": "^1.2.0"}}, "client/node_modules/@webassemblyjs/leb128": {"version": "1.13.2", "license": "Apache-2.0", "dependencies": {"@xtuc/long": "4.2.2"}}, "client/node_modules/@webassemblyjs/utf8": {"version": "1.13.2", "license": "MIT"}, "client/node_modules/@webassemblyjs/wasm-edit": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/helper-wasm-section": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-opt": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1", "@webassemblyjs/wast-printer": "1.14.1"}}, "client/node_modules/@webassemblyjs/wasm-gen": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "client/node_modules/@webassemblyjs/wasm-opt": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-buffer": "1.14.1", "@webassemblyjs/wasm-gen": "1.14.1", "@webassemblyjs/wasm-parser": "1.14.1"}}, "client/node_modules/@webassemblyjs/wasm-parser": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@webassemblyjs/helper-api-error": "1.13.2", "@webassemblyjs/helper-wasm-bytecode": "1.13.2", "@webassemblyjs/ieee754": "1.13.2", "@webassemblyjs/leb128": "1.13.2", "@webassemblyjs/utf8": "1.13.2"}}, "client/node_modules/@webassemblyjs/wast-printer": {"version": "1.14.1", "license": "MIT", "dependencies": {"@webassemblyjs/ast": "1.14.1", "@xtuc/long": "4.2.2"}}, "client/node_modules/@webpack-cli/configtest": {"version": "2.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=14.15.0"}, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}}, "client/node_modules/@webpack-cli/info": {"version": "2.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=14.15.0"}, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}}, "client/node_modules/@webpack-cli/serve": {"version": "2.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=14.15.0"}, "peerDependencies": {"webpack": "5.x.x", "webpack-cli": "5.x.x"}, "peerDependenciesMeta": {"webpack-dev-server": {"optional": true}}}, "client/node_modules/@xtuc/ieee754": {"version": "1.2.0", "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/@xtuc/long": {"version": "4.2.2", "license": "Apache-2.0"}, "client/node_modules/accepts": {"version": "1.3.8", "devOptional": true, "license": "MIT", "dependencies": {"mime-types": "~2.1.34", "negotiator": "0.6.3"}, "engines": {"node": ">= 0.6"}}, "client/node_modules/accepts/node_modules/negotiator": {"version": "0.6.3", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/acorn": {"version": "8.15.0", "license": "MIT", "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "client/node_modules/acorn-jsx": {"version": "5.3.2", "dev": true, "license": "MIT", "peerDependencies": {"acorn": "^6.0.0 || ^7.0.0 || ^8.0.0"}}, "client/node_modules/acorn-walk": {"version": "8.3.4", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "client/node_modules/address": {"version": "1.2.2", "dev": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "client/node_modules/adjust-sourcemap-loader": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"loader-utils": "^2.0.0", "regex-parser": "^2.2.11"}, "engines": {"node": ">=8.9"}}, "client/node_modules/adm-zip": {"version": "0.5.16", "dev": true, "license": "MIT", "engines": {"node": ">=12.0"}}, "client/node_modules/agent-base": {"version": "7.1.3", "dev": true, "license": "MIT", "engines": {"node": ">= 14"}}, "client/node_modules/agentkeepalive": {"version": "4.6.0", "dev": true, "license": "MIT", "dependencies": {"humanize-ms": "^1.2.1"}, "engines": {"node": ">= 8.0.0"}}, "client/node_modules/ajv": {"version": "6.12.6", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.1", "fast-json-stable-stringify": "^2.0.0", "json-schema-traverse": "^0.4.1", "uri-js": "^4.2.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "client/node_modules/ajv-formats": {"version": "2.1.1", "license": "MIT", "dependencies": {"ajv": "^8.0.0"}, "peerDependencies": {"ajv": "^8.0.0"}, "peerDependenciesMeta": {"ajv": {"optional": true}}}, "client/node_modules/ajv-formats/node_modules/ajv": {"version": "8.17.1", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "client/node_modules/ajv-formats/node_modules/json-schema-traverse": {"version": "1.0.0", "license": "MIT"}, "client/node_modules/ajv-keywords": {"version": "3.5.2", "dev": true, "license": "MIT", "peerDependencies": {"ajv": "^6.9.1"}}, "client/node_modules/ansi-escapes": {"version": "4.3.2", "license": "MIT", "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/ansi-html-community": {"version": "0.0.8", "devOptional": true, "engines": ["node >= 0.8.0"], "license": "Apache-2.0", "bin": {"ansi-html": "bin/ansi-html"}}, "client/node_modules/any-promise": {"version": "1.3.0", "dev": true, "license": "MIT"}, "client/node_modules/anymatch": {"version": "3.1.3", "license": "ISC", "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "client/node_modules/archive-type": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"file-type": "^4.2.0"}, "engines": {"node": ">=4"}}, "client/node_modules/archive-type/node_modules/file-type": {"version": "4.4.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/archy": {"version": "1.0.0", "license": "MIT"}, "client/node_modules/argparse": {"version": "2.0.1", "dev": true, "license": "Python-2.0"}, "client/node_modules/array-buffer-byte-length": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "is-array-buffer": "^3.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/array-flatten": {"version": "1.1.1", "devOptional": true, "license": "MIT"}, "client/node_modules/array-includes": {"version": "3.1.9", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.24.0", "es-object-atoms": "^1.1.1", "get-intrinsic": "^1.3.0", "is-string": "^1.1.1", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/array-union": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/array.prototype.findlast": {"version": "1.2.5", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/array.prototype.findlastindex": {"version": "1.2.6", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-shim-unscopables": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/array.prototype.flat": {"version": "1.3.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/array.prototype.flatmap": {"version": "1.3.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/array.prototype.tosorted": {"version": "1.1.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.3", "es-errors": "^1.3.0", "es-shim-unscopables": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/arraybuffer.prototype.slice": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.1", "call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "is-array-buffer": "^3.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/arrify": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/asn1": {"version": "0.2.6", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": "~2.1.0"}}, "client/node_modules/assert-plus": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "client/node_modules/astral-regex": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/async-function": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "client/node_modules/asynckit": {"version": "0.4.0", "dev": true, "license": "MIT"}, "client/node_modules/autoprefixer": {"version": "10.4.21", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/autoprefixer"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"browserslist": "^4.24.4", "caniuse-lite": "^1.0.30001702", "fraction.js": "^4.3.7", "normalize-range": "^0.1.2", "picocolors": "^1.1.1", "postcss-value-parser": "^4.2.0"}, "bin": {"autoprefixer": "bin/autoprefixer"}, "engines": {"node": "^10 || ^12 || >=14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "client/node_modules/available-typed-arrays": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"possible-typed-array-names": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/aws-sign2": {"version": "0.7.0", "dev": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "client/node_modules/aws4": {"version": "1.13.2", "dev": true, "license": "MIT"}, "client/node_modules/axios": {"version": "0.21.4", "dev": true, "license": "MIT", "dependencies": {"follow-redirects": "^1.14.0"}}, "client/node_modules/babel-helper-evaluate-path": {"version": "0.5.0", "dev": true, "license": "MIT"}, "client/node_modules/babel-helper-mark-eval-scopes": {"version": "0.4.3", "dev": true, "license": "MIT"}, "client/node_modules/babel-helper-remove-or-void": {"version": "0.4.3", "dev": true, "license": "MIT"}, "client/node_modules/babel-loader": {"version": "8.2.1", "dev": true, "license": "MIT", "dependencies": {"find-cache-dir": "^2.1.0", "loader-utils": "^1.4.0", "make-dir": "^2.1.0", "pify": "^4.0.1", "schema-utils": "^2.6.5"}, "engines": {"node": ">= 8.9"}, "peerDependencies": {"@babel/core": "^7.0.0", "webpack": ">=2"}}, "client/node_modules/babel-loader/node_modules/json5": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "client/node_modules/babel-loader/node_modules/loader-utils": {"version": "1.4.2", "dev": true, "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^1.0.1"}, "engines": {"node": ">=4.0.0"}}, "client/node_modules/babel-plugin-const-enum": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@babel/plugin-syntax-typescript": "^7.3.3", "@babel/traverse": "^7.16.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "client/node_modules/babel-plugin-dynamic-import-node": {"version": "2.3.3", "dev": true, "license": "MIT", "dependencies": {"object.assign": "^4.1.0"}}, "client/node_modules/babel-plugin-minify-dead-code-elimination": {"version": "0.5.2", "dev": true, "license": "MIT", "dependencies": {"babel-helper-evaluate-path": "^0.5.0", "babel-helper-mark-eval-scopes": "^0.4.3", "babel-helper-remove-or-void": "^0.4.3", "lodash": "^4.17.11"}}, "client/node_modules/babel-plugin-polyfill-corejs2": {"version": "0.4.14", "dev": true, "license": "MIT", "dependencies": {"@babel/compat-data": "^7.27.7", "@babel/helper-define-polyfill-provider": "^0.6.5", "semver": "^6.3.1"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "client/node_modules/babel-plugin-polyfill-corejs3": {"version": "0.13.0", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.5", "core-js-compat": "^3.43.0"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "client/node_modules/babel-plugin-polyfill-regenerator": {"version": "0.6.5", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-define-polyfill-provider": "^0.6.5"}, "peerDependencies": {"@babel/core": "^7.4.0 || ^8.0.0-0 <8.0.0"}}, "client/node_modules/babel-plugin-transform-imports-api": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"is-invalid-path": "^1.0.2"}}, "client/node_modules/babel-plugin-transform-solid-jsx": {"version": "4.0.6", "dev": true, "license": "MIT", "dependencies": {"@babel/helper-module-imports": "7.18.6", "@babel/plugin-syntax-jsx": "^7.18.6", "html-entities": "2.3.3", "validate-html-nesting": "^1.2.1"}}, "client/node_modules/babel-plugin-transform-solid-jsx/node_modules/@babel/helper-module-imports": {"version": "7.18.6", "dev": true, "license": "MIT", "dependencies": {"@babel/types": "^7.18.6"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/babel-preset-taro": {"version": "4.0.6", "dev": true, "license": "MIT", "dependencies": {"@babel/plugin-proposal-decorators": "^7.24.1", "@babel/plugin-transform-class-properties": "^7.24.1", "@babel/plugin-transform-runtime": "^7.24.3", "@babel/preset-env": "^7.24.4", "@babel/preset-typescript": "^7.24.1", "@babel/runtime": "^7.24.4", "@babel/runtime-corejs3": "^7.24.4", "@rnx-kit/babel-preset-metro-react-native": "^1.1.8", "@tarojs/helper": "4.0.6", "babel-plugin-dynamic-import-node": "^2.3.3", "babel-plugin-minify-dead-code-elimination": "^0.5.2", "babel-plugin-transform-imports-api": "1.0.0", "babel-plugin-transform-solid-jsx": "4.0.6", "core-js": "^3.36.1"}, "engines": {"node": ">= 18"}, "peerDependencies": {"@babel/core": "^7.0.0", "@babel/preset-react": "^7.24.1", "@prefresh/babel-plugin": "^0.5.1", "@tarojs/taro-rn": "4.0.6", "@vue/babel-plugin-jsx": "^1.2.2", "babel-preset-solid": "^1.8.16", "react-refresh": "^0.14.0"}, "peerDependenciesMeta": {"@babel/preset-react": {"optional": true}, "@prefresh/babel-plugin": {"optional": true}, "@tarojs/taro-rn": {"optional": true}, "@vue/babel-plugin-jsx": {"optional": true}, "babel-preset-solid": {"optional": true}, "react-refresh": {"optional": true}}}, "client/node_modules/balanced-match": {"version": "1.0.2", "license": "MIT"}, "client/node_modules/base64-js": {"version": "1.5.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "client/node_modules/batch": {"version": "0.6.1", "devOptional": true, "license": "MIT"}, "client/node_modules/bcrypt-pbkdf": {"version": "1.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"tweetnacl": "^0.14.3"}}, "client/node_modules/big.js": {"version": "5.2.2", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "client/node_modules/bignumber.js": {"version": "9.3.0", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "client/node_modules/binary-extensions": {"version": "2.3.0", "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/bl": {"version": "4.1.0", "license": "MIT", "dependencies": {"buffer": "^5.5.0", "inherits": "^2.0.4", "readable-stream": "^3.4.0"}}, "client/node_modules/body-parser": {"version": "1.20.3", "devOptional": true, "license": "MIT", "dependencies": {"bytes": "3.1.2", "content-type": "~1.0.5", "debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "on-finished": "2.4.1", "qs": "6.13.0", "raw-body": "2.5.2", "type-is": "~1.6.18", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "client/node_modules/body-parser/node_modules/debug": {"version": "2.6.9", "devOptional": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "client/node_modules/body-parser/node_modules/ms": {"version": "2.0.0", "devOptional": true, "license": "MIT"}, "client/node_modules/body-parser/node_modules/qs": {"version": "6.13.0", "devOptional": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/bonjour-service": {"version": "1.3.0", "devOptional": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "multicast-dns": "^7.2.5"}}, "client/node_modules/boolbase": {"version": "1.0.0", "devOptional": true, "license": "ISC"}, "client/node_modules/brace-expansion": {"version": "1.1.12", "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "client/node_modules/braces": {"version": "3.0.3", "license": "MIT", "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "client/node_modules/browserslist": {"version": "4.25.1", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"caniuse-lite": "^1.0.30001726", "electron-to-chromium": "^1.5.173", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "client/node_modules/bson": {"version": "4.7.2", "license": "Apache-2.0", "dependencies": {"buffer": "^5.6.0"}, "engines": {"node": ">=6.9.0"}}, "client/node_modules/buffer": {"version": "5.7.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"base64-js": "^1.3.1", "ieee754": "^1.1.13"}}, "client/node_modules/buffer-alloc": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"buffer-alloc-unsafe": "^1.1.0", "buffer-fill": "^1.0.0"}}, "client/node_modules/buffer-alloc-unsafe": {"version": "1.1.0", "dev": true, "license": "MIT"}, "client/node_modules/buffer-crc32": {"version": "0.2.13", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "client/node_modules/buffer-equal-constant-time": {"version": "1.0.1", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/buffer-fill": {"version": "1.0.0", "dev": true, "license": "MIT"}, "client/node_modules/buffer-from": {"version": "1.1.2", "license": "MIT"}, "client/node_modules/bytes": {"version": "3.1.2", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "client/node_modules/cacheable-request": {"version": "2.1.4", "dev": true, "license": "MIT", "dependencies": {"clone-response": "1.0.2", "get-stream": "3.0.0", "http-cache-semantics": "3.8.1", "keyv": "3.0.0", "lowercase-keys": "1.0.0", "normalize-url": "2.0.1", "responselike": "1.0.2"}}, "client/node_modules/cacheable-request/node_modules/json-buffer": {"version": "3.0.0", "dev": true, "license": "MIT"}, "client/node_modules/cacheable-request/node_modules/keyv": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.0"}}, "client/node_modules/cacheable-request/node_modules/lowercase-keys": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/call-bind": {"version": "1.0.8", "dev": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.0", "es-define-property": "^1.0.0", "get-intrinsic": "^1.2.4", "set-function-length": "^1.2.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/call-bind-apply-helpers": {"version": "1.0.2", "devOptional": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/call-bound": {"version": "1.0.4", "devOptional": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "get-intrinsic": "^1.3.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/callsites": {"version": "3.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/camel-case": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"no-case": "^2.2.0", "upper-case": "^1.1.1"}}, "client/node_modules/camelcase": {"version": "5.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/camelcase-keys": {"version": "6.2.2", "dev": true, "license": "MIT", "dependencies": {"camelcase": "^5.3.1", "map-obj": "^4.0.0", "quick-lru": "^4.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/caniuse-api": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.0.0", "caniuse-lite": "^1.0.0", "lodash.memoize": "^4.1.2", "lodash.uniq": "^4.5.0"}}, "client/node_modules/caniuse-lite": {"version": "1.0.30001726", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "CC-BY-4.0"}, "client/node_modules/caseless": {"version": "0.12.0", "dev": true, "license": "Apache-2.0"}, "client/node_modules/caw": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"get-proxy": "^2.0.0", "isurl": "^1.0.0-alpha5", "tunnel-agent": "^0.6.0", "url-to-options": "^1.0.1"}, "engines": {"node": ">=4"}}, "client/node_modules/chardet": {"version": "0.7.0", "dev": true, "license": "MIT"}, "client/node_modules/charenc": {"version": "0.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "client/node_modules/chokidar": {"version": "3.6.0", "license": "MIT", "dependencies": {"anymatch": "~3.1.2", "braces": "~3.0.2", "glob-parent": "~5.1.2", "is-binary-path": "~2.1.0", "is-glob": "~4.0.1", "normalize-path": "~3.0.0", "readdirp": "~3.6.0"}, "engines": {"node": ">= 8.10.0"}, "funding": {"url": "https://paulmillr.com/funding/"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "client/node_modules/chrome-trace-event": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">=6.0"}}, "client/node_modules/ci-info": {"version": "3.9.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/classnames": {"version": "2.5.1", "license": "MIT"}, "client/node_modules/clean-css": {"version": "4.2.4", "dev": true, "license": "MIT", "dependencies": {"source-map": "~0.6.0"}, "engines": {"node": ">= 4.0"}}, "client/node_modules/cli-cursor": {"version": "3.1.0", "license": "MIT", "dependencies": {"restore-cursor": "^3.1.0"}, "engines": {"node": ">=8"}}, "client/node_modules/cli-highlight": {"version": "2.1.11", "dev": true, "license": "ISC", "dependencies": {"chalk": "^4.0.0", "highlight.js": "^10.7.1", "mz": "^2.4.0", "parse5": "^5.1.1", "parse5-htmlparser2-tree-adapter": "^6.0.0", "yargs": "^16.0.0"}, "bin": {"highlight": "bin/highlight"}, "engines": {"node": ">=8.0.0", "npm": ">=5.0.0"}}, "client/node_modules/cli-spinners": {"version": "2.9.2", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/cli-width": {"version": "3.0.0", "dev": true, "license": "ISC", "engines": {"node": ">= 10"}}, "client/node_modules/cliui": {"version": "7.0.4", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.0", "wrap-ansi": "^7.0.0"}}, "client/node_modules/cliui/node_modules/wrap-ansi": {"version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "client/node_modules/clone": {"version": "2.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "client/node_modules/clone-deep": {"version": "4.0.1", "license": "MIT", "dependencies": {"is-plain-object": "^2.0.4", "kind-of": "^6.0.2", "shallow-clone": "^3.0.0"}, "engines": {"node": ">=6"}}, "client/node_modules/clone-response": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"mimic-response": "^1.0.0"}}, "client/node_modules/cloudbase-adapter-wx_mp": {"version": "0.2.0", "license": "ISC", "dependencies": {"@cloudbase/adapter-interface": "^0.4.0"}}, "client/node_modules/colord": {"version": "2.9.3", "dev": true, "license": "MIT"}, "client/node_modules/colorette": {"version": "2.0.20", "devOptional": true, "license": "MIT"}, "client/node_modules/combined-stream": {"version": "1.0.8", "dev": true, "license": "MIT", "dependencies": {"delayed-stream": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "client/node_modules/commander": {"version": "2.20.3", "license": "MIT"}, "client/node_modules/commondir": {"version": "1.0.1", "dev": true, "license": "MIT"}, "client/node_modules/compressible": {"version": "2.0.18", "devOptional": true, "license": "MIT", "dependencies": {"mime-db": ">= 1.43.0 < 2"}, "engines": {"node": ">= 0.6"}}, "client/node_modules/compression": {"version": "1.8.0", "devOptional": true, "license": "MIT", "dependencies": {"bytes": "3.1.2", "compressible": "~2.0.18", "debug": "2.6.9", "negotiator": "~0.6.4", "on-headers": "~1.0.2", "safe-buffer": "5.2.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.8.0"}}, "client/node_modules/compression/node_modules/debug": {"version": "2.6.9", "devOptional": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "client/node_modules/compression/node_modules/ms": {"version": "2.0.0", "devOptional": true, "license": "MIT"}, "client/node_modules/concat-map": {"version": "0.0.1", "license": "MIT"}, "client/node_modules/config-chain": {"version": "1.1.13", "dev": true, "license": "MIT", "dependencies": {"ini": "^1.3.4", "proto-list": "~1.2.1"}}, "client/node_modules/connect-history-api-fallback": {"version": "2.0.0", "devOptional": true, "license": "MIT", "engines": {"node": ">=0.8"}}, "client/node_modules/consola": {"version": "2.15.3", "dev": true, "license": "MIT"}, "client/node_modules/content-disposition": {"version": "0.5.4", "devOptional": true, "license": "MIT", "dependencies": {"safe-buffer": "5.2.1"}, "engines": {"node": ">= 0.6"}}, "client/node_modules/content-type": {"version": "1.0.5", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/convert-source-map": {"version": "2.0.0", "license": "MIT"}, "client/node_modules/cookie": {"version": "0.7.1", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/cookie-signature": {"version": "1.0.6", "devOptional": true, "license": "MIT"}, "client/node_modules/copy-anything": {"version": "2.0.6", "dev": true, "license": "MIT", "dependencies": {"is-what": "^3.14.1"}, "funding": {"url": "https://github.com/sponsors/mesqueeb"}}, "client/node_modules/copy-webpack-plugin": {"version": "12.0.2", "dev": true, "license": "MIT", "dependencies": {"fast-glob": "^3.3.2", "glob-parent": "^6.0.1", "globby": "^14.0.0", "normalize-path": "^3.0.0", "schema-utils": "^4.2.0", "serialize-javascript": "^6.0.2"}, "engines": {"node": ">= 18.12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}}, "client/node_modules/copy-webpack-plugin/node_modules/ajv": {"version": "8.17.1", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "client/node_modules/copy-webpack-plugin/node_modules/ajv-keywords": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "client/node_modules/copy-webpack-plugin/node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "client/node_modules/copy-webpack-plugin/node_modules/globby": {"version": "14.1.0", "dev": true, "license": "MIT", "dependencies": {"@sindresorhus/merge-streams": "^2.1.0", "fast-glob": "^3.3.3", "ignore": "^7.0.3", "path-type": "^6.0.0", "slash": "^5.1.0", "unicorn-magic": "^0.3.0"}, "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/copy-webpack-plugin/node_modules/ignore": {"version": "7.0.5", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "client/node_modules/copy-webpack-plugin/node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT"}, "client/node_modules/copy-webpack-plugin/node_modules/path-type": {"version": "6.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/copy-webpack-plugin/node_modules/schema-utils": {"version": "4.3.2", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "client/node_modules/copy-webpack-plugin/node_modules/serialize-javascript": {"version": "6.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "client/node_modules/copy-webpack-plugin/node_modules/slash": {"version": "5.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=14.16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/core-js": {"version": "3.43.0", "dev": true, "hasInstallScript": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "client/node_modules/core-js-compat": {"version": "3.43.0", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.25.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "client/node_modules/core-js-pure": {"version": "3.43.0", "dev": true, "hasInstallScript": true, "license": "MIT", "funding": {"type": "opencollective", "url": "https://opencollective.com/core-js"}}, "client/node_modules/core-util-is": {"version": "1.0.2", "devOptional": true, "license": "MIT"}, "client/node_modules/cosmiconfig": {"version": "9.0.0", "dev": true, "license": "MIT", "dependencies": {"env-paths": "^2.2.1", "import-fresh": "^3.3.0", "js-yaml": "^4.1.0", "parse-json": "^5.2.0"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/d-fischer"}, "peerDependencies": {"typescript": ">=4.9.5"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "client/node_modules/cross-spawn": {"version": "7.0.6", "license": "MIT", "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "client/node_modules/cross-spawn/node_modules/which": {"version": "2.0.2", "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "client/node_modules/crypt": {"version": "0.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "client/node_modules/crypto-js": {"version": "4.2.0", "license": "MIT"}, "client/node_modules/css-declaration-sorter": {"version": "7.2.0", "dev": true, "license": "ISC", "engines": {"node": "^14 || ^16 || >=18"}, "peerDependencies": {"postcss": "^8.0.9"}}, "client/node_modules/css-functions-list": {"version": "3.2.3", "dev": true, "license": "MIT", "engines": {"node": ">=12 || >=16"}}, "client/node_modules/css-loader": {"version": "7.1.2", "dev": true, "license": "MIT", "dependencies": {"icss-utils": "^5.1.0", "postcss": "^8.4.33", "postcss-modules-extract-imports": "^3.1.0", "postcss-modules-local-by-default": "^4.0.5", "postcss-modules-scope": "^3.2.0", "postcss-modules-values": "^4.0.0", "postcss-value-parser": "^4.2.0", "semver": "^7.5.4"}, "engines": {"node": ">= 18.12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "webpack": "^5.27.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}}, "client/node_modules/css-loader/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "client/node_modules/css-minimizer-webpack-plugin": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "^0.3.21", "cssnano": "^6.0.3", "jest-worker": "^29.7.0", "postcss": "^8.4.33", "schema-utils": "^4.2.0", "serialize-javascript": "^6.0.2"}, "engines": {"node": ">= 18.12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}, "peerDependenciesMeta": {"@parcel/css": {"optional": true}, "@swc/css": {"optional": true}, "clean-css": {"optional": true}, "csso": {"optional": true}, "esbuild": {"optional": true}, "lightningcss": {"optional": true}}}, "client/node_modules/css-minimizer-webpack-plugin/node_modules/ajv": {"version": "8.17.1", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "client/node_modules/css-minimizer-webpack-plugin/node_modules/ajv-keywords": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "client/node_modules/css-minimizer-webpack-plugin/node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT"}, "client/node_modules/css-minimizer-webpack-plugin/node_modules/schema-utils": {"version": "4.3.2", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "client/node_modules/css-minimizer-webpack-plugin/node_modules/serialize-javascript": {"version": "6.0.2", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "client/node_modules/css-select": {"version": "4.3.0", "devOptional": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.0.1", "domhandler": "^4.3.1", "domutils": "^2.8.0", "nth-check": "^2.0.1"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "client/node_modules/css-tree": {"version": "1.0.0-alpha.29", "dev": true, "license": "MIT", "dependencies": {"mdn-data": "~1.1.0", "source-map": "^0.5.3"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/css-tree/node_modules/source-map": {"version": "0.5.7", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "client/node_modules/css-what": {"version": "6.2.2", "devOptional": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">= 6"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "client/node_modules/cssesc": {"version": "3.0.0", "dev": true, "license": "MIT", "bin": {"cssesc": "bin/cssesc"}, "engines": {"node": ">=4"}}, "client/node_modules/cssnano": {"version": "6.1.2", "dev": true, "license": "MIT", "dependencies": {"cssnano-preset-default": "^6.1.2", "lilconfig": "^3.1.1"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/cssnano"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/cssnano-preset-default": {"version": "6.1.2", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.23.0", "css-declaration-sorter": "^7.2.0", "cssnano-utils": "^4.0.2", "postcss-calc": "^9.0.1", "postcss-colormin": "^6.1.0", "postcss-convert-values": "^6.1.0", "postcss-discard-comments": "^6.0.2", "postcss-discard-duplicates": "^6.0.3", "postcss-discard-empty": "^6.0.3", "postcss-discard-overridden": "^6.0.2", "postcss-merge-longhand": "^6.0.5", "postcss-merge-rules": "^6.1.1", "postcss-minify-font-values": "^6.1.0", "postcss-minify-gradients": "^6.0.3", "postcss-minify-params": "^6.1.0", "postcss-minify-selectors": "^6.0.4", "postcss-normalize-charset": "^6.0.2", "postcss-normalize-display-values": "^6.0.2", "postcss-normalize-positions": "^6.0.2", "postcss-normalize-repeat-style": "^6.0.2", "postcss-normalize-string": "^6.0.2", "postcss-normalize-timing-functions": "^6.0.2", "postcss-normalize-unicode": "^6.1.0", "postcss-normalize-url": "^6.0.2", "postcss-normalize-whitespace": "^6.0.2", "postcss-ordered-values": "^6.0.2", "postcss-reduce-initial": "^6.1.0", "postcss-reduce-transforms": "^6.0.2", "postcss-svgo": "^6.0.3", "postcss-unique-selectors": "^6.0.4"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/cssnano-utils": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/csso": {"version": "3.5.1", "dev": true, "license": "MIT", "dependencies": {"css-tree": "1.0.0-alpha.29"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/cssstyle": {"version": "4.6.0", "dev": true, "license": "MIT", "dependencies": {"@asamuzakjp/css-color": "^3.2.0", "rrweb-cssom": "^0.8.0"}, "engines": {"node": ">=18"}}, "client/node_modules/cssstyle/node_modules/rrweb-cssom": {"version": "0.8.0", "dev": true, "license": "MIT"}, "client/node_modules/csstype": {"version": "3.1.3", "license": "MIT"}, "client/node_modules/cuint": {"version": "0.2.2", "dev": true, "license": "MIT"}, "client/node_modules/dashdash": {"version": "1.14.1", "dev": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}, "engines": {"node": ">=0.10"}}, "client/node_modules/data-urls": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"whatwg-mimetype": "^4.0.0", "whatwg-url": "^14.0.0"}, "engines": {"node": ">=18"}}, "client/node_modules/data-view-buffer": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/data-view-byte-length": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-data-view": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/inspect-js"}}, "client/node_modules/data-view-byte-offset": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-data-view": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/debug": {"version": "4.4.1", "license": "MIT", "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "client/node_modules/decamelize": {"version": "1.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/decamelize-keys": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"decamelize": "^1.1.0", "map-obj": "^1.0.0"}, "engines": {"node": ">=0.10.0"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/decamelize-keys/node_modules/map-obj": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/decimal.js": {"version": "10.5.0", "dev": true, "license": "MIT"}, "client/node_modules/decode-uri-component": {"version": "0.2.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10"}}, "client/node_modules/decompress": {"version": "4.2.1", "dev": true, "license": "MIT", "dependencies": {"decompress-tar": "^4.0.0", "decompress-tarbz2": "^4.0.0", "decompress-targz": "^4.0.0", "decompress-unzip": "^4.0.1", "graceful-fs": "^4.1.10", "make-dir": "^1.0.0", "pify": "^2.3.0", "strip-dirs": "^2.0.0"}, "engines": {"node": ">=4"}}, "client/node_modules/decompress-response": {"version": "3.3.0", "dev": true, "license": "MIT", "dependencies": {"mimic-response": "^1.0.0"}, "engines": {"node": ">=4"}}, "client/node_modules/decompress-tar": {"version": "4.1.1", "dev": true, "license": "MIT", "dependencies": {"file-type": "^5.2.0", "is-stream": "^1.1.0", "tar-stream": "^1.5.2"}, "engines": {"node": ">=4"}}, "client/node_modules/decompress-tar/node_modules/file-type": {"version": "5.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/decompress-tar/node_modules/is-stream": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/decompress-tarbz2": {"version": "4.1.1", "dev": true, "license": "MIT", "dependencies": {"decompress-tar": "^4.1.0", "file-type": "^6.1.0", "is-stream": "^1.1.0", "seek-bzip": "^1.0.5", "unbzip2-stream": "^1.0.9"}, "engines": {"node": ">=4"}}, "client/node_modules/decompress-tarbz2/node_modules/file-type": {"version": "6.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/decompress-tarbz2/node_modules/is-stream": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/decompress-targz": {"version": "4.1.1", "dev": true, "license": "MIT", "dependencies": {"decompress-tar": "^4.1.1", "file-type": "^5.2.0", "is-stream": "^1.1.0"}, "engines": {"node": ">=4"}}, "client/node_modules/decompress-targz/node_modules/file-type": {"version": "5.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/decompress-targz/node_modules/is-stream": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/decompress-unzip": {"version": "4.0.1", "dev": true, "license": "MIT", "dependencies": {"file-type": "^3.8.0", "get-stream": "^2.2.0", "pify": "^2.3.0", "yauzl": "^2.4.2"}, "engines": {"node": ">=4"}}, "client/node_modules/decompress-unzip/node_modules/file-type": {"version": "3.9.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/decompress-unzip/node_modules/get-stream": {"version": "2.3.1", "dev": true, "license": "MIT", "dependencies": {"object-assign": "^4.0.1", "pinkie-promise": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/decompress-unzip/node_modules/pify": {"version": "2.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/decompress/node_modules/make-dir": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "client/node_modules/decompress/node_modules/make-dir/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/decompress/node_modules/pify": {"version": "2.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/deep-extend": {"version": "0.6.0", "dev": true, "license": "MIT", "engines": {"node": ">=4.0.0"}}, "client/node_modules/deep-is": {"version": "0.1.4", "dev": true, "license": "MIT"}, "client/node_modules/deepmerge": {"version": "1.5.2", "devOptional": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/default-gateway": {"version": "6.0.3", "devOptional": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"execa": "^5.0.0"}, "engines": {"node": ">= 10"}}, "client/node_modules/defaults": {"version": "1.0.4", "license": "MIT", "dependencies": {"clone": "^1.0.2"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/defaults/node_modules/clone": {"version": "1.0.4", "license": "MIT", "engines": {"node": ">=0.8"}}, "client/node_modules/defer-to-connect": {"version": "1.1.3", "dev": true, "license": "MIT"}, "client/node_modules/define-data-property": {"version": "1.1.4", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0", "es-errors": "^1.3.0", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/define-lazy-prop": {"version": "2.0.0", "devOptional": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/define-properties": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.0.1", "has-property-descriptors": "^1.0.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/delayed-stream": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.4.0"}}, "client/node_modules/depd": {"version": "2.0.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "client/node_modules/destroy": {"version": "1.2.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.8", "npm": "1.2.8000 || >= 1.4.16"}}, "client/node_modules/detect-libc": {"version": "2.0.4", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=8"}}, "client/node_modules/detect-node": {"version": "2.1.0", "devOptional": true, "license": "MIT"}, "client/node_modules/detect-port": {"version": "1.6.1", "dev": true, "license": "MIT", "dependencies": {"address": "^1.0.1", "debug": "4"}, "bin": {"detect": "bin/detect-port.js", "detect-port": "bin/detect-port.js"}, "engines": {"node": ">= 4.0.0"}}, "client/node_modules/dir-glob": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"path-type": "^4.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/dns-packet": {"version": "5.6.1", "devOptional": true, "license": "MIT", "dependencies": {"@leichtgewicht/ip-codec": "^2.0.1"}, "engines": {"node": ">=6"}}, "client/node_modules/doctrine": {"version": "3.0.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=6.0.0"}}, "client/node_modules/dom-converter": {"version": "0.2.0", "devOptional": true, "license": "MIT", "dependencies": {"utila": "~0.4"}}, "client/node_modules/dom-serializer": {"version": "1.4.1", "devOptional": true, "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.2.0", "entities": "^2.0.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "client/node_modules/dom-serializer/node_modules/entities": {"version": "2.2.0", "devOptional": true, "license": "BSD-2-<PERSON><PERSON>", "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "client/node_modules/domelementtype": {"version": "2.3.0", "devOptional": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "BSD-2-<PERSON><PERSON>"}, "client/node_modules/domhandler": {"version": "4.3.1", "devOptional": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.2.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "client/node_modules/domutils": {"version": "2.8.0", "devOptional": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^1.0.1", "domelementtype": "^2.2.0", "domhandler": "^4.2.0"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "client/node_modules/dot-case": {"version": "3.0.4", "devOptional": true, "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "client/node_modules/dot-case/node_modules/lower-case": {"version": "2.0.2", "devOptional": true, "license": "MIT", "dependencies": {"tslib": "^2.0.3"}}, "client/node_modules/dot-case/node_modules/no-case": {"version": "3.0.4", "devOptional": true, "license": "MIT", "dependencies": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "client/node_modules/dot-case/node_modules/tslib": {"version": "2.8.1", "devOptional": true, "license": "0BSD"}, "client/node_modules/dotenv": {"version": "16.6.1", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "client/node_modules/dotenv-expand": {"version": "11.0.7", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dotenv": "^16.4.5"}, "engines": {"node": ">=12"}, "funding": {"url": "https://dotenvx.com"}}, "client/node_modules/download": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"archive-type": "^4.0.0", "caw": "^2.0.1", "content-disposition": "^0.5.2", "decompress": "^4.2.0", "ext-name": "^5.0.0", "file-type": "^8.1.0", "filenamify": "^2.0.0", "get-stream": "^3.0.0", "got": "^8.3.1", "make-dir": "^1.2.0", "p-event": "^2.1.0", "pify": "^3.0.0"}, "engines": {"node": ">=6"}}, "client/node_modules/download-git-repo": {"version": "3.0.2", "dev": true, "license": "MIT", "dependencies": {"download": "^7.1.0", "git-clone": "^0.1.0", "rimraf": "^3.0.0"}}, "client/node_modules/download-git-repo/node_modules/rimraf": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "client/node_modules/download/node_modules/make-dir": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "client/node_modules/download/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/dunder-proto": {"version": "1.0.1", "devOptional": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.1", "es-errors": "^1.3.0", "gopd": "^1.2.0"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/duplexer3": {"version": "0.1.5", "dev": true, "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/eastasianwidth": {"version": "0.2.0", "dev": true, "license": "MIT"}, "client/node_modules/ecc-jsbn": {"version": "0.1.2", "dev": true, "license": "MIT", "dependencies": {"jsbn": "~0.1.0", "safer-buffer": "^2.1.0"}}, "client/node_modules/ecdsa-sig-formatter": {"version": "1.0.11", "dev": true, "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}}, "client/node_modules/ee-first": {"version": "1.1.1", "devOptional": true, "license": "MIT"}, "client/node_modules/electron-to-chromium": {"version": "1.5.178", "license": "ISC"}, "client/node_modules/emojis-list": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "client/node_modules/encodeurl": {"version": "2.0.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "client/node_modules/end-of-stream": {"version": "1.4.5", "dev": true, "license": "MIT", "dependencies": {"once": "^1.4.0"}}, "client/node_modules/enhanced-resolve": {"version": "5.18.2", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.4", "tapable": "^2.2.0"}, "engines": {"node": ">=10.13.0"}}, "client/node_modules/entities": {"version": "4.5.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "client/node_modules/env-paths": {"version": "2.2.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/envinfo": {"version": "7.14.0", "dev": true, "license": "MIT", "bin": {"envinfo": "dist/cli.js"}, "engines": {"node": ">=4"}}, "client/node_modules/errno": {"version": "0.1.8", "dev": true, "license": "MIT", "optional": true, "dependencies": {"prr": "~1.0.1"}, "bin": {"errno": "cli.js"}}, "client/node_modules/error-ex": {"version": "1.3.2", "dev": true, "license": "MIT", "dependencies": {"is-arrayish": "^0.2.1"}}, "client/node_modules/es-abstract": {"version": "1.24.0", "dev": true, "license": "MIT", "dependencies": {"array-buffer-byte-length": "^1.0.2", "arraybuffer.prototype.slice": "^1.0.4", "available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "data-view-buffer": "^1.0.2", "data-view-byte-length": "^1.0.2", "data-view-byte-offset": "^1.0.1", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "es-set-tostringtag": "^2.1.0", "es-to-primitive": "^1.3.0", "function.prototype.name": "^1.1.8", "get-intrinsic": "^1.3.0", "get-proto": "^1.0.1", "get-symbol-description": "^1.1.0", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "internal-slot": "^1.1.0", "is-array-buffer": "^3.0.5", "is-callable": "^1.2.7", "is-data-view": "^1.0.2", "is-negative-zero": "^2.0.3", "is-regex": "^1.2.1", "is-set": "^2.0.3", "is-shared-array-buffer": "^1.0.4", "is-string": "^1.1.1", "is-typed-array": "^1.1.15", "is-weakref": "^1.1.1", "math-intrinsics": "^1.1.0", "object-inspect": "^1.13.4", "object-keys": "^1.1.1", "object.assign": "^4.1.7", "own-keys": "^1.0.1", "regexp.prototype.flags": "^1.5.4", "safe-array-concat": "^1.1.3", "safe-push-apply": "^1.0.0", "safe-regex-test": "^1.1.0", "set-proto": "^1.0.0", "stop-iteration-iterator": "^1.1.0", "string.prototype.trim": "^1.2.10", "string.prototype.trimend": "^1.0.9", "string.prototype.trimstart": "^1.0.8", "typed-array-buffer": "^1.0.3", "typed-array-byte-length": "^1.0.3", "typed-array-byte-offset": "^1.0.4", "typed-array-length": "^1.0.7", "unbox-primitive": "^1.1.0", "which-typed-array": "^1.1.19"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/es-define-property": {"version": "1.0.1", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "client/node_modules/es-errors": {"version": "1.3.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "client/node_modules/es-iterator-helpers": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-abstract": "^1.23.6", "es-errors": "^1.3.0", "es-set-tostringtag": "^2.0.3", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.6", "globalthis": "^1.0.4", "gopd": "^1.2.0", "has-property-descriptors": "^1.0.2", "has-proto": "^1.2.0", "has-symbols": "^1.1.0", "internal-slot": "^1.1.0", "iterator.prototype": "^1.1.4", "safe-array-concat": "^1.1.3"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/es-module-lexer": {"version": "0.10.5", "dev": true, "license": "MIT"}, "client/node_modules/es-object-atoms": {"version": "1.1.1", "devOptional": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/es-set-tostringtag": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "get-intrinsic": "^1.2.6", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/es-shim-unscopables": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/es-to-primitive": {"version": "1.3.0", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.2.7", "is-date-object": "^1.0.5", "is-symbol": "^1.0.4"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/esbuild": {"version": "0.21.5", "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=12"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.21.5", "@esbuild/android-arm": "0.21.5", "@esbuild/android-arm64": "0.21.5", "@esbuild/android-x64": "0.21.5", "@esbuild/darwin-arm64": "0.21.5", "@esbuild/darwin-x64": "0.21.5", "@esbuild/freebsd-arm64": "0.21.5", "@esbuild/freebsd-x64": "0.21.5", "@esbuild/linux-arm": "0.21.5", "@esbuild/linux-arm64": "0.21.5", "@esbuild/linux-ia32": "0.21.5", "@esbuild/linux-loong64": "0.21.5", "@esbuild/linux-mips64el": "0.21.5", "@esbuild/linux-ppc64": "0.21.5", "@esbuild/linux-riscv64": "0.21.5", "@esbuild/linux-s390x": "0.21.5", "@esbuild/linux-x64": "0.21.5", "@esbuild/netbsd-x64": "0.21.5", "@esbuild/openbsd-x64": "0.21.5", "@esbuild/sunos-x64": "0.21.5", "@esbuild/win32-arm64": "0.21.5", "@esbuild/win32-ia32": "0.21.5", "@esbuild/win32-x64": "0.21.5"}}, "client/node_modules/esbuild-loader": {"version": "4.3.0", "dev": true, "license": "MIT", "dependencies": {"esbuild": "^0.25.0", "get-tsconfig": "^4.7.0", "loader-utils": "^2.0.4", "webpack-sources": "^1.4.3"}, "funding": {"url": "https://github.com/privatenumber/esbuild-loader?sponsor=1"}, "peerDependencies": {"webpack": "^4.40.0 || ^5.0.0"}}, "client/node_modules/esbuild-loader/node_modules/@esbuild/darwin-arm64": {"version": "0.25.5", "cpu": ["arm64"], "dev": true, "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": ">=18"}}, "client/node_modules/esbuild-loader/node_modules/esbuild": {"version": "0.25.5", "dev": true, "hasInstallScript": true, "license": "MIT", "bin": {"esbuild": "bin/esbuild"}, "engines": {"node": ">=18"}, "optionalDependencies": {"@esbuild/aix-ppc64": "0.25.5", "@esbuild/android-arm": "0.25.5", "@esbuild/android-arm64": "0.25.5", "@esbuild/android-x64": "0.25.5", "@esbuild/darwin-arm64": "0.25.5", "@esbuild/darwin-x64": "0.25.5", "@esbuild/freebsd-arm64": "0.25.5", "@esbuild/freebsd-x64": "0.25.5", "@esbuild/linux-arm": "0.25.5", "@esbuild/linux-arm64": "0.25.5", "@esbuild/linux-ia32": "0.25.5", "@esbuild/linux-loong64": "0.25.5", "@esbuild/linux-mips64el": "0.25.5", "@esbuild/linux-ppc64": "0.25.5", "@esbuild/linux-riscv64": "0.25.5", "@esbuild/linux-s390x": "0.25.5", "@esbuild/linux-x64": "0.25.5", "@esbuild/netbsd-arm64": "0.25.5", "@esbuild/netbsd-x64": "0.25.5", "@esbuild/openbsd-arm64": "0.25.5", "@esbuild/openbsd-x64": "0.25.5", "@esbuild/sunos-x64": "0.25.5", "@esbuild/win32-arm64": "0.25.5", "@esbuild/win32-ia32": "0.25.5", "@esbuild/win32-x64": "0.25.5"}}, "client/node_modules/escape-html": {"version": "1.0.3", "devOptional": true, "license": "MIT"}, "client/node_modules/escape-string-regexp": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/eslint": {"version": "8.57.1", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.2.0", "@eslint-community/regexpp": "^4.6.1", "@eslint/eslintrc": "^2.1.4", "@eslint/js": "8.57.1", "@humanwhocodes/config-array": "^0.13.0", "@humanwhocodes/module-importer": "^1.0.1", "@nodelib/fs.walk": "^1.2.8", "@ungap/structured-clone": "^1.2.0", "ajv": "^6.12.4", "chalk": "^4.0.0", "cross-spawn": "^7.0.2", "debug": "^4.3.2", "doctrine": "^3.0.0", "escape-string-regexp": "^4.0.0", "eslint-scope": "^7.2.2", "eslint-visitor-keys": "^3.4.3", "espree": "^9.6.1", "esquery": "^1.4.2", "esutils": "^2.0.2", "fast-deep-equal": "^3.1.3", "file-entry-cache": "^6.0.1", "find-up": "^5.0.0", "glob-parent": "^6.0.2", "globals": "^13.19.0", "graphemer": "^1.4.0", "ignore": "^5.2.0", "imurmurhash": "^0.1.4", "is-glob": "^4.0.0", "is-path-inside": "^3.0.3", "js-yaml": "^4.1.0", "json-stable-stringify-without-jsonify": "^1.0.1", "levn": "^0.4.1", "lodash.merge": "^4.6.2", "minimatch": "^3.1.2", "natural-compare": "^1.4.0", "optionator": "^0.9.3", "strip-ansi": "^6.0.1", "text-table": "^0.2.0"}, "bin": {"eslint": "bin/eslint.js"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "client/node_modules/eslint-config-taro": {"version": "4.0.6", "dev": true, "license": "MIT", "dependencies": {"@babel/eslint-parser": "^7.24.1", "@typescript-eslint/eslint-plugin": "^6.2.0", "@typescript-eslint/parser": "^6.2.0", "eslint-plugin-import": "^2.29.1"}, "engines": {"node": ">= 18"}, "peerDependencies": {"eslint": "^8", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.4.0", "eslint-plugin-vue": "^9.17.0"}, "peerDependenciesMeta": {"eslint-plugin-react": {"optional": true}, "eslint-plugin-react-hooks": {"optional": true}, "eslint-plugin-vue": {"optional": true}}}, "client/node_modules/eslint-config-taro/node_modules/@typescript-eslint/eslint-plugin": {"version": "6.21.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/regexpp": "^4.5.1", "@typescript-eslint/scope-manager": "6.21.0", "@typescript-eslint/type-utils": "6.21.0", "@typescript-eslint/utils": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0", "debug": "^4.3.4", "graphemer": "^1.4.0", "ignore": "^5.2.4", "natural-compare": "^1.4.0", "semver": "^7.5.4", "ts-api-utils": "^1.0.1"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"@typescript-eslint/parser": "^6.0.0 || ^6.0.0-alpha", "eslint": "^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "client/node_modules/eslint-config-taro/node_modules/@typescript-eslint/parser": {"version": "6.21.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/scope-manager": "6.21.0", "@typescript-eslint/types": "6.21.0", "@typescript-eslint/typescript-estree": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0", "debug": "^4.3.4"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "client/node_modules/eslint-config-taro/node_modules/@typescript-eslint/scope-manager": {"version": "6.21.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "client/node_modules/eslint-config-taro/node_modules/@typescript-eslint/type-utils": {"version": "6.21.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/typescript-estree": "6.21.0", "@typescript-eslint/utils": "6.21.0", "debug": "^4.3.4", "ts-api-utils": "^1.0.1"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "client/node_modules/eslint-config-taro/node_modules/@typescript-eslint/types": {"version": "6.21.0", "dev": true, "license": "MIT", "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "client/node_modules/eslint-config-taro/node_modules/@typescript-eslint/typescript-estree": {"version": "6.21.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@typescript-eslint/types": "6.21.0", "@typescript-eslint/visitor-keys": "6.21.0", "debug": "^4.3.4", "globby": "^11.1.0", "is-glob": "^4.0.3", "minimatch": "9.0.3", "semver": "^7.5.4", "ts-api-utils": "^1.0.1"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "client/node_modules/eslint-config-taro/node_modules/@typescript-eslint/utils": {"version": "6.21.0", "dev": true, "license": "MIT", "dependencies": {"@eslint-community/eslint-utils": "^4.4.0", "@types/json-schema": "^7.0.12", "@types/semver": "^7.5.0", "@typescript-eslint/scope-manager": "6.21.0", "@typescript-eslint/types": "6.21.0", "@typescript-eslint/typescript-estree": "6.21.0", "semver": "^7.5.4"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}, "peerDependencies": {"eslint": "^7.0.0 || ^8.0.0"}}, "client/node_modules/eslint-config-taro/node_modules/@typescript-eslint/visitor-keys": {"version": "6.21.0", "dev": true, "license": "MIT", "dependencies": {"@typescript-eslint/types": "6.21.0", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^16.0.0 || >=18.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/typescript-eslint"}}, "client/node_modules/eslint-config-taro/node_modules/brace-expansion": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0"}}, "client/node_modules/eslint-config-taro/node_modules/minimatch": {"version": "9.0.3", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=16 || 14 >=14.17"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "client/node_modules/eslint-config-taro/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "client/node_modules/eslint-import-resolver-node": {"version": "0.3.9", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7", "is-core-module": "^2.13.0", "resolve": "^1.22.4"}}, "client/node_modules/eslint-import-resolver-node/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "client/node_modules/eslint-module-utils": {"version": "2.12.1", "dev": true, "license": "MIT", "dependencies": {"debug": "^3.2.7"}, "engines": {"node": ">=4"}, "peerDependenciesMeta": {"eslint": {"optional": true}}}, "client/node_modules/eslint-module-utils/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "client/node_modules/eslint-plugin-import": {"version": "2.32.0", "dev": true, "license": "MIT", "dependencies": {"@rtsao/scc": "^1.1.0", "array-includes": "^3.1.9", "array.prototype.findlastindex": "^1.2.6", "array.prototype.flat": "^1.3.3", "array.prototype.flatmap": "^1.3.3", "debug": "^3.2.7", "doctrine": "^2.1.0", "eslint-import-resolver-node": "^0.3.9", "eslint-module-utils": "^2.12.1", "hasown": "^2.0.2", "is-core-module": "^2.16.1", "is-glob": "^4.0.3", "minimatch": "^3.1.2", "object.fromentries": "^2.0.8", "object.groupby": "^1.0.3", "object.values": "^1.2.1", "semver": "^6.3.1", "string.prototype.trimend": "^1.0.9", "tsconfig-paths": "^3.15.0"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^2 || ^3 || ^4 || ^5 || ^6 || ^7.2.0 || ^8 || ^9"}}, "client/node_modules/eslint-plugin-import/node_modules/debug": {"version": "3.2.7", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.1.1"}}, "client/node_modules/eslint-plugin-import/node_modules/doctrine": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/eslint-plugin-react": {"version": "7.37.5", "dev": true, "license": "MIT", "dependencies": {"array-includes": "^3.1.8", "array.prototype.findlast": "^1.2.5", "array.prototype.flatmap": "^1.3.3", "array.prototype.tosorted": "^1.1.4", "doctrine": "^2.1.0", "es-iterator-helpers": "^1.2.1", "estraverse": "^5.3.0", "hasown": "^2.0.2", "jsx-ast-utils": "^2.4.1 || ^3.0.0", "minimatch": "^3.1.2", "object.entries": "^1.1.9", "object.fromentries": "^2.0.8", "object.values": "^1.2.1", "prop-types": "^15.8.1", "resolve": "^2.0.0-next.5", "semver": "^6.3.1", "string.prototype.matchall": "^4.0.12", "string.prototype.repeat": "^1.0.0"}, "engines": {"node": ">=4"}, "peerDependencies": {"eslint": "^3 || ^4 || ^5 || ^6 || ^7 || ^8 || ^9.7"}}, "client/node_modules/eslint-plugin-react-hooks": {"version": "4.6.2", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "peerDependencies": {"eslint": "^3.0.0 || ^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0-0"}}, "client/node_modules/eslint-plugin-react/node_modules/doctrine": {"version": "2.1.0", "dev": true, "license": "Apache-2.0", "dependencies": {"esutils": "^2.0.2"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/eslint-plugin-react/node_modules/resolve": {"version": "2.0.0-next.5", "dev": true, "license": "MIT", "dependencies": {"is-core-module": "^2.13.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/eslint-scope": {"version": "5.1.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^4.1.1"}, "engines": {"node": ">=8.0.0"}}, "client/node_modules/eslint-scope/node_modules/estraverse": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "client/node_modules/eslint-visitor-keys": {"version": "3.4.3", "dev": true, "license": "Apache-2.0", "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "client/node_modules/eslint/node_modules/eslint-scope": {"version": "7.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"esrecurse": "^4.3.0", "estraverse": "^5.2.0"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "client/node_modules/eslint/node_modules/glob-parent": {"version": "6.0.2", "dev": true, "license": "ISC", "dependencies": {"is-glob": "^4.0.3"}, "engines": {"node": ">=10.13.0"}}, "client/node_modules/espree": {"version": "9.6.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"acorn": "^8.9.0", "acorn-jsx": "^5.3.2", "eslint-visitor-keys": "^3.4.1"}, "engines": {"node": "^12.22.0 || ^14.17.0 || >=16.0.0"}, "funding": {"url": "https://opencollective.com/eslint"}}, "client/node_modules/esquery": {"version": "1.6.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.1.0"}, "engines": {"node": ">=0.10"}}, "client/node_modules/esrecurse": {"version": "4.3.0", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"estraverse": "^5.2.0"}, "engines": {"node": ">=4.0"}}, "client/node_modules/estraverse": {"version": "5.3.0", "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=4.0"}}, "client/node_modules/estree-walker": {"version": "2.0.2", "license": "MIT"}, "client/node_modules/esutils": {"version": "2.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "client/node_modules/etag": {"version": "1.8.1", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/eventemitter3": {"version": "4.0.7", "devOptional": true, "license": "MIT"}, "client/node_modules/events": {"version": "3.3.0", "license": "MIT", "engines": {"node": ">=0.8.x"}}, "client/node_modules/execa": {"version": "5.1.1", "devOptional": true, "license": "MIT", "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "client/node_modules/execa/node_modules/get-stream": {"version": "6.0.1", "devOptional": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/expr-parser": {"version": "1.0.0", "dev": true, "license": "MIT"}, "client/node_modules/express": {"version": "4.21.2", "devOptional": true, "license": "MIT", "dependencies": {"accepts": "~1.3.8", "array-flatten": "1.1.1", "body-parser": "1.20.3", "content-disposition": "0.5.4", "content-type": "~1.0.4", "cookie": "0.7.1", "cookie-signature": "1.0.6", "debug": "2.6.9", "depd": "2.0.0", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "etag": "~1.8.1", "finalhandler": "1.3.1", "fresh": "0.5.2", "http-errors": "2.0.0", "merge-descriptors": "1.0.3", "methods": "~1.1.2", "on-finished": "2.4.1", "parseurl": "~1.3.3", "path-to-regexp": "0.1.12", "proxy-addr": "~2.0.7", "qs": "6.13.0", "range-parser": "~1.2.1", "safe-buffer": "5.2.1", "send": "0.19.0", "serve-static": "1.16.2", "setprototypeof": "1.2.0", "statuses": "2.0.1", "type-is": "~1.6.18", "utils-merge": "1.0.1", "vary": "~1.1.2"}, "engines": {"node": ">= 0.10.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/express"}}, "client/node_modules/express/node_modules/debug": {"version": "2.6.9", "devOptional": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "client/node_modules/express/node_modules/ms": {"version": "2.0.0", "devOptional": true, "license": "MIT"}, "client/node_modules/express/node_modules/qs": {"version": "6.13.0", "devOptional": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.0.6"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/ext-list": {"version": "2.2.2", "dev": true, "license": "MIT", "dependencies": {"mime-db": "^1.28.0"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/ext-name": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"ext-list": "^2.0.0", "sort-keys-length": "^1.0.0"}, "engines": {"node": ">=4"}}, "client/node_modules/extend": {"version": "3.0.2", "dev": true, "license": "MIT"}, "client/node_modules/external-editor": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"chardet": "^0.7.0", "iconv-lite": "^0.4.24", "tmp": "^0.0.33"}, "engines": {"node": ">=4"}}, "client/node_modules/extsprintf": {"version": "1.3.0", "dev": true, "engines": ["node >=0.6.0"], "license": "MIT"}, "client/node_modules/fast-deep-equal": {"version": "3.1.3", "license": "MIT"}, "client/node_modules/fast-glob": {"version": "3.3.3", "dev": true, "license": "MIT", "dependencies": {"@nodelib/fs.stat": "^2.0.2", "@nodelib/fs.walk": "^1.2.3", "glob-parent": "^5.1.2", "merge2": "^1.3.0", "micromatch": "^4.0.8"}, "engines": {"node": ">=8.6.0"}}, "client/node_modules/fast-json-stable-stringify": {"version": "2.1.0", "dev": true, "license": "MIT"}, "client/node_modules/fast-levenshtein": {"version": "2.0.6", "dev": true, "license": "MIT"}, "client/node_modules/fast-uri": {"version": "3.0.6", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/fastest-levenshtein": {"version": "1.0.16", "dev": true, "license": "MIT", "engines": {"node": ">= 4.9.1"}}, "client/node_modules/fastq": {"version": "1.19.1", "dev": true, "license": "ISC", "dependencies": {"reusify": "^1.0.4"}}, "client/node_modules/faye-websocket": {"version": "0.11.4", "devOptional": true, "license": "Apache-2.0", "dependencies": {"websocket-driver": ">=0.5.1"}, "engines": {"node": ">=0.8.0"}}, "client/node_modules/fd-slicer": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"pend": "~1.2.0"}}, "client/node_modules/figures": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.5"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/figures/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "client/node_modules/file-entry-cache": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"flat-cache": "^3.0.4"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "client/node_modules/file-type": {"version": "8.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/filename-reserved-regex": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/filenamify": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"filename-reserved-regex": "^2.0.0", "strip-outer": "^1.0.0", "trim-repeated": "^1.0.0"}, "engines": {"node": ">=4"}}, "client/node_modules/fill-range": {"version": "7.1.1", "license": "MIT", "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "client/node_modules/finalhandler": {"version": "1.3.1", "devOptional": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "encodeurl": "~2.0.0", "escape-html": "~1.0.3", "on-finished": "2.4.1", "parseurl": "~1.3.3", "statuses": "2.0.1", "unpipe": "~1.0.0"}, "engines": {"node": ">= 0.8"}}, "client/node_modules/finalhandler/node_modules/debug": {"version": "2.6.9", "devOptional": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "client/node_modules/finalhandler/node_modules/ms": {"version": "2.0.0", "devOptional": true, "license": "MIT"}, "client/node_modules/find-cache-dir": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"commondir": "^1.0.1", "make-dir": "^2.0.0", "pkg-dir": "^3.0.0"}, "engines": {"node": ">=6"}}, "client/node_modules/find-up": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^6.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/find-yarn-workspace-root": {"version": "2.0.0", "license": "Apache-2.0", "dependencies": {"micromatch": "^4.0.2"}}, "client/node_modules/flat": {"version": "5.0.2", "license": "BSD-3-<PERSON><PERSON>", "bin": {"flat": "cli.js"}}, "client/node_modules/flat-cache": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"flatted": "^3.2.9", "keyv": "^4.5.3", "rimraf": "^3.0.2"}, "engines": {"node": "^10.12.0 || >=12.0.0"}}, "client/node_modules/flat-cache/node_modules/rimraf": {"version": "3.0.2", "dev": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "client/node_modules/flatted": {"version": "3.3.3", "dev": true, "license": "ISC"}, "client/node_modules/follow-redirects": {"version": "1.15.9", "devOptional": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/Ruben<PERSON>"}], "license": "MIT", "engines": {"node": ">=4.0"}, "peerDependenciesMeta": {"debug": {"optional": true}}}, "client/node_modules/for-each": {"version": "0.3.5", "dev": true, "license": "MIT", "dependencies": {"is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/foreground-child": {"version": "3.3.1", "dev": true, "license": "ISC", "dependencies": {"cross-spawn": "^7.0.6", "signal-exit": "^4.0.1"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "client/node_modules/foreground-child/node_modules/signal-exit": {"version": "4.1.0", "dev": true, "license": "ISC", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "client/node_modules/forever-agent": {"version": "0.6.1", "dev": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "client/node_modules/form-data": {"version": "4.0.3", "dev": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.8", "es-set-tostringtag": "^2.1.0", "hasown": "^2.0.2", "mime-types": "^2.1.12"}, "engines": {"node": ">= 6"}}, "client/node_modules/forwarded": {"version": "0.2.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/fraction.js": {"version": "4.3.7", "dev": true, "license": "MIT", "engines": {"node": "*"}, "funding": {"type": "patreon", "url": "https://github.com/sponsors/rawify"}}, "client/node_modules/fresh": {"version": "0.5.2", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/from2": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "readable-stream": "^2.0.0"}}, "client/node_modules/from2/node_modules/isarray": {"version": "1.0.0", "dev": true, "license": "MIT"}, "client/node_modules/from2/node_modules/readable-stream": {"version": "2.3.8", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "client/node_modules/from2/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT"}, "client/node_modules/from2/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "client/node_modules/fs-constants": {"version": "1.0.0", "dev": true, "license": "MIT"}, "client/node_modules/fs-extra": {"version": "11.3.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "client/node_modules/fs-monkey": {"version": "1.0.6", "devOptional": true, "license": "Unlicense"}, "client/node_modules/fs.realpath": {"version": "1.0.0", "license": "ISC"}, "client/node_modules/fsevents": {"version": "2.3.3", "license": "MIT", "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "client/node_modules/function-bind": {"version": "1.1.2", "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/function.prototype.name": {"version": "1.1.8", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "functions-have-names": "^1.2.3", "hasown": "^2.0.2", "is-callable": "^1.2.7"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/functions-have-names": {"version": "1.2.3", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/gensync": {"version": "1.0.0-beta.2", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "client/node_modules/get-intrinsic": {"version": "1.3.0", "devOptional": true, "license": "MIT", "dependencies": {"call-bind-apply-helpers": "^1.0.2", "es-define-property": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.1.1", "function-bind": "^1.1.2", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "hasown": "^2.0.2", "math-intrinsics": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/get-proto": {"version": "1.0.1", "devOptional": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/get-proxy": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"npm-conf": "^1.1.0"}, "engines": {"node": ">=4"}}, "client/node_modules/get-stream": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/get-symbol-description": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/get-tsconfig": {"version": "4.10.1", "dev": true, "license": "MIT", "dependencies": {"resolve-pkg-maps": "^1.0.0"}, "funding": {"url": "https://github.com/privatenumber/get-tsconfig?sponsor=1"}}, "client/node_modules/getpass": {"version": "0.1.7", "dev": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0"}}, "client/node_modules/git-clone": {"version": "0.1.0", "dev": true, "license": "ISC"}, "client/node_modules/glob": {"version": "7.2.3", "license": "ISC", "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "client/node_modules/glob-parent": {"version": "5.1.2", "license": "ISC", "dependencies": {"is-glob": "^4.0.1"}, "engines": {"node": ">= 6"}}, "client/node_modules/glob-to-regexp": {"version": "0.4.1", "license": "BSD-2-<PERSON><PERSON>"}, "client/node_modules/global-modules": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"global-prefix": "^3.0.0"}, "engines": {"node": ">=6"}}, "client/node_modules/global-prefix": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"ini": "^1.3.5", "kind-of": "^6.0.2", "which": "^1.3.1"}, "engines": {"node": ">=6"}}, "client/node_modules/globals": {"version": "13.24.0", "dev": true, "license": "MIT", "dependencies": {"type-fest": "^0.20.2"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/globals/node_modules/type-fest": {"version": "0.20.2", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/globalthis": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.2.1", "gopd": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/globby": {"version": "11.1.0", "dev": true, "license": "MIT", "dependencies": {"array-union": "^2.1.0", "dir-glob": "^3.0.1", "fast-glob": "^3.2.9", "ignore": "^5.2.0", "merge2": "^1.4.1", "slash": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/globjoin": {"version": "0.1.4", "dev": true, "license": "MIT"}, "client/node_modules/globs": {"version": "0.1.4", "license": "MIT", "dependencies": {"glob": "^7.1.1"}}, "client/node_modules/gopd": {"version": "1.2.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/got": {"version": "8.3.2", "dev": true, "license": "MIT", "dependencies": {"@sindresorhus/is": "^0.7.0", "cacheable-request": "^2.1.1", "decompress-response": "^3.3.0", "duplexer3": "^0.1.4", "get-stream": "^3.0.0", "into-stream": "^3.1.0", "is-retry-allowed": "^1.1.0", "isurl": "^1.0.0-alpha5", "lowercase-keys": "^1.0.0", "mimic-response": "^1.0.0", "p-cancelable": "^0.4.0", "p-timeout": "^2.0.1", "pify": "^3.0.0", "safe-buffer": "^5.1.1", "timed-out": "^4.0.1", "url-parse-lax": "^3.0.0", "url-to-options": "^1.0.1"}, "engines": {"node": ">=4"}}, "client/node_modules/got/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/graceful-fs": {"version": "4.2.11", "license": "ISC"}, "client/node_modules/graphemer": {"version": "1.4.0", "dev": true, "license": "MIT"}, "client/node_modules/hammerjs": {"version": "2.0.8", "license": "MIT", "engines": {"node": ">=0.8.0"}}, "client/node_modules/handle-thing": {"version": "2.0.1", "devOptional": true, "license": "MIT"}, "client/node_modules/har-schema": {"version": "2.0.0", "dev": true, "license": "ISC", "engines": {"node": ">=4"}}, "client/node_modules/har-validator": {"version": "5.1.5", "dev": true, "license": "MIT", "dependencies": {"ajv": "^6.12.3", "har-schema": "^2.0.0"}, "engines": {"node": ">=6"}}, "client/node_modules/hard-rejection": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/has-bigints": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/has-property-descriptors": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"es-define-property": "^1.0.0"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/has-proto": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/has-symbol-support-x": {"version": "1.4.2", "dev": true, "license": "MIT", "engines": {"node": "*"}}, "client/node_modules/has-symbols": {"version": "1.1.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/has-to-string-tag-x": {"version": "1.4.1", "dev": true, "license": "MIT", "dependencies": {"has-symbol-support-x": "^1.4.1"}, "engines": {"node": "*"}}, "client/node_modules/has-tostringtag": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"has-symbols": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/hash-sum": {"version": "2.0.0", "license": "MIT"}, "client/node_modules/hasown": {"version": "2.0.2", "license": "MIT", "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/he": {"version": "1.2.0", "devOptional": true, "license": "MIT", "bin": {"he": "bin/he"}}, "client/node_modules/highlight.js": {"version": "10.7.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": "*"}}, "client/node_modules/hls.js": {"version": "1.6.6", "license": "Apache-2.0"}, "client/node_modules/hosted-git-info": {"version": "4.1.0", "dev": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "engines": {"node": ">=10"}}, "client/node_modules/hosted-git-info/node_modules/lru-cache": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "client/node_modules/hosted-git-info/node_modules/yallist": {"version": "4.0.0", "dev": true, "license": "ISC"}, "client/node_modules/hpack.js": {"version": "2.1.6", "devOptional": true, "license": "MIT", "dependencies": {"inherits": "^2.0.1", "obuf": "^1.0.0", "readable-stream": "^2.0.1", "wbuf": "^1.1.0"}}, "client/node_modules/hpack.js/node_modules/isarray": {"version": "1.0.0", "devOptional": true, "license": "MIT"}, "client/node_modules/hpack.js/node_modules/readable-stream": {"version": "2.3.8", "devOptional": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "client/node_modules/hpack.js/node_modules/safe-buffer": {"version": "5.1.2", "devOptional": true, "license": "MIT"}, "client/node_modules/hpack.js/node_modules/string_decoder": {"version": "1.1.1", "devOptional": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "client/node_modules/html-encoding-sniffer": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"whatwg-encoding": "^3.1.1"}, "engines": {"node": ">=18"}}, "client/node_modules/html-entities": {"version": "2.3.3", "devOptional": true, "license": "MIT"}, "client/node_modules/html-minifier": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"camel-case": "^3.0.0", "clean-css": "^4.2.1", "commander": "^2.19.0", "he": "^1.2.0", "param-case": "^2.1.1", "relateurl": "^0.2.7", "uglify-js": "^3.5.1"}, "bin": {"html-minifier": "cli.js"}, "engines": {"node": ">=6"}}, "client/node_modules/html-minifier-terser": {"version": "6.1.0", "devOptional": true, "license": "MIT", "dependencies": {"camel-case": "^4.1.2", "clean-css": "^5.2.2", "commander": "^8.3.0", "he": "^1.2.0", "param-case": "^3.0.4", "relateurl": "^0.2.7", "terser": "^5.10.0"}, "bin": {"html-minifier-terser": "cli.js"}, "engines": {"node": ">=12"}}, "client/node_modules/html-minifier-terser/node_modules/camel-case": {"version": "4.1.2", "devOptional": true, "license": "MIT", "dependencies": {"pascal-case": "^3.1.2", "tslib": "^2.0.3"}}, "client/node_modules/html-minifier-terser/node_modules/clean-css": {"version": "5.3.3", "devOptional": true, "license": "MIT", "dependencies": {"source-map": "~0.6.0"}, "engines": {"node": ">= 10.0"}}, "client/node_modules/html-minifier-terser/node_modules/commander": {"version": "8.3.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 12"}}, "client/node_modules/html-minifier-terser/node_modules/param-case": {"version": "3.0.4", "devOptional": true, "license": "MIT", "dependencies": {"dot-case": "^3.0.4", "tslib": "^2.0.3"}}, "client/node_modules/html-minifier-terser/node_modules/terser": {"version": "5.43.1", "devOptional": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.14.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "client/node_modules/html-minifier-terser/node_modules/terser/node_modules/commander": {"version": "2.20.3", "devOptional": true, "license": "MIT"}, "client/node_modules/html-minifier-terser/node_modules/tslib": {"version": "2.8.1", "devOptional": true, "license": "0BSD"}, "client/node_modules/html-tags": {"version": "3.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/html-webpack-plugin": {"version": "5.6.3", "devOptional": true, "license": "MIT", "dependencies": {"@types/html-minifier-terser": "^6.0.0", "html-minifier-terser": "^6.0.2", "lodash": "^4.17.21", "pretty-error": "^4.0.0", "tapable": "^2.0.0"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/html-webpack-plugin"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "webpack": "^5.20.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}}, "client/node_modules/htmlparser2": {"version": "6.1.0", "devOptional": true, "funding": ["https://github.com/fb55/htmlparser2?sponsor=1", {"type": "github", "url": "https://github.com/sponsors/fb55"}], "license": "MIT", "dependencies": {"domelementtype": "^2.0.1", "domhandler": "^4.0.0", "domutils": "^2.5.2", "entities": "^2.0.0"}}, "client/node_modules/htmlparser2/node_modules/entities": {"version": "2.2.0", "devOptional": true, "license": "BSD-2-<PERSON><PERSON>", "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "client/node_modules/http-cache-semantics": {"version": "3.8.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "client/node_modules/http-deceiver": {"version": "1.2.7", "devOptional": true, "license": "MIT"}, "client/node_modules/http-errors": {"version": "2.0.0", "devOptional": true, "license": "MIT", "dependencies": {"depd": "2.0.0", "inherits": "2.0.4", "setprototypeof": "1.2.0", "statuses": "2.0.1", "toidentifier": "1.0.1"}, "engines": {"node": ">= 0.8"}}, "client/node_modules/http-parser-js": {"version": "0.5.10", "devOptional": true, "license": "MIT"}, "client/node_modules/http-proxy": {"version": "1.18.1", "devOptional": true, "license": "MIT", "dependencies": {"eventemitter3": "^4.0.0", "follow-redirects": "^1.0.0", "requires-port": "^1.0.0"}, "engines": {"node": ">=8.0.0"}}, "client/node_modules/http-proxy-agent": {"version": "7.0.2", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.0", "debug": "^4.3.4"}, "engines": {"node": ">= 14"}}, "client/node_modules/http-proxy-middleware": {"version": "2.0.9", "devOptional": true, "license": "MIT", "dependencies": {"@types/http-proxy": "^1.17.8", "http-proxy": "^1.18.1", "is-glob": "^4.0.1", "is-plain-obj": "^3.0.0", "micromatch": "^4.0.2"}, "engines": {"node": ">=12.0.0"}, "peerDependencies": {"@types/express": "^4.17.13"}, "peerDependenciesMeta": {"@types/express": {"optional": true}}}, "client/node_modules/http-proxy-middleware/node_modules/is-plain-obj": {"version": "3.0.0", "devOptional": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/http-signature": {"version": "1.2.0", "dev": true, "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "jsprim": "^1.2.2", "sshpk": "^1.7.0"}, "engines": {"node": ">=0.8", "npm": ">=1.3.7"}}, "client/node_modules/https-proxy-agent": {"version": "7.0.6", "dev": true, "license": "MIT", "dependencies": {"agent-base": "^7.1.2", "debug": "4"}, "engines": {"node": ">= 14"}}, "client/node_modules/human-signals": {"version": "2.1.0", "devOptional": true, "license": "Apache-2.0", "engines": {"node": ">=10.17.0"}}, "client/node_modules/humanize-ms": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"ms": "^2.0.0"}}, "client/node_modules/iconv-lite": {"version": "0.4.24", "devOptional": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/icss-utils": {"version": "5.1.0", "dev": true, "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "client/node_modules/ieee754": {"version": "1.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "BSD-3-<PERSON><PERSON>"}, "client/node_modules/ignore": {"version": "5.3.2", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "client/node_modules/image-size": {"version": "0.5.5", "dev": true, "license": "MIT", "optional": true, "bin": {"image-size": "bin/image-size.js"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/immutable": {"version": "5.1.3", "license": "MIT"}, "client/node_modules/import-fresh": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"parent-module": "^1.0.0", "resolve-from": "^4.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/import-lazy": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/import-local": {"version": "3.2.0", "dev": true, "license": "MIT", "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/import-local/node_modules/find-up": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/import-local/node_modules/locate-path": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "client/node_modules/import-local/node_modules/p-locate": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "client/node_modules/import-local/node_modules/pkg-dir": {"version": "4.2.0", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/imurmurhash": {"version": "0.1.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.19"}}, "client/node_modules/indent-string": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/inflight": {"version": "1.0.6", "license": "ISC", "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "client/node_modules/inherits": {"version": "2.0.4", "license": "ISC"}, "client/node_modules/ini": {"version": "1.3.8", "dev": true, "license": "ISC"}, "client/node_modules/inquirer": {"version": "8.2.6", "dev": true, "license": "MIT", "dependencies": {"ansi-escapes": "^4.2.1", "chalk": "^4.1.1", "cli-cursor": "^3.1.0", "cli-width": "^3.0.0", "external-editor": "^3.0.3", "figures": "^3.0.0", "lodash": "^4.17.21", "mute-stream": "0.0.8", "ora": "^5.4.1", "run-async": "^2.4.0", "rxjs": "^7.5.5", "string-width": "^4.1.0", "strip-ansi": "^6.0.0", "through": "^2.3.6", "wrap-ansi": "^6.0.1"}, "engines": {"node": ">=12.0.0"}}, "client/node_modules/internal-slot": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "hasown": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/interpret": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=10.13.0"}}, "client/node_modules/into-stream": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"from2": "^2.1.1", "p-is-promise": "^1.1.0"}, "engines": {"node": ">=4"}}, "client/node_modules/ipaddr.js": {"version": "2.2.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 10"}}, "client/node_modules/is-array-buffer": {"version": "3.0.5", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-arrayish": {"version": "0.2.1", "dev": true, "license": "MIT"}, "client/node_modules/is-async-function": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"async-function": "^1.0.0", "call-bound": "^1.0.3", "get-proto": "^1.0.1", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-bigint": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"has-bigints": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-binary-path": {"version": "2.1.0", "license": "MIT", "dependencies": {"binary-extensions": "^2.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/is-boolean-object": {"version": "1.2.2", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-buffer": {"version": "1.1.6", "dev": true, "license": "MIT"}, "client/node_modules/is-callable": {"version": "1.2.7", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-core-module": {"version": "2.16.1", "license": "MIT", "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-data-view": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "is-typed-array": "^1.1.13"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-date-object": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-docker": {"version": "2.2.1", "devOptional": true, "license": "MIT", "bin": {"is-docker": "cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/is-extglob": {"version": "2.1.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/is-finalizationregistry": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-generator-function": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-proto": "^1.0.0", "has-tostringtag": "^1.0.2", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-glob": {"version": "4.0.3", "license": "MIT", "dependencies": {"is-extglob": "^2.1.1"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/is-interactive": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/is-invalid-path": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=6.0"}}, "client/node_modules/is-map": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-natural-number": {"version": "4.0.1", "dev": true, "license": "MIT"}, "client/node_modules/is-negative-zero": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-number": {"version": "7.0.0", "license": "MIT", "engines": {"node": ">=0.12.0"}}, "client/node_modules/is-number-object": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-object": {"version": "1.0.2", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-path-inside": {"version": "3.0.3", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/is-plain-obj": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/is-plain-object": {"version": "2.0.4", "license": "MIT", "dependencies": {"isobject": "^3.0.1"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/is-potential-custom-element-name": {"version": "1.0.1", "dev": true, "license": "MIT"}, "client/node_modules/is-regex": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2", "hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-retry-allowed": {"version": "1.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/is-set": {"version": "2.0.3", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-shared-array-buffer": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-stream": {"version": "2.0.1", "devOptional": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/is-string": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-symbol": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "has-symbols": "^1.1.0", "safe-regex-test": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-typed-array": {"version": "1.1.15", "dev": true, "license": "MIT", "dependencies": {"which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-typedarray": {"version": "1.0.0", "dev": true, "license": "MIT"}, "client/node_modules/is-unicode-supported": {"version": "0.1.0", "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/is-weakmap": {"version": "2.0.2", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-weakref": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-weakset": {"version": "2.0.4", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "get-intrinsic": "^1.2.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/is-what": {"version": "3.14.1", "dev": true, "license": "MIT"}, "client/node_modules/is-wsl": {"version": "2.2.0", "devOptional": true, "license": "MIT", "dependencies": {"is-docker": "^2.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/isarray": {"version": "2.0.5", "dev": true, "license": "MIT"}, "client/node_modules/isexe": {"version": "2.0.0", "license": "ISC"}, "client/node_modules/isobject": {"version": "3.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/isstream": {"version": "0.1.2", "dev": true, "license": "MIT"}, "client/node_modules/isurl": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"has-to-string-tag-x": "^1.2.0", "is-object": "^1.0.1"}, "engines": {"node": ">= 4"}}, "client/node_modules/iterator.prototype": {"version": "1.1.5", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.6", "get-proto": "^1.0.0", "has-symbols": "^1.1.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/j-component": {"version": "1.4.9", "dev": true, "license": "MIT", "dependencies": {"expr-parser": "^1.0.0", "miniprogram-api-typings": "^3.2.2", "miniprogram-exparser": "latest"}}, "client/node_modules/jackspeak": {"version": "2.3.6", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"@isaacs/cliui": "^8.0.2"}, "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/isaacs"}, "optionalDependencies": {"@pkgjs/parseargs": "^0.11.0"}}, "client/node_modules/javascript-stringify": {"version": "2.1.0", "devOptional": true, "license": "MIT"}, "client/node_modules/jest-util": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "client/node_modules/jest-worker": {"version": "29.7.0", "dev": true, "license": "MIT", "dependencies": {"@types/node": "*", "jest-util": "^29.7.0", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "client/node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "client/node_modules/jiti": {"version": "1.21.7", "dev": true, "license": "MIT", "bin": {"jiti": "bin/jiti.js"}}, "client/node_modules/joi": {"version": "17.13.3", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@hapi/hoek": "^9.3.0", "@hapi/topo": "^5.1.0", "@sideway/address": "^4.1.5", "@sideway/formula": "^3.0.1", "@sideway/pinpoint": "^2.0.0"}}, "client/node_modules/js-tokens": {"version": "4.0.0", "license": "MIT"}, "client/node_modules/js-yaml": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"argparse": "^2.0.1"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "client/node_modules/jsbn": {"version": "0.1.1", "dev": true, "license": "MIT"}, "client/node_modules/jsdom": {"version": "24.1.3", "dev": true, "license": "MIT", "dependencies": {"cssstyle": "^4.0.1", "data-urls": "^5.0.0", "decimal.js": "^10.4.3", "form-data": "^4.0.0", "html-encoding-sniffer": "^4.0.0", "http-proxy-agent": "^7.0.2", "https-proxy-agent": "^7.0.5", "is-potential-custom-element-name": "^1.0.1", "nwsapi": "^2.2.12", "parse5": "^7.1.2", "rrweb-cssom": "^0.7.1", "saxes": "^6.0.0", "symbol-tree": "^3.2.4", "tough-cookie": "^4.1.4", "w3c-xmlserializer": "^5.0.0", "webidl-conversions": "^7.0.0", "whatwg-encoding": "^3.1.1", "whatwg-mimetype": "^4.0.0", "whatwg-url": "^14.0.0", "ws": "^8.18.0", "xml-name-validator": "^5.0.0"}, "engines": {"node": ">=18"}, "peerDependencies": {"canvas": "^2.11.2"}, "peerDependenciesMeta": {"canvas": {"optional": true}}}, "client/node_modules/jsdom/node_modules/entities": {"version": "6.0.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=0.12"}, "funding": {"url": "https://github.com/fb55/entities?sponsor=1"}}, "client/node_modules/jsdom/node_modules/parse5": {"version": "7.3.0", "dev": true, "license": "MIT", "dependencies": {"entities": "^6.0.0"}, "funding": {"url": "https://github.com/inikulin/parse5?sponsor=1"}}, "client/node_modules/jsesc": {"version": "3.1.0", "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "client/node_modules/json-bigint": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"bignumber.js": "^9.0.0"}}, "client/node_modules/json-buffer": {"version": "3.0.1", "dev": true, "license": "MIT"}, "client/node_modules/json-parse-even-better-errors": {"version": "2.3.1", "license": "MIT"}, "client/node_modules/json-schema": {"version": "0.4.0", "dev": true, "license": "(AFL-2.1 OR BSD-3-Clause)"}, "client/node_modules/json-schema-traverse": {"version": "0.4.1", "dev": true, "license": "MIT"}, "client/node_modules/json-stable-stringify-without-jsonify": {"version": "1.0.1", "dev": true, "license": "MIT"}, "client/node_modules/json-stringify-safe": {"version": "5.0.1", "dev": true, "license": "ISC"}, "client/node_modules/json5": {"version": "2.2.3", "license": "MIT", "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "client/node_modules/jsonfile": {"version": "6.1.0", "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "client/node_modules/jsonwebtoken": {"version": "8.5.1", "dev": true, "license": "MIT", "dependencies": {"jws": "^3.2.2", "lodash.includes": "^4.3.0", "lodash.isboolean": "^3.0.3", "lodash.isinteger": "^4.0.4", "lodash.isnumber": "^3.0.3", "lodash.isplainobject": "^4.0.6", "lodash.isstring": "^4.0.1", "lodash.once": "^4.0.0", "ms": "^2.1.1", "semver": "^5.6.0"}, "engines": {"node": ">=4", "npm": ">=1.4.28"}}, "client/node_modules/jsonwebtoken/node_modules/semver": {"version": "5.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "client/node_modules/jsprim": {"version": "1.4.2", "dev": true, "license": "MIT", "dependencies": {"assert-plus": "1.0.0", "extsprintf": "1.3.0", "json-schema": "0.4.0", "verror": "1.10.0"}, "engines": {"node": ">=0.6.0"}}, "client/node_modules/jsx-ast-utils": {"version": "3.3.5", "dev": true, "license": "MIT", "dependencies": {"array-includes": "^3.1.6", "array.prototype.flat": "^1.3.1", "object.assign": "^4.1.4", "object.values": "^1.1.6"}, "engines": {"node": ">=4.0"}}, "client/node_modules/jwa": {"version": "1.4.2", "dev": true, "license": "MIT", "dependencies": {"buffer-equal-constant-time": "^1.0.1", "ecdsa-sig-formatter": "1.0.11", "safe-buffer": "^5.0.1"}}, "client/node_modules/jws": {"version": "3.2.2", "dev": true, "license": "MIT", "dependencies": {"jwa": "^1.4.1", "safe-buffer": "^5.0.1"}}, "client/node_modules/keyv": {"version": "4.5.4", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.1"}}, "client/node_modules/kind-of": {"version": "6.0.3", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/kleur": {"version": "4.1.5", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/known-css-properties": {"version": "0.26.0", "dev": true, "license": "MIT"}, "client/node_modules/latest-version": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"package-json": "^6.3.0"}, "engines": {"node": ">=8"}}, "client/node_modules/launch-editor": {"version": "2.10.0", "devOptional": true, "license": "MIT", "dependencies": {"picocolors": "^1.0.0", "shell-quote": "^1.8.1"}}, "client/node_modules/less": {"version": "3.13.1", "dev": true, "license": "Apache-2.0", "dependencies": {"copy-anything": "^2.0.1", "tslib": "^1.10.0"}, "bin": {"lessc": "bin/lessc"}, "engines": {"node": ">=6"}, "optionalDependencies": {"errno": "^0.1.1", "graceful-fs": "^4.1.2", "image-size": "~0.5.0", "make-dir": "^2.1.0", "mime": "^1.4.1", "native-request": "^1.0.5", "source-map": "~0.6.0"}}, "client/node_modules/less-loader": {"version": "12.3.0", "dev": true, "license": "MIT", "engines": {"node": ">= 18.12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "less": "^3.5.0 || ^4.0.0", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}}, "client/node_modules/levn": {"version": "0.4.1", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1", "type-check": "~0.4.0"}, "engines": {"node": ">= 0.8.0"}}, "client/node_modules/lightningcss": {"version": "1.30.1", "dev": true, "license": "MPL-2.0", "dependencies": {"detect-libc": "^2.0.3"}, "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}, "optionalDependencies": {"lightningcss-darwin-arm64": "1.30.1", "lightningcss-darwin-x64": "1.30.1", "lightningcss-freebsd-x64": "1.30.1", "lightningcss-linux-arm-gnueabihf": "1.30.1", "lightningcss-linux-arm64-gnu": "1.30.1", "lightningcss-linux-arm64-musl": "1.30.1", "lightningcss-linux-x64-gnu": "1.30.1", "lightningcss-linux-x64-musl": "1.30.1", "lightningcss-win32-arm64-msvc": "1.30.1", "lightningcss-win32-x64-msvc": "1.30.1"}}, "client/node_modules/lightningcss-darwin-arm64": {"version": "1.30.1", "cpu": ["arm64"], "dev": true, "license": "MPL-2.0", "optional": true, "os": ["darwin"], "engines": {"node": ">= 12.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/parcel"}}, "client/node_modules/lilconfig": {"version": "3.1.3", "dev": true, "license": "MIT", "engines": {"node": ">=14"}, "funding": {"url": "https://github.com/sponsors/antonk52"}}, "client/node_modules/lines-and-columns": {"version": "1.2.4", "dev": true, "license": "MIT"}, "client/node_modules/loader-runner": {"version": "4.3.0", "license": "MIT", "engines": {"node": ">=6.11.5"}}, "client/node_modules/loader-utils": {"version": "2.0.4", "dev": true, "license": "MIT", "dependencies": {"big.js": "^5.2.2", "emojis-list": "^3.0.0", "json5": "^2.1.2"}, "engines": {"node": ">=8.9.0"}}, "client/node_modules/locate-path": {"version": "6.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^5.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/lodash.clonedeep": {"version": "4.5.0", "license": "MIT"}, "client/node_modules/lodash.debounce": {"version": "4.0.8", "license": "MIT"}, "client/node_modules/lodash.includes": {"version": "4.3.0", "dev": true, "license": "MIT"}, "client/node_modules/lodash.isboolean": {"version": "3.0.3", "dev": true, "license": "MIT"}, "client/node_modules/lodash.isinteger": {"version": "4.0.4", "dev": true, "license": "MIT"}, "client/node_modules/lodash.isnumber": {"version": "3.0.3", "dev": true, "license": "MIT"}, "client/node_modules/lodash.isplainobject": {"version": "4.0.6", "dev": true, "license": "MIT"}, "client/node_modules/lodash.isstring": {"version": "4.0.1", "dev": true, "license": "MIT"}, "client/node_modules/lodash.memoize": {"version": "4.1.2", "dev": true, "license": "MIT"}, "client/node_modules/lodash.merge": {"version": "4.6.2", "dev": true, "license": "MIT"}, "client/node_modules/lodash.once": {"version": "4.1.1", "dev": true, "license": "MIT"}, "client/node_modules/lodash.set": {"version": "4.3.2", "license": "MIT"}, "client/node_modules/lodash.truncate": {"version": "4.4.2", "dev": true, "license": "MIT"}, "client/node_modules/lodash.uniq": {"version": "4.5.0", "dev": true, "license": "MIT"}, "client/node_modules/lodash.unset": {"version": "4.5.2", "license": "MIT"}, "client/node_modules/log-symbols": {"version": "4.1.0", "license": "MIT", "dependencies": {"chalk": "^4.1.0", "is-unicode-supported": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/loglevel": {"version": "1.9.2", "license": "MIT", "engines": {"node": ">= 0.6.0"}, "funding": {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/loglevel"}}, "client/node_modules/loglevel-plugin-prefix": {"version": "0.8.4", "license": "MIT"}, "client/node_modules/long": {"version": "4.0.0", "dev": true, "license": "Apache-2.0"}, "client/node_modules/loose-envify": {"version": "1.4.0", "dev": true, "license": "MIT", "dependencies": {"js-tokens": "^3.0.0 || ^4.0.0"}, "bin": {"loose-envify": "cli.js"}}, "client/node_modules/lower-case": {"version": "1.1.4", "dev": true, "license": "MIT"}, "client/node_modules/lowercase-keys": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/lru-cache": {"version": "5.1.1", "license": "ISC", "dependencies": {"yallist": "^3.0.2"}}, "client/node_modules/magic-string": {"version": "0.30.17", "license": "MIT", "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0"}}, "client/node_modules/make-dir": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"pify": "^4.0.1", "semver": "^5.6.0"}, "engines": {"node": ">=6"}}, "client/node_modules/make-dir/node_modules/semver": {"version": "5.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "client/node_modules/map-obj": {"version": "4.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/math-intrinsics": {"version": "1.1.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "client/node_modules/mathml-tag-names": {"version": "2.1.3", "dev": true, "license": "MIT", "funding": {"type": "github", "url": "https://github.com/sponsors/wooorm"}}, "client/node_modules/md5": {"version": "2.3.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"charenc": "0.0.2", "crypt": "0.0.2", "is-buffer": "~1.1.6"}}, "client/node_modules/mdn-data": {"version": "1.1.4", "dev": true, "license": "MPL-2.0"}, "client/node_modules/media-typer": {"version": "0.3.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/memfs": {"version": "3.5.3", "devOptional": true, "license": "Unlicense", "dependencies": {"fs-monkey": "^1.0.4"}, "engines": {"node": ">= 4.0.0"}}, "client/node_modules/memoize-one": {"version": "6.0.0", "license": "MIT"}, "client/node_modules/meow": {"version": "9.0.0", "dev": true, "license": "MIT", "dependencies": {"@types/minimist": "^1.2.0", "camelcase-keys": "^6.2.2", "decamelize": "^1.2.0", "decamelize-keys": "^1.1.0", "hard-rejection": "^2.1.0", "minimist-options": "4.1.0", "normalize-package-data": "^3.0.0", "read-pkg-up": "^7.0.1", "redent": "^3.0.0", "trim-newlines": "^3.0.0", "type-fest": "^0.18.0", "yargs-parser": "^20.2.3"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/meow/node_modules/type-fest": {"version": "0.18.1", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/merge-descriptors": {"version": "1.0.3", "devOptional": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/merge-stream": {"version": "2.0.0", "license": "MIT"}, "client/node_modules/merge2": {"version": "1.4.1", "dev": true, "license": "MIT", "engines": {"node": ">= 8"}}, "client/node_modules/methods": {"version": "1.1.2", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/micromatch": {"version": "4.0.8", "license": "MIT", "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "client/node_modules/mime": {"version": "1.6.0", "devOptional": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4"}}, "client/node_modules/mime-db": {"version": "1.54.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/mime-types": {"version": "2.1.35", "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "client/node_modules/mime-types/node_modules/mime-db": {"version": "1.52.0", "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/mimic-fn": {"version": "2.1.0", "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/mimic-response": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/min-indent": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/mini-css-extract-plugin": {"version": "2.9.2", "dev": true, "license": "MIT", "dependencies": {"schema-utils": "^4.0.0", "tapable": "^2.2.1"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "client/node_modules/mini-css-extract-plugin/node_modules/ajv": {"version": "8.17.1", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "client/node_modules/mini-css-extract-plugin/node_modules/ajv-keywords": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "client/node_modules/mini-css-extract-plugin/node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT"}, "client/node_modules/mini-css-extract-plugin/node_modules/schema-utils": {"version": "4.3.2", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "client/node_modules/minimalistic-assert": {"version": "1.0.1", "devOptional": true, "license": "ISC"}, "client/node_modules/minimatch": {"version": "3.1.2", "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "client/node_modules/minimist": {"version": "1.2.8", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/minimist-options": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"arrify": "^1.0.1", "is-plain-obj": "^1.1.0", "kind-of": "^6.0.3"}, "engines": {"node": ">= 6"}}, "client/node_modules/minipass": {"version": "6.0.2", "dev": true, "license": "ISC", "engines": {"node": ">=16 || 14 >=14.17"}}, "client/node_modules/miniprogram-api-typings": {"version": "3.12.3", "dev": true, "license": "MIT"}, "client/node_modules/miniprogram-compiler": {"version": "0.2.3", "dev": true, "license": "MIT", "dependencies": {"glob": "^7.1.3", "unescape-js": "^1.1.1"}}, "client/node_modules/miniprogram-exparser": {"version": "2.29.1", "dev": true, "license": "MIT"}, "client/node_modules/miniprogram-simulate": {"version": "1.6.1", "dev": true, "license": "MIT", "dependencies": {"csso": "^3.5.1", "j-component": "^1.4.9", "less": "^3.10.3", "miniprogram-compiler": "latest", "postcss": "^7.0.23", "pretty-format": "^26.0.1"}}, "client/node_modules/miniprogram-simulate/node_modules/picocolors": {"version": "0.2.1", "dev": true, "license": "ISC"}, "client/node_modules/miniprogram-simulate/node_modules/postcss": {"version": "7.0.39", "dev": true, "license": "MIT", "dependencies": {"picocolors": "^0.2.1", "source-map": "^0.6.1"}, "engines": {"node": ">=6.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}}, "client/node_modules/ms": {"version": "2.1.3", "license": "MIT"}, "client/node_modules/multicast-dns": {"version": "7.2.5", "devOptional": true, "license": "MIT", "dependencies": {"dns-packet": "^5.2.2", "thunky": "^1.0.2"}, "bin": {"multicast-dns": "cli.js"}}, "client/node_modules/mute-stream": {"version": "0.0.8", "dev": true, "license": "ISC"}, "client/node_modules/mz": {"version": "2.7.0", "dev": true, "license": "MIT", "dependencies": {"any-promise": "^1.0.0", "object-assign": "^4.0.1", "thenify-all": "^1.0.0"}}, "client/node_modules/nanoid": {"version": "3.3.11", "funding": [{"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "bin": {"nanoid": "bin/nanoid.cjs"}, "engines": {"node": "^10 || ^12 || ^13.7 || ^14 || >=15.0.1"}}, "client/node_modules/native-request": {"version": "1.1.2", "dev": true, "license": "MIT", "optional": true}, "client/node_modules/natural-compare": {"version": "1.4.0", "dev": true, "license": "MIT"}, "client/node_modules/natural-compare-lite": {"version": "1.4.0", "dev": true, "license": "MIT"}, "client/node_modules/negotiator": {"version": "0.6.4", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/neo-async": {"version": "2.6.2", "license": "MIT"}, "client/node_modules/no-case": {"version": "2.3.2", "dev": true, "license": "MIT", "dependencies": {"lower-case": "^1.1.1"}}, "client/node_modules/node-addon-api": {"version": "7.1.1", "license": "MIT", "optional": true}, "client/node_modules/node-forge": {"version": "1.3.1", "devOptional": true, "license": "(BSD-3-<PERSON><PERSON> OR GPL-2.0)", "engines": {"node": ">= 6.13.0"}}, "client/node_modules/node-releases": {"version": "2.0.19", "license": "MIT"}, "client/node_modules/normalize-package-data": {"version": "3.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^4.0.1", "is-core-module": "^2.5.0", "semver": "^7.3.4", "validate-npm-package-license": "^3.0.1"}, "engines": {"node": ">=10"}}, "client/node_modules/normalize-package-data/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "client/node_modules/normalize-path": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/normalize-range": {"version": "0.1.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/normalize-url": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"prepend-http": "^2.0.0", "query-string": "^5.0.1", "sort-keys": "^2.0.0"}, "engines": {"node": ">=4"}}, "client/node_modules/npm-conf": {"version": "1.1.3", "dev": true, "license": "MIT", "dependencies": {"config-chain": "^1.1.11", "pify": "^3.0.0"}, "engines": {"node": ">=4"}}, "client/node_modules/npm-conf/node_modules/pify": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/npm-run-path": {"version": "4.0.1", "devOptional": true, "license": "MIT", "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/nth-check": {"version": "2.1.1", "devOptional": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0"}, "funding": {"url": "https://github.com/fb55/nth-check?sponsor=1"}}, "client/node_modules/nwsapi": {"version": "2.2.20", "dev": true, "license": "MIT"}, "client/node_modules/oauth-sign": {"version": "0.9.0", "dev": true, "license": "Apache-2.0", "engines": {"node": "*"}}, "client/node_modules/object-assign": {"version": "4.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/object-inspect": {"version": "1.13.4", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/object-keys": {"version": "1.1.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "client/node_modules/object.assign": {"version": "4.1.7", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0", "has-symbols": "^1.1.0", "object-keys": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/object.entries": {"version": "1.1.9", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.4", "define-properties": "^1.2.1", "es-object-atoms": "^1.1.1"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/object.fromentries": {"version": "2.0.8", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/object.groupby": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-abstract": "^1.23.2"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/object.values": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/obuf": {"version": "1.1.2", "devOptional": true, "license": "MIT"}, "client/node_modules/on-finished": {"version": "2.4.1", "devOptional": true, "license": "MIT", "dependencies": {"ee-first": "1.1.1"}, "engines": {"node": ">= 0.8"}}, "client/node_modules/on-headers": {"version": "1.0.2", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "client/node_modules/once": {"version": "1.4.0", "license": "ISC", "dependencies": {"wrappy": "1"}}, "client/node_modules/onetime": {"version": "5.1.2", "license": "MIT", "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/open": {"version": "8.4.2", "devOptional": true, "license": "MIT", "dependencies": {"define-lazy-prop": "^2.0.0", "is-docker": "^2.1.1", "is-wsl": "^2.2.0"}, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/optionator": {"version": "0.9.4", "dev": true, "license": "MIT", "dependencies": {"deep-is": "^0.1.3", "fast-levenshtein": "^2.0.6", "levn": "^0.4.1", "prelude-ls": "^1.2.1", "type-check": "^0.4.0", "word-wrap": "^1.2.5"}, "engines": {"node": ">= 0.8.0"}}, "client/node_modules/ora": {"version": "5.4.1", "license": "MIT", "dependencies": {"bl": "^4.1.0", "chalk": "^4.1.0", "cli-cursor": "^3.1.0", "cli-spinners": "^2.5.0", "is-interactive": "^1.0.0", "is-unicode-supported": "^0.1.0", "log-symbols": "^4.1.0", "strip-ansi": "^6.0.0", "wcwidth": "^1.0.1"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/os-tmpdir": {"version": "1.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/own-keys": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"get-intrinsic": "^1.2.6", "object-keys": "^1.1.1", "safe-push-apply": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/p-cancelable": {"version": "0.4.1", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/p-event": {"version": "2.3.1", "dev": true, "license": "MIT", "dependencies": {"p-timeout": "^2.0.1"}, "engines": {"node": ">=6"}}, "client/node_modules/p-finally": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/p-is-promise": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/p-limit": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/p-locate": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^3.0.2"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/p-locate/node_modules/p-limit": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/p-retry": {"version": "4.6.2", "devOptional": true, "license": "MIT", "dependencies": {"@types/retry": "0.12.0", "retry": "^0.13.1"}, "engines": {"node": ">=8"}}, "client/node_modules/p-timeout": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"p-finally": "^1.0.0"}, "engines": {"node": ">=4"}}, "client/node_modules/p-try": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/package-json": {"version": "6.5.0", "dev": true, "license": "MIT", "dependencies": {"got": "^9.6.0", "registry-auth-token": "^4.0.0", "registry-url": "^5.0.0", "semver": "^6.2.0"}, "engines": {"node": ">=8"}}, "client/node_modules/package-json/node_modules/@sindresorhus/is": {"version": "0.14.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/package-json/node_modules/cacheable-request": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"clone-response": "^1.0.2", "get-stream": "^5.1.0", "http-cache-semantics": "^4.0.0", "keyv": "^3.0.0", "lowercase-keys": "^2.0.0", "normalize-url": "^4.1.0", "responselike": "^1.0.2"}, "engines": {"node": ">=8"}}, "client/node_modules/package-json/node_modules/cacheable-request/node_modules/get-stream": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/package-json/node_modules/cacheable-request/node_modules/lowercase-keys": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/package-json/node_modules/get-stream": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"pump": "^3.0.0"}, "engines": {"node": ">=6"}}, "client/node_modules/package-json/node_modules/got": {"version": "9.6.0", "dev": true, "license": "MIT", "dependencies": {"@sindresorhus/is": "^0.14.0", "@szmarczak/http-timer": "^1.1.2", "cacheable-request": "^6.0.0", "decompress-response": "^3.3.0", "duplexer3": "^0.1.4", "get-stream": "^4.1.0", "lowercase-keys": "^1.0.1", "mimic-response": "^1.0.1", "p-cancelable": "^1.0.0", "to-readable-stream": "^1.0.0", "url-parse-lax": "^3.0.0"}, "engines": {"node": ">=8.6"}}, "client/node_modules/package-json/node_modules/http-cache-semantics": {"version": "4.2.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>"}, "client/node_modules/package-json/node_modules/json-buffer": {"version": "3.0.0", "dev": true, "license": "MIT"}, "client/node_modules/package-json/node_modules/keyv": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"json-buffer": "3.0.0"}}, "client/node_modules/package-json/node_modules/normalize-url": {"version": "4.5.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/package-json/node_modules/p-cancelable": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/param-case": {"version": "2.1.1", "dev": true, "license": "MIT", "dependencies": {"no-case": "^2.2.0"}}, "client/node_modules/parent-module": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"callsites": "^3.0.0"}, "engines": {"node": ">=6"}}, "client/node_modules/parse-json": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/parse5": {"version": "5.1.1", "dev": true, "license": "MIT"}, "client/node_modules/parse5-htmlparser2-tree-adapter": {"version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"parse5": "^6.0.1"}}, "client/node_modules/parse5-htmlparser2-tree-adapter/node_modules/parse5": {"version": "6.0.1", "dev": true, "license": "MIT"}, "client/node_modules/parseurl": {"version": "1.3.3", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "client/node_modules/pascal-case": {"version": "3.1.2", "devOptional": true, "license": "MIT", "dependencies": {"no-case": "^3.0.4", "tslib": "^2.0.3"}}, "client/node_modules/pascal-case/node_modules/lower-case": {"version": "2.0.2", "devOptional": true, "license": "MIT", "dependencies": {"tslib": "^2.0.3"}}, "client/node_modules/pascal-case/node_modules/no-case": {"version": "3.0.4", "devOptional": true, "license": "MIT", "dependencies": {"lower-case": "^2.0.2", "tslib": "^2.0.3"}}, "client/node_modules/pascal-case/node_modules/tslib": {"version": "2.8.1", "devOptional": true, "license": "0BSD"}, "client/node_modules/path-exists": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/path-is-absolute": {"version": "1.0.1", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/path-key": {"version": "3.1.1", "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/path-parse": {"version": "1.0.7", "license": "MIT"}, "client/node_modules/path-scurry": {"version": "1.11.1", "dev": true, "license": "BlueOak-1.0.0", "dependencies": {"lru-cache": "^10.2.0", "minipass": "^5.0.0 || ^6.0.2 || ^7.0.0"}, "engines": {"node": ">=16 || 14 >=14.18"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "client/node_modules/path-scurry/node_modules/lru-cache": {"version": "10.4.3", "dev": true, "license": "ISC"}, "client/node_modules/path-to-regexp": {"version": "0.1.12", "devOptional": true, "license": "MIT"}, "client/node_modules/path-type": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/pend": {"version": "1.2.0", "dev": true, "license": "MIT"}, "client/node_modules/performance-now": {"version": "2.1.0", "dev": true, "license": "MIT"}, "client/node_modules/picocolors": {"version": "1.1.1", "license": "ISC"}, "client/node_modules/picomatch": {"version": "2.3.1", "license": "MIT", "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "client/node_modules/pify": {"version": "4.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/pinkie": {"version": "2.0.4", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/pinkie-promise": {"version": "2.0.1", "dev": true, "license": "MIT", "dependencies": {"pinkie": "^2.0.0"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/pirates": {"version": "4.0.7", "license": "MIT", "engines": {"node": ">= 6"}}, "client/node_modules/pkg-dir": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"find-up": "^3.0.0"}, "engines": {"node": ">=6"}}, "client/node_modules/pkg-dir/node_modules/find-up": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^3.0.0"}, "engines": {"node": ">=6"}}, "client/node_modules/pkg-dir/node_modules/locate-path": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^3.0.0", "path-exists": "^3.0.0"}, "engines": {"node": ">=6"}}, "client/node_modules/pkg-dir/node_modules/p-locate": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.0.0"}, "engines": {"node": ">=6"}}, "client/node_modules/pkg-dir/node_modules/path-exists": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/possible-typed-array-names": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">= 0.4"}}, "client/node_modules/postcss": {"version": "8.5.6", "funding": [{"type": "opencollective", "url": "https://opencollective.com/postcss/"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/postcss"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"nanoid": "^3.3.11", "picocolors": "^1.1.1", "source-map-js": "^1.2.1"}, "engines": {"node": "^10 || ^12 || >=14"}}, "client/node_modules/postcss-calc": {"version": "9.0.1", "dev": true, "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.11", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.2.2"}}, "client/node_modules/postcss-colormin": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.23.0", "caniuse-api": "^3.0.0", "colord": "^2.9.3", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-convert-values": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.23.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-discard-comments": {"version": "6.0.2", "dev": true, "license": "MIT", "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-discard-duplicates": {"version": "6.0.3", "dev": true, "license": "MIT", "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-discard-empty": {"version": "6.0.3", "dev": true, "license": "MIT", "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-discard-overridden": {"version": "6.0.2", "dev": true, "license": "MIT", "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-html-transform": {"version": "4.0.6", "dev": true, "license": "MIT", "engines": {"node": ">= 18"}, "peerDependencies": {"postcss": "^8"}}, "client/node_modules/postcss-import": {"version": "16.1.1", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.0.0", "read-cache": "^1.0.0", "resolve": "^1.1.7"}, "engines": {"node": ">=18.0.0"}, "peerDependencies": {"postcss": "^8.0.0"}}, "client/node_modules/postcss-loader": {"version": "8.1.1", "dev": true, "license": "MIT", "dependencies": {"cosmiconfig": "^9.0.0", "jiti": "^1.20.0", "semver": "^7.5.4"}, "engines": {"node": ">= 18.12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "postcss": "^7.0.0 || ^8.0.1", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}}, "client/node_modules/postcss-loader/node_modules/semver": {"version": "7.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "client/node_modules/postcss-media-query-parser": {"version": "0.2.3", "dev": true, "license": "MIT"}, "client/node_modules/postcss-merge-longhand": {"version": "6.0.5", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0", "stylehacks": "^6.1.1"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-merge-rules": {"version": "6.1.1", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.23.0", "caniuse-api": "^3.0.0", "cssnano-utils": "^4.0.2", "postcss-selector-parser": "^6.0.16"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-minify-font-values": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-minify-gradients": {"version": "6.0.3", "dev": true, "license": "MIT", "dependencies": {"colord": "^2.9.3", "cssnano-utils": "^4.0.2", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-minify-params": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.23.0", "cssnano-utils": "^4.0.2", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-minify-selectors": {"version": "6.0.4", "dev": true, "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.16"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-modules-extract-imports": {"version": "3.1.0", "dev": true, "license": "ISC", "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "client/node_modules/postcss-modules-local-by-default": {"version": "4.2.0", "dev": true, "license": "MIT", "dependencies": {"icss-utils": "^5.0.0", "postcss-selector-parser": "^7.0.0", "postcss-value-parser": "^4.1.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "client/node_modules/postcss-modules-local-by-default/node_modules/postcss-selector-parser": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "client/node_modules/postcss-modules-scope": {"version": "3.2.1", "dev": true, "license": "ISC", "dependencies": {"postcss-selector-parser": "^7.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "client/node_modules/postcss-modules-scope/node_modules/postcss-selector-parser": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "client/node_modules/postcss-modules-values": {"version": "4.0.0", "dev": true, "license": "ISC", "dependencies": {"icss-utils": "^5.0.0"}, "engines": {"node": "^10 || ^12 || >= 14"}, "peerDependencies": {"postcss": "^8.1.0"}}, "client/node_modules/postcss-normalize-charset": {"version": "6.0.2", "dev": true, "license": "MIT", "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-normalize-display-values": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-normalize-positions": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-normalize-repeat-style": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-normalize-string": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-normalize-timing-functions": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-normalize-unicode": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.23.0", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-normalize-url": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-normalize-whitespace": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-ordered-values": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"cssnano-utils": "^4.0.2", "postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-plugin-constparse": {"version": "4.0.6", "dev": true, "license": "MIT", "engines": {"node": ">= 18"}, "peerDependencies": {"postcss": "^8"}}, "client/node_modules/postcss-pxtransform": {"version": "4.0.6", "dev": true, "license": "MIT", "engines": {"node": ">= 18"}, "peerDependencies": {"postcss": "^8"}}, "client/node_modules/postcss-reduce-initial": {"version": "6.1.0", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.23.0", "caniuse-api": "^3.0.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-reduce-transforms": {"version": "6.0.2", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-resolve-nested-selector": {"version": "0.1.6", "dev": true, "license": "MIT"}, "client/node_modules/postcss-safe-parser": {"version": "6.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/postcss/"}, "peerDependencies": {"postcss": "^8.3.3"}}, "client/node_modules/postcss-selector-parser": {"version": "6.1.2", "dev": true, "license": "MIT", "dependencies": {"cssesc": "^3.0.0", "util-deprecate": "^1.0.2"}, "engines": {"node": ">=4"}}, "client/node_modules/postcss-svgo": {"version": "6.0.3", "dev": true, "license": "MIT", "dependencies": {"postcss-value-parser": "^4.2.0", "svgo": "^3.2.0"}, "engines": {"node": "^14 || ^16 || >= 18"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-unique-selectors": {"version": "6.0.4", "dev": true, "license": "MIT", "dependencies": {"postcss-selector-parser": "^6.0.16"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/postcss-url": {"version": "10.1.3", "dev": true, "license": "MIT", "dependencies": {"make-dir": "~3.1.0", "mime": "~2.5.2", "minimatch": "~3.0.4", "xxhashjs": "~0.2.2"}, "engines": {"node": ">=10"}, "peerDependencies": {"postcss": "^8.0.0"}}, "client/node_modules/postcss-url/node_modules/make-dir": {"version": "3.1.0", "dev": true, "license": "MIT", "dependencies": {"semver": "^6.0.0"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/postcss-url/node_modules/mime": {"version": "2.5.2", "dev": true, "license": "MIT", "bin": {"mime": "cli.js"}, "engines": {"node": ">=4.0.0"}}, "client/node_modules/postcss-url/node_modules/minimatch": {"version": "3.0.8", "dev": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "client/node_modules/postcss-value-parser": {"version": "4.2.0", "dev": true, "license": "MIT"}, "client/node_modules/prelude-ls": {"version": "1.2.1", "dev": true, "license": "MIT", "engines": {"node": ">= 0.8.0"}}, "client/node_modules/prepend-http": {"version": "2.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/pretty-bytes": {"version": "5.6.0", "license": "MIT", "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/pretty-error": {"version": "4.0.0", "devOptional": true, "license": "MIT", "dependencies": {"lodash": "^4.17.20", "renderkid": "^3.0.0"}}, "client/node_modules/pretty-format": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@jest/types": "^26.6.2", "ansi-regex": "^5.0.0", "ansi-styles": "^4.0.0", "react-is": "^17.0.1"}, "engines": {"node": ">= 10"}}, "client/node_modules/pretty-format/node_modules/@jest/types": {"version": "26.6.2", "dev": true, "license": "MIT", "dependencies": {"@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^15.0.0", "chalk": "^4.0.0"}, "engines": {"node": ">= 10.14.2"}}, "client/node_modules/pretty-format/node_modules/@types/yargs": {"version": "15.0.19", "dev": true, "license": "MIT", "dependencies": {"@types/yargs-parser": "*"}}, "client/node_modules/pretty-format/node_modules/react-is": {"version": "17.0.2", "dev": true, "license": "MIT"}, "client/node_modules/pretty-time": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/process-nextick-args": {"version": "2.0.1", "devOptional": true, "license": "MIT"}, "client/node_modules/prop-types": {"version": "15.8.1", "dev": true, "license": "MIT", "dependencies": {"loose-envify": "^1.4.0", "object-assign": "^4.1.1", "react-is": "^16.13.1"}}, "client/node_modules/proto-list": {"version": "1.2.4", "dev": true, "license": "ISC"}, "client/node_modules/protobufjs": {"version": "6.11.4", "dev": true, "hasInstallScript": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"@protobufjs/aspromise": "^1.1.2", "@protobufjs/base64": "^1.1.2", "@protobufjs/codegen": "^2.0.4", "@protobufjs/eventemitter": "^1.1.0", "@protobufjs/fetch": "^1.1.0", "@protobufjs/float": "^1.0.2", "@protobufjs/inquire": "^1.1.0", "@protobufjs/path": "^1.1.2", "@protobufjs/pool": "^1.1.0", "@protobufjs/utf8": "^1.1.0", "@types/long": "^4.0.1", "@types/node": ">=13.7.0", "long": "^4.0.0"}, "bin": {"pbjs": "bin/pbjs", "pbts": "bin/pbts"}}, "client/node_modules/proxy-addr": {"version": "2.0.7", "devOptional": true, "license": "MIT", "dependencies": {"forwarded": "0.2.0", "ipaddr.js": "1.9.1"}, "engines": {"node": ">= 0.10"}}, "client/node_modules/proxy-addr/node_modules/ipaddr.js": {"version": "1.9.1", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "client/node_modules/proxy-from-env": {"version": "1.1.0", "dev": true, "license": "MIT"}, "client/node_modules/prr": {"version": "1.0.1", "dev": true, "license": "MIT", "optional": true}, "client/node_modules/psl": {"version": "1.15.0", "dev": true, "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "funding": {"url": "https://github.com/sponsors/lupomontero"}}, "client/node_modules/pump": {"version": "3.0.3", "dev": true, "license": "MIT", "dependencies": {"end-of-stream": "^1.1.0", "once": "^1.3.1"}}, "client/node_modules/punycode": {"version": "2.3.1", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/qs": {"version": "6.14.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"side-channel": "^1.1.0"}, "engines": {"node": ">=0.6"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/query-string": {"version": "5.1.1", "dev": true, "license": "MIT", "dependencies": {"decode-uri-component": "^0.2.0", "object-assign": "^4.1.0", "strict-uri-encode": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/querystringify": {"version": "2.2.0", "dev": true, "license": "MIT"}, "client/node_modules/queue-microtask": {"version": "1.2.3", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "client/node_modules/quick-lru": {"version": "4.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/randombytes": {"version": "2.1.0", "license": "MIT", "dependencies": {"safe-buffer": "^5.1.0"}}, "client/node_modules/range-parser": {"version": "1.2.1", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/raw-body": {"version": "2.5.2", "devOptional": true, "license": "MIT", "dependencies": {"bytes": "3.1.2", "http-errors": "2.0.0", "iconv-lite": "0.4.24", "unpipe": "1.0.0"}, "engines": {"node": ">= 0.8"}}, "client/node_modules/rc": {"version": "1.2.8", "dev": true, "license": "(BSD-2-<PERSON><PERSON> OR MIT OR Apache-2.0)", "dependencies": {"deep-extend": "^0.6.0", "ini": "~1.3.0", "minimist": "^1.2.0", "strip-json-comments": "~2.0.1"}, "bin": {"rc": "cli.js"}}, "client/node_modules/rc/node_modules/strip-json-comments": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/react-is": {"version": "16.13.1", "dev": true, "license": "MIT"}, "client/node_modules/read-cache": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"pify": "^2.3.0"}}, "client/node_modules/read-cache/node_modules/pify": {"version": "2.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/read-pkg": {"version": "5.2.0", "dev": true, "license": "MIT", "dependencies": {"@types/normalize-package-data": "^2.4.0", "normalize-package-data": "^2.5.0", "parse-json": "^5.0.0", "type-fest": "^0.6.0"}, "engines": {"node": ">=8"}}, "client/node_modules/read-pkg-up": {"version": "7.0.1", "dev": true, "license": "MIT", "dependencies": {"find-up": "^4.1.0", "read-pkg": "^5.2.0", "type-fest": "^0.8.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/read-pkg-up/node_modules/find-up": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/read-pkg-up/node_modules/locate-path": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "client/node_modules/read-pkg-up/node_modules/p-locate": {"version": "4.1.0", "dev": true, "license": "MIT", "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "client/node_modules/read-pkg-up/node_modules/type-fest": {"version": "0.8.1", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "client/node_modules/read-pkg/node_modules/hosted-git-info": {"version": "2.8.9", "dev": true, "license": "ISC"}, "client/node_modules/read-pkg/node_modules/normalize-package-data": {"version": "2.5.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"hosted-git-info": "^2.1.4", "resolve": "^1.10.0", "semver": "2 || 3 || 4 || 5", "validate-npm-package-license": "^3.0.1"}}, "client/node_modules/read-pkg/node_modules/semver": {"version": "5.7.2", "dev": true, "license": "ISC", "bin": {"semver": "bin/semver"}}, "client/node_modules/read-pkg/node_modules/type-fest": {"version": "0.6.0", "dev": true, "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=8"}}, "client/node_modules/readable-stream": {"version": "3.6.2", "license": "MIT", "dependencies": {"inherits": "^2.0.3", "string_decoder": "^1.1.1", "util-deprecate": "^1.0.1"}, "engines": {"node": ">= 6"}}, "client/node_modules/readdirp": {"version": "3.6.0", "license": "MIT", "dependencies": {"picomatch": "^2.2.1"}, "engines": {"node": ">=8.10.0"}}, "client/node_modules/rechoir": {"version": "0.8.0", "dev": true, "license": "MIT", "dependencies": {"resolve": "^1.20.0"}, "engines": {"node": ">= 10.13.0"}}, "client/node_modules/redent": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"indent-string": "^4.0.0", "strip-indent": "^3.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/reflect.getprototypeof": {"version": "1.0.10", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-abstract": "^1.23.9", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.7", "get-proto": "^1.0.1", "which-builtin-type": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/regenerate": {"version": "1.4.2", "dev": true, "license": "MIT"}, "client/node_modules/regenerate-unicode-properties": {"version": "10.2.0", "dev": true, "license": "MIT", "dependencies": {"regenerate": "^1.4.2"}, "engines": {"node": ">=4"}}, "client/node_modules/regenerator-runtime": {"version": "0.11.1", "dev": true, "license": "MIT"}, "client/node_modules/regex-parser": {"version": "2.3.1", "dev": true, "license": "MIT"}, "client/node_modules/regexp.prototype.flags": {"version": "1.5.4", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "define-properties": "^1.2.1", "es-errors": "^1.3.0", "get-proto": "^1.0.1", "gopd": "^1.2.0", "set-function-name": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/regexpu-core": {"version": "6.2.0", "dev": true, "license": "MIT", "dependencies": {"regenerate": "^1.4.2", "regenerate-unicode-properties": "^10.2.0", "regjsgen": "^0.8.0", "regjsparser": "^0.12.0", "unicode-match-property-ecmascript": "^2.0.0", "unicode-match-property-value-ecmascript": "^2.1.0"}, "engines": {"node": ">=4"}}, "client/node_modules/registry-auth-token": {"version": "4.2.2", "dev": true, "license": "MIT", "dependencies": {"rc": "1.2.8"}, "engines": {"node": ">=6.0.0"}}, "client/node_modules/registry-url": {"version": "5.1.0", "dev": true, "license": "MIT", "dependencies": {"rc": "^1.2.8"}, "engines": {"node": ">=8"}}, "client/node_modules/regjsgen": {"version": "0.8.0", "dev": true, "license": "MIT"}, "client/node_modules/regjsparser": {"version": "0.12.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"jsesc": "~3.0.2"}, "bin": {"regjsparser": "bin/parser"}}, "client/node_modules/regjsparser/node_modules/jsesc": {"version": "3.0.2", "dev": true, "license": "MIT", "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "client/node_modules/relateurl": {"version": "0.2.7", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.10"}}, "client/node_modules/renderkid": {"version": "3.0.0", "devOptional": true, "license": "MIT", "dependencies": {"css-select": "^4.1.3", "dom-converter": "^0.2.0", "htmlparser2": "^6.1.0", "lodash": "^4.17.21", "strip-ansi": "^6.0.1"}}, "client/node_modules/request": {"version": "2.88.2", "dev": true, "license": "Apache-2.0", "dependencies": {"aws-sign2": "~0.7.0", "aws4": "^1.8.0", "caseless": "~0.12.0", "combined-stream": "~1.0.6", "extend": "~3.0.2", "forever-agent": "~0.6.1", "form-data": "~2.3.2", "har-validator": "~5.1.3", "http-signature": "~1.2.0", "is-typedarray": "~1.0.0", "isstream": "~0.1.2", "json-stringify-safe": "~5.0.1", "mime-types": "~2.1.19", "oauth-sign": "~0.9.0", "performance-now": "^2.1.0", "qs": "~6.5.2", "safe-buffer": "^5.1.2", "tough-cookie": "~2.5.0", "tunnel-agent": "^0.6.0", "uuid": "^3.3.2"}, "engines": {"node": ">= 6"}}, "client/node_modules/request/node_modules/form-data": {"version": "2.3.3", "dev": true, "license": "MIT", "dependencies": {"asynckit": "^0.4.0", "combined-stream": "^1.0.6", "mime-types": "^2.1.12"}, "engines": {"node": ">= 0.12"}}, "client/node_modules/request/node_modules/qs": {"version": "6.5.3", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.6"}}, "client/node_modules/request/node_modules/tough-cookie": {"version": "2.5.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.28", "punycode": "^2.1.1"}, "engines": {"node": ">=0.8"}}, "client/node_modules/require-from-string": {"version": "2.0.2", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/requires-port": {"version": "1.0.0", "devOptional": true, "license": "MIT"}, "client/node_modules/resolve": {"version": "1.22.10", "license": "MIT", "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/resolve-cwd": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/resolve-cwd/node_modules/resolve-from": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/resolve-from": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/resolve-pathname": {"version": "3.0.0", "license": "MIT"}, "client/node_modules/resolve-pkg-maps": {"version": "1.0.0", "dev": true, "license": "MIT", "funding": {"url": "https://github.com/privatenumber/resolve-pkg-maps?sponsor=1"}}, "client/node_modules/resolve-url-loader": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"adjust-sourcemap-loader": "^4.0.0", "convert-source-map": "^1.7.0", "loader-utils": "^2.0.0", "postcss": "^8.2.14", "source-map": "0.6.1"}, "engines": {"node": ">=12"}}, "client/node_modules/resolve-url-loader/node_modules/convert-source-map": {"version": "1.9.0", "dev": true, "license": "MIT"}, "client/node_modules/responselike": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"lowercase-keys": "^1.0.0"}}, "client/node_modules/restore-cursor": {"version": "3.1.0", "license": "MIT", "dependencies": {"onetime": "^5.1.0", "signal-exit": "^3.0.2"}, "engines": {"node": ">=8"}}, "client/node_modules/retry": {"version": "0.13.1", "devOptional": true, "license": "MIT", "engines": {"node": ">= 4"}}, "client/node_modules/reusify": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"iojs": ">=1.0.0", "node": ">=0.10.0"}}, "client/node_modules/rollup": {"version": "3.29.5", "license": "MIT", "bin": {"rollup": "dist/bin/rollup"}, "engines": {"node": ">=14.18.0", "npm": ">=8.0.0"}, "optionalDependencies": {"fsevents": "~2.3.2"}}, "client/node_modules/rrweb-cssom": {"version": "0.7.1", "dev": true, "license": "MIT"}, "client/node_modules/run-async": {"version": "2.4.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.12.0"}}, "client/node_modules/run-parallel": {"version": "1.2.0", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT", "dependencies": {"queue-microtask": "^1.2.2"}}, "client/node_modules/safe-array-concat": {"version": "1.1.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "get-intrinsic": "^1.2.6", "has-symbols": "^1.1.0", "isarray": "^2.0.5"}, "engines": {"node": ">=0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/safe-buffer": {"version": "5.2.1", "funding": [{"type": "github", "url": "https://github.com/sponsors/feross"}, {"type": "patreon", "url": "https://www.patreon.com/feross"}, {"type": "consulting", "url": "https://feross.org/support"}], "license": "MIT"}, "client/node_modules/safe-push-apply": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "isarray": "^2.0.5"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/safe-regex-test": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "is-regex": "^1.2.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/safer-buffer": {"version": "2.1.2", "devOptional": true, "license": "MIT"}, "client/node_modules/sass": {"version": "1.89.2", "license": "MIT", "dependencies": {"chokidar": "^4.0.0", "immutable": "^5.0.2", "source-map-js": ">=0.6.2 <2.0.0"}, "bin": {"sass": "sass.js"}, "engines": {"node": ">=14.0.0"}, "optionalDependencies": {"@parcel/watcher": "^2.4.1"}}, "client/node_modules/sass-loader": {"version": "14.2.1", "dev": true, "license": "MIT", "dependencies": {"neo-async": "^2.6.2"}, "engines": {"node": ">= 18.12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "node-sass": "^4.0.0 || ^5.0.0 || ^6.0.0 || ^7.0.0 || ^8.0.0 || ^9.0.0", "sass": "^1.3.0", "sass-embedded": "*", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "node-sass": {"optional": true}, "sass": {"optional": true}, "sass-embedded": {"optional": true}, "webpack": {"optional": true}}}, "client/node_modules/sass/node_modules/chokidar": {"version": "4.0.3", "license": "MIT", "dependencies": {"readdirp": "^4.0.1"}, "engines": {"node": ">= 14.16.0"}, "funding": {"url": "https://paulmillr.com/funding/"}}, "client/node_modules/sass/node_modules/readdirp": {"version": "4.1.2", "license": "MIT", "engines": {"node": ">= 14.18.0"}, "funding": {"type": "individual", "url": "https://paulmillr.com/funding/"}}, "client/node_modules/sax": {"version": "1.2.4", "dev": true, "license": "ISC"}, "client/node_modules/saxes": {"version": "6.0.0", "dev": true, "license": "ISC", "dependencies": {"xmlchars": "^2.2.0"}, "engines": {"node": ">=v12.22.7"}}, "client/node_modules/schema-utils": {"version": "2.7.1", "dev": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.5", "ajv": "^6.12.4", "ajv-keywords": "^3.5.2"}, "engines": {"node": ">= 8.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "client/node_modules/scss-bundle": {"version": "3.1.2", "license": "MIT", "dependencies": {"@types/archy": "^0.0.31", "@types/debug": "^4.1.5", "@types/fs-extra": "^8.0.1", "@types/glob": "^7.1.1", "@types/lodash.debounce": "^4.0.6", "@types/sass": "^1.16.0", "archy": "^1.0.0", "chalk": "^3.0.0", "chokidar": "^3.3.1", "commander": "^4.0.1", "fs-extra": "^8.1.0", "globs": "^0.1.4", "lodash.debounce": "^4.0.8", "loglevel": "^1.6.6", "loglevel-plugin-prefix": "^0.8.4", "pretty-bytes": "^5.3.0", "sass": "^1.23.7", "tslib": "^1.10.0"}, "bin": {"scss-bundle": "dist/cli/main.js"}}, "client/node_modules/scss-bundle/node_modules/chalk": {"version": "3.0.0", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=8"}}, "client/node_modules/scss-bundle/node_modules/commander": {"version": "4.1.1", "license": "MIT", "engines": {"node": ">= 6"}}, "client/node_modules/scss-bundle/node_modules/fs-extra": {"version": "8.1.0", "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^4.0.0", "universalify": "^0.1.0"}, "engines": {"node": ">=6 <7 || >=8"}}, "client/node_modules/scss-bundle/node_modules/jsonfile": {"version": "4.0.0", "license": "MIT", "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "client/node_modules/scss-bundle/node_modules/universalify": {"version": "0.1.2", "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "client/node_modules/seek-bzip": {"version": "1.0.6", "dev": true, "license": "MIT", "dependencies": {"commander": "^2.8.1"}, "bin": {"seek-bunzip": "bin/seek-bunzip", "seek-table": "bin/seek-bzip-table"}}, "client/node_modules/select-hose": {"version": "2.0.0", "devOptional": true, "license": "MIT"}, "client/node_modules/selfsigned": {"version": "2.4.1", "devOptional": true, "license": "MIT", "dependencies": {"@types/node-forge": "^1.3.0", "node-forge": "^1"}, "engines": {"node": ">=10"}}, "client/node_modules/semver": {"version": "6.3.1", "license": "ISC", "bin": {"semver": "bin/semver.js"}}, "client/node_modules/send": {"version": "0.19.0", "devOptional": true, "license": "MIT", "dependencies": {"debug": "2.6.9", "depd": "2.0.0", "destroy": "1.2.0", "encodeurl": "~1.0.2", "escape-html": "~1.0.3", "etag": "~1.8.1", "fresh": "0.5.2", "http-errors": "2.0.0", "mime": "1.6.0", "ms": "2.1.3", "on-finished": "2.4.1", "range-parser": "~1.2.1", "statuses": "2.0.1"}, "engines": {"node": ">= 0.8.0"}}, "client/node_modules/send/node_modules/debug": {"version": "2.6.9", "devOptional": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "client/node_modules/send/node_modules/debug/node_modules/ms": {"version": "2.0.0", "devOptional": true, "license": "MIT"}, "client/node_modules/send/node_modules/encodeurl": {"version": "1.0.2", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "client/node_modules/serve-index": {"version": "1.9.1", "devOptional": true, "license": "MIT", "dependencies": {"accepts": "~1.3.4", "batch": "0.6.1", "debug": "2.6.9", "escape-html": "~1.0.3", "http-errors": "~1.6.2", "mime-types": "~2.1.17", "parseurl": "~1.3.2"}, "engines": {"node": ">= 0.8.0"}}, "client/node_modules/serve-index/node_modules/debug": {"version": "2.6.9", "devOptional": true, "license": "MIT", "dependencies": {"ms": "2.0.0"}}, "client/node_modules/serve-index/node_modules/depd": {"version": "1.1.2", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/serve-index/node_modules/http-errors": {"version": "1.6.3", "devOptional": true, "license": "MIT", "dependencies": {"depd": "~1.1.2", "inherits": "2.0.3", "setprototypeof": "1.1.0", "statuses": ">= 1.4.0 < 2"}, "engines": {"node": ">= 0.6"}}, "client/node_modules/serve-index/node_modules/inherits": {"version": "2.0.3", "devOptional": true, "license": "ISC"}, "client/node_modules/serve-index/node_modules/ms": {"version": "2.0.0", "devOptional": true, "license": "MIT"}, "client/node_modules/serve-index/node_modules/setprototypeof": {"version": "1.1.0", "devOptional": true, "license": "ISC"}, "client/node_modules/serve-index/node_modules/statuses": {"version": "1.5.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "client/node_modules/serve-static": {"version": "1.16.2", "devOptional": true, "license": "MIT", "dependencies": {"encodeurl": "~2.0.0", "escape-html": "~1.0.3", "parseurl": "~1.3.3", "send": "0.19.0"}, "engines": {"node": ">= 0.8.0"}}, "client/node_modules/set-function-length": {"version": "1.2.2", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "function-bind": "^1.1.2", "get-intrinsic": "^1.2.4", "gopd": "^1.0.1", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/set-function-name": {"version": "2.0.2", "dev": true, "license": "MIT", "dependencies": {"define-data-property": "^1.1.4", "es-errors": "^1.3.0", "functions-have-names": "^1.2.3", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/set-proto": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"dunder-proto": "^1.0.1", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/setprototypeof": {"version": "1.2.0", "devOptional": true, "license": "ISC"}, "client/node_modules/shallow-clone": {"version": "3.0.1", "license": "MIT", "dependencies": {"kind-of": "^6.0.2"}, "engines": {"node": ">=8"}}, "client/node_modules/shebang-command": {"version": "2.0.0", "license": "MIT", "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/shebang-regex": {"version": "3.0.0", "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/side-channel": {"version": "1.1.0", "devOptional": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3", "side-channel-list": "^1.0.0", "side-channel-map": "^1.0.1", "side-channel-weakmap": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/side-channel-list": {"version": "1.0.0", "devOptional": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/side-channel-map": {"version": "1.0.1", "devOptional": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/side-channel-weakmap": {"version": "1.0.2", "devOptional": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "es-errors": "^1.3.0", "get-intrinsic": "^1.2.5", "object-inspect": "^1.13.3", "side-channel-map": "^1.0.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/signal-exit": {"version": "3.0.7", "license": "ISC"}, "client/node_modules/slash": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/slice-ansi": {"version": "4.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "client/node_modules/sockjs": {"version": "0.3.24", "devOptional": true, "license": "MIT", "dependencies": {"faye-websocket": "^0.11.3", "uuid": "^8.3.2", "websocket-driver": "^0.7.4"}}, "client/node_modules/sockjs/node_modules/uuid": {"version": "8.3.2", "devOptional": true, "license": "MIT", "bin": {"uuid": "dist/bin/uuid"}}, "client/node_modules/sort-keys": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"is-plain-obj": "^1.0.0"}, "engines": {"node": ">=4"}}, "client/node_modules/sort-keys-length": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"sort-keys": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/sort-keys-length/node_modules/sort-keys": {"version": "1.1.2", "dev": true, "license": "MIT", "dependencies": {"is-plain-obj": "^1.0.0"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/source-list-map": {"version": "2.0.1", "dev": true, "license": "MIT"}, "client/node_modules/source-map": {"version": "0.6.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "client/node_modules/source-map-js": {"version": "1.2.1", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "client/node_modules/source-map-support": {"version": "0.5.21", "license": "MIT", "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "client/node_modules/sourcemapped-stacktrace": {"version": "1.1.11", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"source-map": "0.5.6"}}, "client/node_modules/sourcemapped-stacktrace/node_modules/source-map": {"version": "0.5.6", "license": "BSD-3-<PERSON><PERSON>", "engines": {"node": ">=0.10.0"}}, "client/node_modules/spdx-correct": {"version": "3.2.0", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-expression-parse": "^3.0.0", "spdx-license-ids": "^3.0.0"}}, "client/node_modules/spdx-exceptions": {"version": "2.5.0", "dev": true, "license": "CC-BY-3.0"}, "client/node_modules/spdx-expression-parse": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"spdx-exceptions": "^2.1.0", "spdx-license-ids": "^3.0.0"}}, "client/node_modules/spdx-license-ids": {"version": "3.0.21", "dev": true, "license": "CC0-1.0"}, "client/node_modules/spdy": {"version": "4.0.2", "devOptional": true, "license": "MIT", "dependencies": {"debug": "^4.1.0", "handle-thing": "^2.0.0", "http-deceiver": "^1.2.7", "select-hose": "^2.0.0", "spdy-transport": "^3.0.0"}, "engines": {"node": ">=6.0.0"}}, "client/node_modules/spdy-transport": {"version": "3.0.0", "devOptional": true, "license": "MIT", "dependencies": {"debug": "^4.1.0", "detect-node": "^2.0.4", "hpack.js": "^2.1.6", "obuf": "^1.1.2", "readable-stream": "^3.0.6", "wbuf": "^1.7.3"}}, "client/node_modules/sshpk": {"version": "1.18.0", "dev": true, "license": "MIT", "dependencies": {"asn1": "~0.2.3", "assert-plus": "^1.0.0", "bcrypt-pbkdf": "^1.0.0", "dashdash": "^1.12.0", "ecc-jsbn": "~0.1.1", "getpass": "^0.1.1", "jsbn": "~0.1.0", "safer-buffer": "^2.0.2", "tweetnacl": "~0.14.0"}, "bin": {"sshpk-conv": "bin/sshpk-conv", "sshpk-sign": "bin/sshpk-sign", "sshpk-verify": "bin/sshpk-verify"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/statuses": {"version": "2.0.1", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "client/node_modules/std-env": {"version": "3.9.0", "dev": true, "license": "MIT"}, "client/node_modules/stop-iteration-iterator": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"es-errors": "^1.3.0", "internal-slot": "^1.1.0"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/strict-uri-encode": {"version": "1.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/string_decoder": {"version": "1.3.0", "license": "MIT", "dependencies": {"safe-buffer": "~5.2.0"}}, "client/node_modules/string-width-cjs": {"name": "string-width", "version": "4.2.3", "dev": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "client/node_modules/string.fromcodepoint": {"version": "0.2.1", "dev": true}, "client/node_modules/string.prototype.matchall": {"version": "4.0.12", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.3", "define-properties": "^1.2.1", "es-abstract": "^1.23.6", "es-errors": "^1.3.0", "es-object-atoms": "^1.0.0", "get-intrinsic": "^1.2.6", "gopd": "^1.2.0", "has-symbols": "^1.1.0", "internal-slot": "^1.1.0", "regexp.prototype.flags": "^1.5.3", "set-function-name": "^2.0.2", "side-channel": "^1.1.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/string.prototype.repeat": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"define-properties": "^1.1.3", "es-abstract": "^1.17.5"}}, "client/node_modules/string.prototype.trim": {"version": "1.2.10", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-data-property": "^1.1.4", "define-properties": "^1.2.1", "es-abstract": "^1.23.5", "es-object-atoms": "^1.0.0", "has-property-descriptors": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/string.prototype.trimend": {"version": "1.0.9", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "call-bound": "^1.0.2", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/string.prototype.trimstart": {"version": "1.0.8", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "define-properties": "^1.2.1", "es-object-atoms": "^1.0.0"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/strip-ansi-cjs": {"name": "strip-ansi", "version": "6.0.1", "dev": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "client/node_modules/strip-bom": {"version": "3.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/strip-dirs": {"version": "2.1.0", "dev": true, "license": "MIT", "dependencies": {"is-natural-number": "^4.0.1"}}, "client/node_modules/strip-final-newline": {"version": "2.0.0", "devOptional": true, "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/strip-indent": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"min-indent": "^1.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/strip-json-comments": {"version": "3.1.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/strip-outer": {"version": "1.0.1", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/strip-outer/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "client/node_modules/style-loader": {"version": "3.3.4", "dev": true, "license": "MIT", "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.0.0"}}, "client/node_modules/style-search": {"version": "0.1.0", "dev": true, "license": "ISC"}, "client/node_modules/stylehacks": {"version": "6.1.1", "dev": true, "license": "MIT", "dependencies": {"browserslist": "^4.23.0", "postcss-selector-parser": "^6.0.16"}, "engines": {"node": "^14 || ^16 || >=18.0"}, "peerDependencies": {"postcss": "^8.4.31"}}, "client/node_modules/stylelint": {"version": "14.16.1", "dev": true, "license": "MIT", "dependencies": {"@csstools/selector-specificity": "^2.0.2", "balanced-match": "^2.0.0", "colord": "^2.9.3", "cosmiconfig": "^7.1.0", "css-functions-list": "^3.1.0", "debug": "^4.3.4", "fast-glob": "^3.2.12", "fastest-levenshtein": "^1.0.16", "file-entry-cache": "^6.0.1", "global-modules": "^2.0.0", "globby": "^11.1.0", "globjoin": "^0.1.4", "html-tags": "^3.2.0", "ignore": "^5.2.1", "import-lazy": "^4.0.0", "imurmurhash": "^0.1.4", "is-plain-object": "^5.0.0", "known-css-properties": "^0.26.0", "mathml-tag-names": "^2.1.3", "meow": "^9.0.0", "micromatch": "^4.0.5", "normalize-path": "^3.0.0", "picocolors": "^1.0.0", "postcss": "^8.4.19", "postcss-media-query-parser": "^0.2.3", "postcss-resolve-nested-selector": "^0.1.1", "postcss-safe-parser": "^6.0.0", "postcss-selector-parser": "^6.0.11", "postcss-value-parser": "^4.2.0", "resolve-from": "^5.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1", "style-search": "^0.1.0", "supports-hyperlinks": "^2.3.0", "svg-tags": "^1.0.0", "table": "^6.8.1", "v8-compile-cache": "^2.3.0", "write-file-atomic": "^4.0.2"}, "bin": {"stylelint": "bin/stylelint.js"}, "engines": {"node": "^12.20.0 || ^14.13.1 || >=16.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/stylelint"}}, "client/node_modules/stylelint/node_modules/@csstools/selector-specificity": {"version": "2.2.0", "dev": true, "license": "CC0-1.0", "engines": {"node": "^14 || ^16 || >=18"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/csstools"}, "peerDependencies": {"postcss-selector-parser": "^6.0.10"}}, "client/node_modules/stylelint/node_modules/balanced-match": {"version": "2.0.0", "dev": true, "license": "MIT"}, "client/node_modules/stylelint/node_modules/cosmiconfig": {"version": "7.1.0", "dev": true, "license": "MIT", "dependencies": {"@types/parse-json": "^4.0.0", "import-fresh": "^3.2.1", "parse-json": "^5.0.0", "path-type": "^4.0.0", "yaml": "^1.10.0"}, "engines": {"node": ">=10"}}, "client/node_modules/stylelint/node_modules/is-plain-object": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/stylelint/node_modules/resolve-from": {"version": "5.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/stylus-loader": {"version": "8.1.1", "dev": true, "license": "MIT", "dependencies": {"fast-glob": "^3.3.2", "normalize-path": "^3.0.0"}, "engines": {"node": ">= 18.12.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"@rspack/core": "0.x || 1.x", "stylus": ">=0.52.4", "webpack": "^5.0.0"}, "peerDependenciesMeta": {"@rspack/core": {"optional": true}, "webpack": {"optional": true}}}, "client/node_modules/supports-color": {"version": "7.2.0", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/supports-hyperlinks": {"version": "2.3.0", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0", "supports-color": "^7.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/svg-tags": {"version": "1.0.0", "dev": true}, "client/node_modules/svgo": {"version": "3.3.2", "dev": true, "license": "MIT", "dependencies": {"@trysound/sax": "0.2.0", "commander": "^7.2.0", "css-select": "^5.1.0", "css-tree": "^2.3.1", "css-what": "^6.1.0", "csso": "^5.0.5", "picocolors": "^1.0.0"}, "bin": {"svgo": "bin/svgo"}, "engines": {"node": ">=14.0.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/svgo"}}, "client/node_modules/svgo/node_modules/commander": {"version": "7.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 10"}}, "client/node_modules/svgo/node_modules/css-select": {"version": "5.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"boolbase": "^1.0.0", "css-what": "^6.1.0", "domhandler": "^5.0.2", "domutils": "^3.0.1", "nth-check": "^2.0.1"}, "funding": {"url": "https://github.com/sponsors/fb55"}}, "client/node_modules/svgo/node_modules/css-tree": {"version": "2.3.1", "dev": true, "license": "MIT", "dependencies": {"mdn-data": "2.0.30", "source-map-js": "^1.0.1"}, "engines": {"node": "^10 || ^12.20.0 || ^14.13.0 || >=15.0.0"}}, "client/node_modules/svgo/node_modules/csso": {"version": "5.0.5", "dev": true, "license": "MIT", "dependencies": {"css-tree": "~2.2.0"}, "engines": {"node": "^10 || ^12.20.0 || ^14.13.0 || >=15.0.0", "npm": ">=7.0.0"}}, "client/node_modules/svgo/node_modules/csso/node_modules/css-tree": {"version": "2.2.1", "dev": true, "license": "MIT", "dependencies": {"mdn-data": "2.0.28", "source-map-js": "^1.0.1"}, "engines": {"node": "^10 || ^12.20.0 || ^14.13.0 || >=15.0.0", "npm": ">=7.0.0"}}, "client/node_modules/svgo/node_modules/csso/node_modules/mdn-data": {"version": "2.0.28", "dev": true, "license": "CC0-1.0"}, "client/node_modules/svgo/node_modules/dom-serializer": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"domelementtype": "^2.3.0", "domhandler": "^5.0.2", "entities": "^4.2.0"}, "funding": {"url": "https://github.com/cheeriojs/dom-serializer?sponsor=1"}}, "client/node_modules/svgo/node_modules/domhandler": {"version": "5.0.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"domelementtype": "^2.3.0"}, "engines": {"node": ">= 4"}, "funding": {"url": "https://github.com/fb55/domhandler?sponsor=1"}}, "client/node_modules/svgo/node_modules/domutils": {"version": "3.2.2", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"dom-serializer": "^2.0.0", "domelementtype": "^2.3.0", "domhandler": "^5.0.3"}, "funding": {"url": "https://github.com/fb55/domutils?sponsor=1"}}, "client/node_modules/svgo/node_modules/mdn-data": {"version": "2.0.30", "dev": true, "license": "CC0-1.0"}, "client/node_modules/swiper": {"version": "11.1.0", "funding": [{"type": "patreon", "url": "https://www.patreon.com/swiperjs"}, {"type": "open_collective", "url": "http://opencollective.com/swiper"}], "license": "MIT", "engines": {"node": ">= 4.7.0"}}, "client/node_modules/symbol-tree": {"version": "3.2.4", "dev": true, "license": "MIT"}, "client/node_modules/table": {"version": "6.9.0", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"ajv": "^8.0.1", "lodash.truncate": "^4.4.2", "slice-ansi": "^4.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=10.0.0"}}, "client/node_modules/table/node_modules/ajv": {"version": "8.17.1", "dev": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "client/node_modules/table/node_modules/json-schema-traverse": {"version": "1.0.0", "dev": true, "license": "MIT"}, "client/node_modules/tapable": {"version": "2.2.2", "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/tar-stream": {"version": "1.6.2", "dev": true, "license": "MIT", "dependencies": {"bl": "^1.0.0", "buffer-alloc": "^1.2.0", "end-of-stream": "^1.0.0", "fs-constants": "^1.0.0", "readable-stream": "^2.3.0", "to-buffer": "^1.1.1", "xtend": "^4.0.0"}, "engines": {"node": ">= 0.8.0"}}, "client/node_modules/tar-stream/node_modules/bl": {"version": "1.2.3", "dev": true, "license": "MIT", "dependencies": {"readable-stream": "^2.3.5", "safe-buffer": "^5.1.1"}}, "client/node_modules/tar-stream/node_modules/isarray": {"version": "1.0.0", "dev": true, "license": "MIT"}, "client/node_modules/tar-stream/node_modules/readable-stream": {"version": "2.3.8", "dev": true, "license": "MIT", "dependencies": {"core-util-is": "~1.0.0", "inherits": "~2.0.3", "isarray": "~1.0.0", "process-nextick-args": "~2.0.0", "safe-buffer": "~5.1.1", "string_decoder": "~1.1.1", "util-deprecate": "~1.0.1"}}, "client/node_modules/tar-stream/node_modules/safe-buffer": {"version": "5.1.2", "dev": true, "license": "MIT"}, "client/node_modules/tar-stream/node_modules/string_decoder": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"safe-buffer": "~5.1.0"}}, "client/node_modules/terser-webpack-plugin": {"version": "5.3.14", "license": "MIT", "dependencies": {"@jridgewell/trace-mapping": "^0.3.25", "jest-worker": "^27.4.5", "schema-utils": "^4.3.0", "serialize-javascript": "^6.0.2", "terser": "^5.31.1"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^5.1.0"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "esbuild": {"optional": true}, "uglify-js": {"optional": true}}}, "client/node_modules/terser-webpack-plugin/node_modules/ajv": {"version": "8.17.1", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "client/node_modules/terser-webpack-plugin/node_modules/ajv-keywords": {"version": "5.1.0", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "client/node_modules/terser-webpack-plugin/node_modules/jest-worker": {"version": "27.5.1", "license": "MIT", "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": ">= 10.13.0"}}, "client/node_modules/terser-webpack-plugin/node_modules/json-schema-traverse": {"version": "1.0.0", "license": "MIT"}, "client/node_modules/terser-webpack-plugin/node_modules/schema-utils": {"version": "4.3.2", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "client/node_modules/terser-webpack-plugin/node_modules/serialize-javascript": {"version": "6.0.2", "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"randombytes": "^2.1.0"}}, "client/node_modules/terser-webpack-plugin/node_modules/supports-color": {"version": "8.1.1", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "client/node_modules/terser-webpack-plugin/node_modules/terser": {"version": "5.43.1", "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"@jridgewell/source-map": "^0.3.3", "acorn": "^8.14.0", "commander": "^2.20.0", "source-map-support": "~0.5.20"}, "bin": {"terser": "bin/terser"}, "engines": {"node": ">=10"}}, "client/node_modules/text-table": {"version": "0.2.0", "dev": true, "license": "MIT"}, "client/node_modules/thenify": {"version": "3.3.1", "dev": true, "license": "MIT", "dependencies": {"any-promise": "^1.0.0"}}, "client/node_modules/thenify-all": {"version": "1.6.0", "dev": true, "license": "MIT", "dependencies": {"thenify": ">= 3.1.0 < 4"}, "engines": {"node": ">=0.8"}}, "client/node_modules/through": {"version": "2.3.8", "dev": true, "license": "MIT"}, "client/node_modules/thunky": {"version": "1.1.0", "devOptional": true, "license": "MIT"}, "client/node_modules/timed-out": {"version": "4.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/tmp": {"version": "0.0.33", "dev": true, "license": "MIT", "dependencies": {"os-tmpdir": "~1.0.2"}, "engines": {"node": ">=0.6.0"}}, "client/node_modules/to-buffer": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"isarray": "^2.0.5", "safe-buffer": "^5.2.1", "typed-array-buffer": "^1.0.3"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/to-readable-stream": {"version": "1.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=6"}}, "client/node_modules/to-regex-range": {"version": "5.0.1", "license": "MIT", "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "client/node_modules/toidentifier": {"version": "1.0.1", "devOptional": true, "license": "MIT", "engines": {"node": ">=0.6"}}, "client/node_modules/tough-cookie": {"version": "4.1.4", "dev": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"psl": "^1.1.33", "punycode": "^2.1.1", "universalify": "^0.2.0", "url-parse": "^1.5.3"}, "engines": {"node": ">=6"}}, "client/node_modules/tough-cookie/node_modules/universalify": {"version": "0.2.0", "dev": true, "license": "MIT", "engines": {"node": ">= 4.0.0"}}, "client/node_modules/tr46": {"version": "5.1.1", "dev": true, "license": "MIT", "dependencies": {"punycode": "^2.3.1"}, "engines": {"node": ">=18"}}, "client/node_modules/trim-newlines": {"version": "3.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=8"}}, "client/node_modules/trim-repeated": {"version": "1.0.0", "dev": true, "license": "MIT", "dependencies": {"escape-string-regexp": "^1.0.2"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/trim-repeated/node_modules/escape-string-regexp": {"version": "1.0.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.8.0"}}, "client/node_modules/ts-api-utils": {"version": "1.4.3", "dev": true, "license": "MIT", "engines": {"node": ">=16"}, "peerDependencies": {"typescript": ">=4.2.0"}}, "client/node_modules/tsconfig-paths": {"version": "3.15.0", "dev": true, "license": "MIT", "dependencies": {"@types/json5": "^0.0.29", "json5": "^1.0.2", "minimist": "^1.2.6", "strip-bom": "^3.0.0"}}, "client/node_modules/tsconfig-paths/node_modules/json5": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"minimist": "^1.2.0"}, "bin": {"json5": "lib/cli.js"}}, "client/node_modules/tslib": {"version": "1.14.1", "license": "0BSD"}, "client/node_modules/tsutils": {"version": "3.21.0", "dev": true, "license": "MIT", "dependencies": {"tslib": "^1.8.1"}, "engines": {"node": ">= 6"}, "peerDependencies": {"typescript": ">=2.8.0 || >= 3.2.0-dev || >= 3.3.0-dev || >= 3.4.0-dev || >= 3.5.0-dev || >= 3.6.0-dev || >= 3.6.0-beta || >= 3.7.0-dev || >= 3.7.0-beta"}}, "client/node_modules/tunnel-agent": {"version": "0.6.0", "dev": true, "license": "Apache-2.0", "dependencies": {"safe-buffer": "^5.0.1"}, "engines": {"node": "*"}}, "client/node_modules/tweetnacl": {"version": "0.14.5", "dev": true, "license": "Unlicense"}, "client/node_modules/type-check": {"version": "0.4.0", "dev": true, "license": "MIT", "dependencies": {"prelude-ls": "^1.2.1"}, "engines": {"node": ">= 0.8.0"}}, "client/node_modules/type-fest": {"version": "0.21.3", "license": "(MIT OR CC0-1.0)", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/type-is": {"version": "1.6.18", "devOptional": true, "license": "MIT", "dependencies": {"media-typer": "0.3.0", "mime-types": "~2.1.24"}, "engines": {"node": ">= 0.6"}}, "client/node_modules/typed-array-buffer": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "es-errors": "^1.3.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/typed-array-byte-length": {"version": "1.0.3", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.14"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/typed-array-byte-offset": {"version": "1.0.4", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "for-each": "^0.3.3", "gopd": "^1.2.0", "has-proto": "^1.2.0", "is-typed-array": "^1.1.15", "reflect.getprototypeof": "^1.0.9"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/typed-array-length": {"version": "1.0.7", "dev": true, "license": "MIT", "dependencies": {"call-bind": "^1.0.7", "for-each": "^0.3.3", "gopd": "^1.0.1", "is-typed-array": "^1.1.13", "possible-typed-array-names": "^1.0.0", "reflect.getprototypeof": "^1.0.6"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/typescript": {"version": "4.9.5", "devOptional": true, "license": "Apache-2.0", "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=4.2.0"}}, "client/node_modules/uglify-js": {"version": "3.19.3", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "bin": {"uglifyjs": "bin/uglifyjs"}, "engines": {"node": ">=0.8.0"}}, "client/node_modules/unbox-primitive": {"version": "1.1.0", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.3", "has-bigints": "^1.0.2", "has-symbols": "^1.1.0", "which-boxed-primitive": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/unbzip2-stream": {"version": "1.4.3", "dev": true, "license": "MIT", "dependencies": {"buffer": "^5.2.1", "through": "^2.3.8"}}, "client/node_modules/unescape-js": {"version": "1.1.4", "dev": true, "license": "MIT", "dependencies": {"string.fromcodepoint": "^0.2.1"}}, "client/node_modules/unicode-canonical-property-names-ecmascript": {"version": "2.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/unicode-match-property-ecmascript": {"version": "2.0.0", "dev": true, "license": "MIT", "dependencies": {"unicode-canonical-property-names-ecmascript": "^2.0.0", "unicode-property-aliases-ecmascript": "^2.0.0"}, "engines": {"node": ">=4"}}, "client/node_modules/unicode-match-property-value-ecmascript": {"version": "2.2.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/unicode-property-aliases-ecmascript": {"version": "2.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=4"}}, "client/node_modules/unicorn-magic": {"version": "0.3.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "client/node_modules/universalify": {"version": "2.0.1", "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "client/node_modules/unpipe": {"version": "1.0.0", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "client/node_modules/update-browserslist-db": {"version": "1.1.3", "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "license": "MIT", "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "client/node_modules/upper-case": {"version": "1.1.3", "dev": true, "license": "MIT"}, "client/node_modules/uri-js": {"version": "4.4.1", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "dependencies": {"punycode": "^2.1.0"}}, "client/node_modules/url": {"version": "0.11.4", "dev": true, "license": "MIT", "dependencies": {"punycode": "^1.4.1", "qs": "^6.12.3"}, "engines": {"node": ">= 0.4"}}, "client/node_modules/url-parse": {"version": "1.5.10", "dev": true, "license": "MIT", "dependencies": {"querystringify": "^2.1.1", "requires-port": "^1.0.0"}}, "client/node_modules/url-parse-lax": {"version": "3.0.0", "dev": true, "license": "MIT", "dependencies": {"prepend-http": "^2.0.0"}, "engines": {"node": ">=4"}}, "client/node_modules/url-to-options": {"version": "1.0.1", "dev": true, "license": "MIT", "engines": {"node": ">= 4"}}, "client/node_modules/url/node_modules/punycode": {"version": "1.4.1", "dev": true, "license": "MIT"}, "client/node_modules/util-deprecate": {"version": "1.0.2", "license": "MIT"}, "client/node_modules/utila": {"version": "0.4.0", "devOptional": true, "license": "MIT"}, "client/node_modules/utils-merge": {"version": "1.0.1", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.4.0"}}, "client/node_modules/uuid": {"version": "3.4.0", "dev": true, "license": "MIT", "bin": {"uuid": "bin/uuid"}}, "client/node_modules/v8-compile-cache": {"version": "2.4.0", "dev": true, "license": "MIT"}, "client/node_modules/validate-html-nesting": {"version": "1.2.3", "dev": true, "license": "ISC"}, "client/node_modules/validate-npm-package-license": {"version": "3.0.4", "dev": true, "license": "Apache-2.0", "dependencies": {"spdx-correct": "^3.0.0", "spdx-expression-parse": "^3.0.0"}}, "client/node_modules/validate-npm-package-name": {"version": "5.0.1", "dev": true, "license": "ISC", "engines": {"node": "^14.17.0 || ^16.13.0 || >=18.0.0"}}, "client/node_modules/vary": {"version": "1.1.2", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.8"}}, "client/node_modules/verror": {"version": "1.10.0", "dev": true, "engines": ["node >=0.6.0"], "license": "MIT", "dependencies": {"assert-plus": "^1.0.0", "core-util-is": "1.0.2", "extsprintf": "^1.2.0"}}, "client/node_modules/vm2": {"version": "3.9.19", "dev": true, "license": "MIT", "dependencies": {"acorn": "^8.7.0", "acorn-walk": "^8.2.0"}, "bin": {"vm2": "bin/vm2"}, "engines": {"node": ">=6.0"}}, "client/node_modules/vue": {"version": "3.5.17", "license": "MIT", "dependencies": {"@vue/compiler-dom": "3.5.17", "@vue/compiler-sfc": "3.5.17", "@vue/runtime-dom": "3.5.17", "@vue/server-renderer": "3.5.17", "@vue/shared": "3.5.17"}, "peerDependencies": {"typescript": "*"}, "peerDependenciesMeta": {"typescript": {"optional": true}}}, "client/node_modules/vue-loader": {"version": "17.4.2", "license": "MIT", "dependencies": {"chalk": "^4.1.0", "hash-sum": "^2.0.0", "watchpack": "^2.4.0"}, "peerDependencies": {"webpack": "^4.1.0 || ^5.0.0-0"}, "peerDependenciesMeta": {"@vue/compiler-sfc": {"optional": true}, "vue": {"optional": true}}}, "client/node_modules/vue-router": {"version": "4.5.1", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.6.4"}, "funding": {"url": "https://github.com/sponsors/posva"}, "peerDependencies": {"vue": "^3.2.0"}}, "client/node_modules/vuex": {"version": "4.1.0", "license": "MIT", "dependencies": {"@vue/devtools-api": "^6.0.0-beta.11"}, "peerDependencies": {"vue": "^3.2.0"}}, "client/node_modules/w3c-xmlserializer": {"version": "5.0.0", "dev": true, "license": "MIT", "dependencies": {"xml-name-validator": "^5.0.0"}, "engines": {"node": ">=18"}}, "client/node_modules/watchpack": {"version": "2.4.4", "license": "MIT", "dependencies": {"glob-to-regexp": "^0.4.1", "graceful-fs": "^4.1.2"}, "engines": {"node": ">=10.13.0"}}, "client/node_modules/wbuf": {"version": "1.7.3", "devOptional": true, "license": "MIT", "dependencies": {"minimalistic-assert": "^1.0.0"}}, "client/node_modules/wcwidth": {"version": "1.0.1", "license": "MIT", "dependencies": {"defaults": "^1.0.3"}}, "client/node_modules/webidl-conversions": {"version": "7.0.0", "dev": true, "license": "BSD-2-<PERSON><PERSON>", "engines": {"node": ">=12"}}, "client/node_modules/webpack": {"version": "5.99.9", "license": "MIT", "dependencies": {"@types/eslint-scope": "^3.7.7", "@types/estree": "^1.0.6", "@types/json-schema": "^7.0.15", "@webassemblyjs/ast": "^1.14.1", "@webassemblyjs/wasm-edit": "^1.14.1", "@webassemblyjs/wasm-parser": "^1.14.1", "acorn": "^8.14.0", "browserslist": "^4.24.0", "chrome-trace-event": "^1.0.2", "enhanced-resolve": "^5.17.1", "es-module-lexer": "^1.2.1", "eslint-scope": "5.1.1", "events": "^3.2.0", "glob-to-regexp": "^0.4.1", "graceful-fs": "^4.2.11", "json-parse-even-better-errors": "^2.3.1", "loader-runner": "^4.2.0", "mime-types": "^2.1.27", "neo-async": "^2.6.2", "schema-utils": "^4.3.2", "tapable": "^2.1.1", "terser-webpack-plugin": "^5.3.11", "watchpack": "^2.4.1", "webpack-sources": "^3.2.3"}, "bin": {"webpack": "bin/webpack.js"}, "engines": {"node": ">=10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependenciesMeta": {"webpack-cli": {"optional": true}}}, "client/node_modules/webpack-chain": {"version": "6.5.1", "devOptional": true, "license": "MPL-2.0", "dependencies": {"deepmerge": "^1.5.2", "javascript-stringify": "^2.0.1"}, "engines": {"node": ">=8"}}, "client/node_modules/webpack-cli": {"version": "5.1.4", "dev": true, "license": "MIT", "dependencies": {"@discoveryjs/json-ext": "^0.5.0", "@webpack-cli/configtest": "^2.1.1", "@webpack-cli/info": "^2.0.2", "@webpack-cli/serve": "^2.0.5", "colorette": "^2.0.14", "commander": "^10.0.1", "cross-spawn": "^7.0.3", "envinfo": "^7.7.3", "fastest-levenshtein": "^1.0.12", "import-local": "^3.0.2", "interpret": "^3.1.1", "rechoir": "^0.8.0", "webpack-merge": "^5.7.3"}, "bin": {"webpack-cli": "bin/cli.js"}, "engines": {"node": ">=14.15.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "5.x.x"}, "peerDependenciesMeta": {"@webpack-cli/generators": {"optional": true}, "webpack-bundle-analyzer": {"optional": true}, "webpack-dev-server": {"optional": true}}}, "client/node_modules/webpack-cli/node_modules/commander": {"version": "10.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=14"}}, "client/node_modules/webpack-dev-middleware": {"version": "5.3.4", "devOptional": true, "license": "MIT", "dependencies": {"colorette": "^2.0.10", "memfs": "^3.4.3", "mime-types": "^2.1.31", "range-parser": "^1.2.1", "schema-utils": "^4.0.0"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.0.0 || ^5.0.0"}}, "client/node_modules/webpack-dev-middleware/node_modules/ajv": {"version": "8.17.1", "devOptional": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "client/node_modules/webpack-dev-middleware/node_modules/ajv-keywords": {"version": "5.1.0", "devOptional": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "client/node_modules/webpack-dev-middleware/node_modules/json-schema-traverse": {"version": "1.0.0", "devOptional": true, "license": "MIT"}, "client/node_modules/webpack-dev-middleware/node_modules/schema-utils": {"version": "4.3.2", "devOptional": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "client/node_modules/webpack-dev-server": {"version": "4.15.2", "devOptional": true, "license": "MIT", "dependencies": {"@types/bonjour": "^3.5.9", "@types/connect-history-api-fallback": "^1.3.5", "@types/express": "^4.17.13", "@types/serve-index": "^1.9.1", "@types/serve-static": "^1.13.10", "@types/sockjs": "^0.3.33", "@types/ws": "^8.5.5", "ansi-html-community": "^0.0.8", "bonjour-service": "^1.0.11", "chokidar": "^3.5.3", "colorette": "^2.0.10", "compression": "^1.7.4", "connect-history-api-fallback": "^2.0.0", "default-gateway": "^6.0.3", "express": "^4.17.3", "graceful-fs": "^4.2.6", "html-entities": "^2.3.2", "http-proxy-middleware": "^2.0.3", "ipaddr.js": "^2.0.1", "launch-editor": "^2.6.0", "open": "^8.0.9", "p-retry": "^4.5.0", "rimraf": "^3.0.2", "schema-utils": "^4.0.0", "selfsigned": "^2.1.1", "serve-index": "^1.9.1", "sockjs": "^0.3.24", "spdy": "^4.0.2", "webpack-dev-middleware": "^5.3.4", "ws": "^8.13.0"}, "bin": {"webpack-dev-server": "bin/webpack-dev-server.js"}, "engines": {"node": ">= 12.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}, "peerDependencies": {"webpack": "^4.37.0 || ^5.0.0"}, "peerDependenciesMeta": {"webpack": {"optional": true}, "webpack-cli": {"optional": true}}}, "client/node_modules/webpack-dev-server/node_modules/ajv": {"version": "8.17.1", "devOptional": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "client/node_modules/webpack-dev-server/node_modules/ajv-keywords": {"version": "5.1.0", "devOptional": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "client/node_modules/webpack-dev-server/node_modules/json-schema-traverse": {"version": "1.0.0", "devOptional": true, "license": "MIT"}, "client/node_modules/webpack-dev-server/node_modules/rimraf": {"version": "3.0.2", "devOptional": true, "license": "ISC", "dependencies": {"glob": "^7.1.3"}, "bin": {"rimraf": "bin.js"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "client/node_modules/webpack-dev-server/node_modules/schema-utils": {"version": "4.3.2", "devOptional": true, "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "client/node_modules/webpack-format-messages": {"version": "3.0.1", "dev": true, "license": "MIT", "dependencies": {"kleur": "^4.0.0"}, "engines": {"node": ">=6"}}, "client/node_modules/webpack-merge": {"version": "5.10.0", "license": "MIT", "dependencies": {"clone-deep": "^4.0.1", "flat": "^5.0.2", "wildcard": "^2.0.0"}, "engines": {"node": ">=10.0.0"}}, "client/node_modules/webpack-sources": {"version": "1.4.3", "dev": true, "license": "MIT", "dependencies": {"source-list-map": "^2.0.0", "source-map": "~0.6.1"}}, "client/node_modules/webpack-virtual-modules": {"version": "0.6.2", "dev": true, "license": "MIT"}, "client/node_modules/webpack/node_modules/ajv": {"version": "8.17.1", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "client/node_modules/webpack/node_modules/ajv-keywords": {"version": "5.1.0", "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3"}, "peerDependencies": {"ajv": "^8.8.2"}}, "client/node_modules/webpack/node_modules/es-module-lexer": {"version": "1.7.0", "license": "MIT"}, "client/node_modules/webpack/node_modules/json-schema-traverse": {"version": "1.0.0", "license": "MIT"}, "client/node_modules/webpack/node_modules/schema-utils": {"version": "4.3.2", "license": "MIT", "dependencies": {"@types/json-schema": "^7.0.9", "ajv": "^8.9.0", "ajv-formats": "^2.1.1", "ajv-keywords": "^5.1.0"}, "engines": {"node": ">= 10.13.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/webpack"}}, "client/node_modules/webpack/node_modules/webpack-sources": {"version": "3.3.3", "license": "MIT", "engines": {"node": ">=10.13.0"}}, "client/node_modules/webpackbar": {"version": "5.0.2", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.0", "consola": "^2.15.3", "pretty-time": "^1.1.0", "std-env": "^3.0.1"}, "engines": {"node": ">=12"}, "peerDependencies": {"webpack": "3 || 4 || 5"}}, "client/node_modules/websocket-driver": {"version": "0.7.4", "devOptional": true, "license": "Apache-2.0", "dependencies": {"http-parser-js": ">=0.5.1", "safe-buffer": ">=5.1.0", "websocket-extensions": ">=0.1.1"}, "engines": {"node": ">=0.8.0"}}, "client/node_modules/websocket-extensions": {"version": "0.1.4", "devOptional": true, "license": "Apache-2.0", "engines": {"node": ">=0.8.0"}}, "client/node_modules/whatwg-encoding": {"version": "3.1.1", "dev": true, "license": "MIT", "dependencies": {"iconv-lite": "0.6.3"}, "engines": {"node": ">=18"}}, "client/node_modules/whatwg-encoding/node_modules/iconv-lite": {"version": "0.6.3", "dev": true, "license": "MIT", "dependencies": {"safer-buffer": ">= 2.1.2 < 3.0.0"}, "engines": {"node": ">=0.10.0"}}, "client/node_modules/whatwg-mimetype": {"version": "4.0.0", "dev": true, "license": "MIT", "engines": {"node": ">=18"}}, "client/node_modules/whatwg-url": {"version": "14.2.0", "dev": true, "license": "MIT", "dependencies": {"tr46": "^5.1.0", "webidl-conversions": "^7.0.0"}, "engines": {"node": ">=18"}}, "client/node_modules/which": {"version": "1.3.1", "dev": true, "license": "ISC", "dependencies": {"isexe": "^2.0.0"}, "bin": {"which": "bin/which"}}, "client/node_modules/which-boxed-primitive": {"version": "1.1.1", "dev": true, "license": "MIT", "dependencies": {"is-bigint": "^1.1.0", "is-boolean-object": "^1.2.1", "is-number-object": "^1.1.1", "is-string": "^1.1.1", "is-symbol": "^1.1.1"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/which-builtin-type": {"version": "1.2.1", "dev": true, "license": "MIT", "dependencies": {"call-bound": "^1.0.2", "function.prototype.name": "^1.1.6", "has-tostringtag": "^1.0.2", "is-async-function": "^2.0.0", "is-date-object": "^1.1.0", "is-finalizationregistry": "^1.1.0", "is-generator-function": "^1.0.10", "is-regex": "^1.2.1", "is-weakref": "^1.0.2", "isarray": "^2.0.5", "which-boxed-primitive": "^1.1.0", "which-collection": "^1.0.2", "which-typed-array": "^1.1.16"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/which-collection": {"version": "1.0.2", "dev": true, "license": "MIT", "dependencies": {"is-map": "^2.0.3", "is-set": "^2.0.3", "is-weakmap": "^2.0.2", "is-weakset": "^2.0.3"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/which-typed-array": {"version": "1.1.19", "dev": true, "license": "MIT", "dependencies": {"available-typed-arrays": "^1.0.7", "call-bind": "^1.0.8", "call-bound": "^1.0.4", "for-each": "^0.3.5", "get-proto": "^1.0.1", "gopd": "^1.2.0", "has-tostringtag": "^1.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "client/node_modules/wildcard": {"version": "2.0.1", "license": "MIT"}, "client/node_modules/word-wrap": {"version": "1.2.5", "dev": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "client/node_modules/wrap-ansi": {"version": "6.2.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=8"}}, "client/node_modules/wrap-ansi-cjs": {"name": "wrap-ansi", "version": "7.0.0", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "client/node_modules/wrappy": {"version": "1.0.2", "license": "ISC"}, "client/node_modules/write-file-atomic": {"version": "4.0.2", "dev": true, "license": "ISC", "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "client/node_modules/ws": {"version": "8.18.3", "devOptional": true, "license": "MIT", "engines": {"node": ">=10.0.0"}, "peerDependencies": {"bufferutil": "^4.0.1", "utf-8-validate": ">=5.0.2"}, "peerDependenciesMeta": {"bufferutil": {"optional": true}, "utf-8-validate": {"optional": true}}}, "client/node_modules/wx-server-sdk": {"version": "2.7.2", "dev": true, "license": "MIT", "dependencies": {"@cloudbase/node-sdk": "2.10.0", "json-bigint": "^1.0.0", "protobufjs": "^6.8.8", "tslib": "^1.9.3"}}, "client/node_modules/xml-name-validator": {"version": "5.0.0", "dev": true, "license": "Apache-2.0", "engines": {"node": ">=18"}}, "client/node_modules/xml2js": {"version": "0.5.0", "dev": true, "license": "MIT", "dependencies": {"sax": ">=0.6.0", "xmlbuilder": "~11.0.0"}, "engines": {"node": ">=4.0.0"}}, "client/node_modules/xmlbuilder": {"version": "11.0.1", "dev": true, "license": "MIT", "engines": {"node": ">=4.0"}}, "client/node_modules/xmlchars": {"version": "2.2.0", "dev": true, "license": "MIT"}, "client/node_modules/xtend": {"version": "4.0.2", "dev": true, "license": "MIT", "engines": {"node": ">=0.4"}}, "client/node_modules/xxhashjs": {"version": "0.2.2", "dev": true, "license": "MIT", "dependencies": {"cuint": "^0.2.2"}}, "client/node_modules/yallist": {"version": "3.1.1", "license": "ISC"}, "client/node_modules/yaml": {"version": "1.10.2", "dev": true, "license": "ISC", "engines": {"node": ">= 6"}}, "client/node_modules/yargs": {"version": "16.2.0", "dev": true, "license": "MIT", "dependencies": {"cliui": "^7.0.2", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.0", "y18n": "^5.0.5", "yargs-parser": "^20.2.2"}, "engines": {"node": ">=10"}}, "client/node_modules/yargs-parser": {"version": "20.2.9", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "client/node_modules/yauzl": {"version": "2.10.0", "dev": true, "license": "MIT", "dependencies": {"buffer-crc32": "~0.2.3", "fd-slicer": "~1.1.0"}}, "client/node_modules/yocto-queue": {"version": "0.1.0", "dev": true, "license": "MIT", "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/@babel/runtime": {"version": "7.27.6", "resolved": "https://registry.npmjs.org/@babel/runtime/-/runtime-7.27.6.tgz", "integrity": "sha512-vbavdySgbTTrmFE+EsiqUTzlOr5bzlnJtUv9PynGCAKvfQqjIXbvFdumPM/GxMDfyuGMJaJAU6TO4zc1Jf1i8Q==", "license": "MIT", "engines": {"node": ">=6.9.0"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "license": "MIT", "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/chalk/node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/cliui": {"version": "8.0.1", "resolved": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "dev": true, "license": "ISC", "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "license": "MIT"}, "node_modules/concurrently": {"version": "8.2.2", "resolved": "https://registry.npmjs.org/concurrently/-/concurrently-8.2.2.tgz", "integrity": "sha512-1dP4gpXFhei8IOtlXRE/T/4H88ElHgTiUzh71YUmtjTEHMSRS2Z/fgOxHSxxusGHogsRfxNq1vyAwxSC+EVyDg==", "dev": true, "license": "MIT", "dependencies": {"chalk": "^4.1.2", "date-fns": "^2.30.0", "lodash": "^4.17.21", "rxjs": "^7.8.1", "shell-quote": "^1.8.1", "spawn-command": "0.0.2", "supports-color": "^8.1.1", "tree-kill": "^1.2.2", "yargs": "^17.7.2"}, "bin": {"conc": "dist/bin/concurrently.js", "concurrently": "dist/bin/concurrently.js"}, "engines": {"node": "^14.13.0 || >=16.0.0"}, "funding": {"url": "https://github.com/open-cli-tools/concurrently?sponsor=1"}}, "node_modules/date-fns": {"version": "2.30.0", "resolved": "https://registry.npmjs.org/date-fns/-/date-fns-2.30.0.tgz", "integrity": "sha512-fnULvOpxnC5/Vg3NCiWelDsLiUc9bRwAPs/+LfTLNvetFCtCTN+yQz15C/fs4AwX1R9K5GLtLfn8QW+dWisaAw==", "dev": true, "license": "MIT", "dependencies": {"@babel/runtime": "^7.21.0"}, "engines": {"node": ">=0.11"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/date-fns"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "license": "MIT"}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "license": "ISC", "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/lodash": {"version": "4.17.21", "resolved": "https://registry.npmjs.org/lodash/-/lodash-4.17.21.tgz", "integrity": "sha512-v2kDEe57lecTulaDIuNTPy3Ry4gLGJ6Z1O3vE1krgXZNrsQ+LFTGHVxVjcXPs17LhbZVGedAJv8XZ1tvj5FvSg==", "license": "MIT"}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/rxjs": {"version": "7.8.2", "resolved": "https://registry.npmjs.org/rxjs/-/rxjs-7.8.2.tgz", "integrity": "sha512-dhKf903U/PQZY6boNNtAGdWbG85WAbjT/1xYoZIC7FAY0yWapOBQVsVrDl58W86//e1VpMNBtRV4MaXfdMySFA==", "dev": true, "license": "Apache-2.0", "dependencies": {"tslib": "^2.1.0"}}, "node_modules/shell-quote": {"version": "1.8.3", "resolved": "https://registry.npmjs.org/shell-quote/-/shell-quote-1.8.3.tgz", "integrity": "sha512-ObmnIF4hXNg1BqhnHmgbDETF8dLPCggZWBjkQfhZpbszZnYur5DUljTcCHii5LC3J5E0yeO/1LIMyH+UvHQgyw==", "devOptional": true, "license": "MIT", "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/spawn-command": {"version": "0.0.2", "resolved": "https://registry.npmjs.org/spawn-command/-/spawn-command-0.0.2.tgz", "integrity": "sha512-zC8zGoGkmc8J9ndvml8Xksr1Amk9qBujgbF0JAIWO7kXr43w0h/0GJNM/Vustixu+YE8N/MTrQ7N31FvHUACxQ==", "dev": true}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/supports-color": {"version": "8.1.1", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==", "dev": true, "license": "MIT", "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/tree-kill": {"version": "1.2.2", "resolved": "https://registry.npmjs.org/tree-kill/-/tree-kill-1.2.2.tgz", "integrity": "sha512-L0Orpi8qGpRG//Nd+H90vFB+3iHnue1zSSGmNOOCh1GLJ7rUKVwV2HvijphGQS2UmhUZewS9VgvxYIdgr+fG1A==", "dev": true, "license": "MIT", "bin": {"tree-kill": "cli.js"}}, "node_modules/tslib": {"version": "2.8.1", "resolved": "https://registry.npmjs.org/tslib/-/tslib-2.8.1.tgz", "integrity": "sha512-oJFu94HQb+KVduSUQL7wnpmqnfmLsOA/nAh6b6EH0wCEoK0/mPeXU6c3wKDV83MkOuHPRHtSXKKU99IBazS/2w==", "dev": true, "license": "0BSD"}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "dev": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "dev": true, "license": "ISC", "engines": {"node": ">=10"}}, "node_modules/yargs": {"version": "17.7.2", "resolved": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "dev": true, "license": "MIT", "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "dev": true, "license": "ISC", "engines": {"node": ">=12"}}, "node_modules/yixintang-admin": {"resolved": "admin", "link": true}, "node_modules/yixintang-miniapp": {"resolved": "client", "link": true}}}