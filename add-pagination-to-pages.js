#!/usr/bin/env node

/**
 * 🎯 页面翻页组件添加脚本
 * 用途: 为所有标准化页面添加翻页组件和相关功能
 * 作者: AI助手
 * 日期: 2025-01-27
 */

const fs = require('fs');
const path = require('path');

// 颜色定义
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

// 翻页HTML模板
const paginationHTML = `
      <!-- 翻页组件 -->
      <div class="pagination-container">
        <div class="pagination-info">
          <span class="total-info">
            共 <span class="highlight-number">{{ totalRecords }}</span> 条记录
          </span>
        </div>

        <div class="pagination-controls">
          <div class="page-size-selector">
            <label class="page-size-label">每页显示：</label>
            <select v-model="pageSize" @change="handlePageSizeChange" class="page-size-select">
              <option value="10">10条</option>
              <option value="20">20条</option>
              <option value="50">50条</option>
            </select>
          </div>

          <div class="page-navigation" v-if="totalRecords > 0">
            <button
              class="page-btn prev-btn"
              @click="prevPage"
              :disabled="currentPage === 1"
              aria-label="上一页">
              ‹ 上一页
            </button>

            <div class="page-numbers">
              <button
                v-for="page in visiblePages"
                :key="page"
                class="page-btn page-number"
                :class="{ active: page === currentPage }"
                @click="goToPage(page)"
                aria-label="页码">
                {{ page }}
              </button>
            </div>

            <button
              class="page-btn next-btn"
              @click="nextPage"
              :disabled="currentPage === totalPages"
              aria-label="下一页">
              下一页 ›
            </button>
          </div>
        </div>
      </div>`;

// 翻页JavaScript方法
const paginationMethods = `
    // 翻页相关方法
    const visiblePages = computed(() => {
      const pages = [];
      const start = Math.max(1, currentPage.value - 2);
      const end = Math.min(totalPages.value, currentPage.value + 2);
      
      for (let i = start; i <= end; i++) {
        pages.push(i);
      }
      
      return pages;
    });
    
    const goToPage = (page) => {
      if (page >= 1 && page <= totalPages.value) {
        currentPage.value = page;
      }
    };
    
    const prevPage = () => {
      if (currentPage.value > 1) {
        currentPage.value--;
      }
    };
    
    const nextPage = () => {
      if (currentPage.value < totalPages.value) {
        currentPage.value++;
      }
    };
    
    const handlePageSizeChange = () => {
      currentPage.value = 1; // 重置到第一页
    };`;

// 翻页CSS样式
const paginationCSS = `
/* 翻页组件样式 */
.pagination-container {
  display: flex !important;
  position: fixed !important;
  right: 50px !important;
  bottom: 20px !important;
  left: 230px !important;
  z-index: var(--service-z-toolbar);
  height: 30px !important;
  margin: 0 !important;
  padding: 4px 12px !important;
  border: 1px solid rgb(139 92 246 / 15%) !important;
  border-radius: 12px !important;
  background: rgb(139 92 246 / 3%) !important;
  box-shadow: 0 4px 16px rgb(139 92 246 / 8%) !important;
  backdrop-filter: blur(20px) !important;
  justify-content: space-between !important;
  align-items: center !important;
  flex-wrap: wrap !important;
  gap: 15px !important;
}

.pagination-info {
  display: flex;
  align-items: center;
}

.total-info {
  font-size: 11px;
  font-weight: 600;
  color: #4f46e5;
  text-shadow: 1px 1px 2px rgb(0 0 0 / 30%);
}

.highlight-number {
  font-size: 12px;
  font-weight: 700;
  color: #7c3aed;
  text-shadow: 1px 1px 2px rgb(0 0 0 / 40%);
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 20px;
  flex-wrap: wrap;
}

.page-size-selector {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-size-label {
  font-size: 10px;
  font-weight: 500;
  color: #6b7280;
}

.page-size-select {
  padding: 2px 6px;
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: #4f46e5;
  font-size: 10px;
  cursor: pointer;
}

.page-navigation {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-btn {
  padding: 4px 8px;
  border: 1px solid rgba(139, 92, 246, 0.2);
  border-radius: 4px;
  background: rgba(255, 255, 255, 0.1);
  color: #4f46e5;
  font-size: 10px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.page-btn:hover:not(:disabled) {
  background: rgba(139, 92, 246, 0.1);
  transform: translateY(-1px);
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-btn.active {
  background: rgba(139, 92, 246, 0.2);
  color: #4f46e5;
  font-weight: 600;
}

.page-numbers {
  display: flex;
  gap: 4px;
}

`;

/**
 * 为页面添加翻页功能
 */
function addPaginationToPage(pageName) {
  console.log(`${colors.blue}🔧 为页面添加翻页功能: ${pageName}${colors.reset}`);
  
  const filePath = path.resolve(`admin/src/views/${pageName}`);
  if (!fs.existsSync(filePath)) {
    console.log(`${colors.red}❌ 文件不存在: ${filePath}${colors.reset}`);
    return false;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 检查是否已经有翻页组件
  if (content.includes('pagination-container')) {
    console.log(`${colors.yellow}⚠️  页面已有翻页组件: ${pageName}${colors.reset}`);
    return true;
  }
  
  // 1. 添加翻页HTML（在表格容器结束后）
  const tableEndPattern = /(\s+)<\/div>\s+<\/div>\s+<\/div>\s+<\/template>/;
  if (tableEndPattern.test(content)) {
    content = content.replace(tableEndPattern, `$1</div>
      </div>
${paginationHTML}
    </div>
  </div>
</template>`);
  }
  
  // 2. 添加翻页方法（在其他方法之后）
  const methodsEndPattern = /(const \w+ = \(\w*\) => \{[^}]+\};)\s+(\/\/ 生命周期|onMounted)/;
  if (methodsEndPattern.test(content)) {
    content = content.replace(methodsEndPattern, `$1
${paginationMethods}
    
    $2`);
  }
  
  // 3. 添加翻页CSS（在响应式设计之前）
  const responsivePattern = /(\/\* 响应式设计 \*\/)/;
  if (responsivePattern.test(content)) {
    content = content.replace(responsivePattern, `${paginationCSS}
$1`);
  }
  
  // 4. 在返回数据中添加翻页方法
  const returnPattern = /(return \{[^}]+)(}\s*;\s*}\s*;\s*<\/script>)/s;
  if (returnPattern.test(content)) {
    content = content.replace(returnPattern, (match, returnContent, ending) => {
      if (!returnContent.includes('visiblePages')) {
        const newReturnContent = returnContent.replace(
          /(totalPages,)/,
          '$1\n      visiblePages,'
        ).replace(
          /(getStatusText)/,
          '$1,\n      goToPage,\n      prevPage,\n      nextPage,\n      handlePageSizeChange'
        );
        return newReturnContent + ending;
      }
      return match;
    });
  }
  
  // 写入文件
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`${colors.green}✅ 翻页功能添加完成: ${pageName}${colors.reset}`);
  
  return true;
}

// 主函数
function main() {
  console.log(`${colors.blue}🚀 页面翻页功能添加工具启动${colors.reset}\n`);
  
  const targetPages = process.argv.slice(2);
  
  if (targetPages.length === 0) {
    console.log(`${colors.yellow}📋 使用方法: node add-pagination-to-pages.js <页面名称>${colors.reset}`);
    console.log(`${colors.cyan}📋 示例: node add-pagination-to-pages.js CustomerManagement.vue AppointmentManagement.vue${colors.reset}`);
    return;
  }
  
  let successCount = 0;
  let totalCount = targetPages.length;
  
  targetPages.forEach(pageName => {
    if (addPaginationToPage(pageName)) {
      successCount++;
    }
  });
  
  console.log(`\n${colors.magenta}📊 翻页功能添加结果统计:${colors.reset}`);
  console.log(`   成功: ${successCount}/${totalCount}`);
  console.log(`   失败: ${totalCount - successCount}/${totalCount}`);
  
  if (successCount === totalCount) {
    console.log(`\n${colors.green}🎉 所有页面翻页功能添加完成！${colors.reset}`);
  } else {
    console.log(`\n${colors.red}❌ 部分页面翻页功能添加失败，请检查错误信息${colors.reset}`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { addPaginationToPage };
