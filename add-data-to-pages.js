#!/usr/bin/env node

/**
 * 🎯 页面数据添加脚本
 * 用途: 为所有标准化页面添加基本的数据展示功能
 * 作者: AI助手
 * 日期: 2025-01-27
 */

const fs = require('fs');
const path = require('path');

// 颜色定义
const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  magenta: '\x1b[35m',
  cyan: '\x1b[36m',
  reset: '\x1b[0m'
};

// 页面数据配置
const PAGE_DATA_CONFIGS = {
  'CustomerManagement.vue': {
    dataName: 'customers',
    sampleData: [
      {
        id: 'C001',
        name: '张小美',
        phone: '13800138001',
        email: '<EMAIL>',
        level: 3,
        points: 1580,
        status: 'active'
      },
      {
        id: 'C002',
        name: '李先生',
        phone: '13800138002',
        email: '<EMAIL>',
        level: 2,
        points: 890,
        status: 'active'
      }
    ]
  },
  'AppointmentManagement.vue': {
    dataName: 'appointments',
    sampleData: [
      {
        id: 'A001',
        customer: '张小美',
        service: '中式按摩',
        therapist: '王师傅',
        time: '2025-01-27 14:00',
        status: 'confirmed'
      },
      {
        id: 'A002',
        customer: '李先生',
        service: '足疗',
        therapist: '李小芳',
        time: '2025-01-27 15:30',
        status: 'pending'
      }
    ]
  },
  'Dashboard.vue': {
    dataName: 'metrics',
    sampleData: [
      {
        id: 'M001',
        metric: '今日营业额',
        today: '¥3,200',
        week: '¥18,500',
        month: '¥85,600',
        trend: 'up'
      },
      {
        id: 'M002',
        metric: '客户数量',
        today: '45',
        week: '312',
        month: '1,256',
        trend: 'up'
      }
    ]
  },
  'FinanceOverview.vue': {
    dataName: 'finances',
    sampleData: [
      {
        id: 'F001',
        item: '服务收入',
        income: '¥85,600',
        expense: '¥0',
        profit: '¥85,600',
        status: 'positive'
      },
      {
        id: 'F002',
        item: '运营成本',
        income: '¥0',
        expense: '¥25,800',
        profit: '-¥25,800',
        status: 'negative'
      }
    ]
  }
};

/**
 * 为页面添加数据展示功能
 */
function addDataToPage(pageName) {
  console.log(`${colors.blue}🔧 为页面添加数据: ${pageName}${colors.reset}`);
  
  const config = PAGE_DATA_CONFIGS[pageName];
  if (!config) {
    console.log(`${colors.yellow}⚠️  跳过页面: ${pageName} (无数据配置)${colors.reset}`);
    return true;
  }
  
  const filePath = path.resolve(`admin/src/views/${pageName}`);
  if (!fs.existsSync(filePath)) {
    console.log(`${colors.red}❌ 文件不存在: ${filePath}${colors.reset}`);
    return false;
  }
  
  let content = fs.readFileSync(filePath, 'utf8');
  
  // 1. 替换空的数据数组
  const emptyDataPattern = /const paginatedData = ref\(\[\]\);/;
  if (emptyDataPattern.test(content)) {
    const dataCode = `// ${config.dataName}数据
    const ${config.dataName} = ref(${JSON.stringify(config.sampleData, null, 6)});
    
    // 计算属性
    const filteredData = computed(() => {
      return ${config.dataName}.value;
    });
    
    const paginatedData = computed(() => {
      const start = (currentPage.value - 1) * pageSize.value;
      const end = start + pageSize.value;
      return filteredData.value.slice(start, end);
    });
    
    const currentPage = ref(1);
    const pageSize = ref(10);
    const totalRecords = computed(() => filteredData.value.length);
    const totalPages = computed(() => Math.ceil(totalRecords.value / pageSize.value));`;
    
    content = content.replace(emptyDataPattern, dataCode);
  }
  
  // 2. 添加基本的数据展示HTML
  const emptyTableBodyPattern = /<!-- 数据行将在这里动态生成 -->\s*<div v-for=".*?" :key=".*?" class="data-row">\s*<!-- 数据行内容将根据具体页面需求实现 -->\s*<\/div>/s;
  
  if (emptyTableBodyPattern.test(content)) {
    const tableBodyCode = `<!-- 数据行 -->
          <div v-for="(item, index) in paginatedData" :key="item.id" class="data-row">
            <div class="data-cell" v-for="(value, key) in item" :key="key" style="flex: 1;">
              <span class="cell-content">{{ value }}</span>
            </div>
          </div>`;
    
    content = content.replace(emptyTableBodyPattern, tableBodyCode);
  }
  
  // 3. 添加基本的CSS样式
  if (!content.includes('.data-cell')) {
    const cssAddition = `
/* 数据展示样式 */
.data-cell {
  display: flex;
  align-items: center;
  padding: 0 8px;
  font-size: 0.9rem;
}

.cell-content {
  color: #1f2937;
  font-weight: 500;
}

/* 响应式设计 */`;
    
    content = content.replace('/* 响应式设计 */', cssAddition);
  }
  
  // 写入文件
  fs.writeFileSync(filePath, content, 'utf8');
  console.log(`${colors.green}✅ 数据添加完成: ${pageName}${colors.reset}`);
  
  return true;
}

// 主函数
function main() {
  console.log(`${colors.blue}🚀 页面数据添加工具启动${colors.reset}\n`);
  
  const targetPages = process.argv.slice(2);
  
  if (targetPages.length === 0) {
    console.log(`${colors.yellow}📋 使用方法: node add-data-to-pages.js <页面名称>${colors.reset}`);
    console.log(`${colors.cyan}📋 可用页面:${colors.reset}`);
    Object.keys(PAGE_DATA_CONFIGS).forEach(page => {
      console.log(`   - ${page}`);
    });
    return;
  }
  
  let successCount = 0;
  let totalCount = targetPages.length;
  
  targetPages.forEach(pageName => {
    if (addDataToPage(pageName)) {
      successCount++;
    }
  });
  
  console.log(`\n${colors.magenta}📊 数据添加结果统计:${colors.reset}`);
  console.log(`   成功: ${successCount}/${totalCount}`);
  console.log(`   失败: ${totalCount - successCount}/${totalCount}`);
  
  if (successCount === totalCount) {
    console.log(`\n${colors.green}🎉 所有页面数据添加完成！${colors.reset}`);
  } else {
    console.log(`\n${colors.red}❌ 部分页面数据添加失败，请检查错误信息${colors.reset}`);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  main();
}

module.exports = { addDataToPage, PAGE_DATA_CONFIGS };
