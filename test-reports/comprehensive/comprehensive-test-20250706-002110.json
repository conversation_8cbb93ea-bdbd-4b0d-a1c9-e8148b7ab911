{"summary": {"type": "comprehensive-system-test", "timestamp": "2025-07-06T00:21:10+08:00", "test_phases": ["backend", "miniprogram", "admin-api", "page-operations"]}, "phases": {"backend": {"status": "completed", "report_location": "server/test-reports/"}, "miniprogram": {"status": "completed", "report_location": "client/test-reports/"}, "admin_api": {"status": "completed", "report_location": "admin/test-reports/"}, "page_operations": {"status": "completed", "report_location": "admin/test-reports/"}}}