# 🖥️ PC端分辨率测试配置

> 管理端专用的PC端多分辨率测试配置，不包含移动端和平板端

## 🎯 **测试目标**

### **管理端定位**
- **目标用户**: 管理员、操作员
- **使用场景**: 办公室、工作站
- **设备类型**: 台式机、笔记本电脑
- **操作方式**: 鼠标+键盘

### **测试重点**
- **桌面端优化**: 针对PC端操作习惯优化
- **多分辨率适配**: 支持常见桌面分辨率
- **自适应缩放**: 根据页面尺寸自动调整缩放比例
- **视觉效果优化**: 在不同分辨率下保持最佳视觉体验
- **键盘导航**: 支持Tab键导航和快捷键
- **鼠标交互**: 优化鼠标悬停和点击体验

## 📊 **PC端分辨率矩阵**

### 🖥️ **支持的桌面分辨率**
```javascript
const pcResolutions = [
  // 最小支持分辨率
  {
    name: '1024x768-MinDesktop',
    width: 1024,
    height: 768,
    deviceScaleFactor: 1,
    description: '最小支持分辨率，边界测试'
  },

  // 标准笔记本分辨率
  {
    name: '1366x768-Laptop',
    width: 1366,
    height: 768,
    deviceScaleFactor: 1,
    description: '标准笔记本，主要测试目标'
  },

  // 1080P桌面显示器
  {
    name: '1920x1080-Desktop',
    width: 1920,
    height: 1080,
    deviceScaleFactor: 1,
    description: '1080P显示器，主要测试目标'
  },

  // 2K高分辨率显示器
  {
    name: '2560x1440-2K',
    width: 2560,
    height: 1440,
    deviceScaleFactor: 1,
    description: '2K显示器，高分辨率测试'
  },

  // 4K超高分辨率显示器
  {
    name: '3840x2160-4K',
    width: 3840,
    height: 2160,
    deviceScaleFactor: 1,
    description: '4K显示器，超高分辨率测试'
  },


];
```

## 🎨 **CSS断点规范**

### **PC端媒体查询标准**
```css
/* 最小支持分辨率 */
@media (min-width: 1024px) {
  /* 基础桌面端样式 */
  .container {
    min-width: 1000px;
    padding: 20px;
  }
}

/* 小屏桌面端 */
@media (min-width: 1024px) and (max-width: 1365px) {
  .container {
    padding: 15px;
  }
  
  .table-wrapper {
    min-width: 1000px;
    overflow-x: auto;
  }
}

/* 标准桌面端 */
@media (min-width: 1366px) and (max-width: 1919px) {
  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }
}

/* 大屏桌面端 */
@media (min-width: 1920px) {
  .container {
    max-width: 1600px;
    margin: 0 auto;
    padding: 25px;
  }
}

/* 4K显示器 */
@media (min-width: 3840px) {
  .container {
    max-width: 1800px;
    margin: 0 auto;
    padding: 30px;
  }

  /* 4K下的字体优化 */
  body {
    font-size: 16px;
    line-height: 1.6;
  }
}


```

## 🔧 **测试配置**

### **Playwright配置**
```javascript
// playwright.config.js
const pcTestConfig = {
  projects: [
    {
      name: 'PC-MinDesktop',
      use: {
        viewport: { width: 1024, height: 768 }
      }
    },
    {
      name: 'PC-Laptop',
      use: {
        viewport: { width: 1366, height: 768 }
      }
    },
    {
      name: 'PC-Desktop',
      use: {
        viewport: { width: 1920, height: 1080 }
      }
    },
    {
      name: 'PC-2K',
      use: {
        viewport: { width: 2560, height: 1440 }
      }
    },
    {
      name: 'PC-4K',
      use: {
        viewport: { width: 3840, height: 2160 }
      }
    },

  ]
};
```

### **Puppeteer配置**
```javascript
// puppeteer测试配置
const puppeteerPcConfig = {
  headless: false,
  args: ['--no-sandbox', '--disable-setuid-sandbox'],
  defaultViewport: null, // 使用实际窗口大小

  // PC端分辨率测试
  resolutions: [
    { width: 1024, height: 768 },
    { width: 1366, height: 768 },
    { width: 1920, height: 1080 },
    { width: 2560, height: 1440 },
    { width: 3840, height: 2160 }
  ]
};
```

## 📋 **测试检查项**

### **PC端特有检查项**
- [ ] **鼠标悬停效果**: 按钮、链接的hover状态
- [ ] **键盘导航**: Tab键顺序、焦点样式
- [ ] **右键菜单**: 上下文菜单支持
- [ ] **拖拽操作**: 表格列宽调整、文件拖拽
- [ ] **滚动条样式**: 自定义滚动条在不同分辨率下的表现
- [ ] **工具提示**: tooltip在边界位置的显示
- [ ] **快捷键**: Ctrl+S保存、Ctrl+F搜索等

### **布局检查项**
- [ ] **最小宽度**: 1024px下内容不被截断
- [ ] **最大宽度**: 大屏幕下内容居中显示
- [ ] **表格适配**: 不同分辨率下表格列宽合理
- [ ] **模态框居中**: 弹窗在各分辨率下居中显示
- [ ] **侧边栏宽度**: 固定宽度在不同分辨率下的表现

## 🚨 **测试失败标准**

### **严重问题 (必须修复)**
- 1024px下出现水平滚动条
- 关键功能按钮不可见
- 表格内容完全无法查看
- 模态框超出屏幕边界

### **中等问题 (建议修复)**
- 文字过小影响阅读
- 按钮间距过小影响点击
- 表格列宽不合理
- 工具提示位置不当

### **轻微问题 (优化建议)**
- 视觉效果可以更好
- 间距可以更优化
- 动画效果可以改进

## 📊 **测试报告模板**

### **PC端测试结果**
```
测试时间: 2025-01-19 10:00:00
测试范围: PC端4种分辨率
总测试用例: 672
通过用例: XXX
失败用例: XXX
通过率: XX.X%

分辨率测试结果:
- 1024x768: ✅ 通过 (XXX/XXX)
- 1366x768: ✅ 通过 (XXX/XXX)  
- 1920x1080: ✅ 通过 (XXX/XXX)
- 2560x1440: ⚠️ 部分通过 (XXX/XXX)
```

---

**专注PC端，确保管理系统在所有桌面环境下完美运行！** 🖥️
