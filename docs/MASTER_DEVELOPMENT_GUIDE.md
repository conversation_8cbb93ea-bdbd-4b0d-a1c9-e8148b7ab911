# 🎯 壹心堂开发主指南 v6.0

> **📋 文档目的**: 壹心堂管理系统开发的统一权威指南
> **🔄 更新日期**: 2025-01-21
> **🎯 基于**: 100%完整环境配置 + 现成工具集成 + 轮廓调试标准规则v4.0
> **👥 适用**: AI助手、开发团队、新成员入职

## 🚨 核心原则

### ⚡ 三大强制原则
1. **现成工具优先** - 使用成熟的现成工具，避免重复造轮子
2. **自动化优先** - 所有检查和验证都自动化执行
3. **标准化优先** - 严格遵循CSS标准库和组件模板

### 🔴 绝对禁止
❌ 跳过Context 7和memory-server查询
❌ 不使用CSS标准变量
❌ 批量修改文件
❌ 跳过自动化检查
❌ 重复造轮子

## 🛠️ 完整工具生态（100%已配置）

### ✅ 开发环境工具
| 工具 | 状态 | 功能 | 使用方式 |
|------|------|------|----------|
| **IntelliJ IDEA + Stylelint插件** | ✅ 已配置 | 实时CSS检查 | 自动提示错误 |
| **Chrome Debug CSS扩展** | ✅ 已安装 | 一键轮廓调试 | 点击扩展图标 |
| **Chrome Web Developer扩展** | ✅ 已安装 | 综合开发工具 | 工具栏菜单 |
| **CSS标准库** | ✅ 已创建 | 统一样式变量 | `var(--row-height)` |
| **标准组件模板** | ✅ 已创建 | 预制组件 | `<StandardDataRow>` |
| **Git Hooks** | ✅ 已配置 | 提交前检查 | 自动执行 |

### ✅ 可用命令
```bash
npm run stylelint          # 自动修复CSS问题
npm run stylelint-check     # 检查CSS标准合规性
npm run outline-check       # 轮廓调试检查
npm run pre-commit          # 手动运行提交前检查
./scripts/dev-setup.sh      # 一键环境配置（已完成）
```

## 📋 标准开发流程（简化版）

### 🔍 阶段1: 信息收集（强制）
```javascript
1. Context 7查询 - 查找相关代码和示例
2. memory-server查询 - 搜索历史经验
3. Sequential thinking分析 - 制定技术方案
```

### 🔧 阶段2: 标准化开发
```vue
<!-- 使用标准组件模板 -->
<template>
  <StandardDataRow>
    <StandardDataCell align="center">
      内容
    </StandardDataCell>
  </StandardDataRow>
</template>

<style scoped>
/* 使用CSS标准变量 */
.component {
  height: var(--row-height);
  padding: var(--cell-padding);
  color: var(--primary-color);
}
</style>
```

### ✅ 阶段3: 自动化验证
```bash
# IDEA中实时检查 - Stylelint自动提示
# Chrome中调试 - Debug CSS扩展轮廓调试
# 提交时检查 - Git Hooks自动验证
git commit -m "feat: 新功能"  # 自动触发6项检查
```

## 🎯 CSS标准库使用指南

### 📏 标准尺寸变量
```css
/* 数据行标准 */
height: var(--row-height);        /* 50px - 与菜单项对齐 */
padding: var(--cell-padding);     /* 0 8px - 防止边界超出 */
font-size: var(--cell-font-size); /* 0.9rem - 适应50px高度 */
line-height: var(--cell-line-height); /* 1.2 - 紧凑行高 */

/* 服务图片标准 */
width: var(--service-image-width);   /* 55px */
height: var(--service-image-height); /* 20px */

/* 壹心堂色彩标准 */
color: var(--primary-color);      /* #8B5CF6 - 主紫色 */
background: var(--secondary-color); /* #A78BFA - 浅紫色 */
```

### 🧩 标准组件使用
```vue
<script setup>
import StandardDataRow from '@/components/standards/StandardDataRow.vue'
import StandardDataCell from '@/components/standards/StandardDataCell.vue'
</script>

<template>
  <!-- 标准数据行 - 自动50px高度，防止超出 -->
  <StandardDataRow :debug="false">
    <!-- 标准数据单元格 - 自动对齐，标准内边距 -->
    <StandardDataCell align="center">
      {{ content }}
    </StandardDataCell>
  </StandardDataRow>
</template>
```

## 🎯 轮廓调试使用指南

### 🌐 Chrome扩展调试
```javascript
// 方法1: Debug CSS扩展（推荐）
1. 点击Chrome工具栏的Debug CSS图标
2. 所有元素自动显示彩色轮廓
3. 一键开启/关闭

// 方法2: Web Developer扩展
1. 点击Web Developer工具栏
2. Outline → Outline All Elements
3. 选择不同的轮廓选项
```

### 🔧 IDEA中实时调试
```vue
<!-- 开发时启用调试模式 -->
<StandardDataRow :debug="true">
  <!-- 会显示紫色轮廓和标签 -->
</StandardDataRow>
```

### 🤖 自动化轮廓检查
```bash
# 启动开发服务器
npm run dev

# 运行自动化轮廓调试检查
npm run outline-check

# 查看检查报告
ls reports/outline-check-*.json
```

## 🚀 部署流程

### ✅ 部署前检查
```bash
# 1. 运行完整质量检查
npm run pre-commit

# 2. 构建和预览
npm run build
npm run preview

# 3. 最终验证
npm run outline-check
```

### 🌐 微信云托管部署
```bash
# 推送代码触发自动部署
git push origin main

# 监控部署状态
# 验证部署结果
```

## 🔍 故障排除

### 常见问题快速解决
```bash
# Stylelint检查失败
npm run stylelint  # 自动修复

# Git提交被阻止
# 查看错误信息，修复问题后重新提交

# 轮廓调试检查失败
# 使用Chrome Debug CSS扩展手动检查
# 调整CSS使用标准变量
```

## 📚 相关文档

### 📖 详细文档
- **CSS标准详情**: `docs/standards/CSS_STANDARDS.md`
- **组件标准详情**: `docs/standards/COMPONENT_STANDARDS.md`
- **轮廓调试详情**: `docs/standards/OUTLINE_DEBUG_STANDARDS.md`
- **MCP配置详情**: `docs/MCP_CONFIGURATION_GUIDE.md`
- **MCP缺失检查**: `config/mcp-missing-checklist.json`
- **部署详情**: `docs/workflows/DEPLOYMENT_WORKFLOW.md`
- **工具详情**: `docs/tools/RECOMMENDED_TOOLS.md`

### 🗂️ 文档结构
```
docs/
├── MASTER_DEVELOPMENT_GUIDE.md (本文档 - 主入口)
├── standards/ (标准规范)
├── workflows/ (工作流程)
├── tools/ (工具指南)
└── archive/ (归档文档)
```

## 🏆 成功标准

### ✅ 质量指标
- **CSS标准合规率**: 100%
- **Git Hooks通过率**: 100%
- **轮廓调试成功率**: 95%+
- **多分辨率兼容性**: 5种分辨率100%支持

### 🎯 开发效率
- **工具配置时间**: 从4-5周缩短到1天
- **问题发现前移**: 90%问题在开发阶段发现
- **代码质量**: 显著提升，自动化保证

## 🚀 快速开始

### 🔧 新成员入职
1. **确认环境**: 检查IDEA插件和Chrome扩展已安装
2. **熟悉工具**: 测试Stylelint和Debug CSS功能
3. **阅读标准**: 了解CSS变量和组件模板
4. **实践流程**: 完成一个小功能的完整开发流程

### 📋 日常开发
1. **开始开发**: Context 7查询 → memory-server查询 → 编码
2. **实时检查**: IDEA中观察Stylelint提示
3. **调试验证**: Chrome中使用Debug CSS扩展
4. **提交代码**: Git Hooks自动验证质量

**这是壹心堂开发的统一权威指南，基于100%完整配置的现成工具，确保高效、高质量的开发体验！** 🎯✅
