# 壹心堂中医推拿管理系统 - 系统架构文档

## 🏗️ 系统架构概览

### 技术栈
- **前端**: Vue 3 + Ant Design Vue + Vite
- **后端**: Django 3.2 + Django REST Framework
- **数据库**: MySQL 8.0 (腾讯云CynosDB)
- **部署**: 微信云托管 + Docker
- **测试**: Puppeteer + 自研完美主义测试框架

### 架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   前端管理后台   │    │   Django后端    │    │   MySQL数据库   │
│   Vue 3 + AntD  │◄──►│   REST API      │◄──►│   腾讯云CynosDB │
│   Port: 3001    │    │   Port: 8000    │    │   Port: 3306    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         │              ┌─────────────────┐              │
         └──────────────►│   微信云托管     │◄─────────────┘
                        │   Docker部署    │
                        └─────────────────┘
```

## 📁 项目结构

### 前端结构 (admin/)
```
admin/
├── src/
│   ├── components/          # 组件库
│   │   ├── Layout/         # 布局组件
│   │   ├── TestConsole.vue # 测试控制台
│   │   ├── AutoTestStatus.vue # 自动化测试状态
│   │   └── PerfectionistStatus.vue # 完美主义测试状态
│   ├── views/              # 页面视图
│   │   ├── DashboardView.vue
│   │   ├── AppointmentListView.vue
│   │   ├── CustomerListView.vue
│   │   └── ...
│   ├── utils/              # 工具库
│   │   ├── autoTest.js     # 基础自动化测试
│   │   ├── comprehensiveTest.js # 完善测试工具
│   │   ├── autoTestScheduler.js # 自动化测试调度器
│   │   ├── perfectionist.js # 完美主义测试器
│   │   └── realTimeFixer.js # 实时修复工具
│   ├── api/                # API接口
│   ├── store/              # 状态管理
│   └── router/             # 路由配置
├── scripts/                # 脚本文件
│   └── runAutoTests.js     # Puppeteer自动化测试脚本
└── public/                 # 静态资源
```

### 后端结构 (server/)
```
server/
├── wxcloudrun/             # Django主应用
│   ├── models.py          # 数据模型
│   ├── views.py           # 视图函数
│   ├── urls.py            # URL路由
│   └── settings.py        # 配置文件
├── api/                   # API应用
│   ├── views.py          # API视图
│   ├── serializers.py    # 序列化器
│   └── urls.py           # API路由
├── create_test_data.py   # 测试数据创建脚本
└── requirements.txt      # Python依赖
```

## 🔧 核心功能模块

### 1. 用户管理
- 客户信息管理
- 技师信息管理
- 权限控制

### 2. 预约管理
- 预约创建和编辑
- 预约日历视图
- 预约状态跟踪

### 3. 服务管理
- 服务项目配置
- 价格管理
- 服务分类

### 4. 财务管理
- 收支记录
- 财务报表
- 统计分析

### 5. 系统管理
- 系统设置
- 操作日志
- 版本管理

## 🧪 测试架构

### 测试层级
1. **基础自动化测试** (autoTest.js)
   - API连接测试
   - Store状态测试
   - 路由导航测试

2. **完善测试工具** (comprehensiveTest.js)
   - 用户工作流程测试
   - 业务逻辑测试
   - 集成场景测试

3. **完美主义测试器** (perfectionist.js)
   - 实时问题检测
   - 自动修复机制
   - 完美度评分

4. **浏览器自动化测试** (Puppeteer)
   - 真实用户操作模拟
   - 跨浏览器兼容性测试
   - 性能监控

### 测试覆盖率要求
- API测试: 100%
- 前端操作测试: 95%+
- 用户工作流程: 100%
- 完美度要求: 95%+

## 🚀 部署架构

### 开发环境
- 前端: `npm run dev` (localhost:3001)
- 后端: `python manage.py runserver` (localhost:8000)
- 数据库: 腾讯云测试数据库

### 生产环境
- 部署平台: 微信云托管
- 容器化: Docker
- 数据库: 腾讯云CynosDB生产环境
- 域名: 微信云托管提供的域名

### CI/CD流程
1. 代码提交到main分支
2. 自动触发完美主义测试
3. 测试通过后自动构建Docker镜像
4. 部署到微信云托管
5. 自动更新版本号

## 📊 监控和日志

### 前端监控
- 实时错误监控
- 性能监控
- 用户行为分析

### 后端监控
- API响应时间监控
- 数据库性能监控
- 系统资源监控

### 日志系统
- 操作日志记录
- 错误日志收集
- 性能日志分析

## 🔒 安全架构

### 数据安全
- 数据库连接加密
- 敏感信息脱敏
- 数据备份策略

### 接口安全
- CSRF保护
- 请求频率限制
- 输入验证

### 部署安全
- HTTPS强制
- 容器安全扫描
- 访问控制

## 📈 性能优化

### 前端优化
- 代码分割
- 懒加载
- 缓存策略

### 后端优化
- 数据库查询优化
- 缓存机制
- 异步处理

### 数据库优化
- 索引优化
- 查询优化
- 连接池配置

## 🔄 版本管理

### 版本策略
- 语义化版本控制
- 自动版本递增
- 版本历史记录

### 发布流程
1. 功能开发完成
2. 完美主义测试通过
3. 代码审查
4. 合并到main分支
5. 自动部署
6. 版本号递增

## 📚 技术文档

### 开发文档
- [API文档](./API.md)
- [前端组件文档](./FRONTEND.md)
- [数据库设计文档](./DATABASE.md)

### 运维文档
- [部署指南](./DEPLOYMENT.md)
- [监控指南](./MONITORING.md)
- [故障排查指南](./TROUBLESHOOTING.md)

---

**文档版本**: v1.0  
**最后更新**: 2025-07-05  
**维护者**: CTO级别完美主义团队
