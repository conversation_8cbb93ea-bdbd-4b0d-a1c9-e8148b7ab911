# 怡心堂中医理疗管理系统

## 🚀 一键启动

**智能启动，自动适配所有环境！**

```bash
# 1. 进入服务器目录
cd server

# 2. 一键智能启动
python3 start.py
```

系统会自动：
- 🔍 **检测运行环境**（开发/生产）
- 🗄️ **验证数据库连接**（内网/外网自动切换）
- 🚀 **选择最佳启动方式**（Gunicorn/Django runserver）
- ⚡ **即时启动服务**，无需复杂配置

> 💡 **提示**: 系统优先判断生产环境，如果不是则自动切换到开发环境

## 项目简介

这是一个基于现代技术栈开发的中医理疗管理系统，包含小程序、管理后台和后端服务。系统提供完整的中医理疗服务预约、管理和健康档案维护功能，帮助中医理疗机构提升服务质量和管理效率。

## 项目结构

项目主要由三部分组成：

1. `client` - 前端小程序（基于Taro框架开发）
2. `admin` - 后台管理系统（基于Vue 3 + Ant Design Vue开发）
3. `server` - 后端服务（基于Django + Django REST Framework开发）

## 功能特性

### 小程序端

- 用户登录与注册
- 预约推拿服务
- 查看和管理个人预约
- 浏览健康小贴士
- 技师选择和评价
- 健康评估工具
- 会员中心和个人档案

### 管理后台

- 预约管理
- 客户管理
- 服务项目管理
- 技师管理
- 健康小贴士管理
- 财务统计

## 技术栈

### 前端小程序（Taro）

- **框架**：Taro（跨端开发框架）
- **UI组件库**：Taro UI
- **状态管理**：Taro内置状态管理
- **样式预处理**：Scss
- **响应式单位**：rpx

### 后台管理系统（Vue 3）

- **框架**：Vue 3
- **UI组件库**：Ant Design Vue
- **状态管理**：Pinia
- **路由**：Vue Router
- **构建工具**：Vite
- **HTTP请求**：Axios
- **图表**：ECharts

### 后端服务（Django）

- **开发语言**：Python 3
- **后端框架**：Django + Django REST Framework
- **数据库**：MySQL 5.7（支持开发/生产环境分离）
- **缓存**：Redis（可选）
- **服务端口**：8000（开发）/ 80（生产）

## 数据库环境

### 数据库配置说明

项目使用微信云托管MySQL数据库，支持开发和生产环境分离：

| 环境 | 数据库名 | 连接地址 | 端口 | 说明 |
|------|----------|----------|------|------|
| 开发环境 | `wechatcloud_dev` | `sh-cynosdbmysql-grp-l681flue.sql.tencentcdb.com` | 25524 | 外网地址，用于本地开发 |
| 生产环境 | `wechatcloud_prod` | `*************` | 3306 | 内网地址，用于生产部署 |

**数据库凭据**：
- 用户名：`root`
- 密码：`Yixintang2025`

**自动切换机制**：
- 当 `DEBUG=True` 时，自动使用开发环境配置
- 当 `DEBUG=False` 时，自动使用生产环境配置

## 开发环境

### 系统要求

- Node.js 16+
- Python 3.9+
- MySQL 5.7+（已配置云数据库）
- 微信开发者工具
- VSCode或其他IDE

### 环境配置

1. 安装Node.js和npm
2. 安装Python 3.9及以上版本
3. 安装MySQL 5.7及以上版本
4. 安装微信开发者工具
5. 配置Python虚拟环境

## 🚀 快速开始

### 一键启动（推荐）

系统提供智能启动脚本，自动检测环境并选择最佳启动方式：

```bash
# 1. 克隆项目
git clone <repository-url>
cd wechatcloud

# 2. 安装后端依赖
cd server
pip install -r requirements.txt

# 3. 配置环境（可选，有默认配置）
cp .env.example .env
# 编辑.env文件设置数据库等配置

# 4. 一键智能启动
python3 start.py
```

**启动脚本会自动：**
- 🔍 检测是否为生产环境（优先判断）
- 🗄️ 验证数据库连接状态
- ⚙️ 选择合适的启动命令：
  - 生产环境 → `gunicorn`（高性能）
  - 开发环境 → `python manage.py runserver`（热重载）

### 启动脚本选项

| 脚本 | 特点 | 使用场景 |
|------|------|----------|
| `python3 start.py` | 快速启动，简洁输出 | 日常开发和生产部署 |
| `python3 auto_start.py` | 详细检测，交互选择 | 环境诊断和调试 |
| `./start.sh` | Shell版本，跨平台 | 容器部署和CI/CD |

### 环境强制指定

```bash
# 强制开发环境
DEBUG=True python3 start.py

# 强制生产环境
DEBUG=False PROD_DB_NAME=wechatcloud_prod python3 start.py
```

## 开发指南

### 准备工作

1. 安装依赖

```bash
# 安装根目录依赖
npm install

# 安装小程序依赖
cd client
npm install

# 安装管理后台依赖
cd ../admin
npm install

# 安装后端服务依赖（Python）
cd ../server
pip install -r requirements.txt
```

2. 配置环境

**数据库配置**

系统支持开发和生产环境的数据库分离：

- **开发环境**：使用外网地址 `sh-cynosdbmysql-grp-l681flue.sql.tencentcdb.com:25524`
- **生产环境**：使用内网地址 `*************:3306`
- **数据库分离**：开发环境使用 `wechatcloud_dev`，生产环境使用 `wechatcloud_prod`

配置步骤：

```bash
# 复制环境变量配置文件
cd server
cp .env.example .env

# 编辑.env文件，配置数据库连接
# 开发环境示例：
DEBUG=True
DB_NAME=wechatcloud_dev
DB_USER=root
DB_PASSWORD=Yixintang2025
DB_HOST=sh-cynosdbmysql-grp-l681flue.sql.tencentcdb.com
DB_PORT=25524

# 运行数据库迁移
python manage.py migrate

# 创建测试数据（可选）
python create_test_data.py
```

**其他配置**

- 在 `client/config` 目录下修改 `dev.js` 和 `prod.js` 的配置
- 配置微信小程序AppID和AppSecret

### 本地开发

1. 启动后端服务

**推荐方式 - 一键智能启动：**
```bash
cd server
python3 start.py  # 自动检测环境，选择最佳启动方式
```

**传统方式：**
```bash
# 手动启动Django开发服务器
cd server
python manage.py runserver 0.0.0.0:8000

# 或者启动Mock服务器（开发阶段）
npm run mock-server
```

**启动验证：**
```bash
# 检查服务状态
curl http://localhost:8000/health/

# 查看API文档
open http://localhost:8000/swagger/
```

2. 开发小程序

```bash
# 启动Taro小程序开发
cd client
npm run dev:weapp
```

然后使用微信开发者工具打开 `client/dist` 目录。

3. 开发管理后台

```bash
# 启动Vue管理后台开发
cd admin
npm run dev
```

访问 http://localhost:3000 查看管理后台。

### 部署

#### 生产环境部署

1. **配置生产环境变量**

```bash
# 在生产服务器上配置.env文件
DEBUG=False
DB_NAME=wechatcloud_prod
DB_USER=root
DB_PASSWORD=Yixintang2025
DB_HOST=*************
DB_PORT=3306
ALLOWED_HOSTS=your-domain.com,your-ip-address
```

2. **部署后端服务**

**推荐方式 - 一键智能启动：**
```bash
cd server
pip install -r requirements.txt
python manage.py migrate
python manage.py collectstatic --noinput

# 一键启动（自动检测为生产环境）
python3 start.py
```

**Docker部署：**
```bash
cd server
docker build -t wechatcloud-server .
docker run -p 80:8000 \
  -e DEBUG=False \
  -e PROD_DB_HOST=************* \
  -e PROD_DB_NAME=wechatcloud_prod \
  wechatcloud-server
```

**传统Gunicorn部署：**
```bash
pip install -r requirements.txt gunicorn
python manage.py migrate
python manage.py collectstatic --noinput
gunicorn core.wsgi:application --bind 0.0.0.0:8000 --workers 4
```

3. **部署管理后台**

```bash
cd admin
npm run build
# 将dist目录部署到Web服务器（如Nginx）
```

4. **部署小程序**

```bash
cd client
npm run build:weapp
# 使用微信开发者工具上传代码
```

#### 环境切换说明

系统会根据 `DEBUG` 环境变量自动切换数据库配置：

- **开发环境** (`DEBUG=True`)：
  - 数据库：`wechatcloud_dev`
  - 地址：`sh-cynosdbmysql-grp-l681flue.sql.tencentcdb.com:25524`（外网）
  - 用途：本地开发、测试

- **生产环境** (`DEBUG=False`)：
  - 数据库：`wechatcloud_prod`
  - 地址：`*************:3306`（内网）
  - 用途：正式部署、生产服务

## 项目进度

### 已完成

- [x] 项目基础架构搭建
- [x] 用户登录/注册云函数
- [x] 小程序基本页面布局
- [x] 健康小贴士模块
- [x] 图片上传组件
- [x] 隐私协议弹窗组件
- [x] 云存储工具类
- [x] 管理后台基础架构
- [x] 健康小贴士管理页面
- [x] 客户管理页面
- [x] 预约功能（预约页面和云函数）
- [x] 服务项目管理（管理后台和小程序端）

### 待开发

- [ ] 技师管理
- [ ] 财务统计
- [ ] 健康档案管理
- [ ] 完善小程序页面

## 开发规范

请查看 `.cursor/rules.md` 文件了解完整的开发规范。

## 开发规范

请严格遵循项目根目录下 `.cursor/rules.md` 中的开发规范，包括：

- **前端小程序**：使用Taro框架，组件化开发，统一使用rpx单位
- **后台管理**：使用Vue 3 Composition API + `<script setup>`语法
- **后端服务**：遵循Django最佳实践，使用Python 3类型注解
- **代码风格**：统一的命名规范和代码格式
- **测试规范**：使用Playwright进行自动化测试

## 联系方式

如有问题或建议，请查看开发规范文档或提交Issue。