# 怡心堂中医理疗管理系统 - Git 分支管理工作流程

## 🌳 分支策略概述

本项目采用 **Git Flow** 分支管理策略，确保代码质量和部署安全：

```
main (生产分支)     ←── 稳定的生产代码
  ↑
develop (开发分支)  ←── 最新的开发代码
  ↑
feature/* (功能分支) ←── 新功能开发
hotfix/* (热修复分支) ←── 紧急修复
```

## 📋 分支说明

### 1. main 分支 (生产分支)
- **用途**: 生产环境部署分支
- **特点**: 
  - 只包含经过充分测试的稳定代码
  - 每次合并都对应一个生产版本发布
  - 受保护，只能通过 Pull Request 合并
  - 自动触发生产环境部署

### 2. develop 分支 (开发分支)
- **用途**: 开发环境集成分支
- **特点**:
  - 包含最新的开发功能
  - 所有新功能首先合并到此分支
  - 用于开发环境测试和集成
  - 定期合并到 main 分支

### 3. feature/* 分支 (功能分支)
- **用途**: 新功能开发
- **命名规范**: `feature/功能描述`
- **生命周期**: 从 develop 分支创建，完成后合并回 develop

### 4. hotfix/* 分支 (热修复分支)
- **用途**: 生产环境紧急修复
- **命名规范**: `hotfix/修复描述`
- **生命周期**: 从 main 分支创建，修复后同时合并到 main 和 develop

## 🔄 工作流程

### 日常开发流程

#### 1. 开始新功能开发
```bash
# 切换到开发分支并更新
git checkout develop
git pull origin develop

# 创建新功能分支
git checkout -b feature/用户认证系统
```

#### 2. 开发过程中
```bash
# 定期提交代码
git add .
git commit -m "feat: 添加用户登录功能"

# 定期推送到远程分支
git push origin feature/用户认证系统
```

#### 3. 功能完成后
```bash
# 确保代码最新
git checkout develop
git pull origin develop

# 合并最新的 develop 到功能分支
git checkout feature/用户认证系统
git merge develop

# 解决冲突（如有）
# 运行测试确保功能正常
npm test  # 或其他测试命令

# 推送更新
git push origin feature/用户认证系统
```

#### 4. 创建 Pull Request
- 在 GitHub 上创建从 `feature/用户认证系统` 到 `develop` 的 PR
- 填写详细的功能描述和测试说明
- 等待代码审查和测试通过
- 合并到 develop 分支

### 生产发布流程

#### 1. 准备发布
```bash
# 确保 develop 分支代码稳定
git checkout develop
git pull origin develop

# 运行完整测试套件
npm run test:full
python manage.py test  # 后端测试
```

#### 2. 合并到生产分支
```bash
# 切换到 main 分支
git checkout main
git pull origin main

# 创建发布 PR 或直接合并（根据团队规定）
git merge develop

# 推送到生产分支
git push origin main
```

#### 3. 生产部署
```bash
# 自动触发生产环境部署
# 或手动执行部署脚本
./deploy.sh --deploy --push
```

### 热修复流程

#### 1. 创建热修复分支
```bash
# 从 main 分支创建热修复分支
git checkout main
git pull origin main
git checkout -b hotfix/修复支付bug
```

#### 2. 修复问题
```bash
# 修复代码
# 运行测试确保修复有效
git add .
git commit -m "hotfix: 修复支付接口超时问题"
```

#### 3. 合并到两个分支
```bash
# 合并到 main 分支
git checkout main
git merge hotfix/修复支付bug
git push origin main

# 合并到 develop 分支
git checkout develop
git merge hotfix/修复支付bug
git push origin develop

# 删除热修复分支
git branch -d hotfix/修复支付bug
git push origin --delete hotfix/修复支付bug
```

## 📝 提交规范

### Commit Message 格式
```
<type>(<scope>): <subject>

<body>

<footer>
```

### Type 类型
- `feat`: 新功能
- `fix`: 修复bug
- `docs`: 文档更新
- `style`: 代码格式调整
- `refactor`: 代码重构
- `test`: 测试相关
- `chore`: 构建过程或辅助工具的变动

### 示例
```bash
git commit -m "feat(auth): 添加微信登录功能

- 集成微信 OAuth 2.0 认证
- 添加用户信息自动同步
- 支持手机号绑定

Closes #123"
```

## 🛡️ 分支保护规则

### main 分支保护
- ✅ 禁止直接推送
- ✅ 要求 Pull Request 审查
- ✅ 要求状态检查通过
- ✅ 要求分支为最新状态
- ✅ 限制推送权限

### develop 分支保护
- ✅ 要求 Pull Request 审查
- ✅ 要求状态检查通过
- ✅ 允许管理员绕过限制

## 🔧 Git 配置

### 全局配置
```bash
# 设置用户信息
git config --global user.name "Your Name"
git config --global user.email "<EMAIL>"

# 设置默认编辑器
git config --global core.editor "code --wait"

# 设置默认分支名
git config --global init.defaultBranch main

# 启用颜色输出
git config --global color.ui auto
```

### 项目配置
```bash
# 设置上游分支自动跟踪
git config branch.autosetupmerge always
git config branch.autosetuprebase always

# 设置推送策略
git config push.default simple
```

## 🚀 自动化工具

### 1. Git Hooks
```bash
# 安装 pre-commit 钩子
cp scripts/pre-commit .git/hooks/
chmod +x .git/hooks/pre-commit
```

### 2. GitHub Actions
- **CI/CD 流水线**: 自动测试和部署
- **代码质量检查**: ESLint, Prettier, 代码覆盖率
- **安全扫描**: 依赖漏洞检查

### 3. 分支管理脚本
```bash
# 快速切换到开发分支
alias gdev="git checkout develop && git pull origin develop"

# 快速切换到生产分支
alias gmain="git checkout main && git pull origin main"

# 创建新功能分支
function gnew() {
    git checkout develop
    git pull origin develop
    git checkout -b "feature/$1"
}
```

## 📊 工作流程图

```mermaid
graph TD
    A[开始开发] --> B[切换到 develop 分支]
    B --> C[创建 feature 分支]
    C --> D[开发新功能]
    D --> E[提交代码]
    E --> F[推送到远程]
    F --> G[创建 Pull Request]
    G --> H[代码审查]
    H --> I{审查通过?}
    I -->|否| D
    I -->|是| J[合并到 develop]
    J --> K[删除 feature 分支]
    K --> L{准备发布?}
    L -->|否| A
    L -->|是| M[合并到 main]
    M --> N[生产部署]
    N --> O[发布完成]
```

## 🎯 最佳实践

### 1. 代码提交
- **小而频繁**: 每个提交只包含一个逻辑变更
- **清晰描述**: 提交信息要清楚说明变更内容
- **测试通过**: 提交前确保所有测试通过

### 2. 分支管理
- **及时清理**: 合并后及时删除功能分支
- **保持同步**: 定期从 develop 分支更新功能分支
- **避免长期分支**: 功能分支生命周期不超过一周

### 3. 代码审查
- **必须审查**: 所有代码变更都要经过审查
- **及时响应**: 24小时内完成代码审查
- **建设性反馈**: 提供具体的改进建议

### 4. 发布管理
- **版本标签**: 每次发布都要打版本标签
- **发布说明**: 详细记录每个版本的变更
- **回滚准备**: 保持快速回滚的能力

## 🆘 常见问题

### Q: 如何解决合并冲突？
```bash
# 1. 拉取最新代码
git pull origin develop

# 2. 手动解决冲突文件
# 编辑冲突文件，删除冲突标记

# 3. 标记冲突已解决
git add .
git commit -m "resolve: 解决合并冲突"
```

### Q: 如何撤销错误的提交？
```bash
# 撤销最后一次提交（保留文件变更）
git reset --soft HEAD~1

# 撤销最后一次提交（丢弃文件变更）
git reset --hard HEAD~1

# 撤销已推送的提交
git revert <commit-hash>
```

### Q: 如何同步远程分支？
```bash
# 获取所有远程分支信息
git fetch origin

# 删除本地已删除的远程分支
git remote prune origin

# 查看所有分支状态
git branch -a
```

## 📞 技术支持

如遇 Git 相关问题，请：
1. 查看本文档的常见问题部分
2. 搜索项目 Issues
3. 联系技术负责人
4. 在团队群组寻求帮助

---

**Git 工作流程让团队协作更高效，代码质量更可靠！** 🚀
