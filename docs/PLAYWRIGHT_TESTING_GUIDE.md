# 🎭 Playwright自动化测试指南

> **📋 文档目的**: 规范Playwright测试的使用方法和最佳实践
> **🔄 更新日期**: 2025-01-20
> **🎯 适用范围**: 壹心堂管理系统界面测试

## 🛠️ **Playwright配置**

### **基础配置**
- **测试目录**: `admin/tests/`
- **配置文件**: `admin/playwright.config.js`
- **报告输出**: `admin/test-results/`
- **超时设置**: 30秒

### **支持的浏览器**
- ✅ **Chromium** (主要测试)
- ✅ **Firefox** (兼容性测试)
- ✅ **WebKit** (Safari兼容性)
- ✅ **移动端** (响应式测试)

## 🎯 **测试标准**

### **界面测试要求**
- **零重叠**: 元素不得重叠
- **零覆盖**: 功能区域不得被遮挡
- **零错位**: 像素级精确对齐
- **零超界**: 元素不得超出容器边界

### **自适应测试**
- **缩放范围**: 0.8x - 1.2x
- **分辨率**: 1024px - 5120px
- **响应式**: PC端专用设计

## 📝 **测试用例编写**

### **基础测试模板**
```javascript
import { test, expect } from '@playwright/test';

test('页面基础功能测试', async ({ page }) => {
  // 导航到页面
  await page.goto('/dashboard');
  
  // 等待页面加载
  await page.waitForLoadState('networkidle');
  
  // 验证页面标题
  await expect(page).toHaveTitle(/仪表盘/);
  
  // 验证关键元素存在
  await expect(page.locator('.dashboard-content')).toBeVisible();
});
```

### **界面对齐测试**
```javascript
test('界面元素对齐测试', async ({ page }) => {
  await page.goto('/services');
  
  // 获取元素位置
  const header = page.locator('.table-header');
  const content = page.locator('.table-content');
  
  // 验证对齐
  const headerBox = await header.boundingBox();
  const contentBox = await content.boundingBox();
  
  expect(headerBox.x).toBe(contentBox.x);
});
```

### **响应式测试**
```javascript
test('响应式布局测试', async ({ page }) => {
  // 测试不同分辨率
  const resolutions = [
    { width: 1024, height: 768 },
    { width: 1920, height: 1080 },
    { width: 2560, height: 1440 }
  ];
  
  for (const resolution of resolutions) {
    await page.setViewportSize(resolution);
    await page.goto('/dashboard');
    
    // 验证布局不破坏
    await expect(page.locator('.sidebar')).toBeVisible();
    await expect(page.locator('.main-content')).toBeVisible();
  }
});
```

## 🔧 **运行测试**

### **本地测试**
```bash
cd admin
npx playwright test                    # 运行所有测试
npx playwright test --headed          # 显示浏览器窗口
npx playwright test --debug           # 调试模式
npx playwright test services.spec.js  # 运行特定测试
```

### **CI/CD测试**
```bash
# GitHub Actions自动运行
npm run test:auto
```

### **生成报告**
```bash
npx playwright show-report           # 查看HTML报告
npx playwright test --reporter=html  # 生成HTML报告
```

## 📊 **测试报告**

### **报告类型**
- **HTML报告**: 详细的可视化报告
- **JSON报告**: 机器可读的结果数据
- **JUnit报告**: CI/CD集成报告

### **关键指标**
- **通过率**: 目标 ≥ 95%
- **执行时间**: 单个测试 < 30秒
- **覆盖率**: 主要功能 100%

## 🚨 **测试最佳实践**

### **编写原则**
1. **独立性**: 每个测试独立运行
2. **可重复**: 结果一致可靠
3. **清晰性**: 测试意图明确
4. **高效性**: 执行时间合理

### **选择器策略**
```javascript
// 推荐：使用data-testid
await page.locator('[data-testid="submit-button"]').click();

// 推荐：使用语义化选择器
await page.locator('button:has-text("提交")').click();

// 避免：使用CSS类名
await page.locator('.btn-primary').click(); // 不推荐
```

### **等待策略**
```javascript
// 等待网络空闲
await page.waitForLoadState('networkidle');

// 等待元素可见
await page.waitForSelector('.loading', { state: 'hidden' });

// 等待特定条件
await page.waitForFunction(() => window.dataLoaded === true);
```

## 🔍 **调试技巧**

### **截图调试**
```javascript
test('调试测试', async ({ page }) => {
  await page.goto('/dashboard');
  
  // 截图保存
  await page.screenshot({ path: 'debug-screenshot.png' });
  
  // 元素截图
  await page.locator('.sidebar').screenshot({ path: 'sidebar.png' });
});
```

### **控制台日志**
```javascript
// 监听控制台消息
page.on('console', msg => console.log('浏览器日志:', msg.text()));

// 监听页面错误
page.on('pageerror', err => console.log('页面错误:', err.message));
```

## 📋 **测试检查清单**

### **测试前检查**
- [ ] 测试环境已启动
- [ ] 数据库状态正确
- [ ] 网络连接正常
- [ ] 浏览器版本兼容

### **测试后验证**
- [ ] 所有测试通过
- [ ] 报告生成成功
- [ ] 无遗留进程
- [ ] 清理临时文件

## 🎯 **集成工作流**

### **开发阶段**
1. 编写功能代码
2. 编写对应测试
3. 本地运行测试
4. 修复发现的问题

### **提交阶段**
1. 运行完整测试套件
2. 确保所有测试通过
3. 提交代码和测试
4. CI/CD自动验证

---

> **⚠️ 重要**: Playwright是当前唯一的自动化测试框架，所有界面测试必须使用Playwright！
