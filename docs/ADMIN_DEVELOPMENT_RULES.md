# 🎯 管理端开发规范 (当前阶段专用)

> 专注于管理端开发的核心规范，提高开发效率

## 🚨 **核心开发约束 (必须遵守)**

### **修改流程 (强制)**
1. **单文件修改**: 每次只修改一个文件
2. **立即测试**: 修改后立即在浏览器验证
3. **自动检查**: 运行 `node auto_check_after_changes.js` (通过率≥85%)
4. **功能验证**: 确保不破坏现有功能
5. **增量提交**: 每完成几个文件就提交

### **🚫 严禁行为**
- ❌ 批量脚本修改代码
- ❌ 同时修改多个文件
- ❌ 不测试就继续修改
- ❌ 累积多个问题再解决

## 🎨 **UI设计规范 (管理端)**

### **设计风格**
- **主题**: 毕加索艺术风格 + 紫色主色调 (#8e44ad)
- **布局**: 黄金比例，窄侧边栏，扁平结构
- **分辨率**: PC端专用 (1024px-4K)
- **缩放**: 自适应缩放 0.8x-1.2x

### **组件标准**
- **表格**: 默认5条/页，梵高风格分页
- **模态框**: 响应式，无滚动条，完美居中
- **按钮**: 七彩阴影效果，hover动画
- **Logo**: 占比8-12%，彩虹渐变

## 💻 **Vue 3 开发规范**

### **组件结构**
```vue
<script setup>
// 1. 导入
// 2. 响应式数据
// 3. 计算属性
// 4. 方法
// 5. 生命周期
</script>

<template>
<!-- 模板内容 -->
</template>

<style scoped>
/* 样式 */
</style>
```

### **错误处理 (强制)**
```javascript
const handleSubmit = async () => {
  try {
    loadingStates.submitLoading = true;
    // 表单验证
    if (!validateForm()) {
      showToast('请检查输入内容', 'error');
      return;
    }
    // API调用
    await apiCall();
    showToast('操作成功', 'success');
  } catch (error) {
    console.error('操作失败:', error);
    showToast('操作失败，请重试', 'error');
  } finally {
    loadingStates.submitLoading = false;
  }
};
```

### **状态管理**
```javascript
// 加载状态
const loadingStates = reactive({
  dataLoading: false,
  submitLoading: false,
  deleteLoading: false
});

// 表单状态
const formState = reactive({
  field1: '',
  field2: ''
});

// 错误状态
const formErrors = reactive({
  field1: '',
  field2: ''
});
```

## 🧪 **测试要求 (管理端)**

### **分辨率测试**
- 1024x768 (缩放0.8x)
- 1366x768 (基准1.0x)
- 1920x1080 (标准1.0x)
- 2560x1440 (缩放1.2x)
- 3840x2160 (缩放1.2x)

### **检查项目**
- ✅ 零重叠: 元素不能重叠
- ✅ 零错位: 像素级对齐
- ✅ 零超界: 不超出容器边界
- ✅ 功能完整: 所有交互正常

### **测试工具**
```javascript
// 启用调试
debug.on();
// 检查缩放
debug.scale();
// 检查重叠
debug.overlap();
// 清除调试
debug.clear();
```

## 📦 **依赖管理**

### **包管理器使用**
```bash
# 安装依赖
cd admin && npm install package-name

# 更新依赖
npm update package-name

# 🚫 禁止手动编辑 package.json
```

## 🔧 **开发环境**

### **启动服务**
```bash
# 一键启动
python one_click_start.py

# 或手动启动
cd admin && npm run dev
cd server && python manage.py runserver
```

### **开发工具**
- **热重载**: 自动刷新
- **错误检查**: ESLint + Prettier
- **自动检查**: `node auto_check_after_changes.js`

## 🎯 **管理端页面优先级**

### **第一优先级 (核心功能)**
1. **登录页面** - 系统入口
2. **仪表板** - 数据概览
3. **服务管理** - 核心业务 (已完成，作为模板)

### **第二优先级 (业务管理)**
4. **技师管理** - 人员管理
5. **客户管理** - 客户信息
6. **预约管理** - 预约流程

### **第三优先级 (财务系统)**
7. **财务概览** - 财务数据
8. **财务记录** - 收支记录

### **第四优先级 (系统功能)**
9. **健康贴士** - 内容管理
10. **系统设置** - 配置管理

## ⚡ **效率提升策略**

### **复用优先**
- 以服务管理页面为模板
- 复用组件和样式
- 统一交互模式

### **渐进开发**
- 先实现基础功能
- 再完善交互效果
- 最后优化性能

### **质量保障**
- 每个页面完成后立即测试
- 通过率≥85%才进入下一个
- 发现问题立即修复

---

**专注当前，逐层推进，保障质量！** 🚀
