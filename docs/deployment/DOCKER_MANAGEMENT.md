# 🐳 Docker智能管理指南

> **按需启动，节省资源** - Docker只在需要时运行，平时自动停止

## 🎯 设计理念

### **问题**
- Docker Desktop占用大量系统资源（CPU、内存）
- 开发时不总是需要Docker
- 手动管理Docker启停繁琐

### **解决方案**
- **按需启动**: 只在项目需要时自动启动Docker
- **智能检测**: 自动判断项目是否需要Docker
- **自动停止**: 开发结束后询问是否停止Docker

## 🛠️ 使用方法

### **自动管理（推荐）**
```bash
# 一键启动开发环境（自动管理Docker）
./start-all-dev.sh

# 开发结束时会自动询问是否停止Docker
# 按 Ctrl+C 退出时触发
```

### **手动管理**
```bash
# 检查Docker状态
./scripts/docker-manager.sh status

# 按需启动Docker
./scripts/docker-manager.sh start-if-needed

# 启动Docker
./scripts/docker-manager.sh start

# 停止Docker
./scripts/docker-manager.sh stop

# 重启Docker
./scripts/docker-manager.sh restart
```

## 🔧 工作原理

### **智能检测逻辑**
```bash
# 检查项目是否需要Docker
if [ -f "docker-compose.yml" ] || [ -f "Dockerfile" ]; then
    # 需要Docker，自动启动
    ./scripts/docker-manager.sh start-if-needed
else
    # 不需要Docker，跳过
    echo "项目不需要Docker"
fi
```

### **自动停止流程**
1. **开发结束**: 用户按 Ctrl+C 或脚本退出
2. **检查容器**: 是否有Docker容器在运行
3. **用户确认**: 询问是否停止Docker
4. **优雅停止**: 停止Docker Desktop释放资源

## 📊 资源节省效果

### **Docker运行时占用**
- **CPU**: 5-15%
- **内存**: 2-4GB
- **磁盘IO**: 持续读写

### **Docker停止后**
- **CPU**: 0%
- **内存**: 释放2-4GB
- **磁盘IO**: 无

## 🎛️ 配置选项

### **环境变量**
```bash
# 设置Docker自动管理行为
export DOCKER_AUTO_START=true    # 自动启动
export DOCKER_AUTO_STOP=true     # 自动询问停止
export DOCKER_WAIT_TIMEOUT=60    # 启动等待超时（秒）
```

### **项目配置**
```bash
# 在项目根目录创建 .docker-config
echo "auto_start=true" > .docker-config
echo "auto_stop=true" >> .docker-config
```

## 🚨 故障排除

### **Docker启动失败**
```bash
# 检查Docker Desktop是否安装
docker --version

# 手动启动Docker Desktop
open -a Docker

# 检查Docker状态
./scripts/docker-manager.sh status
```

### **权限问题**
```bash
# 给脚本执行权限
chmod +x scripts/docker-manager.sh
chmod +x scripts/auto-stop-docker.sh
```

### **启动超时**
```bash
# 增加等待时间
export DOCKER_WAIT_TIMEOUT=120

# 或手动启动后再运行开发脚本
./scripts/docker-manager.sh start
./start-all-dev.sh
```

## 📋 最佳实践

### **开发工作流**
1. **开始开发**: `./start-all-dev.sh` (自动管理Docker)
2. **专注开发**: Docker在后台按需运行
3. **结束开发**: `Ctrl+C` (自动询问是否停止Docker)
4. **确认停止**: 选择 `y` 释放系统资源

### **团队协作**
- 所有开发者使用相同的启动脚本
- Docker管理完全自动化
- 无需手动管理Docker状态

### **CI/CD集成**
```bash
# 在CI环境中跳过交互式询问
export CI=true
./start-all-dev.sh
```

---

**🎯 目标**: 让Docker管理完全透明，开发者专注于代码开发  
**💡 原则**: 按需使用，自动管理，节省资源
