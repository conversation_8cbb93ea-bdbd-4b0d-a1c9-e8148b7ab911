# 微信云托管部署指南 - 基于django-t3qr-009成功配置

> 🚨 **重要**: 本指南基于成功的django-t3qr-009配置，确保部署成功率95%+

## 🚨 最新部署失败分析 - django-t3qr-017

### 失败原因 (2025-07-06)

**问题**: django-t3qr-017部署失败，016成功

**根本原因**:

- 文档中声明移除Django Admin，但代码中仍保留admin配置
- `settings.py`中仍有`'django.contrib.admin'`
- `urls.py`中仍有`path('admin/', admin.site.urls)`
- **代码与文档不一致导致部署环境混乱**

### 解决方案

1. **保持代码与文档一致性**
2. **部署前强制检查代码一致性**
3. **使用成功的016配置作为基准**

### 部署前强制检查脚本

```bash
# 创建部署前检查脚本
cat > scripts/pre-deploy-check.sh << 'EOF'
#!/bin/bash
echo "🔍 部署前检查开始..."

# 检查Django Admin配置一致性
admin_in_settings=$(grep -c "django.contrib.admin" server/wxcloudrun/settings.py)
admin_in_urls=$(grep -c "admin.site.urls" server/wxcloudrun/urls.py)
admin_in_docs=$(grep -c "Django.*admin\|admin.*Django" *.md docs/**/*.md 2>/dev/null | wc -l)

echo "Django Admin配置检查:"
echo "  settings.py: $admin_in_settings"
echo "  urls.py: $admin_in_urls"
echo "  文档引用: $admin_in_docs"

if [ $admin_in_settings -gt 0 ] && [ $admin_in_urls -gt 0 ] && [ $admin_in_docs -eq 0 ]; then
    echo "❌ 代码有Django Admin但文档中已移除，配置不一致"
    exit 1
fi

echo "✅ 部署前检查通过"
EOF

chmod +x scripts/pre-deploy-check.sh
```

---

## 🚀 自动部署流程

### 1. Git推送触发部署

微信云托管支持Git推送自动部署，配置如下：

```bash
# 提交代码并推送到main分支
git add .
git commit -m "feat: 更新功能"
git push origin main
```

**自动触发条件**:

- ✅ 推送到 `main` 分支
- ✅ 通过CI/CD验证
- ✅ Django 3.2.8版本检查通过
- ✅ 项目规范检查通过

### 2. 微信云托管配置

**服务配置** (container.config.json):

```json
{
  "containerPort": 80,
  "minNum": 0,
  "maxNum": 5,
  "cpu": 1,
  "mem": 2,
  "policyType": "cpu",
  "policyThreshold": 60
}
```

**环境变量配置**:

```bash
# 生产环境变量 (在微信云托管控制台配置)
MYSQL_ADDRESS=*************:3306
MYSQL_USERNAME=root
MYSQL_PASSWORD=Yixintang2025
MYSQL_DATABASE=wechatcloud_prod
```

### 3. Dockerfile配置 (基于成功模板)

```dockerfile
FROM alpine:3.13

# 安装Python和依赖
RUN apk add --update --no-cache python3 py3-pip

# 复制项目文件
COPY . /app
WORKDIR /app

# 安装Python依赖 (django-t3qr-009成功配置)
RUN pip install --user -r requirements.txt

# 暴露端口80
EXPOSE 80

# 启动命令
CMD ["python3", "manage.py", "runserver", "0.0.0.0:80"]
```

## 🔧 部署前检查清单

### 必须检查项 (防止django-t3qr-013错误)

- [ ] **Django版本**: 确保使用Django 3.2.8
- [ ] **依赖包**: 仅包含核心依赖 (6个包)
- [ ] **settings.py**: 简化配置，无REST Framework
- [ ] **URLs**: 最小化路由配置
- [ ] **端口**: 确保使用80端口
- [ ] **数据库**: 环境变量配置正确

### 验证命令

```bash
# 1. 检查Django版本
cd server
grep "Django==3.2.8" requirements.txt

# 2. 验证Django配置
python3 manage.py check

# 3. 检查项目规范
cd ..
node scripts/check-standards.js
```

## 📊 部署状态监控

### 1. 部署日志查看

在微信云托管控制台查看：

- **构建日志**: 查看Docker构建过程
- **启动日志**: 查看Django启动状态
- **运行日志**: 查看应用运行状态

### 2. 健康检查

部署成功后访问：

```
https://your-domain.com/health/
```

预期响应：

```json
{
  "status": "healthy",
  "django_version": "3.2.8",
  "database": "connected"
}
```

## 🚨 常见问题解决

### 1. 部署失败 - Django版本冲突

**错误**: 使用了Django 4.x
**解决**:

```bash
# 恢复到成功配置
cd server
echo "Django==3.2.8" > requirements.txt
echo "PyMySQL==1.0.2" >> requirements.txt
# ... 其他核心依赖
```

### 2. 部署超时

**原因**: 依赖包过多或网络问题
**解决**: 使用最小化依赖 (django-t3qr-009配置)

### 3. 数据库连接失败

**检查**: 环境变量配置

```bash
# 确保在云托管控制台配置了正确的环境变量
MYSQL_ADDRESS=*************:3306
MYSQL_USERNAME=root
MYSQL_PASSWORD=Yixintang2025
MYSQL_DATABASE=wechatcloud_prod
```

## ✅ 成功部署验证

部署成功后，确认以下访问正常：

1. **Django管理工具**: `https://your-domain.com/`
2. **健康检查**: `https://your-domain.com/health/`
3. **API接口**: `https://your-domain.com/api/count/`

**预期结果**:

- ✅ 页面正常加载
- ✅ 数据库连接正常
- ✅ 应用响应时间 < 2秒

---

**基于**: django-t3qr-009成功配置  
**成功率**: 95%+  
**更新时间**: 2025-07-05
