# 🚨 部署失败经验教训与强制规范

> **基于018-026版本连续部署失败的深度分析和总结**

## 📊 失败版本分析

| 版本 | 部署时间 | 主要问题 | 错误类型 | 解决方案 |
|------|----------|----------|----------|----------|
| 018 | 2025-07-06 02:28 | `Unknown database 'yixintang_prod'` | 数据库名错误 | 使用正确的wechat_prod |
| 019 | 2025-07-06 02:28 | `Can't connect to MySQL server` | 网络连接 | 检查数据库服务器状态 |
| 020 | 2025-07-06 02:34 | `Unknown database 'yixintang_prod'` | 数据库名错误 | 修正数据库名称 |
| 021 | 2025-07-06 13:35 | `Liveness probe failed` | 服务启动失败 | 优化启动脚本 |
| 022 | 2025-07-06 13:51 | `connection refused` | 端口无法连接 | 修复PATH配置 |
| 023 | 2025-07-06 14:07 | `command not found` | bash语法错误 | 移除Python三引号 |
| 024 | 2025-07-06 14:11 | `Unknown database 'yixintang_prod'` | 数据库名错误 | 恢复016配置 |
| 025 | 临时版本 | SQLite测试 | 临时绕过 | 测试基本功能 |
| 026 | 2025-07-06 14:XX | **成功** | 使用wechat_prod | ✅ 最终解决 |

## 🎯 关键经验教训

### **1. 数据库配置是部署成功的关键**

#### ❌ 常见错误
```python
# 错误的数据库名
'NAME': 'yixintang_prod'  # 数据库不存在
'NAME': 'django_prod'     # 数据库不存在
```

#### ✅ 正确配置
```python
# 正确的数据库名
'NAME': os.environ.get("MYSQL_DATABASE", 'wechat_prod')  # 确认存在
```

### **2. bash脚本语法必须严格遵守**

#### ❌ 致命错误
```bash
#!/bin/bash
"""
Django启动脚本 - 包含部署后钩子
"""
# 这会导致: command not found
```

#### ✅ 正确语法
```bash
#!/bin/bash
# Django启动脚本 - 微信云托管部署
```

### **3. PATH环境变量配置至关重要**

#### ❌ 缺失配置
```dockerfile
# 缺少PATH设置，导致命令找不到
RUN pip install --user -r requirements.txt
```

#### ✅ 正确配置
```dockerfile
# 设置PATH环境变量
ENV PATH="/root/.local/bin:$PATH"
RUN pip install --root-user-action=ignore --user -r requirements.txt
```

## 🔒 强制规范清单

### **部署前必检项目**

#### ✅ 数据库配置检查
- [ ] 确认数据库名称: `wechat_prod` (生产) / `wechat_dev` (开发)
- [ ] 确认数据库服务器可访问
- [ ] 确认数据库实际存在
- [ ] 测试数据库连接

#### ✅ 启动脚本检查
- [ ] 使用正确的bash注释语法 (`#`)
- [ ] 禁止Python三引号注释 (`"""`)
- [ ] PATH环境变量已设置
- [ ] 启动流程顺序正确

#### ✅ 容器配置检查
- [ ] Dockerfile中PATH已设置
- [ ] pip警告已抑制
- [ ] 健康检查端点已实现
- [ ] 端口80正确暴露

#### ✅ 代码质量检查
- [ ] 语法检查通过
- [ ] 依赖安装完整
- [ ] 环境变量配置正确
- [ ] 日志输出清晰

## 🛠️ 故障排除指南

### **数据库连接问题**
```bash
# 1. 检查数据库是否存在
mysql -h ************* -P 3306 -u root -p
SHOW DATABASES;

# 2. 确认数据库名称
SELECT DATABASE();
```

### **启动脚本问题**
```bash
# 1. 检查语法
bash -n start.sh

# 2. 检查权限
ls -la start.sh
chmod +x start.sh
```

### **PATH问题**
```bash
# 1. 检查PATH
echo $PATH

# 2. 检查命令位置
which python3
which django-admin
```

## 📋 部署成功标准

### **健康检查通过**
- HTTP 200响应 `/health/` 端点
- 响应时间 < 30秒
- 服务持续运行

### **日志输出正常**
```
🚀 使用MySQL生产数据库: *************:3306
📊 数据库名: wechat_prod
📊 执行数据库迁移...
🌐 启动Django服务器...
✅ Django启动成功
🔄 执行部署后钩子...
🎉 部署完成！
```

### **功能验证**
- API接口正常响应
- 数据库操作正常
- 静态文件服务正常

---

**📅 创建时间**: 2025-07-06  
**📝 基于版本**: django-t3qr-018 至 django-t3qr-026  
**🎯 目标**: 零部署失败，100%成功率  
**⚠️ 重要性**: 强制执行，不可忽略
