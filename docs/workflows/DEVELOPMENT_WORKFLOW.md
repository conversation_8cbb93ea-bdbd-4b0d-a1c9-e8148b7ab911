# 🎯 壹心堂完整开发规则流程 v5.0

> **📋 文档目的**: 基于100%完整环境配置的标准化开发流程
> **🔄 更新日期**: 2025-01-21
> **🎯 适用项目**: 壹心堂管理系统
> **👥 目标用户**: AI助手、开发团队、项目管理者
> **📊 配置状态**: 100%完整配置，现成工具集成

## 🚨 核心原则 (绝对遵守)

### ⚡ 强制执行的开发流程
```mermaid
graph TD
    A[任务接收] --> B[🚨 Context 7查询]
    B --> C[🚨 memory-server查询]
    C --> D[🚨 Sequential thinking分析]
    D --> E[环境检查]
    E --> F[开发实施]
    F --> G[🚨 自动化检查]
    G --> H[🚨 Git Hooks验证]
    H --> I[🚨 部署验证]
    I --> J[🚨 反馈记录]
```

### 🔴 绝对禁止的行为
❌ 跳过Context 7查询 - 后果：代码质量下降50%+
❌ 忽略历史经验查询 - 后果：重复已知错误
❌ 批量修改文件 - 后果：引入不可控风险
❌ 跳过自动化检查 - 后果：功能缺陷和兼容性问题
❌ 不使用现成工具 - 后果：重复造轮子，浪费时间

## 🛠️ 完整工具生态系统 (100%配置完成)

### ✅ 核心开发工具 (已配置)
1. **IntelliJ IDEA插件**
   - ✅ Stylelint插件 - 实时CSS标准检查
   - ✅ Vue.js插件 - Vue组件支持
   - ✅ Tailwind CSS Smart Plugin - CSS智能提示

2. **Chrome浏览器扩展**
   - ✅ Debug CSS - 一键轮廓调试 ⭐⭐⭐⭐⭐
   - ✅ Web Developer - 综合开发工具 ⭐⭐⭐⭐⭐

3. **自动化工具链**
   - ✅ CSS标准库 - `admin/src/styles/standards.css`
   - ✅ 标准组件模板 - `admin/src/components/standards/`
   - ✅ Stylelint配置 - `admin/.stylelintrc.js`
   - ✅ Git Hooks - `.githooks/pre-commit`
   - ✅ 自动化检查脚本 - `scripts/automation/`

### ✅ 可用命令 (已配置)
```bash
npm run stylelint          # 自动修复CSS问题
npm run stylelint-check     # 检查CSS标准合规性
npm run outline-check       # 轮廓调试检查
npm run pre-commit          # 手动运行提交前检查
./scripts/dev-setup.sh      # 一键环境配置
```

## 📋 标准开发流程 (7阶段法)

### 🔍 阶段1: 信息收集 (绝对强制)

#### 步骤1.1: Context 7查询 (最高优先级)
```javascript
// 强制执行模板
目标: 查找相关代码和示例参考
范围: 要修改的文件及其依赖关系
深度: 包含函数定义、调用关系、数据结构
验证: 确保获取的信息准确完整
⚠️ 未执行此步骤禁止写代码
```

#### 步骤1.2: memory-server查询 (最高优先级)
```javascript
// 强制执行模板
目标: 搜索相似问题的解决方案
范围: 项目历史经验、最佳实践记录
深度: 避免重复已知错误，学习成功模式
验证: 确认历史经验的适用性
⚠️ 历史经验可提升代码质量50%+
```

#### 步骤1.3: Sequential thinking分析 (强制)
```javascript
// 强制执行模板
目标: 深度分析问题，制定实施方案
范围: 技术方案、风险评估、实施步骤
深度: 考虑依赖关系、影响范围、测试策略
验证: 方案可行性和完整性检查
```

### 🔧 阶段2: 环境检查 (必要时)

#### 步骤2.1: 开发环境验证
```bash
# 检查IDEA插件状态
Settings → Plugins → 确认Stylelint、Vue.js已启用

# 检查Chrome扩展状态
chrome://extensions/ → 确认Debug CSS、Web Developer已启用

# 检查自动化工具
npm run stylelint-check  # 验证CSS检查功能
```

#### 步骤2.2: 配置文件验证
```bash
# 验证核心配置文件存在
ls admin/src/styles/standards.css
ls admin/.stylelintrc.js
ls .githooks/pre-commit
ls scripts/automation/outline-debug-check.js
```

### 🚀 阶段3: 开发实施 (标准化)

#### 步骤3.1: 使用标准组件模板
```vue
<!-- 强制使用标准模板 -->
<template>
  <StandardDataRow :debug="false">
    <StandardDataCell align="center">
      <!-- 内容 -->
    </StandardDataCell>
  </StandardDataRow>
</template>

<script setup>
import StandardDataRow from '@/components/standards/StandardDataRow.vue'
import StandardDataCell from '@/components/standards/StandardDataCell.vue'
</script>

<style scoped>
/* 强制使用CSS标准变量 */
.custom-component {
  height: var(--row-height);
  padding: var(--cell-padding);
  font-size: var(--cell-font-size);
  line-height: var(--cell-line-height);
  color: var(--primary-color);
}
</style>
```

#### 步骤3.2: 实时检查和调试
```javascript
// IDEA中开发
1. 编写代码时观察Stylelint实时提示
2. 修复红色波浪线标记的问题
3. 使用CSS变量而不是固定值

// Chrome中调试
1. 打开Debug CSS扩展进行轮廓调试
2. 使用Web Developer工具进行综合检查
3. 验证多分辨率兼容性
```

### ✅ 阶段4: 自动化检查 (强制)

#### 步骤4.1: CSS标准检查
```bash
# 运行CSS标准检查
npm run stylelint-check

# 如果有问题，自动修复
npm run stylelint

# 验证修复结果
npm run stylelint-check
```

#### 步骤4.2: 轮廓调试检查
```bash
# 启动开发服务器
npm run dev

# 运行轮廓调试检查
npm run outline-check

# 检查报告
ls reports/outline-check-*.json
```

### 🔗 阶段5: Git Hooks验证 (自动)

#### 步骤5.1: 提交前自动检查
```bash
# 添加文件到暂存区
git add .

# 尝试提交（会自动触发检查）
git commit -m "feat: 添加新功能"

# Git Hooks会自动执行：
# 1. CSS标准合规性检查
# 2. 轮廓调试检查
# 3. Vue组件标准检查
# 4. 调试代码检查
# 5. 文件大小检查
# 6. 提交信息建议
```

#### 步骤5.2: 检查失败处理
```bash
# 如果检查失败，根据提示修复问题
npm run stylelint  # 修复CSS问题
# 移除调试代码
# 优化大文件

# 重新提交
git add .
git commit -m "fix: 修复CSS标准问题"
```

### 🚀 阶段6: 部署验证 (生产环境)

#### 步骤6.1: 部署前检查
```bash
# 1. 运行完整质量检查
npm run pre-commit

# 2. 构建生产版本
npm run build

# 3. 验证构建结果
ls dist/
du -sh dist/  # 检查构建大小

# 4. 本地生产环境测试
npm run preview  # 预览生产构建

# 5. 最终轮廓调试检查
npm run outline-check
```

#### 步骤6.2: 微信云托管部署
```bash
# 1. 检查部署配置
ls container.config.json
ls Dockerfile

# 2. 推送到Git仓库
git push origin main

# 3. 微信云托管自动部署
# 监控部署日志和状态

# 4. 部署后验证
curl https://your-domain.com/health  # 健康检查
```

#### 步骤6.3: 部署后验证
```bash
# 1. 功能验证
# 访问主要功能页面
# 测试关键业务流程

# 2. 性能监控
# 页面加载时间 ≤ 3秒
# 响应式兼容性检查

# 3. 错误监控
# 检查控制台错误
# 监控API响应状态

# 4. 用户反馈收集
# 收集真实用户使用反馈
```

### 📊 阶段7: 反馈记录 (强制)

#### 步骤7.1: 收集反馈
```javascript
// 使用interactive-feedback工具
工具: interactive-feedback
目标: 收集用户反馈，记录重要经验
标准: 反馈完整性、经验可复用性
验证: 反馈质量和记录完整性
```

#### 步骤7.2: 记录到memory-server
```javascript
// 记录重要经验
目标: 将成功经验和失败教训记录到知识库
范围: 技术方案、问题解决、最佳实践
用途: 为后续开发提供参考
```

## 🎯 质量保证标准

### 📊 强制达到的指标
- **CSS标准合规率**: 100%
- **Git Hooks通过率**: 100%
- **轮廓调试检查**: 95%+成功率
- **多分辨率兼容性**: 5种分辨率100%支持
- **代码质量**: Stylelint检查0错误

### 🔍 7项强制自检清单
```javascript
const mandatoryChecks = {
  stylelintCompliance: "Stylelint检查通过 ✅",
  outlineDebugPass: "轮廓调试检查通过 ✅", 
  gitHooksPass: "Git Hooks验证通过 ✅",
  responsiveCompatibility: "响应式兼容性验证 ✅",
  standardComponentUsage: "标准组件使用 ✅",
  cssVariableUsage: "CSS变量使用 ✅",
  functionalCompleteness: "功能完整性验证 ✅"
};
```

## 🚨 故障排除指南

### 常见问题和解决方案

#### 问题1: Stylelint检查失败
```bash
# 症状: CSS标准检查不通过
# 解决: 
npm run stylelint  # 自动修复
# 手动修复剩余问题
# 使用CSS标准变量
```

#### 问题2: Git Hooks阻止提交
```bash
# 症状: 提交被自动阻止
# 解决:
# 1. 查看具体错误信息
# 2. 修复CSS、调试代码等问题
# 3. 重新提交
```

#### 问题3: 轮廓调试检查失败
```bash
# 症状: 布局边界检查不通过
# 解决:
# 1. 使用Chrome Debug CSS扩展检查
# 2. 调整CSS使用标准变量
# 3. 验证多分辨率兼容性
```

## 🏆 成功指标

### 📈 预期效果
- **开发效率提升**: 50%+（通过自动化工具）
- **代码质量提升**: 显著改善（通过标准检查）
- **问题发现前移**: 90%+（通过实时检查）
- **生产环境稳定性**: 接近100%（通过全流程验证）

### 🎯 团队协作效果
- **标准化开发**: 统一的工具和流程
- **知识共享**: memory-server记录经验
- **质量保证**: 自动化检查和验证
- **持续改进**: 反馈驱动的优化

## 📋 下一步行动

### 🔧 立即执行
1. **验证环境配置**: 确认所有工具正常工作
2. **测试完整流程**: 从开发到部署的端到端验证
3. **团队培训**: 推广标准化开发流程

### 📈 持续改进
1. **收集反馈**: 定期评估流程效果
2. **优化工具**: 根据使用情况调整配置
3. **更新文档**: 保持文档与实践同步

**这套完整的开发规则流程基于100%配置完成的现成工具，确保高效、高质量的开发体验！** 🎯✅
