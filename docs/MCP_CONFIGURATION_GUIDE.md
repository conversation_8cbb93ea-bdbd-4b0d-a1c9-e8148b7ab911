# 🔧 MCP服务器配置指南 v2.0

> **📋 文档目的**: 壹心堂项目9个稳定MCP服务器的配置和使用方法
> **🔄 更新日期**: 2025-01-25
> **🎯 适用范围**: 壹心堂管理系统开发
> **📁 配置文件**: `config/mcp-config.json` 和 `config/mcp-config-stable.json`

## 🚨 **9个稳定MCP服务器 (100%可靠性)**

### **🔴 核心强制层 (绝对必须)**

#### **1. 🔍 context7 - 代码库上下文查询**
- **名称**: `context7`
- **命令**: `npx -y @upstash/context7-mcp@latest`
- **功能**: 代码库上下文查询和分析 (8 tools)
- **环境变量**:
  - `CONTEXT_DEPTH=deep` (深度上下文分析)
  - `SEARCH_SCOPE=project` (项目范围搜索)

#### **2. 🧠 memory-server - 长期记忆和知识图谱**
- **名称**: `memory-server`
- **命令**: `npx -y @modelcontextprotocol/server-memory`
- **功能**: 长期记忆和知识图谱管理 (18 tools)
- **环境变量**:
  - `MEMORY_STORAGE=./user-memories` (存储路径)
  - `ENABLE_GRAPH=true` (启用知识图谱)

#### **3. 🧩 sequential-thinking - 思维链分析**
- **名称**: `sequential-thinking`
- **命令**: `npx -y @modelcontextprotocol/server-sequential-thinking`
- **功能**: 思维链分析和推理 (4 tools)
- **环境变量**:
  - `THINKING_DEPTH=5` (思维深度)
  - `ENABLE_REFLECTION=true` (启用反思)

### **🟡 任务管理层 (推荐安装)**

#### **4. 📋 shrimp-task-manager - AI任务管理**
- **名称**: `shrimp-task-manager`
- **命令**: `npx -y mcp-shrimp-task-manager`
- **功能**: AI任务管理和规划 (15 tools)
- **环境变量**:
  - `TASK_STORAGE_PATH=./tasks` (任务存储路径)
  - `ENABLE_REFLECTION=true` (启用反思)
  - `CHAIN_OF_THOUGHT=true` (启用思维链)
  - `ENABLE_PLANNING=true` (启用规划)

#### **5. 💬 interactive-feedback - 强制反馈收集**
- **名称**: `interactive-feedback`
- **命令**: `npx -y mcp-interactive-feedback`
- **功能**: 交互式反馈收集 (1 tools)
- **环境变量**:
  - `FEEDBACK_STORAGE=./feedback` (反馈存储路径)
  - `ENABLE_AI_REPORTS=true` (启用AI报告)
  - `REPORT_FORMAT=markdown` (报告格式)
  - `FORCE_FEEDBACK=true` (强制反馈)

### **🟢 实施验证层 (必要时安装)**

#### **6. 📁 filesystem - 文件系统操作**
- **名称**: `filesystem`
- **命令**: `npx -y @modelcontextprotocol/server-filesystem /Users/<USER>/Documents/wechatcloud`
- **功能**: 文件系统操作 (12 tools)
- **环境变量**:
  - `ALLOWED_DIRECTORIES=/Users/<USER>/Documents/wechatcloud` (允许访问的目录)
  - `ENABLE_WRITE=true` (启用写入权限)

#### **7. 🎭 playwright - 自动化测试**
- **名称**: `playwright`
- **命令**: `npx -y @playwright/mcp@latest`
- **功能**: 自动化测试和浏览器操作 (96 tools)
- **环境变量**:
  - `PLAYWRIGHT_HEADLESS=true` (无头模式)
  - `PLAYWRIGHT_TIMEOUT=30000` (超时设置)

#### **8. 📊 chart-generator - 数据可视化**
- **名称**: `chart-generator`
- **命令**: `npx -y @antv/mcp-server-chart`
- **功能**: 数据可视化和图表生成 (25 tools)
- **环境变量**:
  - `CHART_OUTPUT_DIR=./charts` (图表输出目录)
  - `DEFAULT_THEME=purple` (默认紫色主题)
  - `ENABLE_EXPORT=true` (启用导出)

#### **9. 🔧 everything - 调试和测试**
- **名称**: `everything`
- **命令**: `npx -y @modelcontextprotocol/server-everything`
- **功能**: 调试和测试工具 (8 tools)
- **环境变量**:
  - `DEBUG_MODE=true` (调试模式)

## 🎯 **使用场景和最佳实践**

### **🔴 核心强制层使用场景**

#### **🔍 context7 使用场景**
```
✅ 查找相关代码和示例参考 (写代码前绝对强制)
✅ 分析代码结构和依赖关系
✅ 搜索项目中相似功能的实现
✅ 验证函数定义和调用关系
✅ 提取可复用的代码模式
```

#### **🧠 memory-server 使用场景**
```
✅ 记录开发过程中的重要经验 (绝对强制)
✅ 建立项目知识图谱和关系网络
✅ 存储问题解决方案和最佳实践
✅ 避免重复已知错误和问题
✅ 维护项目历史和演进记录
```

#### **🧩 sequential-thinking 使用场景**
```
✅ 分解复杂问题为可管理的子问题 (强制)
✅ 制定详细的技术实施方案
✅ 分析潜在风险和依赖关系
✅ 验证方案的完整性和可行性
✅ 进行深度逻辑推理和分析
```

## 🔄 **集成到开发流程**

### **信息收集阶段**
1. **Context 7**: 查询代码库信息
2. **fetch-tool**: 获取外部技术资源
3. **memory-server**: 查询历史经验和知识

### **问题分析阶段**
1. **Sequential thinking**: 复杂问题分解
2. **reasoning-enhance**: 增强推理分析
3. **memory-server**: 查找相似问题解决方案

### **方案规划阶段**
1. **planning-server**: 制定详细执行计划
2. **memory-server**: 记录规划决策和依据
3. **Sequential thinking**: 验证方案可行性

### **实施执行阶段**
1. **Context 7**: 精确定位修改位置
2. **Playwright**: 自动化测试验证
3. **memory-server**: 记录实施过程和结果

### **质量保证阶段**
1. **完成后自检**: 7项强制检查
2. **emotion-server**: 分析用户体验影响
3. **memory-server**: 更新经验库

## 📊 **工具优先级**

### **🚨 核心工具 (必须使用)**
1. **Context 7** - 代码库查询
2. **Sequential thinking** - 问题分析
3. **Playwright** - 自动化测试

### **🔧 增强工具 (推荐使用)**
1. **memory-server** - 知识管理
2. **planning-server** - 任务规划
3. **fetch-tool** - 外部资源获取

### **🎨 辅助工具 (按需使用)**
1. **reasoning-enhance** - 复杂推理
2. **emotion-server** - 用户体验分析

## 🎯 **最佳实践**

### **memory-server 使用建议**
```
✅ 记录重要的设计决策和原因
✅ 建立功能模块之间的关系
✅ 存储常见问题的解决方案
✅ 维护代码规范和约束
✅ 跟踪项目演进历史
```

### **planning-server 使用建议**
```
✅ 将大任务分解为小步骤
✅ 为每个步骤设定验证标准
✅ 考虑依赖关系和执行顺序
✅ 预留测试和文档更新时间
✅ 制定风险应对措施
```

### **fetch-tool 使用建议**
```
✅ 获取官方文档和最佳实践
✅ 查询最新的API变更信息
✅ 收集相关技术的示例代码
✅ 验证外部依赖的可用性
✅ 获取社区解决方案参考
```

## 🚨 **注意事项**

### **环境变量配置**
```bash
# 在AI助手配置中设置
FETCH_TIMEOUT=5000
MEMORY_STORAGE=./user-memories
MAX_PLAN_STEPS=10
```

### **存储路径管理**
- memory-server的存储路径需要确保可写权限
- 定期备份重要的记忆数据
- 避免存储敏感信息

### **网络请求限制**
- fetch-tool需要网络访问权限
- 注意请求频率和超时设置
- 遵守目标网站的robots.txt规则

## 🔄 **更新机制**

### **配置更新**
- MCP服务器配置变更时立即更新文档
- 新增服务器时补充使用场景和最佳实践
- 定期检查服务器版本和功能更新

### **使用反馈**
- 记录各服务器的使用效果
- 优化工具组合和使用流程
- 完善最佳实践指南

---

> **⚠️ 重要提醒**: 
> 1. MCP服务器增强了AI助手的能力，但核心开发流程保持不变
> 2. 优先使用核心工具，合理利用增强工具
> 3. 及时记录使用经验到memory-server中
