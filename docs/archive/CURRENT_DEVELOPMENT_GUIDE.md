# 🎯 当前开发指南 (2025-01-20)

> **📋 文档目的**: 明确当前使用的工具栈、开发规范和AI工作指引
> **🔄 更新频率**: 每次工具或规范变更时立即更新
> **👥 目标用户**: AI助手、开发团队

## 🛠️ **当前工具栈 (强制使用)**

### **🎭 AI工具**
- **Sequential thinking**: 复杂问题分析和思维链推理
- **Context 7**: 代码库上下文引擎，用于代码检索和理解
- **Playwright**: 自动化测试框架

### **🔧 MCP服务器 (新增 2025-01-20)**
- **fetch-tool**: 网络请求和数据获取 (`@modelcontextprotocol/server-fetch`)
- **memory-server**: 长期记忆和知识图谱管理 (`@modelcontextprotocol/server-memory`)
- **planning-server**: 任务规划和步骤分解 (`@modelcontextprotocol/server-planning`)
- **emotion-server**: 情感分析和处理 (`emotion-mcp-server`)
- **reasoning-enhance**: 推理能力增强 (`reasoning-mcp-server`)

### **🚫 已弃用工具**
- ❌ **Node.js相关**: 不再使用npm、yarn、Node.js环境
- ❌ **手动测试**: 不再使用浏览器控制台手动测试
- ❌ **Puppeteer**: 已替换为Playwright

## 📋 **开发规范优先级**

### **🚨 强制遵守 (优先级1)**
1. **DEVELOPMENT_CONSTRAINTS.md** - 开发约束和自检规范
2. **当前文档** - 工具栈和基础规范
3. **Playwright测试** - 自动化测试标准

### **📖 参考文档 (优先级2)**
1. **COMPREHENSIVE_DEVELOPMENT_STANDARDS.md** - 全面开发规范标准 (新增)
2. **MCP_CONFIGURATION_GUIDE.md** - MCP服务器配置指南
3. **PLAYWRIGHT_TESTING_GUIDE.md** - Playwright测试规范
4. **AI_WORKFLOW_GUIDE.md** - AI工作流程指引
5. **CI_CD_STANDARDS.md** - 持续集成规范
6. **PROBLEM_EXPERIENCE_LIBRARY.md** - 问题经验库

### **📁 已归档 (优先级3)**
- `docs/archive/` - 过时文档，仅供历史参考

## 🎯 **AI工作指引**

### **🔍 信息获取流程 (已更新 2025-01-20)**

#### **⚡ 写代码前绝对强制约束**
> **🚨 重要**: 必须严格指令AI在写代码前执行知识查询
> **📈 效果**: 示例参考文件可提升代码质量50%+

#### **🔴 强制流程 (写代码前必须按顺序执行)**
1. **【绝对强制】使用Context 7**: 查找代码库相关信息和示例参考 (最高优先级)
2. **【绝对强制】查找示例参考**: 寻找相似功能的成功实现案例 (最高优先级)
3. **【绝对强制】使用memory-server**: 查询历史经验和解决方案 (高优先级)
4. **【绝对强制】使用Sequential thinking**: 分析复杂问题和制定方案 (高优先级)

#### **🟡 规范流程 (建议执行)**
4. **使用shrimp-task-manager**: 分解复杂任务和依赖管理
5. **使用web-fetch**: 获取外部技术文档和资源
6. **参考开发规范**: 确认约束和最佳实践

#### **🟢 辅助流程 (按需执行)**
7. **使用deep-reasoning**: 处理复杂逻辑推理
8. **使用interactive-feedback**: 收集用户反馈和改进建议

### **🚨 强制检查项**
- ✅ 每次修改后执行完成后自检
- ✅ 使用Playwright进行自动化测试
- ✅ 遵守开发约束规范
- ✅ 更新相关文档

### **❌ 禁止行为**
- ❌ 引用Node.js相关内容
- ❌ 使用已弃用的测试方法
- ❌ 忽略自检流程
- ❌ 批量修改文件

## 📊 **项目技术栈**

### **后端**
- **Python 3.9+**: 主要开发语言
- **Django**: Web框架
- **pyenv**: Python版本管理

### **前端**
- **Vue 3**: 管理后台框架
- **Taro**: 小程序框架
- **Playwright**: 自动化测试

### **开发工具**
- **Git**: 版本控制
- **VSCode**: 推荐IDE
- **Chrome DevTools**: 调试工具

## 🔄 **文档更新机制**

### **触发更新条件**
- 工具栈变更
- 开发规范调整
- 新功能添加
- 问题解决方案更新

### **更新责任**
- **AI助手**: 立即更新相关文档
- **开发者**: 确认更新内容
- **文档维护**: 定期清理过时内容

### **更新标记**
```markdown
## 📝 更新日志
- 2025-01-20: 创建当前开发指南，明确工具栈
- 2025-01-20: 弃用Node.js相关工具和文档
- 2025-01-20: 确立Playwright为唯一测试框架
```

## 🎨 **界面开发规范**

### **设计风格**
- **主题**: 毕加索艺术风格
- **主色调**: 紫色系
- **布局**: 黄金比例
- **响应式**: PC端专用 (1024px+)

### **测试标准**
- **零重叠**: 元素不得重叠
- **零覆盖**: 功能区域不得被遮挡
- **零错位**: 像素级精确对齐
- **自适应缩放**: 0.8x-1.2x范围

## 📁 **文档结构说明**

### **当前有效文档**
```
docs/
├── CURRENT_DEVELOPMENT_GUIDE.md    # 👈 当前文档 (主要参考)
├── DEVELOPMENT_CONSTRAINTS.md      # 开发约束
├── CI_CD_STANDARDS.md             # CI/CD规范
├── PROBLEM_EXPERIENCE_LIBRARY.md  # 问题经验库
└── guides/                        # 操作指南
```

### **已归档文档**
```
docs/archive/
├── TESTING.md                     # 旧测试方法
├── TROUBLESHOOTING.md            # 旧故障排除
└── DEVELOPMENT_ISSUES.md         # 旧开发问题
```

## 🎯 **下一步行动**

1. **清理过时文档**: 移动到archive目录
2. **更新现有文档**: 移除Node.js相关内容
3. **完善测试规范**: 基于Playwright制定标准
4. **建立监控机制**: 确保文档及时更新

---

> **⚠️ 重要提醒**: 本文档是当前开发的唯一权威指引，所有AI助手必须优先参考此文档！
