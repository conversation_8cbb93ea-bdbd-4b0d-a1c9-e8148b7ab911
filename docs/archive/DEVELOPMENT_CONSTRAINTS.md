# 🚨 开发约束和规范 (强制执行)

> 这些约束是基于实际开发经验总结的重要规则，必须严格遵守

## 🎯 **问题解决优先级 (2025-01-19 血的教训)**

### **🚨 用户反馈问题处理原则**
- **直接解决**: 🚨 **用户反馈问题时，直接分析并解决，不要先写文档**
- **行动优于规范**: 🚨 **解决问题比制定规范更重要**
- **立即验证**: 🚨 **每次修改后立即测试效果**
- **记录经验**: 🚨 **解决问题后再总结经验教训**

### **🚨 CSS调试强制流程**
```javascript
// 1. 检查DOM结构
const element = document.querySelector('.target');
console.log('DOM结构:', element.outerHTML);

// 2. 分析计算样式
const style = getComputedStyle(element);
console.log('实际样式:', style.backgroundColor);

// 3. 强制修复冲突
element.style.setProperty('background-color', '#ffffff', 'important');
```

### **🚨 设计原则 (简洁优于复杂)**
- **简单方案**: 🚨 **优先使用简单直接的解决方案**
- **避免过度设计**: 🚨 **复杂的系统往往造成更多问题**
- **固定尺寸**: 🚨 **简单的固定尺寸比复杂的自适应更可靠**

## 🔧 **修改流程强制约束**

### **🚨 唯一启动脚本约束 (2025-01-21新增)**
- **唯一启动方式**: 🚨 **只能使用 `./start.sh` 启动所有服务**
- **禁止行为**: 手动启动前端/后端、使用其他启动脚本、重复启动进程
- **热更新支持**: 🚨 **启动脚本默认支持热更新，不需要每次重启**
- **进程检测**: 🚨 **脚本自动检测进程是否存在，避免重复启动**
- **违规后果**: 可能导致端口冲突、进程混乱、开发效率低下

### **🚨 严禁批量脚本修改**
- **禁止行为**: 使用任何批量脚本、自动化工具进行代码修改
- **禁止工具**: sed, awk, 批量替换脚本, 自动重构工具
- **原因**: 批量修改容易引入错误，难以控制影响范围
- **违规后果**: 可能导致系统崩溃，功能异常，难以排查问题

### **✅ 正确的修改流程 (强制执行)**
1. **单文件修改**: 🚨 **每次只修改一个文件**
2. **立即测试**: 🚨 **修改后立即在浏览器中验证效果**
3. **🆕 自动检查**: 🚨 **立即运行自动检查，通过率必须≥85%**
4. **🆕 完成后自检**: 🚨 **每次功能完成后必须执行全面自检**
5. **问题修复**: 🚨 **发现问题立即修复，不能带问题继续**
6. **功能验证**: 🚨 **确保修改不破坏现有功能**
7. **影响评估**: 🚨 **检查是否影响其他页面或组件**
8. **增量提交**: 🚨 **每完成几个文件就提交一次**

### **🔍 完成后自检规范 (2025-01-20 新增)**
#### **🚨 强制自检项目**
1. **HTML结构检查**: div标签匹配、嵌套正确
2. **函数定义检查**: 所有新增函数已正确定义
3. **模板引用检查**: 模板中的函数调用完整
4. **CSS样式检查**: 所有样式类已定义且生效
5. **事件绑定检查**: 所有事件处理器正确绑定
6. **语法错误检查**: 括号匹配、语法正确
7. **功能完整性检查**: 新功能完整可用

#### **🚨 自检执行标准**
- **通过率要求**: 所有检查项必须100%通过
- **问题处理**: 发现问题立即修复，不得遗留
- **记录要求**: 自检结果必须记录到开发日志
- **验证方式**: 使用自动化脚本进行客观检查

### **🔍 修改前必须检查**
- **依赖关系**: 检查文件被哪些组件引用
- **样式继承**: 检查CSS样式是否被其他元素继承
- **全局影响**: 检查是否影响全局样式或变量
- **组件复用**: 检查组件是否在多个页面中使用

## 🎯 **自适应缩放约束**

### **🚨 缩放实现强制要求**
- **禁止整体缩放**: ❌ 不能对整个容器使用 `transform: scale()`
- **元素级缩放**: ✅ 必须对每个元素的具体属性进行缩放
- **缩放属性**: 宽度、高度、内边距、外边距、圆角、阴影、字体
- **缩放公式**: `calc(原始值 * var(--scale-factor))`

### **✅ 正确的缩放方式**
```css
/* ✅ 正确 - 元素级自适应缩放 */
.login-card {
  width: calc(380px * var(--scale-factor));
  padding: calc(40px * var(--scale-factor));
  border-radius: calc(24px * var(--scale-factor));
}

/* ❌ 错误 - 整体容器缩放 */
.container {
  transform: scale(var(--scale-factor)); /* 会导致定位问题 */
}
```

### **🔧 缩放因子定义**
```css
/* 页面根容器 */
.page-container {
  --base-width: 1366;
  --scale-factor: clamp(0.8, calc(100vw / var(--base-width)), 1.2);
}
```

## 🧪 **测试约束**

### **🚨 测试强制要求**
- **即时测试**: 每次修改后必须立即测试
- **🆕 自动检查**: 🚨 **修改后立即运行自动检查，通过率≥85%**
- **多分辨率测试**: 必须在所有5个分辨率下测试
- **功能完整性**: 确保所有交互功能正常
- **回归测试**: 确保修改不影响其他页面

### **🚨 自动检查强制流程**
```javascript
// 每次修改后必须执行
browser_evaluate_Playwright(() => {
  // 运行完整的自动检查
  const results = runAutoCheck();
  if (results.passRate < 85) {
    throw new Error('通过率不足85%，必须修复问题');
  }
  return results;
});
```

### **📋 测试检查清单**
- [ ] **1024x768**: 元素缩小后仍可正常使用
- [ ] **1366x768**: 基准分辨率，所有功能正常
- [ ] **1920x1080**: 大屏显示效果良好
- [ ] **2560x1440**: 2K分辨率下元素放大合理
- [ ] **3840x2160**: 4K分辨率下视觉效果完美

### **🔍 必须检查的项目**
- **零重叠**: 🚨 **使用轮廓调试+坐标显示检查元素重叠（严禁单独轮廓）**
- **零错位**: 🚨 **基于坐标数据检查元素对齐是否精确**
- **零超界**: 🚨 **基于坐标边界数据确保元素不超出容器**
- **功能完整**: 所有按钮、表单、链接正常工作
- **🆕 坐标验证**: 🚨 **所有调试必须包含坐标信息验证**

## 🎨 **视觉约束**

### **🚨 视觉标准强制要求**
- **Logo比例标准**: 🚨 **Logo占卡片比例必须8-12%，参考Apple/Google/Stripe标准**
- **毕加索艺术风格**: 保持紫色主题和艺术风格
- **黄金比例布局**: 使用1.618比例进行布局
- **视觉平衡**: 元素分布要协调美观
- **色彩一致**: 保持紫色系主色调

### **🚨 Logo设计强制规范**
- **尺寸标准**: Logo宽度 = 卡片宽度 × 8-12%
- **行业参考**: Apple(8-12%), Google(10-15%), Stripe(8-10%), Microsoft(10-12%)
- **检查方法**: `logoWidth / cardWidth * 100` 必须在8-12%范围内
- **修复要求**: 超出范围立即调整，不能妥协

### **📏 精度要求**
- **像素级对齐**: 误差不超过1px
- **间距统一**: 使用8px网格系统
- **圆角一致**: 相同类型元素圆角保持一致
- **阴影协调**: 阴影效果要自然统一

## 🎯 **轮廓测试强制约束 (2025-01-21重大更新)**

### **🚨 轮廓测试强制规范 (绝对遵守)**
- **禁止截图测试**: 🚨 **以后都不要用截图测试，必须使用控制台打印上下坐标来定位**
- **强制坐标验证**: 🚨 **每次调整后都需要核对调整后的效果，一定要核对自己的计算方法是否正确**
- **轮廓测试参数**: 🚨 **轮廓测试使用的参数一定不要搞错，要准确识别哪个是关键参数**
- **边框调整规范**: 🚨 **调整的必须是轮廓测试能看到的边框区域，不能瞎搞**
- **工具验证成功**: 🚨 **修复成功后需要用轮廓测试工具测试无误后才是成功**

### **🚨 轮廓测试工作流程 (强制执行)**
1. **启用轮廓测试工具**: `window.debug.layout()` + `window.debug.overlap()`
2. **查看控制台坐标**: 分析打印的具体数值，不依赖视觉判断
3. **识别问题边框**: 基于轮廓测试显示的边框区域（蓝色/绿色/红色）
4. **精确计算调整**: 基于坐标数据计算需要调整的像素值
5. **针对性修复**: 只调整轮廓测试能看到的边框对应的CSS
6. **重新验证**: 使用轮廓测试工具验证修复效果
7. **确认成功**: 工具测试无误后才算真正成功

### **✅ 正确的轮廓测试方式**
```javascript
// ✅ 正确 - 轮廓测试工具标准流程
console.log('🎯 启用轮廓测试工具验证...');

// 1. 启用轮廓测试工具
window.debug.layout();   // 启用布局调试
window.debug.overlap();  // 启用重叠检测

// 2. 查找关键元素并获取坐标
const tableContainer = document.querySelector('.table-container');
const tableBody = document.querySelector('.table-body');
const lastRow = document.querySelector('.data-row:last-child');

// 3. 获取精确坐标数据
const containerRect = tableContainer.getBoundingClientRect();
const bodyRect = tableBody.getBoundingClientRect();
const lastRowRect = lastRow.getBoundingClientRect();

// 4. 打印详细坐标信息
console.log('🔵 蓝色区域 (表格容器) 坐标:');
console.log(`  下边界 (bottom): ${containerRect.bottom}px`);
console.log('🟢 绿色区域 (表格主体) 坐标:');
console.log(`  下边界 (bottom): ${bodyRect.bottom}px`);
console.log('🔴 红色区域 (最后一行) 坐标:');
console.log(`  下边界 (bottom): ${lastRowRect.bottom}px`);

// 5. 计算关键间距并验证
const greenToBlueGap = containerRect.bottom - bodyRect.bottom;
console.log(`🎯 绿色底部到蓝色底部: ${greenToBlueGap}px`);

// 6. 判断测试结果
const testPassed = Math.abs(greenToBlueGap) <= 5;
console.log(testPassed ? '✅ 轮廓测试通过' : '❌ 轮廓测试失败');
```

### **❌ 严禁的轮廓测试方式**
```javascript
// ❌ 错误 - 使用截图进行测试
browser_take_screenshot_Playwright();  // 严禁使用截图测试

// ❌ 错误 - 不使用轮廓测试工具
// 直接修改CSS而不先用工具检测问题

// ❌ 错误 - 不基于坐标数据
// 凭感觉或视觉判断进行调整

// ❌ 错误 - 调整非轮廓测试边框
margin-bottom: 80px;  // 调整了菜单边距而不是轮廓测试显示的边框

// ❌ 错误 - 修复后不验证
// 修改CSS后不重新运行轮廓测试工具验证效果
```

### **🚨 轮廓测试关键参数识别**
- **蓝色边框**: 表格容器 (`.table-container`) - 调整 `margin-bottom` 或 `padding-bottom`
- **绿色边框**: 表格主体 (`.table-body`) - 通常不需要调整
- **红色边框**: 最后一行 (`.data-row:last-child`) - 检查是否在绿色边框内
- **关键间距**: 绿色底部到蓝色底部的距离，目标值为0px (±5px容差)

### **🚨 轮廓测试边框调整规范**
```javascript
// ✅ 正确 - 基于轮廓测试结果调整对应的CSS
// 问题: 绿色区域与蓝色区域底部不对齐(-58px)
// 解决: 调整蓝色边框对应的CSS (.table-container)

// 修复前轮廓测试结果: greenToBlueGap = -58px
// 修复方案: 调整 .table-container 的 margin-bottom 和 padding-bottom
margin-bottom: -68px !important;  // 原值-10px，调整-58px = -68px
padding-bottom: 58px !important;  // 新增58px内边距，确保蓝色边框向下扩展

// 修复后轮廓测试结果: greenToBlueGap = 0px ✅
```

### **🚨 违规处理和质量保证**
- **发现违规**: 立即停止当前工作，重新学习轮廓测试规范
- **强制重做**: 使用正确的轮廓测试方法重新进行调试
- **工具验证**: 修复后必须用轮廓测试工具验证，testPassed=true才算成功
- **记录经验**: 将成功的轮廓测试案例记录到长期记忆
- **持续改进**: 不断完善轮廓测试的方法和工具

## 🔒 **安全约束**

### **🚨 代码安全要求**
- **备份确认**: 修改前确保代码已提交到git
- **回滚准备**: 每次修改都要能快速回滚
- **权限控制**: 不要修改核心系统文件
- **测试环境**: 先在开发环境测试，再部署

### **📝 文档约束**
- **修改记录**: 详细记录每次修改的内容和原因
- **问题追踪**: 记录发现的问题和解决方案
- **经验总结**: 及时更新开发规范和约束
- **知识共享**: 重要发现要及时分享

## ⚡ **性能约束**

### **🚨 性能要求**
- **加载速度**: 页面加载时间不超过3秒
- **渲染性能**: 缩放动画要流畅，不卡顿
- **内存使用**: 避免内存泄漏和过度占用
- **兼容性**: 确保在主流浏览器中正常工作

### **🔧 优化要求**
- **CSS优化**: 避免过度复杂的选择器
- **图片优化**: 使用合适的图片格式和大小
- **缓存利用**: 合理使用浏览器缓存
- **懒加载**: 对大型资源使用懒加载

## 📊 **质量约束**

### **🚨 质量标准**
- **代码质量**: 代码要清晰、可维护
- **注释完整**: 重要逻辑要有详细注释
- **命名规范**: 使用有意义的变量和类名
- **结构清晰**: 代码结构要逻辑清晰

### **✅ 验收标准**
- **功能完整**: 所有功能正常工作
- **视觉完美**: 达到设计要求
- **性能良好**: 满足性能指标
- **兼容性好**: 多浏览器兼容

## 🔄 **持续改进**

### **📈 改进机制**
- **问题收集**: 及时收集开发中遇到的问题
- **规范更新**: 根据实际情况更新约束规范
- **经验分享**: 定期分享开发经验和教训
- **工具优化**: 持续优化开发和测试工具

### **🎯 目标导向**
- **用户体验**: 始终以用户体验为中心
- **代码质量**: 追求高质量的代码
- **团队协作**: 促进团队协作和知识共享
- **持续学习**: 保持学习和改进的态度

---

## 🎯 **完美布局检查规范**

### 🚨 **零容忍标准 (Zero Tolerance Standards)**

#### **1. 零重叠 (Zero Overlap)**
- **要求**: 任何同级元素重叠都不允许，哪怕1px (父子元素除外)
- **检查方法**: 使用轮廓调试检查元素重叠
- **违规标准**: 同级元素边界重叠>0px

#### **2. 零覆盖 (Zero Coverage)**
- **要求**: 元素不能覆盖其他功能区域，保持交互完整性
- **检查方法**: 检查元素是否遮挡其他可交互元素
- **违规标准**: 功能元素被非功能元素覆盖

#### **3. 零错位 (Zero Misalignment)**
- **要求**: 元素位置必须精确对齐，误差≤1px，像素级精度
- **检查方法**: 检查元素对齐是否精确
- **违规标准**: 对齐误差>1px

#### **4. 零超界 (Zero Boundary Violation)**
- **要求**: 所有元素必须在容器边界内，不能超出视口
- **检查方法**: 确保元素不超出容器边界
- **违规标准**: 元素边界超出容器边界

#### **5. 零变形 (Zero Deformation)**
- **要求**: 缩放后元素比例必须协调，保持视觉美感
- **检查方法**: 检查不同分辨率下的元素比例
- **违规标准**: 元素变形影响可用性

#### **🆕 6. 零贴边 (Zero Edge-Touching)**
- **要求**: 所有元素与窗口边界必须保持≥10px安全边距
- **检查方法**: 使用getBoundingClientRect()检查元素边界与视口边界的距离
- **违规标准**: 元素距离窗口边界<10px
- **🚨 强制检查代码**:
```javascript
function checkElementBoundary(element) {
  const rect = element.getBoundingClientRect();
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;

  const violations = [];
  if (rect.left < 10) violations.push(`左边界过近: ${rect.left}px < 10px`);
  if (rect.right > windowWidth - 10) violations.push(`右边界过近: ${rect.right}px > ${windowWidth - 10}px`);
  if (rect.top < 10) violations.push(`上边界过近: ${rect.top}px < 10px`);
  if (rect.bottom > windowHeight - 10) violations.push(`下边界过近: ${rect.bottom}px > ${windowHeight - 10}px`);

  return violations;
}
```

#### **🆕 7. 零父子重叠 (Zero Parent-Child Overlap)**
- **要求**: 父元素不能与子元素完全重叠，必须有明确层级关系
- **检查方法**: 检查父子元素的边界关系，确保父元素边界大于子元素
- **违规标准**: 父子元素边界完全一致或父元素小于子元素
- **🚨 强制检查代码**:
```javascript
function checkParentChildOverlap(parentElement, childElement) {
  const parentRect = parentElement.getBoundingClientRect();
  const childRect = childElement.getBoundingClientRect();

  const violations = [];
  if (Math.abs(parentRect.left - childRect.left) <= 1) violations.push('父子左边界重叠');
  if (Math.abs(parentRect.right - childRect.right) <= 1) violations.push('父子右边界重叠');
  if (Math.abs(parentRect.top - childRect.top) <= 1) violations.push('父子上边界重叠');
  if (Math.abs(parentRect.bottom - childRect.bottom) <= 1) violations.push('父子下边界重叠');

  return violations;
}
```

### 📐 **边界安全规范 (Boundary Safety Standards)**

#### **最小安全边距要求**
- **元素与视口边界**: ≥10px
- **模态框与视口边界**: ≥20px
- **按钮与容器边界**: ≥15px
- **父子容器边距**: ≥5px (父容器必须比子元素大≥5px)

#### **模态框边界规范**
- **顶部边距**: ≥20px (避免贴顶)
- **底部边距**: ≥20px (避免贴底)
- **左右边距**: ≥20px (避免贴边)
- **按钮区域**: 距离模态框底部≥15px

#### **🚨 按钮边界专用检查规范 (强制执行)**
基于确认和取消按钮超出窗口边界的问题，制定专门的检查规范：

**按钮边界检查函数 (必须实现)**:
```javascript
function checkButtonBoundary() {
  const buttons = document.querySelectorAll('button[aria-label="操作按钮"], .action-btn, .footer-actions button');
  const windowWidth = window.innerWidth;
  const windowHeight = window.innerHeight;
  const violations = [];

  buttons.forEach((btn, index) => {
    const rect = btn.getBoundingClientRect();
    const btnText = btn.textContent.trim();

    // 🚨 严重违规检查 - 按钮超出窗口边界
    if (rect.bottom > windowHeight) {
      violations.push({
        type: 'CRITICAL',
        element: btn,
        message: `${btnText}按钮底部超出窗口: ${Math.round(rect.bottom)}px > ${windowHeight}px`,
        overflow: Math.round(rect.bottom - windowHeight)
      });
    }

    if (rect.right > windowWidth) {
      violations.push({
        type: 'CRITICAL',
        element: btn,
        message: `${btnText}按钮右边超出窗口: ${Math.round(rect.right)}px > ${windowWidth}px`,
        overflow: Math.round(rect.right - windowWidth)
      });
    }

    // ⚠️ 警告检查 - 按钮贴边
    if (rect.bottom > windowHeight - 10) {
      violations.push({
        type: 'WARNING',
        element: btn,
        message: `${btnText}按钮底部过近: ${Math.round(rect.bottom)}px > ${windowHeight - 10}px`,
        distance: Math.round(windowHeight - rect.bottom)
      });
    }
  });

  return violations;
}
```

**按钮父元素边界检查函数 (必须实现)**:
```javascript
function checkButtonParentBoundary() {
  const buttonContainers = document.querySelectorAll('.footer-actions, .modal-footer, .button-group');
  const violations = [];

  buttonContainers.forEach(container => {
    const containerRect = container.getBoundingClientRect();
    const buttons = container.querySelectorAll('button');

    buttons.forEach(btn => {
      const btnRect = btn.getBoundingClientRect();

      // 检查按钮是否超出父容器
      if (btnRect.bottom > containerRect.bottom + 1) {
        violations.push({
          type: 'PARENT_OVERFLOW',
          element: btn,
          parent: container,
          message: `按钮超出父容器底部: ${Math.round(btnRect.bottom)}px > ${Math.round(containerRect.bottom)}px`
        });
      }
    });

    // 检查父容器是否与窗口边界重叠
    const parentBoundaryViolations = checkElementBoundary(container);
    if (parentBoundaryViolations.length > 0) {
      violations.push({
        type: 'PARENT_BOUNDARY',
        element: container,
        message: `按钮父容器边界违规: ${parentBoundaryViolations.join(', ')}`
      });
    }
  });

  return violations;
}
```

### 🔧 **修复优先级**
1. **🚨 严重违规**: 元素超出窗口边界 - 立即修复
2. **⚠️ 警告违规**: 元素贴边(<10px) - 优先修复
3. **📋 优化建议**: 间距不够美观 - 后续优化

### 🚨 **强制边界检查流程 (每次必须执行)**

#### **完整边界检查函数 (强制实现)**
```javascript
function performCompleteBoundaryCheck() {
  console.log('🚨 开始强制边界检查流程');
  console.log('==========================================');

  const allViolations = [];

  // 1. 🚨 按钮边界检查 (最高优先级)
  console.log('🔍 1. 按钮边界检查...');
  const buttonViolations = checkButtonBoundary();
  allViolations.push(...buttonViolations);

  // 2. 🚨 按钮父元素边界检查
  console.log('🔍 2. 按钮父元素边界检查...');
  const parentViolations = checkButtonParentBoundary();
  allViolations.push(...parentViolations);

  // 3. 🚨 全局元素边界检查
  console.log('🔍 3. 全局元素边界检查...');
  const allElements = document.querySelectorAll('*:not(script):not(style):not(meta)');
  allElements.forEach(el => {
    const violations = checkElementBoundary(el);
    if (violations.length > 0) {
      allViolations.push({
        type: 'BOUNDARY',
        element: el,
        message: violations.join(', ')
      });
    }
  });

  // 4. 📊 检查结果报告
  console.log('\\n📊 边界检查结果:');
  console.log('==========================================');

  const criticalViolations = allViolations.filter(v => v.type === 'CRITICAL');
  const warningViolations = allViolations.filter(v => v.type === 'WARNING');
  const otherViolations = allViolations.filter(v => !['CRITICAL', 'WARNING'].includes(v.type));

  console.log(`🚨 严重违规: ${criticalViolations.length}个`);
  console.log(`⚠️ 警告违规: ${warningViolations.length}个`);
  console.log(`📋 其他问题: ${otherViolations.length}个`);

  // 5. 🚨 详细违规报告
  if (criticalViolations.length > 0) {
    console.log('\\n🚨 严重违规详情 (必须立即修复):');
    criticalViolations.forEach((violation, index) => {
      console.log(`  ${index + 1}. ${violation.message}`);
    });
  }

  if (warningViolations.length > 0) {
    console.log('\\n⚠️ 警告违规详情 (建议修复):');
    warningViolations.forEach((violation, index) => {
      console.log(`  ${index + 1}. ${violation.message}`);
    });
  }

  // 6. 🎯 检查结果判定
  const hasViolations = allViolations.length > 0;
  const hasCriticalViolations = criticalViolations.length > 0;

  if (!hasViolations) {
    console.log('\\n🎉 ✅ 所有边界检查通过！');
    return { status: 'PASS', violations: [] };
  } else if (hasCriticalViolations) {
    console.log('\\n❌ 🚨 存在严重边界违规，必须立即修复！');
    return { status: 'CRITICAL_FAIL', violations: allViolations };
  } else {
    console.log('\\n⚠️ 存在边界警告，建议修复');
    return { status: 'WARNING', violations: allViolations };
  }
}
```

#### **强制检查触发机制 (必须实现)**
```javascript
// 🚨 页面加载完成后自动检查
document.addEventListener('DOMContentLoaded', () => {
  setTimeout(() => {
    performCompleteBoundaryCheck();
  }, 1000);
});

// 🚨 窗口大小改变时自动检查
window.addEventListener('resize', debounce(() => {
  performCompleteBoundaryCheck();
}, 500));

// 🚨 模态框打开时自动检查
const observer = new MutationObserver((mutations) => {
  mutations.forEach((mutation) => {
    if (mutation.type === 'childList') {
      const addedNodes = Array.from(mutation.addedNodes);
      const hasModal = addedNodes.some(node =>
        node.nodeType === 1 &&
        (node.classList?.contains('modal') || node.querySelector?.('.modal'))
      );

      if (hasModal) {
        setTimeout(() => {
          performCompleteBoundaryCheck();
        }, 300);
      }
    }
  });
});

observer.observe(document.body, { childList: true, subtree: true });

// 🚨 手动检查命令 (开发时使用)
window.checkBoundary = performCompleteBoundaryCheck;
```

## 🎨 **智能美观间距系统规范**

### 🧮 **黄金比例美学算法**
基于窗口尺寸和黄金比例(1.618)的智能间距计算系统：

#### **基础单位计算公式**
```javascript
const baseUnit = Math.max(8, Math.min(24, Math.floor(availableHeight / 35)));
```

#### **间距系列生成规则**
- **XS间距**: `baseUnit * 0.5` (超小间距: 4-12px)
- **SM间距**: `baseUnit * 0.75` (小间距: 6-18px)
- **MD间距**: `baseUnit` (中等间距: 8-24px)
- **LG间距**: `baseUnit * 1.618` (大间距: 13-39px)
- **XL间距**: `baseUnit * 1.618 * 1.5` (超大间距: 19-58px)

#### **专用间距计算**
- **模态框边距**: `Math.max(20, Math.min(40, Math.floor(availableWidth / 25)))`
- **按钮间距**: `Math.max(12, Math.min(20, Math.floor(baseUnit * 1.2)))`
- **表单间距**: `Math.max(16, Math.min(28, Math.floor(baseUnit * 1.4)))`
- **区块间距**: `Math.max(24, Math.min(40, Math.floor(baseUnit * 2)))`

### 🎭 **视觉层次和节奏感规范**
基于内容关系的间距分级系统：

- **紧密关系** (`tight`): XS间距 - 标签与输入框
- **相关元素** (`related`): SM间距 - 同组表单项
- **分离元素** (`separate`): MD间距 - 不同功能区域
- **明显区分** (`distinct`): LG间距 - 主要区块分离
- **独立区块** (`isolated`): XL间距 - 完全独立的内容区

### 📐 **响应式尺寸计算规范**

#### **按钮尺寸自适应**
```javascript
buttonHeight: Math.max(36, Math.min(48, Math.floor(availableHeight / 16)))
buttonMinWidth: Math.max(80, Math.min(120, Math.floor(availableWidth / 12)))
buttonPadding: Math.max(12, Math.min(24, spacing.md))
```

#### **输入控件尺寸自适应**
```javascript
inputHeight: Math.max(40, Math.min(52, Math.floor(availableHeight / 15)))
inputPadding: Math.max(12, Math.min(20, spacing.sm))
```

#### **字体大小自适应**
```javascript
fontSize: Math.max(14, Math.min(18, Math.floor(baseUnit * 0.8)))
titleSize: Math.max(18, Math.min(24, Math.floor(baseUnit * 1.2)))
labelSize: Math.max(13, Math.min(16, Math.floor(baseUnit * 0.7)))
```

### 🎨 **CSS变量系统规范**
必须定义的CSS变量：

```css
:root {
  --spacing-xs: [计算值]px;
  --spacing-sm: [计算值]px;
  --spacing-md: [计算值]px;
  --spacing-lg: [计算值]px;
  --spacing-xl: [计算值]px;
  --rhythm-tight: [计算值]px;
  --rhythm-related: [计算值]px;
  --rhythm-separate: [计算值]px;
  --rhythm-distinct: [计算值]px;
  --rhythm-isolated: [计算值]px;
}
```

### 🏗️ **模态框布局强制规范**

#### **边界安全约束**
- **顶部边距**: `spacing.modal`px
- **左侧边距**: `sidebarWidth + spacing.modal`px
- **右侧边距**: `spacing.modal`px
- **底部边距**: `spacing.modal`px

#### **高度分配规则**
- **模态框最大高度**: `calc(100vh - spacing.modal * 3)px`
- **头部固定高度**: `60px`
- **按钮区域固定高度**: `80px`
- **主体最大高度**: `calc(100vh - 220px)` (为头部和按钮预留空间)

#### **Flexbox布局要求**
```css
.service-form-modal {
  display: flex !important;
  flex-direction: column !important;
}

.modal-header {
  flex-shrink: 0 !important;
}

.modal-body {
  flex: 1 !important;
  overflow-y: auto !important;
}

.footer-actions {
  flex-shrink: 0 !important;
  position: sticky !important;
  bottom: 0 !important;
}
```

### 🎯 **智能间距应用流程**

#### **1. 窗口分析阶段**
```javascript
const windowWidth = window.innerWidth;
const windowHeight = window.innerHeight;
const availableWidth = windowWidth - sidebarWidth;
const availableHeight = windowHeight;
```

#### **2. 基础单位计算阶段**
```javascript
const baseUnit = Math.max(8, Math.min(24, Math.floor(availableHeight / 35)));
const goldenRatio = 1.618;
```

#### **3. 间距系列生成阶段**
根据基础单位和黄金比例生成完整间距系列

#### **4. CSS样式注入阶段**
动态创建并注入自适应样式表

#### **5. 效果验证阶段**
验证所有元素是否符合边界安全规范

### 📊 **质量检查标准**

#### **必须通过的检查项**
1. **边界安全检查**: 所有按钮底部 ≤ `windowHeight - 5px`
2. **间距一致性检查**: 同类元素间距误差 ≤ 2px
3. **视觉协调检查**: 间距比例符合黄金比例规律
4. **响应式适配检查**: 不同窗口尺寸下布局保持美观

#### **性能要求**
- **样式注入时间**: ≤ 100ms
- **布局重排次数**: ≤ 1次
- **视觉稳定性**: 无闪烁或跳动

---

**这些约束是血的教训总结，必须严格遵守！违反约束可能导致严重后果！** 🚨
