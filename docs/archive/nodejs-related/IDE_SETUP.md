# IDE 开发环境配置指南

> 🎯 **目标**: 配置IDE以正确使用 pyenv 和 nvm 管理的开发环境

## 📋 环境信息

### **当前项目环境**
- **Python**: 3.9.6 (通过 pyenv 管理)
- **Node.js**: v22.14.0 (通过 nvm 管理)
- **项目路径**: `/Users/<USER>/Documents/wechatcloud`

### **版本配置文件**
- `.python-version`: 指定 Python 版本
- `.nvmrc`: 指定 Node.js 版本

## 🔧 IDE 配置

### **PyCharm 配置**

#### **1. Python 解释器设置**
1. 打开 `File` → `Settings` → `Project` → `Python Interpreter`
2. 点击齿轮图标 → `Add...`
3. 选择 `Existing environment`
4. 设置解释器路径：
   ```
   /Users/<USER>/Documents/wechatcloud/server/venv/bin/python
   ```

#### **2. Node.js 配置**
1. 打开 `File` → `Settings` → `Languages & Frameworks` → `Node.js`
2. 设置 Node.js 路径：
   ```
   ~/.nvm/versions/node/v22.14.0/bin/node
   ```
3. 设置 npm 路径：
   ```
   ~/.nvm/versions/node/v22.14.0/bin/npm
   ```

### **VS Code 配置**

#### **1. Python 解释器设置**
1. 按 `Cmd+Shift+P` 打开命令面板
2. 输入 `Python: Select Interpreter`
3. 选择或输入路径：
   ```
   /Users/<USER>/Documents/wechatcloud/server/venv/bin/python
   ```

#### **2. 工作区设置 (.vscode/settings.json)**
```json
{
    "python.defaultInterpreterPath": "./server/venv/bin/python",
    "python.terminal.activateEnvironment": true,
    "nodejs.referencesCodeLens.enabled": true,
    "typescript.preferences.includePackageJsonAutoImports": "on"
}
```

### **DataGrip 数据库配置**

#### **连接信息**
```
数据库类型: MySQL
主机: sh-cynosdbmysql-grp-l681flue.sql.tencentcdb.com
端口: 25524
数据库: wechat_dev
用户名: root
密码: Yixintang2025
```

#### **JDBC URL**
```
*************************************************************************************************************************************************
```

## 🚀 环境验证

### **自动设置脚本**
```bash
# 运行环境设置脚本
./scripts/setup-dev-env.sh
```

### **手动验证**
```bash
# 检查 Python 版本
python3 --version  # 应该显示 Python 3.9.6

# 检查 Node.js 版本
node --version      # 应该显示 v22.14.0

# 检查虚拟环境
source server/venv/bin/activate
which python        # 应该指向项目虚拟环境

# 检查 nvm 当前版本
nvm current         # 应该显示 22.14.0
```

## ⚠️ 常见问题解决

### **问题1: Python解释器路径错误**
```
错误: Cannot run program "/Users/<USER>/Documents/other_project/.venv/bin/python"
```

**解决方案:**
1. 重新配置IDE的Python解释器
2. 确保路径指向当前项目的虚拟环境
3. 路径应该是: `/Users/<USER>/Documents/wechatcloud/server/venv/bin/python`

### **问题2: Node.js版本不匹配**
```bash
# 检查当前版本
nvm current

# 切换到项目版本
nvm use 22.14.0

# 如果版本未安装
nvm install 22.14.0
nvm use 22.14.0
```

### **问题3: 虚拟环境未激活**
```bash
# 创建虚拟环境
cd server
python -m venv venv

# 激活虚拟环境
source venv/bin/activate

# 安装依赖
pip install -r requirements.txt
```

## 📝 开发工作流

### **每次开发前的检查**
1. 确认在正确的项目目录
2. 检查Python版本: `python3 --version`
3. 检查Node.js版本: `node --version`
4. 激活虚拟环境: `source server/venv/bin/activate`
5. 启动开发服务: `./start-all-dev.sh`

### **IDE重启后的设置**
1. 重新选择Python解释器
2. 确认Node.js路径正确
3. 重新连接数据库（如果需要）

## 🎯 最佳实践

1. **使用项目配置文件**: 依赖 `.python-version` 和 `.nvmrc`
2. **自动化设置**: 使用 `setup-dev-env.sh` 脚本
3. **环境隔离**: 每个项目使用独立的虚拟环境
4. **版本锁定**: 确保团队使用相同的Python和Node.js版本
