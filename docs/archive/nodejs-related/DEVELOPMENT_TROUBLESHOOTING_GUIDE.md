# 开发问题解决指南

> 📋 **文档目的**: 整合常见开发问题的解决方案和最佳实践
> 📅 **最后更新**: 2025-07-17

## 📋 目录

1. [环境配置问题](#环境配置问题)
2. [端口冲突处理](#端口冲突处理)
3. [代理配置问题](#代理配置问题)
4. [热重载问题](#热重载问题)
5. [命名一致性问题](#命名一致性问题)
6. [服务编辑问题](#服务编辑问题)
7. [部署相关问题](#部署相关问题)

---

## 🔧 环境配置问题

### **Python环境问题**
```bash
# 问题：Python版本不匹配
# 解决：使用pyenv管理Python版本
pyenv install 3.11.0
pyenv local 3.11.0

# 问题：虚拟环境损坏
# 解决：重新创建虚拟环境
rm -rf server/venv
cd server
python -m venv venv
source venv/bin/activate
pip install -r requirements.txt
```

### **Node.js环境问题**
```bash
# 问题：Node.js版本不匹配
# 解决：使用nvm管理Node.js版本
nvm install 18.17.0
nvm use 18.17.0

# 问题：依赖安装失败
# 解决：清理缓存重新安装
cd admin
rm -rf node_modules package-lock.json
npm cache clean --force
npm install
```

### **数据库连接问题**
```python
# 问题：数据库连接失败
# 解决：检查数据库配置
# server/local_settings.py
DATABASES = {
    'default': {
        'ENGINE': 'django.db.backends.sqlite3',
        'NAME': BASE_DIR / 'db.sqlite3',
    }
}

# 重新迁移数据库
python manage.py makemigrations
python manage.py migrate
```

---

## 🔌 端口冲突处理

### **自动端口冲突检测**
```bash
# 检查端口占用
lsof -ti:8000 | xargs kill -9  # 清理8000端口
lsof -ti:3000 | xargs kill -9  # 清理3000端口
lsof -ti:10086 | xargs kill -9 # 清理10086端口

# 使用一键启动脚本自动处理
./start-all-dev.sh  # 自动检测和清理端口冲突
```

### **手动端口配置**
```javascript
// admin/vite.config.js - 前端端口配置
export default defineConfig({
  server: {
    port: 3000,
    host: '0.0.0.0',
    strictPort: true  // 端口被占用时报错而不是自动切换
  }
});
```

```python
# server/dev.py - 后端端口配置
if __name__ == "__main__":
    import os
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'wxcloudrun.settings')
    
    from django.core.management import execute_from_command_line
    execute_from_command_line(['manage.py', 'runserver', '0.0.0.0:8000'])
```

---

## 🌐 代理配置问题

### **Vite代理配置**
```javascript
// admin/vite.config.js
export default defineConfig({
  server: {
    proxy: {
      '/api': {
        target: 'http://localhost:8000',
        changeOrigin: true,
        secure: false,
        configure: (proxy, options) => {
          proxy.on('error', (err, req, res) => {
            console.log('proxy error', err);
          });
          proxy.on('proxyReq', (proxyReq, req, res) => {
            console.log('Sending Request to the Target:', req.method, req.url);
          });
        }
      }
    }
  }
});
```

### **Django CORS配置**
```python
# server/settings.py
CORS_ALLOWED_ORIGINS = [
    "http://localhost:3000",
    "http://127.0.0.1:3000",
]

CORS_ALLOW_CREDENTIALS = True

CORS_ALLOW_ALL_ORIGINS = True  # 仅开发环境使用
```

### **API路径一致性**
```javascript
// 前端API调用
const API_BASE = '/api';  // 使用相对路径，通过代理转发

// 正确的API调用方式
export const getServices = () => {
  return axios.get(`${API_BASE}/services/`);
};

// 错误的API调用方式 - 避免硬编码完整URL
// return axios.get('http://localhost:8000/api/services/');
```

---

## 🔥 热重载问题

### **前端热重载配置**
```javascript
// admin/vite.config.js
export default defineConfig({
  server: {
    hmr: {
      overlay: true  // 显示错误覆盖层
    },
    watch: {
      usePolling: true,  // 在某些系统上需要轮询
      interval: 1000
    }
  }
});
```

### **后端热重载配置**
```python
# server/dev.py - 开发服务器配置
import os
import sys
from django.core.management import execute_from_command_line

if __name__ == "__main__":
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'wxcloudrun.settings')
    
    # 启用自动重载
    sys.argv = ['manage.py', 'runserver', '--noreload']  # 禁用自动重载
    # 或者
    sys.argv = ['manage.py', 'runserver']  # 启用自动重载
    
    execute_from_command_line(sys.argv)
```

### **文件监控优化**
```bash
# 增加文件监控限制 (macOS)
echo fs.inotify.max_user_watches=524288 | sudo tee -a /etc/sysctl.conf
sudo sysctl -p

# 或者使用环境变量
export CHOKIDAR_USEPOLLING=true
```

---

## 📝 命名一致性问题

### **前后端字段映射**
```python
# 后端模型定义
class Service(models.Model):
    name = models.CharField(max_length=100)
    price = models.DecimalField(max_digits=10, decimal_places=2)
    commission = models.DecimalField(max_digits=10, decimal_places=2)  # 统一使用commission
    duration = models.IntegerField()
    is_active = models.BooleanField(default=True)
```

```javascript
// 前端数据结构
const serviceData = {
  name: '',
  price: 0,
  commission: 0,  // 与后端保持一致，使用commission而不是technician_price
  duration: 0,
  is_active: true
};
```

### **API接口命名规范**
```python
# 后端API视图
class ServiceViewSet(viewsets.ModelViewSet):
    def update_commission(self, request, pk=None):  # 使用commission
        """更新服务提成"""
        pass
    
    def get_commission_history(self, request, pk=None):  # 使用commission
        """获取提成历史记录"""
        pass
```

```javascript
// 前端API调用
export const updateCommission = (serviceId, commission) => {
  return axios.patch(`/api/services/${serviceId}/update_commission/`, {
    commission: commission  // 使用commission字段名
  });
};
```

---

## ✏️ 服务编辑问题

### **模态框状态管理**
```javascript
// 正确的模态框状态管理
const modalState = reactive({
  visible: false,
  mode: 'add', // 'add' | 'edit'
  editingId: null
});

const showEditModal = (service) => {
  modalState.mode = 'edit';
  modalState.editingId = service.id;
  modalState.visible = true;
  
  // 重要：深拷贝数据，避免直接修改原数据
  Object.assign(formState, JSON.parse(JSON.stringify(service)));
};

const hideModal = () => {
  modalState.visible = false;
  modalState.mode = 'add';
  modalState.editingId = null;
  
  // 清空表单数据
  Object.assign(formState, getDefaultFormState());
};
```

### **表单验证处理**
```javascript
// 统一的表单验证
const validateForm = () => {
  // 清空之前的错误
  Object.assign(formErrors, {
    name: '',
    price: '',
    commission: ''
  });

  let isValid = true;

  if (!formState.name?.trim()) {
    formErrors.name = '请输入服务名称';
    isValid = false;
  }

  if (!formState.price || formState.price <= 0) {
    formErrors.price = '请输入有效的服务费用';
    isValid = false;
  }

  if (!formState.commission || formState.commission <= 0) {
    formErrors.commission = '请输入有效的提成金额';
    isValid = false;
  }

  if (formState.commission > formState.price) {
    formErrors.commission = '提成不能超过服务费用';
    isValid = false;
  }

  return isValid;
};
```

---

## 🚀 部署相关问题

### **Docker构建问题**
```dockerfile
# server/Dockerfile - 优化的Docker配置
FROM python:3.11-slim

WORKDIR /app

# 复制依赖文件
COPY requirements.txt .

# 安装依赖
RUN pip install --no-cache-dir -r requirements.txt

# 复制应用代码
COPY . .

# 收集静态文件
RUN python manage.py collectstatic --noinput

# 暴露端口
EXPOSE 8000

# 启动命令
CMD ["python", "manage.py", "runserver", "0.0.0.0:8000"]
```

### **环境变量配置**
```python
# server/settings.py - 生产环境配置
import os

DEBUG = os.getenv('DEBUG', 'False').lower() == 'true'

ALLOWED_HOSTS = os.getenv('ALLOWED_HOSTS', 'localhost').split(',')

# 数据库配置
if os.getenv('DATABASE_URL'):
    import dj_database_url
    DATABASES = {
        'default': dj_database_url.parse(os.getenv('DATABASE_URL'))
    }
```

### **静态文件处理**
```python
# server/settings.py - 静态文件配置
STATIC_URL = '/static/'
STATIC_ROOT = os.path.join(BASE_DIR, 'staticfiles')

STATICFILES_DIRS = [
    os.path.join(BASE_DIR, 'static'),
]

# 媒体文件配置
MEDIA_URL = '/media/'
MEDIA_ROOT = os.path.join(BASE_DIR, 'media')
```

---

## 🔍 调试技巧

### **前端调试**
```javascript
// 使用Vue DevTools
// 在组件中添加调试信息
const debugInfo = computed(() => ({
  formState: formState,
  modalState: modalState,
  loadingStates: loadingStates
}));

// 在模板中显示调试信息（开发环境）
// <pre v-if="isDev">{{ JSON.stringify(debugInfo, null, 2) }}</pre>
```

### **后端调试**
```python
# server/settings.py - 开发环境日志配置
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
        },
    },
    'loggers': {
        'django': {
            'handlers': ['console'],
            'level': 'INFO',
        },
        'api': {
            'handlers': ['console'],
            'level': 'DEBUG',
        },
    },
}

# 在视图中使用日志
import logging
logger = logging.getLogger('api')

def my_view(request):
    logger.debug(f"Request data: {request.data}")
    # ... 处理逻辑
    logger.info(f"Response: {response_data}")
```

---

**📋 文档版本**: v1.0  
**👥 维护团队**: 壹心堂开发团队  
**🔄 更新频率**: 根据问题发现及时更新
