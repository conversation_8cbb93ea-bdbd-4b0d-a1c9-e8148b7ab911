# ⚠️ 已归档文档 - 仅供历史参考

> **归档日期**: 2025-01-20
> **归档原因**: 包含已弃用的Node.js相关内容
> **替代文档**: [CURRENT_DEVELOPMENT_GUIDE.md](../../CURRENT_DEVELOPMENT_GUIDE.md)
> **状态**: 不再维护，仅供历史参考

---

# 壹心堂项目故障排除指南 (已归档)

## 🚨 常见问题快速解决

### 🔧 环境配置问题

#### 问题1: Node.js 命令找不到
```bash
# 错误信息
bash: npm: command not found
bash: node: command not found

# 解决方案
source ~/.zshrc && which node
# 如果还是找不到，重新安装 nvm
curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.39.0/install.sh | bash
source ~/.zshrc
nvm install 22.14.0
nvm use 22.14.0
```

#### 问题2: Vue 版本冲突
```bash
# 错误信息
Vue packages version mismatch:
- vue@3.5.17
- vue-template-compiler@2.7.16

# 解决方案 (在 client 目录下执行)
npm uninstall vue-template-compiler vue-loader
rm -rf node_modules package-lock.json
npm install
```

#### 问题3: Python 虚拟环境冲突
```bash
# 错误信息
ModuleNotFoundError: No module named 'django'

# 解决方案
cd server
source venv/bin/activate
pip install -r requirements.txt
```

### 🎨 前端开发问题

#### 问题4: Taro 编译失败
```bash
# 错误信息
Error: Cannot resolve module '@tarojs/webpack5-runner'

# 解决方案
cd client
npm install @tarojs/webpack5-runner
npm run dev:weapp
```

#### 问题5: API 导入错误
```javascript
// 错误信息
No matching export in "src/api/cloud-api.js" for import "employeesApi"

// 解决方案 - 在 api/index.js 中
import {
  therapistsApi as employeesApi,
  healthTipsApi,
  // ...其他导入
} from './cloud-api'
```

### 🗄️ 数据库问题

#### 问题6: MySQL 连接失败
```python
# 错误信息
django.db.utils.OperationalError: (2003, "Can't connect to MySQL server")

# 解决方案 - 检查环境配置
# 开发环境使用 SQLite
ENVIRONMENT=development python manage.py runserver

# 生产环境检查 MySQL 配置
MYSQL_PASSWORD=Yixintang2025 python manage.py runserver
```

### 🔌 VS Code 扩展问题

#### 问题7: 扩展不显示
```bash
# 检查已安装扩展
code --list-extensions | wc -l

# 重新加载 VS Code
Cmd+Shift+P → "Developer: Reload Window"
```

## 🛠️ 诊断工具

### 环境检查脚本
```bash
#!/bin/bash
echo "=== 环境诊断 ==="
echo "Node.js: $(node --version 2>/dev/null || echo '未安装')"
echo "npm: $(npm --version 2>/dev/null || echo '未安装')"
echo "Python: $(python --version 2>/dev/null || echo '未安装')"
echo "Git: $(git --version 2>/dev/null || echo '未安装')"

echo "=== 项目状态 ==="
echo "后端服务: $(curl -s http://localhost:8000/health/ || echo '未运行')"
echo "管理后台: $(curl -s http://localhost:3000/ | head -1 || echo '未运行')"

echo "=== VS Code 扩展 ==="
echo "已安装扩展数量: $(code --list-extensions | wc -l)"
```

### 一键重置脚本
```bash
#!/bin/bash
echo "🔄 重置开发环境..."

# 重置前端
cd client
echo "清理前端依赖..."
rm -rf node_modules package-lock.json
npm install

# 重置后端
cd ../server
echo "重置后端环境..."
source venv/bin/activate
pip install -r requirements.txt
python manage.py migrate

echo "✅ 环境重置完成！"
```

## 📞 技术支持

### 联系方式
- **开发团队**: 内部技术支持
- **文档更新**: 发现问题请及时更新本文档

### 问题报告模板
```markdown
## 问题描述
[详细描述遇到的问题]

## 错误信息
```
[粘贴完整的错误信息]
```

## 环境信息
- 操作系统: 
- Node.js 版本: 
- Python 版本: 
- 项目分支: 

## 重现步骤
1. 
2. 
3. 

## 期望结果
[描述期望的正确行为]
```

---

**最后更新**: 2025-07-05
**维护者**: 开发团队
