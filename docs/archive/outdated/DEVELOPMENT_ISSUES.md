# 壹心堂项目开发问题记录与解决方案

## 📋 问题分类

### 🔧 环境配置问题

#### 1. Node.js 环境路径问题

**问题描述：**

- 终端中找不到 `npm` 和 `node` 命令
- PATH 环境变量未正确配置

**解决方案：**

```bash
# 检查 Node.js 安装
source ~/.zshrc && which node

# 如果使用 nvm，确保正确加载
echo 'export NVM_DIR="$HOME/.nvm"' >> ~/.zshrc
echo '[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"' >> ~/.zshrc
echo '[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"' >> ~/.zshrc

# 重新加载配置
source ~/.zshrc
```

**预防措施：**

- 在项目根目录创建 `.nvmrc` 文件指定 Node.js 版本
- 在启动脚本中自动检查和设置环境

#### 2. Python 虚拟环境冲突

**问题描述：**

- VS Code 自动激活了错误的虚拟环境
- PATH 中包含多个 Python 环境路径

**解决方案：**

```bash
# 明确指定正确的虚拟环境
export PYTHONPATH="/Users/<USER>/Documents/wechatcloud/server"
source /Users/<USER>/Documents/wechatcloud/server/venv/bin/activate
```

### 🎨 前端开发问题

#### 3. Vue 版本冲突 (关键问题)

**问题描述：**

```
Vue packages version mismatch:
- vue@3.5.17
- vue-template-compiler@2.7.16
```

**根本原因：**

- Taro Vue 3 项目中残留了 Vue 2 的 `vue-template-compiler`
- Vue 3 应该使用 `@vue/compiler-sfc` 而不是 `vue-template-compiler`

**解决方案：**

```bash
# 1. 完全移除 vue-template-compiler
npm uninstall vue-template-compiler vue-loader

# 2. 清理 node_modules 和 package-lock.json
rm -rf node_modules package-lock.json

# 3. 重新安装依赖
npm install

# 4. 如果问题持续，检查是否有隐藏的依赖
npm ls vue-template-compiler
```

**预防措施：**

- 在 `package.json` 中明确排除 Vue 2 相关包
- 使用 `.npmrc` 配置文件锁定包版本

#### 4. Taro 编译依赖缺失

**问题描述：**

- 缺少 `@tarojs/webpack5-runner` 等编译依赖
- 编译过程中断

**解决方案：**

```bash
# 安装缺失的 Taro 依赖
npm install @tarojs/webpack5-runner
npm install @tarojs/plugin-framework-vue3
```

#### 5. API 导入导出不匹配

**问题描述：**

- `cloud-api.js` 导出的是 `therapistsApi`
- `index.js` 导入的是 `employeesApi`
- 导致模块找不到错误

**解决方案：**

```javascript
// 在 index.js 中使用别名导入
import {
  therapistsApi as employeesApi,
  // ...其他导入
} from './cloud-api'
```

### 🔌 VS Code 扩展问题

#### 6. 扩展安装后不显示

**问题描述：**

- 命令行显示 33 个扩展已安装
- VS Code 扩展面板只显示 22 个

**原因分析：**

- 依赖扩展（如 `cweijan.dbclient-jdbc`）不单独显示
- 扩展面板筛选器设置
- VS Code 缓存问题

**解决方案：**

```bash
# 重新加载 VS Code 窗口
Cmd+Shift+P → "Developer: Reload Window"

# 检查扩展面板筛选器
# 确保选择 "显示已安装的扩展"
```

### 🗄️ 数据库连接问题

#### 7. MySQL 连接配置

**问题描述：**

- 开发环境和生产环境数据库配置混乱
- 连接参数不正确

**解决方案：**

```python
# 在 settings.py 中明确区分环境
if os.getenv('ENVIRONMENT') == 'production':
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.mysql',
            'HOST': '*************',
            'PORT': '3306',
            'NAME': 'yixintang_prod',
            'USER': 'root',
            'PASSWORD': os.getenv('MYSQL_PASSWORD', 'Yixintang2025'),
        }
    }
else:
    DATABASES = {
        'default': {
            'ENGINE': 'django.db.backends.sqlite3',
            'NAME': BASE_DIR / 'db.sqlite3',
        }
    }
```

## 🛠️ 开发规范更新

### 环境管理规范

1. **Node.js 版本管理**
   - 使用 `.nvmrc` 文件锁定版本
   - 启动脚本自动检查 Node.js 版本

2. **Python 环境管理**
   - 明确指定虚拟环境路径
   - 避免全局 Python 包污染

3. **依赖管理规范**
   - 定期清理 `node_modules`
   - 使用 `package-lock.json` 锁定版本
   - 避免混用 Vue 2 和 Vue 3 依赖

### 代码质量规范

1. **导入导出一致性**
   - API 模块命名保持一致
   - 使用 TypeScript 进行类型检查

2. **错误处理规范**
   - 所有异步操作必须有错误处理
   - 用户友好的错误提示

3. **测试规范**
   - 每个模块都要有对应的测试文件
   - 使用模拟数据进行开发测试

## 🚨 常见错误及快速解决

### 快速诊断命令

```bash
# 检查 Node.js 环境
source ~/.zshrc && node --version && npm --version

# 检查 Python 环境
python --version && pip --version

# 检查项目依赖
cd client && npm ls --depth=0
cd server && pip list

# 检查服务状态
curl -s http://localhost:8000/health/
curl -s http://localhost:3000/
```

### 一键重置环境

```bash
# 重置前端环境
cd client
rm -rf node_modules package-lock.json
npm install

# 重置后端环境
cd server
pip install -r requirements.txt
python manage.py migrate
```

## 📚 参考文档

- [Taro Vue 3 官方文档](https://docs.taro.zone/docs/vue3)
- [Django REST Framework](https://www.django-rest-framework.org/)
- [Vue 3 迁移指南](https://v3-migration.vuejs.org/)
- [微信小程序开发文档](https://developers.weixin.qq.com/miniprogram/dev/framework/)

## 🔄 问题跟踪

| 问题ID | 状态 | 优先级 | 描述 | 解决时间 |
|--------|------|--------|------|----------|
| DEV-001 | ✅ 已解决 | 高 | Vue 版本冲突 | 2025-07-05 |
| DEV-002 | ✅ 已解决 | 中 | Node.js 环境配置 | 2025-07-05 |
| DEV-003 | ✅ 已解决 | 中 | API 导入导出不匹配 | 2025-07-05 |
| DEV-004 | ✅ 已解决 | 低 | VS Code 扩展显示 | 2025-07-05 |
| DEV-005 | 🔄 进行中 | 高 | Taro Webpack ProgressPlugin 错误 | 2025-07-05 |

## 📋 新发现问题详情

### DEV-005: Taro Webpack ProgressPlugin 错误

**问题描述：**

- Taro 3.6.37 与 webpack 4.x 存在兼容性问题
- ProgressPlugin.js 中 `compilation.hooks[name].intercept` 报错
- 错误信息：`Cannot read properties of undefined (reading 'intercept')`

**根本原因：**

- Taro 内部使用的 webpack ProgressPlugin 与当前 webpack 版本不兼容
- Node.js v22.14.0 可能与 Taro 3.6.37 存在兼容性问题

**临时解决方案：**

```bash
# 降级 Node.js 版本 (如果需要)
nvm install 18.19.0
nvm use 18.19.0

# 或者等待 Taro 更新版本
npm update @tarojs/cli
```

**长期解决方案：**

- 升级到 Taro 4.x (支持 webpack 5)
- 或使用 Vite 作为构建工具
- 考虑迁移到 uni-app 或原生小程序开发

**当前状态：** 🔄 进行中 - 小程序编译暂时无法完成

---

**最后更新：** 2025-07-05
**维护者：** 开发团队
**版本：** 1.1.0
