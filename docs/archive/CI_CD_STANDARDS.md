# 壹心堂开发规范约束文档 v2.0

> 🚨 **强制执行规范** - 涵盖开发、测试、部署全流程的标准化约束
> 📋 **适用范围** - 壹心堂中医推拿管理系统所有开发活动
> 🎯 **目标** - 确保代码质量、开发效率和系统稳定性
> 📅 **更新日期** - 2025-01-17 (基于100%规范合规率实践经验)

## 🏆 规范合规率要求

### **📊 质量标准**
- **规范合规率**: 必须达到 **100% (6/6)**
- **功能测试通过率**: 必须达到 **100%**
- **代码质量得分**: 建议达到 **60%+** (企业级标准)
- **性能优化得分**: 建议达到 **40%+**
- **可访问性得分**: 建议达到 **30%+**

### **🔍 强制检查项目**
1. ✅ **Try-Catch块完整性** - 所有异步操作必须有错误处理
2. ✅ **错误处理规范** - 统一的错误处理和用户反馈
3. ✅ **表单验证规范** - 完整的表单验证和错误提示
4. ✅ **加载状态管理** - 所有异步操作必须有加载状态
5. ✅ **用户反馈机制** - 统一的Toast通知系统
6. ✅ **响应式设计** - 支持PC端（桌面端）多分辨率适配

## 🚨 强制约定 - 每次开发必须遵守

### **📋 开发前置检查清单 (强制执行)**
每次开发任务开始前，必须完成以下检查：

- [ ] **阅读相关规范** - 熟悉本文档和[目录结构规范](../PROJECT_STRUCTURE_STANDARDS.md)
- [ ] **运行规范检查** - `python project_check.py --mode workflow`
- [ ] **检查现有代码** - 了解当前实现和代码结构
- [ ] **确认需求理解** - 明确功能需求和技术要求
- [ ] **制定实施计划** - 详细的开发步骤和验证方案
- [ ] **准备测试用例** - 功能测试和边界测试场景
- [ ] **确认设计规范** - UI/UX设计和交互规范
- [ ] **🚫 确认不使用批量修改脚本** - 承诺手动逐个修改文件
- [ ] **📁 确认文件放置位置** - 遵循目录结构规范
- [ ] **📚 查阅MCP最佳实践** - 使用Context7工具获取最新技术文档

## 📚 MCP最佳实践集成 (基于Context7文档)

### **🔧 Vue.js开发规范 (来源: Vue官方文档)**

#### **组件命名规范**
- [ ] **多词组件名** - 避免与HTML元素冲突 (如: `TodoItem` 而非 `Item`)
- [ ] **PascalCase文件名** - SFC文件使用PascalCase命名 (如: `MyComponent.vue`)
- [ ] **基础组件前缀** - 使用 `Base`、`App` 或 `V` 前缀 (如: `BaseButton.vue`)
- [ ] **紧密耦合组件** - 子组件以父组件名为前缀 (如: `TodoList.vue`, `TodoListItem.vue`)

#### **模板语法规范**
- [ ] **指令缩写一致性** - 统一使用 `:` 和 `@` 缩写或完整形式
- [ ] **属性值引号** - 非空HTML属性值必须加引号
- [ ] **多属性换行** - 多个属性时每个属性占一行
- [ ] **组件自闭合** - SFC中无内容组件使用自闭合标签

#### **样式规范**
- [ ] **作用域样式** - 使用 `<style scoped>` 或CSS Modules
- [ ] **类选择器优先** - scoped样式中优先使用类选择器而非元素选择器
- [ ] **深度选择器** - 使用 `:deep()` 访问子组件样式
- [ ] **组件顺序** - 统一 `<script>`, `<template>`, `<style>` 顺序

### **🔧 开发过程强制要求 (基于实践经验)**

#### **1. 错误处理规范 (强制)**
```javascript
// ✅ 正确示例 - 完整的错误处理
const handleSubmit = async () => {
  try {
    loadingStates.submitLoading = true;

    // 表单验证
    if (!validateForm()) {
      showToast('请检查输入内容', 'error');
      return;
    }

    // API调用
    await apiCall();
    showToast('操作成功', 'success');

  } catch (error) {
    console.error('操作失败:', error);
    showToast('操作失败，请重试', 'error');
  } finally {
    loadingStates.submitLoading = false;
  }
};
```

#### **2. 表单验证规范 (强制)**
```javascript
// ✅ 正确示例 - 完整的表单验证
const formErrors = reactive({
  name: '',
  phone: '',
  email: ''
});

const validateForm = () => {
  // 清空之前的错误
  Object.assign(formErrors, { name: '', phone: '', email: '' });

  let isValid = true;

  if (!formState.name?.trim()) {
    formErrors.name = '请输入姓名';
    isValid = false;
  }

  if (!formState.phone?.trim()) {
    formErrors.phone = '请输入电话';
    isValid = false;
  } else if (!/^1[3-9]\d{9}$/.test(formState.phone)) {
    formErrors.phone = '请输入正确的手机号码';
    isValid = false;
  }

  return isValid;
};
```

#### **3. Toast通知系统 (强制)**
```javascript
// ✅ 正确示例 - 统一的Toast通知
const toastState = reactive({
  visible: false,
  message: '',
  type: 'success' // success, error, warning
});

const showToast = (message, type = 'success') => {
  toastState.message = message;
  toastState.type = type;
  toastState.visible = true;

  setTimeout(() => {
    toastState.visible = false;
  }, 3000);
};
```

#### **4. 加载状态管理 (强制)**
```javascript
// ✅ 正确示例 - 完整的加载状态
const loadingStates = reactive({
  submitLoading: false,
  dataLoading: false,
  deleteLoading: false
});

// 按钮禁用控制
<button
  @click="handleSubmit"
  :disabled="loadingStates.submitLoading"
  :class="{ 'disabled': loadingStates.submitLoading }"
>
  {{ loadingStates.submitLoading ? '提交中...' : '提交' }}
</button>
```

#### **5. PC端自适应缩放规范 (强制)**
```css
/* ✅ 正确示例 - PC端自适应缩放 */

/* 根容器自适应缩放 */
.app-container {
  /* 基于视口宽度的缩放比例 */
  --scale-factor: clamp(0.8, calc(100vw / 1366), 1.2);
  transform: scale(var(--scale-factor));
  transform-origin: top left;
  width: calc(100% / var(--scale-factor));
  height: calc(100% / var(--scale-factor));
}

/* 标准桌面端 (1366px及以上) - 100%缩放 */
@media (min-width: 1366px) {
  .app-container {
    --scale-factor: 1;
  }

  .container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
  }
}

/* 小屏幕桌面端 (1024px - 1365px) - 自动缩放 */
@media (min-width: 1024px) and (max-width: 1365px) {
  .app-container {
    --scale-factor: calc(100vw / 1366);
  }

  .container {
    padding: 15px;
  }

  /* 确保元素不重叠 */
  .table-wrapper {
    min-width: 1000px;
    overflow: visible;
  }
}

/* 大屏幕桌面端 (1920px及以上) - 限制最大缩放 */
@media (min-width: 1920px) {
  .app-container {
    --scale-factor: clamp(1, calc(100vw / 1366), 1.2);
  }

  .container {
    max-width: 1600px;
    margin: 0 auto;
  }
}

/* 强制防重叠样式 */
.no-overlap {
  position: relative;
  z-index: auto;
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

/* 元素间距保护 */
.element-spacing {
  margin: 8px 0;
  padding: 4px;
  min-height: 32px;
}
```

#### **6. 性能优化规范 (建议)**
```javascript
// ✅ 正确示例 - 性能优化模式
import { ref, reactive, shallowRef, nextTick, watchEffect } from 'vue';

// 使用shallowRef优化大数据渲染
const largeDataList = shallowRef([]);

// 防抖函数优化
let debounceTimer = null;
const debouncedSearch = (query) => {
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  debounceTimer = setTimeout(() => {
    performSearch(query);
  }, 300);
};

// 性能监控
const trackPerformance = (operation, startTime) => {
  const endTime = performance.now();
  const duration = endTime - startTime;
  if (duration > 100) {
    console.warn(`性能警告: ${operation} 耗时 ${duration.toFixed(2)}ms`);
  }
};
```

#### **7. 可访问性规范 (建议)**
```html
<!-- ✅ 正确示例 - 可访问性属性 -->
<input
  type="text"
  v-model="formState.name"
  class="form-input"
  placeholder="请输入姓名"
  aria-label="姓名输入框"
  required
/>

<button
  @click="handleSubmit"
  aria-label="提交表单"
  :disabled="loading"
>
  提交
</button>

<div
  class="search-dropdown"
  role="listbox"
  aria-label="搜索建议"
>
  <!-- 搜索结果 -->
</div>
```

### **📊 质量检查强制流程**

#### **提交前强制检查 (更新版)**
```bash
# 1. 开发规范强制检查 (必须100%通过)
python project_check.py --mode check

# 2. 完整开发工作流程 (推荐用于新功能开发)
python project_check.py --mode workflow

# 3. 快速规范检查 (用于小修改)
python project_check.py --mode quick

# 4. 语法和类型检查
npm run lint
npm run type-check

# 5. 构建检查
npm run build
```

#### **统一检查脚本说明 (2025-07-17更新)**
- **project_check.py** - 统一开发规范检查脚本，支持三种模式：
  - `--mode check` - 默认模式，完整规范检查
  - `--mode workflow` - 完整开发工作流程，包含前置检查和后置验证
  - `--mode quick` - 快速检查模式，仅检查关键规范
- **one_click_start.py** - 一键启动开发环境脚本
- **🚫 已删除所有批量修改脚本** - 严格禁止使用批量修改脚本

#### **发现问题强制修复原则**
- 🚨 **所有ERROR级别问题必须立即修复**
- 🚨 **所有WARNING级别问题必须在当次提交中修复**
- 🚨 **所有INFO级别问题必须记录并计划修复**
- 🚨 **不允许提交有已知问题的代码**
- 🚨 **每次修复后必须重新运行完整检查**

### **🎯 开发完成强制验证**

#### **功能验证清单**
- [ ] **核心功能正常** - 新增/编辑/删除/查询功能
- [ ] **边界情况处理** - 空数据/网络错误/权限限制
- [ ] **用户体验优化** - 加载状态/错误提示/成功反馈
- [ ] **PC端多分辨率适配** - 桌面端多种分辨率测试
- [ ] **性能表现** - 加载速度/内存使用/网络请求优化

#### **代码质量验证**
- [ ] **代码规范** - ESLint/Prettier检查通过
- [ ] **类型安全** - TypeScript或JSDoc注释完整
- [ ] **错误处理** - 所有异常情况都有处理
- [ ] **测试覆盖** - 单元测试和集成测试通过
- [ ] **文档更新** - 相关文档和注释更新

## 🚨 错误处理和异常管理规范 (强制)

### **📋 Try-Catch强制规范**

#### **必须使用Try-Catch的场景**
- 🚨 **所有API调用**
- 🚨 **所有异步操作**
- 🚨 **所有文件操作**
- 🚨 **所有JSON解析**
- 🚨 **所有DOM操作**

#### **Try-Catch标准格式**
```javascript
// ✅ 正确的Try-Catch格式
const handleAsyncOperation = async () => {
  try {
    // 设置加载状态
    loadingStates.operation = true;

    // 执行操作
    const result = await apiCall();

    // 成功处理
    showToast('操作成功', 'success');
    return result;

  } catch (error) {
    // 错误处理
    console.error('操作失败:', error);
    showToast('操作失败，请重试', 'error');

    // 错误上报 (生产环境)
    if (process.env.NODE_ENV === 'production') {
      reportError(error);
    }

  } finally {
    // 清理工作
    loadingStates.operation = false;
  }
};
```

#### **🚨 禁止的错误处理方式**
```javascript
// ❌ 禁止：空的catch块
try {
  await apiCall();
} catch (error) {
  // 空的catch块
}

// ❌ 禁止：只有try没有catch或finally
try {
  await apiCall();
}

// ❌ 禁止：吞掉错误不处理
try {
  await apiCall();
} catch (error) {
  // 什么都不做
}
```

### **🔔 用户反馈强制规范**

#### **Toast通知标准实现**
```javascript
// 🚨 必须实现的Toast通知系统
const toastState = reactive({
  visible: false,
  message: '',
  type: 'success' // success, error, warning, info
});

const showToast = (message, type = 'success') => {
  toastState.message = message;
  toastState.type = type;
  toastState.visible = true;

  // 3秒后自动隐藏
  setTimeout(() => {
    toastState.visible = false;
  }, 3000);
};
```

#### **加载状态标准管理**
```javascript
// 🚨 必须实现的加载状态管理
const loadingStates = reactive({
  dataLoading: false,    // 数据加载
  submitLoading: false,  // 提交操作
  deleteLoading: false   // 删除操作
});

// 按钮禁用控制
<button
  :disabled="loadingStates.submitLoading"
  @click="handleSubmit"
>
  {{ loadingStates.submitLoading ? '提交中...' : '确定' }}
</button>
```

## 🛠️ 开发环境规范 (强制)

### **📦 版本管理工具**
- **Python版本管理**: 🚨 **必须使用 pyenv**
  ```bash
  # 安装指定Python版本
  pyenv install 3.11.0
  pyenv local 3.11.0
  ```
- **Node.js版本管理**: 🚨 **必须使用 nvm**
  ```bash
  # 安装指定Node.js版本
  nvm install 18.17.0
  nvm use 18.17.0
  ```

### **🔧 开发工具配置**
- **启动脚本**: 🚨 **必须使用一键启动脚本**
  ```bash
  # 使用统一启动脚本
  ./start-all-dev.sh
  ```
- **热重载**: 🚨 **必须确保所有组件支持热重载**
- **进程管理**: 🚨 **启动前自动清理冲突进程**
- **健康检查**: 🚨 **启动后自动验证服务状态**
- **日志记录**: 🚨 **自动记录启动日志和错误信息**

### **🔧 一键启动脚本功能**
- **✅ 环境检查**: Python/Node.js版本管理 (pyenv/nvm)
- **✅ 依赖管理**: 虚拟环境创建和依赖安装
- **✅ 端口管理**: 自动检测和清理端口冲突
- **✅ 服务启动**: Django后端、Vue前端、Taro小程序
- **✅ 健康检查**: 服务启动验证和超时处理
- **✅ 热重载**: 强制启用所有服务的热重载模式
- **✅ 状态监控**: 实时监控服务状态和自动重启
- **✅ 日志管理**: 启动日志记录和问题诊断

### **📁 项目结构规范**
```
wechatcloud/
├── admin/                    # 管理后台 (Vue 3 + Vite)
├── client/                   # 小程序前端
├── server/                   # Django后端
├── docs/                     # 文档目录
├── scripts/                  # 通用脚本
└── start-all-dev.sh         # 🚨 一键启动脚本
```

## 🎨 UI设计规范 (强制)

### **🎭 设计风格约束**
- **主题风格**: 🚨 **必须使用毕加索艺术风格**
  - 核心色彩: 紫色 (#8e44ad) 为主色调
  - 艺术元素: 几何变形、渐变效果、倾斜变换
  - 背景保持: 🚨 **禁止修改全局背景色**

### **📐 布局规范**
- **黄金比例**: 🚨 **必须使用黄金比例布局，禁用右侧定位**
- **侧边栏**: 🚨 **必须使用窄侧边栏，扁平结构，禁用折叠功能**
- **页面标题**: 🚨 **禁止重复显示页面标题和描述性副标题**
- **间距统一**: 🚨 **必须保持一致的间距、边距和视觉平衡**

### **🎨 组件设计约束**
- **Logo显示**: 🚨 **必须50%原始宽度 + 彩虹渐变效果**
- **菜单选中**: 🚨 **必须使用彩虹闪光效果 + 平滑动画**
- **表格样式**: 🚨 **禁用钻石或梯形变换效果，使用标准表格**
- **滚动条**: 🚨 **必须应用于tbody元素，禁用主内容区滚动条**
- **阴影效果**: 🚨 **必须使用七彩阴影，禁用边框效果**

### **🖥️ PC端多分辨率设计约束**
- **分页显示**: 🚨 **默认20条/页，使用梵高风格分页组件**
- **超出处理**: 🚨 **仅当内容超过5行时显示滚动条**
- **桌面端适配**: 🚨 **必须支持1024px及以上分辨率**
- **自适应缩放**: 🚨 **必须根据页面尺寸自动调整缩放比例**
- **零覆盖原则**: 🚨 **强制要求元素不能覆盖、错位、重叠**
- **最佳视觉效果**: 🚨 **在所有支持分辨率下保持最佳视觉体验**
- **鼠标友好**: 🚨 **按钮大小必须适合鼠标点击操作**

### **📄 分页组件标准 (强制)**
- **分页风格**: 🚨 **必须使用梵高风格分页组件 (pagination-container)**
- **分页信息**: 🚨 **必须显示总记录数、当前页/总页数**
- **每页条数**: 🚨 **必须提供5/10/20/50条选择器**
- **页码导航**: 🚨 **必须包含上一页、页码按钮、下一页**
- **CSS类名**: 🚨 **pagination-container, page-btn, page-number等**
- **多分辨率适配**: 🚨 **不同分辨率下保持良好布局**

### **📊 表格排版标准 (以服务管理页面为模板)**

#### **🔢 数据显示规范 (2025-01-18强化)**
- **默认每页**: 🚨 **强制要求: 默认每页5条记录 (pageSize: 5)**
- **表格高度**: 🚨 **强制要求: 确保5行数据完整显示，避免内容被遮挡**
- **最小高度**: 🚨 **min-height: 350px (5行 × 70px行高)**
- **最大高度**: 🚨 **max-height: calc(100vh - 320px) (减去头部、搜索、分页空间)**
- **数据行高**: 🚨 **数据行高: min-height: 55px**
- **表头高度**: 🚨 **padding: 12px 0**
- **滚动条控制**: 🚨 **≤5条时隐藏滚动条 (hide-scrollbar)**
- **表格容器**: 🚨 **flex: 1, overflow-y: auto**

#### **🚨 强制检查项目 (所有管理页面)**
```javascript
// ✅ 正确示例 - 所有管理页面必须遵循
const pageSize = ref(5); // 默认5条每页，符合开发规范

// ❌ 错误示例 - 禁止使用其他默认值
const pageSize = ref(8); // 违反规范
const pageSize = ref(10); // 违反规范
```

#### **📋 适用页面清单 (必须检查)**
- ✅ **服务管理** (ServiceManagement.vue) - 已符合规范
- ✅ **技师管理** (TherapistManagement.vue) - 已修复为5条
- ✅ **客户管理** (CustomerManagement.vue) - 已修复为5条
- ✅ **预约管理** (AppointmentManagement.vue) - 已修复为5条
- ✅ **财务记录** (FinanceRecordsView.vue) - 已修复为5条

#### **🎨 字体规范**
- **表头字体**: 🚨 **表头字体: font-size: 0.85rem, font-weight: 900**
- **数据内容**: 🚨 **font-size: 0.9rem, font-weight: 600**
- **服务名称**: 🚨 **font-size: 1rem, font-weight: bold**
- **价格显示**: 🚨 **font-size: 16px, font-weight: 600**
- **状态文字**: 🚨 **font-size: 0.85rem, font-weight: 700**
- **操作按钮**: 🚨 **font-size: 0.85rem, font-weight: 600**

#### **📏 间距规范**
- **单元格内边距**: 🚨 **单元格内边距: padding: 0 12px**
- **数据行间距**: 🚨 **margin-bottom: 6px**
- **表头边距**: 🚨 **margin-bottom: 8px**
- **行内元素间距**: 🚨 **gap: 4px-16px (根据内容)**

## 🎨 **智能美观间距系统规范** (2025-01-20新增)

### **🧮 黄金比例美学算法 (强制)**

#### **基础单位计算公式 (必须实现)**
```javascript
// 🚨 强制要求: 基于窗口高度的智能基础单位计算
const baseUnit = Math.max(8, Math.min(24, Math.floor(availableHeight / 35)));
const goldenRatio = 1.618; // 🚨 必须使用黄金比例
```

#### **间距系列生成规则 (强制标准)**
- **🚨 XS间距**: `baseUnit * 0.5` - 超小间距(4-12px) - 用于紧密关系元素
- **🚨 SM间距**: `baseUnit * 0.75` - 小间距(6-18px) - 用于相关元素
- **🚨 MD间距**: `baseUnit` - 中等间距(8-24px) - 用于分离元素
- **🚨 LG间距**: `baseUnit * 1.618` - 大间距(13-39px) - 用于明显区分
- **🚨 XL间距**: `baseUnit * 1.618 * 1.5` - 超大间距(19-58px) - 用于独立区块

#### **专用间距计算公式 (强制实现)**
```javascript
// 🚨 模态框边距 - 基于可用宽度自适应
modalPadding: Math.max(20, Math.min(40, Math.floor(availableWidth / 25)))

// 🚨 按钮间距 - 基于基础单位的协调比例
buttonSpacing: Math.max(12, Math.min(20, Math.floor(baseUnit * 1.2)))

// 🚨 表单间距 - 基于基础单位的视觉层次
formSpacing: Math.max(16, Math.min(28, Math.floor(baseUnit * 1.4)))

// 🚨 区块间距 - 基于基础单位的明显分离
sectionSpacing: Math.max(24, Math.min(40, Math.floor(baseUnit * 2)))
```

### **🎭 视觉层次和节奏感规范 (强制)**

#### **内容关系间距分级 (必须遵守)**
- **🚨 紧密关系** (`tight`): XS间距 - 标签与输入框、图标与文字
- **🚨 相关元素** (`related`): SM间距 - 同组表单项、相关按钮
- **🚨 分离元素** (`separate`): MD间距 - 不同功能区域、表单项间
- **🚨 明显区分** (`distinct`): LG间距 - 主要区块分离、模块间距
- **🚨 独立区块** (`isolated`): XL间距 - 完全独立的内容区、页面区块

#### **CSS变量系统 (强制定义)**
```css
/* 🚨 必须定义的CSS变量 */
:root {
  --spacing-xs: [计算值]px;
  --spacing-sm: [计算值]px;
  --spacing-md: [计算值]px;
  --spacing-lg: [计算值]px;
  --spacing-xl: [计算值]px;
  --rhythm-tight: [计算值]px;
  --rhythm-related: [计算值]px;
  --rhythm-separate: [计算值]px;
  --rhythm-distinct: [计算值]px;
  --rhythm-isolated: [计算值]px;
}
```

### **📐 响应式尺寸计算规范 (强制)**

#### **按钮尺寸自适应公式 (必须实现)**
```javascript
// 🚨 按钮高度 - 基于窗口高度的协调比例
buttonHeight: Math.max(36, Math.min(48, Math.floor(availableHeight / 16)))

// 🚨 按钮最小宽度 - 基于可用宽度的合理比例
buttonMinWidth: Math.max(80, Math.min(120, Math.floor(availableWidth / 12)))

// 🚨 按钮内边距 - 基于间距系统的协调值
buttonPadding: Math.max(12, Math.min(24, spacing.md))
```

#### **输入控件尺寸自适应公式 (必须实现)**
```javascript
// 🚨 输入框高度 - 基于窗口高度的用户友好尺寸
inputHeight: Math.max(40, Math.min(52, Math.floor(availableHeight / 15)))

// 🚨 输入框内边距 - 基于间距系统的舒适间距
inputPadding: Math.max(12, Math.min(20, spacing.sm))
```

#### **字体大小自适应公式 (必须实现)**
```javascript
// 🚨 正文字体 - 基于基础单位的可读性优化
fontSize: Math.max(14, Math.min(18, Math.floor(baseUnit * 0.8)))

// 🚨 标题字体 - 基于基础单位的层次感
titleSize: Math.max(18, Math.min(24, Math.floor(baseUnit * 1.2)))

// 🚨 标签字体 - 基于基础单位的辅助信息显示
labelSize: Math.max(13, Math.min(16, Math.floor(baseUnit * 0.7)))
```

### **🏗️ 模态框布局强制规范 (必须遵守)**

#### **边界安全约束 (零容忍标准)**
```css
/* 🚨 强制要求: 模态框边界安全设置 */
.modal-overlay {
  top: [spacing.modal]px !important;
  left: [sidebarWidth + spacing.modal]px !important;
  width: calc(100% - [sidebarWidth + spacing.modal * 2]px) !important;
  height: calc(100% - [spacing.modal * 2]px) !important;
}
```

#### **高度分配规则 (强制执行)**
- **🚨 模态框最大高度**: `calc(100vh - spacing.modal * 3)px`
- **🚨 头部固定高度**: `60px` (flex-shrink: 0)
- **🚨 按钮区域固定高度**: `80px` (flex-shrink: 0)
- **🚨 主体最大高度**: `calc(100vh - 220px)` (为头部和按钮预留空间)

#### **Flexbox布局要求 (强制实现)**
```css
/* 🚨 必须实现的Flexbox布局结构 */
.service-form-modal {
  display: flex !important;
  flex-direction: column !important;
  overflow: hidden !important;
}

.modal-header {
  flex-shrink: 0 !important;
  height: 60px !important;
}

.modal-body {
  flex: 1 !important;
  overflow-y: auto !important;
  max-height: calc(100vh - 220px) !important;
}

.footer-actions {
  flex-shrink: 0 !important;
  height: 80px !important;
  position: sticky !important;
  bottom: 0 !important;
}
```

### **🎯 智能间距应用流程 (强制执行)**

#### **1. 窗口分析阶段 (必须执行)**
```javascript
// 🚨 强制要求: 获取准确的窗口尺寸信息
const windowWidth = window.innerWidth;
const windowHeight = window.innerHeight;
const sidebarWidth = 180; // 固定侧边栏宽度
const availableWidth = windowWidth - sidebarWidth;
const availableHeight = windowHeight;
```

#### **2. 基础单位计算阶段 (必须执行)**
```javascript
// 🚨 强制要求: 基于黄金比例的美学计算
const baseUnit = Math.max(8, Math.min(24, Math.floor(availableHeight / 35)));
const goldenRatio = 1.618; // 经典美学比例
```

#### **3. 间距系列生成阶段 (必须执行)**
```javascript
// 🚨 强制要求: 生成完整的间距系列
const spacing = {
  xs: Math.round(baseUnit * 0.5),
  sm: Math.round(baseUnit * 0.75),
  md: baseUnit,
  lg: Math.round(baseUnit * goldenRatio),
  xl: Math.round(baseUnit * goldenRatio * 1.5)
};
```

#### **4. CSS样式注入阶段 (必须执行)**
```javascript
// 🚨 强制要求: 动态创建并注入自适应样式表
const adaptiveStyle = document.createElement('style');
adaptiveStyle.id = 'intelligent-spacing-system';
// ... 样式内容
document.head.appendChild(adaptiveStyle);
```

#### **5. 效果验证阶段 (必须执行)**
```javascript
// 🚨 强制要求: 验证所有元素符合边界安全规范
setTimeout(() => {
  // 检查按钮是否在窗口边界内
  // 验证间距是否符合设计规范
  // 确认视觉效果是否协调
}, 300);
```

### **📊 质量检查标准 (强制通过)**

#### **必须通过的检查项 (零容忍)**
1. **🚨 边界安全检查**: 所有按钮底部 ≤ `windowHeight - 5px`
2. **🚨 间距一致性检查**: 同类元素间距误差 ≤ 2px
3. **🚨 视觉协调检查**: 间距比例符合黄金比例规律
4. **🚨 响应式适配检查**: 不同窗口尺寸下布局保持美观
5. **🚨 零重叠检查**: 任何同级元素不能重叠
6. **🚨 零贴边检查**: 所有元素与窗口边界 ≥ 10px安全边距
7. **🚨 按钮边界专检**: 确认和取消按钮绝对不能超出窗口边界
8. **🚨 父子边界检查**: 按钮父元素不能与子元素完全重叠
9. **🚨 强制检查执行**: 每次页面加载、窗口改变、模态框打开都必须执行边界检查

#### **🚨 强制边界检查标准 (基于确认取消按钮问题)**

**检查函数强制要求 (必须实现)**:
```javascript
// 🚨 必须实现的检查函数
checkButtonBoundary()           // 按钮边界检查
checkButtonParentBoundary()     // 按钮父元素边界检查
checkElementBoundary(element)   // 单个元素边界检查
performCompleteBoundaryCheck()  // 完整边界检查流程
```

**检查触发机制强制要求 (必须实现)**:
```javascript
// 🚨 必须实现的自动检查触发
document.addEventListener('DOMContentLoaded', checkTrigger);  // 页面加载检查
window.addEventListener('resize', checkTrigger);             // 窗口改变检查
MutationObserver监听模态框打开                                // 模态框检查
window.checkBoundary = performCompleteBoundaryCheck;        // 手动检查命令
```

**检查结果强制要求 (必须达标)**:
- **🚨 严重违规**: 0个 (按钮超出窗口边界)
- **⚠️ 警告违规**: ≤ 2个 (元素贴边问题)
- **📋 其他问题**: ≤ 5个 (一般边界问题)
- **🎯 整体通过率**: ≥ 95%

#### **性能要求 (强制达标)**
- **🚨 样式注入时间**: ≤ 100ms
- **🚨 布局重排次数**: ≤ 1次
- **🚨 视觉稳定性**: 无闪烁或跳动
- **🚨 内存占用**: 新增CSS ≤ 50KB

#### **美学质量评估 (强制标准)**
- **🚨 黄金比例符合度**: ≥ 95%
- **🚨 视觉层次清晰度**: ≥ 90%
- **🚨 间距协调性**: ≥ 95%
- **🚨 用户体验满意度**: ≥ 90%

#### **🎯 列宽标准 (服务管理页面)**
- **服务信息列**: 🚨 **服务信息列: flex-basis: 35%, min-width: 200px**
- **服务费列**: 🚨 **flex-basis: 15%, min-width: 100px**
- **提成列**: 🚨 **flex-basis: 15%, min-width: 100px**
- **时长列**: 🚨 **flex-basis: 12%, min-width: 80px**
- **状态列**: 🚨 **flex-basis: 10%, min-width: 70px**
- **操作列**: 🚨 **flex-basis: 13%, min-width: 120px**

#### **🖥️ PC端分辨率断点**
- **大屏桌面端**: 🚨 **≥1920px 正常表格布局，最大宽度限制**
- **标准桌面端**: 🚨 **1366px-1919px 正常表格布局**
- **小屏桌面端**: 🚨 **1024px-1365px 紧凑布局，保持功能完整**

#### **🔄 滚动条样式**
- **宽度**: 🚨 **width: 8px**
- **颜色**: 🚨 **梵高风格渐变 (#8e44ad, #9b59b6)**
- **显示条件**: 🚨 **仅当数据 > 5条时显示**
- **隐藏类**: 🚨 **hide-scrollbar (overflow: hidden)**

#### **🎨 表格视觉效果**
- **表头背景**: 🚨 **linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1)**
- **表头圆角**: 🚨 **border-radius: 15px 15px 0 0**
- **数据行背景**: 🚨 **linear-gradient(90deg, rgba(142,68,173,0.1)...)**
- **数据行圆角**: 🚨 **border-radius: 10px**
- **悬停效果**: 🚨 **transform: scale(1.02), box-shadow增强**

## 🎯 **表单行与菜单栏对齐规范** (2025-01-20强制新增)

### **📐 核心对齐原则 (强制执行)**

#### **🚨 基础对齐策略**
- **第1行数据** ↔ **第1个菜单项**（仪表盘）
- **第2行数据** ↔ **第2个菜单项**（预约管理）
- **第3行数据** ↔ **第3个菜单项**（客户管理）
- **第4行数据** ↔ **第4个菜单项**（技师管理）
- **第5行数据** ↔ **第5个菜单项**（服务管理）
- **第6行数据** ↔ **第6个菜单项**（财务概览）
- **第7行数据** ↔ **第7个菜单项**（系统设置）
- **第8行数据** ↔ **第8个菜单项**（退出登录）
- **第9-10行数据** ↔ **保持固定间距，不强制对齐菜单**

#### **🚨 间距匹配要求**
```css
/* 必须实现的数据行间距匹配 */
.data-row {
  min-height: 60px !important; /* 🚨 调整行高匹配菜单项间距 */
  height: 60px !important; /* 🚨 固定高度确保间距一致 */
  line-height: 60px !important; /* 🚨 文字垂直居中 */
  font-size: 16px !important; /* 🚨 与菜单项字体大小一致 */
  font-weight: 500 !important; /* 🚨 字重协调 */
}
```

#### **🚨 位置调整公式**
```css
/* 绿色区域位置调整 - 必须精确计算 */
.data-cubism {
  margin-top: -31px !important; /* 🚨 上移使第1行与第1个菜单项对齐 */
  padding: 0px 20px [动态计算]px 20px !important; /* 🚨 底部空间自适应 */
  margin-bottom: [动态计算]px !important; /* 🚨 与蓝色框底部保持一致 */
}

/* 蓝色区域空间扩展 - 必须匹配绿色区域 */
.table-container {
  padding: 0 5px [动态计算]px 5px !important; /* 🚨 底部空间与绿色区域协调 */
}
```

### **📊 区域边界管理规范 (强制执行)**

#### **🚨 翻页组件对齐标准**
- **蓝色框底部** = **翻页组件顶部** (误差 ≤ 5px)
- **绿色框底部** = **蓝色框底部** (误差 ≤ 2px)
- **数据行完全包含** 在绿色区域内，无溢出

#### **🚨 动态空间计算公式**
```javascript
// 必须实现的动态空间计算
const calculateSpacing = () => {
  const paginationTop = document.querySelector('.pagination-container').getBoundingClientRect().top;
  const tableBottom = document.querySelector('.table-container').getBoundingClientRect().bottom;
  const greenBottom = document.querySelector('.data-cubism').getBoundingClientRect().bottom;

  // 计算需要的调整量
  const blueAdjustment = paginationTop - tableBottom;
  const greenAdjustment = paginationTop - greenBottom;

  return { blueAdjustment, greenAdjustment };
};
```

### **🎯 显示行数管理规范 (强制执行)**

#### **🚨 默认显示策略**
- **标准显示**: 10行数据 (`pageSize = 10`)
- **菜单对齐**: 前8行精确对齐8个菜单项
- **额外显示**: 第9-10行保持固定间距
- **选项提供**: 5条/8条/10条/20条/50条选择

#### **🚨 特殊间距处理**
```css
/* 移除第7-8行间的特殊间距 */
.data-row:nth-child(7) {
  margin-bottom: 0px !important; /* 🚨 与其他行保持一致间距 */
}

/* 最后一行标准处理 */
.data-row:last-child {
  margin-bottom: 0 !important; /* 🚨 移除底部间距 */
}
```

### **📏 精确对齐验证规范 (强制执行)**

#### **🚨 对齐精度检查标准**
```javascript
// 必须实现的对齐检查函数
const validateRowAlignment = () => {
  const menuItems = document.querySelectorAll('[role="menuitem"]');
  const dataRows = document.querySelectorAll('.data-row');

  let alignedCount = 0;
  const maxCheck = Math.min(8, dataRows.length, menuItems.length);

  for (let i = 0; i < maxCheck; i++) {
    const dataTop = dataRows[i].getBoundingClientRect().top;
    const menuTop = menuItems[i].getBoundingClientRect().top;
    const diff = Math.abs(dataTop - menuTop);

    if (diff <= 5) alignedCount++; // 5px容差
  }

  const alignmentRate = (alignedCount / maxCheck) * 100;
  return {
    alignedCount,
    totalChecked: maxCheck,
    alignmentRate,
    success: alignmentRate >= 75 // 75%通过率
  };
};
```

#### **🚨 强制检查触发机制**
- **页面加载时**: 自动检查对齐效果
- **窗口调整时**: 重新验证对齐精度
- **数据更新时**: 确保对齐关系保持
- **手动验证**: `window.validateAlignment()` 命令

### **🎯 轮廓调试与坐标显示规范 (2025-01-20强制新增)**

#### **🚨 轮廓调试强制要求**
- **坐标同步显示**: 🚨 **轮廓显示必须与元素坐标信息一起显示，严禁单独使用轮廓调试**
- **控制台坐标打印**: 🚨 **必须将关键元素坐标打印到控制台，便于精确分析**
- **强制参数设置**: 🚨 **`debugElement(selector, color, true)` 第三个参数必须为true**
- **标准调试命令**: 🚨 **必须使用 `debug.coords()` 和 `debug.printCoords()` 命令**

#### **🚨 标准轮廓调试流程**
```javascript
// 强制包含坐标显示的调试流程
debug.on();                                    // 1. 启用全局调试
debug.coords();                                // 2. 🚨 强制显示所有坐标
debug.element('.problematic-element', 'red', true);  // 3. 🚨 必须启用坐标显示
debug.printCoords();                           // 4. 🚨 打印关键坐标到控制台
debug.overlap();                               // 5. 检查重叠问题
debug.clear();                                 // 6. 清除调试样式
```

### **🚨 计算验证与历史追踪强制规范 (2025-01-20血的教训)**

#### **🚨 每次CSS调整强制要求**
- **调整前记录**: 🚨 **必须精确记录当前坐标状态**
- **预期计算**: 🚨 **必须明确说明预期达到的坐标值**
- **调整后验证**: 🚨 **必须测量实际坐标并与预期对比**
- **历史追踪**: 🚨 **必须记录每次调整的完整历史**

#### **🚨 强制验证代码模板**
```javascript
// 🚨 调整前必须记录
const beforeCoords = {
  paginationTop: document.querySelector('.pagination-container').getBoundingClientRect().top,
  tableBottom: document.querySelector('.table-container').getBoundingClientRect().bottom,
  dataBottom: document.querySelector('.data-cubism').getBoundingClientRect().bottom
};

// 🚨 调整后必须验证
const afterCoords = {
  paginationTop: document.querySelector('.pagination-container').getBoundingClientRect().top,
  tableBottom: document.querySelector('.table-container').getBoundingClientRect().bottom,
  dataBottom: document.querySelector('.data-cubism').getBoundingClientRect().bottom
};

// 🚨 强制计算验证
const verification = {
  diff1Before: Math.abs(beforeCoords.paginationTop - beforeCoords.tableBottom),
  diff1After: Math.abs(afterCoords.paginationTop - afterCoords.tableBottom),
  diff1Improved: diff1Before - diff1After,
  calculationCorrect: diff1After < diff1Before
};

console.log('🚨 验证结果:', verification.calculationCorrect ? '✅正确' : '❌错误');
```

#### **🚨 血的教训记录**
- **2025-01-20案例**: margin-bottom调整8次，问题2差异始终90px未变
- **错误模式**: 盲目调整数值而不验证实际效果
- **教训**: 每次调整后必须立即验证，发现错误立即停止

### **🎨 视觉协调规范 (强制执行)**

#### **🚨 行高与间距协调**
- **菜单项间距**: 70px (固定)
- **数据行间距**: 68px (接近菜单项间距)
- **行高设置**: 60px (确保文字垂直居中)
- **字体统一**: 16px, font-weight: 500

#### **🚨 容器尺寸自适应**
```css
/* 绿色区域自适应扩展 */
.data-cubism {
  /* 🚨 根据数据行数量动态调整底部空间 */
  padding-bottom: calc([行数] * 60px + 40px) !important;
}

/* 蓝色区域协调扩展 */
.table-container {
  /* 🚨 与绿色区域底部保持一致 */
  padding-bottom: calc([绿色区域高度] - [当前高度差]) !important;
}
```

### **📋 适用页面清单 (必须遵守)**

#### **🚨 强制应用页面**
- ✅ **服务管理** (ServiceManagement.vue) - 已完成标准实现
- 🔄 **技师管理** (TherapistManagement.vue) - 必须按此规范调整
- 🔄 **客户管理** (CustomerManagement.vue) - 必须按此规范调整
- 🔄 **预约管理** (AppointmentManagement.vue) - 必须按此规范调整
- 🔄 **财务记录** (FinanceRecordsView.vue) - 必须按此规范调整

#### **🚨 实施检查清单**
- [ ] **默认显示**: pageSize = 10
- [ ] **行高调整**: min-height: 60px
- [ ] **位置对齐**: margin-top: -31px
- [ ] **间距统一**: 移除特殊间距
- [ ] **容器扩展**: 动态计算底部空间
- [ ] **边界对齐**: 与翻页组件顶部对齐
- [ ] **验证通过**: 对齐率 ≥ 75%

### **🔧 技术实现要求 (强制执行)**

#### **🚨 CSS修改强制流程**
1. **检查冲突**: 分析现有样式优先级
2. **计算调整**: 精确计算位置和尺寸
3. **应用样式**: 使用!important确保生效
4. **验证效果**: 运行对齐检查函数
5. **确认解决**: 达到75%以上对齐率

#### **🚨 调试工具要求**
```javascript
// 必须提供的调试命令
window.validateAlignment = validateRowAlignment;
window.showAlignmentInfo = () => {
  // 显示详细对齐信息
};
window.adjustAlignment = (offset) => {
  // 手动微调对齐位置
};
```

### **📊 质量标准 (强制达标)**

#### **🚨 对齐精度要求**
- **完美对齐**: ≥ 6/8 行 (差异 ≤ 5px)
- **良好对齐**: ≥ 7/8 行 (差异 ≤ 10px)
- **最低标准**: ≥ 6/8 行 (差异 ≤ 15px)
- **整体通过率**: ≥ 75%

#### **🚨 视觉效果要求**
- **间距一致性**: 标准差 ≤ 5px
- **容器协调性**: 蓝绿底部差异 ≤ 2px
- **边界安全性**: 无元素溢出容器
- **响应式适配**: 多分辨率下保持对齐

#### **🚨 性能要求**
- **计算时间**: ≤ 50ms
- **重排次数**: ≤ 1次
- **内存占用**: 新增CSS ≤ 10KB
- **视觉稳定**: 无闪烁或跳动

#### **📝 内容溢出处理**
- **单行文本**: 🚨 **text-overflow: ellipsis, white-space: nowrap**
- **多行文本**: 🚨 **-webkit-line-clamp: 2, max-height: 2.8em**
- **行高**: 🚨 **line-height: 1.4**
- **长单词**: 🚨 **word-break: break-all, word-wrap: break-word**

#### **🔧 加载状态标准**
- **数据加载**: 🚨 **loading-spinner-large (32px), 居中显示**
- **加载容器**: 🚨 **min-height: 300px, padding: 40px 0**
- **空状态**: 🚨 **empty-icon (48px), empty-text (16px)**
- **加载文字**: 🚨 **font-size: 14px, color: var(--van-gogh-text-secondary)**

#### **💰 特殊组件规范**
- **价格单元格**: 🚨 **font-size: 16px, 可点击编辑**
- **状态指示器**: 🚨 **圆形指示器 + 文字标签**
- **操作按钮**: 🚨 **padding: 8px 16px, 图标+文字组合**
- **历史记录**: 🚨 **font-size: 12px, 悬浮显示**

## 💰 功能开发规范 (强制)

### **🔍 搜索功能约束**
- **智能搜索**: 🚨 **必须支持汉字、拼音全拼、拼音首字母搜索**
- **自动完成**: 🚨 **必须提供下拉选择选项**
- **实时搜索**: 🚨 **禁用手动刷新按钮，必须实现输入即搜索**
- **键盘导航**: 🚨 **必须支持上下键选择、Enter确认、ESC关闭**

### **📊 数据显示约束**
- **排序规则**: 🚨 **上架项目必须显示在前，下架项目显示在后**
- **状态标识**: 🚨 **必须使用不同颜色区分上架/下架状态**
- **软删除**: 🚨 **必须使用状态标记，禁用硬删除，支持恢复**
- **历史记录**: 🚨 **价格/提成修改必须记录时间，禁用原因字段**

### **🔒 数据安全约束 (新增)**
- **后端验证**: 🚨 **所有业务逻辑必须在后端实现，前端仅负责展示**
- **唯一性检查**: 🚨 **数据唯一性必须在后端数据库层面保证**
- **软删除机制**: 🚨 **删除操作必须使用软删除，保留数据完整性**
- **智能恢复**: 🚨 **重名数据创建时必须自动检测并恢复已删除数据**
- **事务安全**: 🚨 **关键操作必须使用数据库事务确保一致性**

### **🏗️ 后端开发规范 (新增)**
- **ViewSet重写**: 🚨 **必须重写create/update方法实现业务逻辑**
- **数据验证**: 🚨 **必须在序列化器和视图中双重验证数据**
- **错误响应**: 🚨 **必须返回结构化的JSON错误信息**
- **状态管理**: 🚨 **必须使用字段映射处理前后端状态差异**
- **查询优化**: 🚨 **必须优先查询未删除数据，避免数据混乱**

### **💸 价格管理约束**
- **可编辑字段**: 🚨 **价格和提成必须支持点击编辑**
- **修改记录**: 🚨 **必须记录修改日期时间，移除修改原因**
- **历史查看**: 🚨 **必须支持查看历史修改记录**
- **数据验证**: 🚨 **必须验证数值格式和范围**

### **🤖 AI集成约束 (已更新)**
- **图像生成**: 🚨 **必须使用火山引擎豆包AI (后端Python SDK集成)**
- **文本优化**: 🚨 **必须使用DeepSeek AI API**
- **架构要求**: 🚨 **前端调用后端API，后端使用官方SDK**
- **手动触发**: 🚨 **必须使用专用按钮，禁用自动生成**
- **错误处理**: 🚨 **必须提供友好的错误提示和备用方案**
- **CORS处理**: 🚨 **禁止前端直接调用第三方API，必须通过后端代理**

### **🎨 前端开发规范 (新增)**
- **错误处理**: 🚨 **必须解析后端JSON错误响应并显示用户友好消息**
- **状态管理**: 🚨 **必须简化前端逻辑，将复杂判断移至后端**
- **API调用**: 🚨 **必须信任后端返回结果，减少前端验证逻辑**
- **用户反馈**: 🚨 **必须提供清晰的操作反馈和错误提示**
- **代理配置**: 🚨 **必须正确配置Vite代理，保持API路径一致性**

## 📝 表单验证和数据处理规范 (强制)

### **🚨 表单验证强制标准**

#### **验证函数标准格式**
```javascript
// 🚨 必须遵循的验证函数格式
const validateForm = () => {
  // 清空之前的错误
  Object.assign(formErrors, {
    field1: '',
    field2: '',
    field3: ''
  });

  let isValid = true;

  // 必填字段验证
  if (!formState.field1.trim()) {
    formErrors.field1 = '请输入XXX';
    isValid = false;
  }

  // 格式验证
  if (formState.field2 && !/^pattern$/.test(formState.field2)) {
    formErrors.field2 = '格式不正确';
    isValid = false;
  }

  return isValid;
};
```

#### **表单状态管理标准**
```javascript
// 🚨 必须使用的表单状态结构
const formState = reactive({
  field1: '',
  field2: '',
  field3: ''
});

const formErrors = reactive({
  field1: '',
  field2: '',
  field3: ''
});
```

#### **表单提交标准流程**
```javascript
// 🚨 必须遵循的提交流程
const handleSubmit = async () => {
  // 防抖处理
  if (submitTimeout) clearTimeout(submitTimeout);

  submitTimeout = setTimeout(async () => {
    try {
      // 1. 表单验证
      if (!validateForm()) {
        showToast('请检查输入内容', 'error');
        return;
      }

      // 2. 设置加载状态
      loadingStates.submitLoading = true;

      // 3. API调用
      const result = await apiCall(formState);

      // 4. 成功处理
      showToast('操作成功', 'success');
      hideModal();

    } catch (error) {
      console.error('提交失败:', error);
      showToast('操作失败，请重试', 'error');
    } finally {
      loadingStates.submitLoading = false;
    }
  }, 300); // 300ms防抖
};
```

### **🔄 数据流转强制规范**

#### **新增数据标准流程**
```javascript
// 🚨 新增数据必须遵循的流程
const handleAdd = async (newData) => {
  try {
    // 1. 数据验证
    if (!validateData(newData)) return;

    // 2. API调用
    const result = await createAPI(newData);

    // 3. 更新本地数据 - 添加到列表顶部
    dataList.value.unshift(result);

    // 4. 用户反馈
    showToast('添加成功', 'success');

  } catch (error) {
    handleError(error, '添加失败');
  }
};
```

#### **编辑数据标准流程**
```javascript
// 🚨 编辑数据必须遵循的流程
const handleEdit = async (id, updatedData) => {
  try {
    // 1. 数据验证
    if (!validateData(updatedData)) return;

    // 2. API调用
    const result = await updateAPI(id, updatedData);

    // 3. 更新本地数据 - 找到对应项并更新
    const index = dataList.value.findIndex(item => item.id === id);
    if (index !== -1) {
      dataList.value[index] = result;
    }

    // 4. 用户反馈
    showToast('更新成功', 'success');

  } catch (error) {
    handleError(error, '更新失败');
  }
};
```

## 🎨 模态框和表单验证标准 (2025-01-16)

### **📋 模态框组件标准**

#### **🚨 必须遵循的模态框结构**
```vue
<!-- 标准模态框结构 - 必须严格遵循 -->
<div v-if="modalVisible" class="modal-overlay" @click="hideModal">
  <div class="form-modal" @click.stop>
    <div class="form-header">
      <h3 class="form-title">{{ modalTitle }}</h3>
      <button class="close-btn" @click="hideModal">×</button>
    </div>
    <div class="form-content">
      <!-- 表单内容区域 -->
    </div>
    <div class="form-actions">
      <button class="cancel-btn" @click="hideModal">取消</button>
      <button class="confirm-btn" :disabled="loadingStates.submitLoading" @click="handleSubmit">
        {{ loadingStates.submitLoading ? '提交中...' : '确定' }}
      </button>
    </div>
  </div>
</div>
```

#### **🚨 必须的CSS样式类**
- **`.modal-overlay`**: 🚨 **必须设置fixed定位和backdrop-filter模糊效果**
- **`.form-modal`**: 🚨 **必须设置最大宽度600px和响应式适配**
- **`.form-header`**: 🚨 **必须使用梵高风格渐变背景**
- **`.form-content`**: 🚨 **必须设置最大高度80vh和滚动条**
- **`.form-actions`**: 🚨 **必须右对齐按钮布局**

#### **🚨 模态框尺寸规范 (2025-01-18新增)**
**核心原则**: 根据内容实际情况调整宽高，弹窗不要上下滑动条，在单个窗口内显示全部数据

##### **📏 不同类型模态框的标准尺寸**
```css
/* 时长编辑模态框 - 内容简单，使用紧凑尺寸 */
.duration-edit-modal {
  max-width: 420px !important;
  width: 420px !important;
  max-height: 280px !important;
  min-height: 280px !important;
}

/* 价格编辑模态框 - 包含历史记录，中等尺寸 */
.price-edit-modal {
  max-width: 500px !important;
  width: 500px !important;
  max-height: 450px !important;
}

/* 服务创建/编辑模态框 - 内容丰富，使用默认大尺寸 */
.service-form-modal {
  max-width: 900px;
  max-height: calc(100vh - 40px);
}
```

##### **🚨 强制要求**
1. **内容适配**: 🚨 **必须根据实际内容调整模态框尺寸**
2. **无滚动条**: 🚨 **单个窗口必须显示全部内容，禁止出现滚动条**
3. **多分辨率适配**: 🚨 **必须在不同桌面分辨率下正确适配**
4. **一致性**: 🚨 **同类型模态框必须使用相同尺寸规范**

#### **🚨 必须的PC端分辨率断点**
```css
/* 小屏桌面端适配 - 必须实现 */
@media (min-width: 1024px) and (max-width: 1365px) {
  .form-modal { width: 90%; max-width: 800px; }
  .form-row { gap: 15px; }
  .form-actions { justify-content: center; }
}
```

### **📝 表单验证标准**

#### **🚨 必须的验证函数结构**
```javascript
// 标准验证函数 - 必须严格遵循
const validateForm = () => {
  // 清空之前的错误
  Object.assign(formErrors, {
    field1: '', field2: '', field3: ''
  });

  let isValid = true;

  // 必填字段验证
  if (!formState.field1.trim()) {
    formErrors.field1 = '请输入XXX';
    isValid = false;
  }

  return isValid;
};
```

#### **🚨 必须的错误状态管理**
```javascript
// 错误状态定义 - 必须使用reactive
const formErrors = reactive({
  name: '',
  employee_id: '',
  phone: ''
});
```

#### **🚨 必须的验证规则标准**
| 字段类型 | 验证规则 | 错误提示格式 |
|---------|---------|-------------|
| **姓名** | 🚨 **必填，至少2个字符** | "请输入XXX" / "XXX至少2个字符" |
| **工号** | 🚨 **必填，字母数字组合** | "请输入工号" / "工号只能包含字母和数字" |
| **手机号** | 🚨 **必填，11位数字格式** | "请输入联系电话" / "请输入正确的手机号码" |

#### **🚨 必须的视觉反馈样式**
```css
/* 必填字段标识 */
.required { color: #ef4444; font-weight: bold; }

/* 错误状态输入框 */
.form-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

/* 错误提示信息 */
.error-message {
  color: #ef4444;
  font-size: 0.85rem;
  margin-top: 4px;
  font-weight: 500;
}
```

### **⏳ 加载状态管理标准**

#### **🚨 必须的加载状态定义**
```javascript
// 统一加载状态管理 - 必须使用reactive
const loadingStates = reactive({
  dataLoading: false,      // 数据加载状态
  submitLoading: false,    // 提交加载状态
  deleteLoading: false     // 删除加载状态
});
```

#### **🚨 必须的按钮状态控制**
```vue
<button
  class="confirm-btn"
  :disabled="loadingStates.submitLoading"
  @click="handleSubmit"
>
  {{ loadingStates.submitLoading ? '提交中...' : '确定' }}
</button>
```

### **🔄 数据流转标准**

#### **🚨 必须的新增数据流程**
```javascript
const handleAdd = async () => {
  if (!validateForm()) return;

  loadingStates.submitLoading = true;
  try {
    // API调用
    const newItem = await apiCall();
    // 添加到列表顶部
    items.value.unshift(newItem);
    hideModal();
  } finally {
    loadingStates.submitLoading = false;
  }
};
```

#### **🚨 必须的编辑数据流程**
```javascript
const handleEdit = (item) => {
  modalTitle.value = '编辑XXX';
  // 数据回填 - 必须使用Object.assign
  Object.assign(formState, {...item});
  // 清空错误状态
  Object.assign(formErrors, {});
  modalVisible.value = true;
};
```

## ⚡ 性能优化强制规范

### **🚨 防抖和节流强制要求**

#### **用户操作防抖处理**
```javascript
// 🚨 所有用户操作必须实现防抖
let debounceTimeout = null;

const handleUserAction = (...args) => {
  if (debounceTimeout) {
    clearTimeout(debounceTimeout);
  }

  debounceTimeout = setTimeout(() => {
    actualHandler(...args);
  }, 300); // 300ms防抖延迟
};
```

#### **搜索功能节流处理**
```javascript
// 🚨 搜索功能必须实现节流
let throttleTimeout = null;

const handleSearch = (keyword) => {
  if (throttleTimeout) return;

  throttleTimeout = setTimeout(() => {
    performSearch(keyword);
    throttleTimeout = null;
  }, 500); // 500ms节流间隔
};
```

### **🔧 内存管理强制规范**

#### **事件监听器清理**
```javascript
// 🚨 必须在组件卸载时清理事件监听器
onUnmounted(() => {
  // 清理定时器
  if (debounceTimeout) {
    clearTimeout(debounceTimeout);
  }

  // 清理事件监听器
  window.removeEventListener('resize', handleResize);

  // 清理其他资源
  cleanup();
});
```

#### **大数据量处理**
```javascript
// 🚨 超过100条数据必须实现虚拟滚动或分页
const handleLargeDataset = (data) => {
  if (data.length > 100) {
    // 实现虚拟滚动或分页
    return implementVirtualScroll(data);
  }
  return data;
};
```

### **🎯 代码质量强制标准**

#### **函数复杂度控制**
- 🚨 **单个函数不超过50行**
- 🚨 **嵌套层级不超过4层**
- 🚨 **参数个数不超过5个**
- 🚨 **必须有清晰的函数注释**

#### **变量命名规范**
```javascript
// ✅ 正确的命名方式
const userList = ref([]);           // 列表数据
const isLoading = ref(false);       // 布尔值
const handleSubmit = () => {};      // 事件处理函数
const validateForm = () => {};      // 验证函数

// ❌ 禁止的命名方式
const data = ref([]);               // 过于模糊
const flag = ref(false);            // 无意义命名
const func = () => {};              // 缩写命名
```

### **🖥️ PC端多分辨率设计强制标准**

#### **断点管理**
```css
/* 🚨 必须使用的PC端分辨率断点 */
@media (min-width: 1024px) and (max-width: 1365px) {
  /* 小屏桌面端样式 */
}

@media (min-width: 1366px) and (max-width: 1919px) {
  /* 标准桌面端样式 */
}

@media (min-width: 1920px) {
  /* 大屏桌面端样式 */
}
```

#### **桌面端优化要求**
- 🚨 **点击目标最小32px**
- 🚨 **文字大小最小12px**
- 🚨 **按钮间距最小6px**
- 🚨 **支持键盘导航**

#### **自适应缩放强制要求**
- 🚨 **缩放比例范围**: 0.8x - 1.2x (基于1366px基准)
- 🚨 **零重叠原则**: 任何分辨率下元素不能重叠
- 🚨 **零覆盖原则**: 元素不能覆盖其他功能区域
- 🚨 **零错位原则**: 元素位置必须精确对齐
- 🚨 **最小间距保护**: 元素间距不小于4px
- 🚨 **边界检测**: 元素不能超出容器边界
- 🚨 **视觉一致性**: 缩放后保持视觉比例协调

### **🚨 开发约束和禁止事项 (强制执行)**

#### **❌ 严格禁止的操作**
- **批量脚本修改**: 🚨 **严禁使用任何批量修改脚本**
- **整体容器缩放**: 🚨 **严禁使用transform: scale()对容器缩放**
- **忽略对齐检查**: 🚨 **严禁跳过对齐验证步骤**
- **硬编码尺寸**: 🚨 **严禁使用固定像素值，必须动态计算**

#### **✅ 必须遵守的操作**
- **逐个文件修改**: 🚨 **必须手动逐个文件进行调整**
- **即时测试验证**: 🚨 **每次修改后立即测试对齐效果**
- **动态计算尺寸**: 🚨 **必须根据实际内容动态计算空间**
- **多分辨率验证**: 🚨 **必须在多种桌面分辨率下验证效果**

### **📋 实施检查清单模板 (强制使用)**

#### **🚨 每个管理页面必须完成的检查项**
```markdown
## [页面名称]管理页面对齐检查清单

### 基础设置检查
- [ ] **默认显示行数**: pageSize = 10 ✅/❌
- [ ] **选项菜单**: 包含5/8/10/20/50条选项 ✅/❌
- [ ] **数据行高度**: min-height: 60px ✅/❌
- [ ] **文字行高**: line-height: 60px ✅/❌
- [ ] **字体大小**: font-size: 16px ✅/❌

### 位置对齐检查
- [ ] **第1行对齐**: 与仪表盘菜单项对齐 ✅/❌
- [ ] **第2行对齐**: 与预约管理菜单项对齐 ✅/❌
- [ ] **第3行对齐**: 与客户管理菜单项对齐 ✅/❌
- [ ] **第4行对齐**: 与技师管理菜单项对齐 ✅/❌
- [ ] **第5行对齐**: 与服务管理菜单项对齐 ✅/❌
- [ ] **第6行对齐**: 与财务概览菜单项对齐 ✅/❌
- [ ] **第7行对齐**: 与系统设置菜单项对齐 ✅/❌
- [ ] **第8行对齐**: 与退出登录菜单项对齐 ✅/❌

### 容器边界检查
- [ ] **绿色区域**: 包含所有数据行，无溢出 ✅/❌
- [ ] **蓝色区域**: 底部与翻页组件顶部对齐 ✅/❌
- [ ] **绿蓝一致**: 绿色框底部与蓝色框底部一致 ✅/❌
- [ ] **间距统一**: 移除第7-8行特殊间距 ✅/❌

### 验证测试检查
- [ ] **对齐率**: ≥ 75% ✅/❌
- [ ] **完美对齐**: ≥ 6/8行 (差异≤5px) ✅/❌
- [ ] **多分辨率**: 1024px/1366px/1920px下正常 ✅/❌
- [ ] **功能正常**: 数据加载、编辑、删除正常 ✅/❌

### 最终评分
- **总体通过率**: ___/16 项 (≥14项为合格)
- **对齐精度**: ___%  (≥75%为合格)
- **实施状态**: 🎉完成 / ✅良好 / ⚠️需改进 / ❌未达标
```

### **🔧 标准实施流程 (强制执行)**

#### **🚨 第一阶段：基础设置调整**
1. **修改默认显示**: `const pageSize = ref(10);`
2. **调整数据行高**: `min-height: 60px; height: 60px;`
3. **统一字体样式**: `font-size: 16px; line-height: 60px;`
4. **添加选项菜单**: 包含8条和10条选项

#### **🚨 第二阶段：位置精确对齐**
1. **计算菜单位置**: 获取8个菜单项的精确坐标
2. **调整绿色区域**: `margin-top: -31px` 使第1行对齐
3. **验证前8行**: 确保每行与对应菜单项对齐
4. **处理第9-10行**: 保持固定间距，不强制对齐

#### **🚨 第三阶段：容器边界调整**
1. **计算翻页位置**: 获取翻页组件顶部坐标
2. **扩展蓝色区域**: 使底部与翻页组件顶部对齐
3. **调整绿色区域**: 使底部与蓝色区域底部一致
4. **移除特殊间距**: 统一所有行的间距

#### **🚨 第四阶段：验证和优化**
1. **运行对齐检查**: 使用验证函数检查精度
2. **多分辨率测试**: 在不同桌面分辨率下测试
3. **功能完整性**: 确保所有功能正常工作
4. **性能优化**: 确保无性能问题

### **📊 质量保证措施 (强制执行)**

#### **🚨 代码审查要求**
- **对齐精度**: 必须达到75%以上对齐率
- **代码质量**: CSS代码必须有详细注释
- **测试覆盖**: 必须包含多分辨率测试
- **文档更新**: 必须更新相关技术文档

#### **🚨 持续监控要求**
- **定期检查**: 每月检查一次对齐效果
- **版本控制**: 重要调整必须记录版本变更
- **问题跟踪**: 建立对齐问题跟踪机制
- **改进优化**: 持续优化对齐算法和效果

## 🔄 最近更新总结 (2025-01-20)

### **🎯 表单行与菜单栏对齐规范完善 (2025-01-20新增)**
- **✅ 完成**: 表单行与菜单栏精确对齐规范制定
- **✅ 新增**: 10行数据显示标准，前8行对齐8个菜单项
- **✅ 新增**: 动态空间计算公式和容器边界管理规范
- **✅ 新增**: 对齐精度验证函数和质量检查标准
- **✅ 新增**: 强制实施检查清单模板和标准流程
- **✅ 完成**: 服务管理页面作为标准模板实现
- **✅ 规范**: CSS修改强制流程和调试工具要求
- **✅ 约束**: 严禁批量脚本修改，必须逐个文件手动调整

### **📐 核心技术规范**
```css
/* 标准数据行设置 */
.data-row {
  min-height: 60px !important;
  height: 60px !important;
  line-height: 60px !important;
  font-size: 16px !important;
}

/* 绿色区域精确对齐 */
.data-cubism {
  margin-top: -31px !important;
  padding: 0px 20px [动态计算]px 20px !important;
  margin-bottom: [动态计算]px !important;
}
```

### **🎯 质量标准**
- **对齐精度**: ≥ 75%通过率，≥ 6/8行完美对齐
- **容器协调**: 蓝绿底部差异 ≤ 2px
- **边界安全**: 与翻页组件顶部对齐，误差 ≤ 5px
- **适用页面**: 所有管理页面必须遵守此规范

### **🌋 AI服务架构重构**
- **✅ 完成**: 火山引擎AI图片生成服务迁移到后端Python SDK
- **✅ 删除**: 前端直接调用火山引擎API的代码（解决CORS问题）
- **✅ 删除**: 所有百度文心一言相关内容和代码
- **✅ 删除**: 所有豆包相关的文档和验证脚本
- **✅ 删除**: 后端Node.js火山引擎调用代码
- **✅ 更新**: 前端aiService.js使用后端API代理
- **✅ 集成**: DeepSeek AI用于生成专业中文提示词

### **🚀 一键启动脚本优化**
- **✅ 增强**: 端口冲突自动检测和清理机制
- **✅ 增强**: 虚拟环境完整性检查和自动修复
- **✅ 增强**: 依赖安装错误处理和版本冲突解决
- **✅ 增强**: 服务启动健康检查和超时处理
- **✅ 新增**: 开发环境状态检查（Git、磁盘、内存）
- **✅ 新增**: 启动日志记录和问题诊断
- **✅ 新增**: 火山引擎AI服务配置验证

### **📝 技术架构变更**
```
旧架构: 前端JS → 火山引擎API (❌ CORS阻止)
新架构: 前端JS → Django后端 → Python SDK → 火山引擎API (✅ 成功)
```

### **🎯 当前AI服务状态**
- **图片生成**: 火山引擎豆包AI (模型: doubao-seedream-3-0-t2i-250415)
- **提示词生成**: DeepSeek AI (专业中医理疗场景描述)
- **备用方案**: Unsplash高质量图片库

### **🗑️ 软删除功能实现 (2025-07-13)**
- **✅ 完成**: 后端软删除机制，数据库保留完整性
- **✅ 完成**: 智能恢复功能，重名时自动恢复已删除数据
- **✅ 完成**: 前后端数据同步，后端统一业务逻辑判断
- **✅ 完成**: 名称唯一性检查，防止数据冲突
- **✅ 完成**: 自动化测试覆盖完整生命周期
- **✅ 优化**: 按钮UI设计，编辑删除按钮视觉效果

### **🔧 架构优化成果 (2025-07-13)**
- **✅ 后端主导**: 所有业务逻辑迁移到Django后端
- **✅ 数据安全**: 软删除+事务保证数据完整性
- **✅ 智能处理**: 自动检测冲突并恢复已删除数据
- **✅ 用户体验**: 简化前端逻辑，提升操作流畅性
- **✅ 测试保障**: 100%自动化测试覆盖关键业务流程
- **API端点**: `/api/volcengine/generate-image/` 和 `/api/volcengine/config/`

## 📋 CI/CD Pipeline 概述

### **🎯 工作流文件位置**
- `.github/workflows/ci-cd.yml` - 主要CI/CD流水线
- `.github/workflows/playwright.yml` - 自动化测试流水线

### **📊 Pipeline 流程图**
```
Push to main → 代码质量检查 → 前端测试 + 后端测试 → 微信云托管自动部署
任何分支 → 安全扫描
```

## 🔧 作业配置规范 (强制)

### **1. 代码质量检查作业**
```yaml
code-quality:
  name: 代码质量检查
  runs-on: ubuntu-latest
  steps:
    - uses: actions/checkout@v4
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '18'
        cache: 'npm'
        cache-dependency-path: 'admin/package-lock.json'  # 🚨 必须指定正确路径
```

### **2. 前端测试作业**
```yaml
frontend-test:
  needs: code-quality  # 🚨 必须依赖代码质量检查
  steps:
    - name: 设置 Node.js
      uses: actions/setup-node@v4
      with:
        cache-dependency-path: |
          admin/package-lock.json
          client/package-lock.json  # 🚨 支持多个锁文件
```

### **3. 后端测试作业**
```yaml
backend-test:
  needs: code-quality
  services:
    mysql:
      image: mysql:8.0  # 🚨 必须使用MySQL 8.0
      env:
        MYSQL_ROOT_PASSWORD: test_password
        MYSQL_DATABASE: test_db
```

### **4. 自动部署作业**
```yaml
deploy-wechat-cloud:
  needs: [frontend-test, backend-test]  # 🚨 必须等待所有测试通过
  if: github.ref == 'refs/heads/main'   # 🚨 仅main分支部署
  environment: production               # 🚨 使用生产环境
```

## 📦 Actions 版本规范 (强制)

### **🔧 必须使用的Actions版本**
- `actions/checkout@v4` (当前最新稳定版)
- `actions/setup-node@v4` (当前最新稳定版)
- `actions/setup-python@v5` (当前最新稳定版)
- `actions/upload-artifact@v4` (当前最新稳定版)
- `codecov/codecov-action@v4` (当前最新稳定版)
- `github/codeql-action/upload-sarif@v3` (当前最新稳定版)
- `softprops/action-gh-release@v1` (稳定版本)

### **🚨 禁止使用的过时版本**
- ❌ `actions/checkout@v3`
- ❌ `actions/setup-node@v3`
- ❌ `actions/setup-python@v4`
- ❌ `actions/upload-artifact@v3`
- ❌ `codecov/codecov-action@v3`
- ❌ `github/codeql-action/upload-sarif@v2`
- ❌ `actions/create-release@v1`

### **🔄 版本更新策略**
- **主要版本更新**: 需要测试验证后再更新
- **次要版本更新**: 可以直接更新，但需要监控
- **补丁版本更新**: 建议及时更新以获取安全修复
- **定期检查**: 每月检查一次Actions版本更新

## 🗂️ 依赖锁文件配置 (推荐)

### **📦 项目结构 (已更新 2025-01-20)**
```
wechatcloud/
├── admin/                    # 管理后台 (Vue 3)
├── client/                  # 小程序 (Taro)
├── server/                  # 后端服务 (Django)
├── docs/                    # 项目文档
├── scripts/                 # 自动化脚本
└── .github/workflows/       # CI/CD配置 (Playwright)
```

### **🚨 关键配置要求**
1. **测试框架**: 使用Playwright进行自动化测试
2. **Python环境**: 使用pyenv管理Python版本
3. **文档维护**: 遵循当前开发指南

## 📦 包管理规范 (强制)

### **🚨 包管理器使用约束 (已更新 2025-01-20)**
- **Python**: 🚨 **必须使用 pip/poetry/conda 命令**
  ```bash
  # 正确方式
  pip install package-name
  poetry add package-name

  # 🚨 禁止手动编辑 requirements.txt
  ```
- **前端依赖**: 🚨 **使用项目内置的依赖管理**
  ```bash
  # Vue 3项目使用内置依赖
  # Taro项目使用内置依赖
  # 不再使用外部Node.js包管理器
  ```

### **📋 依赖管理约束**
- **版本锁定**: 🚨 **必须使用锁文件 (package-lock.json, poetry.lock)**
- **安全更新**: 🚨 **必须定期检查和更新安全补丁**
- **版本兼容**: 🚨 **必须验证依赖版本兼容性**
- **清理无用**: 🚨 **必须定期清理未使用的依赖**

### **🔒 例外情况**
- **复杂配置**: ✅ **仅在无法通过包管理器完成时才手动编辑**
- **自定义脚本**: ✅ **构建脚本和仓库设置可以手动配置**

## 🚫 批量修改脚本禁用规范 (强制新增)

### **🚨 严格禁止使用批量修改脚本**
- **批量修改**: 🚨 **严格禁止使用任何批量修改代码的脚本**
- **手动修改**: 🚨 **必须逐个文件手动修改，确保质量和准确性**
- **代码审查**: 🚨 **每个修改必须经过仔细审查和测试**
- **增量提交**: 🚨 **必须小步快跑，每次修改少量文件**

### **🚨 禁止的批量操作类型**
- **批量语法修复**: ❌ **禁止使用脚本批量修复语法错误**
- **批量样式调整**: ❌ **禁止使用脚本批量修改CSS样式**
- **批量重构**: ❌ **禁止使用脚本批量重构代码结构**
- **批量格式化**: ❌ **禁止使用脚本批量修改代码格式**
- **批量替换**: ❌ **禁止使用脚本批量替换代码内容**

### **✅ 允许的自动化操作**
- **代码格式化**: ✅ **使用IDE内置的格式化工具 (Prettier/ESLint)**
- **依赖管理**: ✅ **使用包管理器安装/更新依赖**
- **构建打包**: ✅ **使用构建工具进行打包和部署**
- **测试运行**: ✅ **使用测试框架运行自动化测试**

### **🔧 正确的修改流程**
1. **识别问题**: 🚨 **手动识别需要修改的文件和位置**
2. **逐个修改**: 🚨 **使用IDE逐个打开文件进行修改**
3. **即时测试**: 🚨 **每次修改后立即测试功能是否正常**
4. **影响评估**: 🚨 **确保修改不影响其他功能模块**
5. **代码审查**: 🚨 **仔细审查每个修改的准确性**
6. **增量提交**: 🚨 **每完成几个文件就提交一次**

### **🚨 逐步修改强制原则**
- **单文件修改**: 🚨 **每次只修改一个文件，立即测试**
- **功能隔离**: 🚨 **确保修改不破坏现有功能**
- **渐进式改进**: 🚨 **小步快跑，逐步完善**
- **回滚准备**: 🚨 **每次修改都要能快速回滚**
- **依赖检查**: 🚨 **修改前检查文件依赖关系**
- **影响评估**: 🚨 **评估修改对其他组件的影响**
- **测试覆盖**: 🚨 **确保修改的功能有完整测试**

### **🚨 自适应缩放强制规范**
- **元素级缩放**: 🚨 **必须对每个元素属性单独缩放**
- **禁止整体缩放**: 🚨 **严禁对容器使用transform: scale()**
- **缩放公式**: 🚨 **使用calc(原始值 * var(--scale-factor))**
- **缩放范围**: 🚨 **0.8x (1024px) 到 1.2x (4K+)**
- **基准分辨率**: 🚨 **以1366px为设计基准**
- **缩放属性**: 🚨 **宽度、高度、内外边距、圆角、阴影、字体**

### **📊 质量保证措施**
- **手动验证**: 🚨 **每个修改必须手动验证功能正确性**
- **测试覆盖**: 🚨 **修改后必须运行相关测试用例**
- **文档更新**: 🚨 **重要修改必须同步更新文档**
- **回滚准备**: 🚨 **每次提交前确保可以快速回滚**

## 🧪 测试规范 (强制)

### **📝 测试开发约束**
- **测试优先**: 🚨 **代码修改后必须编写或更新测试**
- **测试类型**: 🚨 **必须包含单元测试、集成测试、E2E测试**
- **测试覆盖**: 🚨 **新功能测试覆盖率必须 ≥ 80%**
- **测试运行**: 🚨 **提交前必须确保所有测试通过**

### **🎭 自动化测试约束**
- **功能测试**: 🚨 **必须测试所有用户交互功能**
- **多分辨率测试**: 🚨 **必须测试4种桌面端分辨率**
- **兼容性测试**: 🚨 **必须测试主流浏览器兼容性**
- **性能测试**: 🚨 **必须验证页面加载和响应时间**

### **🔄 业务流程测试约束 (新增)**
- **完整生命周期**: 🚨 **必须测试数据的创建→编辑→删除→恢复完整流程**
- **业务规则验证**: 🚨 **必须测试所有业务约束和验证规则**
- **数据一致性**: 🚨 **必须测试前后端数据同步和一致性**
- **错误场景**: 🚨 **必须测试所有异常情况和错误处理**
- **并发安全**: 🚨 **必须测试并发操作的数据安全性**

### **📊 测试报告约束**
```yaml
- name: 运行Playwright测试
  run: |
    cd admin                    # 🚨 必须在admin目录
    npm run test:auto

- name: 上传测试结果
  uses: actions/upload-artifact@v4
  with:
    name: playwright-report
    path: admin/test-reports/   # 🚨 正确的报告路径
    retention-days: 30
```

### **🔍 测试数据管理**
- **测试数据**: 🚨 **必须使用模拟数据，禁用生产数据**
- **数据清理**: 🚨 **测试后必须清理临时数据**
- **数据隔离**: 🚨 **不同测试间必须数据隔离**

## 📝 代码质量规范 (强制)

### **✍️ 代码风格约束**
- **命名规范**: 🚨 **必须使用有意义的变量和函数名**
- **注释要求**: 🚨 **复杂逻辑必须添加详细注释**
- **代码格式**: 🚨 **必须使用统一的代码格式化工具**
- **文件组织**: 🚨 **必须按功能模块组织文件结构**

### **🔧 代码审查约束**
- **提交前检查**: 🚨 **必须自测功能完整性**
- **代码审查**: 🚨 **重要功能必须经过代码审查**
- **文档更新**: 🚨 **功能变更必须同步更新文档**
- **向后兼容**: 🚨 **必须考虑向后兼容性**

### **📊 性能优化约束**
- **加载优化**: 🚨 **页面首屏加载时间 < 3秒**
- **内存管理**: 🚨 **必须避免内存泄漏**
- **网络优化**: 🚨 **必须优化API调用频率**
- **缓存策略**: 🚨 **必须合理使用缓存机制**

## 🔧 故障排除规范

### **常见问题及解决方案**

#### **1. Dependencies lock file is not found**
- **原因**: 未指定正确的`cache-dependency-path`
- **解决**: 添加`cache-dependency-path: 'admin/package-lock.json'`

#### **2. npm ci 失败**
- **原因**: 在错误目录执行命令
- **解决**: 使用`cd admin && npm ci`

#### **3. 测试报告上传失败**
- **原因**: 路径不正确
- **解决**: 使用`path: admin/test-reports/`

#### **4. Actions版本弃用警告**
- **原因**: 使用过时版本
- **解决**: 更新到最新稳定版本
  - `actions/setup-python@v4` → `actions/setup-python@v5`
  - `codecov/codecov-action@v3` → `codecov/codecov-action@v4`
  - `github/codeql-action/upload-sarif@v2` → `github/codeql-action/upload-sarif@v3`

#### **5. ESLint错误导致构建失败**
- **原因**: 代码质量问题
- **解决**: 修复所有ESLint错误，警告可以保留

## ✅ 强制检查清单

### **🛠️ 开发环境检查**
- ✅ 使用pyenv管理Python版本
- ✅ 使用nvm管理Node.js版本
- ✅ 一键启动脚本正常工作
- ✅ 热重载功能正常
- ✅ 进程冲突自动清理

### **🎨 UI设计检查**
- ✅ 毕加索艺术风格一致
- ✅ 紫色主色调正确应用
- ✅ 黄金比例布局
- ✅ 窄侧边栏扁平结构
- ✅ PC端多分辨率适配
- ✅ 七彩阴影效果

### **🎯 表单行与菜单栏对齐检查 (2025-01-20新增)**
- ✅ 默认显示10行数据 (pageSize = 10)
- ✅ 数据行高度60px，与菜单项间距匹配
- ✅ 第1-8行与8个菜单项精确对齐 (差异≤5px)
- ✅ 第9-10行保持固定间距，不强制对齐菜单
- ✅ 绿色区域包含所有数据行，无溢出
- ✅ 蓝色区域底部与翻页组件顶部对齐
- ✅ 绿色框底部与蓝色框底部一致 (差异≤2px)
- ✅ 移除第7-8行特殊间距，统一行间距
- ✅ 对齐精度验证通过 (≥75%通过率)
- ✅ 多分辨率下对齐效果保持稳定

### **💰 功能实现检查**
- ✅ 智能搜索支持汉字/拼音/首字母
- ✅ 自动完成下拉选项
- ✅ 实时搜索无刷新按钮
- ✅ 上架项目优先显示
- ✅ 价格提成可点击编辑
- ✅ 修改历史记录时间

### **📦 包管理检查**
- ✅ 使用包管理器安装依赖
- ✅ 禁止手动编辑配置文件
- ✅ 锁文件版本一致
- ✅ 安全依赖更新

### **🧪 测试质量检查**
- ✅ 功能测试覆盖率 ≥ 80%
- ✅ PC端多分辨率测试4种分辨率
- ✅ 自动化测试通过
- ✅ 性能测试达标
- ✅ 兼容性测试通过

### **🔄 Git工作流检查**
- ✅ 使用HTTPS推送
- ✅ main分支为默认
- ✅ 规范commit message
- ✅ 增量小步提交
- ✅ 敏感信息保护

### **🚀 部署流程检查**
- ✅ 环境严格隔离
- ✅ 自动化部署流程
- ✅ 健康检查配置
- ✅ 安全扫描通过
- ✅ 回滚方案准备

### **📚 文档质量检查**
- ✅ API文档完整
- ✅ 用户手册清晰
- ✅ 开发文档准确
- ✅ 部署文档详细
- ✅ 版本控制管理

## 🎯 成功标准

### **🛠️ 开发环境成功标准**
1. **环境搭建**: 一键启动成功，所有服务正常运行
2. **热重载**: 代码修改后自动刷新，无需手动重启
3. **版本管理**: Python和Node.js版本正确，依赖安装成功
4. **进程管理**: 启动前自动清理冲突进程

### **🎨 UI设计成功标准**
1. **视觉一致性**: 毕加索风格统一，紫色主题正确应用
2. **布局规范**: 黄金比例布局，窄侧边栏，无重复标题
3. **PC端多分辨率适配**: 4种桌面分辨率下正常显示，桌面端友好
4. **交互体验**: 动画流畅，阴影效果正确，无滚动条问题

### **💰 功能实现成功标准**
1. **搜索功能**: 汉字/拼音/首字母搜索正常，下拉提示准确
2. **数据排序**: 上架项目优先显示，状态区分明确
3. **编辑功能**: 价格提成可点击编辑，历史记录完整
4. **AI集成**: 图像和文本生成正常，错误处理友好

### **🧪 测试成功标准**
1. **功能测试**: 所有用户交互功能测试通过
2. **PC端多分辨率测试**: 4种桌面分辨率测试通过
3. **性能测试**: 页面加载 < 3秒，API响应 < 1秒
4. **兼容性测试**: 主流浏览器兼容性测试通过

### **🚀 部署成功标准**
1. **自动化部署**: main分支推送后自动部署成功
2. **服务健康**: 部署后所有服务健康检查通过
3. **功能验证**: 核心功能在生产环境正常工作
4. **监控告警**: 监控系统正常，告警机制有效

### **📊 性能标准**
- **页面加载**: 首屏 < 3秒，完整加载 < 5秒
- **API响应**: 查询 < 500ms，更新 < 1秒
- **搜索响应**: 输入响应 < 200ms，结果显示 < 500ms
- **构建时间**: 前端构建 < 5分钟，后端测试 < 10分钟
- **部署时间**: 完整部署 < 15分钟

### **📈 质量标准**
- **代码覆盖率**: 新功能 ≥ 80%，整体 ≥ 70%
- **错误率**: 生产环境错误率 < 0.1%
- **可用性**: 系统可用性 ≥ 99.9%
- **用户满意度**: 功能可用性 ≥ 95%

## 📝 维护规范

### **🔄 定期维护任务**
1. **每日**: 监控系统运行状态，检查错误日志
2. **每周**: 检查依赖安全更新，运行完整测试套件
3. **每月**: 检查Actions版本更新，审查代码质量指标
4. **每季度**: 审查Pipeline性能，优化构建配置
5. **每半年**: 更新安全扫描规则，升级主要依赖版本
6. **每年**: 全面审查技术架构，更新开发规范

### **🚨 应急响应流程**
1. **问题发现**: 监控告警或用户反馈
2. **影响评估**: 评估问题影响范围和严重程度
3. **快速响应**: 15分钟内响应，1小时内初步处理
4. **问题修复**: 根据严重程度制定修复计划
5. **验证测试**: 修复后进行全面测试验证
6. **总结改进**: 问题解决后进行复盘和改进

### **📊 质量监控**
- **代码质量**: 持续监控代码质量指标和技术债务
- **性能监控**: 实时监控系统性能和用户体验指标
- **安全监控**: 定期进行安全扫描和漏洞评估
- **用户反馈**: 收集和分析用户反馈，持续改进产品

### **🔧 工具和流程优化**
- **自动化程度**: 持续提高开发、测试、部署自动化程度
- **效率提升**: 定期评估和优化开发工具链和流程
- **知识管理**: 维护和更新技术文档和最佳实践
- **团队培训**: 定期进行技术培训和规范宣贯

## 🔄 Git工作流规范 (强制)

### **🌿 分支管理约束**
- **主分支**: 🚨 **main分支为默认分支，禁用feature分支**
- **提交方式**: 🚨 **必须使用HTTPS进行git push操作**
- **提交频率**: 🚨 **必须小步快跑，增量提交**
- **提交信息**: 🚨 **必须使用规范的commit message格式**

### **📝 Commit Message 规范**
```
feat: 新增功能描述
fix: 修复问题描述
docs: 文档更新
style: 样式调整
refactor: 代码重构
test: 测试相关
chore: 构建/工具链相关

示例:
feat: 实现服务管理智能搜索功能
fix: 修复价格编辑模态框显示问题
docs: 更新开发规范文档
```

### **🚀 提交流程约束**
1. **开发完成**: 🚨 **必须完成功能开发和自测**
2. **测试验证**: 🚨 **必须运行相关测试并确保通过**
3. **代码审查**: 🚨 **重要功能必须经过人工确认**
4. **文档更新**: 🚨 **必须同步更新相关文档**
5. **提交推送**: 🚨 **使用规范的commit message提交**

### **🔒 提交安全约束**
- **敏感信息**: 🚨 **禁止提交API密钥、密码等敏感信息**
- **大文件**: 🚨 **禁止提交大文件，使用Git LFS**
- **临时文件**: 🚨 **必须配置.gitignore忽略临时文件**
- **代码质量**: 🚨 **禁止提交有明显错误的代码**

## 🚀 部署规范 (强制)

### **🌍 环境管理约束**
- **开发环境**: 🚨 **本地开发，支持热重载**
- **测试环境**: 🚨 **自动化测试，模拟生产环境**
- **生产环境**: 🚨 **仅main分支自动部署**
- **环境隔离**: 🚨 **必须严格隔离不同环境的数据和配置**

### **📦 部署流程约束**
1. **代码合并**: 🚨 **代码合并到main分支**
2. **自动测试**: 🚨 **所有测试必须通过**
3. **构建打包**: 🚨 **自动构建和打包**
4. **部署验证**: 🚨 **部署后必须验证功能正常**
5. **回滚准备**: 🚨 **必须准备快速回滚方案**

### **🔍 部署监控约束**
- **健康检查**: 🚨 **必须配置服务健康检查**
- **日志监控**: 🚨 **必须收集和监控应用日志**
- **性能监控**: 🚨 **必须监控关键性能指标**
- **错误告警**: 🚨 **必须配置错误告警机制**

### **🛡️ 安全部署约束**
- **HTTPS**: 🚨 **生产环境必须使用HTTPS**
- **访问控制**: 🚨 **必须配置适当的访问控制**
- **数据备份**: 🚨 **必须定期备份重要数据**
- **安全扫描**: 🚨 **部署前必须进行安全扫描**

## 📚 文档规范 (强制)

### **📖 文档类型约束**
- **API文档**: 🚨 **必须提供完整的API文档**
- **用户手册**: 🚨 **必须提供用户操作手册**
- **开发文档**: 🚨 **必须提供开发环境搭建文档**
- **部署文档**: 🚨 **必须提供部署和运维文档**

### **✍️ 文档质量约束**
- **及时更新**: 🚨 **功能变更必须同步更新文档**
- **内容准确**: 🚨 **文档内容必须与实际功能一致**
- **格式统一**: 🚨 **必须使用统一的文档格式和模板**
- **易于理解**: 🚨 **文档必须清晰易懂，包含示例**

### **🔄 文档维护约束**
- **版本控制**: 🚨 **文档必须进行版本控制**
- **定期审查**: 🚨 **必须定期审查和更新文档**
- **反馈收集**: 🚨 **必须收集用户反馈并改进文档**
- **多语言**: 🚨 **重要文档必须提供中英文版本**

### **紧急修复流程**
1. 发现Pipeline失败
2. 分析失败原因
3. 创建修复分支
4. 测试修复方案
5. 合并到main分支
6. 验证修复效果

## 🔗 相关资源

### **📚 参考文档**
- [Vue 3 官方文档](https://vuejs.org/)
- [Django 官方文档](https://docs.djangoproject.com/)
- [GitHub Actions 文档](https://docs.github.com/en/actions)
- [Playwright 测试文档](https://playwright.dev/)

### **🛠️ 开发工具**
- [pyenv - Python版本管理](https://github.com/pyenv/pyenv)
- [nvm - Node.js版本管理](https://github.com/nvm-sh/nvm)
- [ESLint - 代码质量检查](https://eslint.org/)
- [Prettier - 代码格式化](https://prettier.io/)

### **🎨 设计资源**
- [毕加索艺术风格参考](https://www.pablo-picasso.org/)
- [色彩搭配工具](https://coolors.co/)
- [响应式设计指南](https://web.dev/responsive-web-design-basics/)

### **🔒 安全资源**
- [OWASP 安全指南](https://owasp.org/)
- [GitHub 安全最佳实践](https://docs.github.com/en/code-security)
- [依赖安全扫描](https://snyk.io/)

## 📞 联系方式

### **🆘 技术支持**
- **开发问题**: 提交GitHub Issue
- **紧急问题**: 联系项目负责人
- **文档问题**: 提交文档改进建议

### **📝 反馈渠道**
- **功能建议**: GitHub Discussions
- **Bug报告**: GitHub Issues
- **文档改进**: Pull Request

---

**📋 文档版本**: v2.0
**📅 最后更新**: 2025-07-12
**👥 维护团队**: 壹心堂开发团队
**🔄 更新频率**: 根据项目发展和最佳实践持续更新
**📖 文档类型**: 强制执行规范
**🎯 适用范围**: 壹心堂中医推拿管理系统全栈开发

> 💡 **重要提醒**: 本文档为强制执行规范，所有开发活动必须严格遵守。违反规范可能导致代码审查不通过或部署失败。如有疑问或建议，请及时反馈。
