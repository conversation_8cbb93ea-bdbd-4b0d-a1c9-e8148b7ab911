# 壹心堂管理系统 - API接口文档

## 📡 API概览

### 基础信息
- **基础URL**: `http://localhost:8000` (开发环境)
- **生产URL**: 微信云托管提供的域名
- **API版本**: v1
- **数据格式**: JSON
- **字符编码**: UTF-8

### 通用响应格式
```json
{
  "code": 0,           // 状态码，0表示成功，非0表示失败
  "data": {},          // 响应数据
  "message": "",       // 响应消息
  "errorMsg": ""       // 错误信息（仅在失败时返回）
}
```

## 🏥 业务API接口

### 1. 服务管理 API

#### 1.1 获取服务列表
```http
GET /api/v1/services/
```

**响应示例**:
```json
{
  "code": 0,
  "data": [
    {
      "id": 1,
      "name": "中式按摩",
      "description": "传统中式按摩，缓解肌肉疲劳",
      "price": "198.00",
      "duration": 60,
      "benefits": ["放松肌肉", "缓解疲劳"],
      "contraindications": ["孕妇禁用"],
      "created_at": "2025-07-05T10:00:00Z",
      "updated_at": "2025-07-05T10:00:00Z"
    }
  ]
}
```

#### 1.2 创建服务
```http
POST /api/v1/services/
Content-Type: application/json

{
  "name": "足疗保健",
  "description": "专业足疗，促进血液循环",
  "price": "168.00",
  "duration": 45,
  "benefits": ["促进血液循环"],
  "contraindications": ["足部有伤口禁用"]
}
```

### 2. 客户管理 API

#### 2.1 获取客户列表
```http
GET /api/v1/customers/
```

#### 2.2 创建客户
```http
POST /api/v1/customers/
Content-Type: application/json

{
  "name": "张女士",
  "phone": "13900139001",
  "gender": "female",
  "address": "上海市浦东新区张江高科技园区",
  "birthday": "1985-06-15",
  "emergency_contact": "李先生",
  "emergency_phone": "13800138000"
}
```

### 3. 技师管理 API

#### 3.1 获取技师列表
```http
GET /api/v1/therapists/
```

#### 3.2 创建技师
```http
POST /api/v1/therapists/
Content-Type: application/json

{
  "name": "张师傅",
  "employee_id": "T001",
  "phone": "13800138001",
  "level": 5,
  "specialty": "中式按摩",
  "experience": 8,
  "specialties": ["按摩", "推拿", "拔罐"],
  "certifications": ["中医按摩师证"]
}
```

### 4. 预约管理 API

#### 4.1 获取预约列表
```http
GET /api/v1/appointments/
```

#### 4.2 创建预约
```http
POST /api/v1/appointments/
Content-Type: application/json

{
  "customer_id": 1,
  "service_id": 1,
  "therapist_id": 1,
  "appointment_time": "2025-07-06T14:00:00Z",
  "customer_requirements": "希望轻一点",
  "notes": "客户是老顾客"
}
```

## 🔧 系统API接口

### 1. 健康检查
```http
GET /health/
```

**响应**: `OK` (HTTP 200)

### 2. 版本信息
```http
GET /api/version/
```

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "current_version": 15,
    "deployment_tag": "prod-v1.5.0",
    "deployment_time": "2025-07-05T10:00:00Z",
    "django_version": "3.2.8",
    "git_commit": "abc123def456"
  }
}
```

### 3. 版本递增
```http
POST /api/version/increment/
Content-Type: application/json

{
  "deployment_tag": "prod-v1.6.0",
  "git_commit": "def456abc789",
  "notes": "新功能发布"
}
```

### 4. 创建测试数据
```http
POST /create_test_data/
Content-Type: application/json
```

**响应示例**:
```json
{
  "code": 0,
  "data": {
    "services": 3,
    "therapists": 2,
    "customers": 2,
    "appointments": 2
  },
  "message": "测试数据创建成功"
}
```

## 📊 数据模型

### Service (服务)
```json
{
  "id": "integer",
  "name": "string",
  "description": "string",
  "price": "decimal",
  "duration": "integer (分钟)",
  "benefits": "array[string]",
  "contraindications": "array[string]",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Customer (客户)
```json
{
  "id": "integer",
  "name": "string",
  "phone": "string",
  "gender": "string (male/female)",
  "address": "string",
  "birthday": "date",
  "emergency_contact": "string",
  "emergency_phone": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Therapist (技师)
```json
{
  "id": "integer",
  "name": "string",
  "employee_id": "string",
  "phone": "string",
  "level": "integer (1-5)",
  "specialty": "string",
  "experience": "integer (年)",
  "specialties": "array[string]",
  "certifications": "array[string]",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

### Appointment (预约)
```json
{
  "id": "integer",
  "customer": "Customer object",
  "service": "Service object",
  "therapist": "Therapist object",
  "appointment_time": "datetime",
  "status": "string (pending/confirmed/completed/cancelled)",
  "customer_requirements": "string",
  "notes": "string",
  "created_at": "datetime",
  "updated_at": "datetime"
}
```

## 🔒 认证和授权

### CSRF保护
所有POST、PUT、DELETE请求需要包含CSRF Token：
```http
X-CSRFToken: your-csrf-token-here
```

### 错误码说明
- `0`: 成功
- `-1`: 通用错误
- `400`: 请求参数错误
- `401`: 未授权
- `403`: 禁止访问
- `404`: 资源不存在
- `500`: 服务器内部错误

## 🧪 测试接口

### 自动化测试专用接口
这些接口仅在开发环境可用，用于自动化测试：

1. **创建测试数据**: `POST /create_test_data/`
2. **清理测试数据**: `POST /clear_test_data/`
3. **重置数据库**: `POST /reset_database/`

## 📝 使用示例

### JavaScript (Axios)
```javascript
// 获取服务列表
const response = await axios.get('http://localhost:8000/api/v1/services/')
console.log(response.data)

// 创建客户
const customer = await axios.post('http://localhost:8000/api/v1/customers/', {
  name: '张女士',
  phone: '13900139001',
  gender: 'female'
}, {
  headers: {
    'Content-Type': 'application/json',
    'X-CSRFToken': getCsrfToken()
  }
})
```

### Python (requests)
```python
import requests

# 获取服务列表
response = requests.get('http://localhost:8000/api/v1/services/')
data = response.json()

# 创建客户
customer_data = {
    'name': '张女士',
    'phone': '13900139001',
    'gender': 'female'
}
response = requests.post(
    'http://localhost:8000/api/v1/customers/',
    json=customer_data,
    headers={'Content-Type': 'application/json'}
)
```

---

**文档版本**: v1.0  
**最后更新**: 2025-07-05  
**维护者**: CTO级别完美主义团队
