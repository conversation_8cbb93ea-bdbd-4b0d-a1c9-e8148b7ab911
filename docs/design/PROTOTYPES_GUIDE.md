# 设计稿说明文档 (Prototypes Guide)

> 📐 **设计稿目录**: `/prototypes/` - 包含完整的UI设计原型和交互演示

## 📋 目录结构

### 🎨 设计原型文件

```
prototypes/
├── 📄 60k-demo.html              # 主演示页面 - 60000美元标准展示
├── 📄 README.md                  # 设计稿说明文档
├── 📄 manifest.json              # PWA应用清单
├── 📄 sw.js                      # Service Worker配置
└── 📁 styles/                    # 样式文件
    └── common.css                # 通用样式
```

### 🏥 管理端设计稿

```
prototypes/admin/
├── 📄 index.html                 # 管理端首页
├── 📄 bookings.html              # 预约管理页面
├── 📄 customers.html             # 客户管理页面
├── 📄 services.html              # 服务管理页面
├── 📄 therapists.html            # 技师管理页面
├── 📄 finance.html               # 财务管理页面
├── 📄 test-navigation.html       # 导航测试页面
└── 📁 src/                       # 源码文件
```

### 📱 小程序设计稿

```
prototypes/miniprogram/
├── 📄 index.html                 # 小程序首页
├── 📄 booking.html               # 预约页面
├── 📄 my-services.html           # 我的服务页面
├── 📄 profile.html               # 个人中心页面
├── 📄 test-navigation.html       # 导航测试页面
└── 🖼️ logo.svg                   # 应用Logo
```

### 🔧 开发工具

```
prototypes/scripts/
├── 📄 dev.js                     # 开发服务器
├── 📄 build.js                   # 构建脚本
├── 📄 check-standards.js         # 规范检查
├── 📄 clean.js                   # 清理脚本
├── 📄 common.js                  # 通用工具
├── 📄 ai-intelligence.js         # AI智能功能
├── 📄 data-visualization.js      # 数据可视化
├── 📄 advanced-interactions.js   # 高级交互
├── 📄 internationalization.js    # 国际化支持
├── 📄 pwa-manager.js             # PWA管理
├── 📄 realtime-sync.js           # 实时同步
└── 📄 security-manager.js        # 安全管理
```

### 📊 数据和文档

```
prototypes/data/
└── 📄 mock-data.js               # 模拟数据

prototypes/docs/
└── 📁 reports/                   # 报告文档

prototypes/server/                # 后端原型
prototypes/client/                # 前端原型
```

## 🎯 设计稿用途

### 1. **UI/UX设计参考**
- ✅ 完整的界面设计原型
- ✅ 交互流程演示
- ✅ 视觉效果展示
- ✅ 响应式布局参考

### 2. **开发实现指导**
- ✅ 页面结构参考
- ✅ 功能模块划分
- ✅ 数据结构设计
- ✅ API接口规划

### 3. **产品演示展示**
- ✅ 客户演示材料
- ✅ 功能特性展示
- ✅ 用户体验演示
- ✅ 技术能力证明

## 🚀 使用方法

### 查看设计稿

```bash
# 进入设计稿目录
cd prototypes

# 启动本地服务器
python -m http.server 8080

# 访问演示页面
open http://localhost:8080/60k-demo.html
```

### 主要演示页面

1. **💰 60K标准演示**: `60k-demo.html`
   - 展示60000美元标准的完整功能
   - 包含所有核心业务模块
   - 高质量UI/UX设计展示

2. **🏥 管理端演示**: `admin/index.html`
   - 完整的后台管理系统
   - 预约、客户、服务、财务管理
   - 现代化管理界面设计

3. **📱 小程序演示**: `miniprogram/index.html`
   - 微信小程序界面设计
   - 用户端功能完整展示
   - 移动端交互体验

## 📐 设计规范

### 视觉设计
- **色彩方案**: 中医理疗主题色彩
- **字体规范**: 现代简洁字体
- **图标系统**: 统一的图标风格
- **布局网格**: 响应式栅格系统

### 交互设计
- **导航模式**: 清晰的信息架构
- **操作流程**: 简化的用户操作
- **反馈机制**: 及时的状态反馈
- **错误处理**: 友好的错误提示

## 🎨 UI组件设计规范

### 模态框组件规范

#### 设计原则
- **梵高风格主题**: 紫色渐变背景，艺术感设计
- **毛玻璃效果**: `backdrop-filter: blur(4px)` 现代化视觉
- **响应式适配**: 桌面/平板/手机三种布局
- **流畅动画**: 淡入淡出过渡效果

#### 结构标准
```vue
<!-- 模态框标准结构 -->
<div v-if="modalVisible" class="modal-overlay">
  <div class="form-modal" @click.stop>
    <div class="form-header">
      <h3 class="form-title">{{ modalTitle }}</h3>
      <button class="close-btn" @click="hideModal">×</button>
    </div>
    <div class="form-content">
      <!-- 表单内容 -->
    </div>
    <div class="form-actions">
      <!-- 操作按钮 -->
    </div>
  </div>
</div>
```

#### 尺寸规范
- **桌面端**: 最大宽度600px，居中显示
- **平板端**: 90%宽度，适应屏幕
- **手机端**: 95%宽度，全屏体验
- **最大高度**: 80vh，内容区域可滚动

### 表单验证规范

#### 验证原则
- **实时反馈**: 提交时触发完整验证
- **视觉提示**: 错误字段红色边框高亮
- **清晰信息**: 具体的错误提示文本
- **状态管理**: 独立的错误状态对象

#### 必填字段标识
```vue
<label class="form-label">
  字段名称 <span class="required">*</span>
</label>
```

#### 错误状态显示
```vue
<input
  v-model="formState.field"
  class="form-input"
  :class="{ 'error': formErrors.field }"
  required
/>
<div v-if="formErrors.field" class="error-message">
  {{ formErrors.field }}
</div>
```

#### 验证规则标准
| 字段类型 | 验证规则 | 错误提示格式 |
|---------|---------|-------------|
| **姓名** | 必填，至少2个字符 | "请输入XXX" / "XXX至少2个字符" |
| **工号** | 必填，字母数字组合 | "请输入工号" / "工号只能包含字母和数字" |
| **手机号** | 必填，11位数字格式 | "请输入联系电话" / "请输入正确的手机号码" |
| **邮箱** | 可选，邮箱格式 | "请输入正确的邮箱地址" |

### 加载状态管理规范

#### 状态定义标准
```javascript
// 统一的加载状态管理
const loadingStates = reactive({
  dataLoading: false,      // 数据加载状态
  submitLoading: false,    // 提交加载状态
  deleteLoading: false     // 删除加载状态
});
```

#### 按钮状态控制
```vue
<button
  class="confirm-btn"
  :disabled="loadingStates.submitLoading"
  @click="handleSubmit"
>
  {{ loadingStates.submitLoading ? '提交中...' : '确定' }}
</button>
```

#### 加载提示样式
- **按钮文本**: "提交中..." / "加载中..." / "处理中..."
- **禁用状态**: 灰色背景，不可点击
- **视觉反馈**: 加载动画或文本变化

### 用户体验规范

#### 操作反馈标准
- **成功操作**: Console日志 + 模态框关闭
- **失败操作**: 错误提示 + 保持模态框打开
- **验证失败**: 字段高亮 + 错误信息显示

#### 数据流转规范
```javascript
// 新增数据流程
const handleAdd = () => {
  if (!validateForm()) return;
  // 添加到列表顶部
  items.value.unshift(newItem);
};

// 编辑数据流程
const handleEdit = (item) => {
  // 数据回填
  Object.assign(formState, {...item});
  // 更新列表中对应项
  items.value[index] = updatedItem;
};
```

#### 表单重置规范
- **新增模态框**: 清空所有字段和错误状态
- **编辑模态框**: 预填充现有数据
- **关闭模态框**: 重置表单状态

### 技术规范
- **响应式设计**: 适配多种设备
- **性能优化**: 快速加载体验
- **可访问性**: 无障碍设计支持
- **SEO优化**: 搜索引擎友好

## 🎨 CSS样式规范

### 模态框样式标准
```css
/* 模态框遮罩层 */
.modal-overlay {
  position: fixed;
  top: 0; left: 0; right: 0; bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  z-index: 1000;
  animation: fadeIn 0.3s ease-out;
}

/* 主模态框容器 */
.form-modal {
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(248, 250, 252, 0.95) 100%);
  border-radius: 20px;
  box-shadow: 0 20px 60px rgba(139, 92, 246, 0.3);
  max-width: 600px;
  width: 90%;
  max-height: 80vh;
}
```

### 表单验证样式标准
```css
/* 必填字段标识 */
.required {
  color: #ef4444;
  font-weight: bold;
}

/* 错误状态输入框 */
.form-input.error {
  border-color: #ef4444;
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.2);
}

/* 错误提示信息 */
.error-message {
  color: #ef4444;
  font-size: 0.85rem;
  margin-top: 4px;
  font-weight: 500;
}
```

### 响应式设计标准
```css
/* 移动端适配 */
@media (max-width: 768px) {
  .form-modal {
    width: 95%;
    margin: 20px;
  }

  .form-row {
    flex-direction: column;
  }

  .form-actions {
    flex-direction: column;
  }
}
```

## 📋 实现检查清单

### 模态框功能检查
- [ ] 模态框显示/隐藏正常
- [ ] 新增按钮触发正确
- [ ] 编辑按钮触发正确
- [ ] 关闭按钮功能正常
- [ ] 遮罩点击关闭功能
- [ ] 响应式布局适配

### 表单验证检查
- [ ] 必填字段验证
- [ ] 格式验证（手机号、邮箱等）
- [ ] 错误提示显示
- [ ] 错误状态清除
- [ ] 视觉反馈正确

### 用户体验检查
- [ ] 加载状态显示
- [ ] 按钮禁用状态
- [ ] 成功操作反馈
- [ ] 错误处理机制
- [ ] 数据流转正确

### 性能优化检查
- [ ] 防抖处理
- [ ] 内存泄漏检查
- [ ] 动画性能优化
- [ ] 大数据量处理

## 🔄 与实际开发的关系

### 设计稿 → 实际开发映射

| 设计稿文件 | 实际开发文件 | 说明 |
|-----------|-------------|------|
| `admin/*.html` | `admin/src/views/` | Vue管理端页面实现 |
| `miniprogram/*.html` | `client/src/` | Taro小程序实现 |
| `server/` | `server/` | Django后端实现 |
| `styles/common.css` | 各模块样式文件 | 样式规范参考 |

### 组件规范映射

| 规范组件 | 实际实现 | 应用页面 |
|---------|---------|---------|
| **模态框组件** | `modal-overlay + form-modal` | 所有管理页面 |
| **表单验证** | `validateForm() + formErrors` | 新增/编辑功能 |
| **加载状态** | `loadingStates` 响应式对象 | 异步操作场景 |
| **分页组件** | 梵高风格分页容器 | 数据列表页面 |

### 开发注意事项

1. **严格按照设计稿实现**: 确保UI一致性
2. **保持交互逻辑**: 遵循设计稿的用户流程
3. **响应式适配**: 确保多设备兼容性
4. **性能优化**: 实现设计稿的性能要求

## 💡 最佳实践

### 模态框开发最佳实践
1. **状态管理**: 使用Vue 3 Composition API的`ref()`和`reactive()`
2. **事件处理**: 正确绑定点击事件，避免事件冒泡
3. **数据回填**: 使用`Object.assign()`进行深拷贝
4. **错误处理**: 统一的错误状态管理和清理机制

### 表单验证最佳实践
1. **验证时机**: 提交时进行完整验证，避免过度实时验证
2. **错误提示**: 具体明确的错误信息，避免模糊提示
3. **视觉反馈**: 错误字段高亮，成功状态清晰
4. **用户体验**: 验证失败后保持用户输入，不要清空表单

### 性能优化最佳实践
1. **防抖处理**: 避免重复提交和频繁验证
2. **懒加载**: 大数据量时使用虚拟滚动
3. **内存管理**: 及时清理事件监听器和定时器
4. **动画优化**: 使用CSS3动画，避免JavaScript动画

## 🔧 常见问题解决

### 模态框不显示问题
**症状**: 点击按钮后模态框不出现
**排查步骤**:
1. 检查`modalVisible`状态是否正确更新
2. 确认CSS样式是否正确加载
3. 检查z-index层级是否被覆盖
4. 验证事件绑定是否正确

### 表单验证不生效问题
**症状**: 提交时验证函数不执行
**排查步骤**:
1. 确认`validateForm()`函数是否正确定义
2. 检查提交函数中是否调用验证
3. 验证`formErrors`响应式对象是否正确
4. 确认错误提示DOM是否正确渲染

### 数据更新不同步问题
**症状**: 表单提交后列表数据未更新
**排查步骤**:
1. 检查数据流转逻辑是否正确
2. 确认Vue响应式系统是否正常工作
3. 验证数组/对象更新方式是否正确
4. 检查是否存在异步操作时序问题

## 🚨 开发工作流程规范 (强制执行)

### **📋 每次开发任务的强制流程**

#### **1. 开发前准备 (必须完成)**
```bash
# 运行开发工作流程脚本
python development_workflow.py
```

#### **2. 开发过程规范 (强制遵守)**
- 🚨 **严格按照CI_CD_STANDARDS.md执行**
- 🚨 **每个功能完成后立即自测**
- 🚨 **遇到问题立即查阅规范文档**
- 🚨 **不确定的地方必须询问确认**

#### **3. 提交前检查 (强制执行)**
```bash
# 运行强制规范检查
python enforce_standards_check.py

# 如果检查失败，必须修复所有问题
# 重新运行检查直到全部通过
```

#### **4. 代码提交规范**
- 🚨 **所有ERROR级别问题必须修复**
- 🚨 **所有WARNING级别问题必须处理**
- 🚨 **提交信息必须清晰描述修改内容**
- 🚨 **必须包含测试结果和验证截图**

### **🔧 问题修复强制原则**

#### **发现问题时的处理流程**
1. **立即停止开发** - 不允许在有已知问题的基础上继续
2. **分析问题根源** - 查找问题的根本原因
3. **制定修复方案** - 确保修复方案符合规范
4. **实施修复** - 按照规范进行修复
5. **验证修复效果** - 重新运行检查确认修复
6. **记录修复过程** - 更新相关文档

#### **不允许的行为**
- ❌ **忽略ERROR级别的问题**
- ❌ **临时绕过规范检查**
- ❌ **提交有已知问题的代码**
- ❌ **修改检查脚本来"通过"检查**

## 📝 更新日志

### v2.2.0 (2025-01-16)
- 新增开发工作流程强制规范
- 添加问题修复强制原则
- 完善代码提交规范
- 增加开发过程质量控制

### v2.1.0 (2025-01-16)
- 新增模态框组件设计规范
- 添加表单验证标准规范
- 完善加载状态管理规范
- 增加CSS样式标准和最佳实践
- 添加实现检查清单和问题解决方案

### v2.0.0 (2025-01-16)
- 完善梵高风格设计规范
- 添加响应式设计标准
- 更新组件库规范
- 优化用户体验指南

---

**设计标准**: 60000美元企业级标准  
**更新时间**: 2025-07-05  
**维护状态**: 活跃维护
