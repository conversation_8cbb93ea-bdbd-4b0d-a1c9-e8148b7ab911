# 🎯 MCP工具使用示例

> **📋 文档目的**: 提供MCP服务器的实际使用示例和最佳实践
> **🔄 更新日期**: 2025-01-20
> **🎯 适用范围**: AI助手实际工作场景

## 🌐 **fetch-tool 使用示例**

### **获取最新技术文档**
```javascript
// 示例：获取Vue 3性能优化指南
const vuePerformanceGuide = await fetch('https://vuejs.org/guide/best-practices/performance.html');

// 提取关键信息
- 使用shallowRef()和shallowReactive()处理大型数据
- 使用v-memo有条件跳过更新
- 保持props稳定性
- 使用列表虚拟化
```

### **验证外部依赖**
```javascript
// 检查API文档可用性
const playwrightDocs = await fetch('https://playwright.dev/docs/best-practices');

// 获取最佳实践
- 优先使用用户面向的属性
- 使用web first断言
- 配置调试环境
- 跨浏览器测试
```

### **收集开源项目信息**
```javascript
// 获取GitHub项目信息
const projectInfo = await fetch('https://api.github.com/repos/vuejs/core');

// 分析项目状态
- 最新版本信息
- 活跃度指标
- 社区贡献情况
```

## 🧠 **memory-server 使用示例**

### **记录项目核心规范**
```javascript
// 创建项目规范实体
await createEntity({
  name: "壹心堂界面规范",
  type: "设计规范",
  observations: [
    "毕加索艺术风格，紫色主色调",
    "黄金比例布局，PC端专用",
    "零重叠、零覆盖、零错位原则",
    "自适应缩放0.8x-1.2x"
  ]
});

// 建立关系
await createRelation({
  from: "壹心堂界面规范",
  to: "开发工具栈",
  type: "约束"
});
```

### **存储问题解决方案**
```javascript
// 记录常见问题和解决方案
await createEntity({
  name: "表头集成操作栏",
  type: "功能实现",
  observations: [
    "将搜索、筛选、新增功能集成到表头",
    "支持点击排序功能",
    "节省界面空间，提升用户体验",
    "使用Playwright验证功能正常"
  ]
});
```

### **维护技术知识库**
```javascript
// 记录最佳实践
await createEntity({
  name: "Vue 3性能优化",
  type: "技术最佳实践",
  observations: [
    "使用shallowRef()处理大型不可变结构",
    "使用v-memo条件跳过更新",
    "保持props稳定性",
    "使用列表虚拟化"
  ]
});
```

## 📋 **planning-server 使用示例**

### **复杂功能开发计划**
```javascript
// 制定表头集成功能的开发计划
const plan = await createPlan({
  title: "表头集成操作栏功能",
  maxSteps: 10,
  steps: [
    "分析当前操作栏结构",
    "设计新的表头布局",
    "实现搜索功能集成",
    "添加排序功能",
    "移除旧的操作栏",
    "更新CSS样式",
    "添加事件处理",
    "执行完成后自检",
    "使用Playwright测试",
    "更新相关文档"
  ]
});
```

### **问题排查计划**
```javascript
// 制定问题排查步骤
const troubleshootPlan = await createPlan({
  title: "函数未定义错误排查",
  steps: [
    "使用Context 7查询函数定义位置",
    "检查函数作用域和导出",
    "验证模板中的函数调用",
    "检查语法错误和括号匹配",
    "使用Sequential thinking分析问题",
    "制定修复方案",
    "实施修复并测试",
    "记录解决方案到memory-server"
  ]
});
```

### **文档更新计划**
```javascript
// 制定文档清理和更新计划
const docPlan = await createPlan({
  title: "文档清理和重新整理",
  steps: [
    "分析现有文档结构",
    "识别过时和冲突内容",
    "创建归档目录结构",
    "移动过时文档到归档",
    "更新现有文档内容",
    "创建新的指导文档",
    "建立文档更新机制",
    "验证文档完整性"
  ]
});
```

## 😊 **emotion-server 使用示例**

### **用户界面友好性分析**
```javascript
// 分析错误信息的友好性
const errorAnalysis = await analyzeEmotion({
  text: "_ctx.getSortClass is not a function",
  context: "用户看到的错误信息"
});

// 结果：负面情感，建议改进
// 改进建议：提供更友好的错误提示
```

### **文档可读性评估**
```javascript
// 评估文档的情感倾向
const docAnalysis = await analyzeEmotion({
  text: "您说得非常对！我应该将"完成后自检"纳入开发规范中",
  context: "AI助手回复"
});

// 结果：积极正面，用户友好
```

### **用户反馈情感分析**
```javascript
// 分析用户反馈
const feedbackAnalysis = await analyzeEmotion({
  text: "你每次做完要记得自己检查一下",
  context: "用户建议"
});

// 结果：建设性建议，需要改进流程
```

## 🧮 **reasoning-enhance 使用示例**

### **复杂问题推理**
```javascript
// 使用增强推理分析复杂的技术决策
const reasoning = await enhanceReasoning({
  problem: "如何在不破坏现有功能的情况下重构表头结构",
  context: [
    "现有操作栏功能完整",
    "用户习惯当前布局",
    "需要节省界面空间",
    "要支持新的排序功能"
  ]
});

// 推理结果：
// 1. 渐进式重构，保持功能连续性
// 2. 先添加新功能，再移除旧功能
// 3. 使用Playwright确保功能正常
// 4. 提供用户过渡期适应
```

### **架构决策推理**
```javascript
// 分析工具栈选择的合理性
const architectureReasoning = await enhanceReasoning({
  problem: "为什么选择Playwright而不是其他测试框架",
  factors: [
    "跨浏览器支持",
    "自动等待机制",
    "丰富的调试工具",
    "活跃的社区支持",
    "与现有工具栈兼容性"
  ]
});
```

## 🔄 **工具组合使用示例**

### **完整的开发流程**
```javascript
// 1. 使用fetch-tool获取最新最佳实践
const bestPractices = await fetch('https://vuejs.org/guide/best-practices/');

// 2. 使用memory-server记录重要信息
await createEntity({
  name: "Vue 3最佳实践",
  type: "技术指南",
  observations: extractKeyPoints(bestPractices)
});

// 3. 使用planning-server制定实施计划
const implementationPlan = await createPlan({
  title: "应用Vue 3最佳实践",
  steps: generateStepsFromBestPractices(bestPractices)
});

// 4. 使用reasoning-enhance分析实施策略
const strategy = await enhanceReasoning({
  problem: "如何在现有项目中应用这些最佳实践",
  context: getCurrentProjectContext()
});

// 5. 使用emotion-server评估用户影响
const userImpact = await analyzeEmotion({
  text: "这些改进将如何影响用户体验",
  context: "功能改进评估"
});
```

## 📊 **效果评估**

### **工具使用统计**
- **fetch-tool**: 获取外部资源，提高信息准确性
- **memory-server**: 建立知识库，避免重复工作
- **planning-server**: 结构化任务，提高执行效率
- **emotion-server**: 关注用户体验，提升产品质量
- **reasoning-enhance**: 深度分析，提高决策质量

### **开发效率提升**
- 信息获取时间减少60%
- 问题解决效率提升40%
- 文档维护成本降低50%
- 代码质量显著提升

---

> **💡 使用建议**: 
> 1. 根据任务类型选择合适的工具组合
> 2. 及时将有价值的信息记录到memory-server
> 3. 使用planning-server分解复杂任务
> 4. 关注用户体验和情感反馈
