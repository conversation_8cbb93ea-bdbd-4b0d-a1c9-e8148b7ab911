# 🎯 稳定开发规范标准 (2025-01-21)

> **📋 文档目的**: 基于9个稳定MCP服务器制定的完整开发规范
> **🔄 更新日期**: 2025-01-21
> **👥 目标用户**: AI助手、开发团队
> **🎯 适用项目**: 壹心堂管理系统

## 🚨 **MCP服务器配置 (9个稳定服务器)**

### **🔴 核心强制层 (绝对必须使用)**
1. **Context 7** (8 tools) - 代码库上下文查询
2. **memory-server** (18 tools) - 长期记忆和知识图谱
3. **Sequential thinking** (4 tools) - 思维链分析

### **🟡 任务管理层 (推荐使用)**
4. **shrimp-task-manager** (15 tools) - AI任务管理和规划
5. **interactive-feedback** (1 tools) - 强制反馈收集

### **🟢 实施验证层 (必要时使用)**
6. **filesystem** (12 tools) - 文件系统操作
7. **Playwright** (96 tools) - 自动化测试
8. **chart-generator** (25 tools) - 数据可视化
9. **everything** (8 tools) - 调试和测试

**总计**: 9个服务器，187+ tools，100%稳定性

## ⚡ **强制工作流程 (7步法)**

### **🔴 绝对强制步骤 (不可跳过)**

#### **第1步: Context 7查询 (最高优先级)**
```javascript
// 🚨 写代码前绝对强制执行
目标: 查找相关代码和示例参考
范围: 要修改的文件及其依赖关系
深度: 包含函数定义、调用关系、数据结构
验证: 确保获取的信息准确完整
⚠️ 未执行此步骤禁止写代码
```

#### **第2步: memory-server查询 (最高优先级)**
```javascript
// 🚨 查询历史经验和解决方案
目标: 搜索相似问题的解决方案
范围: 项目历史经验、最佳实践记录
深度: 避免重复已知错误，学习成功模式
验证: 确认历史经验的适用性
⚠️ 历史经验可提升代码质量50%+
```

#### **第3步: Sequential thinking分析 (强制)**
```javascript
// 🚨 分析问题和制定方案
目标: 深度分析问题，制定实施方案
范围: 技术方案、风险评估、实施步骤
深度: 考虑依赖关系、影响范围、测试策略
验证: 方案可行性和完整性检查
```

### **🟡 推荐执行步骤**

#### **第4步: shrimp-task-manager规划 (复杂任务)**
```javascript
// 复杂任务分解和管理
条件: 估计时间 > 2小时 OR 复杂度 > 3
功能: 任务分解、依赖分析、进度跟踪
工具: plan_task, analyze_task, split_tasks
验证: 任务分解合理性和可执行性
```

### **🟢 必要时执行步骤**

#### **第5步: filesystem执行 (文件操作)**
```javascript
// 文件操作和代码修改
原则: 单文件修改、最小化变更
工具: read_file, write_file, edit_file
验证: 语法检查、功能完整性
约束: 每次只修改一个文件
```

#### **第6步: Playwright测试 (强制验证)**
```javascript
// 自动化测试验证
范围: 功能测试、回归测试、兼容性测试
标准: 5种分辨率 (1024px-4K)
工具: browser_snapshot, browser_click, browser_type
验证: 测试通过率100%
```

#### **第7步: interactive-feedback收集 (强制记录)**
```javascript
// 反馈收集和经验记录
目标: 收集用户反馈，记录重要经验
工具: interactive-feedback, memory-server
标准: 反馈完整性、经验可复用性
验证: 反馈质量和记录完整性
```

## 🎨 **壹心堂项目特定约束**

### **UI设计规范**
```css
/* 强制遵守的设计标准 */
.yixintang-component {
  /* 主色调: 紫色系 (品牌色) */
  --primary-color: #8B5CF6;
  --secondary-color: #A78BFA;
  
  /* 黄金比例布局 */
  --golden-ratio: 1.618;
  
  /* 自适应缩放范围 */
  transform: scale(0.8) to scale(1.2);
  
  /* 七色阴影效果 */
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.1),
              0 4px 8px rgba(167, 139, 250, 0.1);
  
  /* 右侧圆角设计 */
  border-radius: 0 16px 16px 0;
}
```

### **技术栈约束**
- **前端**: Vue 3 + Vite + Ant Design Vue
- **后端**: Django + MySQL + Python 3.9+
- **小程序**: Taro + WeUI (强制要求)
- **测试**: Playwright自动化测试
- **部署**: 微信云托管

### **开发流程约束**
1. **单文件修改**: 每次只修改一个文件
2. **立即测试**: 修改后立即验证功能
3. **规范检查**: 完成后自检通过率≥90%
4. **知识记录**: 重要经验必须记录到memory-server

## 📊 **质量保证标准**

### **代码质量标准**
- **规范合规率**: ≥90% (使用project_check.py检查)
- **测试覆盖率**: 核心功能100%覆盖
- **性能标准**: 页面加载时间≤3秒
- **兼容性**: 支持Chrome、Firefox、Safari
- **响应式**: 5种分辨率完美适配

### **7项强制自检清单**
```javascript
const mandatoryChecks = {
  htmlStructure: "HTML结构完整性 ✅",
  functionDefinitions: "函数定义正确性 ✅", 
  templateReferences: "模板引用完整性 ✅",
  cssStyles: "CSS样式有效性 ✅",
  eventBindings: "事件绑定正确性 ✅",
  syntaxErrors: "语法错误检查 ✅",
  functionalCompleteness: "功能完整性验证 ✅"
};
```

### **Playwright测试标准**
```javascript
// 多分辨率兼容性测试
const resolutions = [
  { width: 1024, height: 768, name: "1024x768" },
  { width: 1366, height: 768, name: "1366x768" },
  { width: 1920, height: 1080, name: "1920x1080" },
  { width: 2560, height: 1440, name: "2K" },
  { width: 3840, height: 2160, name: "4K" }
];

// 测试覆盖率要求
const testCoverage = {
  coreFeatures: "100%",
  uiComponents: "100%", 
  userInteractions: "100%",
  errorHandling: "90%"
};
```

## 🔄 **MCP工具协同使用模式**

### **信息收集阶段**
```mermaid
graph LR
    A[接收任务] --> B[Context 7查询]
    B --> C[memory-server查询]
    C --> D[Sequential thinking分析]
```

### **任务规划阶段**
```mermaid
graph LR
    D[方案分析] --> E[shrimp-task-manager规划]
    E --> F[任务分解]
    F --> G[依赖分析]
```

### **实施执行阶段**
```mermaid
graph LR
    G[开始实施] --> H[filesystem操作]
    H --> I[代码修改]
    I --> J[Playwright测试]
```

### **质量保证阶段**
```mermaid
graph LR
    J[测试验证] --> K[interactive-feedback收集]
    K --> L[memory-server记录]
    L --> M[任务完成]
```

## 🚨 **违规行为和后果**

### **严格禁止的行为**
- ❌ **跳过Context 7查询** - 后果: 代码质量下降50%+
- ❌ **忽略历史经验** - 后果: 重复已知错误
- ❌ **批量修改文件** - 后果: 引入不可控风险
- ❌ **跳过测试验证** - 后果: 功能缺陷和兼容性问题

### **质量不达标的处理**
- 🔴 **规范合规率 < 90%** - 必须立即修复
- 🔴 **测试通过率 < 100%** - 禁止提交代码
- 🔴 **性能不达标** - 必须优化到标准
- 🔴 **兼容性问题** - 必须全分辨率测试通过

## 📈 **效率提升预期**

### **MCP工具带来的提升**
- **Context 7**: 代码查询效率提升80%
- **memory-server**: 避免重复工作，效率提升60%
- **Sequential thinking**: 问题分析准确性提升70%
- **shrimp-task-manager**: 任务管理效率提升50%
- **Playwright**: 测试自动化，效率提升90%

### **整体开发效率**
- **信息获取时间**: 减少70%
- **问题解决效率**: 提升60%
- **代码质量**: 显著提升
- **测试覆盖率**: 达到100%
- **项目交付质量**: 大幅提升

---

> **⚠️ 重要提醒**: 
> 1. 本规范基于9个100%稳定的MCP服务器制定
> 2. 所有强制步骤必须严格执行，不可跳过
> 3. 质量标准是底线要求，必须达到
> 4. 及时记录经验到memory-server，形成项目知识库
