# 📋 文档清理计划 (2025-01-20)

> **目标**: 清理过时文档，重新组织文档结构，确保与当前工具栈一致

## 🎯 **清理原则**

### **保留标准**
- ✅ 与当前工具栈一致
- ✅ 包含有效的开发规范
- ✅ 近期更新且内容准确
- ✅ 对AI助手有指导价值

### **归档标准**
- 📁 包含Node.js相关内容
- 📁 使用已弃用的测试方法
- 📁 与当前规范冲突
- 📁 内容重复或过时

### **删除标准**
- 🗑️ 完全无用的临时文件
- 🗑️ 空文件或测试文件
- 🗑️ 严重过时且无参考价值

## 📊 **文档分析结果**

### **🚨 需要立即归档的文档**
1. **TESTING.md** - 包含大量Node.js和npm命令
2. **TROUBLESHOOTING.md** - 主要解决Node.js环境问题
3. **DEVELOPMENT_ISSUES.md** - 过时的开发问题
4. **IDE_SETUP.md** - 包含Node.js配置
5. **DEVELOPMENT_TROUBLESHOOTING_GUIDE.md** - 重复内容

### **🔄 需要更新的文档**
1. **CI_CD_STANDARDS.md** - 移除Node.js相关内容
2. **README.md** - 更新工具栈说明
3. **PERFECT_TESTING_PLAN.md** - 更新为Playwright测试

### **✅ 保持现状的文档**
1. **DEVELOPMENT_CONSTRAINTS.md** - 已更新，符合当前规范
2. **PROBLEM_EXPERIENCE_LIBRARY.md** - 有效的经验库
3. **AI_INTEGRATION_GUIDE.md** - AI服务集成指南

## 🔄 **执行计划**

### **阶段1: 创建归档目录**
```bash
mkdir -p docs/archive/outdated
mkdir -p docs/archive/nodejs-related
mkdir -p docs/archive/old-testing
```

### **阶段2: 移动过时文档**
- `TESTING.md` → `docs/archive/old-testing/`
- `TROUBLESHOOTING.md` → `docs/archive/nodejs-related/`
- `DEVELOPMENT_ISSUES.md` → `docs/archive/outdated/`
- `IDE_SETUP.md` → `docs/archive/nodejs-related/`
- `DEVELOPMENT_TROUBLESHOOTING_GUIDE.md` → `docs/archive/outdated/`

### **阶段3: 更新现有文档**
1. **CI_CD_STANDARDS.md**: 移除Node.js包管理规范
2. **README.md**: 更新技术栈说明
3. **PERFECT_TESTING_PLAN.md**: 替换为Playwright测试方法

### **阶段4: 创建新文档**
1. **PLAYWRIGHT_TESTING_GUIDE.md**: Playwright测试指南
2. **AI_WORKFLOW_GUIDE.md**: AI工作流程指南

## 📝 **更新内容清单**

### **CI_CD_STANDARDS.md 更新**
- ❌ 移除: Node.js项目结构
- ❌ 移除: npm/yarn包管理规范
- ✅ 保留: Python包管理规范
- ✅ 保留: Git工作流规范

### **README.md 更新**
- ❌ 移除: Node.js环境要求
- ❌ 移除: npm相关命令
- ✅ 更新: 当前技术栈说明
- ✅ 添加: Playwright测试说明

### **PERFECT_TESTING_PLAN.md 更新**
- ❌ 移除: 浏览器控制台测试
- ❌ 移除: 手动测试方法
- ✅ 更新: Playwright自动化测试
- ✅ 保留: 界面测试标准

## 🏷️ **归档文档标记**

### **归档文件头部标记**
```markdown
# ⚠️ 已归档文档 - 仅供历史参考

> **归档日期**: 2025-01-20
> **归档原因**: 包含已弃用的Node.js相关内容
> **替代文档**: [CURRENT_DEVELOPMENT_GUIDE.md](../CURRENT_DEVELOPMENT_GUIDE.md)
> **状态**: 不再维护，仅供历史参考

---

# 原文档内容
```

## 🎯 **完成后验证**

### **验证清单**
- [ ] 所有过时文档已移动到archive目录
- [ ] 现有文档已移除Node.js相关内容
- [ ] 新文档已创建并包含当前规范
- [ ] 文档结构清晰，无重复内容
- [ ] AI助手可以清晰识别当前有效文档

### **测试方法**
1. 检查docs目录结构
2. 验证归档文档标记
3. 确认更新文档内容
4. 测试AI助手文档引用

## 📊 **预期效果**

### **清理前问题**
- 文档冲突，AI助手困惑
- 过时信息误导开发
- 重复内容维护困难
- Node.js相关内容与实际不符

### **清理后效果**
- 文档结构清晰明确
- AI助手有明确指引
- 开发规范统一一致
- 维护成本大幅降低

---

> **执行时间**: 预计30分钟完成所有清理工作
> **负责人**: AI助手执行，开发者确认
