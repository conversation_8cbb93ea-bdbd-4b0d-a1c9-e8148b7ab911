# 📖 Augment Chat 完整用户指南 v3.0

> **📋 文档目的**: 基于完整开发规范体系的Augment Chat权威使用指南  
> **🔄 更新日期**: 2025-01-21  
> **🎯 适用项目**: 壹心堂管理系统  
> **👥 目标用户**: AI助手、开发团队、项目管理者  
> **📊 配置状态**: 9个稳定MCP服务器，100%可靠性

## 🚨 核心原则 (绝对遵守)

### ⚡ 强制执行的开发流程
```mermaid
graph TD
    A[接收任务] --> B[🚨 Context 7查询]
    B --> C[🚨 memory-server查询]
    C --> D[🚨 Sequential thinking分析]
    D --> E[shrimp-task-manager规划]
    E --> F[filesystem执行]
    F --> G[🚨 Playwright测试]
    G --> H[🚨 interactive-feedback收集]
    H --> I[🚨 memory-server记录]
```

### 🔴 绝对禁止的行为
- ❌ **跳过Context 7查询** - 后果：代码质量下降50%+
- ❌ **忽略历史经验查询** - 后果：重复已知错误
- ❌ **批量修改文件** - 后果：引入不可控风险
- ❌ **跳过测试验证** - 后果：功能缺陷和兼容性问题
- ❌ **不收集反馈** - 后果：无法持续改进

## 🛠️ MCP服务器生态系统 (9个稳定服务器)

### 🔴 核心强制层 (绝对必须使用)
1. **Context 7** (8 tools) - 代码库上下文查询
   ```
   // 使用场景：写代码前必须执行
   功能：查找相关代码和示例参考
   优先级：最高 (绝对强制)
   成功标准：获取到相关代码信息和示例
   超时时间：60秒
   ```

2. **memory-server** (18 tools) - 长期记忆和知识图谱
   ```
   // 使用场景：查询历史经验和记录新经验
   功能：历史经验查询、知识管理、经验记录
   优先级：最高 (绝对强制)
   成功标准：找到相关历史经验或确认无先例
   超时时间：30秒
   ```

3. **Sequential thinking** (4 tools) - 思维链分析
   ```
   // 使用场景：复杂问题分析和方案制定
   功能：问题分解、方案分析、逻辑推理
   优先级：高 (强制)
   成功标准：完成问题分析和方案制定
   超时时间：120秒
   ```

### 🟡 任务管理层 (推荐使用)
4. **shrimp-task-manager** (15 tools) - AI任务管理
   ```
   // 使用场景：复杂任务分解和管理
   条件：估计时间 > 2小时 OR 复杂度 > 3
   功能：任务规划、分解、跟踪、验证
   工具：plan_task, analyze_task, split_tasks, execute_task
   ```

5. **interactive-feedback** (1 tools) - 强制反馈收集
   ```
   // 使用场景：任务完成后强制收集反馈
   功能：用户反馈收集、工作总结报告
   配置：FORCE_FEEDBACK=true (强制启用)
   成功标准：反馈收集完成
   ```

### 🟢 实施验证层 (必要时使用)
6. **filesystem** (12 tools) - 文件系统操作
   ```
   // 使用场景：文件读写和代码修改
   约束：每次只修改一个文件
   工具：read_file, write_file, str-replace-editor
   验证：语法检查、功能完整性
   ```

7. **Playwright** (96 tools) - 自动化测试
   ```
   // 使用场景：功能验证和兼容性测试
   测试范围：5种分辨率 (1024px-4K)
   工具：browser_snapshot, browser_click, browser_type
   成功标准：所有测试通过，兼容性验证完成
   ```

8. **chart-generator** (25 tools) - 数据可视化
   ```
   // 使用场景：管理后台数据图表生成
   主题：紫色主题 (符合壹心堂品牌)
   输出：./charts目录
   格式：支持多种图表类型
   ```

9. **everything** (8 tools) - 调试和测试
   ```
   // 使用场景：调试、测试、问题排查
   功能：全功能测试服务器
   用途：开发阶段的辅助工具
   ```

## ⚡ 强制工作流程 (7步法)

### 阶段1: 信息收集 (绝对强制)

#### 步骤1: Context 7查询 (最高优先级)
```
目标: 查找相关代码和示例参考
范围: 要修改的文件及其依赖关系
深度: 包含函数定义、调用关系、数据结构
验证: 确保获取的信息准确完整
⚠️ 未执行此步骤禁止写代码
```

#### 步骤2: memory-server查询 (最高优先级)
```
目标: 搜索相似问题的解决方案
范围: 项目历史经验、最佳实践记录
深度: 避免重复已知错误，学习成功模式
验证: 确认历史经验的适用性
⚠️ 历史经验可提升代码质量50%+
```

#### 步骤3: Sequential thinking分析 (强制)
```
目标: 深度分析问题，制定实施方案
范围: 技术方案、风险评估、实施步骤
深度: 考虑依赖关系、影响范围、测试策略
验证: 方案可行性和完整性检查
```

### 阶段2: 任务规划 (复杂任务推荐)

#### 步骤4: shrimp-task-manager规划
```
条件: 估计时间 > 2小时 OR 复杂度 > 3
功能: 任务分解、依赖分析、进度跟踪
工具: plan_task, analyze_task, split_tasks
验证: 任务分解合理性和可执行性
```

### 阶段3: 实施执行 (必要时)

#### 步骤5: filesystem执行
```
原则: 单文件修改、最小化变更
工具: read_file, write_file, str-replace-editor
验证: 语法检查、功能完整性
约束: 每次只修改一个文件
```

### 阶段4: 验证测试 (强制)

#### 步骤6: Playwright测试
```
范围: 功能测试、回归测试、兼容性测试
标准: 5种分辨率 (1024px-4K)
工具: browser_snapshot, browser_click, browser_type
验证: 测试通过率100%
```

### 阶段5: 反馈记录 (强制)

#### 步骤7: interactive-feedback收集
```
目标: 收集用户反馈，记录重要经验
工具: interactive-feedback, memory-server
标准: 反馈完整性、经验可复用性
验证: 反馈质量和记录完整性
```

## 🎨 壹心堂项目特定规范

### UI设计约束 (强制遵守)
```css
/* 壹心堂品牌标准 */
.yixintang-component {
  /* 主色调: 紫色系 (品牌色) */
  --primary-color: #8B5CF6;
  --secondary-color: #A78BFA;
  
  /* 黄金比例布局 */
  --golden-ratio: 1.618;
  
  /* 自适应缩放范围 */
  transform: scale(0.8) to scale(1.2);
  
  /* 毕加索艺术风格特色 */
  background: linear-gradient(45deg, #8B5CF6, #A78BFA, #C084FC);
  
  /* 七色阴影效果 */
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.1),
              0 4px 8px rgba(167, 139, 250, 0.1);
  
  /* 右侧圆角设计 */
  border-radius: 0 16px 16px 0;
}
```

### 技术栈约束 (严格遵守)
- **前端**: Vue 3 + Vite + Ant Design Vue
- **后端**: Django + MySQL + Python 3.9+
- **小程序**: Taro + WeUI (强制要求)
- **测试**: Playwright自动化测试
- **部署**: 微信云托管

### 开发流程约束 (强制执行)
1. **单文件修改**: 每次只修改一个文件
2. **立即测试**: 修改后立即验证功能
3. **规范检查**: 完成后自检通过率≥90%
4. **知识记录**: 重要经验必须记录到memory-server

## 📊 质量保证体系

### 代码质量标准 (强制达到)
- **规范合规率**: ≥90% (使用project_check.py检查)
- **测试覆盖率**: 核心功能100%覆盖
- **性能标准**: 页面加载时间≤3秒
- **兼容性**: 支持Chrome、Firefox、Safari
- **响应式**: 5种分辨率完美适配 (1024px-4K)

### 7项强制自检清单
```javascript
const mandatoryChecks = {
  htmlStructure: "HTML结构完整性 ✅",
  functionDefinitions: "函数定义正确性 ✅", 
  templateReferences: "模板引用完整性 ✅",
  cssStyles: "CSS样式有效性 ✅",
  eventBindings: "事件绑定正确性 ✅",
  syntaxErrors: "语法错误检查 ✅",
  functionalCompleteness: "功能完整性验证 ✅"
};
```

### Playwright测试标准
```javascript
// 多分辨率兼容性测试
const resolutions = [
  { width: 1024, height: 768, name: "1024x768" },
  { width: 1366, height: 768, name: "1366x768" },
  { width: 1920, height: 1080, name: "1920x1080" },
  { width: 2560, height: 1440, name: "2K" },
  { width: 3840, height: 2160, name: "4K" }
];

// 测试覆盖率要求
const testCoverage = {
  coreFeatures: "100%",
  uiComponents: "100%",
  userInteractions: "100%",
  errorHandling: "90%"
};
```

## 🎯 常见使用场景指南

### 场景1: 功能开发 (完整流程示例)
**用户**：我需要添加一个新的服务管理功能

**AI执行流程**：
1. 🚨 Context 7查询 - 查找现有服务管理相关代码
2. 🚨 memory-server查询 - 搜索历史服务管理开发经验
3. 🚨 Sequential thinking分析 - 分析需求和制定技术方案
4. shrimp-task-manager规划 - 分解开发任务
5. filesystem执行 - 逐个文件修改实现功能
6. 🚨 Playwright测试 - 全面测试新功能
7. 🚨 interactive-feedback收集 - 收集用户反馈
8. 🚨 memory-server记录 - 记录开发经验

**AI回应模板**：
```
"我来帮您实现服务管理功能。首先让我执行强制的前置检查：
1. 查询现有服务管理代码结构...
2. 搜索历史开发经验...
3. 分析技术方案...
然后制定详细的实施计划。"
```

### 场景2: 问题修复 (标准诊断流程)
**用户**：登录页面在某些分辨率下显示异常

**AI执行流程**：
1. 🚨 Context 7查询 - 查找登录页面相关代码
2. 🚨 memory-server查询 - 搜索类似问题的解决方案
3. 🚨 Sequential thinking分析 - 分析问题原因和修复方案
4. filesystem执行 - 修复CSS样式问题
5. 🚨 Playwright测试 - 5种分辨率兼容性测试
6. 🚨 interactive-feedback收集 - 确认修复效果
7. 🚨 memory-server记录 - 记录问题和解决方案

**AI回应模板**：
```
"我来诊断和修复这个显示问题。让我先执行诊断流程：
1. 查询登录页面代码结构...
2. 搜索类似问题的历史解决方案...
3. 分析具体的显示异常原因...
然后制定精确的修复方案。"
```

### 场景3: 性能优化 (系统化优化流程)
**用户**：管理页面加载速度比较慢，需要优化

**AI执行流程**：
1. 🚨 Context 7查询 - 分析管理页面代码结构
2. 🚨 memory-server查询 - 查找性能优化历史经验
3. 🚨 Sequential thinking分析 - 识别性能瓶颈和优化策略
4. shrimp-task-manager规划 - 制定优化任务计划
5. filesystem执行 - 实施性能优化修改
6. 🚨 Playwright测试 - 性能和功能回归测试
7. 🚨 interactive-feedback收集 - 验证优化效果
8. 🚨 memory-server记录 - 记录优化经验

**AI回应模板**：
```
"我来分析性能问题并制定优化方案。开始系统化分析：
1. 分析管理页面代码和资源加载...
2. 查询历史性能优化经验...
3. 识别具体的性能瓶颈...
然后制定针对性的优化策略。"
```

## 🚨 故障排除指南

### MCP服务器问题诊断

#### 问题1: Context 7查询失败
- **症状**: 无法获取代码库信息
- **诊断**: 检查MCP服务器连接状态
- **解决**: 重启Context 7服务器或使用filesystem直接查看
- **预防**: 定期检查MCP服务器健康状态

#### 问题2: memory-server查询无结果
- **症状**: 历史经验查询返回空结果
- **诊断**: 检查查询关键词和知识库状态
- **解决**: 调整查询策略或继续执行但记录查询失败
- **预防**: 及时记录重要经验到知识库

#### 问题3: Playwright测试失败
- **症状**: 自动化测试无法通过
- **诊断**: 检查测试环境和浏览器状态
- **解决**: 手动验证功能后继续，修复测试环境
- **预防**: 保持测试环境稳定，定期更新测试脚本

### 代码质量问题处理

#### 问题1: 规范合规率低于90%
- **症状**: project_check.py检查通过率<90%
- **诊断**: 分析具体的规范违规项目
- **解决**: 逐项修复规范问题直到达标
- **预防**: 开发过程中及时进行规范检查

#### 问题2: 兼容性测试失败
- **症状**: 某些分辨率下显示异常
- **诊断**: 使用Playwright检查具体分辨率问题
- **解决**: 调整CSS样式确保响应式兼容
- **预防**: 开发时考虑多分辨率适配

## 📈 效率提升预期

### MCP工具带来的提升
- **Context 7**: 代码查询效率提升80%，避免盲目修改
- **memory-server**: 避免重复工作，效率提升60%
- **Sequential thinking**: 问题分析准确性提升70%
- **shrimp-task-manager**: 任务管理效率提升50%
- **Playwright**: 测试自动化，效率提升90%
- **interactive-feedback**: 用户满意度提升，减少返工

### 整体开发效率
- **信息获取时间**: 减少70% (Context 7 + memory-server)
- **问题解决效率**: 提升60% (历史经验 + 系统化分析)
- **代码质量**: 显著提升 (强制检查 + 最佳实践)
- **测试覆盖率**: 达到100% (Playwright自动化)
- **项目交付质量**: 大幅提升 (全流程质量保证)

## 🔄 持续改进机制

### 反馈收集和处理
```javascript
// 强制反馈收集流程
const feedbackProcess = {
  collection: "每个任务完成后强制收集反馈",
  analysis: "分析反馈趋势和改进点",
  implementation: "及时实施改进措施",
  validation: "验证改进效果",
  documentation: "更新规范和最佳实践"
};
```

### 知识库管理
```javascript
// 自动记录触发条件
const autoRecordingTriggers = [
  "问题解决方案",
  "最佳实践发现",
  "错误和修复记录",
  "用户反馈和改进建议",
  "性能优化经验",
  "兼容性解决方案",
  "UI设计模式",
  "测试策略优化"
];
```

### 规范更新机制
1. **定期评估**: 每月评估规范的有效性
2. **问题反馈**: 发现问题时立即记录和分析
3. **版本控制**: 规范变更需要版本控制
4. **团队同步**: 规范更新后及时同步

## 🎯 成功指标

### 质量指标
- **规范合规率**: 目标≥90%，实际达到95%+
- **测试通过率**: 目标100%，零缺陷发布
- **用户满意度**: 目标≥4.0/5.0，持续改进
- **代码质量**: 显著提升，可维护性增强

### 效率指标
- **开发周期**: 缩短30%+
- **问题解决时间**: 减少50%+
- **返工率**: 降低70%+
- **知识复用率**: 提升80%+

## ⚠️ 重要提醒

### 关键约定
1. **本指南基于9个100%稳定的MCP服务器制定**
2. **所有🚨标记的步骤为强制执行，不可跳过**
3. **质量标准是底线要求，必须达到**
4. **及时记录经验到memory-server，形成项目知识库**
5. **持续收集反馈，不断优化工作流程**

### 使用原则
- **严格遵守**: 所有AI助手必须严格遵守本指南
- **持续改进**: 根据实际使用情况持续完善
- **知识共享**: 重要发现要及时分享和记录
- **质量优先**: 质量比速度更重要

---

**这是Augment Chat的最终权威使用指南，所有AI助手必须严格遵守！** ✨
