# 🤖 AI工作流程指南

> **📋 文档目的**: 为AI助手提供明确的工作流程和规范指引
> **🔄 更新日期**: 2025-01-20
> **🎯 目标用户**: AI助手 (Claude、GPT等)

## 🛠️ **当前工具栈 (必须使用)**

### **🎭 AI专用工具**
1. **Sequential thinking**: 复杂问题分析和思维链推理
2. **Context 7**: 代码库上下文引擎，代码检索和理解
3. **Playwright**: 自动化测试框架

### **🔧 MCP服务器 (新增 2025-01-20)**
1. **fetch-tool**: 网络请求和数据获取
2. **memory-server**: 长期记忆和知识图谱管理
3. **planning-server**: 任务规划和步骤分解
4. **emotion-server**: 情感分析和处理
5. **reasoning-enhance**: 推理能力增强

### **🚫 禁用工具**
- ❌ **Node.js相关**: npm、yarn、Node.js环境
- ❌ **手动测试**: 浏览器控制台测试
- ❌ **Puppeteer**: 已替换为Playwright

## 📋 **工作流程规范**

### **🔍 信息获取流程 (已更新 2025-01-20)**
```mermaid
graph TD
    A[接收任务] --> B{需要外部信息?}
    B -->|是| C[使用fetch-tool获取]
    B -->|否| D{需要代码信息?}
    C --> E[使用memory-server记录]
    D -->|是| F[使用Context 7查询]
    D -->|否| G[使用Sequential thinking分析]
    E --> H[使用planning-server规划]
    F --> I[分析代码结构]
    G --> H
    I --> H
    H --> J[制定解决方案]
    J --> K[执行修改]
    K --> L[完成后自检]
    L --> M[使用Playwright测试]
    M --> N[更新文档和记忆]
```

### **🚨 强制执行步骤**
1. **信息收集**: 使用Context 7查询相关代码
2. **问题分析**: 使用Sequential thinking分解复杂问题
3. **方案制定**: 基于当前规范制定解决方案
4. **代码修改**: 遵循开发约束进行修改
5. **完成自检**: 执行7项强制检查
6. **自动测试**: 使用Playwright验证功能
7. **文档更新**: 更新相关文档内容

## 📖 **文档优先级**

### **🚨 优先级1 - 必须遵守**
1. **CURRENT_DEVELOPMENT_GUIDE.md** - 当前开发指南
2. **DEVELOPMENT_CONSTRAINTS.md** - 开发约束规范
3. **当前文档** - AI工作流程指南

### **📖 优先级2 - 重要参考**
1. **PLAYWRIGHT_TESTING_GUIDE.md** - 测试规范
2. **CI_CD_STANDARDS.md** - 持续集成规范
3. **PROBLEM_EXPERIENCE_LIBRARY.md** - 问题经验库

### **📁 优先级3 - 历史参考**
- `docs/archive/` - 已归档文档，仅供历史参考

## 🎯 **任务处理指南**

### **代码修改任务**
```
1. 使用Context 7查询相关代码
   - 查询要修改的文件和函数
   - 了解代码结构和依赖关系
   - 确认修改影响范围

2. 使用Sequential thinking分析
   - 分解复杂的修改需求
   - 制定详细的修改计划
   - 考虑潜在的问题和风险

3. 执行修改
   - 遵循开发约束规范
   - 逐个文件进行修改
   - 立即测试每个修改

4. 完成后自检
   - HTML结构检查
   - 函数定义检查
   - 模板引用检查
   - CSS样式检查
   - 事件绑定检查
   - 语法错误检查
   - 功能完整性检查

5. 自动化测试
   - 使用Playwright验证功能
   - 确保界面正常显示
   - 验证交互功能正常

6. 文档更新
   - 更新相关规范文档
   - 记录问题和解决方案
   - 维护文档一致性
```

### **问题排查任务**
```
1. 问题分析
   - 使用Sequential thinking分析问题
   - 查询相关经验库
   - 确定问题类型和优先级

2. 信息收集
   - 使用Context 7查询相关代码
   - 收集错误信息和日志
   - 了解问题发生的上下文

3. 解决方案制定
   - 基于经验库制定方案
   - 考虑多种解决路径
   - 选择最优解决方案

4. 实施和验证
   - 按照开发规范实施修复
   - 使用Playwright验证修复效果
   - 记录解决过程到经验库
```

## 🚨 **强制检查项**

### **每次修改后必须执行**
- ✅ **HTML结构检查**: div标签匹配、嵌套正确
- ✅ **函数定义检查**: 所有新增函数已正确定义
- ✅ **模板引用检查**: 模板中的函数调用完整
- ✅ **CSS样式检查**: 所有样式类已定义且生效
- ✅ **事件绑定检查**: 所有事件处理器正确绑定
- ✅ **语法错误检查**: 括号匹配、语法正确
- ✅ **功能完整性检查**: 新功能完整可用

### **自检脚本使用**
```python
# 使用自检脚本模板
from scripts.self_check_template import comprehensive_self_check

config = {
    'required_functions': ['新增的函数名'],
    'template_calls': ['模板中调用的函数'],
    'required_styles': ['新增的CSS类'],
    'event_bindings': ['事件绑定'],
    'completeness_checks': {'功能名': '检查模式'}
}

result = comprehensive_self_check('文件路径', config)
```

## ❌ **禁止行为**

### **绝对禁止**
- ❌ **引用Node.js内容**: 不得提及npm、yarn、Node.js
- ❌ **使用旧测试方法**: 不得使用浏览器控制台测试
- ❌ **忽略自检流程**: 每次修改后必须自检
- ❌ **批量修改文件**: 必须逐个文件处理
- ❌ **跳过文档更新**: 必须及时更新相关文档

### **需要确认的行为**
- ⚠️ **大规模重构**: 需要用户明确确认
- ⚠️ **删除现有功能**: 需要用户明确授权
- ⚠️ **修改核心配置**: 需要详细说明影响

## 📊 **质量标准**

### **代码质量**
- **通过率**: 自检必须100%通过
- **测试覆盖**: 新功能必须有测试
- **文档同步**: 修改必须更新文档

### **响应质量**
- **信息准确**: 基于最新文档和代码
- **方案可行**: 经过充分分析验证
- **执行完整**: 包含完整的实施步骤

## 🔄 **持续改进**

### **经验积累**
- 记录每次问题解决过程
- 更新问题经验库
- 完善工作流程

### **规范更新**
- 根据实际情况调整规范
- 及时更新文档内容
- 保持工具栈一致性

---

> **⚠️ 重要提醒**: 
> 1. 优先参考CURRENT_DEVELOPMENT_GUIDE.md
> 2. 每次修改后必须执行完成后自检
> 3. 使用Playwright进行自动化测试
> 4. 及时更新相关文档内容
