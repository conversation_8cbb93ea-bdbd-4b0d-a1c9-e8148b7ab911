# 🎯 全面开发规范标准 (2025-01-20)

> **📋 文档目的**: 基于历史经验和MCP能力，全面补充开发规范细节，提高AI工作准确性
> **🔄 更新频率**: 每次发现新问题或优化点时立即更新
> **👥 目标用户**: AI助手、开发团队

## 🚨 **强制执行规范 (基于历史经验)**

### **⚡ 写代码前强制约束 (最高优先级)**

#### **🔴 绝对强制 - 写代码前必须执行**
```mermaid
graph TD
    A[接收编码任务] --> B[🚨 强制: Context 7查询相关代码]
    B --> C[🚨 强制: 查找示例参考文件]
    C --> D[🚨 强制: memory-server查询历史经验]
    D --> E[🚨 强制: Sequential thinking分析方案]
    E --> F[开始编写代码]
    F --> G[完成后自检]
    G --> H[Playwright测试]
    H --> I[memory-server记录]
```

**⚠️ 违反此约束的后果**：
- 代码质量下降50%+
- 重复已知错误
- 不符合项目规范
- 需要大量返工

### **📋 MCP工具使用流程控制**

#### **🟡 规范层 - 建议按流程使用**
- **任务管理**: shrimp-task-manager分解复杂任务
- **用户反馈**: interactive-feedback收集和分析
- **数据可视化**: chart-generator生成图表

#### **🟢 自由层 - 灵活使用**
- **深度推理**: deep-reasoning处理复杂逻辑
- **浏览器自动化**: puppeteer-automation辅助测试
- **调试工具**: everything全功能测试

### **🔧 详细操作规范**

#### **1. 信息收集阶段 (绝对强制 - 写代码前必须完成)**
```javascript
// 🚨 强制执行顺序 - 不可跳过任何步骤
1. 【强制第一步】使用Context 7查询相关代码
   - 查询范围: 要修改的文件及其依赖
   - 查询深度: 包含函数定义、调用关系、数据结构
   - 查找示例: 寻找相似功能的实现参考
   - 验证信息: 确保获取的信息准确完整
   - ⚠️ 未执行此步骤禁止写代码

2. 【强制第二步】查找示例参考文件
   - 搜索项目中相似功能的实现
   - 分析成功案例的代码模式
   - 提取可复用的代码结构
   - 确保代码风格一致性
   - ⚠️ 示例参考可提升代码质量50%+

3. 【强制第三步】使用memory-server查询历史经验
   - 搜索相似问题的解决方案
   - 查看相关的最佳实践记录
   - 避免重复已知的错误
   - 学习成功模式和失败教训

4. 【强制第四步】使用Sequential thinking分析问题
   - 分解复杂问题为子问题
   - 识别潜在风险和依赖关系
   - 制定详细的实施计划
   - 确保方案的完整性和可行性
```

#### **2. 任务规划阶段 (规范)**
```javascript
// 使用shrimp-task-manager进行任务分解
const taskPlan = {
  mainTask: "具体功能实现",
  subTasks: [
    {
      name: "子任务1",
      dependencies: [],
      estimatedTime: "2小时",
      verificationCriteria: "具体验收标准"
    }
  ],
  riskAssessment: "潜在风险分析",
  rollbackPlan: "回滚方案"
};
```

#### **3. 代码修改阶段 (强制)**
```javascript
// 标准修改流程
1. 使用filesystem读取目标文件
2. 分析现有代码结构和逻辑
3. 制定最小化修改方案
4. 逐步实施修改
5. 每次修改后立即验证语法
6. 确保不破坏现有功能
```

#### **4. 测试验证阶段 (强制)**
```javascript
// 多层测试策略
1. 语法检查: 确保代码语法正确
2. 功能测试: 使用Playwright自动化测试
3. 回归测试: 确保不影响现有功能
4. 界面测试: 验证UI显示和交互
5. 兼容性测试: 多分辨率和浏览器测试
```

#### **5. 质量保证阶段 (强制)**
```javascript
// 完成后自检 (7项强制检查)
const selfCheckList = {
  htmlStructure: "HTML结构完整性",
  functionDefinitions: "函数定义正确性", 
  templateReferences: "模板引用完整性",
  cssStyles: "CSS样式有效性",
  eventBindings: "事件绑定正确性",
  syntaxErrors: "语法错误检查",
  functionalCompleteness: "功能完整性验证"
};
```

## 📊 **基于历史经验的问题预防**

### **🚨 常见问题及预防措施**

#### **1. HTML结构问题**
```html
<!-- ❌ 常见错误 -->
<div class="container">
  <div class="content">
    <!-- 忘记闭合标签 -->

<!-- ✅ 正确做法 -->
<div class="container">
  <div class="content">
    <!-- 内容 -->
  </div>
</div>
```

**预防措施**:
- 每次修改HTML后立即检查标签匹配
- 使用自检脚本验证结构完整性
- 保持缩进一致性便于检查

#### **2. 函数定义和调用不匹配**
```javascript
// ❌ 常见错误
// 模板中调用: @click="handleSort('name')"
// 但函数未定义或名称不匹配

// ✅ 正确做法
// 1. 先定义函数
const handleSort = (field) => {
  // 实现逻辑
};

// 2. 再在模板中调用
// @click="handleSort('name')"
```

**预防措施**:
- 使用Context 7查询现有函数定义
- 确保函数名称完全匹配
- 验证参数类型和数量

#### **3. CSS样式冲突**
```css
/* ❌ 常见错误 - 样式优先级问题 */
.button { color: blue; }
.header .button { color: red; } /* 优先级更高 */

/* ✅ 正确做法 - 明确优先级 */
.header-button { 
  color: red !important; 
  /* 或使用更具体的选择器 */
}
```

**预防措施**:
- 使用具体的CSS类名避免冲突
- 检查现有样式的优先级
- 使用浏览器开发工具验证样式生效

#### **4. 事件绑定错误**
```vue
<!-- ❌ 常见错误 -->
<button @click="undefinedFunction">按钮</button>

<!-- ✅ 正确做法 -->
<button @click="handleClick" :disabled="loading">
  {{ loading ? '处理中...' : '按钮' }}
</button>
```

**预防措施**:
- 确保事件处理函数已定义
- 添加loading状态防止重复点击
- 提供用户反馈

### **🎯 界面开发特殊规范**

#### **1. 壹心堂项目特定约束**
```css
/* 强制遵守的设计规范 - 基于壹心堂项目特点 */
.component {
  /* 主色调: 紫色系 (壹心堂品牌色) */
  --primary-color: #8B5CF6;
  --secondary-color: #A78BFA;

  /* 黄金比例布局 (壹心堂设计标准) */
  --golden-ratio: 1.618;

  /* 自适应缩放范围 (壹心堂多分辨率支持) */
  transform: scale(0.8) to scale(1.2);

  /* 零重叠原则 (壹心堂界面规范) */
  z-index: auto; /* 避免不必要的层级 */

  /* 零错位原则 (壹心堂精确对齐要求) */
  position: relative; /* 精确定位 */

  /* 壹心堂特色: 彩虹渐变效果 */
  background: linear-gradient(45deg, #8B5CF6, #A78BFA, #C084FC);

  /* 壹心堂特色: 七色阴影效果 */
  box-shadow: 0 2px 4px rgba(139, 92, 246, 0.1),
              0 4px 8px rgba(167, 139, 250, 0.1);
}

/* 壹心堂项目特定组件样式 */
.yixintang-header {
  /* 表头集成操作栏 - 壹心堂成功模式 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 60px; /* 壹心堂标准行高 */
}

.yixintang-table {
  /* 壹心堂表格标准 */
  --row-height: 60px;
  --default-rows: 10;
  --aligned-rows: 8; /* 与菜单对齐 */
}
```

#### **2. 响应式设计标准**
```css
/* 支持的分辨率 */
@media (min-width: 1024px) { /* 1024x768 */ }
@media (min-width: 1366px) { /* 1366x768 */ }
@media (min-width: 1920px) { /* 1920x1080 */ }
@media (min-width: 2560px) { /* 2560x1440 */ }
@media (min-width: 3840px) { /* 3840x2160 */ }
```

#### **3. 数据展示规范**
```javascript
// 表格数据显示标准
const tableConfig = {
  defaultRows: 10, // 默认显示10行
  alignedRows: 8,  // 前8行与菜单对齐
  rowHeight: 60,   // 行高60px
  pagination: {
    itemsPerPage: 20,
    position: 'bottom'
  }
};
```

## 🔄 **MCP工具协同使用规范**

### **1. 工具组合使用模式**

#### **复杂功能开发模式**
```javascript
// 1. 信息收集
Context7.query("相关代码结构");
memory.search("相似功能实现");

// 2. 任务规划
shrimpTaskManager.createPlan({
  feature: "新功能",
  complexity: "high",
  dependencies: ["前端", "后端", "数据库"]
});

// 3. 实施开发
filesystem.readFile("target.vue");
sequentialThinking.analyze("实现方案");
filesystem.writeFile("target.vue", modifiedContent);

// 4. 测试验证
playwright.test("功能测试");
interactiveFeedback.collect("用户反馈");

// 5. 记录总结
memory.store("解决方案", "实施过程", "经验教训");
```

#### **问题排查模式**
```javascript
// 1. 问题分析
Context7.query("错误相关代码");
memory.search("类似问题解决方案");
deepReasoning.analyze("问题根因");

// 2. 解决方案制定
sequentialThinking.breakdown("解决步骤");
shrimpTaskManager.createTask("问题修复");

// 3. 实施修复
filesystem.backup("原始文件");
filesystem.modify("目标文件");
playwright.test("修复验证");

// 4. 经验记录
memory.store("问题类型", "解决方案", "预防措施");
```

### **2. 工具使用优先级**

#### **信息获取优先级**
1. **Context 7** (最高) - 准确的代码信息
2. **memory-server** (高) - 历史经验和解决方案
3. **web-fetch** (中) - 外部技术资源
4. **Sequential thinking** (中) - 逻辑分析

#### **开发工具优先级**
1. **filesystem** (最高) - 文件操作
2. **Playwright** (高) - 自动化测试
3. **shrimp-task-manager** (中) - 任务管理
4. **chart-generator** (低) - 数据可视化

## 📝 **记录和文档规范**

### **1. memory-server使用规范**
```javascript
// 标准记录格式
const memoryRecord = {
  entityName: "具体问题或功能名称",
  entityType: "问题类型/功能类型",
  observations: [
    "问题描述或功能需求",
    "解决方案或实现方法", 
    "关键技术点",
    "注意事项",
    "相关文件和代码位置",
    "测试方法和验证标准"
  ]
};
```

### **2. 文档更新规范**
```markdown
## 更新记录格式
- **日期**: 2025-01-20
- **类型**: 新增功能/问题修复/优化改进
- **描述**: 具体的更改内容
- **影响**: 对现有功能的影响
- **测试**: 验证方法和结果
- **相关**: 关联的文件和功能
```

## 🎯 **AI工作准确性提升措施**

### **1. 信息验证机制**
- 使用Context 7获取的信息必须二次验证
- 重要修改前必须查询memory-server历史经验
- 复杂逻辑必须使用Sequential thinking分析

### **2. 错误预防机制**
- 每次修改后立即执行完成后自检
- 使用Playwright进行自动化回归测试
- 重要功能修改必须有回滚方案

### **3. 质量保证机制**
- 所有修改必须记录到memory-server
- 定期使用interactive-feedback收集使用反馈
- 持续优化开发流程和工具使用方式

## 🔧 **MCP工具详细分类和使用指南**

### **🔴 核心开发工具 (强制使用)**

#### **Context 7 - 代码库上下文引擎**
```javascript
// 使用规范
const contextQuery = {
  purpose: "查询相关代码结构和依赖关系",
  scope: "要修改的文件及其关联文件",
  depth: "包含函数定义、调用关系、数据结构",
  verification: "确保信息准确完整"
};

// 标准查询流程
1. 查询目标文件的完整结构
2. 查询相关依赖和调用关系
3. 查询相似功能的实现方式
4. 验证查询结果的准确性
```

#### **Sequential thinking - 思维链分析**
```javascript
// 使用场景
const thinkingScenarios = [
  "复杂问题分解和分析",
  "多步骤解决方案制定",
  "风险评估和预防措施",
  "架构设计和技术选型"
];

// 标准分析流程
1. 问题定义和范围确定
2. 分解为可管理的子问题
3. 分析每个子问题的解决方案
4. 评估方案的可行性和风险
5. 制定详细的实施计划
```

#### **memory-server - 长期记忆管理**
```javascript
// 记录标准
const memoryStandards = {
  entityNaming: "使用具体、描述性的名称",
  entityType: "明确分类：问题、功能、经验、规范",
  observations: [
    "问题描述或功能需求",
    "解决方案或实现方法",
    "关键技术点和注意事项",
    "相关文件和代码位置",
    "测试方法和验证标准",
    "经验教训和改进建议"
  ]
};
```

#### **filesystem - 文件系统操作**
```javascript
// 安全操作规范
const filesystemSafety = {
  backup: "重要修改前必须备份",
  validation: "修改后立即验证语法",
  incremental: "采用增量修改策略",
  rollback: "准备回滚方案"
};
```

### **🟡 项目管理工具 (规范使用)**

#### **shrimp-task-manager - AI任务管理**
```javascript
// 任务分解标准
const taskDecomposition = {
  granularity: "每个子任务1-2个工作日完成",
  dependencies: "明确任务间的依赖关系",
  verification: "每个任务有明确的验收标准",
  estimation: "提供时间估算和风险评估"
};
```

#### **interactive-feedback - 交互式反馈**
```javascript
// 反馈收集规范
const feedbackCollection = {
  timing: "功能完成后立即收集",
  scope: "包含功能性和用户体验反馈",
  analysis: "使用AI分析反馈内容",
  action: "制定改进措施和优先级"
};
```

### **🟢 测试和自动化工具 (重要使用)**

#### **Playwright - 自动化测试**
```javascript
// 测试策略
const testingStrategy = {
  coverage: "核心功能100%覆盖",
  scenarios: "正常流程 + 异常处理",
  browsers: "Chrome, Firefox, Safari",
  resolutions: "1024px - 4K多分辨率"
};
```

#### **puppeteer-automation - 浏览器自动化**
```javascript
// 与Playwright的协同使用
const puppeteerUsage = {
  complement: "补充Playwright未覆盖的场景",
  scraping: "网页内容抓取和数据收集",
  debugging: "复杂交互的调试和验证"
};
```

### **🔵 数据处理工具 (按需使用)**

#### **chart-generator - 图表生成**
```javascript
// 图表生成规范
const chartStandards = {
  theme: "符合项目紫色主题风格",
  responsive: "支持多分辨率自适应",
  accessibility: "符合无障碍访问标准",
  performance: "优化加载和渲染性能"
};
```

### **🟣 辅助增强工具 (灵活使用)**

#### **deep-reasoning - 深度推理**
```javascript
// 使用场景
const reasoningScenarios = [
  "复杂算法设计和优化",
  "架构决策的深度分析",
  "性能问题的根因分析",
  "安全风险的评估和防护"
];
```

#### **everything - 全功能测试服务器**
```javascript
// 调试和验证用途
const debuggingUsage = {
  protocol: "验证MCP协议实现",
  integration: "测试工具间的协同",
  troubleshooting: "问题排查和诊断"
};
```

## 📊 **基于历史经验的最佳实践**

### **1. 常见错误模式及预防**

#### **错误模式1: 函数未定义**
```javascript
// 历史问题
Template: @click="getSortClass('name')"
Script: // 函数未定义

// 预防措施
1. 使用Context 7查询现有函数
2. 确保函数名完全匹配
3. 验证参数类型和数量
4. 执行完成后自检验证
```

#### **错误模式2: HTML结构不匹配**
```html
<!-- 历史问题 -->
<div class="container">
  <div class="content">
    <!-- 缺少闭合标签 -->

<!-- 预防措施 -->
1. 每次修改后检查标签匹配
2. 保持一致的缩进格式
3. 使用自检脚本验证结构
4. 重要修改前备份原文件
```

#### **错误模式3: CSS样式冲突**
```css
/* 历史问题 */
.button { color: blue; }
.header .button { color: red; } /* 意外覆盖 */

/* 预防措施 */
1. 使用具体的CSS类名
2. 检查样式优先级
3. 使用开发工具验证效果
4. 记录样式修改的影响范围
```

### **2. 成功模式及复用**

#### **成功模式1: 表头集成操作栏 (壹心堂项目实际案例)**
```vue
<!-- 成功的表头集成实现 -->
<template>
  <div class="service-info-header yixintang-header">
    <!-- 左侧: 点击搜索功能 -->
    <div class="header-text"
         @click="focusSearchInput"
         :title="'点击搜索服务'">
      服务信息
    </div>

    <!-- 右侧: 独立排序按钮 -->
    <div class="sort-btn"
         @click="handleSort('name')"
         :title="getSortTitle('name')">
      <span class="sort-indicator"
            :class="getSortClass('name')">
        {{ getSortIcon('name') }}
      </span>
    </div>
  </div>
</template>

<script setup>
// 成功的函数实现
const focusSearchInput = () => {
  const searchInput = document.querySelector('#searchInput');
  if (searchInput) {
    searchInput.focus();
    if (searchInput.value) {
      searchInput.select(); // 全选现有内容
    }
  }
};

const getSortTitle = (field) => {
  const currentSort = sortConfig.value[field];
  if (!currentSort || currentSort === 'none') {
    return `点击按${getFieldName(field)}升序排序`;
  } else if (currentSort === 'asc') {
    return `当前${getFieldName(field)}升序，点击降序`;
  } else {
    return `当前${getFieldName(field)}降序，点击取消排序`;
  }
};
</script>

<style scoped>
/* 成功的样式实现 */
.service-info-header {
  justify-content: space-between !important;
  padding: 0 8px !important;
}

.header-text {
  cursor: pointer;
  transition: all 0.3s ease;
  padding: 4px 8px;
  border-radius: 4px;
}

.sort-btn {
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}
</style>
```

**复用指导和代码示例**:
```javascript
// 1. 分析现有布局和功能需求
const analysisTemplate = {
  currentLayout: "分析现有表头结构",
  functionalRequirements: "识别需要集成的功能",
  spaceConstraints: "评估可用的界面空间",
  userExperience: "考虑用户操作习惯"
};

// 2. 设计集成方案和交互逻辑
const integrationDesign = {
  leftSide: "主要功能 (如搜索)",
  rightSide: "辅助功能 (如排序)",
  separation: "功能独立，避免冲突",
  feedback: "提供清晰的用户反馈"
};

// 3. 实施时保持功能独立性
const implementationPrinciples = {
  separateHandlers: "每个功能独立的事件处理器",
  clearNaming: "使用清晰的函数和变量命名",
  modularCSS: "模块化的CSS样式设计",
  errorHandling: "完善的错误处理机制"
};

// 4. 全面测试各种交互场景
const testingScenarios = [
  "单独点击每个功能区域",
  "快速连续点击测试",
  "不同屏幕分辨率测试",
  "键盘导航测试",
  "触摸设备兼容性测试"
];
```

#### **成功模式2: 完成后自检流程**
```javascript
// 成功经验
const selfCheckProcess = {
  coverage: "7项强制检查项目",
  automation: "使用脚本自动化检查",
  effectiveness: "显著减少错误率",
  integration: "集成到开发流程中"
};

// 复用指导
1. 每次功能完成后必须执行
2. 使用自动化脚本提高效率
3. 记录检查结果和问题
4. 持续优化检查项目和标准
```

---

> **⚠️ 重要提醒**:
> 1. 本规范基于实际开发经验制定，必须严格遵守
> 2. 所有AI助手必须按照此规范执行工作流程
> 3. 发现新问题或优化点时立即更新此文档
> 4. MCP工具的使用必须遵循分层控制策略
> 5. 重要修改前必须查询历史经验和最佳实践
