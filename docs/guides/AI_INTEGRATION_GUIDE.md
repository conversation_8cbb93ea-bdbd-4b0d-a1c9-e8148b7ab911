# AI集成完整指南

> 📋 **文档目的**: 整合所有AI相关功能的实现指南和最佳实践
> 📅 **最后更新**: 2025-07-17

## 📋 目录

1. [AI服务架构](#ai服务架构)
2. [图片生成服务](#图片生成服务)
3. [文本生成服务](#文本生成服务)
4. [API配置管理](#api配置管理)
5. [错误处理机制](#错误处理机制)
6. [性能优化](#性能优化)

---

## 🏗️ AI服务架构

### **当前架构**
```
前端 → Django后端 → AI服务提供商
Vue.js   Python SDK   (火山引擎/DeepSeek)
```

### **架构优势**
- **安全性**: API密钥在后端管理，前端无法访问
- **统一性**: 所有AI调用通过后端统一处理
- **可控性**: 后端可以实现缓存、限流、监控等功能
- **兼容性**: 解决CORS跨域问题

### **核心组件**
```python
# server/api/ai_service.py
class AIService:
    def __init__(self):
        self.volcengine_client = VolcengineClient()
        self.deepseek_client = DeepSeekClient()
    
    def generate_image(self, prompt):
        """图片生成服务"""
        pass
    
    def generate_text(self, prompt):
        """文本生成服务"""
        pass
```

---

## 🖼️ 图片生成服务

### **火山引擎豆包AI配置**
- **模型**: `doubao-seedream-3-0-t2i-250415`
- **尺寸**: `1024x512` (横向比例)
- **API密钥**: `ae14c0e4-a270-45bc-a5ff-3a9437bb7315`

### **后端实现**
```python
# server/api/volcengine_service.py
class VolcengineImageService:
    def __init__(self):
        self.api_key = settings.VOLCENGINE_API_KEY
        self.model = "doubao-seedream-3-0-t2i-250415"
    
    def generate_image(self, prompt):
        request_data = {
            "model": self.model,
            "prompt": prompt,
            "size": "1024x512",
            "response_format": "url",
            "seed": 12,
            "guidance_scale": 2.5,
            "watermark": True
        }
        
        response = self.client.post(
            "/api/v1/images/generations",
            json=request_data
        )
        
        return response.json()
```

### **前端调用**
```javascript
// admin/src/services/aiService.js
export const generateServiceImage = async (serviceName) => {
  try {
    const response = await axios.post('/api/volcengine/generate-image/', {
      service_name: serviceName
    });
    
    return response.data.image_url;
  } catch (error) {
    console.error('图片生成失败:', error);
    throw error;
  }
};
```

### **提示词优化**
```python
def create_image_prompt(service_name):
    """创建专业的图片生成提示词"""
    base_prompt = f"""
    专业中医理疗场景：{service_name}
    
    场景要求：
    - 温馨舒适的中医理疗环境
    - 专业的理疗师正在进行{service_name}
    - 客户表情放松，享受服务
    - 现代化的理疗设备和传统中医元素结合
    - 柔和的灯光，营造舒适氛围
    
    画质要求：
    - 高清晰度，专业摄影质量
    - 色彩温暖，光线柔和
    - 构图平衡，视觉美观
    - 无文字水印，纯图片内容
    """
    
    return base_prompt.strip()
```

---

## 📝 文本生成服务

### **DeepSeek AI配置**
- **API密钥**: `***********************************`
- **模型**: `deepseek-chat`
- **用途**: 生成专业的服务描述

### **后端实现**
```python
# server/api/deepseek_service.py
class DeepSeekTextService:
    def __init__(self):
        self.api_key = settings.DEEPSEEK_API_KEY
        self.base_url = "https://api.deepseek.com"
    
    def generate_description(self, service_name):
        prompt = f"""
        请为中医理疗服务"{service_name}"生成一段专业的服务描述。
        
        要求：
        1. 描述服务的起源和历史背景
        2. 说明服务的主要功效和益处
        3. 适合的人群和注意事项
        4. 语言专业但易懂，约100-150字
        5. 突出中医理疗的专业性和效果
        """
        
        response = self.client.chat.completions.create(
            model="deepseek-chat",
            messages=[{"role": "user", "content": prompt}],
            max_tokens=200,
            temperature=0.7
        )
        
        return response.choices[0].message.content
```

### **前端调用**
```javascript
// 生成服务描述
export const generateServiceDescription = async (serviceName) => {
  try {
    const response = await axios.post('/api/deepseek/generate-description/', {
      service_name: serviceName
    });
    
    return response.data.description;
  } catch (error) {
    console.error('描述生成失败:', error);
    throw error;
  }
};
```

---

## ⚙️ API配置管理

### **环境变量配置**
```python
# server/settings.py
import os

# AI服务配置
VOLCENGINE_API_KEY = os.getenv('VOLCENGINE_API_KEY', 'ae14c0e4-a270-45bc-a5ff-3a9437bb7315')
DEEPSEEK_API_KEY = os.getenv('DEEPSEEK_API_KEY', '***********************************')

# AI服务URL
VOLCENGINE_BASE_URL = "https://ark.cn-beijing.volces.com"
DEEPSEEK_BASE_URL = "https://api.deepseek.com"
```

### **配置验证接口**
```python
# server/api/views.py
@api_view(['GET'])
def check_ai_config(request):
    """检查AI服务配置状态"""
    config_status = {
        'volcengine': {
            'configured': bool(settings.VOLCENGINE_API_KEY),
            'status': 'active' if test_volcengine_connection() else 'error'
        },
        'deepseek': {
            'configured': bool(settings.DEEPSEEK_API_KEY),
            'status': 'active' if test_deepseek_connection() else 'error'
        }
    }
    
    return Response(config_status)
```

---

## 🛡️ 错误处理机制

### **后端错误处理**
```python
def generate_image_with_fallback(service_name):
    """带备用方案的图片生成"""
    try:
        # 尝试AI生成
        return volcengine_service.generate_image(service_name)
    except VolcengineAPIError as e:
        logger.error(f"火山引擎API错误: {e}")
        # 返回默认图片URL
        return get_default_service_image(service_name)
    except Exception as e:
        logger.error(f"图片生成未知错误: {e}")
        return None
```

### **前端错误处理**
```javascript
const handleAIGeneration = async () => {
  try {
    loadingStates.aiGenerating = true;
    
    const [description, imageUrl] = await Promise.all([
      generateServiceDescription(formState.name),
      generateServiceImage(formState.name)
    ]);
    
    formState.description = description;
    formState.image_url = imageUrl;
    
    showToast('AI生成完成', 'success');
  } catch (error) {
    console.error('AI生成失败:', error);
    showToast('AI生成失败，请手动输入', 'error');
  } finally {
    loadingStates.aiGenerating = false;
  }
};
```

---

## ⚡ 性能优化

### **缓存策略**
```python
# 使用Redis缓存AI生成结果
from django.core.cache import cache

def generate_with_cache(service_name, generation_type):
    cache_key = f"ai_{generation_type}_{service_name}"
    cached_result = cache.get(cache_key)
    
    if cached_result:
        return cached_result
    
    # 生成新内容
    if generation_type == 'image':
        result = volcengine_service.generate_image(service_name)
    else:
        result = deepseek_service.generate_description(service_name)
    
    # 缓存24小时
    cache.set(cache_key, result, 86400)
    return result
```

### **并发控制**
```python
# 限制并发AI请求
import asyncio
from asyncio import Semaphore

class AIServiceManager:
    def __init__(self):
        self.semaphore = Semaphore(3)  # 最多3个并发请求
    
    async def generate_with_limit(self, service_name):
        async with self.semaphore:
            return await self.generate_content(service_name)
```

### **超时设置**
```python
# 设置合理的超时时间
import requests

def call_ai_api(url, data, timeout=30):
    try:
        response = requests.post(url, json=data, timeout=timeout)
        return response.json()
    except requests.Timeout:
        raise AIServiceTimeout("AI服务响应超时")
    except requests.RequestException as e:
        raise AIServiceError(f"AI服务请求失败: {e}")
```

---

## 📊 监控和日志

### **API调用监控**
```python
# 记录AI API调用统计
class AIUsageTracker:
    def track_usage(self, service_type, service_name, success=True):
        usage_log = {
            'timestamp': timezone.now(),
            'service_type': service_type,
            'service_name': service_name,
            'success': success,
            'user_id': self.request.user.id if self.request.user.is_authenticated else None
        }
        
        # 保存到数据库或日志文件
        logger.info(f"AI使用记录: {usage_log}")
```

### **性能监控**
```python
import time

def monitor_ai_performance(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            logger.info(f"AI调用成功: {func.__name__}, 耗时: {duration:.2f}s")
            return result
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"AI调用失败: {func.__name__}, 耗时: {duration:.2f}s, 错误: {e}")
            raise
    return wrapper
```

---

**📋 文档版本**: v1.0  
**👥 维护团队**: 壹心堂开发团队  
**🔄 更新频率**: 根据AI服务变更及时更新
