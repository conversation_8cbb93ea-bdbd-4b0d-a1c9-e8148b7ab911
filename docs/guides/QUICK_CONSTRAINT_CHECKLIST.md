# ⚡ 快速约束检查清单
> 每次任务必须执行，不可跳过

## 🎯 **任务开始前 (必须100%完成)**

### **理解确认检查**
```
□ 我已向用户复述了理解的需求
□ 用户已确认我的理解正确
□ 对于模糊的地方我已主动询问
□ 我使用了用户的准确用词
□ 重要修改我已列出清单让用户确认
```

### **影响评估检查**
```
□ 修改文件数量 ≤ 3个
□ 不会跨版本大幅修改
□ 对现有功能影响可控
□ 有明确的回滚方案
□ 技术方案适合项目特点
```

## 🚀 **执行过程中 (每个步骤必须检查)**

### **小步快跑检查**
```
□ 当前只修改1个文件
□ 修改完立即测试
□ 测试通过才继续下一个
□ 遇到问题立即停止
□ 详细记录每个步骤
```

### **基础UX主动检查**
```
□ Enter键导航已实现
□ Tab键顺序已优化
□ 自动聚焦已添加
□ 表单验证已完善
□ 错误提示已优化
□ 加载状态已显示
□ 空链接已处理
```

## ✅ **交付前检查 (必须100%通过)**

### **完美规范检查**
```
□ 运行了完美规范检查器
□ 检查通过率 ≥ 90%
□ 所有关键项都通过
□ 警告项已评估处理
```

### **手动验证检查**
```
□ 键盘交互正常工作
□ 表单验证正确响应
□ 用户反馈及时显示
□ 视觉效果符合要求
□ 核心功能稳定运行
```

## 🔄 **任务完成后 (持续改进必须)**

### **经验总结检查**
```
□ 分析了成功的地方
□ 识别了需要改进的地方
□ 提取了具体的经验教训
□ 更新了约束规则
□ 记录了知识点
```

## 🚨 **紧急情况处理**

### **发现理解错误时**
```
1. 立即停止当前修改
2. 向用户确认正确理解
3. 评估已修改内容的影响
4. 决定是回滚还是调整方向
5. 重新开始执行流程
```

### **发现技术问题时**
```
1. 立即停止继续修改
2. 分析问题的根本原因
3. 寻找合适的解决方案
4. 测试解决方案的有效性
5. 继续执行或寻求帮助
```

### **质量检查失败时**
```
1. 不允许交付给用户
2. 分析失败的具体原因
3. 制定详细的修复计划
4. 逐项修复并重新检查
5. 直到所有检查通过
```

## 💡 **关键提醒**

### **永远记住**
- 🎯 理解准确比快速开始更重要
- 🔧 小步快跑比一次完成更安全  
- ✨ 完美交付比功能实现更有价值
- 📚 持续改进比避免犯错更重要

### **绝对禁止**
- ❌ 未确认理解就开始修改
- ❌ 一次性修改多个文件
- ❌ 修改后不立即测试
- ❌ 跳过基础UX规范
- ❌ 质量检查不通过就交付
- ❌ 任务完成后不总结经验

### **遇到困难时**
- 🤔 承认自己可能理解错误
- 🙋 主动向用户寻求帮助
- 🔄 不要盲目尝试，要系统分析
- 📖 参考约束规则和经验教训
- 🎯 保持专业和负责的态度

---

**使用方法**: 每次任务开始前打开此清单，逐项检查，确保100%完成后再开始下一步！
