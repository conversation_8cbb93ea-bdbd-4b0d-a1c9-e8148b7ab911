# 📖 User Guidelines - Augment Chat 使用指南

> **📋 文档目的**: 为用户提供与Augment Chat协作的最佳实践指南
> **🔄 更新日期**: 2025-01-20
> **🎯 适用项目**: 壹心堂管理系统
> **👥 目标用户**: 开发者、项目管理者

## 🤖 **AI助手能力概览 (基于9个稳定MCP服务器)**

### **✅ AI助手可以做什么**

#### **代码开发能力**
- ✅ **Vue 3组件开发** - 创建和修改Vue组件、样式、逻辑
- ✅ **Django后端开发** - API接口、数据模型、业务逻辑
- ✅ **数据库操作** - MySQL查询、模型设计、数据迁移
- ✅ **Taro小程序开发** - 微信小程序页面和功能
- ✅ **样式设计** - CSS/SCSS、响应式布局、UI组件

#### **项目管理能力**
- ✅ **代码审查** - 检查代码质量、规范合规性
- ✅ **自动化测试** - Playwright测试脚本编写和执行
- ✅ **文档维护** - 更新技术文档、API文档、使用指南
- ✅ **问题诊断** - 分析错误日志、性能问题、兼容性问题
- ✅ **架构优化** - 代码重构、性能优化、最佳实践应用

#### **AI增强功能 (9个稳定MCP服务器)**
- ✅ **智能分析** - 使用9个100%稳定的MCP服务器进行深度分析
- ✅ **上下文理解** - Context 7引擎提供精确的代码理解 (8 tools)
- ✅ **思维链推理** - Sequential thinking处理复杂逻辑 (4 tools)
- ✅ **长期记忆** - memory-server记录和查询历史经验 (18 tools)
- ✅ **任务管理** - shrimp-task-manager智能任务分解 (15 tools)
- ✅ **自动化测试** - Playwright全面测试覆盖 (96 tools)
- ✅ **数据可视化** - chart-generator专业图表生成 (25 tools)

### **❌ AI助手不能做什么**

#### **严格禁止的操作**
- ❌ **批量脚本修改** - 不允许一次性修改多个文件
- ❌ **跨版本大改** - 不允许大规模架构重构
- ❌ **生产环境操作** - 不允许直接操作生产数据
- ❌ **安全敏感操作** - 不允许修改密钥、权限配置

#### **需要用户确认的操作**
- ⚠️ **删除现有功能** - 需要明确授权
- ⚠️ **修改核心配置** - 需要详细说明影响
- ⚠️ **数据库结构变更** - 需要确认迁移方案
- ⚠️ **第三方服务集成** - 需要确认API密钥和配置

## 💬 **用户交互最佳实践**

### **🎯 如何有效描述需求**

#### **需求描述模板**
```
📋 需求描述：
- 功能目标：[具体要实现什么功能]
- 影响范围：[涉及哪些文件/模块]
- 验收标准：[如何判断完成]
- 特殊要求：[UI风格、性能要求等]

🔍 示例：
- 功能目标：修改菜单栏的退出登录按钮样式
- 影响范围：AdminLayout.vue文件
- 验收标准：退出登录按钮与菜单项样式完全一致
- 特殊要求：保持紫色边框和右侧圆角设计
```

#### **清晰沟通的要点**
- ✅ **使用具体术语** - "菜单栏"而不是"左边那个"
- ✅ **提供上下文** - 说明在哪个页面、什么场景下
- ✅ **明确优先级** - 哪些是必须的，哪些是可选的
- ✅ **给出示例** - 提供参考图片或类似功能

### **🔄 任务分解策略**

#### **复杂任务分解原则**
```
大任务 → 小任务 → 验证点

示例：实现用户管理功能
├── 1. 创建用户数据模型 → 验证：数据库迁移成功
├── 2. 实现用户API接口 → 验证：API测试通过
├── 3. 开发用户管理页面 → 验证：页面显示正常
└── 4. 添加权限控制 → 验证：权限验证有效
```

#### **任务优先级设定**
- 🔴 **P0 - 紧急** - 影响核心功能，需要立即处理
- 🟡 **P1 - 重要** - 影响用户体验，需要优先处理
- 🟢 **P2 - 一般** - 功能优化，可以排期处理
- 🔵 **P3 - 低优** - 锦上添花，有时间再处理

### **✅ 验收标准设定**

#### **功能验收清单**
```
□ 功能实现完整 - 所有需求点都已实现
□ 界面显示正常 - UI符合设计要求
□ 交互体验良好 - 操作流畅，反馈及时
□ 兼容性测试通过 - 多分辨率、多浏览器
□ 性能表现良好 - 加载速度、响应时间
□ 代码质量合格 - 规范检查通过
□ 文档更新完成 - 相关文档已同步更新
```

## 🏗️ **壹心堂项目特定规范**

### **🎨 UI设计约束**
- **设计风格**: 毕加索艺术风格 + 紫色主色调 (#8B5CF6)
- **布局原则**: 黄金比例、窄侧边栏、扁平结构
- **分辨率支持**: 1024px-4K，自适应缩放0.8x-1.2x
- **组件标准**: 七色阴影效果、彩虹渐变、右侧圆角

### **🔧 技术栈约束**
- **前端**: Vue 3 + Vite + Ant Design Vue
- **后端**: Django + MySQL + Python 3.9+
- **小程序**: Taro + WeUI（强制要求）
- **测试**: Playwright自动化测试
- **部署**: 微信云托管

### **📋 开发流程约束**
- **强制检查**: 写代码前必须执行知识查询
- **单文件修改**: 每次只修改一个文件
- **立即测试**: 修改后立即验证功能
- **规范检查**: 完成后自检通过率≥90%

## 🎯 **常见使用场景**

### **场景1: 功能开发**
```
用户：我需要添加一个新的服务管理功能
AI：我来帮您实现。首先让我了解具体需求：
1. 这个服务管理功能包括哪些操作？（增删改查？）
2. 需要在哪个页面添加？（新页面还是现有页面？）
3. 数据结构是什么样的？（有哪些字段？）
4. 有什么特殊的业务逻辑吗？

[AI会先查询相关代码，分析现有架构，然后制定实施方案]
```

### **场景2: 问题修复**
```
用户：登录页面在某些分辨率下显示异常
AI：我来帮您诊断和修复。请提供更多信息：
1. 具体是哪些分辨率？（1024px？1366px？）
2. 异常表现是什么？（布局错乱？元素重叠？）
3. 是否有错误截图可以参考？

[AI会查询登录页面代码，分析CSS样式，定位问题并修复]
```

### **场景3: 性能优化**
```
用户：管理页面加载速度比较慢，需要优化
AI：我来分析性能问题。让我先了解情况：
1. 主要是哪个页面加载慢？（服务管理？用户管理？）
2. 大概的加载时间是多少？（3秒？5秒？）
3. 是首次加载慢还是操作响应慢？

[AI会分析代码结构，检查数据查询，提出优化方案]
```

## 🔍 **质量保证指南**

### **代码质量标准**
- **规范合规率**: ≥90%（使用project_check.py检查）
- **测试覆盖率**: 核心功能100%覆盖
- **性能标准**: 页面加载时间≤3秒
- **兼容性**: 支持Chrome、Firefox、Safari

### **交付检查清单**
```
□ 功能完整性 - 所有需求都已实现
□ 代码质量 - 规范检查通过
□ 测试验证 - 自动化测试通过
□ 文档更新 - 相关文档已同步
□ 用户体验 - 界面友好，操作流畅
□ 性能表现 - 加载速度符合要求
□ 兼容性 - 多环境测试通过
```

## 🚨 **故障排除**

### **常见问题及解决方案**

#### **问题1: AI理解偏差**
- **现象**: AI的实现与预期不符
- **解决**: 提供更详细的需求描述和示例
- **预防**: 使用需求描述模板，确认理解后再开始

#### **问题2: 修改影响其他功能**
- **现象**: 修改后其他页面出现问题
- **解决**: 立即回滚，重新分析影响范围
- **预防**: 修改前明确影响范围，修改后全面测试

#### **问题3: 性能问题**
- **现象**: 页面加载变慢或卡顿
- **解决**: 分析性能瓶颈，优化查询和渲染
- **预防**: 关注数据量大的操作，及时优化

## 📞 **获取帮助**

### **如何获得更好的支持**
1. **详细描述问题** - 提供错误信息、截图、复现步骤
2. **说明环境信息** - 浏览器版本、分辨率、操作系统
3. **提供相关代码** - 指出具体的文件和行号
4. **明确期望结果** - 说明希望达到什么效果

### **紧急情况处理**
- **生产环境问题**: 立即说明"紧急"，优先处理
- **数据安全问题**: 立即停止操作，寻求专业支持
- **系统崩溃**: 提供详细错误日志，协助快速恢复

---

> **💡 使用建议**: 
> 1. 开始前先阅读本指南，了解AI助手的能力和限制
> 2. 使用模板和清单，确保沟通清晰有效
> 3. 遵循项目规范，保证代码质量和一致性
> 4. 及时反馈问题，持续改进协作效果
