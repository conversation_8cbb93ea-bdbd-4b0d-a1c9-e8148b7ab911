# 壹心堂管理系统 - 脚本使用指南

## 📋 脚本概览

本项目提供了完整的开发、测试、检查脚本，确保开发流程的标准化和自动化。

## 🚀 开发环境脚本

### `./start-all-dev.sh` - 一键启动开发环境
**主要启动脚本，推荐使用**

```bash
# 启动所有开发服务
./start-all-dev.sh
```

**功能特性：**
- ✅ 自动检查和配置Python/Node.js环境
- ✅ 自动安装缺失的依赖
- ✅ 启动Django后端服务 (端口8000)
- ✅ 启动Vue前端服务 (端口3000)
- ✅ 启动Taro小程序构建 (端口10086)
- ✅ 实时服务监控和自动重启
- ✅ 优雅的进程清理和退出

**访问地址：**
- 后端API: http://localhost:8000/
- 前端管理: http://localhost:3000/
- 健康检查: http://localhost:8000/health/

### `./stop-all-dev.sh` - 停止开发环境
```bash
# 停止所有开发服务
./stop-all-dev.sh
```

### `./one_click_start.py` - Python启动脚本
```bash
# Python版本的启动脚本
python one_click_start.py
```

## 🔍 开发规范检查脚本

### `./project_check.py` - 统一开发规范检查
**新的统一检查脚本，支持多种模式**

```bash
# 默认规范检查模式
python project_check.py --mode check

# 完整开发工作流程模式
python project_check.py --mode workflow

# 快速检查模式
python project_check.py --mode quick

# 显示帮助
python project_check.py --help
```

**功能特性：**
- ✅ Try-Catch块完整性检查
- ✅ 错误处理规范检查
- ✅ 表单验证规范检查
- ✅ 加载状态管理检查
- ✅ 用户反馈机制检查
- ✅ 响应式设计检查
- ✅ 开发工作流程指导
- ✅ 批量修改脚本禁令检查

## 📁 目录结构规范

### 脚本文件位置约束
```
wechatcloud/
├── project_check.py          # 统一检查脚本 (根目录)
├── one_click_start.py         # 启动脚本 (根目录)
├── start-all-dev.sh          # 开发环境启动 (根目录)
├── stop-all-dev.sh           # 开发环境停止 (根目录)
├── scripts/                  # 通用脚本目录
│   └── check-standards.js    # Node.js检查脚本
├── admin/scripts/            # 前端专用脚本
├── client/scripts/           # 小程序专用脚本
├── server/scripts/           # 后端专用脚本
└── docs/guides/              # 脚本使用指南
```

### 测试脚本路径规范
```
wechatcloud/
├── test-reports/             # 测试报告目录 (根目录)
├── admin/test-reports/       # 前端测试报告
├── client/test-reports/      # 小程序测试报告
├── server/test-reports/      # 后端测试报告
└── docs/reports/             # 项目报告文档
```

## 🚨 脚本使用约束

### **🚫 严格禁止的操作**
- ❌ **禁止使用批量修改脚本** - 必须手动逐个修改文件
- ❌ **禁止在根目录创建临时脚本** - 使用指定的scripts目录
- ❌ **禁止修改核心启动脚本** - 除非经过充分测试
- ❌ **禁止跳过规范检查** - 所有代码提交前必须通过检查

### **✅ 推荐的使用流程**
1. **开发前**: 运行 `python project_check.py --mode workflow`
2. **开发中**: 使用 `./start-all-dev.sh` 启动环境
3. **开发后**: 运行 `python project_check.py --mode check`
4. **提交前**: 确保所有检查通过

## 📊 脚本执行标准

### **成功标准**
- ✅ 所有检查脚本返回状态码 0
- ✅ 规范合规率达到 90% 以上
- ✅ 无 ERROR 级别问题
- ✅ 开发环境正常启动

### **失败处理**
- 🚨 检查失败时禁止提交代码
- 🚨 必须修复所有 ERROR 级别问题
- 🚨 WARNING 级别问题建议修复
- 🚨 环境启动失败时检查端口冲突

## 🔄 脚本维护规范

### **更新原则**
- 🚨 **向后兼容**: 脚本更新必须保持向后兼容
- 🚨 **充分测试**: 更新后必须在多环境测试
- 🚨 **文档同步**: 脚本变更必须同步更新文档
- 🚨 **版本记录**: 重要变更必须记录版本信息

### **禁止操作**
- ❌ 直接修改生产环境脚本
- ❌ 删除现有脚本功能
- ❌ 添加未经测试的新功能
- ❌ 修改脚本的核心逻辑

---

**📋 文档版本**: v1.0  
**📅 最后更新**: 2025-07-17  
**👥 维护团队**: 壹心堂开发团队  
**🎯 适用范围**: 壹心堂中医推拿管理系统全栈开发

> 💡 **重要提醒**: 所有脚本使用必须严格遵守本指南，违反规范可能导致开发环境异常或代码质量问题。
