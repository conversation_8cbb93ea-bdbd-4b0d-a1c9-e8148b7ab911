# 🔥 壹心堂管理系统 - 热更新开发规范 v2.0

> **📋 文档目的**: 热更新开发模式的标准规范和最佳实践  
> **🔄 更新日期**: 2025-01-28  
> **🎯 适用范围**: 前端、后端、小程序开发  
> **👥 适用对象**: AI助手、开发团队、项目维护者  
> **🔥 核心理念**: 代码修改即时生效，无需重启服务

## 🚨 核心开发原则 (绝对遵守)

### ⚡ 热更新优先原则
```mermaid
graph TD
    A[代码修改] --> B[自动检测变化]
    B --> C[热更新生效]
    C --> D[浏览器自动刷新]
    D --> E[继续开发]
    E --> A
    
    F[❌ 禁止重启服务] --> G[除非必要情况]
    G --> H[依赖更新/配置变更]
```

### 🔴 绝对禁止的行为
- ❌ **频繁重启服务** - 热更新模式下无需重启
- ❌ **手动刷新浏览器** - 应该自动热更新
- ❌ **修改端口配置** - 使用标准端口避免冲突
- ❌ **忽略热更新失败** - 必须排查并修复热更新问题
- ❌ **在热更新模式下调试** - 使用专门的调试模式

## 🏗️ 热更新技术架构

### **前端热更新 (Vue 3 + Vite)**
```javascript
// vite.config.js - 热更新配置
export default {
  server: {
    port: 3000,
    host: '0.0.0.0',
    hmr: {
      port: 3001,
      overlay: true
    },
    watch: {
      usePolling: true,
      interval: 100
    }
  },
  plugins: [
    vue({
      include: [/\.vue$/, /\.md$/]
    })
  ]
}

// 热更新生效范围：
// ✅ Vue组件 (.vue文件)
// ✅ JavaScript/TypeScript (.js/.ts文件)
// ✅ CSS/SCSS样式 (.css/.scss文件)
// ✅ 静态资源 (图片、字体等)
// ❌ 配置文件 (需要重启)
```

### **后端热更新 (Django)**
```python
# settings.py - 开发模式配置
DEBUG = True
ALLOWED_HOSTS = ['*']

# Django自动重载配置
USE_TZ = True
LANGUAGE_CODE = 'zh-hans'

# 热更新生效范围：
# ✅ Python代码 (.py文件)
# ✅ 模板文件 (.html文件)
# ✅ 静态文件 (CSS/JS)
# ✅ 数据库模型 (需要migrate)
# ❌ settings.py (需要重启)
# ❌ urls.py根配置 (需要重启)
```

### **小程序热更新 (微信开发者工具)**
```javascript
// project.config.json - 小程序配置
{
  "setting": {
    "urlCheck": false,
    "es6": true,
    "enhance": true,
    "postcss": true,
    "preloadBackgroundData": false,
    "minified": false,
    "newFeature": true,
    "autoAudits": false,
    "checkInvalidKey": true,
    "checkSiteMap": true,
    "uploadWithSourceMap": true,
    "compileHotReLoad": true,  // 启用热重载
    "lazyloadPlaceholderEnable": false,
    "useMultiFrameRuntime": true,
    "useApiHook": true,
    "useApiHostProcess": true,
    "babelSetting": {
      "ignore": [],
      "disablePlugins": [],
      "outputPath": ""
    }
  }
}

// 热更新生效范围：
// ✅ 页面文件 (.wxml/.wxss/.js)
// ✅ 组件文件
// ✅ 样式文件
// ✅ 配置文件 (app.json/page.json)
// ❌ project.config.json (需要重新编译)
```

## 🛠️ 启动脚本增强功能

### **智能端口冲突检测**
```bash
# 端口检测逻辑
check_port_conflict() {
    local port=$1
    local service_name=$2
    
    # 1. 检测端口占用
    local pids=$(lsof -ti:$port)
    
    # 2. 识别进程归属
    for pid in $pids; do
        if is_our_process $pid; then
            echo "✅ 本项目进程，保持运行"
        else
            echo "❌ 其他程序进程，自动清理"
            kill -9 $pid
        fi
    done
}

# 进程归属识别
is_our_process() {
    local pid=$1
    local process_info=$(ps -p $pid -o command --no-headers)
    
    # 检查关键词
    if echo "$process_info" | grep -q -E "(django|runserver|npm.*dev|vue|vite|yixintang)"; then
        return 0  # 是我们的进程
    fi
    
    return 1  # 不是我们的进程
}
```

### **进程监控和管理**
```bash
# PID文件管理
PID_DIR=".pids"
BACKEND_PID_FILE="$PID_DIR/backend.pid"
FRONTEND_PID_FILE="$PID_DIR/frontend.pid"
MINIPROGRAM_PID_FILE="$PID_DIR/miniprogram.pid"

# 启动服务时记录PID
start_service() {
    local service_name=$1
    local command=$2
    local pid_file=$3
    
    # 检查是否已运行
    if [ -f "$pid_file" ]; then
        local existing_pid=$(cat "$pid_file")
        if kill -0 $existing_pid 2>/dev/null; then
            echo "✅ $service_name 已在运行，支持热更新"
            return 0
        fi
    fi
    
    # 启动新进程
    $command &
    local new_pid=$!
    echo $new_pid > "$pid_file"
    echo "🚀 $service_name 启动成功 (PID: $new_pid)"
}
```

## 📋 AI助手开发规范

### **强制执行的热更新流程**
1. **🚨 启动检查** - 使用 `./start-enhanced.sh` 启动所有服务
2. **🚨 代码修改** - 直接修改代码，无需重启
3. **🚨 自动验证** - 等待热更新生效，检查浏览器变化
4. **🚨 问题排查** - 如果热更新失败，检查控制台错误
5. **🚨 必要重启** - 仅在配置文件修改时重启对应服务

### **AI助手记忆要点**
```markdown
# AI助手必须记住的热更新规范

## 🔥 热更新优先
- 代码修改后，等待3-5秒让热更新生效
- 不要建议用户重启服务，除非明确需要
- 前端修改立即生效，后端修改需要等待Django重载

## 🚀 启动方式 (统一使用start.sh)
- 使用 ./start.sh 启动所有服务 (已增强端口冲突检测)
- 脚本自动检测进程归属，保护本项目进程
- 支持热更新模式，已运行的服务不会重复启动
- 智能端口管理，自动清理冲突的其他程序

## 🛠️ 调试方式
- 前端: Chrome开发者工具 + Vue DevTools
- 后端: Django Debug Toolbar + 控制台日志
- 小程序: 微信开发者工具调试器
- 自动化测试: Selenium + Chrome浏览器

## ❌ 避免重启的情况
- Vue组件修改
- CSS样式调整
- JavaScript逻辑更新
- Django视图函数修改
- 模板文件更新

## ✅ 需要重启的情况
- 安装新的npm包
- 修改vite.config.js
- 修改Django settings.py
- 修改数据库模型 (需要migrate)

## 🎯 端口冲突处理
- 自动检测端口占用情况
- 识别进程归属 (本项目 vs 其他程序)
- 保护本项目进程，清理其他冲突进程
- 支持PID文件管理，避免重复启动
```

## 🧪 热更新测试验证

### **前端热更新测试**
```bash
# 测试步骤
1. 启动前端服务: ./start-enhanced.sh frontend
2. 打开浏览器: http://localhost:3000
3. 修改Vue组件中的文本
4. 验证: 浏览器自动更新，无需刷新
5. 修改CSS样式
6. 验证: 样式立即生效

# 预期结果
- 修改后3秒内生效
- 浏览器控制台显示HMR更新日志
- 页面状态保持，不会重新加载
```

### **后端热更新测试**
```bash
# 测试步骤
1. 启动后端服务: ./start-enhanced.sh backend
2. 修改Django视图函数
3. 发送API请求
4. 验证: 新逻辑立即生效
5. 修改模板文件
6. 验证: 页面渲染更新

# 预期结果
- 修改后5秒内生效
- 控制台显示"Watching for file changes"
- 自动重载提示: "Performing system checks..."
```

## 🔧 故障排除指南

### **热更新失败的常见原因**
```bash
# 前端热更新失败
❌ 问题: 修改后页面不更新
✅ 解决: 检查vite.config.js配置
✅ 解决: 检查浏览器控制台错误
✅ 解决: 确认文件保存成功

❌ 问题: HMR连接失败
✅ 解决: 检查端口3001是否被占用
✅ 解决: 重启前端服务
✅ 解决: 清除浏览器缓存

# 后端热更新失败
❌ 问题: 代码修改不生效
✅ 解决: 检查Django DEBUG=True
✅ 解决: 检查文件权限
✅ 解决: 查看控制台错误信息

❌ 问题: 数据库相关修改不生效
✅ 解决: 运行 python manage.py migrate
✅ 解决: 检查模型文件语法
✅ 解决: 重启Django服务
```

### **性能优化建议**
```bash
# 提升热更新速度
1. 使用SSD硬盘
2. 关闭不必要的文件监控
3. 排除node_modules目录
4. 使用增量编译
5. 优化Webpack/Vite配置

# 减少内存占用
1. 限制文件监控范围
2. 定期清理临时文件
3. 使用轻量级编辑器
4. 关闭不必要的浏览器标签
```

## 🎯 最佳实践总结

### **开发工作流程**
1. **启动**: 使用 `./start-enhanced.sh` 一键启动
2. **开发**: 直接修改代码，依赖热更新
3. **测试**: 在浏览器中验证修改效果
4. **调试**: 使用开发者工具调试问题
5. **提交**: 确认功能正常后提交代码

### **团队协作规范**
- 🔥 **统一使用热更新模式** - 所有开发者使用相同的启动方式
- 🔥 **避免频繁重启** - 培养热更新开发习惯
- 🔥 **及时反馈问题** - 热更新失败时立即排查
- 🔥 **保持环境一致** - 使用相同的Node.js和Python版本

## 🌐 浏览器测试要求

### **Selenium自动化测试 (强制要求)**
```bash
# 🎯 主要测试浏览器
Chrome 90+   # Selenium自动化测试专用 (强制)
Firefox 88+  # 兼容性测试
Safari 14+   # 兼容性测试
Edge 90+     # 兼容性测试

# 🎯 自动化测试配置
SELENIUM_BROWSER=chrome      # 强制使用Chrome
SELENIUM_HEADLESS=true       # 无头模式测试
SELENIUM_TIMEOUT=30000       # 30秒超时

# 🎯 测试验证要求
- 所有搜索功能必须在Chrome中测试通过
- 所有排序功能必须在Chrome中测试通过
- 所有页面加载必须在Chrome中验证
- 所有交互功能必须在Chrome中正常工作
```

### **性能指标要求**
```bash
# 🎯 Chrome性能基准
LCP < 2.5s   # 最大内容绘制
FID < 100ms  # 首次输入延迟
CLS < 0.1    # 累积布局偏移
TTI < 3.5s   # 可交互时间

# 🎯 热更新性能要求
前端HMR < 3秒    # Vue热更新响应时间
后端重载 < 5秒   # Django自动重载时间
搜索响应 < 1秒   # 搜索功能响应时间
排序响应 < 1秒   # 排序功能响应时间
```

---

> **🔥 热更新开发理念**: 代码修改即时生效，开发效率最大化
> **⚡ 性能目标**: 前端热更新 < 3秒，后端热更新 < 5秒
> **🎯 用户体验**: 无感知更新，保持开发流畅性
> **🌐 测试标准**: 强制使用Selenium + Chrome进行自动化测试验证
> **🚀 持续改进**: 基于实际使用情况优化热更新配置
