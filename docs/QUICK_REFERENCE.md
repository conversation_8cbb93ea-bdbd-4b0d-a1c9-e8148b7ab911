# ⚡ 壹心堂快速参考手册

> **🎯 单人全栈开发的常用命令和配置速查**
> **📅 更新日期**: 2025-01-21
> **💡 用途**: 日常开发的快速查询手册

## 🚀 一键启动命令

### 🔧 开发环境启动
```bash
# 一键启动所有服务
./scripts/dev-setup.sh

# 或者分别启动：
# 前端开发服务器
cd admin && npm run dev

# 后端开发服务器  
cd backend && python manage.py runserver

# 小程序开发
cd miniprogram && npm run dev:weapp
```

### 🎨 CSS开发工具
```bash
# CSS标准检查
npm run stylelint-check

# CSS自动修复
npm run stylelint

# 轮廓调试检查
npm run outline-check
```

## 🛠️ 常用开发命令

### 📦 前端开发 (Vue + Vite)
```bash
cd admin

# 安装依赖
npm install

# 开发服务器
npm run dev

# 生产构建
npm run build

# 预览构建结果
npm run preview

# 类型检查
npm run type-check
```

### 🐍 后端开发 (Django)
```bash
cd backend

# 虚拟环境
python -m venv venv
source venv/bin/activate  # macOS/Linux
# venv\Scripts\activate   # Windows

# 安装依赖
pip install -r requirements.txt

# 数据库迁移
python manage.py makemigrations
python manage.py migrate

# 创建超级用户
python manage.py createsuperuser

# 开发服务器
python manage.py runserver

# 收集静态文件
python manage.py collectstatic
```

### 📱 小程序开发 (Taro)
```bash
cd miniprogram

# 安装依赖
npm install

# 微信小程序
npm run dev:weapp

# 构建生产版本
npm run build:weapp

# 其他平台
npm run dev:alipay    # 支付宝小程序
npm run dev:swan      # 百度小程序
npm run dev:tt        # 字节跳动小程序
```

## 🔍 调试和测试

### 🌐 Chrome扩展使用
```javascript
// Debug CSS扩展
1. 点击工具栏的Debug CSS图标
2. 所有元素显示彩色轮廓
3. 一键开启/关闭

// Web Developer扩展  
1. 点击Web Developer工具栏
2. Outline → Outline All Elements
3. 选择不同轮廓选项
```

### 🧪 自动化测试
```bash
# 前端测试
cd admin
npm run test

# 后端测试
cd backend  
python manage.py test

# 端到端测试
npm run test:e2e
```

## 📊 数据库操作

### 🗄️ MySQL常用命令
```sql
-- 连接数据库
mysql -u root -p

-- 查看数据库
SHOW DATABASES;

-- 使用数据库
USE yixintang;

-- 查看表
SHOW TABLES;

-- 查看表结构
DESCRIBE table_name;

-- 备份数据库
mysqldump -u root -p yixintang > backup.sql

-- 恢复数据库
mysql -u root -p yixintang < backup.sql
```

### 🔧 Django数据库操作
```bash
# 进入Django shell
python manage.py shell

# 数据库迁移
python manage.py makemigrations app_name
python manage.py migrate

# 重置迁移
python manage.py migrate app_name zero
python manage.py migrate app_name

# 查看迁移状态
python manage.py showmigrations
```

## 🎨 CSS标准变量

### 🎯 壹心堂色彩系统
```css
:root {
  /* 主色调 */
  --primary-color: #8B5CF6;        /* 主紫色 */
  --secondary-color: #A78BFA;      /* 浅紫色 */
  --accent-color: #7C3AED;         /* 深紫色 */
  --hover-color: rgba(139, 92, 246, 0.1); /* 悬停背景 */
}
```

### 📐 标准尺寸系统
```css
:root {
  /* 数据行标准 */
  --row-height: 50px;              /* 与菜单项对齐 */
  --cell-padding: 0 8px;           /* 防止边界超出 */
  --cell-font-size: 0.9rem;        /* 适应50px高度 */
  --cell-line-height: 1.2;         /* 紧凑行高 */
  
  /* 服务图片标准 */
  --service-image-width: 55px;
  --service-image-height: 20px;
}
```

## 🧩 标准组件使用

### 📊 数据表格组件
```vue
<script setup>
import StandardDataRow from '@/components/standards/StandardDataRow.vue'
import StandardDataCell from '@/components/standards/StandardDataCell.vue'
</script>

<template>
  <StandardDataRow>
    <StandardDataCell align="left">文本内容</StandardDataCell>
    <StandardDataCell align="center">数字</StandardDataCell>
    <StandardDataCell align="right">操作按钮</StandardDataCell>
  </StandardDataRow>
</template>
```

## 🔗 Git操作

### 📝 提交流程
```bash
# 查看状态
git status

# 添加文件
git add .

# 提交（会触发自动检查）
git commit -m "feat: 添加新功能"

# 推送到远程
git push origin main

# 查看提交历史
git log --oneline
```

### 🌿 分支操作
```bash
# 创建并切换分支
git checkout -b feature/new-feature

# 切换分支
git checkout main

# 合并分支
git merge feature/new-feature

# 删除分支
git branch -d feature/new-feature
```

## 🚀 部署操作

### 🌐 微信云托管部署
```bash
# 1. 构建前端
cd admin && npm run build

# 2. 准备后端
cd backend && python manage.py collectstatic

# 3. 推送代码（触发自动部署）
git add .
git commit -m "deploy: 部署新版本"
git push origin main

# 4. 监控部署状态
# 在微信云托管控制台查看部署日志
```

## 🔧 故障排除

### ❌ 常见问题解决
```bash
# Stylelint检查失败
npm run stylelint  # 自动修复

# Git提交被阻止
# 查看错误信息，修复问题后重新提交

# 开发服务器启动失败
# 检查端口占用：lsof -i :3000
# 杀死进程：kill -9 PID

# 数据库连接失败
# 检查MySQL服务状态
# 检查数据库配置
```

### 🔍 调试技巧
```javascript
// 前端调试
console.log('调试信息')
debugger; // 断点调试

// Vue组件调试
// 使用Vue Devtools扩展

// 后端调试
import pdb; pdb.set_trace()  # Python断点

// API调试
// 使用Postman或curl测试接口
```

## 📱 微信小程序相关

### 🔧 小程序开发工具
```bash
# 预览小程序
npm run dev:weapp
# 然后在微信开发者工具中打开dist目录

# 上传小程序
# 在微信开发者工具中点击上传

# 小程序配置
# 修改 src/app.config.js
```

### 📊 小程序API
```javascript
// 微信API示例
import Taro from '@tarojs/taro'

// 显示提示
Taro.showToast({
  title: '操作成功',
  icon: 'success'
})

// 页面跳转
Taro.navigateTo({
  url: '/pages/detail/index'
})

// 获取用户信息
Taro.getUserProfile({
  desc: '用于完善用户资料'
})
```

## 🎯 性能优化

### ⚡ 前端优化
```bash
# 分析构建大小
npm run build -- --analyze

# 代码分割
# 使用动态import()

# 图片优化
# 使用WebP格式
# 压缩图片大小
```

### 🚀 后端优化
```python
# Django优化
# 使用数据库索引
# 查询优化
# 缓存配置

# 示例：添加数据库索引
class Meta:
    indexes = [
        models.Index(fields=['created_at']),
    ]
```

**这个快速参考手册包含了日常开发中90%的常用操作，建议收藏备用！** ⚡✅
