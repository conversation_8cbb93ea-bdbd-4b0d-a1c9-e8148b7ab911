# 🎨 服务管理页面模板标准

## 📋 概述
服务管理页面已经实现了完整的弹出窗口、加载状态、布局逻辑等最佳实践，现将其设置为其他页面的参考模板。

## 🏗️ 核心模板结构

### 1. 数据加载状态管理
```javascript
// 交互加载状态
const loadingStates = reactive({
  dataLoading: false,    // 数据加载状态
  itemEdit: false,       // 编辑加载状态
  itemSubmit: false,     // 提交加载状态
  itemDelete: false      // 删除加载状态
});
```

### 2. 表格加载状态显示
```vue
<!-- 数据加载状态 -->
<div v-if="loadingStates.dataLoading" class="table-loading-container">
  <div class="table-loading-content">
    <div class="loading-spinner-large">⏳</div>
    <div class="loading-text">正在加载数据...</div>
  </div>
</div>

<!-- 无数据状态 -->
<div v-else-if="paginatedData.length === 0" class="table-empty-container">
  <div class="table-empty-content">
    <div class="empty-icon">📋</div>
    <div class="empty-text">暂无数据</div>
  </div>
</div>

<!-- 数据内容 -->
<div v-else v-for="record in paginatedData" :key="record.id">
  <!-- 数据行内容 -->
</div>
```

### 3. 模态框标准结构
```vue
<!-- 标准模态框 -->
<div v-if="modalVisible" class="modal-overlay">
  <div class="form-modal" @click.stop>
    <div class="form-header">
      <h3 class="form-title">{{ modalTitle }}</h3>
      <button class="close-btn" @click="hideModal">×</button>
    </div>
    
    <div class="form-content">
      <!-- 表单内容 -->
    </div>
    
    <div class="form-actions">
      <button class="action-btn cancel-btn" @click="hideModal">取消</button>
      <button class="action-btn confirm-btn" @click="handleSubmit" :disabled="confirmLoading">
        {{ confirmLoading ? '提交中...' : '确定' }}
      </button>
    </div>
  </div>
</div>
```

### 4. 响应式模态框CSS
```css
.modal-overlay {
  position: fixed;
  top: 0;
  left: 180px; /* 侧边栏宽度 */
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(5px);
}

.form-modal {
  background: linear-gradient(135deg, rgba(248, 244, 255, 0.98) 0%, rgba(232, 228, 255, 0.95) 100%);
  border-radius: 16px;
  max-width: min(900px, calc(100vw - 240px));
  max-height: calc(100vh - 40px);
  width: min(90%, calc(100vw - 240px));
  overflow: hidden;
  border: 2px solid var(--van-gogh-border-medium);
  box-shadow: 0 20px 40px rgba(139, 92, 246, 0.3);
  display: flex;
  flex-direction: column;
}

.form-content {
  padding: 25px;
  flex: 1;
  overflow: hidden; /* 去掉滚动条 */
  display: flex;
  flex-direction: column;
  gap: 20px;
}
```

### 5. 加载状态CSS
```css
/* 表格加载状态容器 */
.table-loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  min-height: 300px;
  padding: 40px 0;
}

.table-loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 30px;
  background: linear-gradient(135deg, rgba(139, 92, 246, 0.05) 0%, rgba(244, 114, 182, 0.05) 100%);
  border-radius: 12px;
  border: 1px solid rgba(139, 92, 246, 0.2);
  box-shadow: 0 8px 30px rgba(139, 92, 246, 0.1);
  animation: fadeIn 0.5s ease-out;
}

.loading-spinner-large {
  font-size: 32px;
  animation: spin 1.5s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
```

### 6. 数据加载函数模板
```javascript
const loadData = async () => {
  try {
    console.log('🔍 开始加载数据...');
    loadingStates.dataLoading = true;

    const response = await fetch('/api/v1/endpoint/');
    if (!response.ok) {
      throw new Error(`HTTP ${response.status}: ${response.statusText}`);
    }

    const data = await response.json();
    
    if (data.results && Array.isArray(data.results)) {
      items.value = data.results;
    } else if (Array.isArray(data)) {
      items.value = data;
    } else {
      console.log('❌ API响应格式错误:', data);
      loadFallbackData();
    }
  } catch (error) {
    console.error('❌ 加载数据失败:', error);
    loadFallbackData();
  } finally {
    loadingStates.dataLoading = false;
  }
};
```

### 7. 交互操作加载状态
```javascript
// 编辑操作
const handleEdit = async (item) => {
  loadingStates.itemEdit = true;
  try {
    // 编辑逻辑
  } catch (error) {
    console.error('编辑失败:', error);
  } finally {
    loadingStates.itemEdit = false;
  }
};

// 删除操作
const handleDelete = async (item) => {
  loadingStates.itemDelete = true;
  try {
    // 删除逻辑
  } catch (error) {
    console.error('删除失败:', error);
  } finally {
    loadingStates.itemDelete = false;
  }
};
```

### 8. 分页加载状态
```javascript
const goToPage = (page) => {
  loadingStates.dataLoading = true;
  currentPage.value = page;
  
  setTimeout(() => {
    loadingStates.dataLoading = false;
  }, 150);
};
```

## 📱 响应式设计标准

### 移动端适配
```css
@media (max-width: 768px) {
  .modal-overlay {
    left: 0;
    width: 100%;
  }
  
  .form-modal {
    max-width: calc(100vw - 20px);
    width: calc(100vw - 20px);
    margin: 10px;
  }
}

@media (max-width: 480px) {
  .form-modal {
    max-width: calc(100vw - 10px);
    width: calc(100vw - 10px);
    margin: 5px;
    border-radius: 12px;
  }
}
```

## 🎯 应用指南

### 需要应用此模板的页面
1. **技师管理** (TherapistManagement.vue)
2. **客户管理** (CustomerManagement.vue)
3. **预约管理** (AppointmentManagement.vue)
4. **财务记录** (FinanceRecordsView.vue)
5. **健康贴士** (HealthTipsView.vue)

### 应用步骤
1. 复制加载状态管理结构
2. 应用标准模态框布局
3. 实现表格加载状态显示
4. 添加响应式CSS样式
5. 更新交互操作的加载反馈

### 注意事项
- 保持命名一致性
- 确保加载状态正确清除
- 适配各页面的具体业务逻辑
- 保持梵高艺术风格的视觉一致性
