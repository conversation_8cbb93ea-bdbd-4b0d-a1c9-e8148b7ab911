# 🎨 壹心堂CSS标准规范

> **📋 文档目的**: 统一的CSS编写标准和变量使用规范
> **🔄 更新日期**: 2025-01-21
> **🎯 基于**: CSS标准库 `admin/src/styles/standards.css`
> **🛠️ 工具**: Stylelint自动检查

## 🎯 核心原则

### ⚡ 强制使用CSS变量
```css
/* ✅ 正确 - 使用标准变量 */
.data-row {
  height: var(--row-height);
  padding: var(--cell-padding);
  font-size: var(--cell-font-size);
  color: var(--primary-color);
}

/* ❌ 错误 - 使用固定值 */
.data-row {
  height: 50px;
  padding: 0 8px;
  font-size: 0.9rem;
  color: #8B5CF6;
}
```

## 📏 标准变量库

### 🎨 壹心堂色彩系统
```css
:root {
  /* 主色调 */
  --primary-color: #8B5CF6;        /* 主紫色 */
  --secondary-color: #A78BFA;      /* 浅紫色 */
  --accent-color: #7C3AED;         /* 深紫色 */
  --hover-color: rgba(139, 92, 246, 0.1); /* 悬停背景 */
}
```

### 📐 标准尺寸系统
```css
:root {
  /* 数据行标准 - 基于轮廓调试成功案例 */
  --row-height: 50px;              /* 与菜单项对齐 */
  --cell-padding: 0 8px;           /* 防止边界超出 */
  --cell-font-size: 0.9rem;        /* 适应50px高度 */
  --cell-line-height: 1.2;         /* 紧凑行高 */
  
  /* 服务图片标准 */
  --service-image-width: 55px;
  --service-image-height: 20px;
  
  /* 表格容器标准 */
  --table-body-height: 680px;      /* 绿色区域标准高度 */
  --table-container-padding: 58px; /* 蓝色区域底部内边距 */
}
```

### 📱 响应式断点
```css
:root {
  --breakpoint-tablet: 1024px;
  --breakpoint-desktop: 1366px;
  --breakpoint-large: 1920px;
}

/* 标准媒体查询 */
@media (min-width: 1366px) and (max-width: 1919px) {
  :root {
    --table-body-height: 680px;
  }
}
```

## 🧩 标准CSS类

### 📦 数据行标准类
```css
.standard-data-row {
  height: var(--row-height);
  max-height: var(--row-height);
  min-height: var(--row-height);
  overflow: hidden;
  display: flex;
  align-items: center;
}
```

### 📝 数据单元格标准类
```css
.standard-data-cell {
  padding: var(--cell-padding);
  font-size: var(--cell-font-size);
  line-height: var(--cell-line-height);
  max-height: var(--row-height);
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}
```

### 🖼️ 服务图片标准类
```css
.standard-service-image {
  width: var(--service-image-width);
  height: var(--service-image-height);
  object-fit: cover;
  border-radius: 4px;
}
```

## 🔧 Stylelint配置

### ✅ 自动检查规则
我们的Stylelint配置会自动检查：

1. **CSS变量使用**: 强制使用标准变量
2. **选择器具体性**: 防止CSS优先级问题
3. **属性重复**: 避免重复属性定义
4. **媒体查询标准**: 使用标准断点
5. **颜色格式**: 统一颜色表示方式

### 🛠️ 使用方法
```bash
# 检查CSS标准合规性
npm run stylelint-check

# 自动修复CSS问题
npm run stylelint
```

## 🎯 最佳实践

### ✅ 推荐做法
```css
/* 1. 使用CSS变量 */
.component {
  height: var(--row-height);
  padding: var(--cell-padding);
}

/* 2. 使用标准类名 */
.data-table .data-row {
  /* 继承标准样式 */
}

/* 3. 响应式设计 */
@media (min-width: 1366px) {
  .component {
    height: var(--table-body-height);
  }
}
```

### ❌ 避免做法
```css
/* 1. 避免固定像素值 */
.component {
  height: 50px; /* 应该使用 var(--row-height) */
}

/* 2. 避免颜色名称 */
.component {
  color: red; /* 应该使用 var(--primary-color) */
}

/* 3. 避免!important */
.component {
  height: 60px !important; /* 应该解决CSS优先级问题 */
}
```

## 🔍 调试和验证

### 🌐 Chrome扩展调试
```javascript
// 使用Debug CSS扩展
1. 点击Chrome工具栏的Debug CSS图标
2. 查看元素边界是否符合标准
3. 验证50px行高是否正确

// 使用Web Developer扩展
1. Outline → Outline All Elements
2. 检查布局结构
```

### 🔧 IDEA实时检查
```vue
<style scoped>
/* IDEA中Stylelint会实时提示 */
.component {
  height: 60px; /* 红色波浪线提示使用变量 */
}
</style>
```

## 📊 质量标准

### ✅ 合规要求
- **CSS变量使用率**: 100%
- **Stylelint检查**: 0错误
- **标准类使用**: 推荐使用
- **响应式兼容**: 5种分辨率支持

### 🎯 成功指标
- **开发效率**: 减少50%的样式调试时间
- **一致性**: 100%的视觉一致性
- **维护性**: 统一的变量管理
- **兼容性**: 多分辨率完美适配

**通过严格遵循这套CSS标准，确保壹心堂管理系统的视觉一致性和开发效率！** 🎯✅
