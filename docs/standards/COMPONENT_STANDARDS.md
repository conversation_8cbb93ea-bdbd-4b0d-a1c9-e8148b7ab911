# 🧩 壹心堂组件标准规范

> **📋 文档目的**: 标准组件的使用规范和最佳实践
> **🔄 更新日期**: 2025-01-21
> **🎯 基于**: 标准组件模板 `admin/src/components/standards/`
> **🛠️ 工具**: Vue.js + 标准CSS变量

## 🎯 核心原则

### ⚡ 强制使用标准组件
```vue
<!-- ✅ 正确 - 使用标准组件 -->
<StandardDataRow>
  <StandardDataCell align="center">
    内容
  </StandardDataCell>
</StandardDataRow>

<!-- ❌ 错误 - 自定义实现 -->
<div class="data-row" style="height: 50px;">
  <div class="data-cell">内容</div>
</div>
```

## 🧩 标准组件库

### 📊 StandardDataRow 组件

#### 🔧 基本用法
```vue
<script setup>
import StandardDataRow from '@/components/standards/StandardDataRow.vue'
</script>

<template>
  <StandardDataRow>
    <!-- 内容 -->
  </StandardDataRow>
</template>
```

#### ⚙️ 属性配置
```vue
<StandardDataRow 
  :debug="false"                    <!-- 调试模式 -->
  :additional-classes="'custom'"    <!-- 额外CSS类 -->
>
  <!-- 内容 -->
</StandardDataRow>
```

#### 🎯 特性说明
- **强制高度**: 50px（var(--row-height)）
- **防止超出**: overflow: hidden
- **标准布局**: flex + align-items: center
- **调试支持**: 紫色轮廓标记
- **悬停效果**: 壹心堂品牌色

### 📝 StandardDataCell 组件

#### 🔧 基本用法
```vue
<script setup>
import StandardDataCell from '@/components/standards/StandardDataCell.vue'
</script>

<template>
  <StandardDataCell align="center">
    内容
  </StandardDataCell>
</template>
```

#### ⚙️ 属性配置
```vue
<StandardDataCell 
  align="center"                    <!-- left/center/right -->
  :debug="false"                    <!-- 调试模式 -->
  :clickable="false"                <!-- 是否可点击 -->
  :additional-classes="'custom'"    <!-- 额外CSS类 -->
>
  内容
</StandardDataCell>
```

#### 🎯 特性说明
- **标准内边距**: 0 8px（var(--cell-padding)）
- **标准字体**: 0.9rem（var(--cell-font-size)）
- **紧凑行高**: 1.2（var(--cell-line-height)）
- **防止超出**: max-height限制
- **对齐选项**: 左/中/右对齐
- **点击支持**: 可选的点击交互

## 🎨 组件样式系统

### 🎯 标准尺寸
```css
/* StandardDataRow */
height: var(--row-height);          /* 50px */
max-height: var(--row-height);
min-height: var(--row-height);

/* StandardDataCell */
padding: var(--cell-padding);       /* 0 8px */
font-size: var(--cell-font-size);   /* 0.9rem */
line-height: var(--cell-line-height); /* 1.2 */
```

### 🎨 壹心堂品牌色
```css
/* 悬停效果 */
background-color: var(--hover-color);    /* rgba(139, 92, 246, 0.1) */
box-shadow: var(--shadow-light);         /* 轻阴影 */
transform: translateX(-2px);             /* 微移动效果 */
```

### 🔍 调试模式
```css
/* 调试轮廓 */
.debug-outline-purple {
  outline: 3px solid purple !important;
}

.debug-outline-yellow {
  outline: 3px solid yellow !important;
}
```

## 🔧 使用示例

### 📊 数据表格标准用法
```vue
<script setup>
import StandardDataRow from '@/components/standards/StandardDataRow.vue'
import StandardDataCell from '@/components/standards/StandardDataCell.vue'

const services = [
  { id: 1, name: '推拿服务', price: 100 },
  { id: 2, name: '针灸服务', price: 150 }
]
</script>

<template>
  <div class="data-table">
    <StandardDataRow v-for="service in services" :key="service.id">
      <StandardDataCell align="left">
        {{ service.name }}
      </StandardDataCell>
      <StandardDataCell align="center">
        ¥{{ service.price }}
      </StandardDataCell>
      <StandardDataCell align="right" :clickable="true">
        <button>编辑</button>
      </StandardDataCell>
    </StandardDataRow>
  </div>
</template>
```

### 🔍 调试模式用法
```vue
<template>
  <!-- 开发时启用调试模式 -->
  <StandardDataRow :debug="true">
    <StandardDataCell :debug="true" align="center">
      调试内容
    </StandardDataCell>
  </StandardDataRow>
</template>
```

### 🎨 自定义样式扩展
```vue
<template>
  <StandardDataRow :additional-classes="'highlight'">
    <StandardDataCell :additional-classes="'bold-text'">
      重要内容
    </StandardDataCell>
  </StandardDataRow>
</template>

<style scoped>
.highlight {
  background-color: var(--accent-color);
}

.bold-text {
  font-weight: bold;
}
</style>
```

## 🎯 最佳实践

### ✅ 推荐做法

#### 1. 标准组件优先
```vue
<!-- 优先使用标准组件 -->
<StandardDataRow>
  <StandardDataCell>内容</StandardDataCell>
</StandardDataRow>
```

#### 2. 合理使用属性
```vue
<!-- 根据内容选择对齐方式 -->
<StandardDataCell align="left">文本内容</StandardDataCell>
<StandardDataCell align="center">数字</StandardDataCell>
<StandardDataCell align="right">操作按钮</StandardDataCell>
```

#### 3. 调试模式开发
```vue
<!-- 开发时启用调试，生产时关闭 -->
<StandardDataRow :debug="isDev">
  <StandardDataCell :debug="isDev">内容</StandardDataCell>
</StandardDataRow>
```

### ❌ 避免做法

#### 1. 避免内联样式
```vue
<!-- ❌ 错误 -->
<StandardDataRow style="height: 60px;">
  内容
</StandardDataRow>

<!-- ✅ 正确 -->
<StandardDataRow :additional-classes="'custom-height'">
  内容
</StandardDataRow>
```

#### 2. 避免破坏标准尺寸
```css
/* ❌ 错误 - 覆盖标准高度 */
.custom-row {
  height: 60px !important;
}

/* ✅ 正确 - 使用标准变量 */
.custom-row {
  height: var(--row-height);
}
```

## 🔍 调试和验证

### 🌐 Chrome扩展调试
```javascript
// 使用Debug CSS扩展验证组件边界
1. 启用Debug CSS扩展
2. 检查StandardDataRow是否为50px高度
3. 验证StandardDataCell内边距是否为0 8px
```

### 🔧 组件调试模式
```vue
<!-- 启用组件内置调试 -->
<StandardDataRow :debug="true">
  <!-- 显示紫色轮廓和"标准数据行"标签 -->
  <StandardDataCell :debug="true">
    <!-- 显示黄色轮廓和"标准数据单元格"标签 -->
    内容
  </StandardDataCell>
</StandardDataRow>
```

## 📊 质量标准

### ✅ 合规要求
- **标准组件使用率**: 推荐100%
- **属性正确使用**: 必须正确配置align等属性
- **调试模式**: 开发时启用，生产时关闭
- **样式覆盖**: 避免!important覆盖标准样式

### 🎯 成功指标
- **视觉一致性**: 100%统一的行高和间距
- **开发效率**: 减少70%的布局调试时间
- **维护性**: 统一的组件接口
- **可调试性**: 完善的调试支持

## 🚀 扩展开发

### 🔧 创建新的标准组件
```vue
<!-- 基于现有标准组件扩展 -->
<script setup>
import StandardDataRow from '@/components/standards/StandardDataRow.vue'
import StandardDataCell from '@/components/standards/StandardDataCell.vue'

// 新组件逻辑
</script>

<template>
  <StandardDataRow>
    <StandardDataCell>
      <!-- 扩展功能 -->
    </StandardDataCell>
  </StandardDataRow>
</template>

<style scoped>
/* 使用标准CSS变量 */
.extension {
  height: var(--row-height);
  padding: var(--cell-padding);
}
</style>
```

**通过严格使用标准组件，确保壹心堂管理系统的一致性和可维护性！** 🎯✅
