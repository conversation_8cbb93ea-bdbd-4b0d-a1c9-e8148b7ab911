# 🏗️ 壹心堂管理系统开发规范

## 📋 目录
- [开发习惯规范](#开发习惯规范)
- [UI设计规范](#ui设计规范)
- [代码规范](#代码规范)
- [Git工作流规范](#git工作流规范)
- [项目结构规范](#项目结构规范)
- [测试规范](#测试规范)
- [部署规范](#部署规范)
- [AI集成规范](#ai集成规范)

---

## 🏆 开发习惯规范

### 核心开发原则

#### 1. 代码整理优先原则
- ✅ **定期整理**: 每次开发前先整理现有代码，删除重复和无用内容
- ✅ **简化优先**: 优先选择简单、清晰的实现方案
- ✅ **统一管理**: 相同功能只保留一个实现，避免重复
- ✅ **文档同步**: 代码变更时同步更新相关文档

#### 2. 渐进式开发原则
- ✅ **小步快跑**: 将大功能拆分为小的可测试单元
- ✅ **及时提交**: 完成一个小功能立即提交，避免大批量提交
- ✅ **持续测试**: 每次修改后立即测试，确保功能正常
- ✅ **快速反馈**: 发现问题立即修复，不积累技术债务

#### 3. 用户体验优先原则
- ✅ **界面一致性**: 所有页面保持统一的视觉风格和交互模式
- ✅ **响应速度**: 优化加载时间，提供流畅的用户体验
- ✅ **错误处理**: 提供友好的错误提示和恢复机制
- ✅ **无障碍设计**: 考虑不同用户的使用需求

### 日常开发习惯

#### 开发前准备
```bash
# 1. 检查项目状态
git status
git pull origin main

# 2. 启动开发环境
./start-all-dev.sh

# 3. 运行测试确保基础功能正常
cd admin && npm run test:quick
```

#### 开发过程中
- ✅ **实时保存**: 使用IDE自动保存功能，避免代码丢失
- ✅ **增量测试**: 每完成一个小功能立即测试
- ✅ **代码审查**: 提交前自己先审查一遍代码
- ✅ **注释及时**: 复杂逻辑立即添加注释说明

#### 🔥 强制热重载开发规范
- ✅ **端口冲突处理**: 一键启动时自动kill冲突进程，保障当前运行
- ✅ **强制热重载**: 启动后所有服务强制为热更新状态，代码修改无需重启
- ✅ **实时生效**: Django/Vue/Taro代码修改后自动重载，立即查看效果
- ✅ **开发效率**: 利用热重载快速迭代，避免频繁重启服务
- ✅ **状态保持**: 开发过程中保持服务运行状态，专注代码开发

#### 🎨 CSS样式修复规范
- ✅ **手动修复优先**: 禁止使用自动化脚本批量修复CSS，必须逐个手动修复
- ✅ **类名匹配检查**: 修改Vue组件时必须检查模板类名与CSS类名是否匹配
- ✅ **语法完整性**: 删除CSS代码时确保不留下孤立的代码片段
- ✅ **背景统一性**: 所有页面组件背景必须与全局背景保持一致
- ✅ **位移修复**: 使用fixed定位的组件必须添加background-attachment: fixed避免背景位移

#### 🚨 CSS修复禁止事项
```bash
# ❌ 禁止使用自动化CSS修复脚本
node scripts/fixCSS.js  # 禁止
node scripts/removeCSS.js  # 禁止

# ❌ 禁止批量替换CSS
sed -i 's/old/new/g' *.vue  # 禁止

# ✅ 正确做法：逐个手动检查和修复
# 1. 检查模板类名
# 2. 检查CSS定义
# 3. 手动修复不匹配
# 4. 测试修复效果
```

#### 开发完成后
```bash
# 1. 运行完整测试
npm run test:comprehensive

# 2. 检查代码质量
npm run lint

# 3. 提交代码
git add .
git commit -m "规范的提交信息"
git push origin main
```

### 问题解决习惯

#### 遇到问题时的标准流程
1. **🔍 先搜索**: 在项目文档和已有代码中搜索类似问题
2. **📝 记录问题**: 详细记录问题现象和复现步骤
3. **🧪 隔离测试**: 创建最小复现案例
4. **💡 寻求帮助**: 查看文档、搜索资料或询问团队
5. **📚 总结经验**: 解决后更新文档，避免重复问题

#### 调试习惯
```javascript
// 使用结构化的调试信息
console.log('🔍 调试信息:', {
  function: 'functionName',
  params: params,
  result: result,
  timestamp: new Date().toISOString()
})

// 使用条件断点
if (debugMode) {
  debugger;
}
```

---

## 🎨 UI设计规范

### 配色方案

#### 🌈 全局背景统一规范 (强制执行)
```css
/* ✅ 全局背景：梵高风格紫色渐变 (180度垂直渐变) */
body {
  background: linear-gradient(180deg,
    #2d1b69 0%,    /* 深紫色（梵高星夜风格） */
    #3730a3 15%,   /* 靛蓝紫 */
    #4338ca 30%,   /* 中紫色 */
    #5b21b6 45%,   /* 深紫色 */
    #6b21a8 60%,   /* 紫色 */
    #7c2d92 75%,   /* 紫红色 */
    #86198f 90%,   /* 深紫红 */
    #701a75 100%   /* 最深紫色 */
  ) !important;
}

/* ✅ 页面组件背景：与全局背景保持一致 */
.picasso-dashboard,
.picasso-system,
.picasso-therapists,
.picasso-customers,
.picasso-services,
.picasso-appointments,
.picasso-finance {
  /* 设置与全局背景相同的梵高风格紫色渐变 */
  background: linear-gradient(180deg,
    #2d1b69 0%, #3730a3 15%, #4338ca 30%, #5b21b6 45%,
    #6b21a8 60%, #7c2d92 75%, #86198f 90%, #701a75 100%
  ) !important;
  background-attachment: fixed; /* 固定背景，避免位移 */
}
```

#### 🚨 背景设置禁止事项
```css
/* ❌ 禁止在页面组件中设置不同的背景 */
.picasso-dashboard {
  background: linear-gradient(45deg, #8e44ad, #9b59b6); /* 禁止 */
}

/* ❌ 禁止使用动画背景（会导致不一致） */
.picasso-system {
  animation: picassoFlow 20s ease infinite; /* 禁止 */
}

/* ❌ 禁止不设置背景（会导致位移） */
.picasso-therapists {
  /* 不设置背景，使用全局背景 */ /* 禁止这种做法 */
}
```

#### 核心页面配色 (仪表盘/财务概览/系统管理)
```css
/* 四色配色方案 */
.core-color-red    { background: linear-gradient(45deg, #ff6b6b, #ff8e8e); }
.core-color-cyan   { background: linear-gradient(45deg, #4ecdc4, #45b7d1); }
.core-color-orange { background: linear-gradient(45deg, #f39c12, #e67e22); }
.core-color-green  { background: linear-gradient(45deg, #27ae60, #2ecc71); }
```

#### 管理页面配色 (预约/客户/技师/服务/财务记录)
```css
/* 表单元素配色 */
.search-fragment {
  background: linear-gradient(135deg, 
    rgba(255,182,193,0.8), /* 粉色 */
    rgba(255,218,185,0.7), /* 桃色 */
    rgba(255,255,255,0.9)  /* 白色 */
  );
}

.filter-fragment {
  background: linear-gradient(135deg, 
    rgba(173,216,230,0.8), /* 浅蓝 */
    rgba(135,206,235,0.7), /* 天蓝 */
    rgba(255,255,255,0.9)  /* 白色 */
  );
}

.table-header {
  background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1);
}
```

### 界面设计原则

#### 1. 简洁性原则
- ❌ **禁止重复标题**: 不在主内容区显示与侧边栏菜单重复的标题
- ✅ **清晰导航**: 用户只需关注侧边栏菜单进行导航
- ✅ **空间优化**: 为主要内容腾出更多显示空间

#### 2. 一致性原则
- ✅ **配色统一**: 同类功能页面使用相同配色方案
- ✅ **交互统一**: 相同操作在所有页面保持一致的视觉反馈
- ✅ **布局统一**: 相同类型的页面使用相同的布局结构

#### 3. 艺术性原则
- ✅ **毕加索风格**: 保持3D变换、倾斜、旋转等艺术效果
- ✅ **动画效果**: 保持浮动、脉冲、旋转等动态效果
- ✅ **渐变设计**: 使用多色渐变增强视觉层次

### 响应式设计

#### 移动端适配
```css
@media (max-width: 768px) {
  .action-toolbar { flex-direction: column; gap: 15px; }
  .filter-cubism { flex-direction: column; }
  .data-row { flex-direction: column; min-height: auto; }
  .title-layer { font-size: 2rem; }
}
```

---

## 💻 代码规范

### Vue组件规范

#### 文件命名
- 页面组件: `PascalCase.vue` (如: `AppointmentManagement.vue`)
- 通用组件: `PascalCase.vue` (如: `AdminLayout.vue`)
- 工具组件: `kebab-case.vue` (如: `data-table.vue`)

#### 组件结构
```vue
<template>
  <!-- HTML结构 -->
</template>

<script>
// JavaScript逻辑
</script>

<style scoped>
/* 组件样式 */
</style>
```

#### 🔍 CSS类名匹配检查规范 (强制执行)
```vue
<!-- ✅ 正确：模板类名与CSS定义匹配 -->
<template>
  <div class="picasso-system">
    <div class="tabs-cubism">
      <div class="tab-cube">
        <div class="tab-face">
          <!-- 内容 -->
        </div>
      </div>
    </div>
    <div class="content-cubism">
      <!-- 内容 -->
    </div>
  </div>
</template>

<style scoped>
.picasso-system { /* 与模板中的class="picasso-system"匹配 */ }
.tabs-cubism { /* 与模板中的class="tabs-cubism"匹配 */ }
.tab-cube { /* 与模板中的class="tab-cube"匹配 */ }
.tab-face { /* 与模板中的class="tab-face"匹配 */ }
.content-cubism { /* 与模板中的class="content-cubism"匹配 */ }
</style>
```

#### 🚨 类名匹配检查清单 (每次修改必检)
```bash
# 1. 检查主容器类名
grep -n "class=" ComponentName.vue | head -1
grep -n "^\." ComponentName.vue | head -1

# 2. 检查所有类名是否匹配
# 模板中的类名
grep -o 'class="[^"]*"' ComponentName.vue

# CSS中定义的类名
grep -o '^\.[a-zA-Z-]*' ComponentName.vue
```

#### CSS类命名规范
```css
/* 页面级别 */
.picasso-[page-name] { }

/* 功能模块 */
.[module]-cubism { }
.[module]-fragment { }

/* 状态类 */
.active, .inactive, .disabled { }

/* 动画类 */
@keyframes [name]Float { }
@keyframes [name]Pulse { }
```

### JavaScript规范

#### 变量命名
```javascript
// 响应式数据
const selectedKeys = ref([])
const isLoading = ref(false)

// 计算属性
const filteredData = computed(() => { })

// 方法
const handleSubmit = () => { }
const fetchData = async () => { }
```

#### 异步处理
```javascript
// 使用async/await
const fetchData = async () => {
  try {
    const response = await api.getData()
    return response.data
  } catch (error) {
    console.error('获取数据失败:', error)
    throw error
  }
}
```

### 代码质量保证

#### 代码审查清单
- ✅ **功能完整性**: 功能是否按需求正确实现
- ✅ **代码可读性**: 变量命名是否清晰，逻辑是否易懂
- ✅ **性能考虑**: 是否存在性能瓶颈或不必要的计算
- ✅ **错误处理**: 是否有完善的错误处理机制
- ✅ **安全性**: 是否存在安全漏洞或敏感信息泄露

#### 重构原则
```javascript
// 重构前 - 复杂的嵌套逻辑
if (user) {
  if (user.permissions) {
    if (user.permissions.includes('admin')) {
      // 执行管理员操作
    }
  }
}

// 重构后 - 清晰的早期返回
if (!user?.permissions?.includes('admin')) {
  return;
}
// 执行管理员操作
```

#### 性能优化习惯
```javascript
// 使用计算属性缓存复杂计算
const expensiveValue = computed(() => {
  return heavyCalculation(props.data)
})

// 使用防抖处理频繁操作
const debouncedSearch = debounce((query) => {
  performSearch(query)
}, 300)

// 使用虚拟滚动处理大量数据
const virtualizedList = ref(null)
```

---

## 🔄 Git工作流规范

### 仓库配置

#### 远程仓库
```bash
# 主仓库地址
origin: **************:OneBigMoon/wechatcloud.git

# SSH认证
ssh -T **************  # 验证连接
```

#### 分支策略
- **main分支**: 主开发分支，允许直接推送
- **feature分支**: 功能开发分支 (可选)
- **hotfix分支**: 紧急修复分支 (可选)

### 提交规范

#### 提交信息格式
```bash
git commit -m " [类型] 简短描述

✨ 新功能:
- 具体功能描述1
- 具体功能描述2

 修复问题:
- 修复内容描述1
- 修复内容描述2

 样式调整:
- UI/样式改进描述1
- UI/样式改进描述2

 技术改进:
- 代码优化描述1
- 代码优化描述2"
```

#### 提交类型
- ` ✨` : 新功能
- ` 🐛` : 修复bug
- ` 💄` : 样式调整
- ` ♻️` : 代码重构
- ` 📝` : 文档更新
- ` 🔧` : 配置修改

### 推送流程

#### 🚨 默认推送分支规范
```bash
# ✅ 强制要求：所有推送默认到main分支
git config --global push.default simple
git config --global init.defaultBranch main

# ✅ 标准推送命令（默认推送到main）
git push origin main
```

#### 标准推送
```bash
# 1. 添加文件
git add .

# 2. 提交修改
git commit -m "提交信息"

# 3. 推送到远程main分支（强制要求）
git push origin main
```

#### 强制推送 (覆盖远程)
```bash
# 当需要完全覆盖远程分支时
git push --force origin main
```

#### 🚨 禁止的推送操作
```bash
# ❌ 禁止推送到其他分支（除非明确需要）
git push origin develop  # 禁止
git push origin feature  # 禁止

# ❌ 禁止不指定分支的推送
git push  # 禁止，必须明确指定 origin main
```

### Git最佳实践

#### 提交频率和粒度
- ✅ **小而频繁**: 每完成一个小功能就提交，避免大批量提交
- ✅ **原子性提交**: 每次提交只包含一个逻辑变更
- ✅ **完整功能**: 确保每次提交后代码都能正常运行
- ✅ **及时推送**: 本地提交后及时推送到远程仓库

#### 分支管理策略
```bash
# 功能开发分支 (可选)
git checkout -b feature/new-feature
git commit -m "✨ 实现新功能"
git push origin feature/new-feature

# 合并到主分支
git checkout main
git merge feature/new-feature
git push origin main
git branch -d feature/new-feature
```

#### 代码冲突解决
```bash
# 1. 拉取最新代码
git pull origin main

# 2. 解决冲突后提交
git add .
git commit -m "🔧 解决合并冲突"

# 3. 推送解决后的代码
git push origin main
```

---

## 📁 项目结构规范

### 目录结构
```
wechatcloud/
├── admin/                 # 前端管理系统
│   ├── src/
│   │   ├── views/        # 页面组件
│   │   ├── components/   # 通用组件
│   │   ├── assets/       # 静态资源
│   │   └── styles/       # 全局样式
├── server/               # 后端服务
├── .github/              # GitHub配置
│   ├── CODEOWNERS       # 代码所有者
│   └── branch-protection.json
├── docs/                 # 项目文档
└── scripts/              # 脚本文件
```

### 文件组织原则
1. **功能分组**: 相关功能的文件放在同一目录
2. **层次清晰**: 目录层次不超过3层
3. **命名规范**: 使用有意义的文件和目录名
4. **避免重复**: 一个功能只有一个实现文件

### 启动脚本管理

#### 统一启动脚本
- ✅ **唯一入口**: 只使用 `start-all-dev.sh` 启动开发环境
- ✅ **自动化**: 脚本自动处理环境检查、依赖安装、服务启动
- ✅ **错误处理**: 脚本包含完善的错误处理和重试机制
- ✅ **监控功能**: 自动监控服务状态，异常时自动重启

#### 脚本维护原则
```bash
# 定期检查脚本有效性
bash -n start-all-dev.sh

# 测试脚本功能
./start-all-dev.sh --test-mode

# 更新脚本后立即测试
chmod +x start-all-dev.sh && ./start-all-dev.sh
```

---

## 🧪 测试规范

### 测试策略
1. **单元测试**: 测试独立的函数和组件
2. **集成测试**: 测试组件间的交互
3. **端到端测试**: 测试完整的用户流程
4. **视觉测试**: 验证UI设计的一致性

### 测试命名
```javascript
// 测试文件命名
ComponentName.test.js
utils.test.js
api.test.js

// 测试用例命名
describe('组件名称', () => {
  it('应该正确渲染', () => { })
  it('应该响应用户交互', () => { })
})
```

### 自动化测试习惯

#### 测试驱动开发 (TDD)
```javascript
// 1. 先写测试
describe('用户登录功能', () => {
  it('应该验证用户凭据', async () => {
    const result = await login('user', 'password')
    expect(result.success).toBe(true)
  })
})

// 2. 再实现功能
const login = async (username, password) => {
  // 实现登录逻辑
}

// 3. 重构优化
const login = async (username, password) => {
  // 优化后的登录逻辑
}
```

#### 测试覆盖率要求
- ✅ **核心功能**: 100% 测试覆盖率
- ✅ **UI组件**: 80% 以上测试覆盖率
- ✅ **工具函数**: 100% 测试覆盖率
- ✅ **API接口**: 90% 以上测试覆盖率

---

## 🚀 部署规范

### 部署前检查

#### 代码质量检查
```bash
# 1. 运行所有测试
npm run test:all

# 2. 检查代码规范
npm run lint

# 3. 构建项目
npm run build

# 4. 检查构建产物
ls -la dist/
```

#### 环境配置检查
- ✅ **环境变量**: 确认所有必要的环境变量已配置
- ✅ **API密钥**: 验证第三方服务API密钥有效性
- ✅ **数据库连接**: 确认数据库连接正常
- ✅ **静态资源**: 检查静态资源路径正确性

### 部署流程

#### 微信云托管部署
```bash
# 1. 检查后端文件变更
git diff --name-only HEAD~1 HEAD | grep "^server/"

# 2. 如有后端变更，自动触发部署
# 3. 前端变更不触发部署

# 4. 部署后验证
curl -f https://your-domain.com/health/
```

#### 部署回滚策略
```bash
# 1. 记录部署前版本
git tag -a v1.0.0 -m "部署前版本"

# 2. 部署失败时回滚
git checkout v1.0.0
git push --force origin main

# 3. 验证回滚成功
./start-all-dev.sh --verify
```

---

## 🤖 AI集成规范

### AI服务配置

#### 百度文心一言集成
```javascript
// API配置
const BAIDU_API_CONFIG = {
  apiKey: 'bce-v3/ALTAK-fpYd2rn5cS653qxilQ1fm/ec7c7b2079b7adeb584348a13fed4640d3b09f06',
  endpoint: 'https://aip.baidubce.com/rpc/2.0/ai_custom/v1/wenxinworkshop/text2img/sd_xl',
  timeout: 30000
}

// 使用示例
const generateServiceImage = async (serviceName) => {
  try {
    const response = await baiduAPI.generateImage({
      prompt: `中医推拿服务：${serviceName}，专业医疗环境`,
      style: 'realistic'
    })
    return response.data.image_url
  } catch (error) {
    console.error('AI图片生成失败:', error)
    return null
  }
}
```

#### DeepSeek AI集成
```javascript
// API配置
const DEEPSEEK_CONFIG = {
  apiKey: '***********************************',
  model: 'deepseek-chat',
  maxTokens: 1000
}

// 服务描述优化
const optimizeServiceDescription = async (originalDescription) => {
  try {
    const response = await deepseekAPI.chat({
      messages: [{
        role: 'user',
        content: `请优化以下中医推拿服务描述，使其更吸引客户：${originalDescription}`
      }]
    })
    return response.choices[0].message.content
  } catch (error) {
    console.error('AI描述优化失败:', error)
    return originalDescription
  }
}
```

### AI使用原则

#### 内容生成规范
- ✅ **精准匹配**: AI生成的内容必须与服务内容精确匹配
- ✅ **质量优先**: 优先使用高质量的AI生成内容，避免通用模板
- ✅ **人工审核**: AI生成内容需要人工审核后再使用
- ✅ **备选方案**: 为AI服务失败提供备选方案

#### 错误处理
```javascript
const handleAIError = (error, fallbackContent) => {
  console.error('AI服务错误:', error)

  // 记录错误日志
  logError('AI_SERVICE_ERROR', {
    error: error.message,
    timestamp: new Date().toISOString(),
    service: 'baidu_wenxin'
  })

  // 返回备选内容
  return fallbackContent || '暂无描述'
}
```

---

## ✅ 规范检查清单

### 开发习惯检查
- [ ] 开发前已整理现有代码
- [ ] 功能拆分为小的可测试单元
- [ ] 每个小功能完成后立即测试
- [ ] 提交前进行代码自审
- [ ] 遇到问题时先搜索已有解决方案

### UI设计检查
- [ ] 配色方案符合规范
- [ ] 无重复页面标题
- [ ] 响应式设计正常
- [ ] 动画效果流畅
- [ ] 界面一致性良好

### 代码质量检查
- [ ] 组件结构清晰
- [ ] 命名规范一致
- [ ] 无重复代码
- [ ] 错误处理完善
- [ ] 性能优化到位
- [ ] 安全性考虑充分

### Git工作流检查
- [ ] 提交信息规范
- [ ] 提交粒度合适
- [ ] 分支策略正确
- [ ] 推送流程顺畅
- [ ] 代码审查通过

### 测试检查
- [ ] 单元测试覆盖率达标
- [ ] 集成测试通过
- [ ] 端到端测试正常
- [ ] 性能测试满足要求

### 部署检查
- [ ] 环境配置正确
- [ ] 构建过程无错误
- [ ] 部署后功能验证通过
- [ ] 回滚方案准备就绪

### AI集成检查
- [ ] API密钥配置正确
- [ ] 生成内容质量良好
- [ ] 错误处理机制完善
- [ ] 备选方案可用

## 📚 开发资源

### 常用命令速查
```bash
# 项目启动
./start-all-dev.sh

# 测试命令
npm run test:quick          # 快速测试
npm run test:comprehensive  # 完整测试
npm run test:all           # 所有测试

# 代码检查
npm run lint               # 代码规范检查
npm run format            # 代码格式化

# Git操作
git status                # 检查状态
git add .                 # 添加所有文件
git commit -m "message"   # 提交
git push origin main      # 推送
```

### 故障排除指南
1. **端口被占用**: 脚本会自动处理，或手动 `lsof -ti:3000 | xargs kill -9`
2. **依赖安装失败**: 删除 `node_modules` 后重新 `npm install`
3. **Git推送失败**: 检查网络连接和认证配置
4. **测试失败**: 检查测试环境和数据准备
5. **AI服务异常**: 检查API密钥和网络连接

### 学习资源
- **Vue.js官方文档**: https://vuejs.org/
- **Ant Design Vue**: https://antdv.com/
- **Git最佳实践**: https://git-scm.com/book
- **JavaScript现代教程**: https://javascript.info/
- **CSS Grid布局**: https://css-tricks.com/snippets/css/complete-guide-grid/

## 🎯 规范执行

### 强制执行原则
- ⚠️ **每次开发必须遵守**: 所有开发活动都必须严格按照本规范执行
- ⚠️ **代码审查必检项**: 所有规范检查清单项目都是代码审查的必检项
- ⚠️ **违规必须修正**: 发现违反规范的代码必须立即修正
- ⚠️ **持续改进**: 根据实际使用情况持续完善规范内容

### 规范更新机制
1. **定期评估**: 每月评估规范的有效性和完整性
2. **问题反馈**: 发现规范问题时立即记录和讨论
3. **版本控制**: 规范变更需要版本控制和变更记录
4. **团队同步**: 规范更新后及时同步给所有团队成员

### 违规处理
- **轻微违规**: 立即修正，更新相关文档
- **重复违规**: 分析原因，完善规范或加强培训
- **严重违规**: 回滚代码，重新按规范实现

---

## 🚨 代码修复安全规范

### ⚠️ 重要警告：自动化修复工具的危险性
基于2025年7月10日的严重事故教训，制定以下强制性安全规范：

#### 🚫 严格禁止的行为
1. **禁止使用自制的正则表达式批量替换工具**
2. **禁止使用未经充分测试的自动化修复脚本**
3. **禁止在没有备份的情况下进行批量修复**

#### ✅ 强制要求的安全措施
1. **手动修复优先原则** - 确保精确性和安全性
2. **git保护机制** - 每次修复前必须提交当前状态
3. **逐步验证流程** - 逐个文件修复，立即验证

### 应急回退流程
```bash
# 发现问题立即回退
git reset --hard [安全提交点]
```

### 核心教训
- **慢即是快**: 手动修复虽慢但最安全
- **稳即是准**: 稳定的方法比快速的工具更可靠
- **备份为王**: git回退是最后的安全网

详细内容请参考: `admin/docs/LESSONS_LEARNED.md`

---

**📅 更新日期**: 2025-07-10
**👥 维护者**: OneBigMoon
**📧 联系方式**: 项目Issues
**🔄 版本**: v2.1 - 添加代码修复安全规范
