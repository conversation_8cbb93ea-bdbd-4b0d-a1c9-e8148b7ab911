#!/bin/bash
# Django启动脚本 - 微信云托管部署

echo "🚀 开始启动Django应用..."

# 运行数据库迁移
echo "📊 执行数据库迁移..."
python3 manage.py migrate --noinput

# 启动Django服务器（后台运行）
echo "🌐 启动Django服务器..."
python3 manage.py runserver 0.0.0.0:80 &
DJANGO_PID=$!

# 等待Django启动
echo "⏳ 等待Django启动..."
sleep 10

# 检查Django是否启动成功
if curl -f http://localhost:80/health/ > /dev/null 2>&1; then
    echo "✅ Django启动成功"

    # 执行部署后钩子（递增版本号）
    echo "🔄 执行部署后钩子..."
    python3 post_deploy.py

    echo "🎉 部署完成！"
else
    echo "❌ Django启动失败"
    exit 1
fi

# 等待Django进程
wait $DJANGO_PID
