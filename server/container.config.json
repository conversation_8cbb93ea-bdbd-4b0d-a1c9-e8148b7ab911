{
	// 本配置文件仅配合模板部署使用，为模板部署的服务生成「服务设置」的初始值。
	// 模板部署结束后，后续服务发布与本配置文件完全无关，修改「服务设置」请到控制台操作。
	// 复制模板代码自行开发请忽略本配置文件。
	
	"containerPort": 80,                           
	"minNum": 0,                                  
	"maxNum": 5,                                  
	"cpu": 1,                                    
	"mem": 2,                                    
	"policyType": "cpu",                           
	"policyThreshold": 60,
	"policyDetails": [
		{
			"PolicyType": "cpu",
			"PolicyThreshold": 60
		},
		{
			"PolicyType": "mem",
			"PolicyThreshold": 60
		}
	],
	"envParams": {},                               
	"customLogs": "stdout",                         
	"initialDelaySeconds": 2,                       
	"dataBaseName":"django_demo",
	"executeSQLs":[
		"CREATE DATABASE IF NOT EXISTS django_demo;",
		"USE django_demo;",
		"CREATE TABLE IF NOT EXISTS `Counters` (`id` int(11) NOT NULL AUTO_INCREMENT, `count` int(11) NOT NULL DEFAULT 1, `createdAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP, `updatedAt` timestamp NOT NULL DEFAULT CURRENT_TIMESTAMP, PRIMARY KEY (`id`)) ENGINE = InnoDB DEFAULT CHARSET = utf8;"
	]    
}
