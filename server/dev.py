#!/usr/bin/env python3
"""
Django本地开发环境快速启动脚本
"""

import os
import sys
import subprocess
from pathlib import Path

def main():
    """快速启动Django开发环境"""
    print("🚀 启动Django本地开发环境...")
    
    # 确保在正确目录
    script_dir = Path(__file__).parent
    os.chdir(script_dir)
    
    # 检查数据库
    if not Path("db.sqlite3").exists():
        print("📝 初始化数据库...")
        subprocess.run([sys.executable, "manage.py", "migrate"])
        
        # 提示创建超级用户
        print("\n👤 建议创建管理员账户:")
        print("   python manage.py createsuperuser")
    
    # 启动服务器
    print("\n🌐 Django服务器启动信息:")
    print("   管理工具: http://127.0.0.1:8000/")
    print("   API接口:  http://127.0.0.1:8000/api/")
    print("   管理后台: http://127.0.0.1:8000/admin/")
    print("   健康检查: http://127.0.0.1:8000/health/")
    print("\n按 Ctrl+C 停止服务器\n")
    
    try:
        subprocess.run([sys.executable, "manage.py", "runserver", "0.0.0.0:8000"])
    except KeyboardInterrupt:
        print("\n👋 Django服务器已停止")

if __name__ == "__main__":
    main()
