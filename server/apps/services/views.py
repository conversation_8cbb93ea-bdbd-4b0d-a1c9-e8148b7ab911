"""
服务管理视图
基于admin原型设计实现
"""

from django.db.models import Q
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django_filters.rest_framework import DjangoFilterBackend

from .models import ServiceCategory, Service, ServiceTag
from .serializers import (
    ServiceCategorySerializer, ServiceListSerializer, ServiceDetailSerializer,
    ServiceCreateUpdateSerializer, ServiceTagSerializer, ServiceExportSerializer,
    ServiceBatchUpdateSerializer
)


class ServiceCategoryViewSet(viewsets.ModelViewSet):
    """服务分类管理"""
    queryset = ServiceCategory.objects.all()
    serializer_class = ServiceCategorySerializer
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()
        
        # 根据状态筛选
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        return queryset


class ServiceViewSet(viewsets.ModelViewSet):
    """服务管理 - 基于admin原型实现"""
    queryset = Service.objects.select_related('category').all()
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['category', 'is_active', 'is_featured']
    search_fields = ['name', 'description']
    ordering_fields = ['created_at', 'updated_at', 'price', 'sold_count', 'sort_order']
    ordering = ['sort_order', 'id']
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'list':
            return ServiceListSerializer
        elif self.action in ['create', 'update', 'partial_update']:
            return ServiceCreateUpdateSerializer
        elif self.action == 'export':
            return ServiceExportSerializer
        elif self.action == 'batch_update':
            return ServiceBatchUpdateSerializer
        else:
            return ServiceDetailSerializer
    
    def get_queryset(self):
        """获取查询集 - 支持admin原型中的筛选功能"""
        queryset = super().get_queryset()
        
        # 分类筛选
        category_id = self.request.query_params.get('category_id')
        if category_id:
            queryset = queryset.filter(category_id=category_id)
        
        # 状态筛选
        is_active = self.request.query_params.get('is_active')
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        # 推荐筛选
        is_featured = self.request.query_params.get('is_featured')
        if is_featured is not None:
            queryset = queryset.filter(is_featured=is_featured.lower() == 'true')
        
        # 价格范围筛选
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')
        if min_price:
            queryset = queryset.filter(price__gte=min_price)
        if max_price:
            queryset = queryset.filter(price__lte=max_price)
        
        return queryset
    
    def list(self, request, *args, **kwargs):
        """服务列表 - 支持分页和筛选"""
        queryset = self.filter_queryset(self.get_queryset())
        
        # 分页
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = self.get_serializer(queryset, many=True)
        return Response({
            'code': 0,
            'message': '获取成功',
            'data': {
                'list': serializer.data,
                'total': queryset.count()
            }
        })
    
    def create(self, request, *args, **kwargs):
        """创建服务"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        service = serializer.save()
        
        # 返回详情数据
        detail_serializer = ServiceDetailSerializer(service)
        return Response({
            'code': 0,
            'message': '创建成功',
            'data': detail_serializer.data
        }, status=status.HTTP_201_CREATED)
    
    def update(self, request, *args, **kwargs):
        """更新服务"""
        partial = kwargs.pop('partial', False)
        instance = self.get_object()
        serializer = self.get_serializer(instance, data=request.data, partial=partial)
        serializer.is_valid(raise_exception=True)
        service = serializer.save()
        
        # 返回详情数据
        detail_serializer = ServiceDetailSerializer(service)
        return Response({
            'code': 0,
            'message': '更新成功',
            'data': detail_serializer.data
        })
    
    def destroy(self, request, *args, **kwargs):
        """删除服务"""
        instance = self.get_object()
        
        # 检查是否有关联的预约
        if hasattr(instance, 'appointment_set') and instance.appointment_set.exists():
            return Response({
                'code': -1,
                'message': '该服务存在关联预约，无法删除'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        instance.delete()
        return Response({
            'code': 0,
            'message': '删除成功'
        })
    
    @action(detail=False, methods=['post'])
    def batch_update(self, request):
        """批量更新服务"""
        serializer = ServiceBatchUpdateSerializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        service_ids = serializer.validated_data['service_ids']
        action_type = serializer.validated_data['action']
        
        services = Service.objects.filter(id__in=service_ids)
        
        if action_type == 'activate':
            services.update(is_active=True)
            message = '批量启用成功'
        elif action_type == 'deactivate':
            services.update(is_active=False)
            message = '批量禁用成功'
        elif action_type == 'feature':
            services.update(is_featured=True)
            message = '批量设为推荐成功'
        elif action_type == 'unfeature':
            services.update(is_featured=False)
            message = '批量取消推荐成功'
        elif action_type == 'delete':
            # 检查是否有关联预约
            services_with_appointments = services.filter(appointment__isnull=False).distinct()
            if services_with_appointments.exists():
                return Response({
                    'code': -1,
                    'message': '部分服务存在关联预约，无法删除'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            services.delete()
            message = '批量删除成功'
        
        return Response({
            'code': 0,
            'message': message,
            'data': {'affected_count': len(service_ids)}
        })
    
    @action(detail=False, methods=['get'])
    def export(self, request):
        """导出服务数据"""
        queryset = self.filter_queryset(self.get_queryset())
        serializer = ServiceExportSerializer(queryset, many=True)
        
        return Response({
            'code': 0,
            'message': '导出成功',
            'data': serializer.data
        })
    
    @action(detail=True, methods=['post'])
    def toggle_status(self, request, pk=None):
        """切换服务状态"""
        service = self.get_object()
        service.is_active = not service.is_active
        service.save()
        
        status_text = '启用' if service.is_active else '禁用'
        return Response({
            'code': 0,
            'message': f'服务已{status_text}',
            'data': {'is_active': service.is_active}
        })
    
    @action(detail=True, methods=['post'])
    def toggle_featured(self, request, pk=None):
        """切换推荐状态"""
        service = self.get_object()
        service.is_featured = not service.is_featured
        service.save()
        
        featured_text = '推荐' if service.is_featured else '取消推荐'
        return Response({
            'code': 0,
            'message': f'服务已{featured_text}',
            'data': {'is_featured': service.is_featured}
        })


class ServiceTagViewSet(viewsets.ModelViewSet):
    """服务标签管理"""
    queryset = ServiceTag.objects.all()
    serializer_class = ServiceTagSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [filters.SearchFilter, filters.OrderingFilter]
    search_fields = ['name']
    ordering = ['name']
