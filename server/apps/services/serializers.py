"""
服务管理序列化器
基于admin原型设计实现
"""

from rest_framework import serializers
from .models import ServiceCategory, Service, ServiceImage, ServiceTag, ServiceTagRelation


class ServiceCategorySerializer(serializers.ModelSerializer):
    """服务分类序列化器"""
    
    class Meta:
        model = ServiceCategory
        fields = [
            'id', 'name', 'description', 'sort_order', 
            'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class ServiceImageSerializer(serializers.ModelSerializer):
    """服务图片序列化器"""
    
    class Meta:
        model = ServiceImage
        fields = ['id', 'image', 'alt_text', 'sort_order', 'created_at']
        read_only_fields = ['id', 'created_at']


class ServiceTagSerializer(serializers.ModelSerializer):
    """服务标签序列化器"""
    
    class Meta:
        model = ServiceTag
        fields = ['id', 'name', 'color', 'created_at']
        read_only_fields = ['id', 'created_at']


class ServiceListSerializer(serializers.ModelSerializer):
    """服务列表序列化器 - 用于列表展示"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    category_id = serializers.IntegerField(source='category.id', read_only=True)
    
    class Meta:
        model = Service
        fields = [
            'id', 'name', 'category_id', 'category_name', 'description',
            'price', 'duration', 'image', 'is_active', 'is_featured',
            'sort_order', 'sold_count', 'view_count', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'sold_count', 'view_count', 'created_at', 'updated_at']


class ServiceDetailSerializer(serializers.ModelSerializer):
    """服务详情序列化器 - 用于详情展示和编辑"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    images = ServiceImageSerializer(many=True, read_only=True)
    tags = serializers.SerializerMethodField()
    
    class Meta:
        model = Service
        fields = [
            'id', 'name', 'category', 'category_name', 'description',
            'price', 'duration', 'image', 'is_active', 'is_featured',
            'sort_order', 'sold_count', 'view_count', 'images', 'tags',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'sold_count', 'view_count', 'created_at', 'updated_at']
    
    def get_tags(self, obj):
        """获取服务标签"""
        tag_relations = obj.tag_relations.select_related('tag').all()
        return [
            {
                'id': relation.tag.id,
                'name': relation.tag.name,
                'color': relation.tag.color
            }
            for relation in tag_relations
        ]


class ServiceCreateUpdateSerializer(serializers.ModelSerializer):
    """服务创建/更新序列化器"""
    tag_ids = serializers.ListField(
        child=serializers.IntegerField(),
        write_only=True,
        required=False,
        help_text="标签ID列表"
    )
    
    class Meta:
        model = Service
        fields = [
            'name', 'category', 'description', 'price', 'duration',
            'image', 'is_active', 'is_featured', 'sort_order', 'tag_ids'
        ]
    
    def validate_price(self, value):
        """验证价格"""
        if value <= 0:
            raise serializers.ValidationError("价格必须大于0")
        return value
    
    def validate_duration(self, value):
        """验证服务时长"""
        if value <= 0:
            raise serializers.ValidationError("服务时长必须大于0分钟")
        if value > 480:  # 8小时
            raise serializers.ValidationError("服务时长不能超过8小时")
        return value
    
    def create(self, validated_data):
        """创建服务"""
        tag_ids = validated_data.pop('tag_ids', [])
        service = Service.objects.create(**validated_data)
        
        # 处理标签关联
        if tag_ids:
            self._update_service_tags(service, tag_ids)
        
        return service
    
    def update(self, instance, validated_data):
        """更新服务"""
        tag_ids = validated_data.pop('tag_ids', None)
        
        # 更新基本字段
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        
        # 处理标签关联
        if tag_ids is not None:
            self._update_service_tags(instance, tag_ids)
        
        return instance
    
    def _update_service_tags(self, service, tag_ids):
        """更新服务标签关联"""
        # 删除现有关联
        ServiceTagRelation.objects.filter(service=service).delete()
        
        # 创建新关联
        if tag_ids:
            tag_relations = [
                ServiceTagRelation(service=service, tag_id=tag_id)
                for tag_id in tag_ids
                if ServiceTag.objects.filter(id=tag_id).exists()
            ]
            ServiceTagRelation.objects.bulk_create(tag_relations)


class ServiceExportSerializer(serializers.ModelSerializer):
    """服务导出序列化器 - 用于数据导出"""
    category_name = serializers.CharField(source='category.name', read_only=True)
    status = serializers.SerializerMethodField()
    
    class Meta:
        model = Service
        fields = [
            'id', 'name', 'category_name', 'description', 'price',
            'duration', 'status', 'sold_count', 'view_count',
            'created_at', 'updated_at'
        ]
    
    def get_status(self, obj):
        """获取状态文本"""
        return "启用" if obj.is_active else "禁用"


class ServiceBatchUpdateSerializer(serializers.Serializer):
    """服务批量更新序列化器"""
    service_ids = serializers.ListField(
        child=serializers.IntegerField(),
        help_text="服务ID列表"
    )
    action = serializers.ChoiceField(
        choices=[
            ('activate', '启用'),
            ('deactivate', '禁用'),
            ('feature', '设为推荐'),
            ('unfeature', '取消推荐'),
            ('delete', '删除')
        ],
        help_text="批量操作类型"
    )
    
    def validate_service_ids(self, value):
        """验证服务ID列表"""
        if not value:
            raise serializers.ValidationError("服务ID列表不能为空")
        
        # 检查服务是否存在
        existing_ids = set(Service.objects.filter(id__in=value).values_list('id', flat=True))
        invalid_ids = set(value) - existing_ids
        
        if invalid_ids:
            raise serializers.ValidationError(f"以下服务ID不存在: {list(invalid_ids)}")
        
        return value
