"""
服务管理模型
基于admin原型设计实现
"""

from django.db import models


class ServiceCategory(models.Model):
    """服务分类模型"""
    name = models.CharField(max_length=50, verbose_name='分类名称')
    description = models.TextField(blank=True, verbose_name='分类描述')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'service_categories'
        verbose_name = '服务分类'
        verbose_name_plural = '服务分类'
        ordering = ['sort_order', 'id']


class Service(models.Model):
    """服务项目模型 - 基于admin原型设计"""
    name = models.CharField(max_length=100, verbose_name='服务名称')
    category = models.ForeignKey(
        ServiceCategory, 
        on_delete=models.CASCADE, 
        verbose_name='服务分类'
    )
    description = models.TextField(verbose_name='服务描述')
    price = models.DecimalField(
        max_digits=10, 
        decimal_places=2, 
        verbose_name='价格'
    )
    duration = models.IntegerField(verbose_name='服务时长(分钟)')
    image = models.URLField(blank=True, verbose_name='服务图片')
    
    # admin原型中的字段
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    is_featured = models.BooleanField(default=False, verbose_name='是否推荐')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    
    # 统计字段
    sold_count = models.IntegerField(default=0, verbose_name='销售数量')
    view_count = models.IntegerField(default=0, verbose_name='浏览次数')
    
    # 时间字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'services'
        verbose_name = '服务项目'
        verbose_name_plural = '服务项目'
        ordering = ['sort_order', 'id']


class ServiceImage(models.Model):
    """服务图片模型"""
    service = models.ForeignKey(
        Service, 
        on_delete=models.CASCADE, 
        related_name='images',
        verbose_name='服务项目'
    )
    image = models.URLField(verbose_name='图片URL')
    alt_text = models.CharField(max_length=200, blank=True, verbose_name='图片描述')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    def __str__(self):
        return f"{self.service.name} - 图片{self.id}"

    class Meta:
        db_table = 'service_images'
        verbose_name = '服务图片'
        verbose_name_plural = '服务图片'
        ordering = ['sort_order', 'id']


class ServiceTag(models.Model):
    """服务标签模型"""
    name = models.CharField(max_length=30, unique=True, verbose_name='标签名称')
    color = models.CharField(max_length=7, default='#1890ff', verbose_name='标签颜色')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'service_tags'
        verbose_name = '服务标签'
        verbose_name_plural = '服务标签'
        ordering = ['name']


class ServiceTagRelation(models.Model):
    """服务标签关联模型"""
    service = models.ForeignKey(
        Service, 
        on_delete=models.CASCADE,
        related_name='tag_relations',
        verbose_name='服务项目'
    )
    tag = models.ForeignKey(
        ServiceTag, 
        on_delete=models.CASCADE,
        verbose_name='标签'
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    class Meta:
        db_table = 'service_tag_relations'
        verbose_name = '服务标签关联'
        verbose_name_plural = '服务标签关联'
        unique_together = ['service', 'tag']
