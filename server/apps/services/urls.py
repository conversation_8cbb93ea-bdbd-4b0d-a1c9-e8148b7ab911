"""
服务管理URL配置
基于admin原型设计实现
"""

from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import ServiceCategoryViewSet, ServiceViewSet, ServiceTagViewSet

# 创建路由器
router = DefaultRouter()
router.register(r'categories', ServiceCategoryViewSet, basename='service-category')
router.register(r'services', ServiceViewSet, basename='service')
router.register(r'tags', ServiceTagViewSet, basename='service-tag')

app_name = 'services'

urlpatterns = [
    path('', include(router.urls)),
]
