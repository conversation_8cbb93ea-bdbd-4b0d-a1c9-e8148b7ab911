"""
预约管理模型
基于admin原型设计实现
"""

from django.db import models
from django.core.validators import RegexValidator


class Customer(models.Model):
    """客户模型"""
    name = models.CharField(max_length=50, verbose_name='客户姓名')
    phone = models.CharField(
        max_length=11,
        unique=True,
        validators=[RegexValidator(r'^1[3-9]\d{9}$', '手机号格式不正确')],
        verbose_name='手机号'
    )
    avatar = models.URLField(blank=True, verbose_name='头像')
    gender = models.CharField(
        max_length=10, 
        choices=[('male', '男'), ('female', '女'), ('unknown', '未知')], 
        default='unknown',
        verbose_name='性别'
    )
    birth_date = models.DateField(null=True, blank=True, verbose_name='出生日期')
    address = models.TextField(blank=True, verbose_name='地址')
    notes = models.TextField(blank=True, verbose_name='备注')
    
    # 统计字段
    total_spent = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='总消费')
    visit_count = models.IntegerField(default=0, verbose_name='到店次数')
    last_visit = models.DateTimeField(null=True, blank=True, verbose_name='最后到店时间')
    
    # VIP相关
    is_vip = models.BooleanField(default=False, verbose_name='是否VIP')
    vip_expire_date = models.DateField(null=True, blank=True, verbose_name='VIP到期日期')
    
    # 时间字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return f"{self.name} ({self.phone})"

    class Meta:
        db_table = 'customers'
        verbose_name = '客户'
        verbose_name_plural = '客户'
        ordering = ['-created_at']


class Therapist(models.Model):
    """技师模型"""
    LEVEL_CHOICES = [
        (1, '一星技师'),
        (2, '二星技师'),
        (3, '三星技师'),
        (4, '四星技师'),
        (5, '五星技师'),
    ]
    
    name = models.CharField(max_length=50, verbose_name='技师姓名')
    phone = models.CharField(max_length=20, verbose_name='联系电话')
    avatar = models.URLField(blank=True, verbose_name='头像')
    level = models.IntegerField(choices=LEVEL_CHOICES, default=1, verbose_name='技师等级')
    specialty = models.TextField(verbose_name='专长描述')
    experience = models.IntegerField(verbose_name='从业年限')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='服务价格')
    is_active = models.BooleanField(default=True, verbose_name='是否在职')
    
    # 统计字段
    service_count = models.IntegerField(default=0, verbose_name='服务次数')
    rating = models.DecimalField(max_digits=3, decimal_places=2, default=5.0, verbose_name='评分')
    
    # 时间字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'therapists'
        verbose_name = '技师'
        verbose_name_plural = '技师'
        ordering = ['-level', 'id']


class Appointment(models.Model):
    """预约模型 - 基于admin原型设计"""
    STATUS_CHOICES = [
        ('pending', '待确认'),
        ('confirmed', '已确认'),
        ('in_progress', '服务中'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
        ('no_show', '未到店'),
    ]
    
    # 关联字段
    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name='客户')
    service = models.ForeignKey('services.Service', on_delete=models.CASCADE, verbose_name='服务项目')
    therapist = models.ForeignKey(Therapist, on_delete=models.CASCADE, verbose_name='技师')
    
    # 预约信息
    appointment_time = models.DateTimeField(verbose_name='预约时间')
    duration = models.IntegerField(verbose_name='服务时长(分钟)')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='状态')
    notes = models.TextField(blank=True, verbose_name='备注')
    
    # 价格信息
    original_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='原价')
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='优惠金额')
    final_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='实付金额')
    
    # 实际服务时间
    actual_start_time = models.DateTimeField(null=True, blank=True, verbose_name='实际开始时间')
    actual_end_time = models.DateTimeField(null=True, blank=True, verbose_name='实际结束时间')
    
    # 操作记录
    confirmed_at = models.DateTimeField(null=True, blank=True, verbose_name='确认时间')
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name='完成时间')
    cancelled_at = models.DateTimeField(null=True, blank=True, verbose_name='取消时间')
    cancel_reason = models.TextField(blank=True, verbose_name='取消原因')
    
    # 时间字段
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return f"{self.customer.name} - {self.service.name} - {self.appointment_time}"

    class Meta:
        db_table = 'appointments'
        verbose_name = '预约'
        verbose_name_plural = '预约'
        ordering = ['-appointment_time']


class AppointmentStatusLog(models.Model):
    """预约状态变更日志"""
    appointment = models.ForeignKey(
        Appointment, 
        on_delete=models.CASCADE,
        related_name='status_logs',
        verbose_name='预约'
    )
    from_status = models.CharField(max_length=20, verbose_name='原状态')
    to_status = models.CharField(max_length=20, verbose_name='新状态')
    reason = models.TextField(blank=True, verbose_name='变更原因')
    operator = models.CharField(max_length=50, verbose_name='操作人')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='变更时间')

    def __str__(self):
        return f"{self.appointment.id} - {self.from_status} -> {self.to_status}"

    class Meta:
        db_table = 'appointment_status_logs'
        verbose_name = '预约状态日志'
        verbose_name_plural = '预约状态日志'
        ordering = ['-created_at']


class AppointmentReview(models.Model):
    """预约评价模型"""
    appointment = models.OneToOneField(
        Appointment, 
        on_delete=models.CASCADE,
        related_name='review',
        verbose_name='预约'
    )
    
    # 评分字段
    overall_rating = models.IntegerField(
        choices=[(i, f'{i}星') for i in range(1, 6)], 
        verbose_name='总体评分'
    )
    service_rating = models.IntegerField(
        choices=[(i, f'{i}星') for i in range(1, 6)], 
        verbose_name='服务评分'
    )
    therapist_rating = models.IntegerField(
        choices=[(i, f'{i}星') for i in range(1, 6)], 
        verbose_name='技师评分'
    )
    environment_rating = models.IntegerField(
        choices=[(i, f'{i}星') for i in range(1, 6)], 
        verbose_name='环境评分'
    )
    
    # 评价内容
    comment = models.TextField(blank=True, verbose_name='评价内容')
    images = models.JSONField(default=list, blank=True, verbose_name='评价图片')
    
    # 回复
    reply = models.TextField(blank=True, verbose_name='商家回复')
    reply_at = models.DateTimeField(null=True, blank=True, verbose_name='回复时间')
    
    # 状态
    is_anonymous = models.BooleanField(default=False, verbose_name='是否匿名')
    is_published = models.BooleanField(default=True, verbose_name='是否公开')
    
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    def __str__(self):
        return f"{self.appointment.customer.name} - {self.overall_rating}星"

    class Meta:
        db_table = 'appointment_reviews'
        verbose_name = '预约评价'
        verbose_name_plural = '预约评价'
        ordering = ['-created_at']
