"""
预约管理序列化器
基于admin原型设计实现
"""

from rest_framework import serializers
from django.utils import timezone
from .models import Customer, Therapist, Appointment, AppointmentStatusLog, AppointmentReview


class CustomerSerializer(serializers.ModelSerializer):
    """客户序列化器"""
    age = serializers.SerializerMethodField()
    
    class Meta:
        model = Customer
        fields = [
            'id', 'name', 'phone', 'avatar', 'gender', 'birth_date', 'age',
            'address', 'notes', 'total_spent', 'visit_count', 'last_visit',
            'is_vip', 'vip_expire_date', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'total_spent', 'visit_count', 'last_visit', 'created_at', 'updated_at']
    
    def get_age(self, obj):
        """计算年龄"""
        if obj.birth_date:
            today = timezone.now().date()
            return today.year - obj.birth_date.year - ((today.month, today.day) < (obj.birth_date.month, obj.birth_date.day))
        return None
    
    def validate_phone(self, value):
        """验证手机号唯一性"""
        if self.instance and self.instance.phone == value:
            return value
        
        if Customer.objects.filter(phone=value).exists():
            raise serializers.ValidationError("该手机号已被注册")
        return value


class TherapistSerializer(serializers.ModelSerializer):
    """技师序列化器"""
    level_display = serializers.CharField(source='get_level_display', read_only=True)
    
    class Meta:
        model = Therapist
        fields = [
            'id', 'name', 'phone', 'avatar', 'level', 'level_display',
            'specialty', 'experience', 'price', 'is_active',
            'service_count', 'rating', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'service_count', 'rating', 'created_at', 'updated_at']


class AppointmentListSerializer(serializers.ModelSerializer):
    """预约列表序列化器 - 用于admin列表展示"""
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    customer_phone = serializers.CharField(source='customer.phone', read_only=True)
    service_name = serializers.CharField(source='service.name', read_only=True)
    therapist_name = serializers.CharField(source='therapist.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    
    class Meta:
        model = Appointment
        fields = [
            'id', 'customer_name', 'customer_phone', 'service_name', 'therapist_name',
            'appointment_time', 'duration', 'status', 'status_display',
            'final_price', 'notes', 'created_at'
        ]


class AppointmentDetailSerializer(serializers.ModelSerializer):
    """预约详情序列化器"""
    customer = CustomerSerializer(read_only=True)
    therapist = TherapistSerializer(read_only=True)
    service = serializers.SerializerMethodField()
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    status_logs = serializers.SerializerMethodField()
    
    class Meta:
        model = Appointment
        fields = [
            'id', 'customer', 'service', 'therapist', 'appointment_time',
            'duration', 'status', 'status_display', 'notes',
            'original_price', 'discount_amount', 'final_price',
            'actual_start_time', 'actual_end_time',
            'confirmed_at', 'completed_at', 'cancelled_at', 'cancel_reason',
            'status_logs', 'created_at', 'updated_at'
        ]
    
    def get_service(self, obj):
        """获取服务信息"""
        return {
            'id': obj.service.id,
            'name': obj.service.name,
            'category': obj.service.category.name,
            'price': obj.service.price,
            'duration': obj.service.duration,
            'image': obj.service.image
        }
    
    def get_status_logs(self, obj):
        """获取状态变更日志"""
        logs = obj.status_logs.all()[:5]  # 最近5条记录
        return [
            {
                'from_status': log.from_status,
                'to_status': log.to_status,
                'reason': log.reason,
                'operator': log.operator,
                'created_at': log.created_at
            }
            for log in logs
        ]


class AppointmentCreateSerializer(serializers.ModelSerializer):
    """预约创建序列化器"""
    
    class Meta:
        model = Appointment
        fields = [
            'customer', 'service', 'therapist', 'appointment_time',
            'duration', 'notes', 'original_price', 'discount_amount', 'final_price'
        ]
    
    def validate_appointment_time(self, value):
        """验证预约时间"""
        if value <= timezone.now():
            raise serializers.ValidationError("预约时间不能早于当前时间")
        return value
    
    def validate(self, attrs):
        """验证预约数据"""
        appointment_time = attrs['appointment_time']
        therapist = attrs['therapist']
        duration = attrs['duration']
        
        # 检查技师是否在职
        if not therapist.is_active:
            raise serializers.ValidationError("该技师当前不在职")
        
        # 检查时间冲突
        end_time = appointment_time + timezone.timedelta(minutes=duration)
        conflicting_appointments = Appointment.objects.filter(
            therapist=therapist,
            status__in=['pending', 'confirmed', 'in_progress'],
            appointment_time__lt=end_time,
            appointment_time__gte=appointment_time - timezone.timedelta(minutes=duration)
        )
        
        if self.instance:
            conflicting_appointments = conflicting_appointments.exclude(id=self.instance.id)
        
        if conflicting_appointments.exists():
            raise serializers.ValidationError("该时间段技师已有预约")
        
        # 验证价格
        if attrs['final_price'] > attrs['original_price']:
            raise serializers.ValidationError("实付金额不能大于原价")
        
        if attrs['discount_amount'] < 0:
            raise serializers.ValidationError("优惠金额不能为负数")
        
        return attrs


class AppointmentUpdateSerializer(serializers.ModelSerializer):
    """预约更新序列化器"""
    
    class Meta:
        model = Appointment
        fields = [
            'appointment_time', 'duration', 'notes',
            'original_price', 'discount_amount', 'final_price'
        ]
    
    def validate_appointment_time(self, value):
        """验证预约时间"""
        if self.instance.status in ['completed', 'cancelled']:
            raise serializers.ValidationError("已完成或已取消的预约不能修改时间")
        
        if value <= timezone.now():
            raise serializers.ValidationError("预约时间不能早于当前时间")
        
        return value


class AppointmentStatusUpdateSerializer(serializers.Serializer):
    """预约状态更新序列化器"""
    status = serializers.ChoiceField(choices=Appointment.STATUS_CHOICES)
    reason = serializers.CharField(max_length=500, required=False, help_text="状态变更原因")
    operator = serializers.CharField(max_length=50, help_text="操作人")
    
    def validate(self, attrs):
        """验证状态变更"""
        appointment = self.context['appointment']
        new_status = attrs['status']
        current_status = appointment.status
        
        # 定义允许的状态转换
        allowed_transitions = {
            'pending': ['confirmed', 'cancelled'],
            'confirmed': ['in_progress', 'completed', 'cancelled', 'no_show'],
            'in_progress': ['completed'],
            'completed': [],  # 已完成不能再变更
            'cancelled': [],  # 已取消不能再变更
            'no_show': []     # 未到店不能再变更
        }
        
        if new_status not in allowed_transitions.get(current_status, []):
            raise serializers.ValidationError(f"不能从{current_status}状态变更为{new_status}状态")
        
        return attrs


class AppointmentReviewSerializer(serializers.ModelSerializer):
    """预约评价序列化器"""
    customer_name = serializers.CharField(source='appointment.customer.name', read_only=True)
    service_name = serializers.CharField(source='appointment.service.name', read_only=True)
    therapist_name = serializers.CharField(source='appointment.therapist.name', read_only=True)
    
    class Meta:
        model = AppointmentReview
        fields = [
            'id', 'appointment', 'customer_name', 'service_name', 'therapist_name',
            'overall_rating', 'service_rating', 'therapist_rating', 'environment_rating',
            'comment', 'images', 'reply', 'reply_at',
            'is_anonymous', 'is_published', 'created_at'
        ]
        read_only_fields = ['id', 'reply_at', 'created_at']
    
    def validate_appointment(self, value):
        """验证预约状态"""
        if value.status != 'completed':
            raise serializers.ValidationError("只有已完成的预约才能评价")
        
        if hasattr(value, 'review'):
            raise serializers.ValidationError("该预约已经评价过了")
        
        return value


class AppointmentFilterSerializer(serializers.Serializer):
    """预约筛选序列化器 - 用于admin筛选功能"""
    status = serializers.ChoiceField(choices=Appointment.STATUS_CHOICES, required=False)
    customer_name = serializers.CharField(max_length=50, required=False)
    therapist_id = serializers.IntegerField(required=False)
    service_id = serializers.IntegerField(required=False)
    date_from = serializers.DateField(required=False)
    date_to = serializers.DateField(required=False)
    
    def validate(self, attrs):
        """验证筛选条件"""
        date_from = attrs.get('date_from')
        date_to = attrs.get('date_to')
        
        if date_from and date_to and date_from > date_to:
            raise serializers.ValidationError("开始日期不能大于结束日期")
        
        return attrs
