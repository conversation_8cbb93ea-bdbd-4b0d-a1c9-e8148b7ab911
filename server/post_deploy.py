#!/usr/bin/env python3
"""
部署后钩子脚本 - 自动递增全局版本号
每次成功部署后调用此脚本，自动将数据库中的全局版本号+1
"""

import os
import sys
import django
import requests
import subprocess
from datetime import datetime

# 设置Django环境
sys.path.append('/app')
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'wxcloudrun.settings')
django.setup()

from wxcloudrun.models import GlobalVersion


def get_git_commit():
    """获取当前Git提交哈希"""
    try:
        result = subprocess.run(['git', 'rev-parse', 'HEAD'], 
                              capture_output=True, text=True, cwd='/app')
        return result.stdout.strip()[:8] if result.returncode == 0 else ''
    except:
        return ''


def get_deployment_tag():
    """获取部署标签"""
    # 从环境变量或其他方式获取部署标签
    tag = os.environ.get('DEPLOYMENT_TAG', '')
    if not tag:
        # 生成基于时间的标签
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        tag = f'django-t3qr-{timestamp}'
    return tag


def increment_global_version():
    """递增全局版本号"""
    try:
        deployment_tag = get_deployment_tag()
        git_commit = get_git_commit()
        notes = f"自动部署 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}"
        
        # 递增版本
        new_record = GlobalVersion.increment_version(
            deployment_tag=deployment_tag,
            git_commit=git_commit,
            notes=notes
        )
        
        print(f"✅ 全局版本已更新: v{new_record.version}")
        print(f"📋 部署标签: {deployment_tag}")
        print(f"🔗 Git提交: {git_commit}")
        print(f"⏰ 部署时间: {new_record.deployment_time}")
        
        return new_record
        
    except Exception as e:
        print(f"❌ 版本递增失败: {e}")
        return None


def notify_deployment_success(version_record):
    """通知部署成功（可选）"""
    if not version_record:
        return
        
    try:
        # 这里可以添加通知逻辑，比如发送到监控系统
        print(f"🎉 部署成功通知: v{version_record.version} - {version_record.deployment_tag}")
    except Exception as e:
        print(f"⚠️  通知发送失败: {e}")


def main():
    """主函数"""
    print("🚀 开始执行部署后钩子...")
    
    # 等待Django应用完全启动
    import time
    time.sleep(5)
    
    # 递增版本号
    version_record = increment_global_version()
    
    # 发送通知
    notify_deployment_success(version_record)
    
    print("✅ 部署后钩子执行完成")


if __name__ == '__main__':
    main()
