# Django配置
SECRET_KEY=django-insecure-your-secret-key-here
DEBUG=True
ALLOWED_HOSTS=localhost,127.0.0.1,0.0.0.0

# 数据库配置
# 开发环境（DEBUG=True）使用外网地址和开发数据库
# 生产环境（DEBUG=False）使用内网地址和生产数据库

# 开发环境数据库配置
DB_NAME=wechatcloud_dev
DB_USER=root
DB_PASSWORD=Yixintang2025
DB_HOST=sh-cynosdbmysql-grp-l681flue.sql.tencentcdb.com
DB_PORT=25524

# 生产环境数据库配置（当DEBUG=False时使用）
# 使用专用的PROD_前缀环境变量，避免与开发环境冲突
# PROD_DB_NAME=wechatcloud_prod
# PROD_DB_USER=root
# PROD_DB_PASSWORD=Yixintang2025
# PROD_DB_HOST=*************
# PROD_DB_PORT=3306

# Celery配置（可选）
CELERY_BROKER_URL=redis://localhost:6379/0
CELERY_RESULT_BACKEND=redis://localhost:6379/0
