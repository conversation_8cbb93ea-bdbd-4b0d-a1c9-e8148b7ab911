# 本地开发环境依赖包
# 基于requirements.txt，添加开发工具

# 基础依赖（与生产环境一致）
asgiref==3.4.1
Django==3.2.8
powerline-status==2.7
PyMySQL==1.0.2
pytz==2021.3
sqlparse==0.4.2

# 开发工具
django-debug-toolbar==3.2.4    # Django调试工具栏
django-extensions==3.1.5       # Django扩展命令
ipython==8.0.1                 # 增强的Python shell
django-cors-headers==3.10.1    # CORS支持（前端开发）

# 代码质量工具
flake8==4.0.1                  # 代码风格检查
black==21.12b0                 # 代码格式化
isort==5.10.1                  # import排序

# 测试工具
pytest==6.2.5                 # 测试框架
pytest-django==4.5.2          # Django测试支持
coverage==6.2                 # 代码覆盖率

# 文档工具
Sphinx==4.3.2                 # 文档生成
