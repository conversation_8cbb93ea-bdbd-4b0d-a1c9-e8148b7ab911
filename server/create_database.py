#!/usr/bin/env python
"""
创建MySQL数据库脚本
"""
import pymysql

def create_database():
    """创建开发数据库"""
    print("🔧 开始创建开发数据库...")
    
    # 数据库连接配置
    config = {
        'host': 'sh-cynosdbmysql-grp-l681flue.sql.tencentcdb.com',
        'port': 25524,
        'user': 'root',
        'password': 'Yixintang2025',
        'charset': 'utf8mb4'
    }
    
    try:
        # 连接到MySQL服务器（不指定数据库）
        connection = pymysql.connect(**config)
        print("✅ 成功连接到MySQL服务器")
        
        with connection.cursor() as cursor:
            # 创建数据库
            cursor.execute("CREATE DATABASE IF NOT EXISTS wechat_dev CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci")
            print("✅ 数据库 'wechat_dev' 创建成功")
            
            # 显示数据库列表
            cursor.execute("SHOW DATABASES")
            databases = cursor.fetchall()
            print("📋 当前数据库列表:")
            for db in databases:
                if 'wechat' in db[0] or 'yixintang' in db[0]:
                    print(f"   - {db[0]}")
        
        connection.commit()
        print("🎉 数据库创建完成！")
        
    except Exception as e:
        print(f"❌ 创建数据库失败: {e}")
        return False
    finally:
        if 'connection' in locals():
            connection.close()
            print("🔌 数据库连接已关闭")
    
    return True

if __name__ == '__main__':
    create_database()
