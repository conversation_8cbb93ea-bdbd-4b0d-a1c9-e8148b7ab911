#!/usr/bin/env python3
"""
简化后端测试脚本
测试Django项目结构和配置
不依赖运行中的服务
"""

import os
import sys
import json
from datetime import datetime
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(str(Path(__file__).parent.parent))

class SimpleBackendTester:
    def __init__(self):
        self.results = []
        self.project_root = Path(__file__).parent.parent
        
    def log_result(self, category, test, success, details=""):
        """记录测试结果"""
        result = {
            'category': category,
            'test': test,
            'success': success,
            'details': details,
            'timestamp': datetime.now().isoformat()
        }
        self.results.append(result)
        
        status = '✅' if success else '❌'
        print(f"  {status} {test}: {details}")
        
    def test_project_structure(self):
        """测试项目结构"""
        print('📁 测试项目结构...')
        
        # 检查核心文件
        core_files = [
            'manage.py',
            'requirements.txt',
            'wxcloudrun/settings.py',
            'wxcloudrun/urls.py',
            'wxcloudrun/wsgi.py',
        ]
        
        for file_path in core_files:
            full_path = self.project_root / file_path
            exists = full_path.exists()
            self.log_result('项目结构', f'核心文件-{Path(file_path).name}', exists, 
                          '文件存在' if exists else '文件缺失')
        
        # 检查应用目录
        app_dirs = ['wxcloudrun', 'api', 'logs']
        for app_dir in app_dirs:
            full_path = self.project_root / app_dir
            exists = full_path.exists() and full_path.is_dir()
            self.log_result('项目结构', f'目录-{app_dir}', exists, 
                          '目录存在' if exists else '目录缺失')
            
    def test_django_configuration(self):
        """测试Django配置"""
        print('⚙️ 测试Django配置...')
        
        try:
            # 设置Django环境
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'wxcloudrun.settings')
            
            import django
            from django.conf import settings
            
            django.setup()
            
            # 检查基本配置
            self.log_result('Django配置', 'Settings模块', True, 'Django配置加载成功')
            
            # 检查数据库配置
            db_config = getattr(settings, 'DATABASES', {})
            has_default_db = 'default' in db_config
            self.log_result('Django配置', '数据库配置', has_default_db, 
                          f'数据库引擎: {db_config.get("default", {}).get("ENGINE", "未配置")}')
            
            # 检查应用配置
            installed_apps = getattr(settings, 'INSTALLED_APPS', [])
            has_custom_apps = any('wxcloudrun' in app or 'api' in app for app in installed_apps)
            self.log_result('Django配置', '应用配置', has_custom_apps, 
                          f'已安装应用数量: {len(installed_apps)}')
            
            # 检查中间件配置
            middleware = getattr(settings, 'MIDDLEWARE', [])
            has_cors = any('cors' in mw.lower() for mw in middleware)
            self.log_result('Django配置', 'CORS中间件', has_cors, 
                          'CORS已配置' if has_cors else 'CORS未配置')
            
        except Exception as e:
            self.log_result('Django配置', '配置加载', False, str(e))
            
    def test_models(self):
        """测试模型定义"""
        print('🗄️ 测试模型定义...')
        
        try:
            from django.apps import apps
            
            # 获取所有模型
            all_models = apps.get_models()
            custom_models = [model for model in all_models 
                           if model._meta.app_label in ['wxcloudrun', 'api']]
            
            self.log_result('模型定义', '自定义模型', len(custom_models) > 0, 
                          f'自定义模型数量: {len(custom_models)}')
            
            # 检查主要模型
            expected_models = ['User', 'Service', 'Customer', 'Therapist', 'Appointment']
            found_models = []
            
            for model in custom_models:
                model_name = model.__name__
                if model_name in expected_models:
                    found_models.append(model_name)
                    self.log_result('模型定义', f'模型-{model_name}', True, '模型已定义')
            
            missing_models = set(expected_models) - set(found_models)
            for model_name in missing_models:
                self.log_result('模型定义', f'模型-{model_name}', False, '模型缺失')
                
        except Exception as e:
            self.log_result('模型定义', '模型检查', False, str(e))
            
    def test_urls_configuration(self):
        """测试URL配置"""
        print('🌐 测试URL配置...')
        
        try:
            from django.urls import get_resolver
            
            resolver = get_resolver()
            url_patterns = resolver.url_patterns
            
            self.log_result('URL配置', 'URL模式', len(url_patterns) > 0, 
                          f'URL模式数量: {len(url_patterns)}')
            
            # 检查API路由
            has_api_routes = any('api' in str(pattern.pattern) for pattern in url_patterns)
            self.log_result('URL配置', 'API路由', has_api_routes, 
                          'API路由已配置' if has_api_routes else 'API路由未配置')
            
        except Exception as e:
            self.log_result('URL配置', 'URL检查', False, str(e))
            
    def test_views(self):
        """测试视图函数"""
        print('👁️ 测试视图函数...')
        
        try:
            # 检查视图文件
            views_files = [
                self.project_root / 'wxcloudrun' / 'views.py',
                self.project_root / 'api' / 'views.py',
            ]
            
            for views_file in views_files:
                if views_file.exists():
                    content = views_file.read_text()
                    has_views = 'def ' in content or 'class ' in content
                    self.log_result('视图函数', f'视图文件-{views_file.parent.name}', has_views, 
                                  '包含视图定义' if has_views else '无视图定义')
                else:
                    self.log_result('视图函数', f'视图文件-{views_file.parent.name}', False, '文件不存在')
                    
        except Exception as e:
            self.log_result('视图函数', '视图检查', False, str(e))
            
    def test_api_structure(self):
        """测试API结构"""
        print('📡 测试API结构...')
        
        try:
            api_dir = self.project_root / 'api'
            if api_dir.exists():
                api_files = list(api_dir.glob('*.py'))
                self.log_result('API结构', 'API文件', len(api_files) > 0, 
                              f'API文件数量: {len(api_files)}')
                
                # 检查主要API文件
                expected_files = ['views.py', 'models.py', 'serializers.py', 'urls.py']
                for file_name in expected_files:
                    file_path = api_dir / file_name
                    exists = file_path.exists()
                    self.log_result('API结构', f'API文件-{file_name}', exists, 
                                  '文件存在' if exists else '文件缺失')
            else:
                self.log_result('API结构', 'API目录', False, 'API目录不存在')
                
        except Exception as e:
            self.log_result('API结构', 'API检查', False, str(e))
            
    def generate_report(self):
        """生成测试报告"""
        success_count = sum(1 for r in self.results if r['success'])
        total_count = len(self.results)
        success_rate = round((success_count / total_count) * 100) if total_count > 0 else 0
        
        # 按类别统计
        categories = {}
        for result in self.results:
            category = result['category']
            if category not in categories:
                categories[category] = {'total': 0, 'success': 0}
            categories[category]['total'] += 1
            if result['success']:
                categories[category]['success'] += 1
                
        report = {
            'summary': {
                'type': 'simple-backend-test',
                'total_tests': total_count,
                'success_count': success_count,
                'failure_count': total_count - success_count,
                'success_rate': success_rate,
                'timestamp': datetime.now().isoformat()
            },
            'categories': categories,
            'results': self.results
        }
        
        # 保存报告
        report_dir = self.project_root / 'test-reports'
        report_dir.mkdir(exist_ok=True)
        
        report_file = report_dir / f'simple-backend-{datetime.now().strftime("%Y%m%d-%H%M%S")}.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, ensure_ascii=False, indent=2)
            
        return report, report_file
        
    def run_all_tests(self):
        """运行所有测试"""
        print('🔧 启动简化后端测试...')
        
        # 执行所有测试
        self.test_project_structure()
        self.test_django_configuration()
        self.test_models()
        self.test_urls_configuration()
        self.test_views()
        self.test_api_structure()
        
        # 生成报告
        report, report_file = self.generate_report()
        
        # 输出结果
        print('\n🔧 简化后端测试完成')
        print(f'✅ 测试结果: {report["summary"]["success_count"]}/{report["summary"]["total_tests"]} ({report["summary"]["success_rate"]}%)')
        print(f'📄 详细报告: {report_file}')
        
        # 分类统计
        print('\n📊 分类统计:')
        for category, stats in report['categories'].items():
            rate = round((stats['success'] / stats['total']) * 100)
            print(f'  {category}: {stats["success"]}/{stats["total"]} ({rate}%)')
            
        # 综合评估
        if report['summary']['success_rate'] >= 95:
            print('\n🏆 完美！后端结构完整，配置正确')
        elif report['summary']['success_rate'] >= 85:
            print('\n✅ 优秀！后端基本正常')
        elif report['summary']['success_rate'] >= 70:
            print('\n⚠️ 良好！部分功能需要完善')
        else:
            print('\n❌ 需要改进！后端结构存在问题')

if __name__ == '__main__':
    tester = SimpleBackendTester()
    tester.run_all_tests()
