"""
业务视图 - 基于20年经验设计的专业API视图
严谨的业务逻辑和错误处理
"""

from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg
from django.db import transaction
from datetime import datetime, timedelta, time
import logging

from wxcloudrun.models import (
    Service, Therapist, Customer, Appointment, 
    AppointmentReview, FinanceRecord, HealthTip, GlobalVersion
)
from .business_serializers import (
    ServiceSerializer, TherapistSerializer, CustomerSerializer,
    AppointmentSerializer, AppointmentCreateSerializer, AppointmentReviewSerializer,
    HealthTipSerializer, GlobalVersionSerializer, DashboardStatsSerializer,
    TimeSlotSerializer
)

logger = logging.getLogger(__name__)


class ServiceViewSet(viewsets.ModelViewSet):
    """
    服务项目管理

    提供中医推拿服务项目的完整管理功能：
    - 查看所有服务项目
    - 创建新的服务项目
    - 更新服务信息
    - 删除服务项目
    """
    queryset = Service.objects.filter(is_active=True, is_deleted=False)
    serializer_class = ServiceSerializer
    permission_classes = [permissions.AllowAny]  # 小程序需要公开访问
    
    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()
        
        # 按热门程度排序
        is_popular = self.request.query_params.get('popular')
        if is_popular == 'true':
            queryset = queryset.filter(is_popular=True)
        
        # 价格范围筛选
        min_price = self.request.query_params.get('min_price')
        max_price = self.request.query_params.get('max_price')
        if min_price:
            queryset = queryset.filter(price__gte=min_price)
        if max_price:
            queryset = queryset.filter(price__lte=max_price)
        
        return queryset.order_by('-is_popular', 'sort_order', 'id')

    def create(self, request, *args, **kwargs):
        """创建服务，包含名称唯一性检查和恢复逻辑"""
        from django.utils import timezone

        name = request.data.get('name', '').strip()
        if not name:
            return Response({'error': '服务名称不能为空'}, status=status.HTTP_400_BAD_REQUEST)

        # 检查是否存在同名服务
        existing_active = Service.objects.filter(name=name, is_deleted=False).first()
        if existing_active:
            return Response({
                'error': '服务名称已存在',
                'message': f'已存在名为"{name}"的服务，请使用其他名称'
            }, status=status.HTTP_400_BAD_REQUEST)

        # 检查是否有已删除的同名服务
        existing_deleted = Service.objects.filter(name=name, is_deleted=True).first()
        if existing_deleted:
            # 恢复已删除的服务并更新数据
            existing_deleted.description = request.data.get('description', existing_deleted.description)
            existing_deleted.price = request.data.get('price', existing_deleted.price)
            existing_deleted.commission = request.data.get('commission', existing_deleted.commission)
            existing_deleted.duration = request.data.get('duration', existing_deleted.duration)
            existing_deleted.image = request.data.get('image', existing_deleted.image)
            existing_deleted.is_active = request.data.get('status', 'active') == 'active'
            existing_deleted.is_deleted = False
            existing_deleted.deleted_at = None
            existing_deleted.updated_at = timezone.now()
            existing_deleted.save()

            serializer = self.get_serializer(existing_deleted)
            return Response(serializer.data, status=status.HTTP_201_CREATED)

        # 正常创建新服务
        return super().create(request, *args, **kwargs)

    def update(self, request, *args, **kwargs):
        """更新服务，包含名称唯一性检查"""
        instance = self.get_object()
        name = request.data.get('name', '').strip()

        if name and name != instance.name:
            # 名称发生变化，需要检查唯一性
            existing_service = Service.objects.filter(name=name, is_deleted=False).exclude(id=instance.id).first()
            if existing_service:
                return Response({
                    'error': '服务名称已存在',
                    'message': f'已存在名为"{name}"的服务，请使用其他名称'
                }, status=status.HTTP_400_BAD_REQUEST)

        return super().update(request, *args, **kwargs)

    @action(detail=False, methods=['get'])
    def popular(self, request):
        """获取热门服务"""
        popular_services = self.get_queryset().filter(is_popular=True)[:6]
        serializer = self.get_serializer(popular_services, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'])
    def soft_delete(self, request, pk=None):
        """软删除服务"""
        from django.utils import timezone

        service = self.get_object()
        service.is_deleted = True
        service.deleted_at = timezone.now()
        service.save(update_fields=['is_deleted', 'deleted_at'])

        return Response({'message': '服务已删除'}, status=status.HTTP_200_OK)

    @action(detail=False, methods=['get'])
    def check_name(self, request):
        """检查服务名称唯一性"""
        name = request.query_params.get('name')
        exclude_id = request.query_params.get('exclude_id')

        if not name:
            return Response({'error': '请提供服务名称'}, status=status.HTTP_400_BAD_REQUEST)

        # 检查是否存在同名服务，优先检查未删除的服务
        queryset = Service.objects.filter(name=name)
        if exclude_id:
            queryset = queryset.exclude(id=exclude_id)

        # 先查找未删除的服务
        active_service = queryset.filter(is_deleted=False).first()
        if active_service:
            return Response({
                'exists': True,
                'is_deleted': False,
                'service_id': active_service.id
            })

        # 如果没有未删除的，再查找已删除的服务
        deleted_service = queryset.filter(is_deleted=True).first()
        if deleted_service:
            return Response({
                'exists': True,
                'is_deleted': True,
                'service_id': deleted_service.id
            })

        # 没有找到任何同名服务
        return Response({
            'exists': False,
            'is_deleted': False
        })

    @action(detail=False, methods=['get'])
    def get_deleted(self, request):
        """获取已删除服务的详细信息"""
        name = request.query_params.get('name')
        if not name:
            return Response({'error': '请提供服务名称'}, status=status.HTTP_400_BAD_REQUEST)

        # 查找已删除的同名服务（使用所有服务的查询集）
        deleted_service = Service.objects.filter(name=name, is_deleted=True).first()

        if not deleted_service:
            return Response({'error': '未找到已删除的同名服务'}, status=status.HTTP_404_NOT_FOUND)

        # 返回已删除服务的详细信息
        from .business_serializers import ServiceSerializer
        serializer = ServiceSerializer(deleted_service)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=False, methods=['post'])
    def restore(self, request):
        """恢复已删除的服务"""
        from django.utils import timezone

        name = request.data.get('name')
        if not name:
            return Response({'error': '请提供服务名称'}, status=status.HTTP_400_BAD_REQUEST)

        # 查找已删除的同名服务
        deleted_service = Service.objects.filter(name=name, is_deleted=True).first()

        if not deleted_service:
            return Response({'error': '未找到已删除的同名服务'}, status=status.HTTP_404_NOT_FOUND)

        # 更新服务信息
        deleted_service.description = request.data.get('description', deleted_service.description)
        deleted_service.price = request.data.get('price', deleted_service.price)
        deleted_service.commission = request.data.get('commission', deleted_service.commission)
        deleted_service.duration = request.data.get('duration', deleted_service.duration)
        deleted_service.image = request.data.get('image', deleted_service.image)
        deleted_service.is_active = request.data.get('status', 'active') == 'active'
        deleted_service.is_deleted = False
        deleted_service.deleted_at = None
        deleted_service.updated_at = timezone.now()
        deleted_service.save()

        serializer = self.get_serializer(deleted_service)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'])
    def update_price(self, request, pk=None):
        """单独修改服务价格或提成"""
        from django.utils import timezone
        from wxcloudrun.models import ServicePriceHistory

        service = self.get_object()
        new_price = request.data.get('price')
        new_commission = request.data.get('commission')

        if new_price is None and new_commission is None:
            return Response({'error': '请提供价格或提成信息'}, status=status.HTTP_400_BAD_REQUEST)

        # 记录原始值
        old_price = service.price
        old_commission = service.commission

        # 确定修改类型
        change_type = 'both'
        if new_price is not None and new_commission is None:
            change_type = 'price'
            new_commission = old_commission
        elif new_commission is not None and new_price is None:
            change_type = 'commission'
            new_price = old_price

        # 验证数值
        try:
            new_price = float(new_price)
            new_commission = float(new_commission)
            if new_price < 0 or new_commission < 0:
                return Response({'error': '服务费和技师价格不能为负数'}, status=status.HTTP_400_BAD_REQUEST)
            if new_commission > new_price:
                return Response({'error': '技师价格不能超过服务费'}, status=status.HTTP_400_BAD_REQUEST)
        except (ValueError, TypeError):
            return Response({'error': '服务费和技师价格必须是有效数字'}, status=status.HTTP_400_BAD_REQUEST)

        # 检查是否有实际变化
        if float(old_price) == new_price and float(old_commission) == new_commission:
            return Response({'message': '价格和提成未发生变化'}, status=status.HTTP_200_OK)

        # 创建历史记录
        ServicePriceHistory.objects.create(
            service=service,
            old_price=old_price,
            new_price=new_price,
            old_commission=old_commission,
            new_commission=new_commission,
            change_type=change_type
        )

        # 更新服务
        service.price = new_price
        service.commission = new_commission
        service.updated_at = timezone.now()
        service.save(update_fields=['price', 'commission', 'updated_at'])

        serializer = self.get_serializer(service)
        return Response(serializer.data, status=status.HTTP_200_OK)

    @action(detail=True, methods=['get'])
    def price_history(self, request, pk=None):
        """获取服务价格修改历史"""
        from .business_serializers import ServicePriceHistorySerializer
        from wxcloudrun.models import ServicePriceHistory

        service = self.get_object()
        history = ServicePriceHistory.objects.filter(service=service).order_by('-changed_at')

        serializer = ServicePriceHistorySerializer(history, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)


class TherapistViewSet(viewsets.ModelViewSet):
    """技师视图集"""
    queryset = Therapist.objects.filter(is_active=True)
    serializer_class = TherapistSerializer
    permission_classes = [permissions.AllowAny]  # 临时开放用于测试
    
    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()
        
        # 按等级和评分排序
        return queryset.order_by('-level', '-rating', 'id')
    
    @action(detail=True, methods=['get'])
    def available_slots(self, request, pk=None):
        """获取技师可用时间段"""
        therapist = self.get_object()
        date_str = request.query_params.get('date')
        
        if not date_str:
            return Response({'error': '请提供日期参数'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response({'error': '日期格式错误'}, status=status.HTTP_400_BAD_REQUEST)
        
        # 生成时间段（9:00-21:00，每30分钟一个时间段）
        slots = []
        current_time = time(9, 0)
        end_time = time(21, 0)
        
        while current_time < end_time:
            slot_datetime = datetime.combine(target_date, current_time)
            
            # 检查是否有预约冲突
            has_appointment = Appointment.objects.filter(
                therapist=therapist,
                appointment_time__date=target_date,
                appointment_time__time=current_time,
                status__in=['confirmed', 'in_progress']
            ).exists()
            
            slots.append({
                'time': current_time,
                'available': not has_appointment,
                'therapist_id': therapist.id,
                'therapist_name': therapist.name
            })
            
            # 增加30分钟
            current_time = (datetime.combine(target_date, current_time) + timedelta(minutes=30)).time()
        
        serializer = TimeSlotSerializer(slots, many=True)
        return Response(serializer.data)


class CustomerViewSet(viewsets.ModelViewSet):
    """客户视图集"""
    queryset = Customer.objects.all()
    serializer_class = CustomerSerializer
    permission_classes = [permissions.AllowAny]  # 临时开放用于测试
    
    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()
        
        # 搜索功能
        search = self.request.query_params.get('search')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) | Q(phone__icontains=search)
            )
        
        # VIP筛选
        is_vip = self.request.query_params.get('vip')
        if is_vip == 'true':
            queryset = queryset.filter(is_vip=True)
        
        return queryset.order_by('-created_at')
    
    @action(detail=True, methods=['get'])
    def appointments(self, request, pk=None):
        """获取客户预约记录"""
        customer = self.get_object()
        appointments = Appointment.objects.filter(customer=customer).order_by('-appointment_time')
        
        # 分页
        page = self.paginate_queryset(appointments)
        if page is not None:
            serializer = AppointmentSerializer(page, many=True)
            return self.get_paginated_response(serializer.data)
        
        serializer = AppointmentSerializer(appointments, many=True)
        return Response(serializer.data)


class AppointmentViewSet(viewsets.ModelViewSet):
    """预约视图集"""
    queryset = Appointment.objects.all()
    permission_classes = [permissions.AllowAny]  # 临时开放用于测试
    
    def get_serializer_class(self):
        """根据动作选择序列化器"""
        if self.action == 'create':
            return AppointmentCreateSerializer
        return AppointmentSerializer
    
    def get_queryset(self):
        """获取查询集"""
        queryset = super().get_queryset()
        
        # 状态筛选
        status_filter = self.request.query_params.get('status')
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # 日期筛选
        date_filter = self.request.query_params.get('date')
        if date_filter:
            try:
                target_date = datetime.strptime(date_filter, '%Y-%m-%d').date()
                queryset = queryset.filter(appointment_time__date=target_date)
            except ValueError:
                pass
        
        # 技师筛选
        therapist_id = self.request.query_params.get('therapist')
        if therapist_id:
            queryset = queryset.filter(therapist_id=therapist_id)
        
        return queryset.order_by('-appointment_time')
    
    @transaction.atomic
    def create(self, request, *args, **kwargs):
        """创建预约"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        
        # 创建预约
        appointment = serializer.save()
        
        # 更新客户统计
        customer = appointment.customer
        customer.visit_count += 1
        customer.last_visit = timezone.now()
        customer.save(update_fields=['visit_count', 'last_visit'])
        
        # 更新技师统计
        therapist = appointment.therapist
        therapist.service_count += 1
        therapist.save(update_fields=['service_count'])
        
        # 创建财务记录
        FinanceRecord.objects.create(
            type='income',
            category='service',
            amount=appointment.final_price,
            description=f'预约服务：{appointment.service.name}',
            date=appointment.appointment_time.date(),
            appointment=appointment,
            employee_id=1  # 默认系统操作员
        )
        
        logger.info(f'预约创建成功：{appointment.id}')
        
        headers = self.get_success_headers(serializer.data)
        return Response(
            AppointmentSerializer(appointment).data,
            status=status.HTTP_201_CREATED,
            headers=headers
        )
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """取消预约"""
        appointment = self.get_object()
        
        if not appointment.can_cancel():
            return Response(
                {'error': '该预约无法取消'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        reason = request.data.get('reason', '')
        
        with transaction.atomic():
            appointment.status = 'cancelled'
            appointment.cancellation_reason = reason
            appointment.save(update_fields=['status', 'cancellation_reason'])
            
            # 如果已支付，创建退款记录
            if appointment.payment_status == 'paid':
                FinanceRecord.objects.create(
                    type='expense',
                    category='refund',
                    amount=appointment.final_price,
                    description=f'预约取消退款：{appointment.service.name}',
                    date=timezone.now().date(),
                    appointment=appointment,
                    employee_id=1
                )
                appointment.payment_status = 'refunded'
                appointment.save(update_fields=['payment_status'])
        
        logger.info(f'预约取消成功：{appointment.id}，原因：{reason}')
        
        return Response({'message': '预约已取消'})
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """完成预约"""
        appointment = self.get_object()
        
        if appointment.status != 'in_progress':
            return Response(
                {'error': '只能完成进行中的预约'},
                status=status.HTTP_400_BAD_REQUEST
            )
        
        with transaction.atomic():
            appointment.status = 'completed'
            appointment.actual_end_time = timezone.now()
            appointment.save(update_fields=['status', 'actual_end_time'])
            
            # 更新客户消费统计
            customer = appointment.customer
            customer.total_spent += appointment.final_price
            customer.save(update_fields=['total_spent'])
        
        logger.info(f'预约完成：{appointment.id}')
        
        return Response({'message': '预约已完成'})


class DashboardViewSet(viewsets.ViewSet):
    """仪表盘视图集"""
    permission_classes = [permissions.AllowAny]  # 临时开放用于测试
    
    @action(detail=False, methods=['get'])
    def stats(self, request):
        """获取仪表盘统计数据"""
        today = timezone.now().date()
        
        # 今日统计
        today_appointments = Appointment.objects.filter(
            appointment_time__date=today
        ).count()
        
        today_income = FinanceRecord.objects.filter(
            date=today,
            type='income'
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        total_customers = Customer.objects.count()
        active_therapists = Therapist.objects.filter(is_active=True).count()
        
        pending_appointments = Appointment.objects.filter(
            status='pending'
        ).count()
        
        completed_appointments_today = Appointment.objects.filter(
            appointment_time__date=today,
            status='completed'
        ).count()
        
        # 一周收入数据
        week_start = today - timedelta(days=6)
        weekly_income = []
        for i in range(7):
            day = week_start + timedelta(days=i)
            day_income = FinanceRecord.objects.filter(
                date=day,
                type='income'
            ).aggregate(total=Sum('amount'))['total'] or 0
            weekly_income.append(float(day_income))
        
        # 服务分布
        service_distribution = list(
            Service.objects.annotate(
                appointment_count=Count('appointment')
            ).values('name', 'appointment_count')[:5]
        )
        
        # 技师绩效
        therapist_performance = list(
            Therapist.objects.filter(is_active=True).annotate(
                appointment_count=Count('appointment'),
                total_income=Sum('appointment__final_price')
            ).values('name', 'appointment_count', 'total_income')[:5]
        )
        
        stats_data = {
            'today_appointments': today_appointments,
            'today_income': today_income,
            'total_customers': total_customers,
            'active_therapists': active_therapists,
            'pending_appointments': pending_appointments,
            'completed_appointments_today': completed_appointments_today,
            'weekly_income': weekly_income,
            'service_distribution': service_distribution,
            'therapist_performance': therapist_performance
        }
        
        serializer = DashboardStatsSerializer(stats_data)
        return Response(serializer.data)
