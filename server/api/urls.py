"""
API URL配置 - 基于业务模型的完整路由配置
"""
from django.urls import path, include
from rest_framework.routers import DefaultRouter
from . import views
from . import volcengine_views

# 导入新的业务视图集
from .business_views import (
    ServiceViewSet, TherapistViewSet, CustomerViewSet,
    AppointmentViewSet, DashboardViewSet
)

# 创建路由器
router = DefaultRouter()

# 注册业务视图集
router.register(r'services', ServiceViewSet, basename='services')
router.register(r'therapists', TherapistViewSet, basename='therapists')
router.register(r'customers', CustomerViewSet, basename='customers')
router.register(r'appointments', AppointmentViewSet, basename='appointments')
router.register(r'dashboard', DashboardViewSet, basename='dashboard')

urlpatterns = [
    # 原有的调试接口
    path('debug/', views.debug_info, name='api_debug'),
    path('status/', views.health_check, name='api_status'),

    # 认证API
    path('auth/login/', views.admin_login, name='admin_login'),
    path('auth/logout/', views.admin_logout, name='admin_logout'),
    path('auth/profile/', views.admin_profile, name='admin_profile'),

    # 微信扫码登录API
    path('auth/wechat/qr/', views.generate_qr_code, name='wechat_qr'),
    path('auth/wechat/status/', views.check_qr_status, name='wechat_status'),
    path('auth/wechat/callback/', views.wechat_callback, name='wechat_callback'),

    # 微信绑定API
    path('wechat/generate-binding-qr/', views.generate_binding_qr, name='generate_binding_qr'),
    path('wechat/check-binding-status/', views.check_binding_status, name='check_binding_status'),
    path('wechat/binding-status/', views.binding_status, name='binding_status'),
    path('wechat/unbind/', views.unbind_wechat, name='unbind_wechat'),
    path('wechat/bind/callback/', views.wechat_bind_callback, name='wechat_bind_callback'),

    # 火山引擎AI API
    path('volcengine/generate-image/', volcengine_views.VolcengineImageGenerateView.as_view(), name='volcengine_generate_image'),
    path('volcengine/config/', volcengine_views.VolcengineConfigView.as_view(), name='volcengine_config'),

    # 业务API路由
    path('v1/', include(router.urls)),
]
