"""
API序列化器
"""
# 标准库导入
import re

# 第三方库导入
from rest_framework import serializers

# 本地应用导入
from wxcloudrun.models import (
    Service, ServiceCategory, Therapist, Customer,
    Appointment, AppointmentReview, FinanceRecord
)


class ServiceCategorySerializer(serializers.ModelSerializer):
    """服务分类序列化器."""

    class Meta:
        model = ServiceCategory
        fields = [
            'id', 'name', 'description', 'sort_order',
            'is_active', 'created_at'
        ]
        read_only_fields = ['id', 'created_at']


class ServiceSerializer(serializers.ModelSerializer):
    """服务项目序列化器."""

    # 添加status字段映射
    status = serializers.SerializerMethodField()

    def get_status(self, obj):
        """将is_active字段映射为status"""
        return 'active' if obj.is_active else 'inactive'

    class Meta:
        model = Service
        fields = [
            'id', 'name', 'description', 'price', 'commission', 'duration',
            'image', 'rating', 'review_count', 'is_popular', 'benefits',
            'contraindications', 'status', 'sort_order', 'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'rating', 'review_count', 'created_at', 'updated_at']

    def create(self, validated_data):
        """创建服务时处理status字段"""
        status = validated_data.pop('status', 'active')
        validated_data['is_active'] = (status == 'active')
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """更新服务时处理status字段"""
        status = validated_data.pop('status', None)
        if status is not None:
            validated_data['is_active'] = (status == 'active')
        return super().update(instance, validated_data)
