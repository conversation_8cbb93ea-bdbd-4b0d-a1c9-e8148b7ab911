"""
系统健康检查
"""
# 标准库导入
import os
import time

# Django导入
from django.http import JsonResponse
from django.db import connection
from django.core.cache import cache
from django.conf import settings


def health_check(request):
    """系统健康检查端点"""
    
    health_status = {
        'status': 'healthy',
        'timestamp': int(time.time()),
        'checks': {}
    }
    
    # 检查数据库连接
    try:
        with connection.cursor() as cursor:
            cursor.execute("SELECT 1")
            cursor.fetchone()
        health_status['checks']['database'] = {
            'status': 'healthy',
            'message': 'Database connection successful'
        }
    except Exception as e:
        health_status['checks']['database'] = {
            'status': 'unhealthy',
            'message': f'Database connection failed: {str(e)}'
        }
        health_status['status'] = 'unhealthy'
    
    # 检查缓存连接
    try:
        cache.set('health_check', 'test', 10)
        cache.get('health_check')
        health_status['checks']['cache'] = {
            'status': 'healthy',
            'message': 'Cache connection successful'
        }
    except Exception as e:
        health_status['checks']['cache'] = {
            'status': 'unhealthy',
            'message': f'Cache connection failed: {str(e)}'
        }
        health_status['status'] = 'unhealthy'
    
    # 检查磁盘空间
    try:
        disk_usage = os.statvfs(settings.BASE_DIR)
        free_space = disk_usage.f_bavail * disk_usage.f_frsize
        total_space = disk_usage.f_blocks * disk_usage.f_frsize
        usage_percent = (total_space - free_space) / total_space * 100
        
        if usage_percent > 90:
            health_status['checks']['disk'] = {
                'status': 'warning',
                'message': f'Disk usage high: {usage_percent:.1f}%'
            }
        else:
            health_status['checks']['disk'] = {
                'status': 'healthy',
                'message': f'Disk usage: {usage_percent:.1f}%'
            }
    except Exception as e:
        health_status['checks']['disk'] = {
            'status': 'unknown',
            'message': f'Could not check disk usage: {str(e)}'
        }
    
    # 检查日志目录
    try:
        log_dir = settings.BASE_DIR / 'logs'
        if not log_dir.exists():
            os.makedirs(log_dir, exist_ok=True)
        
        health_status['checks']['logs'] = {
            'status': 'healthy',
            'message': 'Log directory accessible'
        }
    except Exception as e:
        health_status['checks']['logs'] = {
            'status': 'unhealthy',
            'message': f'Log directory issue: {str(e)}'
        }
    
    # 设置HTTP状态码
    status_code = 200 if health_status['status'] == 'healthy' else 503
    
    return JsonResponse(
        health_status,
        status=status_code,
        json_dumps_params={'ensure_ascii': False, 'indent': 2}
    )


def readiness_check(request):
    """就绪检查端点"""
    
    readiness_status = {
        'status': 'ready',
        'timestamp': int(time.time()),
        'services': {}
    }
    
    # 检查必要的服务
    try:
        # 检查数据库迁移状态
        from django.core.management import execute_from_command_line
        from django.db.migrations.executor import MigrationExecutor
        from django.db import connections
        
        executor = MigrationExecutor(connections['default'])
        plan = executor.migration_plan(executor.loader.graph.leaf_nodes())
        
        if plan:
            readiness_status['services']['migrations'] = {
                'status': 'not_ready',
                'message': 'Pending migrations found'
            }
            readiness_status['status'] = 'not_ready'
        else:
            readiness_status['services']['migrations'] = {
                'status': 'ready',
                'message': 'All migrations applied'
            }
    except Exception as e:
        readiness_status['services']['migrations'] = {
            'status': 'unknown',
            'message': f'Could not check migrations: {str(e)}'
        }
    
    # 检查静态文件
    try:
        static_root = getattr(settings, 'STATIC_ROOT', None)
        if static_root and os.path.exists(static_root):
            readiness_status['services']['static_files'] = {
                'status': 'ready',
                'message': 'Static files collected'
            }
        else:
            readiness_status['services']['static_files'] = {
                'status': 'not_ready',
                'message': 'Static files not collected'
            }
    except Exception as e:
        readiness_status['services']['static_files'] = {
            'status': 'unknown',
            'message': f'Could not check static files: {str(e)}'
        }
    
    # 设置HTTP状态码
    status_code = 200 if readiness_status['status'] == 'ready' else 503
    
    return JsonResponse(
        readiness_status,
        status=status_code,
        json_dumps_params={'ensure_ascii': False, 'indent': 2}
    )


def liveness_check(request):
    """存活检查端点"""
    
    return JsonResponse({
        'status': 'alive',
        'timestamp': int(time.time()),
        'message': 'Service is running'
    })
