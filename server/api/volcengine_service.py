"""
火山引擎AI图片生成服务
使用官方Python SDK集成火山引擎AI图片生成功能
"""
import os
import json
import logging
import base64
import requests
from typing import Dict, Any, Optional

logger = logging.getLogger(__name__)


class VolcengineImageService:
    """火山引擎图片生成服务"""
    
    def __init__(self):
        """初始化火山引擎服务"""
        self.api_key = os.getenv('ARK_API_KEY', 'ae14c0e4-a270-45bc-a5ff-3a9437bb7315')
        self.base_url = 'https://ark.cn-beijing.volces.com/api/v3'
        self.model = 'doubao-seedream-3-0-t2i-250415'
        
        # DeepSeek配置
        self.deepseek_api_key = '***********************************'
        self.deepseek_base_url = 'https://api.deepseek.com/v1/chat/completions'
        
        logger.info("🌋 火山引擎服务初始化完成")

    def is_configured(self) -> bool:
        """检查服务是否已配置"""
        return bool(self.api_key)

    def _url_to_base64(self, image_url: str) -> str:
        """将图片URL转换为base64格式"""
        try:
            response = requests.get(image_url, timeout=30)
            response.raise_for_status()

            # 获取图片内容
            image_content = response.content

            # 转换为base64
            base64_string = base64.b64encode(image_content).decode('utf-8')

            # 获取图片格式
            content_type = response.headers.get('content-type', 'image/jpeg')

            # 构建完整的base64数据URI
            base64_data_uri = f"data:{content_type};base64,{base64_string}"

            logger.info(f"图片转换为base64成功，大小: {len(base64_string)} 字符")
            return base64_data_uri

        except Exception as e:
            logger.error(f"图片转换base64失败: {str(e)}")
            raise Exception(f"图片转换失败: {str(e)}")
    
    async def generate_prompt_with_deepseek(self, service_name: str, service_description: str) -> str:
        """使用DeepSeek生成图片提示词"""
        try:
            import requests
            
            logger.info(f"🤖 使用DeepSeek生成图片提示词: {service_name}")
            
            prompt_template = f"""你是一个专业的中医理疗服务图片提示词生成专家。请根据服务名称和描述生成适合火山引擎AI图片生成的中文提示词。

服务名称: {service_name}
服务描述: {service_description}

要求:
1. 生成真实的中医理疗场景图片提示词
2. 包含专业的理疗师、设备、环境描述
3. 强调专业性、舒适性、安全性
4. 使用中文描述，适合中国用户
5. 避免卡通、动漫风格
6. 强调摄影风格，自然光线
7. 不要包含任何文字或标识

请直接返回提示词，不要其他解释。"""

            response = requests.post(
                self.deepseek_base_url,
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {self.deepseek_api_key}'
                },
                json={
                    'model': 'deepseek-chat',
                    'messages': [
                        {
                            'role': 'user',
                            'content': prompt_template
                        }
                    ],
                    'max_tokens': 500,
                    'temperature': 0.7
                },
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                prompt = result['choices'][0]['message']['content'].strip()
                logger.info(f"✅ DeepSeek提示词生成成功: {prompt[:100]}...")
                return prompt
            else:
                raise Exception(f"DeepSeek API请求失败: {response.status_code}")
                
        except Exception as e:
            logger.error(f"❌ DeepSeek提示词生成失败: {str(e)}")
            # 返回默认提示词
            return self._get_default_prompt(service_name, service_description)
    
    def _get_default_prompt(self, service_name: str, service_description: str) -> str:
        """获取默认提示词"""
        default_prompts = {
            '按摩': '专业中医按摩师为顾客进行全身按摩，宽敞明亮的理疗室，实木按摩床铺着白色床单，柔和暖光营造放松氛围。技师身着白色工作服，运用专业手法按压背部穴位，顾客闭目享受。墙上挂着中医经络图，角落摆放着熏香炉，木质家具简约雅致，专业摄影风格，自然光线，高清细节，展现真实理疗场景',
            '推拿': '专业中医推拿师为顾客进行全身经络推拿，宽敞明亮的理疗室，实木按摩床铺着白色床单，柔和暖光营造放松氛围。技师身着白色工作服，运用专业手法按压背部穴位，顾客闭目享受。墙上挂着中医经络图，角落摆放着熏香炉，木质家具简约雅致，专业摄影风格，自然光线，高清细节，展现真实理疗场景',
            '足疗': '专业足疗师为顾客进行足部按摩，舒适的足疗椅，温暖的泡脚盆，柔和的灯光。技师专业手法按摩足部穴位，顾客放松享受。环境整洁温馨，专业摄影风格，自然光线',
            '艾灸': '专业中医师进行艾灸治疗，传统艾条燃烧，温暖的治疗环境，顾客安静躺在治疗床上。中医诊室布置典雅，挂着中医理论图表，专业摄影风格，自然光线',
            '拔罐': '专业中医师进行拔罐治疗，传统玻璃火罐，顾客背部接受治疗。中医诊室环境整洁专业，墙上挂着经络图，专业摄影风格，自然光线'
        }
        
        # 根据服务名称匹配默认提示词
        for key, prompt in default_prompts.items():
            if key in service_name:
                return prompt
        
        # 通用默认提示词
        return f'专业的{service_name}服务场景，整洁明亮的理疗环境，专业技师为顾客提供服务，温馨舒适的氛围，专业摄影风格，自然光线，高清细节'
    
    def generate_image_with_volcengine(self, prompt: str) -> str:
        """使用火山引擎生成图片"""
        try:
            if not self.is_configured():
                raise Exception("火山引擎服务未正确配置")

            logger.info(f"🌋 调用火山引擎生成图片...")
            logger.info(f"📝 使用提示词: {prompt}")

            # 构建请求数据
            request_data = {
                "model": self.model,
                "prompt": prompt,
                "size": "1024x512",  # 调整为宽1024高512的横向比例
                "response_format": "url",
                "seed": 12,
                "guidance_scale": 2.5,
                "watermark": True
            }

            # 调用火山引擎API
            response = requests.post(
                f"{self.base_url}/images/generations",
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {self.api_key}'
                },
                json=request_data,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('data') and len(result['data']) > 0:
                    image_url = result['data'][0].get('url')
                    logger.info(f"✅ 火山引擎图片生成成功: {image_url}")

                    # 将图片URL转换为base64
                    base64_image = self._url_to_base64(image_url)
                    logger.info("✅ 图片已转换为base64格式")
                    return base64_image
                else:
                    raise Exception("火山引擎API返回数据格式异常")
            else:
                error_msg = f"火山引擎API请求失败: {response.status_code}"
                if response.text:
                    error_msg += f", 错误信息: {response.text}"
                raise Exception(error_msg)

        except Exception as e:
            logger.error(f"❌ 火山引擎图片生成失败: {str(e)}")
            raise e
    
    async def generate_service_image(self, service_name: str, service_description: str = '') -> Dict[str, Any]:
        """生成服务图片 - 主入口方法"""
        try:
            logger.info(f"🚀 开始火山引擎图片生成流程: {service_name}")
            
            # 1. 使用DeepSeek生成提示词
            prompt = await self.generate_prompt_with_deepseek(service_name, service_description)
            
            # 2. 使用火山引擎生成图片
            base64_image = self.generate_image_with_volcengine(prompt)

            return {
                'success': True,
                'imageUrl': base64_image,  # 现在返回base64格式的图片
                'prompt': prompt,
                'provider': '火山引擎',
                'model': self.model,
                'format': 'base64',  # 标识图片格式
                'timestamp': None  # 将在视图中设置
            }
            
        except Exception as e:
            logger.error(f"❌ 火山引擎图片生成失败: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'provider': '火山引擎',
                'model': self.model
            }


# 全局实例
volcengine_service = VolcengineImageService()
