"""
火山引擎AI图片生成API视图
"""
import json
import asyncio
import logging
from datetime import datetime
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils.decorators import method_decorator
from django.views import View
from .volcengine_service import volcengine_service

logger = logging.getLogger(__name__)


@method_decorator(csrf_exempt, name='dispatch')
class VolcengineImageGenerateView(View):
    """火山引擎图片生成API"""
    
    def post(self, request):
        try:
            data = json.loads(request.body)
            service_name = data.get('serviceName')
            service_description = data.get('serviceDescription', '')
            
            if not service_name:
                return JsonResponse({'error': '缺少服务名称'}, status=400)
            
            logger.info(f'🌋 火山引擎图片生成请求: {service_name}')
            
            # 使用asyncio运行异步方法
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            try:
                result = loop.run_until_complete(
                    volcengine_service.generate_service_image(service_name, service_description)
                )
            finally:
                loop.close()
            
            if result['success']:
                # 添加时间戳
                result['timestamp'] = datetime.now().isoformat()
                
                logger.info(f'✅ 火山引擎图片生成成功: {result["imageUrl"]}')
                
                return JsonResponse({
                    'success': True,
                    'imageUrl': result['imageUrl'],
                    'prompt': result['prompt'],
                    'provider': result['provider'],
                    'model': result['model'],
                    'timestamp': result['timestamp']
                })
            else:
                raise Exception(result['error'])
                
        except Exception as e:
            logger.error(f'❌ 火山引擎图片生成失败: {str(e)}')
            
            return JsonResponse({
                'success': False,
                'error': str(e),
                'message': '图片生成失败，请稍后重试'
            }, status=500)


@method_decorator(csrf_exempt, name='dispatch')
class VolcengineConfigView(View):
    """火山引擎配置检查API"""
    
    def get(self, request):
        try:
            is_configured = volcengine_service.is_configured()
            
            return JsonResponse({
                'success': True,
                'configured': is_configured,
                'provider': '火山引擎',
                'model': volcengine_service.model,
                'base_url': volcengine_service.base_url
            })
            
        except Exception as e:
            logger.error(f'❌ 火山引擎配置检查失败: {str(e)}')
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=500)


# 函数式视图（备用）
@csrf_exempt
@require_http_methods(["POST"])
def generate_image(request):
    """火山引擎图片生成API（函数式视图）"""
    return VolcengineImageGenerateView().post(request)


@csrf_exempt
@require_http_methods(["GET"])
def check_config(request):
    """检查火山引擎配置（函数式视图）"""
    return VolcengineConfigView().get(request)
