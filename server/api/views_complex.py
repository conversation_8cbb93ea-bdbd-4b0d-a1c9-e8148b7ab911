"""
API视图
"""
# 标准库导入
from datetime import datetime, date

# Django导入
from django.db.models import Q

# 第三方库导入
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import viewsets, status, filters
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated, AllowAny, IsAdminUser

# 本地应用导入
from apps.users.models import User, CustomerProfile
from apps.services.models import Service, ServiceCategory, ServicePackage
from apps.employees.models import Employee, WorkSchedule
from apps.appointments.models import Appointment, AppointmentReview
from apps.finance.models import FinanceRecord, DailyReport

from .serializers import (
    UserSerializer, CustomerProfileSerializer,
    ServiceSerializer, ServiceCategorySerializer,
    EmployeeSerializer, WorkScheduleSerializer,
    AppointmentSerializer, AppointmentReviewSerializer,
    FinanceRecordSerializer, DailyReportSerializer
)


class BaseViewSet(viewsets.ModelViewSet):
    """基础ViewSet，提供通用功能."""
    
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    permission_classes = [IsAuthenticated]
    
    def get_permissions(self):
        """根据action设置权限."""
        # 公开访问的actions
        public_actions = [
            'list', 'retrieve', 'therapists', 'available_therapists',
            'recommended', 'available_slots'
        ]

        if self.action in public_actions:
            permission_classes = [AllowAny]
        else:
            permission_classes = [IsAuthenticated]
        return [permission() for permission in permission_classes]


class UserViewSet(BaseViewSet):
    """用户ViewSet."""
    
    queryset = User.objects.all()
    serializer_class = UserSerializer
    search_fields = ['username', 'phone', 'email']
    filterset_fields = ['user_type', 'is_vip', 'gender']
    ordering_fields = ['created_at', 'last_login']
    ordering = ['-created_at']
    
    @action(detail=True, methods=['get'])
    def profile(self, request, pk=None):
        """获取用户档案."""
        user = self.get_object()
        if hasattr(user, 'customer_profile'):
            serializer = CustomerProfileSerializer(user.customer_profile)
            return Response(serializer.data)
        return Response({'detail': '用户档案不存在'}, status=status.HTTP_404_NOT_FOUND)
    
    @action(detail=True, methods=['post'])
    def create_profile(self, request, pk=None):
        """创建用户档案."""
        user = self.get_object()
        if hasattr(user, 'customer_profile'):
            return Response({'detail': '用户档案已存在'}, status=status.HTTP_400_BAD_REQUEST)
        
        serializer = CustomerProfileSerializer(data=request.data)
        if serializer.is_valid():
            serializer.save(user=user)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class ServiceCategoryViewSet(BaseViewSet):
    """服务分类ViewSet."""
    
    queryset = ServiceCategory.objects.filter(is_active=True)
    serializer_class = ServiceCategorySerializer
    search_fields = ['name', 'description']
    ordering_fields = ['sort_order', 'created_at']
    ordering = ['sort_order']


class ServiceViewSet(BaseViewSet):
    """服务项目ViewSet."""
    
    queryset = Service.objects.filter(status='active')
    serializer_class = ServiceSerializer
    search_fields = ['name', 'description']
    filterset_fields = ['category', 'is_recommended', 'status']
    ordering_fields = ['sort_order', 'price', 'rating', 'created_at']
    ordering = ['sort_order']
    
    @action(detail=False, methods=['get'])
    def recommended(self, request):
        """获取推荐服务."""
        queryset = self.get_queryset().filter(is_recommended=True)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def available_therapists(self, request, pk=None):
        """获取可提供此服务的技师."""
        service = self.get_object()
        therapists = Employee.objects.filter(
            is_therapist=True,
            status='active',
            is_available=True,
            specialties=service
        )
        serializer = EmployeeSerializer(therapists, many=True)
        return Response(serializer.data)


class EmployeeViewSet(BaseViewSet):
    """员工ViewSet."""
    
    queryset = Employee.objects.filter(status='active')
    serializer_class = EmployeeSerializer
    search_fields = ['name', 'employee_id', 'position']
    filterset_fields = ['is_therapist', 'department', 'status']
    ordering_fields = ['hire_date', 'rating', 'created_at']
    ordering = ['-created_at']
    
    @action(detail=False, methods=['get'])
    def therapists(self, request):
        """获取技师列表."""
        queryset = self.get_queryset().filter(is_therapist=True, is_available=True)
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def schedule(self, request, pk=None):
        """获取员工排班."""
        employee = self.get_object()
        schedules = WorkSchedule.objects.filter(employee=employee, is_active=True)
        serializer = WorkScheduleSerializer(schedules, many=True)
        return Response(serializer.data)
    
    @action(detail=True, methods=['get'])
    def available_slots(self, request, pk=None):
        """获取员工可预约时间段."""
        employee = self.get_object()
        date_str = request.query_params.get('date')
        
        if not date_str:
            return Response({'detail': '请提供日期参数'}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            target_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response({'detail': '日期格式错误'}, status=status.HTTP_400_BAD_REQUEST)
        
        # 这里可以添加获取可用时间段的逻辑
        # 简化版本，返回示例数据
        available_slots = [
            {'time': '09:00', 'available': True},
            {'time': '10:00', 'available': True},
            {'time': '11:00', 'available': False},
            {'time': '14:00', 'available': True},
            {'time': '15:00', 'available': True},
            {'time': '16:00', 'available': True},
        ]
        
        return Response(available_slots)


class AppointmentViewSet(BaseViewSet):
    """预约ViewSet."""

    queryset = Appointment.objects.all()
    serializer_class = AppointmentSerializer
    search_fields = ['appointment_no', 'customer__username', 'service__name']
    filterset_fields = ['status', 'payment_status', 'appointment_date', 'therapist']
    ordering_fields = ['appointment_date', 'appointment_time', 'created_at']
    ordering = ['-appointment_date', '-appointment_time']

    def get_permissions(self):
        """预约相关操作需要认证."""
        return [IsAuthenticated()]
    
    def get_queryset(self):
        """根据用户类型过滤查询集."""
        queryset = super().get_queryset()
        user = self.request.user
        
        if user.is_authenticated and user.is_customer:
            # 客户只能看到自己的预约
            queryset = queryset.filter(customer=user)
        
        return queryset
    
    @action(detail=True, methods=['post'])
    def confirm(self, request, pk=None):
        """确认预约."""
        appointment = self.get_object()
        
        if appointment.status != Appointment.StatusChoices.PENDING:
            return Response(
                {'detail': '只能确认待确认状态的预约'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        appointment.status = Appointment.StatusChoices.CONFIRMED
        appointment.confirmed_at = datetime.now()
        appointment.save()
        
        serializer = self.get_serializer(appointment)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def cancel(self, request, pk=None):
        """取消预约."""
        appointment = self.get_object()
        
        if not appointment.can_cancel:
            return Response(
                {'detail': '当前状态不允许取消'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        cancel_reason = request.data.get('cancel_reason', '')
        appointment.status = Appointment.StatusChoices.CANCELLED
        appointment.cancelled_at = datetime.now()
        appointment.cancel_reason = cancel_reason
        appointment.save()
        
        serializer = self.get_serializer(appointment)
        return Response(serializer.data)
    
    @action(detail=True, methods=['post'])
    def complete(self, request, pk=None):
        """完成预约."""
        appointment = self.get_object()
        
        if appointment.status != Appointment.StatusChoices.IN_PROGRESS:
            return Response(
                {'detail': '只能完成进行中状态的预约'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
        
        appointment.status = Appointment.StatusChoices.COMPLETED
        appointment.completed_at = datetime.now()
        appointment.save()
        
        serializer = self.get_serializer(appointment)
        return Response(serializer.data)


class FinanceRecordViewSet(BaseViewSet):
    """财务记录ViewSet."""

    queryset = FinanceRecord.objects.all()
    serializer_class = FinanceRecordSerializer
    search_fields = ['record_no', 'description', 'customer__username']
    filterset_fields = ['record_type', 'category', 'payment_method', 'record_date']
    ordering_fields = ['record_date', 'amount', 'created_at']
    ordering = ['-record_date', '-created_at']

    def get_permissions(self):
        """财务记录需要管理员权限."""
        return [IsAuthenticated(), IsAdminUser()]
    
    @action(detail=False, methods=['get'])
    def summary(self, request):
        """获取财务汇总."""
        from django.db.models import Sum
        
        # 获取查询参数
        start_date = request.query_params.get('start_date')
        end_date = request.query_params.get('end_date')
        
        queryset = self.get_queryset()
        
        if start_date:
            queryset = queryset.filter(record_date__gte=start_date)
        if end_date:
            queryset = queryset.filter(record_date__lte=end_date)
        
        # 计算汇总
        income_sum = queryset.filter(
            record_type=FinanceRecord.RecordTypeChoices.INCOME
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        expense_sum = queryset.filter(
            record_type=FinanceRecord.RecordTypeChoices.EXPENSE
        ).aggregate(total=Sum('amount'))['total'] or 0
        
        return Response({
            'total_income': income_sum,
            'total_expense': expense_sum,
            'net_profit': income_sum - expense_sum,
            'record_count': queryset.count()
        })
