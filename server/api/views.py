"""
API视图 - 包含认证和调试功能
"""
from django.http import HttpResponse, JsonResponse
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.contrib.auth import authenticate, login, logout
from django.contrib.auth.models import User
from rest_framework.decorators import api_view, permission_classes
from rest_framework.permissions import AllowAny
from rest_framework.response import Response
from rest_framework import status
from wxcloudrun.models import Employee
import os
import sys
import json
import uuid
import time
import requests
import uuid
import time
from django.core.cache import cache
from django.utils import timezone

def health_check(request):
    """健康检查视图 - 最简单的响应"""
    return HttpResponse("OK - Django is running!", status=200)

def debug_info(request):
    """调试信息视图 - 显示环境信息"""
    info = {
        'status': 'Django application is running',
        'python_version': sys.version,
        'environment_variables': {
            'MYSQL_ADDRESS': os.getenv('MYSQL_ADDRESS', 'Not Set'),
            'MYSQL_USERNAME': os.getenv('MYSQL_USERNAME', 'Not Set'),
            'MYSQL_PASSWORD': '***' if os.getenv('MYSQL_PASSWORD') else 'Not Set',
            'MYSQL_DATABASE': os.getenv('MYSQL_DATABASE', 'Not Set'),
        }
    }
    return JsonResponse(info, json_dumps_params={'indent': 2})

def root_view(request):
    """根路径视图"""
    html = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Django 云托管测试</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 40px; }
            .status { color: green; font-weight: bold; }
            .info { background: #f5f5f5; padding: 15px; border-radius: 5px; margin: 10px 0; }
        </style>
    </head>
    <body>
        <h1>🎉 Django 应用运行成功！</h1>
        <p class="status">✅ 服务状态: 正常运行</p>
        <div class="info">
            <h3>环境信息:</h3>
            <p><strong>Python版本:</strong> {python_version}</p>
            <p><strong>MYSQL_ADDRESS:</strong> {mysql_address}</p>
            <p><strong>MYSQL_USERNAME:</strong> {mysql_username}</p>
            <p><strong>MYSQL_DATABASE:</strong> {mysql_database}</p>
        </div>
        <div class="info">
            <h3>测试链接:</h3>
            <p><a href="/health/">健康检查</a></p>
            <p><a href="/debug/">调试信息</a></p>
        </div>
    </body>
    </html>
    """.format(
        python_version=sys.version.split()[0],
        mysql_address=os.getenv('MYSQL_ADDRESS', 'Not Set'),
        mysql_username=os.getenv('MYSQL_USERNAME', 'Not Set'),
        mysql_database=os.getenv('MYSQL_DATABASE', 'Not Set')
    )
    return HttpResponse(html)


@csrf_exempt
@require_http_methods(["POST"])
def admin_login(request):
    """管理员登录API"""
    try:
        data = json.loads(request.body)
        username = data.get('username')
        password = data.get('password')

        if not username or not password:
            return JsonResponse({
                'success': False,
                'message': '用户名和密码不能为空'
            }, status=400)

        # 验证用户凭据
        user = authenticate(request, username=username, password=password)

        if user is not None:
            # 检查是否是管理员
            try:
                employee = Employee.objects.get(user=user)
                if employee.role in ['admin', 'manager']:
                    login(request, user)

                    return JsonResponse({
                        'success': True,
                        'message': '登录成功',
                        'user': {
                            'id': user.id,
                            'username': user.username,
                            'name': employee.name,
                            'role': employee.role,
                            'phone': employee.phone
                        },
                        'token': f'admin_token_{user.id}_{user.username}'
                    })
                else:
                    return JsonResponse({
                        'success': False,
                        'message': '权限不足，仅管理员可登录'
                    }, status=403)

            except Employee.DoesNotExist:
                return JsonResponse({
                    'success': False,
                    'message': '员工信息不存在'
                }, status=404)
        else:
            return JsonResponse({
                'success': False,
                'message': '用户名或密码错误'
            }, status=401)

    except json.JSONDecodeError:
        return JsonResponse({
            'success': False,
            'message': '请求数据格式错误'
        }, status=400)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'登录失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def generate_qr_code(request):
    """生成微信扫码登录二维码"""
    try:
        # 生成唯一的登录会话ID
        session_id = str(uuid.uuid4())

        # 将会话ID存储到缓存中，设置5分钟过期
        cache.set(f'wechat_login_session_{session_id}', {
            'status': 'waiting',  # waiting, scanned, confirmed, expired
            'created_at': time.time(),
            'user_info': None
        }, timeout=300)  # 5分钟过期

        # 构造微信登录URL（这里需要配置微信开放平台的参数）
        # 注意：这需要在微信开放平台注册应用并获取AppID
        app_id = os.environ.get('WECHAT_APP_ID', 'your_wechat_app_id')
        redirect_uri = os.environ.get('WECHAT_REDIRECT_URI', 'https://your-domain.com/api/auth/wechat/callback')

        wechat_login_url = f"https://open.weixin.qq.com/connect/qrconnect?appid={app_id}&redirect_uri={redirect_uri}&response_type=code&scope=snsapi_login&state={session_id}#wechat_redirect"

        return JsonResponse({
            'success': True,
            'data': {
                'session_id': session_id,
                'qr_url': wechat_login_url,
                'expires_in': 300  # 5分钟
            }
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'生成二维码失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def check_qr_status(request):
    """检查二维码扫描状态"""
    try:
        session_id = request.GET.get('session_id')
        if not session_id:
            return JsonResponse({
                'success': False,
                'message': '缺少session_id参数'
            }, status=400)

        # 从缓存中获取会话状态
        session_data = cache.get(f'wechat_login_session_{session_id}')

        if not session_data:
            return JsonResponse({
                'success': False,
                'status': 'expired',
                'message': '二维码已过期'
            })

        return JsonResponse({
            'success': True,
            'status': session_data['status'],
            'user_info': session_data.get('user_info')
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'检查状态失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def wechat_callback(request):
    """微信登录回调处理"""
    try:
        code = request.GET.get('code')
        state = request.GET.get('state')  # 这是我们的session_id

        if not code or not state:
            return JsonResponse({
                'success': False,
                'message': '缺少必要参数'
            }, status=400)

        # 检查会话是否存在
        session_data = cache.get(f'wechat_login_session_{state}')
        if not session_data:
            return JsonResponse({
                'success': False,
                'message': '会话已过期'
            }, status=400)

        # 使用code获取access_token
        app_id = os.environ.get('WECHAT_APP_ID')
        app_secret = os.environ.get('WECHAT_APP_SECRET')

        token_url = f"https://api.weixin.qq.com/sns/oauth2/access_token?appid={app_id}&secret={app_secret}&code={code}&grant_type=authorization_code"

        token_response = requests.get(token_url)
        token_data = token_response.json()

        if 'access_token' not in token_data:
            return JsonResponse({
                'success': False,
                'message': '获取微信access_token失败'
            }, status=400)

        # 获取用户信息
        user_info_url = f"https://api.weixin.qq.com/sns/userinfo?access_token={token_data['access_token']}&openid={token_data['openid']}"

        user_response = requests.get(user_info_url)
        user_data = user_response.json()

        if 'openid' not in user_data:
            return JsonResponse({
                'success': False,
                'message': '获取微信用户信息失败'
            }, status=400)

        # 查找对应的员工账号
        try:
            employee = Employee.objects.get(wechat_openid=user_data['openid'])
            user = employee.user

            # 检查是否是管理员
            if employee.role in ['admin', 'manager']:
                # 更新微信信息
                employee.wechat_nickname = user_data.get('nickname', '')
                employee.wechat_avatar = user_data.get('headimgurl', '')
                employee.wechat_unionid = user_data.get('unionid', '')
                employee.save()

                # 登录用户
                login(request, user)

                session_data['status'] = 'confirmed'
                session_data['user_info'] = {
                    'id': user.id,
                    'username': user.username,
                    'name': employee.name,
                    'role': employee.role,
                    'phone': employee.phone,
                    'token': f'admin_token_{user.id}_{user.username}',
                    'wechat_info': {
                        'openid': user_data['openid'],
                        'nickname': user_data.get('nickname', ''),
                        'avatar': user_data.get('headimgurl', ''),
                        'unionid': user_data.get('unionid', '')
                    }
                }
            else:
                session_data['status'] = 'error'
                session_data['error'] = '权限不足，仅管理员可登录'

        except Employee.DoesNotExist:
            session_data['status'] = 'error'
            session_data['error'] = '该微信账号未绑定管理员账户，请联系系统管理员'

        # 更新缓存
        cache.set(f'wechat_login_session_{state}', session_data, timeout=300)

        return JsonResponse({
            'success': True,
            'message': '微信登录成功',
            'redirect_url': '/admin/login?wechat_success=1'
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'微信登录回调处理失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def admin_logout(request):
    """管理员登出API"""
    try:
        logout(request)
        return JsonResponse({
            'success': True,
            'message': '登出成功'
        })
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'登出失败: {str(e)}'
        }, status=500)


@require_http_methods(["GET"])
def admin_profile(request):
    """获取管理员信息API"""
    if not request.user.is_authenticated:
        return JsonResponse({
            'success': False,
            'message': '未登录'
        }, status=401)

    try:
        employee = Employee.objects.get(user=request.user)
        return JsonResponse({
            'success': True,
            'user': {
                'id': request.user.id,
                'username': request.user.username,
                'name': employee.name,
                'role': employee.role,
                'phone': employee.phone,
                'avatar': employee.avatar,
                'hire_date': employee.hire_date.isoformat()
            }
        })
    except Employee.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': '员工信息不存在'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'获取用户信息失败: {str(e)}'
        }, status=500)


# 微信绑定相关API
@csrf_exempt
@require_http_methods(["POST"])
def generate_binding_qr(request):
    """生成微信绑定二维码"""
    if not request.user.is_authenticated:
        return JsonResponse({
            'success': False,
            'message': '未登录'
        }, status=401)

    try:
        # 生成唯一的绑定会话ID
        session_id = str(uuid.uuid4())

        # 将绑定会话存储到缓存中
        cache.set(f'wechat_binding_session_{session_id}', {
            'status': 'waiting',  # waiting, scanned, confirmed, expired
            'user_id': request.user.id,
            'created_at': time.time()
        }, timeout=300)  # 5分钟过期

        # 构造微信绑定URL
        app_id = os.environ.get('WECHAT_APP_ID', 'demo_app_id')
        redirect_uri = os.environ.get('WECHAT_BIND_REDIRECT_URI', 'https://your-domain.com/api/wechat/bind/callback')

        wechat_bind_url = f"https://open.weixin.qq.com/connect/qrconnect?appid={app_id}&redirect_uri={redirect_uri}&response_type=code&scope=snsapi_login&state=bind_{session_id}#wechat_redirect"

        return JsonResponse({
            'success': True,
            'data': {
                'session_id': session_id,
                'qr_code': wechat_bind_url,
                'expires_in': 300
            }
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'生成绑定二维码失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def check_binding_status(request):
    """检查微信绑定状态"""
    if not request.user.is_authenticated:
        return JsonResponse({
            'success': False,
            'message': '未登录'
        }, status=401)

    try:
        session_id = request.GET.get('session_id')
        if not session_id:
            return JsonResponse({
                'success': False,
                'message': '缺少session_id参数'
            }, status=400)

        # 从缓存中获取绑定会话状态
        session_data = cache.get(f'wechat_binding_session_{session_id}')

        if not session_data:
            return JsonResponse({
                'success': False,
                'status': 'expired',
                'message': '绑定会话已过期'
            })

        return JsonResponse({
            'success': True,
            'status': session_data['status'],
            'openid': session_data.get('openid', ''),
            'nickname': session_data.get('nickname', ''),
            'avatar': session_data.get('avatar', '')
        })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'检查绑定状态失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def binding_status(request):
    """获取当前用户的微信绑定状态"""
    if not request.user.is_authenticated:
        return JsonResponse({
            'success': False,
            'message': '未登录'
        }, status=401)

    try:
        employee = Employee.objects.get(user=request.user)

        is_bound = bool(employee.wechat_openid)

        return JsonResponse({
            'success': True,
            'is_bound': is_bound,
            'openid': employee.wechat_openid if is_bound else '',
            'nickname': employee.wechat_nickname if is_bound else '',
            'avatar': employee.wechat_avatar if is_bound else '',
            'bind_time': employee.wechat_bind_time.isoformat() if employee.wechat_bind_time else ''
        })

    except Employee.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': '员工信息不存在'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'获取绑定状态失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["POST"])
def unbind_wechat(request):
    """解除微信绑定"""
    if not request.user.is_authenticated:
        return JsonResponse({
            'success': False,
            'message': '未登录'
        }, status=401)

    try:
        employee = Employee.objects.get(user=request.user)

        # 清除微信绑定信息
        employee.wechat_openid = ''
        employee.wechat_nickname = ''
        employee.wechat_avatar = ''
        employee.wechat_unionid = ''
        employee.wechat_bind_time = None
        employee.save()

        return JsonResponse({
            'success': True,
            'message': '微信账号解绑成功'
        })

    except Employee.DoesNotExist:
        return JsonResponse({
            'success': False,
            'message': '员工信息不存在'
        }, status=404)
    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'解绑失败: {str(e)}'
        }, status=500)


@csrf_exempt
@require_http_methods(["GET"])
def wechat_bind_callback(request):
    """微信绑定回调处理"""
    try:
        code = request.GET.get('code')
        state = request.GET.get('state')

        if not code or not state or not state.startswith('bind_'):
            return JsonResponse({
                'success': False,
                'message': '缺少必要参数'
            }, status=400)

        # 提取session_id
        session_id = state.replace('bind_', '')

        # 检查绑定会话是否存在
        session_data = cache.get(f'wechat_binding_session_{session_id}')
        if not session_data:
            return JsonResponse({
                'success': False,
                'message': '绑定会话已过期'
            }, status=400)

        # 使用code获取access_token
        app_id = os.environ.get('WECHAT_APP_ID', 'demo_app_id')
        app_secret = os.environ.get('WECHAT_APP_SECRET', 'demo_app_secret')

        # 模拟微信API调用（实际环境中需要真实调用）
        if app_id == 'demo_app_id':
            # 演示模式，使用模拟数据
            user_data = {
                'openid': f'demo_openid_{int(time.time())}',
                'nickname': '演示用户',
                'headimgurl': 'https://thirdwx.qlogo.cn/mmopen/demo.jpg',
                'unionid': f'demo_unionid_{int(time.time())}'
            }
        else:
            # 真实环境调用微信API
            token_url = f"https://api.weixin.qq.com/sns/oauth2/access_token?appid={app_id}&secret={app_secret}&code={code}&grant_type=authorization_code"

            token_response = requests.get(token_url)
            token_data = token_response.json()

            if 'access_token' not in token_data:
                return JsonResponse({
                    'success': False,
                    'message': '获取微信access_token失败'
                }, status=400)

            # 获取用户信息
            user_info_url = f"https://api.weixin.qq.com/sns/userinfo?access_token={token_data['access_token']}&openid={token_data['openid']}"

            user_response = requests.get(user_info_url)
            user_data = user_response.json()

            if 'openid' not in user_data:
                return JsonResponse({
                    'success': False,
                    'message': '获取微信用户信息失败'
                }, status=400)

        # 检查该微信号是否已被其他账号绑定
        existing_employee = Employee.objects.filter(wechat_openid=user_data['openid']).first()
        if existing_employee:
            return JsonResponse({
                'success': False,
                'message': '该微信账号已被其他管理员账户绑定'
            }, status=400)

        # 绑定微信账号到当前用户
        try:
            user = User.objects.get(id=session_data['user_id'])
            employee = Employee.objects.get(user=user)

            employee.wechat_openid = user_data['openid']
            employee.wechat_nickname = user_data.get('nickname', '')
            employee.wechat_avatar = user_data.get('headimgurl', '')
            employee.wechat_unionid = user_data.get('unionid', '')
            employee.wechat_bind_time = timezone.now()
            employee.save()

            # 更新绑定会话状态
            session_data['status'] = 'confirmed'
            session_data['openid'] = user_data['openid']
            session_data['nickname'] = user_data.get('nickname', '')
            session_data['avatar'] = user_data.get('headimgurl', '')
            cache.set(f'wechat_binding_session_{session_id}', session_data, timeout=300)

            return JsonResponse({
                'success': True,
                'message': '微信账号绑定成功'
            })

        except (User.DoesNotExist, Employee.DoesNotExist):
            return JsonResponse({
                'success': False,
                'message': '用户信息不存在'
            }, status=404)

    except Exception as e:
        return JsonResponse({
            'success': False,
            'message': f'微信绑定回调处理失败: {str(e)}'
        }, status=500)
