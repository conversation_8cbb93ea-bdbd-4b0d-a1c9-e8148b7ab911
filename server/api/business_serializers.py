"""
业务序列化器 - 基于当前模型结构的专业序列化器
严谨的数据验证和业务逻辑处理
"""

from rest_framework import serializers
from django.utils import timezone
from datetime import datetime, timedelta
from wxcloudrun.models import (
    Service, ServicePriceHistory, Therapist, Customer, Appointment,
    AppointmentReview, FinanceRecord, HealthTip, GlobalVersion
)


class ServiceSerializer(serializers.ModelSerializer):
    """服务项目序列化器"""

    # 添加status字段映射
    status = serializers.SerializerMethodField()

    # 添加价格历史记录计数
    price_history_count = serializers.SerializerMethodField()

    def get_status(self, obj):
        """将is_active字段映射为status"""
        return 'active' if obj.is_active else 'inactive'

    def get_price_history_count(self, obj):
        """获取价格修改历史记录数量"""
        return obj.price_history.count()

    class Meta:
        model = Service
        fields = [
            'id', 'name', 'description', 'price', 'commission', 'duration', 'image',
            'rating', 'review_count', 'is_popular', 'benefits',
            'contraindications', 'status', 'price_history_count', 'sort_order',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['rating', 'review_count', 'created_at', 'updated_at']
    
    def validate_price(self, value):
        """验证价格"""
        if value <= 0:
            raise serializers.ValidationError("价格必须大于0")
        if value > 9999:
            raise serializers.ValidationError("价格不能超过9999元")
        return value
    
    def validate_duration(self, value):
        """验证时长"""
        if value <= 0:
            raise serializers.ValidationError("服务时长必须大于0分钟")
        if value > 480:  # 8小时
            raise serializers.ValidationError("服务时长不能超过8小时")
        return value

    def validate(self, data):
        """验证价格和提成的关系"""
        price = data.get('price')
        commission = data.get('commission')

        # 如果是更新操作，获取实例的当前值
        if self.instance:
            price = price if price is not None else self.instance.price
            commission = commission if commission is not None else self.instance.commission

        if price is not None and commission is not None:
            if commission > price:
                raise serializers.ValidationError("提成不能超过服务费")

        return data

    def create(self, validated_data):
        """创建服务时处理status字段"""
        status = validated_data.pop('status', 'active')
        validated_data['is_active'] = (status == 'active')
        return super().create(validated_data)

    def update(self, instance, validated_data):
        """更新服务时处理status字段"""
        status = validated_data.pop('status', None)
        if status is not None:
            validated_data['is_active'] = (status == 'active')
        return super().update(instance, validated_data)


class ServicePriceHistorySerializer(serializers.ModelSerializer):
    """服务价格修改历史序列化器"""

    change_type_display = serializers.CharField(source='get_change_type_display', read_only=True)
    changed_at_formatted = serializers.SerializerMethodField()

    class Meta:
        model = ServicePriceHistory
        fields = [
            'id', 'old_price', 'new_price', 'old_commission', 'new_commission',
            'change_type', 'change_type_display', 'changed_at', 'changed_at_formatted'
        ]
        read_only_fields = ['id', 'changed_at']

    def get_changed_at_formatted(self, obj):
        """格式化修改时间"""
        return obj.changed_at.strftime('%Y-%m-%d %H:%M:%S')


class TherapistSerializer(serializers.ModelSerializer):
    """技师序列化器"""
    level_display = serializers.CharField(source='get_level_display', read_only=True)
    
    class Meta:
        model = Therapist
        fields = [
            'id', 'name', 'employee_id', 'phone', 'avatar', 'level', 'level_display',
            'specialty', 'experience', 'specialties', 'certifications', 
            'working_hours', 'is_active', 'service_count', 'rating',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['service_count', 'rating', 'created_at', 'updated_at']
    
    def validate_employee_id(self, value):
        """验证员工编号"""
        if not value.startswith('T'):
            raise serializers.ValidationError("员工编号必须以T开头")
        return value
    
    def validate_experience(self, value):
        """验证从业年限"""
        if value < 0:
            raise serializers.ValidationError("从业年限不能为负数")
        if value > 50:
            raise serializers.ValidationError("从业年限不能超过50年")
        return value


class CustomerSerializer(serializers.ModelSerializer):
    """客户序列化器"""
    
    class Meta:
        model = Customer
        fields = [
            'id', 'name', 'phone', 'avatar', 'gender', 'birth_date',
            'address', 'notes', 'total_spent', 'visit_count', 'last_visit',
            'is_vip', 'vip_expire_date', 'created_at', 'updated_at'
        ]
        read_only_fields = ['total_spent', 'visit_count', 'last_visit', 'created_at', 'updated_at']
    
    def validate_phone(self, value):
        """验证手机号"""
        import re
        if not re.match(r'^1[3-9]\d{9}$', value):
            raise serializers.ValidationError("请输入有效的手机号码")
        return value


class AppointmentCreateSerializer(serializers.ModelSerializer):
    """预约创建序列化器"""
    
    class Meta:
        model = Appointment
        fields = [
            'customer', 'service', 'therapist', 'appointment_time',
            'customer_requirements', 'notes'
        ]
    
    def validate_appointment_time(self, value):
        """验证预约时间"""
        now = timezone.now()
        
        # 不能预约过去的时间
        if value <= now:
            raise serializers.ValidationError("不能预约过去的时间")
        
        # 不能预约太远的时间（30天内）
        if value > now + timedelta(days=30):
            raise serializers.ValidationError("只能预约30天内的时间")
        
        # 营业时间检查（9:00-21:00）
        if value.hour < 9 or value.hour >= 21:
            raise serializers.ValidationError("营业时间为9:00-21:00")
        
        return value
    
    def validate(self, attrs):
        """整体验证"""
        appointment_time = attrs['appointment_time']
        therapist = attrs['therapist']
        service = attrs['service']
        
        # 检查技师是否有冲突的预约
        end_time = appointment_time + timedelta(minutes=service.duration)
        
        conflicting_appointments = Appointment.objects.filter(
            therapist=therapist,
            status__in=['confirmed', 'in_progress'],
            appointment_time__lt=end_time,
            appointment_time__gte=appointment_time - timedelta(minutes=120)  # 考虑前后2小时
        )
        
        if conflicting_appointments.exists():
            raise serializers.ValidationError("该时间段技师已有预约，请选择其他时间")
        
        return attrs
    
    def create(self, validated_data):
        """创建预约"""
        service = validated_data['service']
        
        # 设置价格信息
        validated_data['original_price'] = service.price
        validated_data['final_price'] = service.price
        validated_data['duration'] = service.duration
        
        return super().create(validated_data)


class AppointmentSerializer(serializers.ModelSerializer):
    """预约详情序列化器"""
    customer_name = serializers.CharField(source='customer.name', read_only=True)
    customer_phone = serializers.CharField(source='customer.phone', read_only=True)
    service_name = serializers.CharField(source='service.name', read_only=True)
    therapist_name = serializers.CharField(source='therapist.name', read_only=True)
    status_display = serializers.CharField(source='get_status_display', read_only=True)
    payment_status_display = serializers.CharField(source='get_payment_status_display', read_only=True)
    can_cancel = serializers.SerializerMethodField()
    end_time = serializers.SerializerMethodField()
    
    class Meta:
        model = Appointment
        fields = [
            'id', 'customer', 'customer_name', 'customer_phone',
            'service', 'service_name', 'therapist', 'therapist_name',
            'appointment_time', 'end_time', 'duration', 'status', 'status_display',
            'payment_status', 'payment_status_display', 'original_price',
            'discount_amount', 'final_price', 'actual_start_time', 'actual_end_time',
            'notes', 'customer_requirements', 'cancellation_reason',
            'can_cancel', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at', 'can_cancel', 'end_time']
    
    def get_can_cancel(self, obj):
        """获取是否可以取消"""
        return obj.can_cancel()
    
    def get_end_time(self, obj):
        """获取结束时间"""
        return obj.get_end_time()


class AppointmentReviewSerializer(serializers.ModelSerializer):
    """预约评价序列化器"""
    
    class Meta:
        model = AppointmentReview
        fields = [
            'id', 'appointment', 'rating', 'comment', 'service_rating',
            'therapist_rating', 'environment_rating', 'created_at'
        ]
        read_only_fields = ['created_at']
    
    def validate_appointment(self, value):
        """验证预约"""
        if value.status != 'completed':
            raise serializers.ValidationError("只能对已完成的预约进行评价")
        
        if hasattr(value, 'appointmentreview'):
            raise serializers.ValidationError("该预约已经评价过了")
        
        return value


class HealthTipSerializer(serializers.ModelSerializer):
    """健康小贴士序列化器"""
    author_name = serializers.CharField(source='author.name', read_only=True)
    
    class Meta:
        model = HealthTip
        fields = [
            'id', 'title', 'content', 'image', 'category', 'tags',
            'is_published', 'view_count', 'sort_order', 'author',
            'author_name', 'created_at', 'updated_at'
        ]
        read_only_fields = ['view_count', 'created_at', 'updated_at']


class GlobalVersionSerializer(serializers.ModelSerializer):
    """全局版本序列化器"""
    
    class Meta:
        model = GlobalVersion
        fields = [
            'id', 'version', 'deployment_tag', 'deployment_time',
            'git_commit', 'django_version', 'is_active', 'notes'
        ]
        read_only_fields = ['deployment_time']


# 统计相关序列化器
class DashboardStatsSerializer(serializers.Serializer):
    """仪表盘统计序列化器"""
    today_appointments = serializers.IntegerField()
    today_income = serializers.DecimalField(max_digits=10, decimal_places=2)
    total_customers = serializers.IntegerField()
    active_therapists = serializers.IntegerField()
    pending_appointments = serializers.IntegerField()
    completed_appointments_today = serializers.IntegerField()
    
    # 图表数据
    weekly_income = serializers.ListField(child=serializers.DecimalField(max_digits=10, decimal_places=2))
    service_distribution = serializers.ListField(child=serializers.DictField())
    therapist_performance = serializers.ListField(child=serializers.DictField())


class TimeSlotSerializer(serializers.Serializer):
    """时间段序列化器"""
    time = serializers.TimeField()
    available = serializers.BooleanField()
    therapist_id = serializers.IntegerField(required=False)
    therapist_name = serializers.CharField(required=False)
