"""
API URL配置
"""
# Django导入
from django.urls import path, include

# 第三方库导入
from rest_framework.routers import DefaultRouter

# 本地应用导入
from api.views import (
    UserViewSet,
    ServiceViewSet,
    ServiceCategoryViewSet,
    EmployeeViewSet,
    AppointmentViewSet,
    FinanceRecordViewSet,
)

# 创建路由器
router = DefaultRouter()

# 注册ViewSet
router.register(r'users', UserViewSet)
router.register(r'services', ServiceViewSet)
router.register(r'service-categories', ServiceCategoryViewSet)
router.register(r'employees', EmployeeViewSet)
router.register(r'appointments', AppointmentViewSet)
router.register(r'finance-records', FinanceRecordViewSet)

urlpatterns = [
    # ViewSet路由
    path('', include(router.urls)),
]
