#!/usr/bin/env python3
"""
后端代码质量检查脚本
用于CI/CD流水线中验证Django后端代码质量
"""

import os
import sys
import subprocess
import django
from django.conf import settings
from django.core.management import execute_from_command_line

def check_django_setup():
    """检查Django配置"""
    print("🔍 检查Django配置...")
    
    try:
        os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'wxcloudrun.settings')
        django.setup()
        print("✅ Django配置正确")
        return True
    except Exception as e:
        print(f"❌ Django配置错误: {e}")
        return False

def check_migrations():
    """检查数据库迁移"""
    print("🔍 检查数据库迁移...")
    
    try:
        # 检查是否有未应用的迁移
        result = subprocess.run([
            sys.executable, 'manage.py', 'showmigrations', '--plan'
        ], capture_output=True, text=True, cwd=os.path.dirname(__file__))
        
        if result.returncode == 0:
            print("✅ 数据库迁移检查通过")
            return True
        else:
            print(f"❌ 数据库迁移检查失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 数据库迁移检查异常: {e}")
        return False

def check_code_style():
    """检查代码风格"""
    print("🔍 检查Python代码风格...")
    
    # 检查基本的Python语法
    python_files = []
    for root, dirs, files in os.walk('.'):
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    syntax_errors = 0
    for py_file in python_files:
        try:
            with open(py_file, 'r', encoding='utf-8') as f:
                compile(f.read(), py_file, 'exec')
        except SyntaxError as e:
            print(f"❌ 语法错误 {py_file}: {e}")
            syntax_errors += 1
        except Exception:
            # 忽略其他编译错误（如导入错误）
            pass
    
    if syntax_errors == 0:
        print("✅ Python代码语法检查通过")
        return True
    else:
        print(f"❌ 发现 {syntax_errors} 个语法错误")
        return False

def check_requirements():
    """检查依赖文件"""
    print("🔍 检查requirements.txt...")
    
    if os.path.exists('requirements.txt'):
        with open('requirements.txt', 'r') as f:
            requirements = f.read()
            
        # 检查关键依赖
        required_packages = ['Django==3.2.8', 'djangorestframework']
        missing_packages = []
        
        for package in required_packages:
            if package not in requirements:
                missing_packages.append(package)
        
        if not missing_packages:
            print("✅ requirements.txt 检查通过")
            return True
        else:
            print(f"❌ requirements.txt 缺少依赖: {missing_packages}")
            return False
    else:
        print("❌ requirements.txt 文件不存在")
        return False

def main():
    """主函数"""
    print("🚀 开始后端代码质量检查")
    print("=" * 50)
    
    checks = [
        check_requirements,
        check_code_style,
        check_django_setup,
        check_migrations
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        try:
            if check():
                passed += 1
            print()  # 空行分隔
        except Exception as e:
            print(f"❌ 检查异常: {e}")
            print()
    
    print("=" * 50)
    print(f"📊 检查结果: {passed}/{total} 通过")
    
    if passed == total:
        print("✅ 所有代码质量检查通过")
        sys.exit(0)
    else:
        print("❌ 代码质量检查失败")
        sys.exit(1)

if __name__ == '__main__':
    main()
