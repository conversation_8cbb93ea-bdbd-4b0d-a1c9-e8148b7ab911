#!/usr/bin/env python
"""
创建管理员用户脚本
用于创建root管理员账号
"""

import os
import sys
import django
from datetime import date

# 设置Django环境
sys.path.append(os.path.dirname(os.path.abspath(__file__)))
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'wxcloudrun.settings')
django.setup()

from django.contrib.auth.models import User
from wxcloudrun.models import Employee

def create_admin_user():
    """创建管理员用户"""
    try:
        # 检查root用户是否已存在
        if User.objects.filter(username='root').exists():
            print("✅ root用户已存在")
            user = User.objects.get(username='root')
        else:
            # 创建root用户
            user = User.objects.create_user(
                username='root',
                password='13210583333',
                email='<EMAIL>',
                first_name='系统',
                last_name='管理员',
                is_staff=True,
                is_superuser=True
            )
            print("✅ 创建root用户成功")
        
        # 检查员工记录是否已存在
        if Employee.objects.filter(user=user).exists():
            print("✅ 员工记录已存在")
            employee = Employee.objects.get(user=user)
        else:
            # 创建员工记录
            employee = Employee.objects.create(
                user=user,
                name='系统管理员',
                phone='13210583333',
                role='admin',
                avatar='',
                hire_date=date.today(),
                salary=0.00  # 管理员工资设为0
            )
            print("✅ 创建员工记录成功")
        
        print(f"""
🎉 管理员账号创建完成！

登录信息：
- 用户名: {user.username}
- 密码: 13210583333
- 姓名: {employee.name}
- 角色: {employee.get_role_display()}
- 电话: {employee.phone}
        """)
        
        return True
        
    except Exception as e:
        print(f"❌ 创建管理员用户失败: {str(e)}")
        return False

if __name__ == '__main__':
    create_admin_user()
