{"summary": {"type": "backend-test", "total_tests": 20, "success_count": 10, "failure_count": 10, "success_rate": 50, "duration": 5.7095701694488525, "timestamp": "2025-07-28T21:12:07.354607"}, "categories": {"服务器状态": {"total": 3, "success": 1}, "数据库": {"total": 1, "success": 0}, "认证": {"total": 1, "success": 0}, "API端点": {"total": 5, "success": 4}, "小程序API": {"total": 4, "success": 4}, "CRUD操作": {"total": 1, "success": 0}, "权限控制": {"total": 4, "success": 0}, "错误处理": {"total": 1, "success": 1}}, "results": [{"category": "服务器状态", "test": "根路径访问", "success": true, "details": "状态码: 200", "timestamp": "2025-07-28T21:12:01.662148"}, {"category": "服务器状态", "test": "API根路径", "success": false, "details": "状态码: 404", "timestamp": "2025-07-28T21:12:01.680889"}, {"category": "服务器状态", "test": "调试接口", "success": false, "details": "响应: {'status': 'Django application is running', 'python_version': '3.9.6 (default, Apr 30 2025, 02:07:17) \\n[Clang 17.0.0 (clang-1700.0.13.5)]', 'environment_variables': {'MYSQL_ADDRESS': 'Not Set', 'MYSQL_USERNAME': 'Not Set', 'MYSQL_PASSWORD': 'Not Set', 'MYSQL_DATABASE': 'Not Set'}}", "timestamp": "2025-07-28T21:12:01.682694"}, {"category": "数据库", "test": "连接状态", "success": false, "details": "数据库状态: {}", "timestamp": "2025-07-28T21:12:01.684099"}, {"category": "认证", "test": "管理员登录", "success": false, "details": "登录失败，状态码: 404", "timestamp": "2025-07-28T21:12:01.689472"}, {"category": "API端点", "test": "服务管理", "success": true, "details": "状态码: 200", "timestamp": "2025-07-28T21:12:02.940719"}, {"category": "API端点", "test": "客户管理", "success": true, "details": "状态码: 200", "timestamp": "2025-07-28T21:12:03.272500"}, {"category": "API端点", "test": "技师管理", "success": true, "details": "状态码: 200", "timestamp": "2025-07-28T21:12:03.671452"}, {"category": "API端点", "test": "预约管理", "success": true, "details": "状态码: 200", "timestamp": "2025-07-28T21:12:04.568036"}, {"category": "API端点", "test": "用户管理", "success": false, "details": "状态码: 404", "timestamp": "2025-07-28T21:12:04.573763"}, {"category": "小程序API", "test": "小程序服务列表", "success": true, "details": "状态码: 404", "timestamp": "2025-07-28T21:12:04.577663"}, {"category": "小程序API", "test": "小程序技师列表", "success": true, "details": "状态码: 404", "timestamp": "2025-07-28T21:12:04.581608"}, {"category": "小程序API", "test": "小程序登录", "success": true, "details": "状态码: 404", "timestamp": "2025-07-28T21:12:04.584982"}, {"category": "小程序API", "test": "小程序预约", "success": true, "details": "状态码: 404", "timestamp": "2025-07-28T21:12:04.588932"}, {"category": "CRUD操作", "test": "前置条件", "success": false, "details": "需要管理员token", "timestamp": "2025-07-28T21:12:04.588956"}, {"category": "权限控制", "test": "未认证访问/api/v1/services/", "success": false, "details": "状态码: 200", "timestamp": "2025-07-28T21:12:05.783151"}, {"category": "权限控制", "test": "未认证访问/api/v1/customers/", "success": false, "details": "状态码: 200", "timestamp": "2025-07-28T21:12:06.115252"}, {"category": "权限控制", "test": "未认证访问/api/v1/therapists/", "success": false, "details": "状态码: 200", "timestamp": "2025-07-28T21:12:06.441356"}, {"category": "权限控制", "test": "未认证访问/api/v1/appointments/", "success": false, "details": "状态码: 200", "timestamp": "2025-07-28T21:12:07.350535"}, {"category": "错误处理", "test": "404错误", "success": true, "details": "状态码: 404", "timestamp": "2025-07-28T21:12:07.354574"}]}