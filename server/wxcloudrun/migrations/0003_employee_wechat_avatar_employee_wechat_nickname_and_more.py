# Generated by Django 4.2.7 on 2025-07-07 09:39

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('wxcloudrun', '0002_remove_appointment_price_remove_service_category_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='employee',
            name='wechat_avatar',
            field=models.URLField(blank=True, verbose_name='微信头像'),
        ),
        migrations.AddField(
            model_name='employee',
            name='wechat_nickname',
            field=models.CharField(blank=True, max_length=100, verbose_name='微信昵称'),
        ),
        migrations.AddField(
            model_name='employee',
            name='wechat_openid',
            field=models.CharField(blank=True, max_length=100, null=True, unique=True, verbose_name='微信OpenID'),
        ),
        migrations.AddField(
            model_name='employee',
            name='wechat_unionid',
            field=models.CharField(blank=True, max_length=100, null=True, verbose_name='微信UnionID'),
        ),
    ]
