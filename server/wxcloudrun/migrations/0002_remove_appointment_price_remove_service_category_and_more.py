# Generated by Django 4.2.7 on 2025-07-05 10:54

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('wxcloudrun', '0001_initial'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='appointment',
            name='price',
        ),
        migrations.RemoveField(
            model_name='service',
            name='category',
        ),
        migrations.RemoveField(
            model_name='therapist',
            name='price',
        ),
        migrations.AddField(
            model_name='appointment',
            name='cancellation_reason',
            field=models.TextField(blank=True, verbose_name='取消原因'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='customer_requirements',
            field=models.TextField(blank=True, verbose_name='客户需求'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='discount_amount',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='优惠金额'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='final_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='实付金额'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='appointment',
            name='original_price',
            field=models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='原价'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='appointment',
            name='payment_status',
            field=models.CharField(choices=[('unpaid', '未支付'), ('paid', '已支付'), ('refunded', '已退款')], default='unpaid', max_length=20, verbose_name='支付状态'),
        ),
        migrations.AddField(
            model_name='service',
            name='benefits',
            field=models.JSONField(default=list, verbose_name='功效列表'),
        ),
        migrations.AddField(
            model_name='service',
            name='contraindications',
            field=models.JSONField(default=list, verbose_name='禁忌症列表'),
        ),
        migrations.AddField(
            model_name='service',
            name='is_popular',
            field=models.BooleanField(default=False, verbose_name='是否热门'),
        ),
        migrations.AddField(
            model_name='service',
            name='rating',
            field=models.DecimalField(decimal_places=2, default=5.0, max_digits=3, verbose_name='评分'),
        ),
        migrations.AddField(
            model_name='service',
            name='review_count',
            field=models.IntegerField(default=0, verbose_name='评价数量'),
        ),
        migrations.AddField(
            model_name='therapist',
            name='certifications',
            field=models.JSONField(default=list, verbose_name='资质证书列表'),
        ),
        migrations.AddField(
            model_name='therapist',
            name='employee_id',
            field=models.CharField(default='T001', max_length=20, unique=True, verbose_name='员工编号'),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='therapist',
            name='specialties',
            field=models.JSONField(default=list, verbose_name='专业技能列表'),
        ),
        migrations.AddField(
            model_name='therapist',
            name='working_hours',
            field=models.JSONField(default=dict, verbose_name='工作时间安排'),
        ),
        migrations.AddConstraint(
            model_name='appointment',
            constraint=models.CheckConstraint(check=models.Q(('final_price__gte', 0)), name='positive_final_price'),
        ),
        migrations.AddConstraint(
            model_name='appointment',
            constraint=models.CheckConstraint(check=models.Q(('discount_amount__gte', 0)), name='positive_discount'),
        ),
    ]
