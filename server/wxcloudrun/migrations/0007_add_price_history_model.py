# Generated by Django 3.2.8 on 2025-07-13 19:37

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('wxcloudrun', '0006_add_soft_delete_fields'),
    ]

    operations = [
        migrations.CreateModel(
            name='ServicePriceHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('old_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='原价格')),
                ('new_price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='新价格')),
                ('old_commission', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='原提成')),
                ('new_commission', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='新提成')),
                ('change_type', models.CharField(choices=[('price', '价格修改'), ('commission', '提成修改'), ('both', '价格和提成修改')], max_length=20, verbose_name='修改类型')),
                ('changed_at', models.DateTimeField(auto_now_add=True, verbose_name='修改时间')),
                ('service', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='price_history', to='wxcloudrun.service', verbose_name='服务')),
            ],
            options={
                'verbose_name': '服务价格修改历史',
                'verbose_name_plural': '服务价格修改历史',
                'db_table': 'service_price_history',
                'ordering': ['-changed_at'],
            },
        ),
    ]
