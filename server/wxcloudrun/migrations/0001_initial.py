# Generated by Django 4.2.7 on 2025-07-05 10:25

import datetime
from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Appointment',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('appointment_time', models.DateTimeField(verbose_name='预约时间')),
                ('duration', models.IntegerField(verbose_name='服务时长(分钟)')),
                ('status', models.CharField(choices=[('pending', '待确认'), ('confirmed', '已确认'), ('in_progress', '服务中'), ('completed', '已完成'), ('cancelled', '已取消'), ('no_show', '未到店')], default='pending', max_length=20, verbose_name='状态')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='服务价格')),
                ('actual_start_time', models.DateTimeField(blank=True, null=True, verbose_name='实际开始时间')),
                ('actual_end_time', models.DateTimeField(blank=True, null=True, verbose_name='实际结束时间')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '预约',
                'verbose_name_plural': '预约',
                'db_table': 'appointments',
                'ordering': ['-appointment_time'],
            },
        ),
        migrations.CreateModel(
            name='Counters',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('count', models.IntegerField(default=0)),
                ('createdAt', models.DateTimeField(default=datetime.datetime.now)),
                ('updatedAt', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'Counters',
            },
        ),
        migrations.CreateModel(
            name='Customer',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='客户姓名')),
                ('phone', models.CharField(max_length=20, unique=True, verbose_name='手机号')),
                ('avatar', models.URLField(blank=True, verbose_name='头像')),
                ('gender', models.CharField(blank=True, choices=[('male', '男'), ('female', '女')], max_length=10, verbose_name='性别')),
                ('birth_date', models.DateField(blank=True, null=True, verbose_name='出生日期')),
                ('address', models.TextField(blank=True, verbose_name='地址')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('total_spent', models.DecimalField(decimal_places=2, default=0, max_digits=10, verbose_name='总消费')),
                ('visit_count', models.IntegerField(default=0, verbose_name='到店次数')),
                ('last_visit', models.DateTimeField(blank=True, null=True, verbose_name='最后到店时间')),
                ('is_vip', models.BooleanField(default=False, verbose_name='是否VIP')),
                ('vip_expire_date', models.DateField(blank=True, null=True, verbose_name='VIP到期日期')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '客户',
                'verbose_name_plural': '客户',
                'db_table': 'customers',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='Employee',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='员工姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='联系电话')),
                ('role', models.CharField(choices=[('admin', '管理员'), ('manager', '店长'), ('receptionist', '前台'), ('therapist', '技师'), ('cleaner', '清洁员')], max_length=20, verbose_name='职位')),
                ('avatar', models.URLField(blank=True, verbose_name='头像')),
                ('hire_date', models.DateField(verbose_name='入职日期')),
                ('salary', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='基本工资')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否在职')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL, verbose_name='用户账号')),
            ],
            options={
                'verbose_name': '员工',
                'verbose_name_plural': '员工',
                'db_table': 'employees',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='GlobalVersion',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('version', models.IntegerField(default=1, verbose_name='全局版本号')),
                ('deployment_tag', models.CharField(help_text='如django-t3qr-014', max_length=100, verbose_name='部署标签')),
                ('deployment_time', models.DateTimeField(auto_now_add=True, verbose_name='部署时间')),
                ('git_commit', models.CharField(blank=True, max_length=40, verbose_name='Git提交哈希')),
                ('django_version', models.CharField(default='3.2.8', max_length=20, verbose_name='Django版本')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否为当前活跃版本')),
                ('notes', models.TextField(blank=True, verbose_name='版本说明')),
            ],
            options={
                'verbose_name': '全局版本',
                'verbose_name_plural': '全局版本',
                'db_table': 'global_versions',
                'ordering': ['-deployment_time'],
            },
        ),
        migrations.CreateModel(
            name='ServiceCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='分类名称')),
                ('description', models.TextField(blank=True, verbose_name='分类描述')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '服务分类',
                'verbose_name_plural': '服务分类',
                'db_table': 'service_categories',
                'ordering': ['sort_order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='Therapist',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=50, verbose_name='技师姓名')),
                ('phone', models.CharField(max_length=20, verbose_name='联系电话')),
                ('avatar', models.URLField(blank=True, verbose_name='头像')),
                ('level', models.IntegerField(choices=[(1, '一星技师'), (2, '二星技师'), (3, '三星技师'), (4, '四星技师'), (5, '五星技师')], default=1, verbose_name='技师等级')),
                ('specialty', models.TextField(verbose_name='专长描述')),
                ('experience', models.IntegerField(verbose_name='从业年限')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='服务价格')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否在职')),
                ('service_count', models.IntegerField(default=0, verbose_name='服务次数')),
                ('rating', models.DecimalField(decimal_places=2, default=5.0, max_digits=3, verbose_name='评分')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
            ],
            options={
                'verbose_name': '技师',
                'verbose_name_plural': '技师',
                'db_table': 'therapists',
                'ordering': ['-level', 'id'],
            },
        ),
        migrations.CreateModel(
            name='Service',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=100, verbose_name='服务名称')),
                ('description', models.TextField(verbose_name='服务描述')),
                ('price', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='价格')),
                ('duration', models.IntegerField(verbose_name='服务时长(分钟)')),
                ('image', models.URLField(blank=True, verbose_name='服务图片')),
                ('is_active', models.BooleanField(default=True, verbose_name='是否启用')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='wxcloudrun.servicecategory', verbose_name='服务分类')),
            ],
            options={
                'verbose_name': '服务项目',
                'verbose_name_plural': '服务项目',
                'db_table': 'services',
                'ordering': ['sort_order', 'id'],
            },
        ),
        migrations.CreateModel(
            name='HealthTip',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('title', models.CharField(max_length=100, verbose_name='标题')),
                ('content', models.TextField(verbose_name='内容')),
                ('image', models.URLField(blank=True, verbose_name='配图')),
                ('category', models.CharField(max_length=50, verbose_name='分类')),
                ('tags', models.CharField(blank=True, max_length=200, verbose_name='标签(逗号分隔)')),
                ('is_published', models.BooleanField(default=True, verbose_name='是否发布')),
                ('view_count', models.IntegerField(default=0, verbose_name='浏览次数')),
                ('sort_order', models.IntegerField(default=0, verbose_name='排序')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('author', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='wxcloudrun.employee', verbose_name='作者')),
            ],
            options={
                'verbose_name': '健康小贴士',
                'verbose_name_plural': '健康小贴士',
                'db_table': 'health_tips',
                'ordering': ['-sort_order', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='FinanceRecord',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('type', models.CharField(choices=[('income', '收入'), ('expense', '支出')], max_length=10, verbose_name='类型')),
                ('category', models.CharField(choices=[('service', '服务收入'), ('product', '产品销售'), ('rent', '房租'), ('salary', '工资'), ('utilities', '水电费'), ('supplies', '耗材'), ('equipment', '设备'), ('marketing', '营销费用'), ('other', '其他')], max_length=20, verbose_name='分类')),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, verbose_name='金额')),
                ('description', models.CharField(max_length=200, verbose_name='描述')),
                ('date', models.DateField(verbose_name='日期')),
                ('notes', models.TextField(blank=True, verbose_name='备注')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('updated_at', models.DateTimeField(auto_now=True, verbose_name='更新时间')),
                ('appointment', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, to='wxcloudrun.appointment', verbose_name='关联预约')),
                ('employee', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='wxcloudrun.employee', verbose_name='操作员工')),
            ],
            options={
                'verbose_name': '财务记录',
                'verbose_name_plural': '财务记录',
                'db_table': 'finance_records',
                'ordering': ['-date', '-created_at'],
            },
        ),
        migrations.CreateModel(
            name='AppointmentReview',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(choices=[(1, '1星'), (2, '2星'), (3, '3星'), (4, '4星'), (5, '5星')], verbose_name='评分')),
                ('comment', models.TextField(blank=True, verbose_name='评价内容')),
                ('service_rating', models.IntegerField(choices=[(1, '1星'), (2, '2星'), (3, '3星'), (4, '4星'), (5, '5星')], verbose_name='服务评分')),
                ('therapist_rating', models.IntegerField(choices=[(1, '1星'), (2, '2星'), (3, '3星'), (4, '4星'), (5, '5星')], verbose_name='技师评分')),
                ('environment_rating', models.IntegerField(choices=[(1, '1星'), (2, '2星'), (3, '3星'), (4, '4星'), (5, '5星')], verbose_name='环境评分')),
                ('created_at', models.DateTimeField(auto_now_add=True, verbose_name='创建时间')),
                ('appointment', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to='wxcloudrun.appointment', verbose_name='预约')),
            ],
            options={
                'verbose_name': '预约评价',
                'verbose_name_plural': '预约评价',
                'db_table': 'appointment_reviews',
                'ordering': ['-created_at'],
            },
        ),
        migrations.AddField(
            model_name='appointment',
            name='customer',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='wxcloudrun.customer', verbose_name='客户'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='service',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='wxcloudrun.service', verbose_name='服务项目'),
        ),
        migrations.AddField(
            model_name='appointment',
            name='therapist',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='wxcloudrun.therapist', verbose_name='技师'),
        ),
    ]
