# Generated by Django 3.2.8 on 2025-07-13 18:20

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('wxcloudrun', '0005_add_commission_field'),
    ]

    operations = [
        migrations.AddField(
            model_name='service',
            name='deleted_at',
            field=models.DateTimeField(blank=True, null=True, verbose_name='删除时间'),
        ),
        migrations.AddField(
            model_name='service',
            name='is_deleted',
            field=models.BooleanField(default=False, verbose_name='是否已删除'),
        ),
        migrations.AddConstraint(
            model_name='service',
            constraint=models.UniqueConstraint(condition=models.Q(('is_deleted', False)), fields=('name',), name='unique_service_name_not_deleted'),
        ),
    ]
