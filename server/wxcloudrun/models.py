from datetime import datetime
from django.db import models
from django.contrib.auth.models import User


# 保持原有计数器模型
class Counters(models.Model):
    """计数器模型 - 保持原有功能"""
    id = models.AutoField(primary_key=True)
    count = models.IntegerField(default=0)
    createdAt = models.DateTimeField(default=datetime.now)
    updatedAt = models.DateTimeField(auto_now=True)

    def __str__(self):
        return str(self.count)

    class Meta:
        db_table = 'Counters'


class ServiceCategory(models.Model):
    """服务分类模型"""
    name = models.CharField(max_length=50, verbose_name='分类名称')
    description = models.TextField(blank=True, verbose_name='分类描述')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'service_categories'
        verbose_name = '服务分类'
        verbose_name_plural = '服务分类'
        ordering = ['sort_order', 'id']


class Service(models.Model):
    """服务项目模型 - 基于设计原型优化"""
    name = models.CharField(max_length=100, verbose_name='服务名称')
    description = models.TextField(verbose_name='服务描述')
    price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='服务费')
    commission = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='技师价格')
    duration = models.IntegerField(verbose_name='服务时长(分钟)')
    image = models.TextField(blank=True, verbose_name='服务图片(base64)')
    is_deleted = models.BooleanField(default=False, verbose_name='是否已删除')
    deleted_at = models.DateTimeField(null=True, blank=True, verbose_name='删除时间')

    # 基于设计原型新增字段
    rating = models.DecimalField(max_digits=3, decimal_places=2, default=5.0, verbose_name='评分')
    review_count = models.IntegerField(default=0, verbose_name='评价数量')
    is_popular = models.BooleanField(default=False, verbose_name='是否热门')
    benefits = models.JSONField(default=list, verbose_name='功效列表')
    contraindications = models.JSONField(default=list, verbose_name='禁忌症列表')

    is_active = models.BooleanField(default=True, verbose_name='是否启用')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return self.name

    def update_rating(self):
        """更新服务评分"""
        reviews = self.appointment_set.filter(
            appointmentreview__isnull=False
        ).select_related('appointmentreview')

        if reviews.exists():
            total_rating = sum(review.appointmentreview.rating for review in reviews)
            self.rating = total_rating / reviews.count()
            self.review_count = reviews.count()
            self.save(update_fields=['rating', 'review_count'])

    class Meta:
        db_table = 'services'
        verbose_name = '服务项目'
        verbose_name_plural = '服务项目'
        ordering = ['sort_order', 'id']
        constraints = [
            models.UniqueConstraint(
                fields=['name'],
                condition=models.Q(is_deleted=False),
                name='unique_service_name_not_deleted'
            )
        ]


class ServicePriceHistory(models.Model):
    """服务价格修改历史记录"""
    service = models.ForeignKey(Service, on_delete=models.CASCADE, related_name='price_history', verbose_name='服务')
    old_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='原服务费')
    new_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='新服务费')
    old_commission = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='原技师价格')
    new_commission = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='新技师价格')
    change_type = models.CharField(max_length=20, choices=[
        ('price', '服务费修改'),
        ('commission', '技师价格修改'),
        ('both', '服务费和技师价格修改')
    ], verbose_name='修改类型')
    changed_at = models.DateTimeField(auto_now_add=True, verbose_name='修改时间')

    class Meta:
        db_table = 'service_price_history'
        verbose_name = '服务价格修改历史'
        verbose_name_plural = '服务价格修改历史'
        ordering = ['-changed_at']

    def __str__(self):
        return f"{self.service.name} - {self.get_change_type_display()} - {self.changed_at.strftime('%Y-%m-%d %H:%M')}"


class Therapist(models.Model):
    """技师模型 - 基于设计原型优化"""
    LEVEL_CHOICES = [
        (1, '一星技师'),
        (2, '二星技师'),
        (3, '三星技师'),
        (4, '四星技师'),
        (5, '五星技师'),
    ]

    name = models.CharField(max_length=50, verbose_name='技师姓名')
    employee_id = models.CharField(max_length=20, unique=True, verbose_name='员工编号')
    phone = models.CharField(max_length=20, verbose_name='联系电话')
    avatar = models.URLField(blank=True, verbose_name='头像')
    level = models.IntegerField(choices=LEVEL_CHOICES, default=1, verbose_name='技师等级')
    specialty = models.TextField(verbose_name='专长描述')
    experience = models.IntegerField(verbose_name='从业年限')

    # 基于设计原型新增字段
    specialties = models.JSONField(default=list, verbose_name='专业技能列表')
    certifications = models.JSONField(default=list, verbose_name='资质证书列表')
    working_hours = models.JSONField(default=dict, verbose_name='工作时间安排')

    is_active = models.BooleanField(default=True, verbose_name='是否在职')
    service_count = models.IntegerField(default=0, verbose_name='服务次数')
    rating = models.DecimalField(max_digits=3, decimal_places=2, default=5.0, verbose_name='评分')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return f"{self.name} ({self.employee_id})"

    def update_rating(self):
        """更新技师评分"""
        reviews = AppointmentReview.objects.filter(
            appointment__therapist=self
        )

        if reviews.exists():
            total_rating = sum(review.therapist_rating for review in reviews)
            self.rating = total_rating / reviews.count()
            self.save(update_fields=['rating'])

    def get_available_slots(self, date):
        """获取指定日期的可用时间段"""
        # 这里实现时间段计算逻辑
        pass

    class Meta:
        db_table = 'therapists'
        verbose_name = '技师'
        verbose_name_plural = '技师'
        ordering = ['-level', 'id']


class Customer(models.Model):
    """客户模型"""
    name = models.CharField(max_length=50, verbose_name='客户姓名')
    phone = models.CharField(max_length=20, unique=True, verbose_name='手机号')
    avatar = models.URLField(blank=True, verbose_name='头像')
    gender = models.CharField(max_length=10, choices=[('male', '男'), ('female', '女')], blank=True, verbose_name='性别')
    birth_date = models.DateField(null=True, blank=True, verbose_name='出生日期')
    address = models.TextField(blank=True, verbose_name='地址')
    notes = models.TextField(blank=True, verbose_name='备注')
    total_spent = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='总消费')
    visit_count = models.IntegerField(default=0, verbose_name='到店次数')
    last_visit = models.DateTimeField(null=True, blank=True, verbose_name='最后到店时间')
    is_vip = models.BooleanField(default=False, verbose_name='是否VIP')
    vip_expire_date = models.DateField(null=True, blank=True, verbose_name='VIP到期日期')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return f"{self.name} ({self.phone})"

    class Meta:
        db_table = 'customers'
        verbose_name = '客户'
        verbose_name_plural = '客户'
        ordering = ['-created_at']


class Appointment(models.Model):
    """预约模型 - 基于设计原型优化"""
    STATUS_CHOICES = [
        ('pending', '待确认'),
        ('confirmed', '已确认'),
        ('in_progress', '服务中'),
        ('completed', '已完成'),
        ('cancelled', '已取消'),
        ('no_show', '未到店'),
    ]

    PAYMENT_STATUS_CHOICES = [
        ('unpaid', '未支付'),
        ('paid', '已支付'),
        ('refunded', '已退款'),
    ]

    customer = models.ForeignKey(Customer, on_delete=models.CASCADE, verbose_name='客户')
    service = models.ForeignKey(Service, on_delete=models.CASCADE, verbose_name='服务项目')
    therapist = models.ForeignKey(Therapist, on_delete=models.CASCADE, verbose_name='技师')
    appointment_time = models.DateTimeField(verbose_name='预约时间')
    duration = models.IntegerField(verbose_name='服务时长(分钟)')
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending', verbose_name='状态')
    payment_status = models.CharField(max_length=20, choices=PAYMENT_STATUS_CHOICES, default='unpaid', verbose_name='支付状态')

    # 价格相关
    original_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='原价')
    discount_amount = models.DecimalField(max_digits=10, decimal_places=2, default=0, verbose_name='优惠金额')
    final_price = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='实付金额')

    # 时间相关
    actual_start_time = models.DateTimeField(null=True, blank=True, verbose_name='实际开始时间')
    actual_end_time = models.DateTimeField(null=True, blank=True, verbose_name='实际结束时间')

    # 其他信息
    notes = models.TextField(blank=True, verbose_name='备注')
    customer_requirements = models.TextField(blank=True, verbose_name='客户需求')
    cancellation_reason = models.TextField(blank=True, verbose_name='取消原因')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return f"{self.customer.name} - {self.service.name} - {self.appointment_time}"

    def save(self, *args, **kwargs):
        """保存时自动计算实付金额"""
        # 设置默认值
        if not self.original_price:
            self.original_price = self.service.price if self.service else 0
        if not self.discount_amount:
            self.discount_amount = 0
        if not self.duration:
            self.duration = self.service.duration if self.service else 60
        if not self.final_price:
            self.final_price = self.original_price - self.discount_amount
        super().save(*args, **kwargs)

    def can_cancel(self):
        """检查是否可以取消"""
        from django.utils import timezone
        from datetime import timedelta

        if self.status in ['completed', 'cancelled']:
            return False

        # 预约时间前2小时内不能取消
        cancel_deadline = self.appointment_time - timedelta(hours=2)
        return timezone.now() < cancel_deadline

    def get_end_time(self):
        """获取预约结束时间"""
        from datetime import timedelta
        return self.appointment_time + timedelta(minutes=self.duration)

    class Meta:
        db_table = 'appointments'
        verbose_name = '预约'
        verbose_name_plural = '预约'
        ordering = ['-appointment_time']
        constraints = [
            models.CheckConstraint(
                check=models.Q(final_price__gte=0),
                name='positive_final_price'
            ),
            models.CheckConstraint(
                check=models.Q(discount_amount__gte=0),
                name='positive_discount'
            ),
        ]


class Employee(models.Model):
    """员工模型"""
    ROLE_CHOICES = [
        ('admin', '管理员'),
        ('manager', '店长'),
        ('receptionist', '前台'),
        ('therapist', '技师'),
        ('cleaner', '清洁员'),
    ]

    user = models.OneToOneField(User, on_delete=models.CASCADE, verbose_name='用户账号')
    name = models.CharField(max_length=50, verbose_name='员工姓名')
    phone = models.CharField(max_length=20, verbose_name='联系电话')
    role = models.CharField(max_length=20, choices=ROLE_CHOICES, verbose_name='职位')
    avatar = models.URLField(blank=True, verbose_name='头像')
    hire_date = models.DateField(verbose_name='入职日期')
    salary = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='基本工资')
    is_active = models.BooleanField(default=True, verbose_name='是否在职')

    # 微信登录相关字段
    wechat_openid = models.CharField(max_length=100, blank=True, null=True, unique=True, verbose_name='微信OpenID')
    wechat_unionid = models.CharField(max_length=100, blank=True, null=True, verbose_name='微信UnionID')
    wechat_nickname = models.CharField(max_length=100, blank=True, verbose_name='微信昵称')
    wechat_avatar = models.URLField(blank=True, verbose_name='微信头像')

    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return f"{self.name} ({self.get_role_display()})"

    class Meta:
        db_table = 'employees'
        verbose_name = '员工'
        verbose_name_plural = '员工'
        ordering = ['-created_at']


class FinanceRecord(models.Model):
    """财务记录模型"""
    TYPE_CHOICES = [
        ('income', '收入'),
        ('expense', '支出'),
    ]

    CATEGORY_CHOICES = [
        ('service', '服务收入'),
        ('product', '产品销售'),
        ('rent', '房租'),
        ('salary', '工资'),
        ('utilities', '水电费'),
        ('supplies', '耗材'),
        ('equipment', '设备'),
        ('marketing', '营销费用'),
        ('other', '其他'),
    ]

    type = models.CharField(max_length=10, choices=TYPE_CHOICES, verbose_name='类型')
    category = models.CharField(max_length=20, choices=CATEGORY_CHOICES, verbose_name='分类')
    amount = models.DecimalField(max_digits=10, decimal_places=2, verbose_name='金额')
    description = models.CharField(max_length=200, verbose_name='描述')
    date = models.DateField(verbose_name='日期')
    appointment = models.ForeignKey(Appointment, on_delete=models.SET_NULL, null=True, blank=True, verbose_name='关联预约')
    employee = models.ForeignKey(Employee, on_delete=models.CASCADE, verbose_name='操作员工')
    notes = models.TextField(blank=True, verbose_name='备注')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return f"{self.get_type_display()} - {self.description} - ¥{self.amount}"

    class Meta:
        db_table = 'finance_records'
        verbose_name = '财务记录'
        verbose_name_plural = '财务记录'
        ordering = ['-date', '-created_at']


class HealthTip(models.Model):
    """健康小贴士模型"""
    title = models.CharField(max_length=100, verbose_name='标题')
    content = models.TextField(verbose_name='内容')
    image = models.URLField(blank=True, verbose_name='配图')
    category = models.CharField(max_length=50, verbose_name='分类')
    tags = models.CharField(max_length=200, blank=True, verbose_name='标签(逗号分隔)')
    is_published = models.BooleanField(default=True, verbose_name='是否发布')
    view_count = models.IntegerField(default=0, verbose_name='浏览次数')
    sort_order = models.IntegerField(default=0, verbose_name='排序')
    author = models.ForeignKey(Employee, on_delete=models.CASCADE, verbose_name='作者')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')
    updated_at = models.DateTimeField(auto_now=True, verbose_name='更新时间')

    def __str__(self):
        return self.title

    class Meta:
        db_table = 'health_tips'
        verbose_name = '健康小贴士'
        verbose_name_plural = '健康小贴士'
        ordering = ['-sort_order', '-created_at']


class AppointmentReview(models.Model):
    """预约评价模型"""
    appointment = models.OneToOneField(Appointment, on_delete=models.CASCADE, verbose_name='预约')
    rating = models.IntegerField(choices=[(i, f'{i}星') for i in range(1, 6)], verbose_name='评分')
    comment = models.TextField(blank=True, verbose_name='评价内容')
    service_rating = models.IntegerField(choices=[(i, f'{i}星') for i in range(1, 6)], verbose_name='服务评分')
    therapist_rating = models.IntegerField(choices=[(i, f'{i}星') for i in range(1, 6)], verbose_name='技师评分')
    environment_rating = models.IntegerField(choices=[(i, f'{i}星') for i in range(1, 6)], verbose_name='环境评分')
    created_at = models.DateTimeField(auto_now_add=True, verbose_name='创建时间')

    def __str__(self):
        return f"{self.appointment.customer.name} - {self.rating}星"

    class Meta:
        db_table = 'appointment_reviews'
        verbose_name = '预约评价'
        verbose_name_plural = '预约评价'
        ordering = ['-created_at']


class GlobalVersion(models.Model):
    """全局版本管理 - 每次成功部署自动递增"""
    version = models.IntegerField(default=1, verbose_name="全局版本号")
    deployment_tag = models.CharField(max_length=100, verbose_name="部署标签", help_text="如django-t3qr-014")
    deployment_time = models.DateTimeField(auto_now_add=True, verbose_name="部署时间")
    git_commit = models.CharField(max_length=40, blank=True, verbose_name="Git提交哈希")
    django_version = models.CharField(max_length=20, default="3.2.8", verbose_name="Django版本")
    is_active = models.BooleanField(default=True, verbose_name="是否为当前活跃版本")
    notes = models.TextField(blank=True, verbose_name="版本说明")

    def __str__(self):
        return f"v{self.version} - {self.deployment_tag}"

    @classmethod
    def get_current_version(cls):
        """获取当前版本号"""
        try:
            return cls.objects.filter(is_active=True).latest('deployment_time').version
        except cls.DoesNotExist:
            return 1

    @classmethod
    def increment_version(cls, deployment_tag, git_commit="", notes=""):
        """递增版本号并创建新记录"""
        # 将所有版本设为非活跃
        cls.objects.update(is_active=False)

        # 获取最新版本号
        try:
            latest_version = cls.objects.latest('version').version
            new_version = latest_version + 1
        except cls.DoesNotExist:
            new_version = 1

        # 创建新版本记录
        new_record = cls.objects.create(
            version=new_version,
            deployment_tag=deployment_tag,
            git_commit=git_commit,
            notes=notes,
            is_active=True
        )

        return new_record

    class Meta:
        db_table = 'global_versions'
        verbose_name = '全局版本'
        verbose_name_plural = '全局版本'
        ordering = ['-deployment_time']
