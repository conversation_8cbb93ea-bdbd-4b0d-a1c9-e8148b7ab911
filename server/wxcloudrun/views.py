import json
import logging
import os
import subprocess

from django.http import JsonResponse, HttpResponse
from django.shortcuts import render, redirect
from django.views.decorators.csrf import csrf_exempt
from django.views.decorators.http import require_http_methods
from django.utils import timezone
from datetime import <PERSON><PERSON><PERSON>
from decimal import Decimal
from wxcloudrun.models import Counters, GlobalVersion, Service, Customer, Therapist, Appointment


logger = logging.getLogger('log')


def health_check(request):
    """
    健康检查接口
    """
    return HttpResponse("OK", status=200)


def api_root(request):
    """
    API根路径 - 重定向到API浏览器
    """
    return redirect('/api/v1/')


def index(request, _):
    """
    获取主页

     `` request `` 请求对象
    """

    return render(request, 'index.html')


def counter(request, _):
    """
    获取当前计数

     `` request `` 请求对象
    """

    rsp = JsonResponse({'code': 0, 'errorMsg': ''}, json_dumps_params={'ensure_ascii': False})
    if request.method == 'GET' or request.method == 'get':
        rsp = get_count()
    elif request.method == 'POST' or request.method == 'post':
        rsp = update_count(request)
    else:
        rsp = JsonResponse({'code': -1, 'errorMsg': '请求方式错误'},
                            json_dumps_params={'ensure_ascii': False})
    logger.info('response result: {}'.format(rsp.content.decode('utf-8')))
    return rsp


def get_count():
    """
    获取当前计数
    """

    try:
        data = Counters.objects.get(id=1)
    except Counters.DoesNotExist:
        return JsonResponse({'code': 0, 'data': 0},
                    json_dumps_params={'ensure_ascii': False})
    return JsonResponse({'code': 0, 'data': data.count},
                        json_dumps_params={'ensure_ascii': False})


def update_count(request):
    """
    更新计数，自增或者清零

    `` request `` 请求对象
    """

    logger.info('update_count req: {}'.format(request.body))

    body_unicode = request.body.decode('utf-8')
    body = json.loads(body_unicode)

    if 'action' not in body:
        return JsonResponse({'code': -1, 'errorMsg': '缺少action参数'},
                            json_dumps_params={'ensure_ascii': False})

    if body['action'] == 'inc':
        try:
            data = Counters.objects.get(id=1)
        except Counters.DoesNotExist:
            data = Counters()
        data.id = 1
        data.count += 1
        data.save()
        return JsonResponse({'code': 0, "data": data.count},
                    json_dumps_params={'ensure_ascii': False})
    elif body['action'] == 'clear':
        try:
            data = Counters.objects.get(id=1)
            data.delete()
        except Counters.DoesNotExist:
            logger.info('record not exist')
        return JsonResponse({'code': 0, 'data': 0},
                    json_dumps_params={'ensure_ascii': False})
    else:
        return JsonResponse({'code': -1, 'errorMsg': 'action参数错误'},
                    json_dumps_params={'ensure_ascii': False})


def get_version_info(request):
    """
    获取当前版本信息
    """
    try:
        current_version = GlobalVersion.get_current_version()
        latest_record = GlobalVersion.objects.filter(is_active=True).first()

        version_info = {
            'current_version': current_version,
            'deployment_tag': latest_record.deployment_tag if latest_record else 'unknown',
            'deployment_time': latest_record.deployment_time.isoformat() if latest_record else None,
            'django_version': latest_record.django_version if latest_record else '3.2.8',
            'git_commit': latest_record.git_commit if latest_record else '',
        }

        return JsonResponse({
            'code': 0,
            'data': version_info
        }, json_dumps_params={'ensure_ascii': False})

    except Exception as e:
        logger.error(f'获取版本信息失败: {e}')
        return JsonResponse({
            'code': -1,
            'errorMsg': f'获取版本信息失败: {str(e)}'
        }, json_dumps_params={'ensure_ascii': False})


@csrf_exempt
@require_http_methods(["POST"])
def increment_version(request):
    """
    递增全局版本号 - 部署成功后调用
    """
    try:
        body = json.loads(request.body)
        deployment_tag = body.get('deployment_tag', 'unknown')
        git_commit = body.get('git_commit', '')
        notes = body.get('notes', '')

        # 递增版本
        new_record = GlobalVersion.increment_version(
            deployment_tag=deployment_tag,
            git_commit=git_commit,
            notes=notes
        )

        logger.info(f'版本递增成功: v{new_record.version} - {deployment_tag}')

        return JsonResponse({
            'code': 0,
            'data': {
                'version': new_record.version,
                'deployment_tag': new_record.deployment_tag,
                'deployment_time': new_record.deployment_time.isoformat(),
                'message': f'版本已更新到 v{new_record.version}'
            }
        }, json_dumps_params={'ensure_ascii': False})

    except Exception as e:
        logger.error(f'版本递增失败: {e}')
        return JsonResponse({
            'code': -1,
            'errorMsg': f'版本递增失败: {str(e)}'
        }, json_dumps_params={'ensure_ascii': False})


def get_version_history(request):
    """
    获取版本历史记录
    """
    try:
        versions = GlobalVersion.objects.all()[:10]  # 最近10个版本

        version_list = []
        for version in versions:
            version_list.append({
                'version': version.version,
                'deployment_tag': version.deployment_tag,
                'deployment_time': version.deployment_time.isoformat(),
                'git_commit': version.git_commit,
                'django_version': version.django_version,
                'is_active': version.is_active,
                'notes': version.notes
            })

        return JsonResponse({
            'code': 0,
            'data': version_list
        }, json_dumps_params={'ensure_ascii': False})

    except Exception as e:
        logger.error(f'获取版本历史失败: {e}')
        return JsonResponse({
            'code': -1,
            'errorMsg': f'获取版本历史失败: {str(e)}'
        }, json_dumps_params={'ensure_ascii': False})


@csrf_exempt
@require_http_methods(["POST"])
def create_test_data(request):
    """
    创建测试数据API端点 - 用于自动化测试
    """
    try:
        logger.info('开始创建测试数据...')

        # 清理现有数据
        Service.objects.all().delete()
        Customer.objects.all().delete()
        Therapist.objects.all().delete()
        Appointment.objects.all().delete()

        # 创建服务项目
        services_data = [
            {
                'name': '中式按摩',
                'description': '传统中式按摩，缓解肌肉疲劳',
                'price': Decimal('198.00'),
                'duration': 60,
                'benefits': ['放松肌肉', '缓解疲劳', '改善血液循环'],
                'contraindications': ['孕妇禁用', '皮肤破损禁用']
            },
            {
                'name': '足疗保健',
                'description': '专业足疗，促进血液循环',
                'price': Decimal('168.00'),
                'duration': 45,
                'benefits': ['促进血液循环', '缓解足部疲劳'],
                'contraindications': ['足部有伤口禁用']
            },
            {
                'name': '拔罐刮痧',
                'description': '传统拔罐刮痧，排毒养颜',
                'price': Decimal('138.00'),
                'duration': 30,
                'benefits': ['排毒养颜', '疏通经络'],
                'contraindications': ['皮肤敏感禁用']
            }
        ]

        created_services = []
        for service_data in services_data:
            service = Service.objects.create(**service_data)
            created_services.append(service)

        # 创建技师
        therapists_data = [
            {
                'name': '张师傅',
                'employee_id': 'T001',
                'phone': '13800138001',
                'level': 5,
                'specialty': '中式按摩',
                'experience': 8,
                'specialties': ['按摩', '推拿', '拔罐'],
                'certifications': ['中医按摩师证', '高级技师证']
            },
            {
                'name': '李师傅',
                'employee_id': 'T002',
                'phone': '13800138002',
                'level': 4,
                'specialty': '足疗保健',
                'experience': 5,
                'specialties': ['足疗', '按摩'],
                'certifications': ['足疗师证']
            }
        ]

        created_therapists = []
        for therapist_data in therapists_data:
            therapist = Therapist.objects.create(**therapist_data)
            created_therapists.append(therapist)

        # 创建客户
        customers_data = [
            {
                'name': '张女士',
                'phone': '13900139001',
                'gender': 'female',
                'address': '上海市浦东新区张江高科技园区'
            },
            {
                'name': '李先生',
                'phone': '13900139002',
                'gender': 'male',
                'address': '上海市徐汇区淮海中路'
            }
        ]

        created_customers = []
        for customer_data in customers_data:
            customer = Customer.objects.create(**customer_data)
            created_customers.append(customer)

        # 创建预约
        appointments_data = [
            {
                'customer': created_customers[0],
                'service': created_services[0],
                'therapist': created_therapists[0],
                'appointment_time': timezone.now() + timedelta(days=1, hours=10),
                'customer_requirements': '希望轻一点，颈椎不太好',
                'notes': '客户是老顾客，服务态度要好'
            },
            {
                'customer': created_customers[1],
                'service': created_services[1],
                'therapist': created_therapists[1],
                'appointment_time': timezone.now() + timedelta(days=2, hours=14),
                'customer_requirements': '足部按摩重一点',
                'notes': '客户工作压力大，需要放松'
            }
        ]

        created_appointments = []
        for appointment_data in appointments_data:
            appointment = Appointment.objects.create(**appointment_data)
            created_appointments.append(appointment)

        # 统计结果
        stats = {
            'services': Service.objects.count(),
            'therapists': Therapist.objects.count(),
            'customers': Customer.objects.count(),
            'appointments': Appointment.objects.count()
        }

        logger.info(f'测试数据创建完成: {stats}')

        return JsonResponse({
            'code': 0,
            'data': stats,
            'message': '测试数据创建成功'
        }, json_dumps_params={'ensure_ascii': False})

    except Exception as e:
        logger.error(f'创建测试数据失败: {e}')
        return JsonResponse({
            'code': -1,
            'errorMsg': f'创建测试数据失败: {str(e)}'
        }, json_dumps_params={'ensure_ascii': False})
