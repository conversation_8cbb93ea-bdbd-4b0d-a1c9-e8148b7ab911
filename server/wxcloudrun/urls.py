"""wxcloudrun URL Configuration

壹心堂中医推拿管理系统 - 主路由配置
基于微信云托管成功模板，Django管理工具在根路径访问
"""

from django.contrib import admin
from django.urls import path, include, re_path
from django.conf import settings
from django.conf.urls.static import static
from wxcloudrun import views

urlpatterns = [
    # Django管理后台 - 根路径访问 (生产环境主要入口)
    path('admin/', admin.site.urls),

    # 健康检查接口
    path('health/', views.health_check),

    # 计数器接口 (保持兼容)
    re_path(r'^api/count(/)?$', views.counter),

    # 版本管理接口
    path('api/version/', views.get_version_info, name='version_info'),
    path('api/version/increment/', views.increment_version, name='increment_version'),
    path('api/version/history/', views.get_version_history, name='version_history'),

    # 测试数据接口
    path('create_test_data/', views.create_test_data, name='create_test_data'),

    # 业务API接口
    path('api/', include('api.urls')),

    # 主页 - API浏览器入口 (Django管理工具在 /admin/)
    re_path(r'^$', views.api_root, name='api_root'),
]

# 开发环境静态文件服务
if settings.DEBUG:
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)

# 自定义管理后台配置
admin.site.site_header = '壹心堂管理系统'
admin.site.site_title = '壹心堂管理系统'
admin.site.index_title = '欢迎使用壹心堂管理系统'
